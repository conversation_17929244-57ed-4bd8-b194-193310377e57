import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import NextAuth from 'next-auth'
import { authConfig } from './lib/auth.config'
import { SIDE_BAR_MAIN_DATA_RAW } from "@/lib/constants/navigation"
import { getToken } from 'next-auth/jwt'

// 原有 NextAuth 中间件
const authMiddleware = NextAuth(authConfig).auth

// 🔥 Language handling middleware
function handleLanguageMiddleware(request: NextRequest) {
  const { searchParams, pathname } = request.nextUrl
  const langParam = searchParams.get('lang')
  const supportedLocales = ['en', 'ja', 'zh']

  // Extract language from wiki path
  const wikiPathRegex = /^\/wiki\/([a-z]{2})/
  const wikiMatch = pathname.match(wikiPathRegex)
  const pathLang = wikiMatch ? wikiMatch[1] : null

  // Get current cookie value
  const currentCookie = request.cookies.get('locale')?.value

  // Determine the target language (URL param takes precedence, then path)
  const targetLang = (langParam && supportedLocales.includes(langParam)) ? langParam :
                     (pathLang && supportedLocales.includes(pathLang)) ? pathLang : null

  // Only set cookie if target language is different from current cookie
  if (targetLang && targetLang !== currentCookie) {
    const response = NextResponse.next()

    // Set cookie with proper options
    response.cookies.set('locale', targetLang, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    })

    console.log('🔥 [middleware] Setting locale cookie:', {
      from: langParam ? 'URL param' : 'wiki path',
      language: targetLang,
      previousCookie: currentCookie,
      path: pathname
    })

    return response
  }

  // If no language change needed, just continue
  if (targetLang) {
    console.log('🔥 [middleware] Language already set:', {
      language: targetLang,
      path: pathname
    })
  }

  return NextResponse.next()
}

// 检查用户权限
async function authorizationCheck(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

  const userAccessLevel = token?.accessLevel || 0; // 从 token 获取 userAccessLevel

  // console.log("🔥 req.nextUrl.pathname🔥", req.nextUrl.pathname);
  const routeData = SIDE_BAR_MAIN_DATA_RAW.find(item =>
    req.nextUrl.pathname.startsWith(item.url) ||
    item.menus?.some((menu: any) => req.nextUrl.pathname.startsWith(menu.url)) ||
    item.menus?.some((menu: any) => menu?.items?.some((subMenu: any) => req.nextUrl.pathname.startsWith(subMenu.url)))
  );

  if (routeData) {
    // First match the sub item
    const subItemMatch = routeData.menus?.find((menu: any) => menu.items?.some((subMenu: any) => req.nextUrl.pathname.startsWith(subMenu.url)));
    // Then match the menu
    const menuMatch = routeData.menus?.find((menu: any) => req.nextUrl.pathname.startsWith(menu.url));
    // logger.info(`🔥 subItemMatch🔥 ${subItemMatch}`);
    // logger.info(`🔥 menuMatch🔥 ${menuMatch}`);
    // logger.info(`🔥 routeData🔥 ${routeData}`);

    const accessLevel = subItemMatch?.accessLevel || menuMatch?.accessLevel || routeData.accessLevel; // 使用最近匹配项的 accessLevel
    if (userAccessLevel && accessLevel && (userAccessLevel as number) < accessLevel) {
      return NextResponse.json({ success: false, message: 'Unauthorized: Access level too low' }, { status: 403 }); // 返回错误信息
    }
  }

  return NextResponse.next();
}

// 检查请求头中的 admin 键
function apiCheckMiddelware(req: NextRequest) {
  const checkAdminHeader = (req: NextRequest) => {
    const apiWhitelistToken = req.headers.get('api-whitelist-token');
    return apiWhitelistToken === process.env.API_WHITELIST_TOKEN; // 只有当 admin 的值为 'true' 时才返回 true
  }

  // logger.info(`🔥 checking api - req.nextUrl.pathname🔥 ${req.nextUrl.pathname}`);
  if (req.nextUrl.pathname.startsWith('/api') && !req.nextUrl.pathname.startsWith('/api/auth') && !req.nextUrl.pathname.startsWith('/api/proxy')) {
    if (!checkAdminHeader(req)) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 403 });
    }
  }

  return NextResponse.next();
}

// **Step 4: Compose Middlewares**
const middlewares = [
  { name: "language", item: handleLanguageMiddleware }, // 🔥 Add language middleware first
  { name: "auth", item: authMiddleware },
  { name: "authorizationCheck", item: authorizationCheck },
  { name: "apiCheck", item: apiCheckMiddelware }
];

async function composeMiddlewares(req: NextRequest, middlewares: { name: string, item: Function }[]) {
  let res = NextResponse.next();

  for (const middleware of middlewares) {
    // console.log(`🔥 middleware ${middleware.name} - req🔥`);
    const result = await middleware.item(req, res); // 确保中间件按顺序执行
    if (result) res = result; // Ensure middleware responses (like redirects) are applied
    if (result && result.status !== 200) break; // 如果中间件返回非200状态，停止执行后续中间件
  }

  return res;
}

// **Step 5: Apply Middleware**
export default async function middleware(req: NextRequest) {
  // console.log("🔥 middleware - req.nextUrl.pathname🔥", req.nextUrl.pathname);

  // 🔥 Skip auth middleware for homepage (but still apply language middleware)
  if (req.nextUrl.pathname === '/') {
    // Only apply language middleware for homepage
    const languageResult = handleLanguageMiddleware(req);
    return languageResult;
  }

  // FIXME: so it can show on wechat?? not sure it works
  if (req.nextUrl.pathname.startsWith('/blog/')) {
    const res = NextResponse.next();
    res.headers.set('Cache-Control', 'public, max-age=600');
    return res;
  }

  if (req.nextUrl.pathname.startsWith('/api/cron')) {
    // Will check the token instead
    return NextResponse.next();
  }

  return await composeMiddlewares(req, middlewares);
}

// 原有匹配规则保持不变
// Start of Selection
// NOTE: that pub path is not included in the matcher
// Also not including the api/auth route
export const config = {
  matcher: [
    // 🔥 Add root and wiki paths for language handling
    '/',
    '/wiki/:path*',
    // Existing protected routes
    '/ex/:path*',
    '/it/:path*',
    '/an/:path*',
    '/pa/:path*',
    '/ad/:path*',
    '/su/:path*',
    "/my/:path*",
    "/api/cron/:path*"
  ],
}
