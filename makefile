lint:
	npx eslint .

prisma:
	npx prisma db push
	npx prisma generate
  # curl --request GET 'https://wcode.net/api/account/billing/grants' --header 'Authorization: Bearer sk-208.LtfWYOH6eeeyeTFVDLGO8Oaf6pIDaErO56kyeTeWRGdzw2U3'

log:
	vercel logs https://urbalytics.jp/
 
deploy-test:
	NODE_ENV=dev serverless invoke local --function getPriceReinsBuilding --data '{"id":"12", "taskExecutionType": "manual"}'

remove:
	serverless remove

invoke-remote:
	serverless invoke --log --function getPriceReinsBuilding --data '{"id":"12", "taskExecutionType": "manual"}'

invoke-local:
	NODE_ENV=dev serverless invoke local --function getPriceReinsBuilding --data '{"id":"12", "taskExecutionType": "manual"}' 

invoke-local-with-id-reins:
	NODE_ENV=dev serverless invoke local --function getPriceReinsHouse  --data '{"id":"54", "taskExecutionType": "manual"}'

prune-old-versions: 
	sls prune -n 10

############### SEQUELIZE ################
init-sequelize:
	npx sequelize init:config
	npx sequelize init:migrations

undo:
	npx sequelize db:migrate:undo --name **************-init-user-lambda-record-sales.js

migration-generate-new-stuff:
	npx sequelize migration:generate --name=init-users
	npx sequelize-cli seed:generate --name=seed-users

migration-migrate:
	npx sequelize db:migrate --debug
	npx sequelize-cli db:seed:all --debug

seed-one:
	npx sequelize-cli db:seed --seed 20220211020554-seed-tll-user-lambda.js

############### EMAILS ################
mail-sendgrid:
	node app/extend/emailTest.js 



# build-and-push:
# 	npm run build:prod
# 	aws s3 sync --delete ./dist s3://admin.tll.jp/
# make	aws cloudfront create-invalidation --distribution-id E2UUVHJNUQ09QM --paths "/*"

build-and-push:
  rm -rf ./dist
	pnpm run build:prod
	aws s3 sync --delete ./dist s3://urbalytics.jp/
	aws cloudfront create-invalidation --distribution-id E2JP0PKV8YUS8G --paths "/*"
	aws cloudfront create-invalidation --distribution-id EIL852EVZRB06 --paths "/*"

deploy-e2e:
	make docker-build && make docker-push && make deploy

docker-build:
	npm run build && docker build -t toshigo-frontend .

docker-push:
	docker tag toshigo-frontend ccr.ccs.tencentyun.com/toshigo/toshigo-frontend:latest
	docker push ccr.ccs.tencentyun.com/toshidgo/toshigo-frontend:latest

deploy:
	kubectl delete -f config/k8s.yaml
	kubectl create -f config/k8s.yaml

docker-run:
	# use host network so that it works locally for upstream
	docker run --network host toshigo-frontend

k8s-secret:
	# get the password from 1 password and remember to put double quote
	kubectl create secret docker-registry docker-registry-secret --docker-server=ccr.ccs.tencentyun.com --docker-username=100001070993 --docker-password=XXX

create-user:
	htpasswd -c ./config/htpasswd tll

umi-get-model:
	umi dva list model


# FOR THE LANDING SITE
serve:
	serve -s build

build:
	npm run build && rm -rf docs && mv dist docs && cp CNAME ./docs/ && git add . && git commit -m "build: commit for relase" && git pom -f

# git push origin `git subtree split --prefix build origin`:gh-pages --force

changelog-first:
	conventional-changelog -p angular -i CHANGELOG.md -s -r 0

changelog:
	conventional-changelog -p angular -i CHANGELOG.md -s
