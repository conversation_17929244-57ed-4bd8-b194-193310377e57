// 主要页面配置
const mainPages = [
  { path: '/', priority: 1.0, changefreq: 'daily' },
  { path: '/blog', priority: 0.8, changefreq: 'daily' },
  { path: '/wiki', priority: 0.6, changefreq: 'weekly' },
  { path: '/pricing', priority: 0.9, changefreq: 'monthly' },
  { path: '/feature/search', priority: 0.9, changefreq: 'monthly' },
  { path: '/feature/rent', priority: 0.9, changefreq: 'monthly' },
  { path: '/feature/valuation', priority: 0.9, changefreq: 'monthly' },
  { path: '/feature/insight', priority: 0.9, changefreq: 'monthly' },
];

// 静态页面配置
const staticPages = [
  // 主要功能页面
  {
    loc: '/',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 1.0
  },
  {
    loc: '/blog',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 0.8
  },
  {
    loc: '/wiki',
    lastmod: new Date().toISOString(),
    changefreq: 'weekly',
    priority: 0.6
  },
  {
    loc: '/pricing',
    lastmod: new Date().toISOString(),
    changefreq: 'monthly',
    priority: 0.9
  },
  
  // 功能页面
  {
    loc: '/feature/search',
    lastmod: new Date().toISOString(),
    changefreq: 'monthly',
    priority: 0.9
  },
  {
    loc: '/feature/rent',
    lastmod: new Date().toISOString(),
    changefreq: 'monthly',
    priority: 0.9
  },
  {
    loc: '/feature/valuation',
    lastmod: new Date().toISOString(),
    changefreq: 'monthly',
    priority: 0.9
  },
  {
    loc: '/feature/insight',
    lastmod: new Date().toISOString(),
    changefreq: 'monthly',
    priority: 0.9
  },
  
  // 多语言版本的主要页面
  {
    loc: '/?lang=en',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 0.9
  },
  {
    loc: '/?lang=zh',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 0.9
  },
  {
    loc: '/blog?lang=en',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 0.7
  },
  {
    loc: '/blog?lang=zh',
    lastmod: new Date().toISOString(),
    changefreq: 'daily',
    priority: 0.7
  },
  {
    loc: '/wiki?lang=en',
    lastmod: new Date().toISOString(),
    changefreq: 'weekly',
    priority: 0.5
  },
  {
    loc: '/wiki?lang=zh',
    lastmod: new Date().toISOString(),
    changefreq: 'weekly',
    priority: 0.5
  }
];

module.exports = {
  mainPages,
  staticPages
};
