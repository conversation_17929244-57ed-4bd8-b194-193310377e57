import { create } from "zustand";
import { TllUserProps } from "@/lib/definitions/tllUser";

interface UserState {
  users: TllUserProps[];
  setUser: (user: TllUserProps[]) => void;
  addUser: (user: TllUserProps) => void;
  updateUser: (user: TllUserProps) => void;
  deleteUser: (id: string) => void;
}

export const useUserStore = create<UserState>((set) => ({
  users: [],
  setUser: (users) => set({ users }),
  addUser: (user) => set((state) => ({ users: [...state.users, user] })),
  updateUser: (user) => set((state) => ({ users: state.users?.map((u) => (u.id === user.id ? user : u)) })),
  deleteUser: (id) => set((state) => ({ users: state.users?.filter((u) => u.id !== id) })),
}));
