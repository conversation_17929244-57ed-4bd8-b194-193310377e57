import { create } from "zustand";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { ProProject } from "@/lib/definitions/proProject";

interface UserLambdaRecordState {
  userLambdaRecords: UserLambdaRecordProps[];
  setAllUserLambdaRecords: (records: UserLambdaRecordProps[]) => void;
  clearUserLambdaRecords: () => void;

  currentUserLambdaRecord: UserLambdaRecordProps | null;
  setCurrentUserLambdaRecord: (record: UserLambdaRecordProps) => void;

  nearbyMaxMin80Percentile: any,
  setNearbyMaxMin80Percentile: (nearbyMaxMin80Percentile: any) => void;

  nearbyRecords: UserLambdaRecordProps[],
  setNearbyRecords: (nearbyRecords: UserLambdaRecordProps[]) => void;

  nearbyProjects: ProProject[],
  setNearbyProjects: (nearbyProjects: ProProject[]) => void;
}

export const useUserLambdaRecordStore = create<UserLambdaRecordState>((set) => ({
  userLambdaRecords: [],
  setAllUserLambdaRecords: (records) => set({ userLambdaRecords: records }),
  clearUserLambdaRecords: () => set({ userLambdaRecords: [] }),

  currentUserLambdaRecord: null,
  setCurrentUserLambdaRecord: (record: UserLambdaRecordProps) => set({ currentUserLambdaRecord: record }),

  nearbyMaxMin80Percentile: null,
  setNearbyMaxMin80Percentile: (nearbyMaxMin80Percentile: any) => set({ nearbyMaxMin80Percentile }),

  nearbyRecords: [],
  setNearbyRecords: (nearbyRecords: UserLambdaRecordProps[]) => set({ nearbyRecords }),

  nearbyProjects: [],
  setNearbyProjects: (nearbyProjects: ProProject[]) => set({ nearbyProjects }),
}));
