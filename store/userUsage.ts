import { create } from 'zustand'

interface UserUsageState {
  todaySearchUsageData: any;
  setTodaySearchUsageData: (todaySearchUsageData: any) => void;

  todayWatchUsageData: any;
  setTodayWatchUsageData: (todayWatchUsageData: any) => void;
}

export const useUserUsageStore = create<UserUsageState>((set) => ({
  todaySearchUsageData: null,
  setTodaySearchUsageData: (todaySearchUsageData: any) => set({ todaySearchUsageData }),

  todayWatchUsageData: null,
  setTodayWatchUsageData: (todayWatchUsageData: any) => set({ todayWatchUsageData }),
}))