

import { create } from "zustand";
import { createExpiringStorage } from "./utility/createExpiringStorage";
import { persist } from "zustand/middleware";

interface UiRecommendationsState {
  recommendationPriceDownProperties: any;
  setRecommendationPriceDownProperties: (recommendations: any) => void;

  recommendationHighYieldProperties: any;
  setRecommendationHighYieldProperties: (recommendations: any) => void;

  recommendationsBasedOnSearchHistory: any;
  setRecommendationsBasedOnSearchHistory: (recommendations: any) => void;

  recommendationsBasedOnValuationHistory: any;
  setRecommendationsBasedOnValuationHistory: (recommendations: any) => void;

  recommendationsWatchListProperties: any;
  setRecommendationsWatchListProperties: (recommendations: any) => void;

  recommendationsBasedOnFav: any;
  setRecommendationsBasedOnFav: (recommendations: any) => void;
}

export const useUIRecommendationsStore = create(
  persist(
    (set) => ({
      recommendationPriceDownProperties: null,
      setRecommendationPriceDownProperties: (recommendations: any) => set({ recommendationPriceDownProperties: recommendations }),

      recommendationHighYieldProperties: null,
      setRecommendationHighYieldProperties: (recommendations: any) => set({ recommendationHighYieldProperties: recommendations }),

      recommendationsBasedOnSearchHistory: null,
      setRecommendationsBasedOnSearchHistory: (recommendations: any[]) => set({ recommendationsBasedOnSearchHistory: recommendations }),

      recommendationsBasedOnValuationHistory: null,
      setRecommendationsBasedOnValuationHistory: (recommendations: any[]) => set({ recommendationsBasedOnValuationHistory: recommendations }),

      recommendationsWatchListProperties: null,
      setRecommendationsWatchListProperties: (recommendations: any[]) => set({ recommendationsWatchListProperties: recommendations }),

      recommendationsBasedOnFav: null,
      setRecommendationsBasedOnFav: (recommendations: any[]) => set({ recommendationsBasedOnFav: recommendations }),
    } as UiRecommendationsState),
    {
      name: "ui-recommendations-storage",
      storage: createExpiringStorage(24 * 60 * 60 * 1000) as any, // 12 小时 expire, since the data is 7 day data
      partialize: (state: any) => ({
        recommendationPriceDownProperties: state.recommendationPriceDownProperties,
        recommendationHighYieldProperties: state.recommendationHighYieldProperties,
        recommendationsBasedOnSearchHistory: state.recommendationsBasedOnSearchHistory,
        recommendationsBasedOnValuationHistory: state.recommendationsBasedOnValuationHistory,
        recommendationsWatchListProperties: state.recommendationsWatchListProperties,
        recommendationsBasedOnFav: state.recommendationsBasedOnFav,
      }),
    }
  )
)