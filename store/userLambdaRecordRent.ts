import { create } from "zustand";

interface UserLambdaRecordRentState {
  nearbySold: number;
  setNearbySold: (nearbySold: number) => void;

  nearbyRent: number;
  setNearbyRent: (nearbyRent: number) => void;

  nearbyRentPart: number;
  setNearbyRentPart: (nearbyRentPart: number) => void;
}

export const useUserLambdaRecordRentStore = create<UserLambdaRecordRentState>((set) => ({
  nearbySold: 0,
  setNearbySold: (nearbySold: number) => set({ nearbySold }),

  nearbyRent: 0,
  setNearbyRent: (nearbyRent: number) => set({ nearbyRent }),

  nearbyRentPart: 0,
  setNearbyRentPart: (nearbyRentPart: number) => set({ nearbyRentPart }),
}));
