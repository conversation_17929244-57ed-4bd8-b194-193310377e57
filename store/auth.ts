import { create } from "zustand";
import { TllUserProps } from "@/lib/definitions/tllUser";

interface AuthState {
  currentUser: TllUserProps | null;

  setCurrentUser: (user: AuthState["currentUser"]) => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  currentUser: null,

  setCurrentUser: (user) => set({ currentUser: user }),
  logout: () => set({ currentUser: null }), // Clears Zustand state
}));