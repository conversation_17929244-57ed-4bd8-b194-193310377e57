import { create } from "zustand";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { createExpiringStorage } from "./utility/createExpiringStorage";
import { persist } from "zustand/middleware";

interface UserDataState {
  mihariData: any;
  setMihariData: (mihariData: any[]) => void;
}

export const useDataStore = create(
  persist(
    (set) => ({
      mihariData: null,
      setMihariData: (mihariData: any[]) => set({ mihariData }),
    } as UserDataState),
    {
      name: "user-data-storage",
      storage: createExpiringStorage(6 * 60 * 60 * 1000) as any, // 6 小时 expire, since the data is 7 day data
      partialize: (state: any) => ({
        mihariData: state.mihariData,
      }),
    }
  )
)