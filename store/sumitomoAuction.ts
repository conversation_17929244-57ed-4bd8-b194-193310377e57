import { create } from "zustand";
import { SumitomoAuctionProps } from "@/lib/definitions";
import { TllUserProps } from "@/lib/definitions/tllUser";

interface SumitomoAuctionState {
  sumitomoAuctions: SumitomoAuctionProps[];
  setSumitomoAuctions: (sumitomoAuctions: SumitomoAuctionProps[]) => void;
  updateSumitomoAuction: (sumitomoAuction: SumitomoAuctionProps) => void;
}

export const useSumitomoAuctionStore = create<SumitomoAuctionState>((set) => ({
  sumitomoAuctions: [],
  setSumitomoAuctions: (sumitomoAuctions: SumitomoAuctionProps[]) => set({ sumitomoAuctions }),
  updateSumitomoAuction: (sumitomoAuction: SumitomoAuctionProps) => set((state) => ({ sumitomoAuctions: state.sumitomoAuctions?.map((u) => (u.id === sumitomoAuction.id ? sumitomoAuction : u)) })),
}));
