import { create } from "zustand";
import { persist } from "zustand/middleware";

const IMAGE_EXPIRE_MS = 7 * 24 * 60 * 60 * 1000; // 7天
const LOCAL_STORAGE_LIMIT_BYTES = 3 * 1024 * 1024; // 3MB，安全限

type CachedImage = {
  base64: string;
  timestamp: number;
};

type ImageCacheStore = {
  images: { [key: string]: CachedImage };
  setImage: (id: string, base64: string) => void;
  getImage: (id: string) => string | null;
  cleanupExpiredAndOversize: () => void;
};

function getStorageSize(obj: object) {
  try {
    return new Blob([JSON.stringify(obj)]).size;
  } catch {
    return 0;
  }
}

export const useImageCacheStore = create<ImageCacheStore>()(
  persist(
    (set, get) => ({
      images: {},

      setImage: (id, base64) => {
        const now = Date.now();
        const currentImages = { ...get().images, [id]: { base64, timestamp: now } };

        // 检查是否超过大小限制
        if (getStorageSize(currentImages) > LOCAL_STORAGE_LIMIT_BYTES) {
          console.info("Storage too large, starting LRU cleanup...");

          // 先排序，根据 timestamp
          const sorted = Object.entries(currentImages).sort((a, b) => a[1].timestamp - b[1].timestamp); // oldest first

          while (getStorageSize(currentImages) > LOCAL_STORAGE_LIMIT_BYTES && sorted.length > 0) {
            const [oldestId] = sorted.shift()!;
            console.info(`Removing oldest cached image: ${oldestId}`);
            delete currentImages[oldestId];
          }
        }

        set({ images: currentImages });
      },

      getImage: (id) => {
        const imageData = get().images[id];
        if (!imageData) return null;

        // 检查是否过期
        if (Date.now() - imageData.timestamp > IMAGE_EXPIRE_MS) {
          console.info(`Image ${id} expired and removed`);
          set((state) => {
            const newImages = { ...state.images };
            delete newImages[id];
            return { images: newImages };
          });
          return null;
        }

        return imageData.base64;
      },

      cleanupExpiredAndOversize: () => {
        const now = Date.now();
        const images = { ...get().images };

        // 删除过期
        Object.keys(images).forEach((id) => {
          if (now - images[id].timestamp > IMAGE_EXPIRE_MS) {
            console.info(`Expired image ${id} removed`);
            delete images[id];
          }
        });

        // 再检查整体大小
        if (getStorageSize(images) > LOCAL_STORAGE_LIMIT_BYTES) {
          console.info("Storage still too large, doing LRU cleanup...");
          const sorted = Object.entries(images).sort((a, b) => a[1].timestamp - b[1].timestamp); // oldest first
          while (getStorageSize(images) > LOCAL_STORAGE_LIMIT_BYTES && sorted.length > 0) {
            const [oldestId] = sorted.shift()!;
            console.info(`Removing oldest cached image: ${oldestId}`);
            delete images[oldestId];
          }
        }

        set({ images });
      },
    }),
    {
      name: "property-image-cache",
      // storage: default localStorage
    }
  )
);