
export const createExpiringStorage = (maxAgeMs: number) => {
  return {
    getItem: (name: string): string | null => {
      const raw = localStorage.getItem(name);
      if (!raw) return null;

      try {
        const parsed = JSON.parse(raw);

        if (parsed._ts && Date.now() - parsed._ts > maxAgeMs) {
          localStorage.removeItem(name);
          return null;
        }
        return parsed.state;
      } catch {
        return null;
      }
    },

    setItem: (name: string, value: string): void => {
      const data = {
        state: value, // ✅ 保持原始字符串
        _ts: Date.now(),
      };
      localStorage.setItem(name, JSON.stringify(data));
    },

    removeItem: (name: string): void => {
      localStorage.removeItem(name);
    },
  };
};