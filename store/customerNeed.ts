import { create } from "zustand";
import { CustomerNeedProps } from "@/lib/definitions";

export const useCustomerNeedStore = create<CustomerNeedStore>((set) => ({
  needs: [],

  setNeeds: (needs: CustomerNeedProps[]) => set({ needs }),
  addNeed: (need: CustomerNeedProps) => set((state) => ({ needs: [...state.needs, need] })),
  updateNeed: (need: CustomerNeedProps) => set((state) => ({ needs: state.needs.map((n) => n.id === need.id ? need : n) })),
  removeNeed: (needId: string) => set((state) => ({ needs: state.needs.filter((n) => n.id !== needId) })),
}));


interface CustomerNeedStore {
  needs: CustomerNeedProps[];

  addNeed: (need: CustomerNeedProps) => void;
  setNeeds: (needs: CustomerNeedProps[]) => void;
  updateNeed: (need: CustomerNeedProps) => void;
  removeNeed: (needId: string) => void;
}
