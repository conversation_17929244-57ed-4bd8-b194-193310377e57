import { create } from "zustand";
import { persist } from "zustand/middleware";
import { createExpiringStorage } from "./utility/createExpiringStorage";

interface SearchHistoryItem {
  queryParams: { [key: string]: string };
  timestamp: number;
}

interface SearchHistoryStore {
  history: SearchHistoryItem[];
  addHistory: (item: SearchHistoryItem) => void;
  clearHistory: () => void;
}

// ✅ 新的比较函数：转成URLSearchParams字符串再比较
const serializeQueryParams = (params: { [key: string]: string }) => {
  return new URLSearchParams(params).toString();
};

export const useSearchHistoryStore = create<SearchHistoryStore>()(
  persist(
    (set, get) => ({
      history: [],
      addHistory: (item) =>
        set((state) => {
          const serializedNew = serializeQueryParams(item.queryParams);

          const alreadyExists = state.history.some((historyItem) => {
            const serializedOld = serializeQueryParams(historyItem.queryParams);
            return serializedOld === serializedNew;
          });

          if (alreadyExists) {
            console.info("✅ 该搜索条件已存在历史中，不再重复保存");
            return { history: state.history };
          }

          const newHistory = [item, ...state.history].slice(0, 50); // 最多50条
          return { history: newHistory };
        }),
      clearHistory: () => set({ history: [] }),
    }),
    {
      name: "search-history",
      storage: createExpiringStorage(7 * 24 * 60 * 60 * 1000) as any, // 7天过期
    }
  )
);