import { create } from "zustand";
import { UserLambdaRecordBidProps } from "@/lib/definitions/userLambdaRecordBid";

interface UserLambdaRecordBidStore {
  bids: UserLambdaRecordBidProps[];
  setBids: (bids: UserLambdaRecordBidProps[]) => void;
  updateBid: (bid: UserLambdaRecordBidProps) => void;
}

export const useUserLambdaRecordBidStore = create<UserLambdaRecordBidStore>((set) => ({
  bids: [],
  setBids: (bids) => set({ bids }),
  updateBid: (bid) => set((state) => ({ bids: state.bids.map((b) => b.id === bid.id ? bid : b) })),
}));
