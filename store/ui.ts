import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { createExpiringStorage } from './utility/createExpiringStorage';
import { TllUserProps } from '@/lib/definitions/tllUser';
interface UIState {
  currentUserFull: TllUserProps | null;
  setCurrentUserFull: (user: TllUserProps) => void;

  bidSheetOpen: boolean;
  setBidSheetOpen: (open: boolean) => void;

  notification: any;
  setNotification: (notification: any) => void;

  selectedProejctIdForKensetsu: string | null;
  setSelectedProejctIdForKensetsu: (id: string | null) => void;
}

export const useUIStore = create(
  persist(
    (set) => ({
      currentUserFull: null,
      setCurrentUserFull: (user: TllUserProps) => set({ currentUserFull: user }),

      bidSheetOpen: false,
      setBidSheetOpen: (open: boolean) => set({ bidSheetOpen: open }),

      notification: null,
      setNotification: (notification: any) => set({ notification }),

      selectedProejctIdForKensetsu: null,
      setSelectedProejctIdForKensetsu: (id: string | null) => set({ selectedProejctIdForKensetsu: id }),
    } as UIState),
    {
      name: "ui-storage",
      storage: createExpiringStorage(6 * 60 * 60 * 1000) as any, // 6 小时 expire
      partialize: (state: any) => ({
        currentLocale: state.currentLocale,
        notification: state.notification
      }),
    }
  )
)