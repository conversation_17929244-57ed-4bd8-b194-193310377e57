import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";
const createNextIntlPlugin = require("next-intl/plugin");
import createMDX from "@next/mdx";

const withNextIntl = createNextIntlPlugin();
const withMDX = createMDX({
  // Add markdown plugins here, as desired
});
import { execSync } from "child_process";

const nextConfig: NextConfig = {
  // Configure `pageExtensions` to include MDX files
  pageExtensions: ["js", "jsx", "mdx", "ts", "tsx"],
  // ✅ Adding the async headers() function
  // https://nextjs.org/docs/app/building-your-application/configuring/progressive-web-apps#8-securing-your-application
  async headers() {
    return [
      {
        source: "/(.*)", // Apply to all routes
        headers: [
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
        ],
      },
      {
        source: "/sw.js", // Service Worker specific headers
        headers: [
          {
            key: "Content-Type",
            value: "application/javascript; charset=utf-8",
          },
          {
            key: "Cache-Control",
            value: "no-cache, no-store, must-revalidate",
          },
          // {
          //   key: 'Content-Security-Policy',
          //   value: "default-src 'self'; script-src 'self'",
          // },
          {
            key: "Content-Security-Policy",
            value: `
              default-src 'self';
              script-src 'self';
              connect-src 'self' https://waqahdtjadldhstauanr.supabase.co;
              img-src 'self' https://waqahdtjadldhstauanr.supabase.co;
              style-src 'self' 'unsafe-inline';
            `
              .replace(/\s{2,}/g, " ")
              .trim(),
          },
        ],
      },
    ];
  },

  env: {
    // 🚀 Only execute git commands in CI or when explicitly needed
    COMMIT_HASH: process.env.CI
      ? execSync("git rev-parse HEAD").toString().trim().slice(0, 6)
      : "dev-build",
    COMMIT_DATE: process.env.CI
      ? execSync('git log -1 --format=%cd --date=format:"%Y-%m-%d %H:%M:%S"')
          .toString()
          .trim()
      : new Date().toISOString(),
  },
  eslint: {
    ignoreDuringBuilds: true, // Ignores ESLint errors when building
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com",
        port: "",
      },
      {
        protocol: "https",
        hostname: "placehold.co",
        port: "",
      },
      {
        protocol: "https",
        hostname: "placehold.jp",
        port: "",
      },
      {
        protocol: "https",
        hostname: "s3.ap-northeast-1.amazonaws.com",
        pathname: "/urbalytics.reins.downloads/**", // 支持的路径
      },
      {
        protocol: "https",
        hostname: "www3.nhk.or.jp",
        port: "",
      },
      {
        protocol: "https",
        hostname: "waqahdtjadldhstauanr.supabase.co",
        port: "",
      },
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
        port: "",
      },
    ],
    dangerouslyAllowSVG: true,
  },
  serverExternalPackages: ["pdf-parse"],
  experimental: {
    mdxRs: true, // 🚀 使用 Rust 编译器优化
    turbo: {
      resolveAlias: {
        canvas: "./empty-module.ts",
      },
    },
    serverActions: {
      bodySizeLimit: "30mb", // Increase the limit (adjust as needed), for uploading fieles
    },
    workerThreads: false, // Disable worker threads in dev mode
    // 🚀 Enable build optimizations
    optimizePackageImports: ["lucide-react", "@radix-ui/react-icons"],
  },

  // Suppress hydration warnings in development
  ...(process.env.NODE_ENV === "development" && {
    onDemandEntries: {
      // period (in ms) where the server will keep pages in the buffer
      maxInactiveAge: 25 * 1000,
      // number of pages that should be kept simultaneously without being disposed
      pagesBufferLength: 2,
    },
  }),

  // Additional React configuration
  reactStrictMode: false, // Disable strict mode to reduce hydration warnings in development

  // Suppress hydration warnings globally (use with caution)
  ...(process.env.NODE_ENV === "development" && {
    webpack: (config: any) => {
      config.resolve.alias = {
        ...config.resolve.alias,
        // Suppress hydration warnings in development
        "react-dom$": "react-dom/profiling",
        "scheduler/tracing": "scheduler/tracing-profiling",
      };
      return config;
    },
  }),

  // Not sure if this works
  // webpack: (config: any, { isServer }: any) => {
  //   if (isServer) {
  //     config.externals = [...(config.externals || []), "require-in-the-middle"];
  //   }
  //   return config;
  // },
};

export default withSentryConfig(withNextIntl(withMDX(nextConfig)), {
  // For all available options, see:
  // https://www.npmjs.com/package/@sentry/webpack-plugin#options

  org: "tan-chen",
  project: "urbalytics",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // 🚀 Optimize source map upload for faster builds
  widenClientFileUpload: process.env.CI ? true : false, // Only in CI

  // 🚀 Skip source map upload in development builds
  sourcemaps: {
    disable: !process.env.CI,
  },

  // Automatically annotate React components to show their full name in breadcrumbs and session replay
  reactComponentAnnotation: {
    enabled: process.env.CI ? true : false, // Only in CI
  },

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  tunnelRoute: "/monitoring",

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: process.env.CI ? true : false, // Only in CI
});
