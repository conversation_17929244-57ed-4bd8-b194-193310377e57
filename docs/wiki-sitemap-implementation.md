# Wiki Sitemap 实施文档

## 📋 概述

成功实施了自动获取所有 wiki 子路由并将它们添加到 sitemap 的功能。

## 🔧 实施的功能

### 1. Wiki URL 生成函数
位置：`next-sitemap.config.js`

```javascript
async function getWikiUrlsForAllLocales() {
  const wikiUrls = [];
  const addedPaths = new Set(); // 防止重复添加
  const wikiDir = path.join(process.cwd(), 'app/wiki');
  
  const addWikiUrl = (pagePath, priority = 0.6) => {
    if (addedPaths.has(pagePath)) return;
    
    addedPaths.add(pagePath);
    
    // 添加基础 wiki 页面
    wikiUrls.push({
      loc: `/wiki/${pagePath}`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority
    });
    
    // 添加多语言版本
    wikiUrls.push({
      loc: `/wiki/${pagePath}?lang=en`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority * 0.8
    });
    
    wikiUrls.push({
      loc: `/wiki/${pagePath}?lang=zh`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority * 0.8
    });
  };
  
  // ... 实现逻辑
}
```

### 2. 功能特性

#### ✅ 自动发现 Wiki 页面
- 读取 `app/wiki/_meta.json` 配置文件
- 扫描 wiki 目录下的所有子目录
- 检查 `page.mdx` 文件是否存在

#### ✅ 多语言支持
- 为每个 wiki 页面生成 3 个版本：
  - 日语（默认）：`/wiki/page-name`
  - 英语：`/wiki/page-name?lang=en`
  - 中文：`/wiki/page-name?lang=zh`

#### ✅ 防重复机制
- 使用 `Set` 数据结构防止重复添加相同路径
- 避免 meta 配置和目录扫描的重复

#### ✅ 优先级设置
- 主要页面：优先级 0.6
- 子页面：优先级 0.5
- 多语言版本：主页面优先级 × 0.8

#### ✅ 错误处理
- 忽略隐藏页面（`display: "hidden"`）
- 忽略 index 页面
- 处理文件访问错误

### 3. 配置文件

#### `config/sitemapPages.js`
```javascript
const staticPages = [
  // 主要功能页面
  { loc: '/', priority: 1.0, changefreq: 'daily' },
  { loc: '/blog', priority: 0.8, changefreq: 'daily' },
  { loc: '/wiki', priority: 0.6, changefreq: 'weekly' },
  // ... 更多页面
];
```

#### `utils/sanityClient.js`
```javascript
function createSanityClient() {
  return createClient({
    projectId: 'gopsma22',
    dataset: "production",
    apiVersion: "2024-01-01",
    useCdn: process.env.NODE_ENV === 'production',
    token: process.env.SANITY_TOKEN,
  });
}
```

### 4. 测试工具

#### `scripts/test-wiki-sitemap.js`
- 独立测试脚本，验证 wiki URL 生成功能
- 提供详细的调试信息
- 统计生成的 URL 数量和语言分布

## 📊 生成的 Sitemap 示例

```xml
<url>
  <loc>http://localhost:3000/wiki/test</loc>
  <lastmod>2025-06-30T06:56:12.960Z</lastmod>
  <changefreq>weekly</changefreq>
  <priority>0.6</priority>
</url>
<url>
  <loc>http://localhost:3000/wiki/test?lang=en</loc>
  <lastmod>2025-06-30T06:56:12.960Z</lastmod>
  <changefreq>weekly</changefreq>
  <priority>0.48</priority>
</url>
<url>
  <loc>http://localhost:3000/wiki/test?lang=zh</loc>
  <lastmod>2025-06-30T06:56:12.960Z</lastmod>
  <changefreq>weekly</changefreq>
  <priority>0.48</priority>
</url>
```

## 🚀 使用方法

### 1. 添加新的 Wiki 页面
1. 在 `app/wiki/_meta.json` 中添加页面配置：
   ```json
   {
     "new-page": {"title": "New Page Title"}
   }
   ```

2. 创建页面文件：
   ```
   app/wiki/new-page/page.mdx
   ```

3. 重新生成 sitemap：
   ```bash
   npx next-sitemap
   ```

### 2. 测试 Wiki URL 生成
```bash
node scripts/test-wiki-sitemap.js
```

### 3. 生成完整 Sitemap
```bash
npx next-sitemap
```

## 📈 SEO 效果

### ✅ 已实现的优势
1. **完整的页面索引** - 所有 wiki 页面都会被搜索引擎发现
2. **多语言 SEO** - 支持日语、英语、中文的独立索引
3. **自动化维护** - 新增页面自动包含在 sitemap 中
4. **优先级优化** - 根据页面重要性设置不同优先级
5. **更新频率指导** - 告诉搜索引擎页面更新频率

### 📊 统计数据
- **当前 Wiki 页面数**：3 个（test, getting-started, investment-basics）
- **生成的 URL 总数**：9 个（每个页面 3 种语言）
- **语言分布**：日语 3 个，英语 3 个，中文 3 个

## 🔧 维护说明

### 定期任务
1. **添加新页面后重新生成 sitemap**
2. **检查 sitemap 文件大小**（当前约 733 行）
3. **验证 Google Search Console 中的 sitemap 状态**

### 故障排除
1. **页面未出现在 sitemap 中**：
   - 检查 `_meta.json` 配置
   - 确认 `page.mdx` 文件存在
   - 运行测试脚本验证

2. **重复 URL**：
   - 检查防重复机制是否正常工作
   - 查看测试脚本输出的警告信息

3. **优先级异常**：
   - 检查 `getPathPriority` 函数
   - 验证多语言优先级计算（× 0.8）

## 🎯 下一步改进

1. **动态优先级** - 根据页面访问量调整优先级
2. **最后修改时间** - 使用文件修改时间作为 lastmod
3. **分类支持** - 支持 wiki 页面的分类和标签
4. **图片 sitemap** - 为 wiki 页面中的图片生成专门的 sitemap
