"use server";

import { ActionResponse } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { supabase } from "@/lib/instances/supabase";
import cuid from 'cuid';
import { getMaterialsFolderContentsInFolder, uploadFileToPropertyMaterialsBucketFromCron } from "./supabaseNode";
import mime from "mime-types";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
const supabaseFolderNames = {
  propertyMaterials: "property-materials",
  propertyMaterialsPublic: "property-materials-public",
  aiContent: "ai-content",
}

export async function uploadImageToSupabase(file: File): Promise<ActionResponse> {
  try {
    const fileName = `exported_${Date.now()}.png`;

    // Convert File to ArrayBuffer for Supabase Upload
    const arrayBuffer = await file.arrayBuffer();

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from(supabaseFolderNames.aiContent) // Your bucket name
      .upload(fileName, arrayBuffer, { contentType: "image/png" });

    if (error) throw error;

    // Get Public URL
    const publicUrl = `${process.env.SUPABASE_URL}/storage/v1/object/public/${supabaseFolderNames.aiContent}/${fileName}`;

    console.log("Uploaded image to Supabase:", publicUrl);
    return {
      success: true,
      data: publicUrl,
    }
  } catch (error) {
    console.error("Upload error:", error);
    return {
      success: false,
      message: "Upload failed"
    }
  }
}

export async function uploadImageToSupabaseAiContentFromUrl({ url }: { url: string }): Promise<ActionResponse> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error("无法下载图像");
    }

    const arrayBuffer = await response.arrayBuffer();
    const fileName = `exported_${Date.now()}.png`;

    // Upload to Supabase Storage
    const { data, error } = await supabase.storage
      .from(supabaseFolderNames.aiContent) // Your bucket name
      .upload(fileName, arrayBuffer, { contentType: "image/png" });

    if (error) throw error;

    // Get Public URL
    const publicUrl = `${process.env.SUPABASE_URL}/storage/v1/object/public/${supabaseFolderNames.aiContent}/${fileName}`;

    console.log("🔥 Uploaded image to Supabase:", publicUrl);
    return {
      success: true,
      data: publicUrl,
    }
  } catch (error) {
    console.error("🚨 Upload error:", error);
    return {
      success: false,
      message: "Upload failed"
    }
  }
}

export async function uploadImageToSupabasePublicFromUrl({ url, recordId }: { url: string, recordId: string }): Promise<ActionResponse> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error("无法下载图像");
    }

    const arrayBuffer = await response.arrayBuffer();
    const fileName = `exported_${Date.now()}.png`;

    // Upload to Supabase Storage
    // const { data, error } = await supabase.storage
    //   .from(supabaseFolderNames.propertyMaterialsPublic) // Your bucket name
    //   .upload(fileName, arrayBuffer, { contentType: "image/png" });

    const mimeType = mime.lookup(fileName) || "application/octet-stream";

    const res = await uploadFileToPropertyMaterialsBucketFromCron({
      file: {
        blob: new Blob([arrayBuffer], {
          type: mimeType
        }), name: fileName
      }, recordId: recordId, isPublic: true
    });

    if (res.success) {
      return {
        success: true,
        data: res.data,
      }
    } else {
      logger.error("🚨 Upload error:", res.message);

      sendLark({
        message: `🚨 Upload error: ${res.message}`,
        url: LARK_URLS.USER_ACTIVITY_CHANNEL,
      });

      return {
        success: false,
        message: "Upload failed"
      }
    }
  } catch (error) {
    console.error("🚨 Upload error:", error);
    return {
      success: false,
      message: "Upload failed"
    }
  }
}

export async function uploadFileToPropertyMaterialsBucket(file: File, recordId: string, uploadUserId: string): Promise<ActionResponse> {
  try {
    const newFileName = cuid(); // 生成新的文件名
    const filePath = `${recordId}/${newFileName}`; // 使用新的文件名
    logger.info("🔥 新的文件名:", newFileName, "文件路径:", filePath);

    const folderContents = await getMaterialsFolderContentsInFolder({ folderName: recordId, bucketKey: "propertyMaterials" });
    if (folderContents.success && folderContents.data.length === 0) {
      logger.info("🔥 文件夹为空，上传 .keep 文件");
      await supabase.storage.from(supabaseFolderNames.propertyMaterials).upload(`${recordId}/.keep`, new Blob([""]), {
        contentType: "text/plain",
        upsert: false,
      });
    }

    const { data, error } = await supabase.storage.from(supabaseFolderNames.propertyMaterials).upload(filePath, file, {
      upsert: false,
    });

    logger.info("🔥 File uploading.. result is:", data);

    if (error || !data) {
      return {
        success: false,
        message: "Upload failed"
      }
    } else {
      // 保存记录到 nameMapping
      await prisma.propertyMaterialNameMapping.create({
        data: {
          id: newFileName,
          name: file.name,
          recordId: recordId,
          uploadUserId: uploadUserId,
        },
      });
    }

    return {
      success: true,
      data: data,
    }
  } catch (error) {
    console.error("🚨 Upload error:", error);
    return {
      success: false,
      message: "Upload failed"
    }
  }
};

// export async function getFileLinksFromSupabaseBucket(lambdaRecordId: string): Promise<ActionResponse> {
//   try {
//     // https://waqahdtjadldhstauanr.supabase.co/storage/v1/object/public/property-record-cm7db6f8g001hju03kxxgvjpl

//     let BUCKET_NAME = "property-materials";

//     const { data, error } = await supabase.storage.from(BUCKET_NAME).list(`${lambdaRecordId}/`);

//     if (error) {
//       console.error("🚨 获取文件列表时出错:", error);
//       return {
//         success: false,
//         message: "无法获取文件列表"
//       };
//     }

//     return {
//       success: true,
// data: data.map(d => {
//   return {
//     name: d.name,
//     updatedAt: d.updated_at,
//     size: d.metadata?.size,
//     type: d.metadata?.mimetype,
//     fileSize: d.metadata?.size,
//     link: `${process.env.SUPABASE_URL}/storage/v1/object/public/${BUCKET_NAME}/${lambdaRecordId}/${d.name}`,
//   }
// }),
//     };
//   } catch (error) {
//     console.error("🚨 error", error);
//     return {
//       success: false,
//       message: "无法获取文件列表"
//     };
//   }
// }

