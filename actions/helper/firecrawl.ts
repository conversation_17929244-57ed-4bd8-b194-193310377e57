import FirecrawlApp, { ScrapeResponse } from '@mendable/firecrawl-js';

export async function fetchContentWithFirecrawl(url: string): Promise<any> {
  try {
    const app = new FirecrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY });

    // Scrape a website:
    const scrapeResult = await app.scrapeUrl(url, { formats: ['markdown', 'html'] }) as ScrapeResponse;

    if (!scrapeResult.success) {
      throw new Error(`Failed to scrape: ${scrapeResult.error}`)
    }

    console.log(scrapeResult)
    return scrapeResult as ScrapeResponse;
  } catch (error) {
    console.error("Error fetching content with Firecrawl:", error);
    return {
      success: false,
      data: null,
      message: 'Failed to use firecrawl to get hotspots',
    };
  }
}
