"use server";

import path from "path";
import mime from "mime-types";
import { ActionResponse, PropertyMaterialNameMappingProps } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import cuid from 'cuid';
import { supabase } from "@/lib/instances/supabase";
import { prisma } from "@/lib/prisma";

const supabaseFolderNames = {
  propertyMaterials: "property-materials",
  propertyMaterialsPublic: "property-materials-public",
  aiContent: "ai-content",
}
// FIXME: this is a node function, FILE does not exist (only a brower type of object)
import fs from 'fs/promises'

export async function localPathToBlob(localPath: string): Promise<ActionResponse<{ blob: Blob, name: string }>> {
  try {
    const stats = await fs.stat(localPath)
    if (!stats.isFile()) {
      return {
        success: false,
        message: `Path is not a file: ${localPath}`
      }
    }

    const fileBuffer = await fs.readFile(localPath);
    const fileName = path.basename(localPath);
    const mimeType = mime.lookup(localPath) || "application/octet-stream";

    return {
      success: true,
      data: {
        blob: new Blob([fileBuffer], { type: mimeType }),
        name: fileName,
      }
    };
  } catch (error) {
    logger.error("🚨 Error in localPathToBlob:", error);
    return {
      success: false,
      message: JSON.stringify(error)
    };
  }
}

export async function getMaterialsFolderContentsInFolder({ folderName, bucketKey }: { folderName: string, bucketKey: keyof typeof supabaseFolderNames }): Promise<ActionResponse<any[]>> {
  if (!supabaseFolderNames[bucketKey]) {
    throw new Error("Invalid bucket name");
  }

  const { data, error } = await supabase.storage.from(supabaseFolderNames[bucketKey]).list(folderName);

  let allFileNames = data?.map(d => d.name) || [];

  let mappedFileNames = await prisma.propertyMaterialNameMapping.findMany({
    where: {
      id: {
        in: allFileNames,
      },
    },
    include: {
      tllUser: {
        select: {
          name: true,
        },
      },
    },
  }) as PropertyMaterialNameMappingProps[];

  return !error && data.length > 0 ? {
    success: true,
    data: data.sort((a, b) => b.updated_at.localeCompare(a.updated_at)).map(d => {
      return {
        name: d.name,
        originalName: mappedFileNames.find((m: any) => m.id === d.name)?.name,
        updatedAt: d.updated_at,
        size: d.metadata?.size,
        type: d.metadata?.mimetype,
        fileSize: d.metadata?.size,
        uploadUserId: mappedFileNames.find((m: any) => m.id === d.name)?.uploadUserId,
        link: `${process.env.SUPABASE_URL}/storage/v1/object/public/${supabaseFolderNames[bucketKey]}/${folderName}/${d.name}`,
        tllUser: mappedFileNames.find((m: any) => m.id === d.name)?.tllUser,
      }
    }),
  } : {
    success: false,
    message: JSON.stringify(error)
  };
}

export async function uploadFileToPropertyMaterialsBucketFromCron({ file, recordId, isPublic = false }: {
  file: {
    blob: Blob,
    name: string,
  },
  recordId: string,
  isPublic?: boolean
}): Promise<ActionResponse> {
  try {
    const newFileName = cuid(); // 生成新的文件名


    const filePath = `${recordId}/${newFileName}`; // 使用新的文件名
    logger.info("🔥 新的文件名:", newFileName, "文件路径:", filePath);

    let bucketName = isPublic ? supabaseFolderNames.propertyMaterialsPublic : supabaseFolderNames.propertyMaterials;
    let bucketKey = isPublic ? "propertyMaterialsPublic" : "propertyMaterials";

    const folderContents = await getMaterialsFolderContentsInFolder({ folderName: recordId, bucketKey: bucketKey as keyof typeof supabaseFolderNames });
    if (folderContents.success && folderContents.data.length === 0) {
      logger.info("🔥 文件夹为空，上传 .keep 文件");
      await supabase.storage.from(bucketName).upload(`${recordId}/.keep`, new Blob([""]), {
        contentType: "text/plain",
        upsert: false,
      });
    }

    // Dat acontains fullPath
    const { data, error } = await supabase.storage.from(bucketName).upload(filePath, file.blob as any, {
      upsert: false,
    });
    logger.info("🔥 File uploading.. result is:", data);

    if (error || !data) {
      return {
        success: false,
        message: JSON.stringify(error)
      }
    } else {
      // 保存记录到 nameMapping
      await prisma.propertyMaterialNameMapping.create({
        data: {
          id: newFileName,
          name: file.name,
          recordId: recordId,
        },
      });
    }

    return {
      success: true,
      data,
    }
  } catch (error) {
    console.error("🚨 Upload error:", error);
    return {
      success: false,
      message: JSON.stringify(error)
    }
  }
};

export async function deleteFileFromPropertyMaterialsBucket(fileLink: string): Promise<ActionResponse> {
  logger.info("🔥 删除文件:", fileLink);
  let reducedFileLink = fileLink.replace(`${process.env.SUPABASE_URL}/storage/v1/object/public/${supabaseFolderNames.propertyMaterials}/`, "");
  logger.info("🔥 删除文件:", reducedFileLink);

  const { data, error } = await supabase.storage.from(supabaseFolderNames.propertyMaterials).remove([reducedFileLink]);
  return {
    success: true,
  }
};