"use server";

import { ActionResponse } from "@/lib/definitions";
import * as cheerio from "cheerio";


// TODO: this perform better than fetchArticleContent
export async function fetchtDataFromAllBody(url: string) {
  try {
    const response = await fetch(url, {
      headers: {
        "User-Agent": "Mozilla/5.0",
      },
    });
    const data = await response.text();

    // Load HTML into cheerio
    const $ = cheerio.load(data);
    let contentArray: any[] = [];
    // Extract relevant data from the page
    $("body *").each((i, el) => {
      const tag = $(el).prop("tagName")?.toLowerCase();
      const text = $(el).text().trim();

      if (text) {
        contentArray.push({ tag, text });
      }
    });

    // Create a JSON object
    const jsonData = {
      url,
      extractedContent: contentArray,
    };

    return jsonData;
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
}

export async function fetchArticleContent(url: string) {
  try {
    const response = await fetch(url);
    const data = await response.text();
    const $ = cheerio.load(data);

    // Extract text content from article <p> tags
    let contentArray: string[] = [];
    $("p").each((i, el) => {
      const text = $(el).text().trim();
      if (text) contentArray.push(text);
    });

    return contentArray.join("\n\n"); // Combine into readable format
  } catch (error) {
    console.error("Error fetching article:", error);
    return null;
  }
}

export async function fetchMetadataFromUrlAction(url: string): Promise<ActionResponse> {
  if (!url) return { success: false, message: "Missing URL" };

  try {
    // Fetch the page content using fetch
    const response = await fetch(url, { method: 'GET' });
    const data = await response.text();

    // Load the HTML into Cheerio
    const $ = cheerio.load(data);

    // Extract metadata
    const metadata = {
      title: $("title").text() || null,
      description: $('meta[name="description"]').attr("content") || null,
      ogTitle: $('meta[property="og:title"]').attr("content") || null,
      ogDescription: $('meta[property="og:description"]').attr("content") || null,
      ogImage: $('meta[property="og:image"]').attr("content") || null,
      favicon: $('link[rel="icon"]').attr("href") || null,
    };

    return { success: true, data: metadata };
  } catch (error) {
    console.error("Error fetching metadata:", error);
    return { success: false, message: "Failed to fetch metadata" };
  }
}
