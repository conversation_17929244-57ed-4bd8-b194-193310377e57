"use server"
// pages/api/wechat-token.ts
import { NextRequest, NextResponse } from "next/server";

const WECHAT_APPID = process.env.WECHAT_APP_ID;
const WECHAT_SECRET = process.env.WECHAT_APP_SECRET;

export async function getWechatTokenAction() {
  const response = await fetch(
    `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WECHAT_APPID}&secret=${WECHAT_SECRET}`
  );
  const data = await response.json();

  console.log(data);
  return data.access_token;
}

export async function pushWechatMessageAction(req: NextRequest, res: NextResponse) {
  if (req.method !== "POST") {
    return NextResponse.json({ error: "Method Not Allowed" }, { status: 405 });
  }

  try {
    const accessToken = await getWechatTokenAction();
    if (!accessToken) {
      return NextResponse.json({ error: "Failed to get access token" }, { status: 400 });
    }

    const body = await req.json();

    const messageData = {
      articles: [
        {
          title: body.title || "Default Title",
          thumb_media_id: body.thumb_media_id || "YOUR_MEDIA_ID",
          author: body.author || "Unknown",
          digest: body.description || "No description",
          content: body.content || "Default content",
          content_source_url: body.url || "",
          show_cover_pic: 1
        }
      ]
    };

    const response = await fetch(
      `https://api.weixin.qq.com/cgi-bin/media/uploadnews?access_token=${accessToken}`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(messageData)
      }
    );

    const result = await response.json();
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json({ error: "Failed to push message" }, { status: 500 });
  }
}

async function uploadImage(filePath: string, access_token: string) {
  const accessToken = await getWechatTokenAction();

  const formData = new FormData();
  formData.append("media", filePath);

  const uploadResponse = await fetch(
    `https://api.weixin.qq.com/cgi-bin/media/upload?access_token=${access_token}&type=thumb`,
    { method: "POST", body: formData }
  );

  const uploadData = await uploadResponse.json();
  console.log("✅ Uploaded Image:", uploadData);
  return uploadData.media_id; // This is thumb_media_id
}