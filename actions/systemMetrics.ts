"use server";

import { prisma } from "@/lib/prisma";

import { SystemMetricProps, SystemUserActivityProps } from "@/lib/definitions/system";
import { ActionResponse } from "@/lib/definitions";
import { SystemMetricKeyEnum } from "@prisma/client";
import dayjs from "dayjs";
import { sumBy } from "lodash-es";

export async function getSystemMetricsAction(): Promise<ActionResponse<SystemMetricProps[]>> {
  const metrics = await prisma.systemMetric.findMany();

  return {
    data: metrics,
    success: true,
    message: "Metrics fetched successfully",
  };
}

export async function getSystemMetricsChangeRecordsDetailedAction(): Promise<ActionResponse<SystemMetricProps[]>> {
  const priceChangeBySource = await prisma.tllUserLambdaRecordPriceChange.groupBy({
    by: ['source'],
    _count: true,
  });

  return {
    data: priceChangeBySource,
    success: true,
    message: "Metrics fetched successfully",
  };
}

export async function getDauMetricsAction(): Promise<ActionResponse<SystemUserActivityProps[]>> {
  // const MULTIPLIER = 5.11;

  const dauMetrics = await prisma.systemMetric.findMany({
    where: {
      key: {
        in: [
          SystemMetricKeyEnum.TOTAL_USER_DAU_COUNT,
          SystemMetricKeyEnum.TOTAL_USER_WAU_COUNT,
          SystemMetricKeyEnum.TOTAL_USER_MAU_COUNT,
          SystemMetricKeyEnum.TOTAL_USER_COUNT,
        ]
      },
      recordDate: {
        gte: dayjs().subtract(1, "month").toDate(),
      },
    },
  })

  // let totalDau = 0;
  // dauMetrics.forEach((metric) => {
  //   totalDau += parseInt(metric.value);
  // });

  return {
    data: dauMetrics,
    success: true,
    message: "Metrics fetched successfully",
  };
}

export async function getMetricsForDB(): Promise<ActionResponse<SystemUserActivityProps[]>> {
  const MULTIPLIER = 5.11;

  let yday = dayjs().subtract(1, "day").toDate();
  const recordsMetrics = await prisma.systemMetric.findMany({
    where: {
      key: {
        in: [
          SystemMetricKeyEnum.TOTAL_PROPERTY_BUILDING_RECORD_COUNT,
          SystemMetricKeyEnum.TOTAL_PROPERTY_HOUSE_RECORD_COUNT,
          SystemMetricKeyEnum.TOTAL_PROPERTY_MANSION_RECORD_COUNT,
          SystemMetricKeyEnum.TOTAL_PROPERTY_LAND_RECORD_COUNT,
        ],
      },
      recordDate: yday,
    },
  })

  let total = 0;
  recordsMetrics.forEach((metric) => {
    total += parseInt(metric.value);
  });


  const changeMetrics = await prisma.systemMetric.findMany({
    where: {
      key: {
        in: [
          SystemMetricKeyEnum.TOTAL_PROPERTY_CHANGE_RECORD_COUNT,
          SystemMetricKeyEnum.TOTAL_PROPERTY_BUILDING_RENT_RECORD_COUNT,
          SystemMetricKeyEnum.TOTAL_PROPERTY_HOUSE_RENT_RECORD_COUNT,
          SystemMetricKeyEnum.TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT,
        ],
      },
      recordDate: yday,
    },
  });
  let totalChange = 0;
  changeMetrics.forEach((metric) => {
    totalChange += parseInt(metric.value);
  });


  const valuationMetrics = await prisma.systemMetric.findMany({
    where: {
      key: SystemMetricKeyEnum.TOTAL_VALUATION_RECORD_COUNT,
      recordDate: yday,
    },
  })

  let totalValuation = 0;
  valuationMetrics.forEach((metric) => {
    totalValuation += parseInt(metric.value);
  });

  return {
    data: {
      recordsMetrics: total * MULTIPLIER,
      changeMetrics: totalChange * MULTIPLIER,
      valuationMetrics: totalValuation * MULTIPLIER,
    },
    success: true,
    message: "Metrics fetched successfully",
  };
}