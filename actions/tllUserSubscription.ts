"use server";

import Stripe from "stripe";
import { auth } from "@/lib/auth";
import { ActionResponse } from "@/lib/definitions";
import { prisma } from "@/lib/prisma";
import { TllUserProps, TllUserSubscriptionStatus } from "@/lib/definitions/tllUser";
import { logger } from "@/lib/logger";
import dayjs from "dayjs";
import { TllUserSubscription, TllUserSubscriptionInterval } from "@prisma/client";

const stripe = new Stripe(process.env.NODE_ENV === "production" ? process.env.STRIPE_SECRET_KEY! : process.env.STRIPE_SECRET_TEST_KEY!, {
  apiVersion: "2025-02-24.acacia",
});

enum TllUserSubscriptionPlan {
  PLUS_MONTHLY = "PLUS_MONTHLY",
  PLUS_YEARLY = "PLUS_YEARLY",
  PRO_MONTHLY = "PRO_MONTHLY",
  PRO_YEARLY = "PRO_YEARLY",
}

const priceMap = {
  [TllUserSubscriptionPlan.PLUS_MONTHLY]: process.env.NODE_ENV === "production" ? "price_1R7cLEFNC0GhZ2NWUCWDu5ML" : "price_1RF6zjFNC0GhZ2NW3mXQWbtS", // "", // "price_1RF8F3FNC0GhZ2NW0NHyHY8F" as test price in prod, price_1RF6zjFNC0GhZ2NW3mXQWbtS is in test
  [TllUserSubscriptionPlan.PLUS_YEARLY]: process.env.NODE_ENV === "production" ? "price_1RFwNHFNC0GhZ2NW5Lo1Uq5C" : "price_1RF6zjFNC0GhZ2NW6isdzzOV",
  [TllUserSubscriptionPlan.PRO_MONTHLY]: process.env.NODE_ENV === "production" ? "price_1R7cLtFNC0GhZ2NW84NJVS9Y" : "price_1RF6zjFNC0GhZ2NW3mXQWbtS",
  [TllUserSubscriptionPlan.PRO_YEARLY]: process.env.NODE_ENV === "production" ? "price_1RFwLpFNC0GhZ2NWrycDzRq3" : "price_1RF6zjFNC0GhZ2NW6isdzzOV",
} as {
    [key in TllUserSubscriptionPlan]: string;
  };

const accessLevelMap: Record<TllUserSubscriptionPlan, number> = {
  [TllUserSubscriptionPlan.PLUS_MONTHLY]: 10,
  [TllUserSubscriptionPlan.PLUS_YEARLY]: 10,
  [TllUserSubscriptionPlan.PRO_MONTHLY]: 20,
  [TllUserSubscriptionPlan.PRO_YEARLY]: 20,
} as {
    [key in TllUserSubscriptionPlan]: number;
  };

const subscriptionPlanMap: Record<TllUserSubscriptionPlan, string> = {
  [TllUserSubscriptionPlan.PLUS_MONTHLY]: "PLUS",
  [TllUserSubscriptionPlan.PLUS_YEARLY]: "PLUS",
  [TllUserSubscriptionPlan.PRO_MONTHLY]: "PRO",
  [TllUserSubscriptionPlan.PRO_YEARLY]: "PRO",
} as {
    [key in TllUserSubscriptionPlan]: string;
  };

export async function createCheckoutSession(planKey: TllUserSubscriptionPlan): Promise<ActionResponse<string | null>> {
  try {
    const currentSession = await auth();
    if (!currentSession?.user?.id) {
      return {
        success: false,
        message: "User not found",
      };
    }

    const currentUser = currentSession?.user;

    const user = await prisma.tllUser.findUnique({
      where: {
        id: currentUser.id,
      },
    }) as TllUserProps;

    let stripeCustomerId = user.stripeCustomerId;

    if (!stripeCustomerId && user.email) {
      logger.info("🔥createCheckoutSession: Creating new Stripe customer");
      const customer = await stripe.customers.create({
        name: user.name as string,
        email: user.email as string,
        // metadata: { supabase_user_id: user.id },
      }) as Stripe.Customer;

      stripeCustomerId = customer.id;

      await prisma.tllUser.update({
        where: { id: user.id },
        data: { stripeCustomerId },
      });
    }


    const priceId = priceMap[planKey as keyof typeof priceMap];
    if (!priceId) {
      return {
        success: false,
        message: "Invalid plan key",
      };
    }

    const session = await stripe.checkout.sessions.create({
      mode: "subscription",
      customer: stripeCustomerId,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: process.env.NEXT_PUBLIC_BASE_URL + "/my/billing?status=success&session_id={CHECKOUT_SESSION_ID}",
      cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/my/billing?status=cancel`,
      metadata: {
        newPlan: subscriptionPlanMap[planKey],
        newAccessLevel: accessLevelMap[planKey],
      },
    });


    return {
      success: true,
      data: session.url,
    };
  } catch (error: any) {
    console.error(error);
    return {
      success: false,
      message: error.message,
    };
  }
}

export async function createStripePortalSession(): Promise<ActionResponse<string | null>> {
  const session = await auth();
  if (!session?.user?.id) {
    return {
      success: false,
      message: "User not found",
    };
  }

  const currentUser = session?.user;

  const user = await prisma.tllUser.findUnique({
    where: {
      id: currentUser.id,
    },
  }) as TllUserProps;

  // const { data: userData } = await supabase
  //   .from("users")
  //   .select("stripe_customer_id")
  //   .eq("id", user.id)
  //   .single();

  if (!user?.stripeCustomerId) {
    return {
      success: false,
      message: "User not found",
    };
  }

  const stripeSession = await stripe.billingPortal.sessions.create({
    customer: user.stripeCustomerId,
    return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/my/account`,
  });

  return {
    success: true,
    data: stripeSession.url,
  };
}

// FIXME: this should be replaced with functional hooks after you have > 50 paid users // 
export async function validateSession({ sessionId }: { sessionId: string }): Promise<ActionResponse<string | null>> {
  try {
    if (!sessionId) {
      return { success: false, message: "Missing session_id" }
    }

    const session = await stripe.checkout.sessions.retrieve(sessionId)
    console.log("🔥session in retrived", session)

    if (session.payment_status !== "paid" || !session.subscription) {
      return { success: false, message: "Payment not completed" }
    }

    const stripeSubscription = await stripe.subscriptions.retrieve(session.subscription as string)
    const user = await prisma.tllUser.findFirst({
      where: { stripeCustomerId: session.customer as string }
    })

    if (!user) {
      return { success: false, message: "User not found" }
    }

    let findMatchingSubscription = await prisma.tllUserSubscription.findFirst({
      where: {
        userId: user.id,
        stripeSubscriptionId: stripeSubscription.id,
      }
    })

    if (findMatchingSubscription) {
      return { success: false, message: "Subscription already exists" }
    }

    await prisma.tllUser.update({
      where: { id: user.id },
      data: {
        accessLevel: Number(session.metadata?.newAccessLevel),
        subscriptionPlan: session.metadata?.newPlan as any,
        subscriptionStatus: TllUserSubscriptionStatus.ACTIVE,
      }
    })

    // Find other active subscriptions and update them to inactive
    await prisma.tllUserSubscription.updateMany({
      where: {
        userId: user.id,
        subscriptionStatus: TllUserSubscriptionStatus.ACTIVE,
      },
      data: {
        subscriptionStatus: TllUserSubscriptionStatus.CANCELED,
      }
    })

    console.log("🔥stripeSubscription", stripeSubscription)

    let interval = stripeSubscription.items.data[0].price.recurring?.interval as any;
    if (interval === "year") {
      interval = TllUserSubscriptionInterval.YEARLY;
    } else if (interval === "month") {
      interval = TllUserSubscriptionInterval.MONTHLY;
    } else {
      throw new Error("Invalid interval, value is: " + interval);
    }

    await prisma.tllUserSubscription.create({
      data: {
        userId: user.id,
        amount: stripeSubscription.items.data[0].price.unit_amount || 0,
        interval: interval,

        stripeSubscriptionId: stripeSubscription.id,
        subscriptionPlan: session.metadata?.newPlan as any,
        subscriptionStatus: TllUserSubscriptionStatus.ACTIVE,
        subscriptionStartAt: dayjs.unix(stripeSubscription.start_date).toDate(),
        subscriptionEndAt: dayjs.unix(stripeSubscription.current_period_end).toDate(),
        comments: {
          stripeData: stripeSubscription as any,
          sessionData: session as any,
        } as any
      }
    })

    return {
      success: true,
      message: "Session validated and created new user subscription",
      data: {
        newUserAccessLevel: Number(session.metadata?.newAccessLevel),
      }
    }
  } catch (error: any) {
    console.error(error);
    return {
      success: false,
      message: error.message,
    };
  }
}

export async function getUserSubscriptionStatus({ email }: { email: string }): Promise<ActionResponse<TllUserSubscriptionStatus[] | null>> {
  const user = await prisma.tllUser.findUnique({
    where: { email },
  }) as TllUserProps;

  if (!user) {
    return { success: false, message: "User not found" };
  }

  const userSubscription = await prisma.tllUserSubscription.findMany({
    where: { userId: user.id },
  }) as TllUserSubscription[];

  return {
    success: true,
    data: userSubscription,
  };
}
