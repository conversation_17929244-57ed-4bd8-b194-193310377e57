"use server"

import { prisma } from "@/lib/prisma";
import { AiNewsProps } from "@/lib/definitions";
import { ActionResponse } from "@/lib/definitions";

export async function getAiNewses(): Promise<ActionResponse<AiNewsProps[]>> {
  try {
    const aiNews = await prisma.aiNews.findMany({
      orderBy: {
        recommendationLevel: 'desc',
      },
    });
    return {
      success: true,
      data: aiNews,
    };
  } catch (error) {
    console.error("🚨 获取ai新闻时出错:", error);

    return {
      success: false,
      message: "Failed to get ai news",
    };
  }
}

export async function createAiNewses(aiNewses: AiNewsProps[]): Promise<ActionResponse<AiNewsProps[]>> {
  console.log('🔥createAiNewses for insertion', aiNewses);
  const createdNews: AiNewsProps[] = [];

  for (const news of aiNewses) {
    const existingNews = await prisma.aiNews.findUnique({
      where: { url: news.url }, // 假设 AiNewsProps 中有 url 字段
    });

    if (!existingNews) {
      createdNews.push(news);
    } else {
      console.log(`🔥 URL已存在，跳过: ${news.url}`);
    }
  }

  if (createdNews.length > 0) {
    try {
      const aiNews = await prisma.aiNews.createMany({
        data: createdNews,
      });

      return {
        success: true,
        data: aiNews,
      };
    } catch (error) {
      console.error("🚨 创建ai新闻时出错:", error);

      return {
        success: false,
        message: "Failed to create ai news",
      };
    }
  } else {
    return {
      success: true,
      data: [],
    };
  }
}
