// app/actions/proRegistrationRecord.ts
"use server";

import { prisma } from "@/lib/prisma";

export async function searchProRegistrationRecords({ keyword }: { keyword: string }) {
  if (!keyword) return [];

  return await prisma.proRegistrationRecord.findMany({
    where: {
      addressChiban: {
        contains: keyword,
        mode: "insensitive",
      },
    },
    // take: 100, // 限制返回数量
    orderBy: { recordDate: "desc" },
  });
}