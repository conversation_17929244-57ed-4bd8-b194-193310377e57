"use server";

import { prisma } from "@/lib/prisma";
import { GeoPostalCodeProps } from "@/lib/definitions";
import { ActionResponse } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { convertKeysToCamelCase } from "@/lib/prisma";
import dayjs from "dayjs";
import { getAveargePriceForType, getAverageAreaForType, getAverageLandAreaForType, getAverageRoiForType } from "@/lib/userLambdaRecord/summaryForType";
import { UserLambdaRecordType, UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { postalCodeCache, stationGroupCache } from "@/lib/instances/nodeCache";
import { auth } from "@/lib/auth";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { SystemReportViewHistoryRecordType } from "@/lib/definitions/system";

export const updateLatLngFromGoogleResult = async (res: any) => {
  let lat, lng, postalCode;

  if (res && res.length) {
    // The last one is ususally more precise match
    let addressFull = res[res.length - 1].formatted_address; // '日本、〒153-0063 東京都目黒区目黒１丁目２−９ シティハウス目黒',
    let postalCodeMatch = addressFull?.match(/\d{3}-\d{4}/g);
    if (postalCodeMatch?.length) {
      postalCode = parseInt(postalCodeMatch[0].replace("-", ""));
    }

    lat = res[res.length - 1].geometry.location.lat;
    lng = res[res.length - 1].geometry.location.lng;
  }

  if (lat && lng && postalCode) {
    const matchRecords = await prisma.geoPostalCode.findMany({
      where: {
        postalCode: postalCode.toString(),
        latitude: null,
        longitude: null,
      },
    });

    if (matchRecords.length) {
      for (const matchRecord of matchRecords) {
        await prisma.geoPostalCode.update({
          where: { id: matchRecord.id },
          data: { latitude: lat, longitude: lng },
        });
      }
    }
  }
}

export const getPostalCodeAction = async (postalCode: number): Promise<ActionResponse<GeoPostalCodeProps>> => {
  const session = await auth();
  const currentUser = session?.user;


  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "用户未登录",
    };
  }

  // TODO: do the quota check ehre too 
  // Also create record for the user (for pricing history)
  const searchHistoryExists = await prisma.systemReportViewHistory.findFirst({
    where: {
      recordType: SystemReportViewHistoryRecordType.POSTAL_CODE,
      userId: currentUser.id,
      postalCode: postalCode.toString(),
      isValid: true,
    },
  });

  if (!searchHistoryExists) {
    const dataToCreate = {
      recordType: SystemReportViewHistoryRecordType.POSTAL_CODE,
      userId: currentUser.id,
      postalCode: postalCode.toString(),
      viewDate: dayjsWithTz().toISOString(),
    }

    await prisma.systemReportViewHistory.create({
      data: dataToCreate,
    });
  }

  // FIXME: this should not have multipel fields
  const record = await prisma.geoPostalCode.findMany({
    where: {
      postalCode: postalCode.toString(),
    },
  });

  return {
    success: true,
    data: record,
  };
};

export const getRecordsWithInRange = async ({ topLeftLat, topLeftLng, bottomRightLat, bottomRightLng, type, recordType }: { topLeftLat: number, topLeftLng: number, bottomRightLat: number, bottomRightLng: number, type: "postalCode" | "stationGroupId", recordType?: UserLambdaRecordType }): Promise<ActionResponse<GeoPostalCodeProps>> => {
  const whereParams = {
    latitude: {
      gte: Math.min(topLeftLat, bottomRightLat),
      lte: Math.max(topLeftLat, bottomRightLat),
    },
    longitude: {
      gte: Math.min(topLeftLng, bottomRightLng),
      lte: Math.max(topLeftLng, bottomRightLng),
    },
  };

  const records = type === "postalCode" ? await prisma.geoPostalCode.findMany({
    where: whereParams,
  }) : await prisma.geoRailwayStationGroup.findMany({
    where: whereParams,
  });

  let diffRecordIds = [];
  let hitCacheIds = [];

  if (type === "postalCode") {
    diffRecordIds = records.filter((record: any) => record.postalCode && !postalCodeCache.get(record.postalCode as number));
    hitCacheIds = records.filter((record: any) => record.postalCode && postalCodeCache.get(record.postalCode as number));
  } else {
    // Need to test
    diffRecordIds = records.filter((record: any) => record.id && !stationGroupCache.get(record.id));
    hitCacheIds = records.filter((record: any) => record.id && stationGroupCache.get(record.id));
  }

  logger.info(`Total postal records vs cached: ${records.length} | ${hitCacheIds.length}`);

  const changeRecords = await prisma.tllUserLambdaRecordPriceChange.findMany({
    where: {
      recordDate: {
        gte: dayjs().subtract(3, "day").toDate()
      }
    },
  });

  const changeIds = changeRecords.map((record) => record.recordId);

  let whereParams2 = {
    ...(type === "postalCode" ? {
      postalCode: {
        in: diffRecordIds.map((record) => parseInt(record.postalCode ?? "0")) as number[]
      }
    } : {
      nearestStationGroupId: {
        in: diffRecordIds.map((record) => record.id) as string[]
      }
    }),
    ...(recordType ? {
      recordType: recordType as UserLambdaRecordType
    } : {})
  }

  let allMatchedRecords = await prisma.tllUserLambdaRecord.findMany({
    where: whereParams2,
    include: {
      priceChanges: {
        orderBy: {
          recordDate: "desc"
        }
      }
    }
  })

  let processedData = diffRecordIds.map((record) => {
    let matchedForPostalCode = allMatchedRecords.filter((r) => r.postalCode && r.postalCode.toString() === record.postalCode?.toString())

    let buildingCount = matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.BUILDING).length;
    let buildingAveragePrice = getAveargePriceForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.BUILDING) as UserLambdaRecordProps[], UserLambdaRecordType.BUILDING)
    let buildingAverageRoi = getAverageRoiForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.BUILDING) as UserLambdaRecordProps[], UserLambdaRecordType.BUILDING)
    let buildingAverageArea = getAverageAreaForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.BUILDING) as UserLambdaRecordProps[], UserLambdaRecordType.BUILDING)

    let mansionCount = matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.MANSION).length;
    let mansionAveragePrice = getAveargePriceForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.MANSION) as UserLambdaRecordProps[], UserLambdaRecordType.MANSION)
    let mansionAverageArea = getAverageAreaForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.MANSION) as UserLambdaRecordProps[], UserLambdaRecordType.MANSION)

    let houseCount = matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.HOUSE).length;
    let houseAveragePrice = getAveargePriceForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.HOUSE) as UserLambdaRecordProps[], UserLambdaRecordType.HOUSE)
    let houseAverageArea = getAverageAreaForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.HOUSE) as UserLambdaRecordProps[], UserLambdaRecordType.HOUSE)

    let landCount = matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.LAND).length;
    let landAveragePrice = getAveargePriceForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.LAND) as UserLambdaRecordProps[], UserLambdaRecordType.LAND)
    let landAverageArea = getAverageLandAreaForType(matchedForPostalCode.filter((r) => r.recordType === UserLambdaRecordType.LAND) as UserLambdaRecordProps[], UserLambdaRecordType.LAND)

    return {
      ...record,
      buildingCount,
      buildingAveragePrice,
      buildingAverageRoi,
      buildingAverageArea,
      mansionCount,
      mansionAveragePrice,
      mansionAverageArea,
      houseCount,
      houseAveragePrice,
      houseAverageArea,
      landCount,
      landAveragePrice,
      landAverageArea,
      recentlyPriceChanged: allMatchedRecords.filter((r) => {
        if (!changeIds.filter((id) => id !== null).includes(r.id)) {
          return false;
        }

        if (r.postalCode !== record.postalCode) {
          return false
        }

        if (r.priceChanges.length < 2) {
          return false;
        }

        if (r.priceChanges[0].price === r.priceChanges[1].price) {
          return false;
        }

        return true
      }),
      recentlyCreatedChanges: matchedForPostalCode.filter((r) => {
        if (!changeIds.filter((id) => id !== null).includes(r.id)) {
          return false;
        }

        return true
      })
    }
  })

  processedData.forEach((record: any) => {
    if (type === "postalCode") {
      postalCodeCache.set(record.postalCode as number, record);
    } else {
      stationGroupCache.set(record.id, record);
    }
  })

  return {
    success: true,
    data: processedData.concat(hitCacheIds.map((r: any) => {
      if (type === "postalCode") {
        return postalCodeCache.get(r.postalCode as number) as any
      } else {
        return stationGroupCache.get(r.id) as any
      }
    }))
  };
}

export const getPostalCodeByAddressAction = async (address: string): Promise<ActionResponse<GeoPostalCodeProps>> => {
  try {
    // const record = await prisma.$queryRaw`
    // SELECT * FROM "public"."geo_postal_codes"
    // WHERE CONCAT(prefecture_name, city_name, area_name, choume_name) ILIKE ${'%' + address + '%'}
    // AND prefecture_code in ('11', '12', '13', '14')
    // ` as GeoPostalCodeProps[];

    console.log("address", address);

    const preprocessedAddress = address.replace(/(.{2,3}市|.{2,3}区|.{2,3}県)/g, "$1 ");
    console.log("preprocessedAddress", preprocessedAddress);

    function normalizeJapaneseAddress(input: string): string {
      const patterns = [
        "都", "道", "府", "県",
        "市", "区", "町", "村"
      ];

      let result = input;

      for (const pattern of patterns) {
        result = result.replace(new RegExp(`${pattern}(?! )`, "g"), `${pattern} `);
      }

      return result.trim();
    }

    const records = await prisma.$queryRaw<GeoPostalCodeProps[]>`
    SELECT id, postal_code, prefecture_code, prefecture_name, city_code, city_name, area_code, area_name, choume_name
    FROM geo_postal_codes
    WHERE search_text @@ websearch_to_tsquery('simple', ${normalizeJapaneseAddress(preprocessedAddress)}) 
    AND prefecture_code IN ('11', '12', '13', '14')
  `;

    // logger.debug(`record: ${JSON.stringify(record)}`);

    const convertedRecord = records.map(convertKeysToCamelCase);

    return {
      success: true,
      data: convertedRecord,
    };
  } catch (error) {
    logger.error("getPostalCodeByAddressAction", error);
    return {
      success: false,
      message: "Failed to fetch postal code by address",
    };
  }
};  