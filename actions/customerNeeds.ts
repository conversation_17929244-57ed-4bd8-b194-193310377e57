"use server";

import { prisma } from "@/lib/prisma";
import { CustomerNeedProps, TllCustomerNeedCriteriaType } from "@/lib/definitions"; // 假设你有一个CustomerNeedProps定义
import { ActionResponse } from "@/lib/definitions";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { auth } from "@/lib/auth";
import dayjs from "dayjs";
import { logger } from "@/lib/logger";
import { TllUserLambdaRecordRecordType } from "@prisma/client";


export const createCustomerNeedForSelfFromSearchAction = async ({ title, recordType, criteriaType, value }: { title: string, recordType: TllUserLambdaRecordRecordType, criteriaType: TllCustomerNeedCriteriaType, value: string }): Promise<ActionResponse<CustomerNeedProps>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "用户未登录",
    };
  }

  try {
    const newCustomerNeed = await prisma.tllCustomerNeed.create({
      data: {
        agentUserId: currentUser?.id as string,
        title,
        recordType,
        criteriaType,
        ...(criteriaType === TllCustomerNeedCriteriaType.STATION_GROUP_ID && { nearestStationGroupIds: value }),
        ...(criteriaType === TllCustomerNeedCriteriaType.POSTAL_CODE && { postalCodes: value }),
        ...(criteriaType === TllCustomerNeedCriteriaType.AREA_CODE && { areaCodes: value }),
        ...(criteriaType === TllCustomerNeedCriteriaType.BUILDING_ID && { buildingIds: value }),
      } as any,
    });

    return {
      success: true,
      data: newCustomerNeed as CustomerNeedProps,
    };
  } catch (error) {
    console.error("🚨 创建需求时出错:", error);
    return {
      success: false,
      message: "无法创建需求",
    };
  }
}

export const createCustomerNeedAction = async ({ customerId, customerNeedData }: { customerId?: string, customerNeedData: CustomerNeedProps }): Promise<ActionResponse<CustomerNeedProps>> => {
  const session = await auth();
  const currentUser = session?.user;

  console.log("customerId", customerId);

  try {
    if (!currentUser || !currentUser?.id) {
      return {
        success: false,
        message: "用户未登录",
      };
    }
    logger.info(`customerNeedData: ${JSON.stringify(customerNeedData)}`);

    if (customerId) {
      const existingCustomer = await prisma.tllCustomer.findUnique({
        where: { id: customerId }
      });

      if (!existingCustomer) {
        throw new Error("Customer with this ID does not exist.");
      }
    }

    let dataToSave = {
      ...customerNeedData,
      ...(customerId && { customer: { connect: { id: customerId } } }),
      agentUser: {
        connect: {
          id: currentUser?.id as string,
        }
      }
    }

    console.log("dataToSave", dataToSave);

    const newCustomerNeed = await prisma.tllCustomerNeed.create({
      data: dataToSave as any,
    });

    return {
      success: true,
      data: await padNeedsWithMatch(newCustomerNeed as CustomerNeedProps),
    };
  } catch (error) {
    console.error("🚨 创建需求时出错:", error);
    return {
      success: false,
      message: "无法创建需求",
    };
  }
}

// 获取需求的示例动作
export const fetchMyNeedsAction = async (): Promise<ActionResponse<CustomerNeedProps[]>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "用户未登录",
    };
  }

  try {
    // const customers = await prisma.tllCustomer.findMany({
    //   where: {
    //     agentUserId: currentUser?.id as string,
    //   },
    // });

    const customerNeeds = await prisma.tllCustomerNeed.findMany({
      where: {
        // customerUserId: { in: customers.map((customer) => customer.id) },
        customerUserId: null,
        agentUserId: currentUser?.id as string,
      },
    });

    // const formattedNeeds = customerNeeds.map((need) => ({
    //   ...need,
    //   customerId: need.customerUserId,
    // }));

    return {
      success: true,
      data: await Promise.all(customerNeeds.map(async (need) => await padNeedsWithMatch(need as CustomerNeedProps))),
    };
  } catch (error) {
    console.error("🚨 获取需求时出错:", error);
    return {
      success: false,
      message: "无法获取需求",
    };
  }
};

export const padNeedsWithMatch = async (need: CustomerNeedProps): Promise<CustomerNeedProps & { paddedWatchedValues: { label: string, value: string | number }[] }> => {
  let paddedWatchedValues: { label: string; value: string | number }[] = [];

  if (need.criteriaType === TllCustomerNeedCriteriaType.STATION_GROUP_ID && need.nearestStationGroupIds) {
    const ids = need.nearestStationGroupIds.split(",").filter(Boolean).slice(0, 100); // 最多取100个
    if (ids.length > 0) {
      const stations = await prisma.geoRailwayStationGroup.findMany({
        where: { id: { in: ids } },
      });
      paddedWatchedValues = stations.map((record) => ({
        label: record.name ?? "",
        value: record.id,
      }));
    }
  }

  if (need.criteriaType === TllCustomerNeedCriteriaType.POSTAL_CODE && need.postalCodes) {
    const codes = need.postalCodes.split(",").map(code => Number(code.trim())).filter(Boolean).slice(0, 100);
    if (codes.length > 0) {
      const postals = await prisma.geoPostalCode.findMany({
        where: { postalCode: { in: codes.map((code) => code.toString()) } },
      });
      paddedWatchedValues = postals.map((record) => ({
        label: `${record.prefectureName}${record.cityName}${record.areaName}${record.choumeName}`,
        value: record.postalCode,
      }));
    }
  }

  if (need.criteriaType === TllCustomerNeedCriteriaType.AREA_CODE && need.areaCodes) {
    const codes = need.areaCodes.split(",").filter(Boolean).slice(0, 100);
    if (codes.length > 0) {
      const areas = await prisma.geoArea.findMany({
        where: { code: { in: codes.map(code => parseInt(code)) as number[] } },
        include: { prefecture: true, city: true },
      });
      paddedWatchedValues = areas.map((record) => ({
        label: `${record.prefecture?.name ?? ""}${record.city ? ` ${record.city.name}` : ""}${record.nameJa}`,
        value: record.code,
      }));
    }
  }

  if (need.criteriaType === TllCustomerNeedCriteriaType.BUILDING_ID && need.buildingIds) {
    const ids = need.buildingIds.split(",").filter(Boolean).slice(0, 100);
    if (ids.length > 0) {
      const buildings = await prisma.proBuilding.findMany({
        where: { id: { in: ids } },
      });
      paddedWatchedValues = buildings.map((record) => ({
        label: `${record.nameJa}`,
        value: record.id,
      }));
    }
  }

  return {
    ...need,
    paddedWatchedValues,
  };
}

export const updateCustomerNeedAction = async (id: string, customerNeedData: CustomerNeedProps): Promise<ActionResponse<CustomerNeedProps>> => {
  logger.debug(`++++customerNeedData: ${JSON.stringify(customerNeedData)}`);

  try {
    const updatedNeed = await prisma.tllCustomerNeed.update({
      where: { id },
      data: customerNeedData as any,
    });

    return {
      success: true,
      data: await padNeedsWithMatch(updatedNeed as CustomerNeedProps),
    };
  } catch (error) {
    console.error("🚨 更新需求时出错:", error);
    return {
      success: false,
      message: "无法更新需求",
    };
  }
}

export const getNeedAction = async (id: string): Promise<ActionResponse<CustomerNeedProps>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "用户未登录",
    };
  }

  try {
    const need = await prisma.tllCustomerNeed.findUnique({
      where: { id },
      include: {
        customer: true,
      },
    });

    return {
      success: true,
      data: await padNeedsWithMatch(need as CustomerNeedProps),
    };
  } catch (error) {
    console.error("🚨 获取需求时出错:", error);
    return {
      success: false,
      message: "无法获取需求",
    };
  }
}

export const getRecordMatchSimpleForNeedIdsAction = async ({ needIds }: { needIds?: string[] }): Promise<ActionResponse<{
  [key: string]: {
    matchCount: number,
    need: CustomerNeedProps,
  }
}>> => {
  try {
    logger.debug(`needIds: ${needIds}`);

    const session = await auth();
    const currentUser = session?.user;

    if (!currentUser || !currentUser?.id) {
      return {
        success: false,
        message: "用户未登录",
      };
    }

    let needIdsToUse = needIds || await prisma.tllCustomerNeed.findMany({
      where: {
        agentUserId: currentUser?.id as string,
      },
      select: {
        id: true,
      },
    });

    console.log("needIdsToUse", needIdsToUse);

    const needs = await prisma.tllCustomerNeed.findMany({
      where: {
        id: { in: needIdsToUse as string[] },
      },
      include: {
        customer: {
          select: {
            name: true,
          },
        },
      },
    }) as CustomerNeedProps[];

    console.log("needs", needs);

    const matchPromises = needs.map(async (need) => {
      const whereParams = await prepParamsFromNeed({ need, sinceDate: dayjs().add(-1, 'day').toISOString() });

      console.log("whereParams", whereParams);

      let paramsWithoutId = { ...whereParams, id: null };
      logger.debug(`paramsWithoutId: ${JSON.stringify(paramsWithoutId)}`);

      const records = await prisma.tllUserLambdaRecord.findMany({
        where: whereParams as any,
        select: {
          id: true,
          yearlyIncome: true,
          price: true,
        },
      }) as UserLambdaRecordProps[];

      const matchCount = records.filter((record: UserLambdaRecordProps) => {
        if (need?.roiFrom) {
          return record.yearlyIncome && record.yearlyIncome / record.price * 100 >= need.roiFrom;
        }
        return true;
      }).length;

      return {
        id: need.id,
        data: {
          matchCount,
          need: need as CustomerNeedProps,
        }
      };
    });

    const results = await Promise.all(matchPromises);


    let res = results.map((result) => {
      return {
        ...result.data.need,
        matchCount: result.data.matchCount,
      }
    });

    return {
      success: true,
      data: res,
    };
  } catch (error) {
    console.error("🚨 获取需求时出错:", error);
    return {
      success: false,
      message: "无法获取需求",
    };
  }
}

export const getRecordsMatchForNeedAction = async ({ needId, sinceDate }: { needId: string, sinceDate: string }): Promise<ActionResponse<UserLambdaRecordProps[]>> => {
  console.log("sinceDate", sinceDate);

  try {
    const need = await prisma.tllCustomerNeed.findUnique({
      where: { id: needId },
    });

    const whereParams = await prepParamsFromNeed({ need, sinceDate: sinceDate });

    const records = await prisma.tllUserLambdaRecord.findMany({
      where: whereParams as any,
      include: {
        priceChanges: true,
        propertyAnalysisResult: true,
      },
      take: 100,
    }) as UserLambdaRecordProps[];

    // FIXME: roi must be filtered here
    return {
      success: true,
      data: records.filter((record: UserLambdaRecordProps) => {
        if (need?.roiFrom) {
          return record.yearlyIncome && record.yearlyIncome / record.price * 100 >= need.roiFrom;
        }
        return true;
      }),
    };
  } catch (error) {
    console.error("🚨 获取需求时出错:", error);
    return {
      success: false,
      message: "无法获取需求",
    };
  }
}

const prepParamsFromNeed = async ({
  need,
  // recordDate,
  sinceDate,
}: {
  need: any,
  // recordDate?: string,
  sinceDate: string,
}): Promise<any> => {
  console.log("sinceDate", sinceDate);

  // const statusChanges = await prisma.tllUserLambdaRecordPriceChange.findMany({
  //   where: {
  //     recordDate: { gte: sinceDate },
  //   },
  // });

  let whereParams = {
    // id: { in: statusChanges.map((statusChange) => statusChange.recordId as string) },
    updatedAt: {
      gte: sinceDate,
    },
    price: {} as any,
    landSize: {} as any,
    buildingSize: {} as any,
  } as any;

  if (need?.recordType) {
    whereParams.recordType = need.recordType;
  }

  if (need?.priceFrom) {
    whereParams.price["gte"] = need.priceFrom;
  }

  if (need?.priceTo) {
    whereParams.price["lte"] = need.priceTo;
  }

  if (need?.yearFrom) {
    whereParams.buildingBuiltYear = {
      gte: need.yearFrom,
    };
  }

  if (need?.criteriaType === TllCustomerNeedCriteriaType.POSTAL_CODE && need?.postalCodes) {
    whereParams.postalCode = {
      in: need.postalCodes.split(',').map((code: string) => Number(code.trim())),
    };
  }

  if (need?.criteriaType === TllCustomerNeedCriteriaType.STATION_GROUP_ID && need?.nearestStationGroupIds) {
    whereParams.nearestStationGroupId = {
      in: need.nearestStationGroupIds.split(','),
    };
  }

  if (need?.criteriaType === TllCustomerNeedCriteriaType.AREA_CODE && need?.areaCodes) {
    whereParams.areaCode = {
      in: need.areaCodes.split(',').map((code: string) => Number(code.trim())),
    };
  }

  if (need?.criteriaType === TllCustomerNeedCriteriaType.BUILDING_ID && need?.buildingIds) {
    whereParams.id = {
      in: need.buildingIds.split(','),
    };
  }

  if (need?.nearestStationWalkMinuteTo) {
    whereParams.nearestStationWalkMinute = {
      lte: need.nearestStationWalkMinuteTo,
    };
  }

  if (need?.landAreaFrom) {
    whereParams.landSize["gte"] = need.landAreaFrom;
  }

  if (need?.landAreaTo) {
    whereParams.landSize["lte"] = need.landAreaTo;
  }

  if (need?.buildingAreaFrom) {
    whereParams.buildingSize["gte"] = need.buildingAreaFrom;
  }

  if (need?.buildingAreaTo) {
    whereParams.buildingSize["lte"] = need.buildingAreaTo;
  }

  if (need?.landCanHotel) {
    whereParams.landType = {
      notIn: ["一低", "一中", "二低", "二中"],
    };
  }

  // if (need?.buildingMaterial) {
  //   // FIXME: i dont think this works 
  //   whereParams.buildingBuiltMaterial = need.buildingMaterial;
  // }


  // if (need?.roiFrom) {
  //   whereParams.roi = {
  //     gte: need.roiFrom,
  //   };
  // }

  Object.keys(whereParams).forEach(key => {
    // Only apply to below
    //     price: {} as any,
    //     landSize: {} as any,
    //     buildingSize: {} as any,
    // because default is empty
    if (typeof whereParams[key] === 'object' && Object.keys(whereParams[key]).length === 0) {
      delete whereParams[key];
    }
  });

  return whereParams;
}

export const deleteCustomerNeedAction = async (id: string): Promise<ActionResponse<CustomerNeedProps>> => {
  try {
    await prisma.tllCustomerNeed.delete({
      where: { id },
    });

    return {
      success: true,
      message: "需求删除成功",
    };
  } catch (error) {
    console.error("🚨 删除需求时出错:", error);
    return {
      success: false,
      message: "无法删除需求",
    };
  }
}