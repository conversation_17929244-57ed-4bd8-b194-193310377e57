"use server";

import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import dayjs from "dayjs";

export async function getProMansionRentForDay({ date }: { date: string }): Promise<ActionResponse<ProMansionRentProps[]>> {
  try {
    const records = await prisma.proMansionRent.findMany({
      where: {
        createdAt: {
          gte: dayjs(date).startOf("day").toDate(),
          lte: dayjs(date).startOf("day").add(1, "day").toDate(),
        },
      },
      include: {
        building: {
          include: {
            mansionRents: {
              select: {
                feeRent: true,
                unitSize: true,
              },
            },
          },
        },
      },
    });

    return {
      data: records,
      success: true,
      message: "Success",
    };
  } catch (error) {
    console.error(error);
    return {
      data: [],
      success: false,
      message: "Error",
    };
  }
}

export async function getProMansionRent({
  postalCode,
  nearestStationGroupId,
  recordType,
  buildingId,
}: {
  postalCode?: number;
  nearestStationGroupId?: string;
  recordType: string;
  buildingId?: string;
}): Promise<ActionResponse<ProMansionRentProps[]>> {
  // Buildign id is just for mansion
  if (!postalCode && !nearestStationGroupId && !buildingId) {
    return {
      data: [],
      success: true,
      message: "Success",
    };
  }

  let where = {
    recordType: recordType,
  } as any;

  if (postalCode && nearestStationGroupId) {
    where.OR = [
      {
        locationPostalCode: postalCode.toString(),
      },
      {
        nearestStationGroupId: nearestStationGroupId,
      },
    ];
  } else if (postalCode) {
    where.locationPostalCode = postalCode.toString();
  } else if (nearestStationGroupId) {
    where.nearestStationGroupId = nearestStationGroupId;
  } else if (buildingId) {
    where.buildingId = buildingId;
  }

  const records = await prisma.proMansionRent.findMany({
    where: where,
  });

  return {
    data: records,
    success: true,
    message: "Success",
  };
}

export async function getProMansionRentSummary({
  postalCode,
  nearestStationGroupId,
}: {
  postalCode?: number;
  nearestStationGroupId?: string;
}): Promise<ActionResponse<ProMansionRentProps[]>> {
  if (!postalCode && !nearestStationGroupId) {
    return {
      data: [],
      success: true,
      message: "Success",
    };
  }

  let where = {
    ...(postalCode ? { locationPostalCode: postalCode.toString() } : {}),
    ...(nearestStationGroupId ? { nearestStationGroupId } : {}),
  };

  const records = await prisma.proMansionRent.findMany({
    where: where,
    select: {
      feeManagement: true,
      feeRent: true,
      unitSize: true,
    },
  });

  return {
    data: records,
    success: true,
    message: "Success",
  };
}

export async function getProMansionRentDetail({
  id,
}: {
  id: string;
}): Promise<ActionResponse<ProMansionRentProps>> {
  const record = await prisma.proMansionRent.findUnique({
    where: { id },
  });

  return {
    data: record,
    success: true,
    message: "Success",
  };
}     