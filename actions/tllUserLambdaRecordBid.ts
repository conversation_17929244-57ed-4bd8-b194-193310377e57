"use server";

import { ActionResponse } from "@/lib/definitions";
import { UserLambdaRecordBidProps, TllUserLambdaRecordBidStatusEnum } from "@/lib/definitions/userLambdaRecordBid";
import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { logger } from "@/lib/logger";
import { TllUserLambdaRecordBidStatus } from "@prisma/client";
import dayjs from "dayjs";
import { UserLambdaRecordBidSchema } from "@/lib/definitions/userLambdaRecordBid";

export async function createUserLambdaRecordBidAction(data: UserLambdaRecordBidProps): Promise<ActionResponse<UserLambdaRecordBidProps>> {
  console.log("🔥 createUserLambdaRecordBidAction");
  const session = await auth();
  const userId = session?.user?.id;

  if (!userId) {
    return {
      success: false,
      message: "ユーザーはログインしていません",
    };
  }

  try {
    const validatedData = UserLambdaRecordBidSchema.parse(data);

    const bid = await prisma.tllUserLambdaRecordBid.create({
      data: {
        recordId: validatedData.recordId,
        recordPrice: validatedData.recordPrice,
        salesUserId: validatedData.salesUserId,
        isSumitomoKeibai: validatedData.isSumitomoKeibai || false,
      }
    });

    return {
      success: true,
      data: bid,
    };
  } catch (error) {
    logger.error("createUserLambdaRecordBidAction error", error);
    return {
      success: false,
      message: JSON.stringify(error),
    };
  }
}

export async function getUserLambdaRecordBidsAction(): Promise<ActionResponse<UserLambdaRecordBidProps[]>> {
  const bids = await prisma.tllUserLambdaRecordBid.findMany({
    include: {
      salesUser: {
        omit: {
          password: true,
        }
      },
      tllUserLambdaRecord: {
        include: {
          priceChanges: true,
          materialMappings: true,
          propertyAnalysisResult: true,
        },
        omit: {
          analysisSimulationConfig: true,
          // analysisSimulationResults: true,
        }
      }
    },
    where: {
      status: { not: "NINE_CANCEL" },  // Condition 1
      // { createdAt: { gte: dayjs().startOf('year').toDate() } }, // Condition 2
      // { biddingPrice: { not: null } }, // Condition 3
    },
    orderBy: [{
      salesUserId: "desc",
    }, {
      createdAt: "desc",
    }],
  });

  return {
    success: true,
    data: bids,
  };
}

export async function getUserLambdaRecordBidAction(id: string): Promise<ActionResponse<UserLambdaRecordBidProps>> {

  const bid = await prisma.tllUserLambdaRecordBid.findUnique({
    where: { id },
    include: {
      tllUserLambdaRecord: {
        include: {
          propertyAnalysisResult: true,
        }
      }
    }
  });

  return {
    success: true,
    data: bid,
  };
}

export async function updateUserLambdaRecordBidAction(id: string, data: UserLambdaRecordBidProps): Promise<ActionResponse<UserLambdaRecordBidProps>> {

  try {
    const updateData: Partial<UserLambdaRecordBidProps> = {};
    if (data.status) updateData.status = data.status as unknown as TllUserLambdaRecordBidStatusEnum;
    if (data.biddingPrice) updateData.biddingPrice = data.biddingPrice;
    if (data.biddingResult) updateData.biddingResult = data.biddingResult;
    if (data.comments) updateData.comments = data.comments;
    if (data.checklist) updateData.checklist = data.checklist;
    if (data.salesUserId) updateData.salesUserId = data.salesUserId;
    if (data.isSumitomoKeibai) updateData.isSumitomoKeibai = data.isSumitomoKeibai;

    const bid = await prisma.tllUserLambdaRecordBid.update({
      where: { id },
      data: updateData as any,
      include: {
        tllUserLambdaRecord: {
          include: {
            propertyAnalysisResult: true,
          },
          omit: {
            analysisSimulationConfig: true,
            analysisSimulationResults: true,
          }
        }
      }
    });

    return {
      success: true,
      data: bid,
    };
  } catch (error) {
    logger.error("updateUserLambdaRecordBidAction error", error);
    return {
      success: false,
      message: JSON.stringify(error),
    };
  }
}

export async function deleteUserLambdaRecordBidAction(id: string): Promise<ActionResponse<UserLambdaRecordBidProps>> {
  try {
    const bid = await prisma.tllUserLambdaRecordBid.delete({
      where: { id },
    });
    return {
      success: true,
      data: bid,
    };
  } catch (error) {
    logger.error("deleteUserLambdaRecordBidAction error", error);
    return {
      success: false,
      message: JSON.stringify(error),
    };
  }
}


export async function getBidForDashboard() {
  const bids = await prisma.tllUserLambdaRecordBid.findMany({
    select: {
      id: true,
      recordId: true,
      recordPrice: true,
      createdAt: true,
      biddingPrice: true,
      biddingResult: true,
      status: true,
      tllUserLambdaRecord: {
        select: {
          recordType: true,
        }
      }
    }
  });

  return {
    success: true,
    data: bids,
  };
}
