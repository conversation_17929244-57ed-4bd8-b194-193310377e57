"use server";

import { ActionResponse } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { getGeocodeInfo, getPostalCodeFromAddress } from "@/lib/thirdParty/google";
export async function getTorusPinData({
  lat,
  lng,
  userId = "10802",
  verifyCode = "yTI3aCmeyjF56O2w1wOssw==",
}: {
  lat: number;
  lng: number;
  userId?: string | number;
  verifyCode?: string;
}): Promise<ActionResponse<any>> {
  try {
    const formData = new FormData();
    formData.append("f[lat]", lat.toString());
    formData.append("f[lng]", lng.toString());
    formData.append("f[verify_code]", verifyCode);
    formData.append("f[user_id]", userId.toString());

    logger.info("formData", formData);

    const response = await fetch("https://bot.torus.co.jp/no-auth/free-pin-search", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Torus API failed with status ${response.status}`);
    }

    const result = await response.json();

    // Save it into GeoAddress
    // await prisma.geoAddress.create({    
    //   data: {
    //     pcode: result.place_pcode,
    //     address: result.place_address,
    //     chibanAddress: result.my_chiban.base,
    //     chibanDetails: result.res,
    //   },
    // });

    let fullAddress = `${result?.place_address}${result?.place_branch}`;
    if (result?.my_chiban?.branch) {
      let fullChibanAddress = `${result?.place_address}${result?.my_chiban?.branch}`;

      let match = await prisma.geoAddress.findFirst({
        where: {
          address: fullAddress,
        },
      });

      if (match) {
        if (match.chibanAddress === null) {
          logger.info("match found, is null so updating updating... id ", match.id);
          await prisma.geoAddress.update({
            where: { id: match.id },
            data: { chibanAddress: fullChibanAddress, chibanDetails: result },
          });
        } else {
          logger.info("match found, not null so skipping ... id");
        }
      } else {
        let pcode = await getGeocodeInfo(fullAddress);

        if (!pcode.postalCode) {
          logger.error("no postal code found for address", fullAddress);
          return {
            success: false,
            message: "地番マップの取得に失敗しました",
          }
        }

        let res = await prisma.geoAddress.create({
          data: {
            address: fullAddress,
            pcode: pcode.postalCode,
            chibanAddress: fullChibanAddress,
            chibanDetails: result,
            longitude: pcode.lng,
            latitude: pcode.lat,
          },
        });

        logger.info("no match found, creating... address.. creating record is  ", res.id);
      }
    }

    return {
      success: true,
      data: result,
    };
  } catch (error: any) {
    logger.error("getTorusPinData error", error);
    return {
      success: false,
      message: error.message || "地番マップの取得に失敗しました",
    };
  }
}