"use server"

import { ActionResponse } from "@/lib/definitions";
import { allHotspots } from "@/lib/constants/allHotspots";
import { fetchContentWithFirecrawl } from "@/actions/helper/firecrawl";
import openaiInstance from "@/lib/instances/openai";
import { ScrapeResponse } from "@mendable/firecrawl-js";

export async function getNewsFromUrlAction({ url }: { url: string }): Promise<ActionResponse<typeof allHotspots>> {
  try {
    console.log('🔥getHotspots', url);

    let articleContent = await fetchContentWithFirecrawl(url) as ScrapeResponse;

    console.log('🔥articleContent fetched , parsing...', articleContent);
    if (!articleContent.success || !articleContent.markdown) {
      return {
        success: false,
        data: null,
        message: 'Failed to use firecrawl to get hotspots',
      };
    }

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1; // 月份从0开始，所以加1

    const resOpenAI = await openaiInstance.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user", content: `
${articleContent.markdown}

请从上面的文章里面取出以下信息，并把结果存入 JSON，JSON 内容保留日语, Value一定要是string或者number, 不要是程序
- **title** (必须)
- **titleCh** (翻译为中文)
- **description** (如果有, 这个一般是标题下面的小字)
- **descriptionCh** (翻译为中文)
- **releaseDate** (发布时间，格式为 YYYY-MM-DD。如果文章内只有月份/日期(e.g., 2/10)，假设年份为 ${currentYear}。如果只有日期(e.g., 10日)，假设年份为 ${currentYear} 现在的月份 ${currentMonth}, 输出不要是程序, 而是 YYYY-MM-DD 格式)
- **url** (必须, 文章 URL)
- **imageLink** (如果有, 文章 thumbnail URL)
- **readingCount**
- **commentCount**
- **recommendationLevel** (1-5, 5为最高, 评分规则如下：
  - 5️⃣ = 非常适合中国 SNS，话题热门，易引起讨论，适合改写成爆款文章
  - 4️⃣ = 适合 SNS，内容有趣，可能有爆款潜力
  - 3️⃣ = 一般文章，可能适合某些用户，但传播性一般
  - 2️⃣ = 内容较冷门，传播性差，不适合作为 SNS 爆款
  - 1️⃣ = 话题枯燥，阅读价值低，不适合 SNS 传播)
- **recommendationReason** (为什么推荐这篇文章, 用中文, 如果评分为 1 或 2, 需要清楚说明原因) 
请将结果存入 JSON，不要输出任何其他内容。 `
        },
      ],
    });

    let content = resOpenAI.choices[0].message.content;

    if (content && typeof content === 'object' && Object.keys(content).length > 0) {
      return {
        success: true,
        data: content,
        message: 'Success',
      };
    }

    content = content ? content.replace(/```json|```/g, "").trim() : "";
    console.log('content', content);
    const parsedJson = content ? JSON.parse(content) : {};
    console.log('🔥parsedJson', parsedJson);

    // 如果parsedJson不是数组，则将其包装在数组中
    const resultData = Array.isArray(parsedJson) ? parsedJson : [parsedJson];

    return {
      success: true,
      data: resultData,
    };
  } catch (error) {
    console.error('🔥error', error);
    return {
      success: false,
      data: null,
      message: 'Failed to get hotspots',
    };
  }
}