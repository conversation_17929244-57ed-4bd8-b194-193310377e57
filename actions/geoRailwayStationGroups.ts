"use server";

import { prisma } from "@/lib/prisma";
import { GetStationName } from "@/lib/railwayStation";
import { logger } from "@/lib/logger";
import { RailwayStationProps } from "@/lib/definitions";
import { ActionResponse } from "@/lib/definitions";
import { auth } from "@/lib/auth";
import { SystemReportViewHistoryRecordType } from "@/lib/definitions/system";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";


export async function fillNearestStationGroupIdWalkMinute(recordId: string, model: "valuationRecord" | "tllUserLambdaRecord" = "tllUserLambdaRecord"): Promise<any> {
  try {
    const rec =
      await (prisma[model as "valuationRecord" | "tllUserLambdaRecord"] as any).findUnique({
        where: {
          id: recordId,
        },
      });

    if ((rec.nearestStation == null || rec.nearestStation === "") && (rec.transport == null || rec.transport === "")) {
      return;
    }

    const station = rec.nearestStation || GetStationName(rec.transport);
    let nearestStationGroupId = rec.nearestStationGroupId || null;

    if (station !== null && rec.prefectureCode > 0 && nearestStationGroupId === null) {
      const match = await prisma.geoRailwayStation.findMany({
        where: {
          name: station,
          prefectureCode: { in: [11, 12, 13, 14] }, // sometimes property in 埼玉 but nearest station is in 東京
        },
      });

      if (match.length > 0) {
        nearestStationGroupId = match[0].stationGroupId;
      }
    }
    console.log("nearestStationGroupId", nearestStationGroupId);

    let nearestStationWalkMinute = rec.nearestStationWalkMinute || null;

    // FIXME: remvoe this.. should not be needed after a while
    if (nearestStationWalkMinute === null) {
      let matches = rec["nearestStationWalkMinute"]?.match(/徒歩(\d+)分/);

      if (matches !== undefined && matches?.length === 2) {
        nearestStationWalkMinute = parseInt(matches[1], 10);
      } else {
        if (rec.recordValues?.nearestStationWalkMinute) {
          let minutes = rec.recordValues.nearestStationWalkMinute.match(/徒歩(\d+)分/);
          if (minutes !== undefined && minutes?.length === 2) {
            nearestStationWalkMinute = parseInt(minutes[1], 10);
          }
        }
      }
    }

    if (station !== null && station.length > 0) {
      const dataToUpdate = {
        nearestStation: station,
        nearestStationWalkMinute,
        nearestStationGroupId,
        updatedAt: rec.updatedAt || new Date(),
      };

      await (prisma[model as "valuationRecord" | "tllUserLambdaRecord"] as any).update({
        where: { id: rec.id },
        data: dataToUpdate,
      });
    }

    return;
  } catch (error) {
    logger.error("fillNearestStationGroupIdWalkMinute error", error);
    return;
  }
}


export async function getStationGroupAction(stationGroupId: number): Promise<ActionResponse<RailwayStationProps[]>> {
  try {
    const session = await auth();
    const currentUser = session?.user;

    if (!currentUser || !currentUser?.id) {
      return {
        success: false,
        message: "用户未登录",
      };
    }

    // TODO: do the quota check ehre too 
    // Also create record for the user (for pricing history)
    const searchHistoryExists = await prisma.systemReportViewHistory.findFirst({
      where: {
        recordType: SystemReportViewHistoryRecordType.STATION_GROUP,
        userId: currentUser.id,
        nearestStationGroupId: stationGroupId.toString(),
        isValid: true,
      },
    });

    if (!searchHistoryExists) {
      const dataToCreate = {
        recordType: SystemReportViewHistoryRecordType.STATION_GROUP,
        userId: currentUser.id,
        nearestStationGroupId: stationGroupId.toString(),
        viewDate: dayjsWithTz().toISOString(),
      }

      await prisma.systemReportViewHistory.create({
        data: dataToCreate,
      });
    }

    const stationGroup = await prisma.geoRailwayStationGroup.findUnique({
      where: {
        id: stationGroupId.toString(),
      },
      include: {
        geoRailwayStations: true,
      },
    });

    return {
      success: true,
      data: stationGroup,
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to fetch station group",
    };
  }
}

export async function findNearestStationAction(nearestStation: string): Promise<ActionResponse<RailwayStationProps[]>> {
  try {
    const stations = await prisma.geoRailwayStation.findMany({
      where: {
        name: {
          contains: nearestStation,
          mode: "insensitive",
        },
        prefectureCode: {
          in: [11, 12, 13, 14],
        },
      },
      include: {
        prefecture: {
          select: {
            name: true,
          },
        },
        geoRailwayStationGroups: {
          select: {
            id: true,
            address: true,
            postalCode: true,
          },
        },
      },
    });

    // 处理重复的车站，选择 name 和 geoRailwayStationGroup.id 相同的任意一个
    const uniqueStations = Array.from(new Map(stations.map(station => [`${station.name}-${station.geoRailwayStationGroups?.id}`, station])).values());

    return {
      success: true,
      data: uniqueStations,
    };
  } catch (error) {
    console.error("Error fetching nearest stations:", error);
    return {
      success: false,
      message: "Failed to fetch nearest stations",
    };
  }
}