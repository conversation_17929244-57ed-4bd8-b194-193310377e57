"use server";

import { fillRecordAnalysis } from "@/app/api/cron/reinsFill/recordAnalysis/fillRecordAnalysis";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

import { ActionResponse } from "@/lib/definitions";

export async function tllUserLambdaRecordReevaluateAction(id: string): Promise<ActionResponse<UserLambdaRecordProps>> {
  try {
    await fillRecordAnalysis({
      recordId: id,
    });

    return {
      success: true,
      data: [],
    };
  } catch (error) {
    console.error("Error fetching user lambda record metadata:", error);
    return {
      success: false,
      message: 'Failed to fetch user lambda record metadata',
    };
  }
}