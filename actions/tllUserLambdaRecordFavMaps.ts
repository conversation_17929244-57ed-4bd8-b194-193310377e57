"use server";

import { auth } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { logger } from "@/lib/logger";
import dayjs from "dayjs";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";

export async function getUserLambdaRecordFavMapsAction({
  period,
  recordType,
}: {
  period: string,
  recordType: string,
}): Promise<ActionResponse<any>> {
  const session = await auth(); // Fetch authenticated session
  const userId = session?.user?.id; // 从 next-auth 获取 userId

  if (!userId) {
    return {
      success: false,
      message: "ユーザーはログインしていません", // 使用日语
    };
  }

  const favMaps = await prisma.tllUsersUserLambdaRecordsFavMap.findMany({
    where: {
      userId: userId,
      createdAt: {
        gte: dayjs().subtract(parseInt(period), 'day').toDate(),
      },
      ...(recordType !== "all" ? { userLambdaRecord: { recordType: recordType as UserLambdaRecordType } } : {}),
    },
  });

  const lambdaRecords = await prisma.tllUserLambdaRecord.findMany({
    where: {
      id: {
        in: favMaps.map((favMap: any) => favMap.userLambdaRecordId).filter((id: string) => id !== null),
      },
    },
    include: {
      priceChanges: {
        include: {
          company: true,
        }
      },
      propertyAnalysisResult: true,
    },
  });

  return {
    success: true,
    data: lambdaRecords.map((lambdaRecord) => ({
      ...lambdaRecord,
      favDate: favMaps.find((favMap) => favMap.userLambdaRecordId === lambdaRecord.id)?.createdAt,
    })).sort((a, b) => dayjs(b.favDate).diff(dayjs(a.favDate))),
  };
}

export async function checkUserLambdaRecordFavMapsAction(userLambdaRecordIds: string[]): Promise<ActionResponse<any>> {
  const session = await auth(); // Fetch authenticated session
  const userId = session?.user?.id; // 从 next-auth 获取 userId

  if (!userId) {
    return {
      success: false,
      message: "ユーザーはログインしていません", // 使用日语
    };
  }

  if (userLambdaRecordIds.length === 0 || !userLambdaRecordIds || userLambdaRecordIds === null || userLambdaRecordIds === undefined) {
    return {
      success: true,
      data: [],
    };
  }

  const whereParams = {
    userId: userId,
    userLambdaRecordId: {
      in: userLambdaRecordIds,
    },
  };

  // logger.debug(`whereParams: ${JSON.stringify(whereParams)}`);
  const favMaps = await prisma.tllUsersUserLambdaRecordsFavMap.findMany({
    where: whereParams,
  });

  return {
    success: true,
    data: favMaps.map((favMap: any) => favMap.userLambdaRecordId),
  };
}

export async function updateUserLambdaRecordFavMapsAction(userLambdaRecordId: string): Promise<ActionResponse<any>> {
  const session = await auth(); // Fetch authenticated session
  const userId = session?.user?.id; // 从 next-auth 获取 userId

  if (!userId) {
    return {
      success: false,
      message: "ユーザーはログインしていません", // 使用日语
    };
  }

  const favMap = await prisma.tllUsersUserLambdaRecordsFavMap.findFirst({
    where: {
      userId: userId,
      userLambdaRecordId: userLambdaRecordId,
    },
  });

  if (favMap) {
    logger.debug(`delete favMap: ${favMap.id}`);
    await prisma.tllUsersUserLambdaRecordsFavMap.delete({
      where: {
        id: favMap.id,
      },
    });
  } else {
    logger.debug(`create favMap: ${userLambdaRecordId}`);
    await prisma.tllUsersUserLambdaRecordsFavMap.create({
      data: {
        userId: userId,
        userLambdaRecordId: userLambdaRecordId,
      },
    });
  }

  return {
    success: true,
    data: favMap ? false : true,
    message: "物件をお気に入りに追加しました", // 使用日语
  };
}

export async function setUserFavIfNotSetAction(userLambdaRecordId: string): Promise<ActionResponse<any>> {
  const session = await auth(); // Fetch authenticated session
  const userId = session?.user?.id; // 从 next-auth 获取 userId

  if (!userId) {
    return {
      success: false,
      message: "ユーザーはログインしていません", // 使用日语
    };
  }

  const favMap = await prisma.tllUsersUserLambdaRecordsFavMap.findFirst({
    where: {
      userId: userId,
      userLambdaRecordId: userLambdaRecordId,
    },
  });

  if (!favMap) {
    logger.debug(`create favMap: ${userLambdaRecordId}`);
    await prisma.tllUsersUserLambdaRecordsFavMap.create({
      data: {
        userId: userId,
        userLambdaRecordId: userLambdaRecordId,
      },
    });
  }

  return {
    success: true,
    data: favMap ? false : true,
    message: "物件をお気に入りに追加しました", // 使用日语
  };
}


export async function getNewMatchFavForDate({ date }: { date: string }): Promise<ActionResponse<any>> {
  const session = await auth(); // Fetch authenticated session
  const userId = session?.user?.id; // 从 next-auth 获取 userId

  if (!userId) {
    return {
      success: false,
      message: "ユーザーはログインしていません", // 使用日语
    };
  }

  try {
    const favMaps = await prisma.tllUsersUserLambdaRecordsFavMap.findMany({
      where: {
        userId: userId,
      },
      select: {
        userLambdaRecordId: true,
      },
    });

    let todayMatchPriceChangeRecords = await prisma.tllUserLambdaRecordPriceChange.findMany({
      where: {
        recordDate: dayjs(date).toDate(),
        recordId: {
          in: favMaps.map((favMap) => favMap.userLambdaRecordId).filter((id) => id !== null),
        },
      },
    });

    let todayRecords = await prisma.tllUserLambdaRecord.findMany({
      where: {
        id: {
          in: todayMatchPriceChangeRecords.map((record) => record.recordId).filter((id) => id !== null),
        },
      },
      include: {
        priceChanges: {
          include: {
            company: true,
          }
        },
        propertyAnalysisResult: true,
      },
    });

    return {
      success: true,
      data: todayRecords,
    };
  } catch (error) {
    logger.error(`error: ${error}`);
    return {
      success: false,
      message: "エラーが発生しました", // 使用日语
    };
  }
}

