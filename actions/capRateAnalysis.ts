"use server";

import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { calcCoordinateDistance } from "@/lib/helper/geoDistance";
import { executeWithRetry } from "@/lib/prismaUtils";
import { logger } from "@/lib/logger";
import dayjs from "dayjs";

const VALUATION_MAX_YEAR_RETRIEVAL = 3;

export async function getCapRateAnalysisData({
  selectedOption,
}: {
  selectedOption: { label: string; value: number; type: string };
}): Promise<ActionResponse<{
  nearbyRecords: UserLambdaRecordProps[];
  centerCoordinates: { latitude: number; longitude: number } | null;
}>> {
  try {
    let centerCoordinates: { latitude: number; longitude: number } | null = null;

    // Get center coordinates based on search type using retry utility
    if (selectedOption.type === "area") {
      const area = await executeWithRetry(async () => {
        return await prisma.geoArea.findUnique({
          where: { id: selectedOption.value?.toString() },
        });
      });
      if (area?.latitude && area?.longitude) {
        centerCoordinates = { latitude: area.latitude, longitude: area.longitude };
      }
    } else if (selectedOption.type === "station") {
      const station = await executeWithRetry(async () => {
        return await prisma.geoRailwayStationGroup.findUnique({
          where: { id: selectedOption.value.toString() },
        });
      });
      if (station?.latitude && station?.longitude) {
        centerCoordinates = { latitude: station.latitude, longitude: station.longitude };
      }
    } else if (selectedOption.type === "building") {
      const building = await executeWithRetry(async () => {
        return await prisma.proBuilding.findUnique({
          where: { id: selectedOption.value.toString() },
        });
      });
      if (building?.latitude && building?.longitude) {
        centerCoordinates = { latitude: building.latitude, longitude: building.longitude };
      }
    } else if (selectedOption.type === "postalCode") {
      const postalCode = await executeWithRetry(async () => {
        return await prisma.geoPostalCode.findFirst({
          where: { postalCode: selectedOption.value.toString() },
        });
      });
      if (postalCode?.latitude && postalCode?.longitude) {
        centerCoordinates = { latitude: postalCode.latitude, longitude: postalCode.longitude };
      }
    }

    if (!centerCoordinates) {
      return {
        success: false,
        message: "座標情報が見つかりません",
      };
    }

    // Search for BUILDING records with broader range (5km instead of 3km)
    const SEARCH_RADIUS = 5000; // 5km in meters
    const RESULT_COUNT = 200; // More results for better analysis

    let whereFilters = {
      longitude: { not: null },
      latitude: { not: null },
      recordType: "BUILDING",
      landRight: "所有権",
      updatedAt: {
        gte: dayjs().subtract(VALUATION_MAX_YEAR_RETRIEVAL, "year").toDate(),
      },
      yearlyIncome: { gt: 0 }, // Only records with income data for cap rate calculation
      price: { gt: 0 }, // Only records with price data
    } as any;

    const records = await executeWithRetry(async () => {
      return await prisma.tllUserLambdaRecord.findMany({
        where: whereFilters,
        include: {
          priceChanges: {
            include: {
              company: true,
            },
          },
          nearestStationGroup: true,
          propertyAnalysisResult: true,
        },
        omit: {
          analysisSimulationConfig: true,
          analysisSimulationResults: true,
        },
      });
    });

    // Calculate distances and filter by radius
    let nearbyRecords: (UserLambdaRecordProps & { distance: number })[] = [];

    records.forEach((record: any) => {
      if (record.latitude && record.longitude) {
        const distance = calcCoordinateDistance(
          record.latitude,
          record.longitude,
          centerCoordinates!.latitude,
          centerCoordinates!.longitude
        ) * 1000; // convert to meters

        if (distance <= SEARCH_RADIUS) {
          nearbyRecords.push({
            ...record,
            distance,
          });
        }
      }
    });

    // Sort by distance and limit results
    nearbyRecords.sort((a, b) => a.distance - b.distance);
    nearbyRecords = nearbyRecords.slice(0, RESULT_COUNT);

    return {
      success: true,
      data: {
        nearbyRecords,
        centerCoordinates,
      },
    };
  } catch (error) {
    logger.error("🔥 Cap Rate Analysis Error:", error);
    console.error("🔥 Cap Rate Analysis Error:", error);
    return {
      success: false,
      message: `データの取得に失敗しました: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }
}
