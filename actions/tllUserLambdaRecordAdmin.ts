"use server";

import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";


export async function getNullStationGroupIdRecords() {
  const records = await prisma.tllUserLambdaRecord.findMany({
    where: {
      nearestStation: {
        not: null,
      },
      nearestStationGroupId: null,
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 100,
  });

  return records;
}


export async function getNullNearestStationWalkMinuteRecords() {
  logger.info("getNullNearestStationWalkMinuteRecords");

  const records = await prisma.tllUserLambdaRecord.findMany({
    where: {
      nearestStationWalkMinute: null,
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 100,
  });

  return records;
}

export async function getRentMansionNullBuldingIdRecords() {
  const records = await prisma.proMansionRent.findMany({
    where: {
      buildingId: null,
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 100,
  });

  return records;
}

export async function getRentBuildingNullBuldingIdRecords() {
  const records = await prisma.proBuildingHouseRent.findMany({
    where: {
      buildingId: null,
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 100,
  });

  return records;
}
