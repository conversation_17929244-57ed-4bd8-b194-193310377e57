"use server";

import { ActionResponse } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { supabase } from "@/lib/instances/supabase";
import openaiInstance from "@/lib/instances/openai";
import { File } from "buffer";
import { fetchtDataFromAllBody } from "./helper/cheerio";

const prompt = `
请取出以下信息，把结果存入json, json内容保留日语。
price(数字，万元为单位)，yearlyIncome(数字，万元为单位)，roi（转换为小数）， buildingSize(总建筑面积, 小数, 去掉单位), landSize(总土地面积, 小数, 去掉单位), address, nearestStation(去掉线路, 只保留车站名), nearestStationWalkMinute(徒步分数, 数字, 如果需要巴士用999), buildingMaterial(木造，铁骨造, 或者RC),  landRight(借地権、底地権、所有権), buildingBuiltYear

只返回json，不要返回其他内容。
`;

const prostProcess = (response: any) => {
  let res = response.choices[0].message.content;

  res = res ? res.replace(/```json|```/g, "").trim() : "";
  const parsedJson = res ? JSON.parse(res) : {};
  logger.info('parsedJson', parsedJson);

  return parsedJson;
}

export async function aiAnalyzeUrl({ url }: { url: string }): Promise<ActionResponse<any>> {
  const content = await fetchtDataFromAllBody(url);

  if (!content) {
    return { success: false, message: "未获取到内容" };
  }

  try {
    const response = await openaiInstance.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are a helpful AI that summarizes web content.",
        },
        {
          role: "user", content: [
            {
              type: "text",
              text: `Extracted webpage content:\n\n${JSON.stringify(content)}\n\n

              ${prompt}
              `,
            },
          ]
        }
      ],
    });
    let postProcessRes = await prostProcess(response);

    return {
      success: true,
      data: postProcessRes,
    };
  } catch (err: any) {
    logger.error("上传文件时出错:", err);
    return {
      success: false,
      message: "上传文件时出错",
      errors: err,
    };
  }
}

export async function aiAnalyzeImage({ base64Data, filename, mime }: { base64Data: string, filename: string, mime: string }): Promise<ActionResponse<any>> {
  const base64WithoutPrefix = base64Data.replace(/^data:image\/\w+;base64,/, "");
  const buffer = Buffer.from(base64WithoutPrefix, "base64");

  const file = new File([buffer], filename, { type: mime });

  if (!file) {
    return { success: false, message: "未上传文件" };
  }

  try {
    logger.debug('file', file);

    const buffer = Buffer.from(await file.arrayBuffer());
    // 上传到Supabase
    const { data, error } = await supabase.storage.from('customer-upload-valuation').upload(`images_${Date.now()}.png`, buffer, {
      contentType: mime,
      upsert: true,
      cacheControl: "3600",
    });

    if (error) {
      logger.error("🚨 上传到Supabase时出错:", error);
      return { success: false, message: "无法上传图像" };
    }

    const supabasePublicUrl = supabase.storage.from('customer-upload-valuation').getPublicUrl(data.path).data.publicUrl;
    logger.debug('supabasePublicUrl', supabasePublicUrl);

    const response = await openaiInstance.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt,
            },
            {
              type: "image_url",
              image_url: {
                url: supabasePublicUrl, // 使用正确的文件位置
              },
            },
          ],
        },
      ],
    });

    // 提取内容字段
    let content = response.choices[0].message.content;
    logger.info('content', content);
    content = content ? content.replace(/```json|```/g, "").trim() : "";
    const parsedJson = content ? JSON.parse(content) : {};

    return {
      success: true,
      data: {
        ...parsedJson,
        imageUrl: supabasePublicUrl,
      },
    };
  } catch (err: any) {
    logger.error("上传文件时出错:", err);
    return {
      success: false,
      message: "上传文件时出错",
      errors: err,
    };
  }
}