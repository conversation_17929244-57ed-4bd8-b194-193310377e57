"use server";

import { prisma } from '@/lib/prisma'; // 假设你有一个 prisma 实例
import { ActionResponse } from '@/lib/definitions';
import { UserLambdaRecordProps } from '@/lib/definitions/userLambdaRecord';

export async function getUserLambdaRecordSummary(data: any): Promise<ActionResponse<UserLambdaRecordProps[]>> {
  let whereParams = {};

  if (Object.keys(data).length === 0) {
    return {
      success: false,
      message: "No data provided, must get a filter",
    }
  }

  if (data) {
    const { postalCode, nearestStationGroupId } = data;

    let allRecentlyUpdatedRecordIds: string[] = [];

    whereParams = {
      ...(nearestStationGroupId ? { nearestStationGroupId } : {}),
      ...(postalCode ? { postalCode } : {}), // 仅在 postalCode 有值时添加
    }
  }

  if (Object.keys(whereParams).length === 0) {
    return {
      success: true,
      data: [],
    }
  }

  const records = await prisma.tllUserLambdaRecord.findMany({
    where: {
      ...whereParams,
      // FIXME: might be outdated
    },
    select: {
      // FIXME:foreigner key 
      id: true,
      recordType: true,
      price: true,
      buildingSize: true,
      recordValues: true,
      yearlyIncome: true,
      landSize: true
    },
  });

  return {
    success: true,
    data: records,
  };
}