"use server";

import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import dayjs from "dayjs";

export async function getProBuildingHouseRentForDay({ date }: { date: string }): Promise<ActionResponse<ProBuildingHouseRentProps[]>> {
  try {
    let records = await prisma.proBuildingHouseRent.findMany({
      where: {
        createdAt: {
          gte: dayjs(date).startOf("day").toDate(),
          lte: dayjs(date).startOf("day").add(1, "day").toDate(),
        },
      },
    });

    return {
      data: records,
      success: true,
      message: "Success",
    };
  } catch (error) {
    console.error(error);
    return {
      data: [],
      success: false,
      message: "Error",
    };
  }
}

export async function getProBuildingHouseRent({
  postalCode,
  nearestStationGroupId,
  recordType,
  recordTypes,
}: {
  postalCode?: number;
  nearestStationGroupId?: string;
  recordType: string;
  recordTypes?: string[];
}): Promise<ActionResponse<ProBuildingHouseRentProps[]>> {
  if (!postalCode && !nearestStationGroupId) {
    return {
      data: [],
      success: true,
      message: "Success",
    };
  }

  let where = {} as any;

  if (postalCode && nearestStationGroupId) {
    where = {
      OR: [
        {
          locationPostalCode: postalCode.toString(),
        },
        {
          nearestStationGroupId: nearestStationGroupId,
        },
      ],
    }
  } else if (postalCode) {
    where = {
      locationPostalCode: postalCode,
    }
  } else if (nearestStationGroupId) {
    where = {
      nearestStationGroupId: nearestStationGroupId,
    }
  }

  if (recordTypes) {
    where.recordType = {
      in: recordTypes,
    }
  } else {
    where.recordType = recordType;
  }

  const records = await prisma.proBuildingHouseRent.findMany({
    where: {
      ...where,
    },
  });

  return {
    data: records,
    success: true,
    message: "Success",
  };
}

export async function getProBuildingHouseRentSummary({
  postalCode,
  nearestStationGroupId,
  recordType,
}: {
  postalCode?: number;
  nearestStationGroupId?: string;
  recordType: "BUILDING" | "HOUSE";
}): Promise<ActionResponse<ProBuildingHouseRentProps[]>> {
  if (!postalCode && !nearestStationGroupId) {
    return {
      data: [],
      success: true,
      message: "Success",
    };
  }

  const records = await prisma.proBuildingHouseRent.findMany({
    where: {
      ...(postalCode ? { locationPostalCode: postalCode.toString() } : {}),
      ...(nearestStationGroupId ? { nearestStationGroupId } : {}),
      recordType: recordType,
    },

    select: {
      feeRent: true,
      feeManagement: true,
      buildingSize: true,
    },
  });

  return {
    data: records,
    success: true,
    message: "Success",
  };
}

export async function getProBuildingHouseRentDetail({
  id,
}: {
  id: string;
}): Promise<ActionResponse<ProBuildingHouseRentProps>> {
  const record = await prisma.proBuildingHouseRent.findUnique({
    where: { id },
  });

  return {
    data: record,
    success: true,
    message: "Success",
  };
}     