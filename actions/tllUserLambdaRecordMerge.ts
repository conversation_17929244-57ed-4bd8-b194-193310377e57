"use server";

import { ActionResponse } from "@/lib/definitions";
import { prisma } from "@/lib/prisma";



export const getAllUsageForThisRecord = async (userLambdaRecordId: string): Promise<ActionResponse> => {
  let res = {
    tllUserLambdaRecord: [],
    tllUserLambdaRecordPriceChange: [],
    tllUsersUserLambdaRecordsFavMap: [],
    proRawSumitomoAuction: [],
    tllUserLambdaRecordBid: [],
    tllUserLambdaRecordSearchHistory: [],
    propertyMaterialNameMapping: [],
  } as any;

  try {
    const [
      tllUserLambdaRecord,
      tllUserLambdaRecordPriceChange,
      tllUsersUserLambdaRecordsFavMap,
      proRawSumitomoAuction,
      tllUserLambdaRecordBid,
      tllUserLambdaRecordSearchHistory,
      propertyMaterialNameMapping,
    ] = await Promise.all([
      prisma.tllUserLambdaRecord.findMany({
        where: {
          id: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordPriceChange.findMany({
        where: {
          recordId: userLambdaRecordId,
        },
      }),
      prisma.tllUsersUserLambdaRecordsFavMap.findMany({
        where: {
          userLambdaRecordId: userLambdaRecordId,
        },
      }),
      prisma.proRawSumitomoAuction.findMany({
        where: {
          lambdaRecordId: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordBid.findMany({
        where: {
          recordId: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordSearchHistory.findMany({
        where: {
          recordId: userLambdaRecordId,
        },
      }),
      prisma.propertyMaterialNameMapping.findMany({
        where: {
          recordId: userLambdaRecordId,
        },
      }),
    ]);

    res.tllUserLambdaRecord = tllUserLambdaRecord;
    res.tllUserLambdaRecordPriceChange = tllUserLambdaRecordPriceChange;
    res.tllUsersUserLambdaRecordsFavMap = tllUsersUserLambdaRecordsFavMap;
    res.proRawSumitomoAuction = proRawSumitomoAuction;
    res.tllUserLambdaRecordBid = tllUserLambdaRecordBid;
    res.tllUserLambdaRecordSearchHistory = tllUserLambdaRecordSearchHistory;
    res.propertyMaterialNameMapping = propertyMaterialNameMapping;

    return {
      success: true,
      data: res,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Internal server error",
    };
  }
}

export const deleteAllUsageForThisRecord = async (userLambdaRecordId: string): Promise<ActionResponse> => {


  try {
    const [
      tllUserLambdaRecord,
      tllUserLambdaRecordPriceChange,
      tllUsersUserLambdaRecordsFavMap,
      proRawSumitomoAuction,
      tllUserLambdaRecordBid,
      tllUserLambdaRecordSearchHistory,
      propertyMaterialNameMapping,
    ] = await Promise.all([
      prisma.tllUserLambdaRecord.deleteMany({
        where: {
          id: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordPriceChange.deleteMany({
        where: {
          recordId: userLambdaRecordId,
        },
      }),
      prisma.tllUsersUserLambdaRecordsFavMap.deleteMany({
        where: {
          userLambdaRecordId: userLambdaRecordId,
        },
      }),
      prisma.proRawSumitomoAuction.deleteMany({
        where: {
          lambdaRecordId: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordBid.deleteMany({
        where: {
          recordId: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordSearchHistory.deleteMany({
        where: {
          recordId: userLambdaRecordId,
        },
      }),
      prisma.propertyMaterialNameMapping.deleteMany({
        where: {
          recordId: userLambdaRecordId,
        },
      }),
    ]);

    return {
      success: true,
      message: {
        tllUserLambdaRecord,
        tllUserLambdaRecordPriceChange,
        tllUsersUserLambdaRecordsFavMap,
        proRawSumitomoAuction,
        tllUserLambdaRecordBid,
        tllUserLambdaRecordSearchHistory,
        propertyMaterialNameMapping,
      } as any,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Internal server error",
    };
  }
}


export const updateAllUsageForThisRecord = async (userLambdaRecordId: string, newId: string): Promise<ActionResponse> => {

  try {
    const [
      tllUserLambdaRecord,
      tllUserLambdaRecordPriceChange,
      tllUsersUserLambdaRecordsFavMap,
      proRawSumitomoAuction,
      tllUserLambdaRecordBid,
      tllUserLambdaRecordSearchHistory,
      propertyMaterialNameMapping,
    ] = await Promise.all([
      prisma.tllUserLambdaRecord.update({
        data: {
          id: newId,
        },
        where: {
          id: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordPriceChange.update({
        data: {
          recordId: newId,
        },
        where: {
          id: userLambdaRecordId,
        },
      }),
      prisma.tllUsersUserLambdaRecordsFavMap.update({
        data: {
          userLambdaRecordId: newId,
        },
        where: {
          id: userLambdaRecordId,
        },
      }),
      prisma.proRawSumitomoAuction.update({
        data: {
          lambdaRecordId: newId,
        },
        where: {
          id: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordBid.update({
        data: {
          recordId: newId,
        },
        where: {
          id: userLambdaRecordId,
        },
      }),
      prisma.tllUserLambdaRecordSearchHistory.update({
        data: {
          recordId: newId,
        },
        where: {
          id: userLambdaRecordId,
        },
      }),
      prisma.propertyMaterialNameMapping.update({
        data: {
          recordId: newId,
        },
        where: {
          id: userLambdaRecordId,
        },
      }),
    ]);

    return {
      success: true,
      data: {
        tllUserLambdaRecord,
        tllUserLambdaRecordPriceChange,
        tllUsersUserLambdaRecordsFavMap,
        proRawSumitomoAuction,
        tllUserLambdaRecordBid,
        tllUserLambdaRecordSearchHistory,
        propertyMaterialNameMapping,
      },
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Internal server error",
    };
  }
}