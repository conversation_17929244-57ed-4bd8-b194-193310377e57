"use server"

import { prisma } from "@/lib/prisma"
import { Prisma, ProProject } from "@prisma/client"
import { ActionResponse } from "@/lib/definitions"
import dayjs from "dayjs"

export type FuzzySearchProjectsParams = {
  name?: string
  type?: "CONSTRUCTION" | "ADDITION" | "DEMOLITION"
  limit?: number
}

export async function getProjectsByPostalCodeAction({ postalCode }: { postalCode: number }): Promise<ActionResponse<any[]>> {
  try {
    let matchPostalCode = await prisma.geoPostalCode.findFirst({
      where: {
        postalCode: postalCode.toString(),
      },
    })

    let postalCodes = [] as number[];
    if (matchPostalCode) {
      let areaCode = matchPostalCode.areaCode;
      let postalCodesMatched = await prisma.geoPostalCode.findMany({
        where: {
          areaCode: areaCode,
        },
      })

      if (postalCodesMatched && postalCodesMatched.length > 0) {
        postalCodes.push(...postalCodesMatched.map(p => Number(p.postalCode)));
      }
    } else {
      postalCodes.push(postalCode);
    }

    const projects = await prisma.proProject.findMany({
      where: {
        postalCode: { in: postalCodes.map(code => code.toString()) as string[] },
        type: {
          not: "DEMOLITION"
        },
        projectEndDateYear: {
          gte: 2025
        },
      },
      orderBy: {
        projectEndDate: "desc",
      },

    })

    return {
      success: true,
      data: projects,
    }
  } catch (error) {
    console.error('🔥 Error in getProjectsByPostalCodeAction:', error)
    return {
      success: false,
      message: 'Failed to get projects by postal code',
    }
  }
}

export async function fuzzySearchProjectsAction({ name, limit = 10 }: FuzzySearchProjectsParams) {
  try {
    console.log('🔥 Searching projects with params:', { name, limit })

    const projects = await prisma.proProject.findMany({
      where: {
        OR: [{
          name: {
            contains: name,
          },
        }, {
          address: {
            contains: name,
          },
        }],
      },
      orderBy: {
        projectEndDate: "desc",
      },
    })

    return {
      success: true,
      data: projects,
    }
  } catch (error) {
    console.error('🔥 Error in fuzzySearchProjectsAction:', error)
    return {
      success: false,
      message: 'Failed to search projects',
      error,
    }
  }
}

export async function groupPostalCodeByYearAction({
  unit = "postalCode"
}: {
  unit: "postalCode" | "areaCode" | "prefectureCode"
}): Promise<ActionResponse<any>> {

  try {
    const result = await prisma.proProject.groupBy({
      by: [unit, "projectEndDateYear"],
      _sum: {
        constructionArea: true,
      },
      _count: {
        id: true, // 👉 统计该邮编下的项目数
      },
      orderBy: {
        _sum: {
          constructionArea: 'desc',
        },
      },
      where: {
        postalCode: {
          not: null,
        },
        constructionArea: {
          not: null,
        },
        type: {
          not: "DEMOLITION"
        },
        projectEndDateYear: {
          gte: 2023,
          lte: 2028,
        },
      },
    });

    const unitByUnit = result.map(g => g[unit as keyof typeof g]);

    // Step 2: fetch postal code info from related table
    let dataEnriched = null;

    if (unit === "postalCode") {
      const postalInfoList = await prisma.geoPostalCode.findMany({
        where: {
          postalCode: { in: unitByUnit.filter(code => code !== null).map(code => code.toString()) },
        },
        select: {
          postalCode: true,
          cityName: true,
          areaName: true,
          choumeName: true,
          prefectureName: true,
        },
      });

      const unitPadded = Object.fromEntries(
        postalInfoList.map(info => [info.postalCode, info])
      );

      // Step 3: enrich results
      dataEnriched = result.map(g => {
        let m = unitPadded[g[unit] as unknown as string] || null
        let compositeName = m === null ? "" : `${m.prefectureName}${m.cityName}${m.areaName}${m.choumeName}`

        return {
          year: g.projectEndDateYear,
          postalCode: g.postalCode,
          totalArea: g._sum.constructionArea,
          count: g._count.id,
          compositeName: compositeName,
          info: m,
        }
      });
    } else if (unit === "areaCode") {
      const areaInfoList = await prisma.geoArea.findMany({
        where: {
          code: { in: unitByUnit.filter(code => code !== null) as number[] },
        },
        select: {
          code: true,
          nameJa: true,
          prefecture: {
            select: {
              name: true,
            }
          }
        },
      });

      const unitPadded = Object.fromEntries(
        areaInfoList.map(info => [info.code, info])
      );

      // Step 3: enrich results
      dataEnriched = result.map(g => {
        let m = unitPadded[g[unit] as unknown as string] || null
        let compositeName = m === null ? "" : `${m.prefecture?.name || ""}${m.nameJa || ""}`

        return {
          year: g.projectEndDateYear,
          postalCode: g.postalCode,
          totalArea: g._sum.constructionArea,
          count: g._count.id,
          compositeName: compositeName,
          info: m,
        }
      });
    }



    return {
      success: true,
      data: dataEnriched,
    }
  } catch (error) {
    console.error('🔥 Error in groupPostalCodeByYearAction:', error)
    return {
      success: false,
      message: 'Failed to group postal code by year',
    }
  }
} 