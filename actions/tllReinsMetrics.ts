"use server";
import { ActionResponse } from "@/lib/definitions";
import { prisma } from "@/lib/prisma";
import { TllReinsMetricsProps } from "@/lib/definitions/tllReinsMetrics";
import dayjs from "dayjs";

export async function getReinsMetrics(): Promise<ActionResponse<TllReinsMetricsProps[]>> {
  try {
    const reinsMetrics = await prisma.tllReinsMetric.findMany({
      where: {
        recordDate: {
          gte: dayjs().subtract(3, "month").toDate(),
        }
      },
      orderBy: {
        recordDate: "desc",
      },
    });

    return {
      success: true,
      data: reinsMetrics,
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to get reins metrics",
    };
  }
}

export async function refillEmptyData({
  address,
}: {
  address: string;
}): Promise<ActionResponse<TllReinsMetricsProps[]>> {

  console.log("🔥 listingTitle 🔥", address);

  try {
    // 获取过去两周的日期范围
    const endDate = dayjs();
    const startDate = dayjs().subtract(14, "day");

    // 获取该地址过去两周的所有记录
    const reinsMetrics = await prisma.tllReinsMetric.findMany({
      where: {
        address: address,
        listingType: "売物件",
        recordDate: {
          gte: startDate.toDate(),
          lte: endDate.toDate()
        },
      },
      orderBy: {
        recordDate: "asc",
      },
    });

    // 生成过去两周的所有日期
    const allDates = Array.from({ length: 15 }, (_, i) =>
      startDate.add(i, "day").format("YYYY-MM-DD")
    );

    // 创建日期到记录的映射
    const dateToRecord = new Map(
      reinsMetrics.map(record => [
        dayjs(record.recordDate).format("YYYY-MM-DD"),
        record
      ])
    );

    // 填充缺失的日期
    const recordsToInsert = allDates.map(date => {
      if (dateToRecord.has(date)) {
        return null; // 跳过已存在的记录
      }

      // 获取前7天的记录用于计算平均值
      const previousWeekRecords = reinsMetrics.filter(record => {
        const recordDate = dayjs(record.recordDate);
        const targetDate = dayjs(date);
        return recordDate.isBefore(targetDate) &&
          recordDate.isAfter(targetDate.subtract(8, "day"));
      });

      if (previousWeekRecords.length === 0) {
        return null;
      }

      // 获取前一条记录（最近的记录）
      const previousRecord = reinsMetrics
        .filter(record => dayjs(record.recordDate).isBefore(dayjs(date)))
        .sort((a, b) => dayjs(b.recordDate).valueOf() - dayjs(a.recordDate).valueOf())[0];

      if (!previousRecord) {
        return null;
      }

      // 计算前7天的平均值，但只用于 downloadDetailsCount 和 checkDetailsCount
      const avgDownloadDetailsCount = Math.round(previousWeekRecords.reduce((sum, r) => sum + r.downloadDetailsCount, 0) / previousWeekRecords.length);
      const avgCheckDetailsCount = Math.round(previousWeekRecords.reduce((sum, r) => sum + r.checkDetailsCount, 0) / previousWeekRecords.length);

      // 创建新记录
      return {
        reinsId: previousRecord.reinsId,
        listingTitle: previousRecord.listingTitle,
        listingType: previousRecord.listingType,
        listingSubtype: previousRecord.listingSubtype,
        address: previousRecord.address,
        roomNumber: previousRecord.roomNumber,
        price: previousRecord.price,
        recordDate: new Date(date),
        registrationDate: previousRecord.registrationDate,
        changeDate: previousRecord.changeDate,
        downloadDetailsCount: avgDownloadDetailsCount,
        checkDetailsCount: avgCheckDetailsCount,
      };
    }).filter((record): record is NonNullable<typeof record> => record !== null);

    console.log("🔥 需要插入的记录数 🔥", recordsToInsert.length);

    if (recordsToInsert.length > 0) {
      // 批量插入记录
      await prisma.tllReinsMetric.createMany({
        data: recordsToInsert,
        skipDuplicates: true, // 跳过可能的重复记录
      });

      // 获取插入后的所有记录（包括新插入的和原有的）
      const updatedRecords = await prisma.tllReinsMetric.findMany({
        where: {
          address: address,
          listingType: "売物件",
          recordDate: {
            gte: startDate.toDate(),
            lte: endDate.toDate()
          },
        },
        orderBy: {
          recordDate: "desc", // 按日期降序排列
        },
      });

      console.log("🔥 更新后的总记录数 🔥", updatedRecords.length);

      return {
        success: true,
        data: updatedRecords,
      };
    }

    // 如果没有需要插入的记录，返回原有记录
    return {
      success: true,
      data: reinsMetrics,
    };
  } catch (error) {
    console.error("🔥 错误 🔥", error);
    return {
      success: false,
      message: "データの取得に失敗しました",
    };
  }
}