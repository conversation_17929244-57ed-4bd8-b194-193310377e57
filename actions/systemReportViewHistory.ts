"use server";

import { auth } from "@/lib/auth";
import { ActionResponse } from "@/lib/definitions";
import { prisma } from "@/lib/prisma";
import { SystemReportViewHistoryProps, SystemReportViewHistoryRecordType } from "@/lib/definitions/system";
import dayjs from "dayjs";
export const getMyReportViewHistory = async ({
  recordType,
  dateFrom,
}: {
  recordType?: SystemReportViewHistoryRecordType;
  dateFrom?: string;
}): Promise<ActionResponse<SystemReportViewHistoryProps[]>> => {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "用户未登录",
    };
  }

  try {
    const reportViewHistory = await prisma.systemReportViewHistory.findMany({
      where: {
        userId: currentUser.id,
        ...(recordType ? { recordType } : {}),
        ...(dateFrom ? { viewDate: { gte: dateFrom } } : {}),
      },
      include: {
        building: true,
      },
    });

    return {
      success: true,
      data: reportViewHistory,
    };
  } catch (error) {
    console.error("🚨 获取我的物件浏览历史时出错:", error);
    return {
      success: false,
      message: "获取我的物件浏览历史时出错",
    };
  }
};




export async function getReportViewHistoryCountPerUserPerDay(): Promise<
  ActionResponse<SystemReportViewHistoryProps>
> {
  try {
    const session = await auth();
    const currentUser = session?.user;

    if (!currentUser?.id) {
      return {
        success: false,
        message: "用户未登录",
      };
    }

    // ✅ 取所有历史数据（可加时间范围过滤）
    const records = await prisma.systemReportViewHistory.findMany({
      select: {
        userId: true,
        viewDate: true,
      },
      orderBy: {
        viewDate: "desc",
      },
    });

    // ✅ 聚合：按 userId + 日期 分组计数
    const counter = new Map<string, { userId: string; dateOfRecord: string; count: number }>();

    for (const r of records) {
      const dateStr = dayjs(r.viewDate).format("YYYY-MM-DD");
      const key = `${r.userId}_${dateStr}`;
      if (!counter.has(key)) {
        counter.set(key, { userId: r.userId, dateOfRecord: dateStr, count: 1 });
      } else {
        counter.get(key)!.count += 1;
      }
    }

    const grouped = Array.from(counter.values());

    const userIds = [...new Set(grouped.map((r) => r.userId))];
    const users = await prisma.tllUser.findMany({
      where: { id: { in: userIds } },
      select: {
        id: true,
        accessLevel: true,
        subscriptionPlan: true,
        email: true,
      },
    });

    const userMap = Object.fromEntries(users.map((u) => [u.id, u]));

    const data = grouped.map((r) => ({
      ...r,
      accessLevel: userMap[r.userId]?.accessLevel ?? null,
      subscriptionPlan: userMap[r.userId]?.subscriptionPlan ?? null,
      email: userMap[r.userId]?.email ?? null,
    }));

    return {
      success: true,
      data,
    };
  } catch (error) {
    console.error("🚨 获取我的物件浏览历史时出错:", error);
    return {
      success: false,
      message: "获取我的物件浏览历史时出错",
    };
  }
}