"use server";

import OpenA<PERSON> from "openai";
import * as cheerio from "cheerio";
import Firecrawl from "@mendable/firecrawl-js";
import openaiInstance from "@/lib/instances/openai";
import { ActionResponse, AiSnsContentProps, AiSnsContentsContentSourceType } from "@/lib/definitions";

import { allInstructions } from "@/lib/constants/aiInstructions";
import { fetchArticleContent } from "@/actions/helper/cheerio";

export async function createSummaryFromOpenAI(url: string): Promise<ActionResponse<string>> {
  try {
    const articleContent = await fetchArticleContent(url);

    if (!articleContent) {
      return {
        success: false,
        message: "Failed to extract article content.",
      };
    }

    console.log('🔥fetching html for url completed ... starting to summarize');

    const resOpenAI = await openaiInstance.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user", content: `${articleContent}
        
        请根据上面的文章文章内容，给我JSON output ：{
          "summary": 文章摘要500字,
          "imagePrompt": 根据文章内容，生成一个图片描述，用于生成phtogtaphic, realistic style 的图片. 输出词应该是句子不是列表。如果你觉得适合近景, 要求 
          a)  resolution ratio 3:4 
          b)	Use specific brand names (Canon, Sony, Nikon, etc.).
          c)	Describe imperfections (dust on surfaces, wrinkles on skin, reflections on glass).
          d)	Mention depth of field (bokeh, blurred background, sharp focus on subject).
          e)	Include lighting type (soft daylight, harsh neon, warm candlelight, etc.).
          f)	Use photography jargon (ISO, f-stop, focal length, shutter speed).
          如果觉得适合远景, 要求 
          a)  resolution ratio 3:4 
          b)	Time of Day & Lighting – Golden hour, blue hour, night with neon lights, midday shadows.
          c)	Weather Conditions – Foggy, rainy, clear skies, sunset glow, snowfall.
          d)	Perspective & Lens – Aerial drone shot, wide-angle, street-level, long exposure.
          e)	Key Details – Reflections on wet streets, moving cars, glowing skyscraper windows.
          e)	Realism Enhancers – Film grain, light flares, atmospheric haze, depth of field.
          f)	Geographical Specifics – New York skyline, Tokyo at night, European old town streets.
        }。 输出内容为中文。请将结果存入 JSON，不要输出任何其他内容。 ` },
      ],
    });

    let content = resOpenAI.choices[0].message.content;

    if (content && typeof content === 'object' && Object.keys(content).length > 0) {
      return {
        success: true,
        data: content,
        message: 'Success',
      };
    }

    content = content ? content.replace(/```json|```/g, "").trim() : "";
    console.log('content', content);
    const parsedJson = content ? JSON.parse(content) : {};
    console.log('🔥parsedJson', parsedJson);

    return {
      success: true,
      data: parsedJson,
    };
  } catch (error) {
    console.error("🚨 要约时出错:", error);
    return {
      success: false,
      message: error as string || "无法获取数据",
    };
  }
}

export async function rewriteContentFromOpenAI(url: string, aiSnsContent: AiSnsContentProps): Promise<ActionResponse<string>> {
  try {
    let instruction = aiSnsContent.contentByOpenAiPrompt || allInstructions.text.XHS_INSTRUCTION;

    let textSourceType = aiSnsContent.contentSourceType;
    let articleContent = "";

    if (textSourceType === AiSnsContentsContentSourceType.FULL_TEXT) {
      console.log('🔥fetching html for url');
      articleContent = await fetchArticleContent(url) || "";
    } else if (textSourceType === AiSnsContentsContentSourceType.MANUAL) {
      articleContent = aiSnsContent.newsFullTextManual || "";
    } else if (textSourceType === AiSnsContentsContentSourceType.SUMMARY) {
      articleContent = aiSnsContent.newsSummary || aiSnsContent.newsDescription || "";
    } else if (textSourceType === AiSnsContentsContentSourceType.TITLE) {
      articleContent = aiSnsContent.newsTitle || "";
    }

    if (!articleContent) {
      return {
        success: false,
        message: "Failed to extract article content.",
      };
    }

    console.log('🔥 [Openai] artcle content type to use is,', textSourceType);
    console.log('🔥 [Openai]artcle instruction to use is,', instruction);

    const resOpenAI = await openaiInstance.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: instruction,
        },
        { role: "user", content: `请根据以下文章内容，按照要求改写为适合SNS引流的营销文案：\n\n${articleContent}` },
      ],
    });

    return {
      success: true,
      data: resOpenAI.choices[0].message.content
    };
  } catch (error) {
    console.error("🚨 要约时出错:", error);
    return {
      success: false,
      message: error as string || "无法获取数据",
    };
  }
}

export async function rewriteContentFromDeepSeek(url: string, aiSnsContent: AiSnsContentProps): Promise<ActionResponse<string>> {
  console.log('🔥rewriteContentFromDeepSeek', url, aiSnsContent);

  try {
    let instruction = aiSnsContent.contentByDeepSeekPrompt || allInstructions.text.XHS_INSTRUCTION;

    let textSourceType = aiSnsContent.contentSourceType;
    let articleContent = "";

    if (textSourceType === AiSnsContentsContentSourceType.SUMMARY && !aiSnsContent.newsSummary) {
      return {
        success: false,
        message: "No summary found",
      };
    }

    if (textSourceType === AiSnsContentsContentSourceType.FULL_TEXT) {
      console.log('🔥fetching html for url');
      articleContent = await fetchArticleContent(url) || "";
    } else if (textSourceType === AiSnsContentsContentSourceType.MANUAL) {
      articleContent = aiSnsContent.newsFullTextManual || "";
    } else if (textSourceType === AiSnsContentsContentSourceType.SUMMARY) {
      articleContent = aiSnsContent.newsSummary || aiSnsContent.newsDescription || "";
    } else if (textSourceType === AiSnsContentsContentSourceType.TITLE) {
      articleContent = aiSnsContent.newsTitle || "";
    }

    if (!articleContent) {
      return {
        success: false,
        message: "Failed to extract article content.",
      };
    }

    console.log('🔥 [Deepseek] artcle content type to use is,', textSourceType);
    console.log('🔥 [Deepseek] artcle instruction to use is,', instruction);

    const DEEPSEEK_API_URL = "https://api.lkeap.cloud.tencent.com/v1/chat/completions"
    const DEEPSEEK_API_KEY = process.env.TENCENT_CLOUD_API_USING_OPENAI;
    const resDeepSeek = await fetch(DEEPSEEK_API_URL, {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${DEEPSEEK_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "deepseek-v3",
        messages: [
          { role: "system", content: instruction }, // Assign the role
          { role: "user", content: `请根据以下文章内容，按照要求改写为适合SNS引流的营销文案：\n\n${articleContent}` },
        ],
        temperature: 0.7,
      }),
    });

    let res = await resDeepSeek.json();

    return {
      success: true,
      data: res?.choices?.[0].message?.content || "No Data"
    };
  } catch (error) {
    console.error("🚨 要约时出错:", error);
    return {
      success: false,
      message: error as string || "无法获取数据",
    };
  }
}
