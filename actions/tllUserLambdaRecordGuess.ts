"use server";
import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";

interface FindPossibleMatchParams {
  buildingSize?: number;
  landSize?: number;
  address?: string;
  nearestStation?: string;
  buildingBuiltYear?: number;
}

export async function findPossibleMatchAction(params?: FindPossibleMatchParams) {
  if (!params) {
    return {
      success: false,
      message: "No parameters provided",
    }
  }

  const { buildingSize, landSize, address, nearestStation } = params;

  let whereParams = {} as FindPossibleMatchParams;

  if (buildingSize !== undefined) {
    whereParams.buildingSize = buildingSize;
  }
  if (landSize) {
    whereParams.landSize = landSize;
  }
  if (address) {
    whereParams.address = address;
  }
  if (nearestStation) {
    whereParams.nearestStation = nearestStation;
  }

  if (Object.keys(whereParams).length === 0) {
    return {
      success: false,
      message: "No parameters provided",
    }
  }

  const record = await prisma.tllUserLambdaRecord.findMany({
    where: whereParams,
    // 这里可以根据params进行进一步的查询或处理
  });

  return {
    success: true,
    data: record,
  }
}