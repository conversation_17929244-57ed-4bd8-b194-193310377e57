"use server";

import { prisma } from "@/lib/prisma";
import { AiSnsContentProps } from "@/lib/definitions";
import { ActionResponse } from "@/lib/definitions";
import { auth } from "@/lib/auth";

export const getAiSnsContents = async (): Promise<ActionResponse<AiSnsContentProps[]>> => {
  try {
    const aiSnsContents = await prisma.aiSnsContent.findMany({
      include: {
        creator: {
          select: {
            name: true,
          },
        },
      }
    });
    return {
      success: true,
      data: aiSnsContents,
      message: "AI SNS コンテンツを取得しました。",
    };
  } catch (error) {
    return {
      success: false,
      data: null,
      message: "AI SNS コンテンツを取得できませんでした。",
    };
  }
};

export const getAiSnsContentById = async (id: string): Promise<ActionResponse<AiSnsContentProps>> => {
  try {
    const aiSnsContent = await prisma.aiSnsContent.findUnique({
      where: {
        id: id,
      },
    });
    return {
      success: true,
      data: aiSnsContent,
      message: "AI SNS コンテンツを取得しました。",
    };
  } catch (error) {
    return {
      success: false,
      data: null,
      message: "AI SNS コンテンツを取得できませんでした。",
    };
  }
};

export const createAiSnsContent = async (data: AiSnsContentProps): Promise<ActionResponse<AiSnsContentProps>> => {
  try {
    if (!data.creatorUserId || data.creatorUserId === undefined) {
      return {
        success: false,
        data: null,
        message: "ユーザーIDがありません。",
      };
    }

    const aiSnsContent = await prisma.aiSnsContent.create({
      data: {
        ...data,
        creatorUserId: data.creatorUserId as string,
      },
    });
    return {
      success: true,
      data: aiSnsContent,
      message: "AI SNS コンテンツを作成しました。",
    };
  } catch (error) {
    console.log('🔥error', error);
    return {
      success: false,
      data: null,
      message: "AI SNS コンテンツを作成できませんでした。",
    };
  }
};

export const updateAiSnsContent = async (id: string, data: AiSnsContentProps): Promise<ActionResponse<AiSnsContentProps>> => {
  console.log('🔥data for uploading', data);
  try {
    const aiSnsContent = await prisma.aiSnsContent.update({
      where: { id },
      data,
    });
    return {
      success: true,
      data: aiSnsContent,
      message: "AI SNS コンテンツを更新しました。",
    };
  } catch (error) {
    return {
      success: false,
      data: null,
      message: "AI SNS コンテンツを更新できませんでした。",
    };
  }
};