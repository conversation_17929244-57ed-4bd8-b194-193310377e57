"use server";

import { auth } from "@/lib/auth";
import { ActionResponse } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { SystemReportViewHistoryRecordType } from "@/lib/definitions/system";

export const getProBuildingFuzzyName = async ({ name, isFuzzy }: { name: string, isFuzzy: boolean }): Promise<ActionResponse> => {
  try {
    const proBuilding = await prisma.proBuilding.findMany({
      where: { nameJa: isFuzzy ? { contains: name } : { equals: name } },
      orderBy: {
        nameJa: "asc",
      },
      include: {
        tllUserLambdaRecords: {
          select: {
            id: true
          },
        },
        mansionRents: {
          select: {
            id: true
          },
        },
      },
    });

    return {
      success: true,
      data: proBuilding,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Internal server error",
    };
  }
}

export const getNearbyBuildingSummary = async (buildingId: string): Promise<ActionResponse> => {
  try {

    logger.info(`[getNearbyBuildingSummary] ${buildingId}`);

    const building = await prisma.proBuilding.findUnique({
      where: {
        id: buildingId,
      },
    });

    if (!building) {
      return {
        success: false,
        message: "Building not found",
      };
    }

    logger.info(`[getNearbyBuildingSummary] ${building.nameJa} ${building.postalCode}`);

    let postalCode = building.postalCode;
    let nearestStationGroupId = building.nearestStationGroupId;
    let whereParams = {};
    if (!postalCode) {
      whereParams = {
        nearestStationGroupId: nearestStationGroupId,
      };
    } else {
      whereParams = {
        postalCode: postalCode,
      };
    }

    if (Object.keys(whereParams).length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    logger.info(`[getNearbyBuildingSummary] ${JSON.stringify(whereParams)}`);

    const nearbyBuildingSummary = await prisma.proBuilding.findMany({
      where: {
        ...whereParams,
      },
      include: {
        mansionRents: {
          select: {
            id: true,
            feeRent: true,
            unitSize: true,
            createdAt: true,
          },
        },
        tllUserLambdaRecords: {
          select: {
            id: true,
            price: true, // Do not use price change to make it simpler 
            buildingSize: true,
            createdAt: true,
            recordValues: true,
            priceChanges: true
          },
        },
      },
    });

    return {
      success: true,
      // Do not show same as it will block the item form being shown as the center point
      data: nearbyBuildingSummary.filter((b) => b.latitude !== building.latitude && b.longitude !== building.longitude),
    };
  } catch (error) {
    console.error(error);

    return {
      success: false,
      message: "Internal server error",
    };
  }
};

export const getProBuildingById = async ({ id, isSystem }: { id: string, isSystem: boolean }): Promise<ActionResponse> => {
  try {
    const session = await auth();
    const currentUser = session?.user;

    if (!id) {
      return {
        success: false,
        message: "Building ID is required",
      };
    }

    if (!currentUser || !currentUser?.id) {
      return {
        success: false,
        message: "用户未登录",
      };
    }

    // TODO: do the quota check ehre too 
    // Also create record for the user (for pricing history)
    if (!isSystem) {
      const searchHistoryExists = await prisma.systemReportViewHistory.findFirst({
        where: {
          recordType: SystemReportViewHistoryRecordType.MANSION,
          userId: currentUser.id,
          buildingId: id,
          isValid: true,
        },
      });

      if (!searchHistoryExists) {
        const dataToCreate = {
          recordType: SystemReportViewHistoryRecordType.MANSION,
          userId: currentUser.id,
          buildingId: id,
          viewDate: dayjsWithTz().toISOString(),
        }

        await prisma.systemReportViewHistory.create({
          data: dataToCreate,
        });
      }
    }

    const proBuilding = await prisma.proBuilding.findUnique({
      where: { id },
      include: {
        tllUserLambdaRecords: {
          include: {
            priceChanges: {
              include: {
                // For dislpay the broker name in the data table
                company: {
                  select: {
                    id: true,
                    fullName: true,
                  },
                },
              }
            }
          },
        },
        mansionRents: true,
      },
    });

    return {
      success: true,
      data: proBuilding,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Internal server error",
    };
  }
};

export const getBuildingWithWeridName = async (): Promise<ActionResponse> => {
  try {
    const result = await prisma.$queryRawUnsafe(`
      SELECT *
      FROM "pro_buildings"
      WHERE "name_ja" ~ '手数料[\\d．．０-９\\.．]*パー?|手[\\d．．０-９\\.．]*パー?|手.*?有り?|（手相）|手あり|手３．０|手３|手２|手５|再募|内見可|テアリ|[ａＡ][ｄＤ][０-９０-９]+|[０-９]+階数'
    `);

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Internal server error",
    };
  }
}

export const getAllUsageForThisBuilding = async (buildingId: string): Promise<ActionResponse> => {
  let res = {
    "userLambdaRecords": [],
    "proRawSumitomoAuctions": [],
    "proBuildingHouseRents": [],
    "proMansionRents": [],
    "systemReportViewHistories": [],
  } as any;

  try {
    const [
      userLambdaRecords,
      proRawSumitomoAuctions,
      proBuildingHouseRents,
      proMansionRents,
      systemReportViewHistories
    ] = await Promise.all([
      prisma.tllUserLambdaRecord.findMany({
        where: {
          buildingId: buildingId,
        },
      }),
      prisma.proRawSumitomoAuction.findMany({
        where: {
          buildingId: buildingId,
        },
      }),
      prisma.proBuildingHouseRent.findMany({
        where: {
          buildingId: buildingId,
        },
      }),
      prisma.proMansionRent.findMany({
        where: {
          buildingId: buildingId,
        },
      }),
      prisma.systemReportViewHistory.findMany({
        where: {
          buildingId: buildingId,
        },
      })
    ]);

    res.userLambdaRecords = userLambdaRecords;
    res.proRawSumitomoAuctions = proRawSumitomoAuctions;
    res.proBuildingHouseRents = proBuildingHouseRents;
    res.proMansionRents = proMansionRents;
    res.systemReportViewHistories = systemReportViewHistories;

    return {
      success: true,
      data: res,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Internal server error",
    };
  }
}

export const updateBuildingWithNewNumber = async ({ oldId, newId }: { oldId: string, newId: string }): Promise<ActionResponse> => {
  try {
    let countForTllUserLambdaRecord = await prisma.tllUserLambdaRecord.updateMany({
      where: {
        buildingId: oldId,
      },
      data: {
        buildingId: newId,
      },
    });

    let countForProRawSumitomoAuction = await prisma.proRawSumitomoAuction.updateMany({
      where: {
        buildingId: oldId,
      },
      data: {
        buildingId: newId,
      },
    });

    let countForProBuildingHouseRent = await prisma.proBuildingHouseRent.updateMany({
      where: {
        buildingId: oldId,
      },
      data: {
        buildingId: newId,
      },
    });

    let countForProMansionRent = await prisma.proMansionRent.updateMany({
      where: {
        buildingId: oldId,
      },
      data: {
        buildingId: newId,
      },
    });

    let countForSystemReportViewHistory = await prisma.systemReportViewHistory.updateMany({
      where: {
        buildingId: oldId,
      },
      data: {
        buildingId: newId,
      },
    });

    let data = {
      countForTllUserLambdaRecord,
      countForProRawSumitomoAuction,
      countForProBuildingHouseRent,
      countForProMansionRent,
      countForSystemReportViewHistory,
    };

    await prisma.proBuilding.delete({
      where: {
        id: oldId,
      },
    });

    return {
      success: true,
      message: "Building updated",
      data: data,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Internal server error",
    };
  }
}