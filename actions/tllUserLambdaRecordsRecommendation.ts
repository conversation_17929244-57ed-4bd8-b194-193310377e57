"use server";

import { prisma } from "@/lib/prisma";
import dayjs from "dayjs";
import { ActionResponse } from "@/lib/definitions";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { padWithLink } from "./dashboard";

export async function getRecommendationOtherTypesForRecommendation(data: any): Promise<ActionResponse<UserLambdaRecordProps[]>> {
  try {
    let whereParams = {
    };
    if (Object.keys(data).length === 0) {
      return {
        success: false,
        message: "No data provided, must get a filter",
      }
    }


    if (data) {
      const { nearestStation, postalCode, recordType, buildingId, nearestStationGroupId, areaCode } = data;

      whereParams = {
        createdAt: {
          gte: dayjs().subtract(1, 'month').toDate(),
        },
        ...(nearestStationGroupId ? { nearestStationGroupId: nearestStationGroupId.toString() } : {}),
        ...(nearestStation ? { nearestStation } : {}), // 仅在 nearestStation 有值时添加
        ...(postalCode ? { postalCode: Number(postalCode) } : {}), // 仅在 postalCode 有值时添加
        ...(recordType ? {
          recordType: {
            notIn: [recordType, "MANSION"] // take out mansion because too many
          }
        } : {}), // 仅在 recordType 有值时添加
        ...(buildingId ? { buildingId: buildingId.toString() } : {}), // 仅在 buildingId 有值时添加
        ...(areaCode ? { areaCode: Number(areaCode) } : {}), // 仅在 areaId 有值时添加
      }
    }

    console.log("whereParams", whereParams);

    const records = await prisma.tllUserLambdaRecord.findMany({
      where: whereParams,
      take: 6,
    });

    return {
      success: true,
      data: (await padWithLink({ records: records as any })).data,
    }
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "Error fetching recommendation other types",
    }
  }
}