"use server";

import openaiInstance from "@/lib/instances/openai";
import { supabase } from "@/lib/instances/supabase";
import { ActionResponse, AiSnsContentProps } from "@/lib/definitions";
import { logger } from "@/lib/logger";

export async function createImageForUrlContent(
  aiSnsContent: AiSnsContentProps,
): Promise<ActionResponse<string>> {
  try {
    if (!aiSnsContent.imagePrompt) {
      return { success: false, message: "Prompt is required" };
    }

    // Generate an image using OpenAI's DALL·E 3
    const response = await openaiInstance.images.generate({
      model: "dall-e-3",
      prompt: aiSnsContent.imagePrompt,
      n: 1, // Number of images
      size: "1024x1792", // Will not support 3:4, so you have to crop manually
    });

    logger.info("🔥dall-e-3 response received", response);
    // 下载生成的图像
    const imageUrl = response?.data?.[0]?.url;
    if (!imageUrl) {
      return { success: false, message: "无法获取图像URL" };
    }

    const imageResponse = await fetch(imageUrl);
    const arrayBuffer = await imageResponse.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // 上传到Supabase
    const { data, error } = await supabase.storage
      .from("ai-content")
      .upload(`images_${Date.now()}.png`, buffer, {
        contentType: "image/png",
        upsert: true,
        cacheControl: "3600",
      });

    if (error) {
      logger.error("🚨 上传到Supabase时出错:", error);
      return { success: false, message: "无法上传图像" };
    }

    const supabasePublicUrl = supabase.storage
      .from("ai-content")
      .getPublicUrl(data.path).data.publicUrl;

    logger.info("🔥image uploaded to supabase, url is", supabasePublicUrl);

    return {
      success: true,
      data: {
        supabasePublicUrl,
        openAiRevisedPrompt: response?.data?.[0]?.revised_prompt,
      },
    };
  } catch (error) {
    logger.error("🚨 要约时出错:", error);
    return {
      success: false,
      message: (error as string) || "无法获取数据",
    };
  }
}
