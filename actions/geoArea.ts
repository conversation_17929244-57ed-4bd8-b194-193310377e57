"use server";
import { convertKeysToCamelCase, prisma } from "@/lib/prisma";
import { GeoAreaProps, ActionResponse, RailwayStationProps } from "@/lib/definitions";
import { findNearestStationAction } from "./geoRailwayStationGroups";
import { getProBuildingFuzzyName } from "./proBuilding";
import { getPostalCodeByAddressAction } from "./geoPostalCodes";
import { logger } from "@/lib/logger";
import { GeoPostalCodeProps } from "@/lib/definitions";
import { any } from "zod";
import { matchBuildingNameIsWeird } from "@/lib/userLambdaRecord/building";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";

export const getAreaByAreaNameAction = async (areaName: string): Promise<ActionResponse<GeoAreaProps>> => {
  const areas = await prisma.geoArea.findMany({
    where: {
      nameJa: {
        contains: areaName,
        mode: "insensitive",
      },
      prefectureCode: {
        in: [11, 12, 13, 14],
      }
    },
    include: {
      city: true,
      prefecture: true,
    }
  });

  return {
    success: true,
    data: areas,
  };
};

export const getAreaPostalStationMansionBuildingByNameAction = async ({ name, recordType }: { name: string, recordType: string }): Promise<ActionResponse<GeoAreaProps>> => {
  console.log("recordType", recordType)

  try {
    const [areas, postalCodes, stations, buildings] = await Promise.all([
      recordType === "MANSION"
        ? Promise.resolve([])
        : prisma.geoArea.findMany({
          where: {
            nameJa: {
              contains: name,
              mode: "insensitive",
            },
            prefectureCode: {
              in: [11, 12, 13, 14],
            }
          },
          include: {
            city: true,
            prefecture: true,
          }
        }),
      getPostalCodeByAddressAction(name),
      findNearestStationAction(name),
      // recordType !== "MANSION" ? Promise.resolve([]) : prisma.proBuilding.findMany({
      //   where: { nameJa: { contains: name, mode: "insensitive" } },
      //   orderBy: {
      //     nameJa: "asc",
      //   },
      //   include: {
      //     tllUserLambdaRecords: {
      //       select: {
      //         id: true
      //       },
      //     },
      //     mansionRents: {
      //       select: {
      //         id: true
      //       },
      //     },
      //   },
      // })s
      recordType !== "MANSION" ? Promise.resolve([]) : prisma.$queryRaw<ProBuildingProps[]>`
        SELECT b.id, b.name_ja, b.address, b.postal_code,
          (
            SELECT COUNT(*) FROM pro_mansion_rents r WHERE r.building_id = b.id
          ) AS "mansionRentsCount",
          (
            SELECT COUNT(*) FROM tll_user_lambda_records l WHERE l.building_id = b.id
          ) AS "tllUserLambdaRecordsCount"
        FROM pro_buildings b
        WHERE LOWER(b.name_ja) % LOWER(${name})
        ORDER BY similarity(LOWER(b.name_ja), LOWER(${name})) DESC
      `
    ]);

    let buildingCamelCased = buildings?.map(convertKeysToCamelCase);

    return {
      success: true,
      data: {
        area: areas?.map(r => ({
          ...r,
          type: "area",
          label: `${r.prefecture?.name || ""}${r.city?.name || ""}${r.nameJa || ""}`.trim(),
          value: r.code.toString(),
        })),
        station: stations?.data?.map((r: any) => ({
          ...r,
          type: "station",
          label: `${r.prefecture?.name || ""}${r.city?.name || ""} - ${r.name || ""}駅`.trim(),
          value: r.stationGroupId.toString(),
        })),
        postalCode: postalCodes?.data?.map((r: any) => ({
          ...r,
          type: "postalCode",
          label: `${r.prefectureName || ""}${r.cityName || ""}${r.areaName || ""}${r.choumeName || ""}`.trim(),
          value: r.postalCode.toString(),
          valueId: r.id,
        })),
        building: buildingCamelCased?.map((r: any) => ({
          ...r,
          type: "building",
          label: `${r.prefecture?.name || ""}${r.city?.name || ""}${r.nameJa || ""}`.trim(),
          value: r.id.toString(),
          tllUserLambdaRecordsCount: Number(r.tllUserLambdaRecordsCount || 0),
          mansionRentsCount: Number(r.mansionRentsCount || 0),
        })).filter((r: any) => !matchBuildingNameIsWeird(r.nameJa)).sort((a: any, b: any) => (b.tllUserLambdaRecordsCount || 0 + b.mansionRentsCount || 0) - (a.tllUserLambdaRecordsCount || 0 + a.mansionRentsCount || 0)),
      },
    };
  } catch (error) {
    logger.error("getAreaPostalStationMansionBuildingByNameAction", error);
    return {
      success: false,
      message: "Failed to fetch area, postal code, station, and building",
    };
  }
};
