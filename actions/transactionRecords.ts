"use server"

import { TransactionRecordProps, TrTransactionRecordType } from "@/lib/definitions/transactionRecords";
import { ActionResponse } from "@/lib/definitions";
import { prisma } from "@/lib/prisma";


export async function getTransactionRecordsAction({ nearestStationGroupId, recordType }: { nearestStationGroupId?: number, recordType?: TrTransactionRecordType }): Promise<ActionResponse<TransactionRecordProps[]>> {
  if (!nearestStationGroupId) {
    return {
      success: false,
      message: "nearestStationGroupId is required",
    };
  }

  console.log("nearestStationGroupId", nearestStationGroupId);

  const transactionRecords = await prisma.trTransactionRecord.findMany({
    where: {
      nearestStationGroupId: nearestStationGroupId,
      ...(recordType ? { propertyType: recordType as TrTransactionRecordType } : {}),
    },
    orderBy: {
      transactionQuartileStartDate: 'desc',
    },
  });

  return {
    success: true,
    data: transactionRecords,
  };
}
