"use server";

import dayjs from "dayjs";
import { getBuildingPrice } from "@/lib/helper/sekisan";

export interface InvestmentParams {
  propertyPrice: number;
  structure: string;
  buildingAge: number;
  buildingArea: number;
  capital: number;
  interestRate: number;
  loanPeriod: number;
  occupancyRate: number;
  buildingPrice: number;
  isCorporate: boolean;
  expectedAnnualIncome: number;
  rentGrowthRate: number;
  propertyTax: number;
  managementFeeRate: number;
  repairFeeRate: number;
  otherFee: number;
  taxRate: number;
  majorRepairYear: number;
  majorRepairCost: number;
  sellSellingCapRatePerc: number;
}

export interface CashFlowResult {
  capital: number;
  year: number;
  income: number;
  expense: number;
  tax: number;
  amortization: number;
  loanRepayment: number;
  majorRepair: number;
  remainLoan: number;
  sellingPrice: number;
  sellingNetProfit: number;
  netCashFlow: number;
  accumulativeCashFlow: number;
  accumulativeNetProfit: number;
  cashFlows: number[];
  irr: number;
}

// IRR计算函数
function calculateIRR(cashFlows: number[], maxIterations: number = 100, tolerance: number = 0.0001): number {
  if (cashFlows.length === 0) return 0;

  let guess = 0.1; // 初始猜测值10%

  for (let iteration = 0; iteration < maxIterations; iteration++) {
    let npv = 0;
    let derivative = 0;

    for (let i = 0; i < cashFlows.length; i++) {
      const discountFactor = Math.pow(1 + guess, i);
      npv += cashFlows[i] / discountFactor;
      derivative -= i * cashFlows[i] / (discountFactor * (1 + guess));
    }

    if (Math.abs(npv) < tolerance) {
      return guess * 100; // 转换为百分比
    }

    const newGuess = guess - npv / derivative;
    if (Math.abs(newGuess - guess) < tolerance) {
      return newGuess * 100;
    }

    guess = newGuess;
  }

  return guess * 100;
}

export async function cashFlowSimulation({ params }: { params: InvestmentParams }): Promise<CashFlowResult[]> {
  if (!params) return [];

  const {
    propertyPrice,
    structure,
    buildingAge,
    buildingArea,
    capital,
    interestRate,
    loanPeriod,
    occupancyRate,
    buildingPrice,
    isCorporate,
    expectedAnnualIncome,
    rentGrowthRate,
    propertyTax,
    managementFeeRate,
    repairFeeRate,
    taxRate,
    majorRepairYear,
    majorRepairCost,
    otherFee,
    sellSellingCapRatePerc,
  } = params;

  const loanAmount = Math.max(0, propertyPrice - capital);
  const r = interestRate / 100;
  const annuity = loanAmount > 0 ? loanAmount * r * Math.pow(1 + r, loanPeriod) / (Math.pow(1 + r, loanPeriod) - 1) : 0;

  let remainLoan = loanAmount;
  let results: CashFlowResult[] = [];
  let incomeBase = expectedAnnualIncome;
  let accumulativeCashFlow = 0;
  let remainYear = getBuildingPrice({
    recordType: "BUILDING",
    buildingMaterial: structure || "木造",
    buildingBuiltYear: dayjs().year() - (buildingAge || 50),
    buildingSize: buildingArea || 0,
    unitArea: buildingArea || 0,
  }).remainY;

  let totalAmortized = 0;
  let cashFlows: number[] = [
    -capital,
  ];

  // 第一年：包含首付作为负现金流

  for (let year = 1; year <= loanPeriod; year++) {
    let income = incomeBase * Math.pow(1 + rentGrowthRate / 100, year - 1);
    let actualIncome = income * (occupancyRate / 100);

    const managementFee = actualIncome * (managementFeeRate / 100);
    const repairFee = actualIncome * (repairFeeRate / 100);
    const expense = propertyTax + managementFee + repairFee + otherFee;

    let amortization = 0;
    if (year <= remainYear) {
      amortization = buildingPrice / remainYear;
      totalAmortized += amortization;
    }

    const interestPayment = remainLoan * r;
    const principalPayment = annuity - interestPayment;
    remainLoan -= principalPayment;

    const taxableIncome = actualIncome - expense - interestPayment - amortization;
    let tax = taxableIncome > 0 ? taxableIncome * (taxRate / 100) : 0;

    const majorRepair = year === majorRepairYear ? majorRepairCost : 0;
    const netCashFlow = actualIncome - expense - tax - annuity - majorRepair;
    accumulativeCashFlow += netCashFlow;

    const sellingPrice = income / (sellSellingCapRatePerc / 100);
    const acquisitionCost = propertyPrice * 1.07;
    const sellingNetProfit = sellingPrice * 0.96 - acquisitionCost;
    const sellingNetProfitAfterLoan = sellingPrice * 0.96 - remainLoan - capital;
    const accumulativeNetProfit = accumulativeCashFlow + sellingNetProfitAfterLoan;

    // 计算当前年份的现金流
    let currentYearCashFlow = netCashFlow;

    let cashFlowForThisYear = [...cashFlows.slice(0, year), currentYearCashFlow + sellingNetProfitAfterLoan + capital]

    // 计算到当前年份的IRR  
    const irr = calculateIRR(cashFlowForThisYear);


    results.push({
      capital: capital,
      year,
      income: Math.round(actualIncome),
      amortization: Math.round(amortization),
      expense: Math.round(expense),
      tax: Math.round(tax),
      loanRepayment: Math.round(annuity),
      majorRepair: Math.round(majorRepair),
      remainLoan: Math.round(remainLoan),
      sellingPrice: Math.round(sellingPrice),
      sellingNetProfit: Math.round(sellingNetProfit),
      netCashFlow: Math.round(netCashFlow),
      accumulativeCashFlow: Math.round(accumulativeCashFlow),
      accumulativeNetProfit: Math.round(accumulativeNetProfit),
      cashFlows: cashFlowForThisYear,
      irr: Math.round(irr * 100) / 100, // 保留两位小数
    });

    cashFlows.push(currentYearCashFlow);
  }

  return results;
}
