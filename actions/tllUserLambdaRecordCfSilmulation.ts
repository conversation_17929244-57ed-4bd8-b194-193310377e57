"use server";
import { getSimpleCFCalculation } from "@/lib/helper/sekisan";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

export async function tllUserLambdaRecordCfSilmulation({ currentUserLambdaRecord, data, type }: { currentUserLambdaRecord: UserLambdaRecordProps, data: any, type: "valuation" | "search" }) {
  let res = await getSimpleCFCalculation({ cfConfigData: data, record: currentUserLambdaRecord });

  let dataToUpdate = {
    analysisSimulationConfig: data,
    analysisSimulationResults: {
      optimalBiddingPriceCalulation: res
    }
  }

  if (type === "valuation") {
    await prisma.valuationRecord.update({
      where: { id: currentUserLambdaRecord.id },
      data: dataToUpdate,
    });
  } else {
    let match = await prisma.tllUserLambdaRecord.findUnique({
      where: { id: currentUserLambdaRecord.id },
    });

    if (!match) {
      return { success: false, message: "Record not found" };
    }

    await prisma.tllUserLambdaRecord.update({
      where: { id: currentUserLambdaRecord.id },
      data: {
        ...dataToUpdate,
        updatedAt: match.updatedAt, // do not update the updatedAt as this happens from the frontend
      },
    });
  }

  return res;
}
