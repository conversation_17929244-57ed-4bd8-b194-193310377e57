"use server";

import { prisma } from "@/lib/prisma";
import { TrEvaluationCombinedRecord } from "@prisma/client";
import { logger } from "@/lib/logger";
import { calcCoordinateDistance } from "@/lib/helper/geoDistance";
import { mean } from "lodash-es";
import { ActionResponse } from "@/lib/definitions";

export async function trEvaluationKijunChangesInP5YAction({
  stationName,
  longitude,
  latitude,
}: {
  stationName: string;
  longitude: number;
  latitude: number;
}): Promise<ActionResponse<{
  mean: number;
  yearly: number;
  records: TrEvaluationCombinedRecord[];
}>> {
  try {
    const records = await prisma.trEvaluationCombinedRecord.findMany({
      where: {
        nearestStation: stationName,
      },
    }) as TrEvaluationCombinedRecord[];

    let padWithDistance = records.map((record) => {
      return {
        ...record,
        distance: calcCoordinateDistance(record.latitude, record.longitude, latitude, longitude) * 1000,
      };
    });

    padWithDistance = padWithDistance.filter((record) => record.distance < 10000).sort((a, b) => a.distance - b.distance);

    let cacl5YInc = (record: TrEvaluationCombinedRecord) => {
      if (record["kouji2023"] && record["kouji2019"]) {
        return (record["kouji2023"] - record["kouji2019"]) / record["kouji2019"] * 100;
      } else if (record["kijun2022"] && record["kijun2018"]) {
        return (record["kijun2022"] - record["kijun2018"]) / record["kijun2018"] * 100;
      }
      return 0;
    }

    let inc5Y = padWithDistance.map((record) => cacl5YInc(record)).filter((inc) => inc !== 0);


    return {
      success: true,
      data: {
        mean: parseFloat(mean(inc5Y).toFixed(2)),
        yearly: parseFloat((mean(inc5Y) / 5).toFixed(2)),
        records: padWithDistance,
      },
    };
  } catch (error) {
    logger.error(`trEvaluationKijunChangesInP5YAction: ${stationName} ${longitude} ${latitude}`, error);
    return {
      success: false,
      message: "Failed to fetch records",
    };
  }
}