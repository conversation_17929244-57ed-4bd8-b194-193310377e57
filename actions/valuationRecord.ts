"use server";

import { ActionResponse, UserLambdaRecordCreateProps, ValuationRecordProps, ValuationRecordSchema } from "@/lib/definitions";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { prisma } from '@/lib/prisma'; // 假设你有一个 prisma 实例
import { fillPrefectureAreaPostalCode } from "./geoPrefecture";
import { fillNearestStationGroupIdWalkMinute } from "./geoRailwayStationGroups";
import { fillRecordAnalysis } from "@/app/api/cron/reinsFill/recordAnalysis/fillRecordAnalysis";

export async function createValuationRecordAction(data: ValuationRecordProps): Promise<ActionResponse<ValuationRecordProps>> {
  logger.debug("createValuationRecord", data);

  const session = await auth(); // Fetch authenticated session
  const loggedInUserId = session?.user?.id; // 从 next-auth 获取 userId

  if (!loggedInUserId) {
    return {
      success: false,
      message: "ユーザーはログインしていません", // 使用日语
    };
  }

  const validatedData = ValuationRecordSchema.parse(data);

  try {
    const record = await prisma.valuationRecord.create({
      data: {
        userId: loggedInUserId,
        ...validatedData,
      } as any,
    });

    // Merge these into one function 
    await fillPrefectureAreaPostalCode(record.id, "valuationRecord");
    await fillNearestStationGroupIdWalkMinute(record.id, "valuationRecord");
    await fillRecordAnalysis({
      recordId: record.id,
      model: "valuationRecord",
    });

    let paddedRecord = await prisma.valuationRecord.findUnique({
      where: { id: record.id },
      include: {
        propertyAnalysisResult: true,
      },
    });

    return {
      success: true,
      data: paddedRecord,
    };
  } catch (error) {
    logger.error("createUserLambdaRecord error", error);

    return {
      success: false,
      message: 'Failed to create user lambda record',
    };
  }
}

export async function getValuationRecordAction(id: string): Promise<ActionResponse<ValuationRecordProps>> {
  const session = await auth(); // Fetch authenticated session  
  const userId = session?.user?.id; // 从 next-auth 获取 userId

  if (!userId) {
    return {
      success: false,
      message: "ユーザーはログインしていません", // 使用日语
    };
  }

  try {
    let record: ValuationRecordProps | null = null;

    if (session.user?.accessLevel && session.user?.accessLevel >= 90) {
      record = await prisma.valuationRecord.findUnique({
        where: { id },
        include: {
          propertyAnalysisResult: true,
        },
      }) as ValuationRecordProps;
    } else {
      record = await prisma.valuationRecord.findUnique({
        where: { id, userId },
        include: {
          propertyAnalysisResult: true,
        },
      }) as ValuationRecordProps;
    }

    return {
      success: true,
      data: record,
    };
  } catch (error) {
    logger.error("getValuationRecordAction error", error);
    return {
      success: false,
      message: 'Failed to get valuation record',
    };
  }
}

export async function getValuationRecordsAction({
  dateFrom,
}: {
  dateFrom?: string;
}): Promise<ActionResponse<ValuationRecordProps>> {
  try {
    const session = await auth(); // Fetch authenticated session  
    const userId = session?.user?.id; // 从 next-auth 获取 userId

    if (!userId) {
      return {
        success: false,
        message: "ユーザーはログインしていません", // 使用日语
      };
    }

    const record = await prisma.valuationRecord.findMany({
      where: { userId, ...(dateFrom && { createdAt: { gte: dateFrom } }) },
      include: {
        propertyAnalysisResult: true,
      },
      orderBy: { createdAt: 'desc' },
    });
    return {
      success: true,
      data: record,
    };
  } catch (error) {
    logger.error("getValuationRecordAction error", error);
    return {
      success: false,
      message: 'Failed to get valuation record',
    };
  }
}


export async function getValuationRecordCountPerUserPerDay(): Promise<ActionResponse<ValuationRecordProps>> {
  try {
    const session = await auth(); // Fetch authenticated session  
    const userId = session?.user?.id; // 从 next-auth 获取 userId 

    if (!userId) {
      return {
        success: false,
        message: "ユーザーはログインしていません", // 使用日语
      };
    }

    const grouped = await prisma.valuationRecord.groupBy({
      by: ["userId", "valuationDate"],
      _count: {
        id: true,
      },
      orderBy: [
        { userId: "asc" },
        { valuationDate: "desc" },
      ],
    });

    // 🧠 获取所有涉及的 userIds
    const userIds = [...new Set(grouped.map((r) => r.userId))];

    const users = await prisma.tllUser.findMany({
      where: { id: { in: userIds } },
      select: {
        id: true,
        accessLevel: true,
        subscriptionPlan: true,
        email: true,
      },
    });

    // 🧬 用户信息映射表
    const userMap = Object.fromEntries(users.map((u) => [u.id, u]));

    // 🧩 组装最终结构
    const data = grouped.map((r) => ({
      userId: r.userId,
      dateOfRecord: r.valuationDate.toISOString().slice(0, 10),
      count: r._count.id,
      accessLevel: userMap[r.userId]?.accessLevel ?? null,
      subscriptionPlan: userMap[r.userId]?.subscriptionPlan ?? null,
      email: userMap[r.userId]?.email ?? null,
    }));

    return {
      success: true,
      data,
    };
  } catch (error) {
    logger.error("getValuationRecordCountPerUserPerDay error", error);
    return {
      success: false,
      message: 'Failed to get valuation record count per user per day',
    };
  }
}

