"use server"

import { auth } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { ActionResponse } from "@/lib/definitions"
import { TllUserProps } from "@/lib/definitions/tllUser"

export async function updateUserProfile({ name }: { name: string }): Promise<ActionResponse> {
  const session = await auth()
  const currentUser = session?.user

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "ユーザーが見つかりません",
    };
  }

  try {
    let updated = await prisma.tllUser.update({ where: { email: currentUser.email as string }, data: { name }, select: { name: true, email: true } });


    return {
      success: true,
      message: "ユーザー情報を更新しました",
      data: updated,
    };
  } catch (error) {
    return { success: false, message: "用户信息更新失败" };
  }
}

export async function getUserByEmail({ email }: { email: string }): Promise<ActionResponse<TllUserProps | null>> {
  try {
    const user = await prisma.tllUser.findUnique({
      where: { email },
      select: {
        name: true,
        email: true,
        imageUrl: true,
        accessLevel: true,

        stripeCustomerId: true,
        subscriptionPlan: true,
        subscriptionStatus: true,
        userSetting: true,
        emailSubscription: true,

        referralCodeId: true,
        referralCode: {
          include: {
            createdByUser: true,
          }
        }
      },
    });
    return {
      success: true,
      message: "ユーザー情報を取得しました",
      data: user,
    };
  } catch (error) {
    console.error(error)

    return { success: false, message: "ユーザー情報取得に失敗しました" };
  }
}

export async function updateUserEmailSubscription({ propertyPickup, salesManMarketingEmail }: {
  propertyPickup?: boolean,
  salesManMarketingEmail?: boolean,
}): Promise<ActionResponse> {
  const session = await auth()
  const currentUser = session?.user

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "ユーザーが見つかりません",
    };
  }

  let match = await prisma.tllUser.findUnique({
    where: { email: currentUser.email as string },
    select: {
      emailSubscription: true,
    },
  })

  try {
    const user = await prisma.tllUser.update({
      where: { email: currentUser.email as string },
      data: {
        emailSubscription: {
          propertyPickup: propertyPickup ?? (match?.emailSubscription as any)?.propertyPickup ?? false,
          salesManMarketingEmail: salesManMarketingEmail ?? (match?.emailSubscription as any)?.salesManMarketingEmail ?? false,
        }
      },
    });

    return {
      success: true,
      message: "ユーザー設定を更新しました",
      data: user,
    };
  } catch (error) {
    return { success: false, message: "ユーザー設定更新に失敗しました" };
  }
}

export async function updateUserSetting({ userSetting }: { userSetting: any }): Promise<ActionResponse> {
  const session = await auth()
  const currentUser = session?.user

  console.log("userSetting for updating", userSetting)

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "ユーザーが見つかりません",
    };
  }

  try {
    const user = await prisma.tllUser.update({
      where: { email: currentUser.email as string },
      data: {
        userSetting,
      },
    });
    return {
      success: true,
      message: "ユーザー設定を更新しました",
      data: user,
    };
  } catch (error) {
    return { success: false, message: "ユーザー設定更新に失敗しました" };
  }
}


export async function getUserByUnsubscribeToken({ token }: { token: string }): Promise<ActionResponse<TllUserProps | null>> {
  try {
    function extractUserId(code: string): string | null {
      const match = code.match(/A7f9K(.*?)3pQ2Z/);
      return match ? match[1] : null;
    }

    const userId = extractUserId(token);

    if (!userId) {
      return {
        success: false,
        message: "ユーザーが見つかりません",
      };
    }

    const user = await prisma.tllUser.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return {
        success: false,
        message: "ユーザーが見つかりません",
      };
    }

    let emailSubscription = user.emailSubscription as any

    await prisma.tllUser.update({
      where: { id: userId },
      data: {
        emailSubscription: {
          propertyPickup: emailSubscription.propertyPickup ?? false,
          salesManMarketingEmail: emailSubscription.salesManMarketingEmail ?? false,
        }
      },
    });

    return {
      success: true,
      message: "メール通知を解除しました",
      data: null,
    };
  } catch (error) {
    return { success: false, message: "ユーザー情報取得に失敗しました" };
  }
}