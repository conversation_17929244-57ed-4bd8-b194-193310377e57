"use server"

import { ActionResponse } from "@/lib/definitions"
import { prisma } from "@/lib/prisma"
import { getProBuildingHouseRent } from "./proBuildingHouseRent"

// 类型定义
type RecordType = "BUILDING" | "HOUSE" | "MANSION"
type SearchType = "building" | "station" | "postalCode" | "area"

interface SearchOption {
  type: SearchType
  value: string
  postalCode?: string
  geoRailwayStationGroups?: {
    postalCode?: string
  }
}

interface RentEstimateResult {
  rentSamePropertyKubun: any[],
  rentKubun: any[],
  rentHouse: any[],
  rentIchibu: any[]
  rentAll: any[]
  saleRecords: any[]
}

// 数据库查询函数
const getRentRecordQuery = {
  mansionRent: async (where: any) => {
    return await prisma.proMansionRent.findMany({ where })
  },
  buildingHouseRent: async (where: any) => {
    return await prisma.proBuildingHouseRent.findMany({ where })
  },
}

// 记录类型转换函数
const recordTypeHelpers = {
  getMansionTypeForSearch: (recordType: RecordType) =>
    recordType === "BUILDING" ? "BUILDING_PART" : "MANSION",
  getBuildingHouseTypeForSearch: (recordType: RecordType) => recordType
}

// 搜索策略实现
const searchStrategies = {
  building: async (option: SearchOption, recordType: RecordType): Promise<Partial<RentEstimateResult>> => {
    const result: Partial<RentEstimateResult> = {}

    if (recordType === "MANSION") {
      result.rentSamePropertyKubun = await getRentRecordQuery.mansionRent({
        recordType: "MANSION",
        buildingId: option.value,
      })

      result.rentKubun = await getRentRecordQuery.mansionRent({
        recordType: "MANSION",
        OR: [
          { buildingId: option.value },
          ...(option.postalCode ? [{ locationPostalCode: option.postalCode?.toString() }] : []),
        ]
      })
    }

    return result
  },

  station: async (option: SearchOption, recordType: RecordType): Promise<Partial<RentEstimateResult>> => {
    const result: Partial<RentEstimateResult> = {}

    const padWhere = (key: string) => {
      return {
        OR: [
          { nearestStationGroupId: option.value },
          ...(option?.geoRailwayStationGroups?.postalCode ? [{ [key]: option.geoRailwayStationGroups.postalCode.replace("-", "").toString() }] : []),
        ],
      }
    }

    if (recordType === "MANSION") {
      result.rentKubun = await getRentRecordQuery.mansionRent({
        recordType: "MANSION",
        ...padWhere("locationPostalCode"),
      })
    }

    if (recordType === "HOUSE") {
      result.rentHouse = await getRentRecordQuery.buildingHouseRent({
        recordType: "HOUSE",
        ...padWhere("locationPostalCode"),
      })
    }

    if (recordType === "BUILDING") {
      result.rentAll = await getRentRecordQuery.buildingHouseRent({
        recordType: "BUILDING",
        ...padWhere("locationPostalCode"),
      })
    }

    result.rentIchibu = await getRentRecordQuery.mansionRent({
      recordType: "BUILDING_PART",
      ...padWhere("locationPostalCode"),
    })

    result.saleRecords = await prisma.tllUserLambdaRecord.findMany({
      where: {
        OR: [
          { nearestStationGroupId: option.value },
          ...(option?.geoRailwayStationGroups?.postalCode ? [{ postalCode: parseInt(option.geoRailwayStationGroups.postalCode.replace("-", "")) }] : []),
        ],
        recordType: recordType,
        yearlyIncome: {
          gt: 0,
        }
      }
    })

    return result
  },

  postalCode: async (option: SearchOption, recordType: RecordType): Promise<Partial<RentEstimateResult>> => {
    const result: Partial<RentEstimateResult> = {}

    result.rentIchibu = await getRentRecordQuery.mansionRent({
      recordType: "BUILDING_PART",
      locationPostalCode: option.value,
    })

    result.saleRecords = await prisma.tllUserLambdaRecord.findMany({
      where: {
        postalCode: parseInt(option.value),
        recordType: recordType,
        yearlyIncome: {
          gt: 0,
        }
      }
    })

    if (recordType === "MANSION") {
      result.rentKubun = await getRentRecordQuery.mansionRent({
        recordType: "MANSION",
        locationPostalCode: option.value,
      })
    }
    else if (recordType === "HOUSE") {
      result.rentHouse = await getRentRecordQuery.buildingHouseRent({
        recordType: "HOUSE",
        locationPostalCode: option.value,
      })
    }

    return result
  },

  area: async (option: SearchOption, recordType: RecordType): Promise<Partial<RentEstimateResult>> => {
    const result: Partial<RentEstimateResult> = {}

    result.saleRecords = await prisma.tllUserLambdaRecord.findMany({
      where: {
        areaCode: parseInt(option.value),
        recordType: recordType,
        yearlyIncome: {
          gt: 0,
        }
      }
    })

    if (recordType === "HOUSE" || recordType === "BUILDING") {
      result.rentIchibu = await getRentRecordQuery.buildingHouseRent({
        locationAreaCode: option.value,
        recordType: recordType
      })
    }

    return result
  }
}

// 主函数
export const getRentEstimate = async ({
  selectedOption,
  recordType
}: {
  selectedOption: SearchOption
  recordType: RecordType
}): Promise<ActionResponse<RentEstimateResult>> => {
  try {
    const searchStrategy = searchStrategies[selectedOption.type]

    if (!searchStrategy) {
      throw new Error(`Invalid search type: ${selectedOption.type}`)
    }

    const result = await searchStrategy(selectedOption, recordType)

    return {
      data: {
        rentSamePropertyKubun: result.rentSamePropertyKubun || [],
        rentKubun: result.rentKubun || [],
        rentHouse: result.rentHouse || [],
        rentIchibu: result.rentIchibu || [],
        rentAll: result.rentAll || [],
        saleRecords: result.saleRecords || [],
      },
      success: true,
    }
  } catch (error) {
    console.log("🔥 error", error)

    return {
      success: false,
      message: `${error}`,
    }
  }
}