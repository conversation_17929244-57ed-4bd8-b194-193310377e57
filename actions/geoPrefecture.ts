"use server"

import { ActionResponse } from "@/lib/definitions";
import { prisma } from "@/lib/prisma";
import { GeoPrefectureProps } from "@/lib/definitions";
import { getLatAndLngFromAddress } from "@/lib/thirdParty/google";
import { updateLatLngFromGoogleResult } from "@/actions/geoPostalCodes";
import { GuessRosenkaFromCoordinates } from "@/lib/helper/geo";
import { logger } from "@/lib/logger";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

export const getPrefecturesAction = async (): Promise<ActionResponse<GeoPrefectureProps[]>> => {
  const prefectures = await prisma.geoPrefecture.findMany();
  return {
    success: true,
    data: prefectures,
  };
}

export async function fillPrefectureAreaPostalCode(recordId: string, model: "valuationRecord" | "tllUserLambdaRecord" = "tllUserLambdaRecord"): Promise<{
  success: boolean;
  data: {
    resultToUpdate: Partial<any>;
  };
}> {

  const rec = await (prisma[model] as any).findFirst({
    where: {
      id: recordId,
      OR: [{
        prefectureCode: null,
        latitude: null,
        longitude: null,
      }, {
        valueRosenka: null,
      },
      {
        valueRosenka: 0,
      }],
    }
  }) as any;

  const resultToUpdate: Partial<any> = {};

  if ((rec.postalCode === null || rec.longitude === null || rec.latitude === null) && rec.address) {
    const address = await prisma.geoAddress.findFirst({ where: { address: rec.address } });

    console.log("address", address);

    if (address) {
      // logger.info(`✔️ Found geoAddress for ${rec.address} with postal code ${address.pcode}`);
      resultToUpdate.postalCode = model === "valuationRecord" ? address.pcode.toString() : address.pcode;

      const postalCodes = await prisma.geoPostalCode.findMany({
        where: { postalCode: resultToUpdate.postalCode.toString() },
      });

      if (postalCodes.length) {
        resultToUpdate.prefectureCode = parseInt(postalCodes[0].prefectureCode.toString(), 10);
        resultToUpdate.areaCode = parseInt(`${postalCodes[0].cityCode || ""}${postalCodes[0].areaCode || ""}`, 10);
        if (postalCodes[0].longitude) {
          resultToUpdate.longitude = postalCodes[0].longitude;
        }
        if (postalCodes[0].latitude) {
          resultToUpdate.latitude = postalCodes[0].latitude;
        }
      }
    } else {
      // logger.info(`🔥 Getting latitude and longitude from address ${rec.address}`);
      const data = await getLatAndLngFromAddress({ address: rec.address });

      if (data?.length) {
        const { location } = data[data.length - 1].geometry;
        resultToUpdate.latitude = location.lat;
        resultToUpdate.longitude = location.lng;

        const postalCodeMatch = data[data.length - 1].formatted_address?.match(/\d{3}-\d{4}/g);

        logger.info(`🔥 postalCodeMatch ${JSON.stringify(postalCodeMatch)}`);

        if (postalCodeMatch?.length) {
          const postalCode = postalCodeMatch[0].replace("-", "");

          await updateLatLngFromGoogleResult(data); // update the database as well 

          resultToUpdate.postalCode = parseInt(postalCode, 10);

          logger.info(`++ Creating geoAddress for ${rec.address} with postal code ${resultToUpdate.postalCode}`);

          await prisma.geoAddress.upsert({
            where: { address: rec.address },
            update: { pcode: resultToUpdate.postalCode },
            create: { address: rec.address, pcode: resultToUpdate.postalCode },
          });

          const postalCodes = await prisma.geoPostalCode.findMany({
            where: { postalCode: resultToUpdate.postalCode.toString() },
          });

          if (postalCodes.length) {
            const postalCodeData = postalCodes[0];
            resultToUpdate.prefectureCode = parseInt(postalCodeData.prefectureCode.toString(), 10);
            resultToUpdate.areaCode = parseInt(`${postalCodeData.cityCode || ""}${postalCodeData.areaCode || ""}`, 10);
          }
        }
      }
    }
  }

  if (rec.valueRosenka === null || rec.valueRosenka === 0) {
    let latitude = resultToUpdate.latitude || rec.latitude;
    let longitude = resultToUpdate.longitude || rec.longitude;
    let prefectureCode = resultToUpdate.prefectureCode || rec.prefectureCode;
    logger.info(`🔥 rosenka .. latitude: ${latitude}, longitude: ${longitude}, prefectureCode: ${prefectureCode}`);
    if (latitude && longitude && prefectureCode) {
      const guessRosenkaResults = await GuessRosenkaFromCoordinates(
        latitude,
        longitude,
        prefectureCode.toString()
      );

      resultToUpdate.valueRosenka = guessRosenkaResults?.guessRosenka;
    }

    if (resultToUpdate.postalCode && model === "valuationRecord") {
      resultToUpdate.postalCode = resultToUpdate.postalCode.toString();
    }
  }

  // logger.info(
  //   `🔥 Processed record, id: ${rec.id}, Data: ${JSON.stringify(resultToUpdate)}`
  // );

  if (Object.keys(resultToUpdate).length) {
    await (prisma[model] as any).update({
      where: { id: rec.id },
      data: { ...resultToUpdate, updatedAt: rec.updatedAt },
    });
  }

  return {
    success: true,
    data: {
      resultToUpdate,
    }
  };
}