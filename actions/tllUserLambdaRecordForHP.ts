"use server";

import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";
import dayjs from "@/lib/thirdParty/dayjsWithTz";
import { hpSummaryCache } from "@/lib/instances/nodeCache";
import { logger } from "@/lib/logger";

export async function getNewCountAndPriceChangeForHP(): Promise<ActionResponse> {
  const cacheKey = "newlyCreatedCountAndPriceChangeForHP";
  const cached = hpSummaryCache.get(cacheKey);
  if (cached) {
    logger.info(`Get newly created count and price change for HP from cache`);
    return {
      success: true,
      data: cached,
    };
  }

  const [newlyCreatedCount, priceChangeCount] = await Promise.all([
    prisma.tllUserLambdaRecord.count({
      where: {
        createdAt: {
          gte: dayjs().subtract(1, "day").toDate(),
        },
      },
    }),
    prisma.tllUserLambdaRecordPriceChange.count({
      where: {
        recordDate: {
          gte: dayjs().subtract(1, "day").toDate(),
        },
      },
    })
  ]);

  logger.info(`Get newly created count and price change for HP from database`);
  hpSummaryCache.set(cacheKey, {
    newlyCreatedCount,
    priceChangeCount,
  });

  return {
    success: true,
    data: {
      newlyCreatedCount,
      priceChangeCount,
    },
  };
}
