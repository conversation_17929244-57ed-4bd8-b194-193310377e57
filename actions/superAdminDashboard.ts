"use server";

import { ActionResponse } from "@/lib/definitions";
import { prisma } from "@/lib/prisma";
import { logger } from "@/lib/logger";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
dayjs.extend(isoWeek); // Extend Day.js with the isoWeek plugin
import { uniqBy } from "lodash-es";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";

export async function getSuperAdminOveralMetricsData(): Promise<ActionResponse<{}>> {
  try {
    const [
      totalUsers,
      totalUsersThisWeek,
      totalUserThisMonth,
      totalPaidUsers,
      totalPaidUserThisMonth,
      totalPaidUsersThisWeek,
      totalValuationCount,
      totalValuationCountThisMonth,
      totalValuationCountThisWeek,
      totalSearchCount,
      totalSearchCountThisMonth,
      totalSearchCountThisWeek,
      totalFavoriteThisYear,
      totalFavoriteThisMonth,
      totalFavoriteThisWeek,
      totalNeedThisYear,
      totalNeedThisMonth,
      totalNeedThisWeek,
      totalDauCount,
      totalWauCount,
      totalMauCount,
    ] = await Promise.all([
      prisma.tllUser.count(),
      prisma.tllUser.count({ where: { createdAt: { gte: dayjs().startOf("isoWeek").toDate() } } }),
      prisma.tllUser.count({ where: { createdAt: { gte: dayjs().startOf("month").toDate() } } }),
      prisma.tllUser.count({ where: { accessLevel: { in: [10, 20] } } }),
      prisma.tllUser.count({ where: { accessLevel: { in: [10, 20] }, createdAt: { gte: dayjs().startOf("month").toDate() } } }),
      prisma.tllUser.count({ where: { accessLevel: { in: [10, 20] }, createdAt: { gte: dayjs().startOf("isoWeek").toDate() } } }),
      prisma.valuationRecord.count({ where: { valuationDate: { gte: dayjs().startOf("year").toDate() } } }),
      prisma.valuationRecord.count({ where: { valuationDate: { gte: dayjs().startOf("month").toDate() } } }),
      prisma.valuationRecord.count({ where: { valuationDate: { gte: dayjs().startOf("isoWeek").toDate() } } }),
      prisma.tllUserLambdaRecordSearchHistory.count({ where: { searchDate: { gte: dayjs().startOf("year").toDate() } } }),
      prisma.tllUserLambdaRecordSearchHistory.count({ where: { searchDate: { gte: dayjs().startOf("month").toDate() } } }),
      prisma.tllUserLambdaRecordSearchHistory.count({ where: { searchDate: { gte: dayjs().startOf("isoWeek").toDate() } } }),
      prisma.tllUsersUserLambdaRecordsFavMap.count({ where: { createdAt: { gte: dayjs().startOf("year").toDate() } } }),
      prisma.tllUsersUserLambdaRecordsFavMap.count({ where: { createdAt: { gte: dayjs().startOf("month").toDate() } } }),
      prisma.tllUsersUserLambdaRecordsFavMap.count({ where: { createdAt: { gte: dayjs().startOf("isoWeek").toDate() } } }),
      prisma.tllCustomerNeed.count({ where: { createdAt: { gte: dayjs().startOf("year").toDate() } } }),
      prisma.tllCustomerNeed.count({ where: { createdAt: { gte: dayjs().startOf("month").toDate() } } }),
      prisma.tllCustomerNeed.count({ where: { createdAt: { gte: dayjs().startOf("isoWeek").toDate() } } }),
      (await prisma.systemUserActivity.findMany({
        where: {
          recordDate: dayjsWithTz().toISOString(),
          userId: {
            not: null, // ✅ exclude nulls
          },
        },
        distinct: ['userId'] as never,
        select: {
          userId: true,
        },
      })).length,
      (await prisma.systemUserActivity.findMany({
        where: {
          recordDate: {
            lte: dayjsWithTz().startOf('day').toISOString(),
            gte: dayjsWithTz().startOf('day').subtract(7, 'day').toISOString(),
          },
          userId: {
            not: null, // ✅ exclude nulls
          },
        },
        distinct: ['userId'] as never,
        select: {
          userId: true,
        },
      })).length,
      (await prisma.systemUserActivity.findMany({
        where: {
          recordDate: {
            lte: dayjsWithTz().startOf('day').toISOString(),
            gte: dayjsWithTz().startOf('day').subtract(30, 'day').toISOString(),
          },
          userId: {
            not: null, // ✅ exclude nulls
          },
        },
        distinct: ['userId'] as never,
        select: {
          userId: true,
        },
      })).length,
    ]);

    return {
      data: {
        totalUsers,
        totalUserThisMonth,
        totalUsersThisWeek,
        totalPaidUsers,
        totalPaidUserThisMonth,
        totalPaidUsersThisWeek,
        totalValuationCount,
        totalValuationCountThisMonth,
        totalValuationCountThisWeek,
        totalSearchCount,
        totalSearchCountThisMonth,
        totalSearchCountThisWeek,
        totalFavoriteThisWeek,
        totalFavoriteThisMonth,
        totalFavoriteThisYear,
        totalNeedThisWeek,
        totalNeedThisMonth,
        totalNeedThisYear,
        totalDauCount,
        totalWauCount,
        totalMauCount,
      },
      success: true,
    };
  } catch (error) {
    logger.error("getSuperAdminOveralMetricsData", error);
    return {
      data: [],
      success: false,
    };
  }
}

export async function getSuperAdminDashboardFavoriteAction(): Promise<ActionResponse<any>> {
  const grouped = await prisma.$queryRaw<any[]>`
    SELECT 
      "user_id", 
      DATE("created_at") AS favorite_date, 
      COUNT(*) AS count
    FROM "tll_users_user_lambda_records_fav_maps"
    GROUP BY user_id, favorite_date
    ORDER BY favorite_date DESC
  `;

  // 去重取出 userIds
  const userIds = [...new Set(grouped.filter(g => g.user_id).map(g => g.user_id))];
  console.log("userIds", userIds);
  const users = await prisma.tllUser.findMany({
    where: {
      id: {
        in: userIds
      }
    }
  });

  console.log("users", users.length);

  // 合并 user 信息
  const finalResults = grouped.map(group => ({
    ...group,
    user: users.find(u => u.id === group.user_id) || null,
    favDate: dayjs(group.favorite_date).format("YYYY-MM-DD"),
    count: Number(group.count),
  }));

  return { data: finalResults, success: true };
}


export async function getSuperAdminDashboardSearchAction(): Promise<ActionResponse<any>> {
  const groupByUserSearchDate = await prisma.tllUserLambdaRecordSearchHistory.groupBy({
    by: ["userId", "searchDate"],
    _count: { _all: true },
  });

  const userIds = uniqBy(groupByUserSearchDate.map((g: any) => g.userId), (id: string) => id);
  const users = await prisma.tllUser.findMany({ where: { id: { in: userIds } } });

  const finalResults = groupByUserSearchDate.map((group: any) => ({
    ...group,
    user: users.find((user: any) => user.id === group.userId) || null,
  }));

  return { data: finalResults, success: true };
}

export async function getSuperAdminDashboardValuationAction(): Promise<ActionResponse<any>> {
  try {
    const groupByUserValuationDate = await prisma.valuationRecord.groupBy({
      by: ["userId", "valuationDate"],
      _count: { _all: true },
    });

    const userIds = uniqBy(groupByUserValuationDate.map((g: any) => g.userId), (id: string) => id);
    const users = await prisma.tllUser.findMany({ where: { id: { in: userIds } } });

    const finalResults = groupByUserValuationDate.map((group: any) => ({
      ...group,
      user: users.find((user: any) => user.id === group.userId) || null,
    }));

    return { data: finalResults, success: true };
  } catch (error) {
    logger.error("getSuperAdminDashboardValuationAction", error);
    return { data: [], success: false };
  }
}

export async function getSuperAdminDashboardWatchAction(): Promise<ActionResponse<any>> {
  const grouped = await prisma.$queryRaw<any[]>`
    SELECT 
      "user_id", 
      DATE("created_at") AS watch_date, 
      COUNT(*) AS count
    FROM "property_watched_criteria"
    GROUP BY user_id, watch_date
    ORDER BY watch_date DESC
  `;

  // 去重取出 userIds
  const userIds = [...new Set(grouped.filter(g => g.user_id).map(g => g.user_id))];
  console.log("userIds", userIds);
  const users = await prisma.tllUser.findMany({
    where: {
      id: {
        in: userIds
      }
    }
  });

  console.log("users", users.length);

  // 合并 user 信息
  const finalResults = grouped.map(group => ({
    ...group,
    user: users.find(u => u.id === group.user_id) || null,
    watchDate: dayjs(group.watch_date).format("YYYY-MM-DD"),
    count: Number(group.count),
  }));

  return { data: finalResults, success: true };
}

export async function getSuperAdminDashboardCustomerNeedAction(): Promise<ActionResponse<any>> {
  const grouped = await prisma.$queryRaw<any[]>`
    SELECT 
      "agent_user_id", 
      DATE("created_at") AS need_date, 
      COUNT(*) AS count
    FROM "tll_customer_needs"
    GROUP BY agent_user_id, need_date
    ORDER BY need_date DESC
  `;

  console.log("grouped", grouped);

  // 去重取出 userIds
  const userIds = [...new Set(grouped.filter(g => g.customer_user_id).map(g => g.customer_user_id))];
  console.log("userIds", userIds);
  const users = await prisma.tllUser.findMany({
    where: {
      id: {
        in: userIds
      }
    }
  });

  console.log("users", users.length);

  // 合并 user 信息
  const finalResults = grouped.map(group => ({
    ...group,
    user: users.find(u => u.id === group.customer_user_id) || null,
    needDate: dayjs(group.need_date).format("YYYY-MM-DD"),
    count: Number(group.count),
  }));

  return { data: finalResults, success: true };
}

export async function getFavoriteMetricsAction(): Promise<ActionResponse<any>> {
  const groupByUserFavoriteDate = await prisma.tllUsersUserLambdaRecordsFavMap.groupBy({
    by: ["userId"],
    _count: { _all: true },
    // where: { createdAt: { gte: dayjs().startOf("month").toDate() } },
  });

  const userIds = uniqBy(groupByUserFavoriteDate.map((g: any) => g.userId), (id: string) => id);
  const users = await prisma.tllUser.findMany({ where: { id: { in: userIds } } });

  const finalResults = groupByUserFavoriteDate.map((group: any) => ({
    ...group,
    user: users.find((user: any) => user.id === group.userId) || null,
  }));

  return { data: finalResults, success: true };
}

export async function getNeedMetricsAction(): Promise<ActionResponse<any>> {
  const groupByUserFavoriteDate = await prisma.tllCustomerNeed.groupBy({
    by: ["agentUserId"],
    _count: { _all: true },
    // where: { createdAt: { gte: dayjs().startOf("month").toDate() } },
  });

  const userIds = uniqBy(groupByUserFavoriteDate.map((g: any) => g.agentUserId), (id: string) => id).filter((id: string) => id !== null);
  console.log("userIds", userIds);
  const users = await prisma.tllUser.findMany({ where: { id: { in: userIds } } });

  console.log("users", users.length);

  const finalResults = groupByUserFavoriteDate.map((group: any) => ({
    ...group,
    user: users.find((user: any) => user.id === group.agentUserId) || null,
  }));

  return { data: finalResults, success: true };
}

export async function getUserIncreaseByDayAction(): Promise<ActionResponse<any>> {
  const grouped = await prisma.$queryRaw<any[]>`
    SELECT 
      DATE("created_at") AS created_date, 
      COUNT(*) AS count
    FROM "tll_users"
    GROUP BY created_date
    ORDER BY created_date DESC
  `;

  return {
    data: grouped.map((g: any) => ({
      ...g,
      createdDate: dayjs(g.created_date).format("YYYY-MM-DD"),
      count: Number(g.count),
    })), success: true
  };
}
