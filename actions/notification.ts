"use server"

import { auth } from "@/lib/auth";
import { ActionResponse } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { merge, uniqBy, sumBy, sum } from "lodash-es";
import { getRecordMatchSimpleForNeedIdsAction } from "./customerNeeds";
import dayjs from "@/lib/thirdParty/dayjsWithTz";

export async function getNotificationAction(): Promise<ActionResponse<any[]>> {
  const session = await auth();
  const currentUser = session?.user;
  if (!currentUser) {
    return {
      data: {},
      success: false,
    };
  }

  let res = {
    "/ex/need": {
      value: null,
      name: "todayWatchListMatchObject"
    },
    "/ex/fav": {
      value: null,
      name: "todayFavListMatchObject"
    },
    "/ad/dashboard": {
      value: null,
      name: "todayUserLambdaRecords"
    },
    "/ad/keibaiSumifu": {
      value: null,
      name: "todayBid / today New"
    },
  } as {
    [key: string]: {
      value: string | null;
      name: string;
    };
  };

  const changeRecordIds = (await prisma.tllUserLambdaRecordPriceChange.findMany({
    where: {
      recordDate: {
        gte: dayjsWithTz().startOf('day').toISOString(),
        lte: dayjsWithTz().endOf('day').toISOString(),
      },
    },
  })).map((record: any) => record.recordId).filter((id: any): id is string => id !== null);


  const [allFavChanges, todayUserLambdaRecordsAll, todaySumifuAllCount, shimekiriSumifuAllCount, myCustomerNeedsIds] = await Promise.all([
    prisma.tllUsersUserLambdaRecordsFavMap.findMany({
      select: {
        userLambdaRecordId: true,
      },
      where: {
        userId: currentUser.id,
      }
    }),
    prisma.tllUserLambdaRecord.count({
      where: {
        OR: [
          { recordType: "BUILDING" },
          { prefectureCode: 13 },
        ],
        price: {
          gte: 3000,
          lte: 100000,
        },
        id: {
          in: changeRecordIds,
        },
        recordType: {
          not: "MANSION"
        },
      },
    }),
    prisma.proRawSumitomoAuction.count({
      where: {
        createdAt: {
          gte: dayjsWithTz().startOf('day').toISOString(),
        },
      },
    }),
    prisma.proRawSumitomoAuction.count({
      where: {
        bidEndDate: {
          gte: dayjsWithTz().startOf('day').toISOString(),
          lte: dayjsWithTz().endOf('day').add(1, 'day').toISOString(),
        },
        isFav: 1,
      },
    }),
    prisma.tllCustomerNeed.findMany({
      where: {
        agentUserId: currentUser.id,
      } as any,
      select: {
        id: true,
      },
    })
  ]);

  let todayCountForEachNeed = process.env.NODE_ENV === "production" ? await getRecordMatchSimpleForNeedIdsAction({ needIds: myCustomerNeedsIds.map(need => need.id) }) : { data: {} };
  let matchCount = process.env.NODE_ENV === "production" ? await getRecordMatchSimpleForNeedIdsAction({ needIds: myCustomerNeedsIds.map(need => need.id) }) : { data: {} };
  // logger.debug(`myCustomerNeedsMatchCount: ${myCustomerNeedsMatchCount}`);

  let uniqCount = todayUserLambdaRecordsAll; // uniqBy(todayUserLambdaRecordsAll, 'recordId')?.length;

  let todayFavChange = allFavChanges.map((record: any) => record.userLambdaRecordId).filter((id: any): id is string => id !== null && changeRecordIds.includes(id));

  res["/ex/fav"].value = todayFavChange.length ? "+" + todayFavChange.length.toString() : null;
  res["/ex/need"].value = (todayCountForEachNeed?.data && todayCountForEachNeed.data.length > 0) ? "+" + sum(Object.values(todayCountForEachNeed.data).map((item: any) => item.matchCount)).toString() : null;
  res["/ad/dashboard"].value = uniqCount ? "+" + uniqCount.toString() : null;
  res["/ad/keibaiSumifu"].value = `${shimekiriSumifuAllCount} / +${todaySumifuAllCount}`;

  if (currentUser?.accessLevel >= 90) {
    const todayBidAll = await prisma.tllUserLambdaRecordBid.findMany({
      where: {
        salesUserId: currentUser.id,
        status: {
          in: ["ONE_REQUEST_DATA", "TWO_NEGOTIATION", "THREE_DOCUMENT_PROPERTY_CHECK"]
        },
      },
    });

    res["/ad/bid"] = {
      value: `${todayBidAll.filter(bid => bid.status === "ONE_REQUEST_DATA").length} | ${todayBidAll.filter(bid => bid.status === "TWO_NEGOTIATION").length} | ${todayBidAll.filter(bid => bid.status === "THREE_DOCUMENT_PROPERTY_CHECK").length}`,
      name: "今日の入札数"
    };
  }

  return {
    data: res,
    success: true,
  };
}
