"use server";

import { auth } from "@/lib/auth";
import { ActionResponse } from "@/lib/definitions";
import { UserLambdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { UserLambdaRecordProps, UserLambdaRecordPropsWithLink, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { prisma } from "@/lib/prisma";
import dayjs from "dayjs";
import { logger } from "@/lib/logger";
import { supabase } from "@/lib/instances/supabase";
import { recommendationCache } from "@/lib/instances/nodeCache";
import { gt } from "lodash-es";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";

const MAX_RECOMMENDATION_COUNT = 10;
const PAST_X_DAY_FIND = 1;

export const getDailyUsage = async (): Promise<ActionResponse<UserLambdaRecordProps[]>> => {
  const session = await auth();

  if (!session?.user?.id) {
    return {
      success: false,
      message: "User not found",
    };
  }

  try {
    const [searchHistory] = await Promise.all([
      prisma.tllUserLambdaRecordSearchHistory.findMany({
        where: {
          userId: session?.user?.id,
          searchDate: {
            gte: dayjsWithTz().tz("Asia/Tokyo").startOf('day').toDate(),
          },
        },
      }),
    ]);

    const todaySearchCount = searchHistory.length;

    return {
      success: true,
      data: {
        todaySearchCount,
      },
    };
  } catch (error) {
    logger.error(`Error getting monthly usage: ${error}`);
    return {
      success: false,
      message: "Error getting lambda records",
    };
  }
}

export const getRecommendationBasedOnSearchHistory = async (): Promise<ActionResponse<UserLambdaRecordProps[]>> => {
  const session = await auth();

  if (!session?.user?.id) {
    return {
      success: false,
      message: "User not found",
    };
  }

  try {
    const searchHistory = await prisma.tllUserLambdaRecordSearchHistory.findMany({
      where: {
        userId: session?.user?.id,
      },
      orderBy: {
        searchDate: 'desc', // 按照搜索日期降序排列
      },
      take: 10, // 只获取最近的10条记录
    });

    const searchHistoryIds = searchHistory.map((item) => item.recordId);

    const records = await prisma.tllUserLambdaRecord.findMany({
      where: {
        id: { in: searchHistoryIds },
      },
      orderBy: {
        price: 'desc',
      },
    });

    const uniquePostalCodes = [...new Set(records.filter((item) => item.postalCode !== null).map((item) => item.postalCode))];
    const uniqueRecordTypes = [...new Set(records.map((item) => item.recordType))];

    const matchedRecords = await prisma.tllUserLambdaRecord.findMany({
      where: {
        postalCode: { in: uniquePostalCodes as number[] },
        id: {
          notIn: searchHistoryIds, // 确保id不在searchHistoryIds中
        },
        updatedAt: {
          gte: dayjs().subtract(PAST_X_DAY_FIND, 'day').startOf('day').toDate(),
          lt: dayjs().endOf('day').toDate(),
        },
        recordType: {
          in: uniqueRecordTypes as UserLambdaRecordType[]
        }
      },
      take: MAX_RECOMMENDATION_COUNT,
      include: {
        priceChanges: true,
        // materialMappings: true,
      }
    });

    return {
      success: true,
      data: (await padWithLink({ records: matchedRecords as UserLambdaRecordProps[] })).data,
    };
  } catch (error) {
    logger.error(`Error getting recommendation based on search history: ${error}`);
    return {
      success: false,
      message: "Error getting recommendation based on search history",
    };
  }
}

export const getRecommendationBasedOnValuationHistory = async (): Promise<ActionResponse<UserLambdaRecordProps[]>> => {
  // Valuation tracking has been removed, return empty recommendations
  return {
    success: true,
    data: [],
  };
}

export const getRecomBasedOnFav = async (): Promise<ActionResponse<UserLambdaRecordProps[]>> => {
  const session = await auth();

  if (!session?.user?.id) {
    return {
      success: false,
      message: "User not found",
    };
  }

  try {
    const userFavs = await prisma.tllUsersUserLambdaRecordsFavMap.findMany({
      where: {
        userId: session?.user?.id,
      },
      include: {
        userLambdaRecord: true,
      },
    });


    if (userFavs.length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    // find most frequent recordType
    const recordTypeCounts = userFavs.filter((item) => item.userLambdaRecord?.recordType !== null).reduce((acc: Record<UserLambdaRecordType, number>, item) => {
      acc[item.userLambdaRecord?.recordType as UserLambdaRecordType] = (acc[item.userLambdaRecord?.recordType as UserLambdaRecordType] ?? 0) + 1;
      return acc;
    }, {} as Record<UserLambdaRecordType, number>);

    const mostFrequentRecordType = Object.entries(recordTypeCounts).reduce((a, b) => a[1] > b[1] ? a : b, ["UNKNOWN", 0])[0];


    const uniqStationGroupIds = [...new Set(userFavs.map((item) => item.userLambdaRecord?.nearestStationGroupId ?? null).filter((item) => item !== null) as string[])];


    const record = await prisma.tllUserLambdaRecord.findMany({
      where: {
        // id: { in: (await getRecentChangeId()) as string[] },
        updatedAt: {
          gte: dayjs().subtract(PAST_X_DAY_FIND, 'day').startOf('day').toDate(),
          lt: dayjs().endOf('day').toDate(),
        },
        nearestStationGroupId: { in: uniqStationGroupIds },
        recordType: mostFrequentRecordType as UserLambdaRecordType,
      },
      take: MAX_RECOMMENDATION_COUNT,
      include: {
        // materialMappings: true,
        priceChanges: true,
      },
    });

    return {
      success: true,
      data: (await padWithLink({ records: record as UserLambdaRecordProps[] })).data,
    };
  } catch (error) {
    logger.error(`Error getting recommendation based on fav: ${error}`);
    return {
      success: false,
      message: "Error getting recommendation based on fav",
    };
  }
}

export const getRecommendationPriceDown = async (): Promise<ActionResponse<UserLambdaRecordProps[]>> => {
  try {
    const cached = recommendationCache.get("recommendationPriceDown");
    if (cached) {
      logger.info(`Recommendation using cached data for price down`);
      return {
        success: true,
        data: cached,
      };
    }

    const record = await prisma.tllUserLambdaRecord.findMany({
      where: {
        updatedAt: {
          gte: dayjs().subtract(PAST_X_DAY_FIND, 'day').startOf('day').toDate(),
          lt: dayjs().endOf('day').toDate(),
        },
      },
      include: {
        priceChanges: true,
        materialMappings: true, // FIxme: this wont mean it has photo, becuase this is for admin, but hsould sfufice for now 
      },
    });

    // filter items that has price down since last item and the change should be > 10% 
    const filteredRecord = record.filter((item) => {
      if (item.materialMappings.length === 0) {
        return false;
      }

      if (item.nearestStationWalkMinute && item.nearestStationWalkMinute > 15) {
        return false;
      }

      if (item.priceChanges.length < 2) {
        return false;
      }

      let last = item.priceChanges.sort((a, b) => a.recordDate.getTime() - b.recordDate.getTime())[item.priceChanges.length - 1];

      if (last.status === "成約") {
        return false;
      }

      let secondLast = item.priceChanges.sort((a, b) => a.recordDate.getTime() - b.recordDate.getTime())[item.priceChanges.length - 2];

      if (!last.price || !secondLast.price || last.price === secondLast.price) {
        return false;
      }

      let changePerc = (last.price - secondLast.price) / secondLast.price;
      return changePerc < 0 && changePerc < -0.1;
    });

    let dataWithPic = (await padWithLink({ records: filteredRecord.slice(0, MAX_RECOMMENDATION_COUNT) as UserLambdaRecordProps[] })).data;
    recommendationCache.set("recommendationPriceDown", dataWithPic);

    return {
      success: true,
      data: dataWithPic,
    };
  } catch (error) {
    logger.error(`Error getting recommendation price down: ${error}`);
    return {
      success: false,
      message: "Error getting recommendation price down",
    };
  }
}

export const getRecommendationHighYield = async (): Promise<ActionResponse<UserLambdaRecordProps[]>> => {
  try {
    const cached = recommendationCache.get("recommendationHighYield");
    if (cached) {
      logger.info(`Recommendation using cached data`);
      return {
        success: true,
        data: cached,
      };
    }

    const record = await prisma.tllUserLambdaRecord.findMany({
      where: {
        updatedAt: {
          gte: dayjs().subtract(PAST_X_DAY_FIND, 'day').startOf('day').toDate(),
          lt: dayjs().endOf('day').toDate(),
        },
        recordType: "BUILDING",
        yearlyIncome: { gt: 0 },
      },
      include: {
        priceChanges: true,
        materialMappings: true, // FIxme: this wont mean it has photo, becuase this is for admin, but hsould sfufice for now 
      },
    });

    // filter items that has price down since last item and the change should be > 10% 
    const filteredRecord = record.filter((item) => {
      if (item.materialMappings.length === 0) {
        return false;
      }

      if (item.nearestStationWalkMinute && item.nearestStationWalkMinute > 15) {
        return false;
      }

      if (item.yearlyIncome === null) {
        return false;
      }

      if (item.price && item.yearlyIncome / item.price < 0.07) {
        return false;
      }

      let last = item.priceChanges.sort((a, b) => a.recordDate.getTime() - b.recordDate.getTime())[item.priceChanges.length - 1];
      if (last.status === "成約") {
        return false;
      }

      return true;
    });

    let dataWithPic = (await padWithLink({ records: filteredRecord.slice(0, MAX_RECOMMENDATION_COUNT) as UserLambdaRecordProps[] })).data;

    recommendationCache.set("recommendationHighYield", dataWithPic);
    return {
      success: true,
      data: dataWithPic,
    };
  } catch (error) {
    logger.error(`Error getting recommendation price down: ${error}`);
    return {
      success: false,
      message: "Error getting recommendation price down",
    };
  }
}

// FIXME: in future you should just keep a link
async function getOneLinkforRecord({ id }: { id: string }): Promise<string> {
  const { data, error } = await supabase.storage.from("property-materials-public").list(id);

  if (error || !data || data.length === 0) {
    return "";
  }

  function convertToCompressedImageUrl(originalUrl: string, width = 400, height = 300, quality = 70) {
    if (!originalUrl.includes("/storage/v1/object/public/")) return originalUrl;

    const base = originalUrl.replace("/storage/v1/object/public/", "/storage/v1/render/image/public/");
    const connector = base.includes("?") ? "&" : "?";
    let res = `${base}${connector}width=${width}&height=${height}&quality=${quality}`;
    return res;
  }

  let raw = `${process.env.SUPABASE_URL}/storage/v1/object/public/property-materials-public/${id}/${data?.[0]?.name || ""}`;
  return convertToCompressedImageUrl(raw);
}


export const padWithLink = async ({ records }: { records: UserLambdaRecordProps[] }): Promise<ActionResponse<UserLambdaRecordPropsWithLink[]>> => {
  try {
    let padded = [] as any;
    for (const record of records) {
      if (record.id) {
        padded.push({
          ...record,
          link: await getOneLinkforRecord({ id: record.id }),
        });
      }
    }

    return {
      success: true,
      data: padded,
    };
  } catch (error) {
    logger.error(`Error getting recommendation price down: ${error}`);
    return {
      success: false,
      message: "Error getting recommendation price down",
    };
  }
}