'use server';

import { signIn } from '@/lib/auth';
import { AuthError } from 'next-auth';
import { ActionResponse } from '@/lib/definitions';
import { prisma } from '@/lib/prisma';
import { Resend } from 'resend';
import { logger } from '@/lib/logger';
import { render } from "@react-email/render";
import { sendLark, LARK_URLS } from '@/lib/thirdParty/lark';
import bcrypt from 'bcrypt';
import { TllUserActivationTokenType } from '@prisma/client';
import { ActivateEmail } from '@/app/pub/emails/activateEmail';
import { sendOnboardingEmail } from '@/lib/emails/user';

export async function authenticate(
  formData: FormData,
): Promise<ActionResponse> {
  console.log('🔥formData', formData);

  try {
    const result = await signIn('credentials', { redirect: false, ...formData });
    if (result?.error) {
      throw new AuthError(result.error);
    }

    return { success: true, message: 'Login successful.' };
  } catch (error) {
    console.log('🔥 error', error);
    if (error instanceof AuthError) {
      switch (error.type) {
        case 'CredentialsSignin':
          return { success: false, message: 'ユーザー名かパスワードが間違っています。' };
        default:
          return { success: false, message: 'Something went wrong.' };
      }
    }
    return { success: false, message: "Unexpected error occurred." };
  }
}

export async function getGoogleLoginRedirectUrl(): Promise<{
  success: boolean;
  message: string;
  url: string;
}> {
  try {
    const resultUrl = await signIn('google', { redirect: false });
    console.log('🔥 resultUrl', resultUrl);
    return { success: true, message: 'Login successful.', url: resultUrl };
  } catch (error) {
    console.log('🔥 error', error);
    throw new AuthError(error as string);
  }
}

export async function checkEmailAndSendEmail(email: string): Promise<ActionResponse> {
  try {
    const existingUser = await prisma.tllUser.findUnique({ where: { email } });

    if (existingUser) {
      return { success: false, message: 'このメールアドレスは既に使用されています。ログインしてください。' };
    }

    // sendLark({
    //   message: `新規ユーザー登録のリクエストがありました。\nEmail: ${email}`,
    //   url: LARK_URLS.USER_ACTIVITY_CHANNEL
    // })

    // return { success: true, message: 'リクエストありがとうございます。登録が可能になりましたらご連絡いたします。' }
    const findExistingToken = await prisma.tllUserActivationToken.findFirst({
      where: { email, used: false, expiresAt: { gt: new Date() } },
      orderBy: { expiresAt: 'desc' },
    });

    let tokenToUse = findExistingToken?.token;
    if (!tokenToUse) {
      tokenToUse = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24); // 24 hours

      await prisma.tllUserActivationToken.create({
        data: { email, token: tokenToUse, expiresAt, tokenType: TllUserActivationTokenType.EMAIL_VERIFICATION },
      });
      logger.info('🔥 new token created', { email, token: tokenToUse });
    } else {
      logger.info('🔥 token found', { email, token: tokenToUse });
    }

    const resend = new Resend(process.env.RESEND_API_KEY);

    const html = await render(
      <ActivateEmail activationLink={`${process.env.NEXT_PUBLIC_BASE_URL}/activate?token=${tokenToUse}`} />
    )
    console.log("🔥 rendered email html", html);

    let data = {
      from: 'Urbalytics <<EMAIL>>',
      to: email,
      replyTo: '<EMAIL>',
      subject: 'アカウント作成の確認',
      html: html
    }
    console.log("🔥 data", data);
    await resend.emails.send(data);

    return { success: true, message: 'メールを送信しました。アカウントを有効化するためにメールをご確認ください。' };
  } catch (error) {
    logger.error('🔥 error', error);
    return { success: false, message: 'Something went wrong.' };
  }
}

export async function verifyToken(token: string): Promise<ActionResponse> {
  const tokenEntry = await prisma.tllUserActivationToken.findUnique({ where: { token } });

  if (!tokenEntry || tokenEntry.used || tokenEntry.expiresAt < new Date()) {
    return { success: false, message: 'Invalid activation token.' };
  }

  await prisma.tllUserActivationToken.update({
    where: { token },
    data: { used: true },
  });

  return { success: true, message: 'Activation token verified.', data: { email: tokenEntry.email } };
}


export async function setUserPassword({ email, password }: { email: string, password: string }): Promise<ActionResponse> {
  const hashed = await bcrypt.hash(password, 10);

  try {
    await prisma.tllUser.create({
      data: {
        name: "New User",
        email,
        password: hashed,
        source: 'ACTIVATION_LINK',
        accessLevel: 1,
        userSetting: {
          dailyEmailNotification: true
        }
      },
    });

    // cleanup token
    await prisma.tllUserActivationToken.deleteMany({ where: { email } });
    sendOnboardingEmail({ email });

    await sendLark({
      message: `[auth][credentials] Account creation successful for user: ${email}`,
      url: LARK_URLS.USER_AQUISITION_CHANNEL,
    });

    return { success: true };
  } catch (err) {
    return { success: false, message: 'Account could not be created.' };
  }
}
