"use server";

import { prisma } from '@/lib/prisma'; // 假设你有一个 prisma 实例
import { ActionResponse } from '@/lib/definitions';
import { UserLambdaRecordProps, UserLambdaRecordType } from '@/lib/definitions/userLambdaRecord';
import dayjsWithTz from '@/lib/thirdParty/dayjsWithTz';
import { logger } from '@/lib/logger';
import { auth } from '@/lib/auth';
import dayjs from '@/lib/thirdParty/dayjsWithTz';
import { number } from 'zod';

export async function getUserLambdaRecords(data: any): Promise<ActionResponse<UserLambdaRecordProps[]>> {
  let whereParams = {};
  if (Object.keys(data).length === 0) {
    return {
      success: false,
      message: "No data provided, must get a filter",
    }
  }

  if (data) {
    const { nearestStation, postalCode, recordType, buildingId, nearestStationGroupId, areaCode } = data;

    // NOTE: must follow the order of index
    whereParams = {
      ...(recordType ? { recordType } : {}), // 仅在 recordType 有值时添加
      ...(nearestStationGroupId ? { nearestStationGroupId: nearestStationGroupId.toString() } : {}),
      ...(nearestStation ? { nearestStation } : {}), // 仅在 nearestStation 有值时添加
      ...(postalCode ? { postalCode: Number(postalCode) } : {}), // 仅在 postalCode 有值时添加
      ...(buildingId ? { buildingId: buildingId.toString() } : {}), // 仅在 buildingId 有值时添加
      ...(areaCode ? { areaCode: Number(areaCode) } : {}), // 仅在 areaId 有值时添加
    }
  }

  console.log("whereParams", whereParams);

  if (Object.keys(whereParams).length === 0) {
    return {
      success: true,
      data: [],
    }
  }

  const records = await prisma.tllUserLambdaRecord.findMany({
    where: {
      ...whereParams,
      // FIXME: might be outdated
    },
    include: {
      // FIXME:foreigner key 
      // userLambda: true,
      priceChanges: {
        include: {
          company: true,
        },
      },
      bids: {
        select: {
          id: true, // 只选择 bids 的 id
        },
      },
      nearestStationGroup: true,
      propertyAnalysisResult: true,
    },
  });

  return {
    success: true,
    data: records,
  };
}

// 获取单个用户记录
export async function getUserLambdaRecordAction(id: string): Promise<ActionResponse<UserLambdaRecordProps>> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return {
        success: false,
        message: "ユーザーはログインしていません",
      };
    }

    // TODO: do the quota check ehre too 
    // Also create record for the user (for pricing history)
    const searchHistoryExists = await prisma.tllUserLambdaRecordSearchHistory.findFirst({
      where: {
        userId: session?.user?.id,
        recordId: id,
        isValid: true,
      },
    });

    if (!searchHistoryExists) {
      const dataToCreate = {
        userId: session?.user?.id,
        recordId: id,
        searchDate: dayjsWithTz().toISOString(),
      }

      await prisma.tllUserLambdaRecordSearchHistory.create({
        data: dataToCreate,
      });
    }


    const record = await prisma.tllUserLambdaRecord.findUnique({
      where: { id },
      include: {
        priceChanges: {
          include: {
            company: true,
          },
        },
        nearestStationGroup: true,
        bids: session?.user?.accessLevel >= 90 ? {
          include: {
            salesUser: {
              omit: {
                password: true,
              }
            },
          },
        } : false,
        materialMappings: true,
        propertyAnalysisResult: true,
        building: {
          include: {
            mansionRents: true,
          }
        }
      },
    }) as UserLambdaRecordProps;


    return {
      success: true,
      data: record,
    };
  } catch (error) {
    logger.error("getUserLambdaRecordAction error", error);
    return {
      success: false,
      message: 'Failed to get user lambda record',
    };
  }
}

// 获取单个用户记录
export async function getUserLambdaRecordForSnsAction(id: string): Promise<ActionResponse<UserLambdaRecordProps>> {
  const record = await prisma.tllUserLambdaRecord.findUnique({
    where: { id },
    include: {
      priceChanges: {
        include: {
          company: true,
        },
      },
      nearestStationGroup: true,
      propertyAnalysisResult: true,
    },
  });

  return {
    success: true,
    data: record,
  };
}

// 删除用户记录
export async function deleteUserLambdaRecord(id: string): Promise<ActionResponse<UserLambdaRecordProps>> {
  await prisma.tllUserLambdaRecord.delete({
    where: { id },
  });
  return {
    success: true,
    message: 'Record 已删除',
  };
}


// 更新用户记录
export async function updateUserLambdaRecord(id: string, data: {
  salesComments?: string;
  buildingRoomCount?: number;
  buildingLevel?: string;
  yearlyIncome?: number;
  valueRosenka?: number;
  landFloorAreaRatio?: number;
  landBuildingCoverageRatio?: number;
  landRight?: string;
  nearestStationWalkMinute?: number;
  buildingMaterial?: string;
  buildingBuiltYear?: number;
  landType?: string;
}): Promise<ActionResponse<UserLambdaRecordProps>> {
  logger.debug("updateUserLambdaRecord", data);

  let match = await prisma.tllUserLambdaRecord.findUnique({
    where: { id },
  });

  if (!match) {
    return { success: false, message: "Record not found" };
  }

  const record = await prisma.tllUserLambdaRecord.update({
    where: { id },
    data: {
      salesComments: data.salesComments,
      buildingRoomCount: data.buildingRoomCount,
      buildingLevel: data.buildingLevel,
      yearlyIncome: data.yearlyIncome,
      valueRosenka: data.valueRosenka,
      landFloorAreaRatio: data.landFloorAreaRatio,
      landBuildingCoverageRatio: data.landBuildingCoverageRatio,
      landRight: data.landRight,
      nearestStationWalkMinute: data.nearestStationWalkMinute,
      updatedAt: match.updatedAt, // do not update the updatedAt as this happens from the frontend
      buildingMaterial: data.buildingMaterial,
      buildingBuiltYear: data.buildingBuiltYear,
      landType: data.landType,
    },
  });
  return {
    success: true,
    data: record,
  };
}

export async function getUserLambdaRecordMetaAction(id: string): Promise<ActionResponse<UserLambdaRecordProps>> {
  try {
    const record = await prisma.tllUserLambdaRecord.findUnique({
      where: { id },
      select: {
        recordType: true,
        price: true,
        landSize: true,
        transport: true,
        nearestStationWalkMinute: true,
        landRight: true,
        landType: true,
        buildingSize: true,
        buildingMaterial: true,
        buildingBuiltYear: true,
        address: true,
        propertyAnalysisResult: {
          select: {
            overallStarLevel: true,
          },
        },
        compositeTitle: true,
      },
    });

    return {
      success: true,
      data: record,
    };
  } catch (error) {
    console.error("Error fetching user lambda record metadata:", error);
    return {
      success: false,
      message: 'Failed to fetch user lambda record metadata',
    };
  }
}

export async function getUserLambdaRecordForSNS(data: { dataDate: string, dataPrefecture: { label: string, value: number }[], dataRecordType: UserLambdaRecordType }): Promise<ActionResponse<UserLambdaRecordProps>> {
  const { dataDate, dataPrefecture, dataRecordType } = data;

  if (!dataDate || !dataPrefecture || !dataRecordType) {
    return {
      success: false,
      message: 'No data provided, must provide all the right filters',
    }
  }

  let dayDiff = 0;
  if (dataDate === 'today') {
    dayDiff = 0;
  } else if (dataDate === 'yesterday') {
    dayDiff = 1;
  } else if (dataDate === 'threeDays') {
    dayDiff = 3;
  } else if (dataDate === 'oneWeek') {
    dayDiff = 7;
  }

  let whereCriteria = {
    createdAt: {
      gte: dayjsWithTz
        .utc()
        .subtract(dayDiff, "days")
        .format("YYYY-MM-DD"),
    },

  };

  let changeRecords = await prisma.tllUserLambdaRecordPriceChange.findMany({
    where: {
      createdAt: {
        gte: dayjs()
          .subtract(dayDiff, "days")
          .toDate(),
      },
    },
  });
  let recordIds = changeRecords.map((v: any) => v.recordId);

  if (recordIds.length === 0) {
    return {
      success: true,
      data: [],
    }
  }

  const records = await prisma.tllUserLambdaRecord.findMany({
    where: {
      id: {
        in: recordIds,
      },
      recordType: dataRecordType,
      prefectureCode: {
        in: dataPrefecture.map((v: any) => v.value),
      },
      // Some additional checkes to make sure only relevant records are shown
      landSize: {
        not: null,
      },
      buildingSize: {
        not: null,
      },
      buildingBuiltYear: {
        not: null,
      },
      buildingMaterial: {
        not: null,
      },
      nearestStation: {
        not: null,
      },
      nearestStationWalkMinute: {
        lte: 15,
      },
      landRight: "所有権"
    },
    include: {
      priceChanges: true,
    },
    omit: {
      analysisSimulationConfig: true,
      analysisSimulationResults: true,
    },
  });

  return {
    success: true,
    data: records,
  };
}

export async function getUserLambdaRecordForDashboardWatchlist({ postalCode, nearestStationGroupId, dataRecordType }: { postalCode?: string, nearestStationGroupId?: string, dataRecordType: UserLambdaRecordType }): Promise<ActionResponse<UserLambdaRecordProps>> {

  if (!dataRecordType || (!postalCode && !nearestStationGroupId)) {
    return {
      success: false,
      message: 'No data provided, must provide all the right filters',
    }
  }

  let changeRecords = await prisma.tllUserLambdaRecordPriceChange.findMany({
    where: {
      createdAt: {
        gte: dayjs()
          .subtract(1, "days")
          .toDate(),
      },
    },
  });

  let recordIds = changeRecords.map((v: any) => v.recordId);

  if (recordIds.length === 0) {
    return {
      success: true,
      data: [],
    }
  }

  let whereParams = {
    id: {
      in: recordIds,
    },
    recordType: dataRecordType,
    ...(postalCode ? { postalCode: parseInt(postalCode) } : {}),
    ...(nearestStationGroupId ? { nearestStationGroupId } : {}),
    landSize: {
      not: null,
    },
    buildingSize: {
      not: null,
    },
    buildingBuiltYear: {
      not: null,
    },
    buildingMaterial: {
      not: null,
    },
    nearestStation: {
      not: null,
    },
    nearestStationWalkMinute: {
      lte: 15,
    },
    landRight: "所有権"
  } as any;

  const records = await prisma.tllUserLambdaRecord.findMany({
    where: whereParams,
    include: {
      priceChanges: true,
    },
    omit: {
      analysisSimulationConfig: true,
      analysisSimulationResults: true,
    },
  });

  return {
    success: true,
    data: records,
  };
}

export async function getMatchedPropertiesBeforeValuation(data: { recordType: UserLambdaRecordType, nearestStationGroupId: string, landSize?: number, buildingSize?: number }): Promise<ActionResponse<UserLambdaRecordProps>> {
  const { recordType, nearestStationGroupId, landSize, buildingSize } = data;

  let whereParams = {
    nearestStationGroupId: nearestStationGroupId,
    recordType: recordType,
    ...(landSize ? { landSize } : {}),
    ...(buildingSize ? { buildingSize } : {}),
  } as any;

  const records = await prisma.tllUserLambdaRecord.findMany({
    where: whereParams,
  });

  return {
    success: true,
    data: records,
  };
}

