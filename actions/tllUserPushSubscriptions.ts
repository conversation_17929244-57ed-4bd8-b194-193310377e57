"use server";

import { auth } from "@/lib/auth";
import { ActionResponse, TllUserPushSubscriptionProps } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import webpush from "@/lib/thirdParty/webpush";

export async function getAllPushSubscriptions() {
  const session = await auth();

  const activeSubscriptions = await prisma.tllUserPushSubscription.findMany({
    include: {
      user: true,
    },
  });

  return {
    success: true,
    message: "All push subscriptions retrieved",
    data: activeSubscriptions,
  };
}


export async function createUserPushSubscription(subscription: any, deviceId: string): Promise<ActionResponse<TllUserPushSubscriptionProps>> {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "Unauthorized",
    };
  }

  if (!subscription || !deviceId) {
    return {
      success: false,
      message: "Subscription and deviceId are required",
    };
  }

  const existingSubscription = await prisma.tllUserPushSubscription.findUnique({
    where: {
      userId_deviceId: {
        userId: currentUser.id,
        deviceId: deviceId,
      },
    },
  });

  if (existingSubscription) {
    return {
      success: false,
      message: "Subscription already exists",
    };
  }

  const newSubscription = await prisma.tllUserPushSubscription.create({
    data: {
      userId: currentUser.id,
      deviceId: deviceId,
      subscription: subscription,
    },
    include: {
      user: true,
    },
  });

  sendLark({
    message: `[PUSH][${newSubscription?.user?.name}] のプッシュ通知が作成されました。Device id: ${deviceId}`,
    url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
  });

  return {
    success: true,
    message: "Subscription created",
    data: newSubscription,
  };
}

export async function deleteUserPushSubscription(id: string): Promise<ActionResponse<TllUserPushSubscriptionProps>> {
  if (!id) {
    return {
      success: false,
      message: "ID is required",
    };
  }

  const deletedSubscription = await prisma.tllUserPushSubscription.delete({
    where: {
      id: id,
    },
    include: {
      user: true,
    },
  });

  sendLark({
    message: `[PUSH][${deletedSubscription?.user?.name}] のプッシュ通知が削除されました。`,
    url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
  });

  return {
    success: true,
    message: "Subscription deleted",
    data: deletedSubscription,
  };
}

export async function sendNotificationsToAll({ url, subAndInfos }: { url: string, subAndInfos: { title: string, message: string, subscription: TllUserPushSubscriptionProps[] }[] }): Promise<ActionResponse<any>> {
  if (subAndInfos.length === 0) {
    return {
      success: false,
      message: "No subscription available",
    };
  }

  let results = await Promise.allSettled(
    subAndInfos.map(async (subAndInfo: any) => {
      try {
        await webpush.sendNotification(
          subAndInfo.subscription as any, // Ensure `subscription` field is correctly formatted
          JSON.stringify({
            title: subAndInfo.title,
            body: subAndInfo.message,
            data: {
              url: url,
            },
          })
        );
        return { success: true, userId: subAndInfo.subscription.userId };
      } catch (error: any) {
        if (error.statusCode === 410) { // 410 means subscription is gone
          console.log(`Removing expired subscription for device ${subAndInfo.subscription.deviceId}`);
          await prisma.tllUserPushSubscription.delete({
            where: { userId_deviceId: { userId: subAndInfo.subscription.userId, deviceId: subAndInfo.subscription.deviceId } },
          });
        }

        logger.error(`❌ Push notification failed for user ${subAndInfo.subscription.userId}:`, error);
        return { success: false, userId: subAndInfo.subscription.userId, error: error instanceof Error ? error.message : 'Unknown error' };
      }
    })
  );

  // Sample output
  //   {
  //     "success": true,
  //     "res": [
  //         {
  //             "status": "fulfilled",
  //             "value": {
  //                 "success": true,
  //                 "userId": "1"
  //             }
  //         }
  //     ]
  // }

  // Log the results of all notifications
  logger.info('📬 Push Notification Results:', results);
  return { success: true, data: results };
}

export async function sendNotificationToMe(message: string, deviceId: string): Promise<ActionResponse<any>> {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "Unauthorized",
    };
  }

  const subscription = await prisma.tllUserPushSubscription.findUnique({
    where: {
      userId_deviceId: {
        userId: currentUser.id,
        deviceId: deviceId,
      },
    },
    include: {
      user: true,
    },
  }) as TllUserPushSubscriptionProps;

  if (!subscription) {
    return {
      success: false,
      message: "Subscription not found",
    };
  }

  await webpush.sendNotification(
    subscription.subscription as any, // Ensure `subscription` field is correctly formatted
    JSON.stringify({
      title: 'Test Notification',
      body: message,
    })
  );

  return {
    success: true,
    message: "Notification sent",
    data: subscription,
  };
}

export async function getUserCurrentActivePushSubscriptionsForDevice(deviceId: string) {
  const session = await auth();
  const currentUser = session?.user;

  if (!currentUser || !currentUser?.id) {
    return {
      success: false,
      message: "Unauthorized",
    };
  }

  const activeSubscriptions = await prisma.tllUserPushSubscription.findUnique({
    where: {
      userId_deviceId: {
        userId: currentUser.id,
        deviceId: deviceId,
      },
    },
    include: {
      user: true,
    },
  }) as TllUserPushSubscriptionProps;

  if (!activeSubscriptions) {
    return {
      success: false,
      message: "No active subscriptions found",
    };
  }

  return {
    success: true,
    message: "Active subscriptions retrieved",
    data: activeSubscriptions,
  };
}
