"use server";
import { supabase } from "@/lib/instances/supabase";
import { ActionResponse } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import openaiInstance from "@/lib/instances/openai";
import pdf from "pdf-parse";

const prompt = `
请取出以下信息，把结果存入json, json内容保留日语。
price(数字，万元为单位)，yearlyIncome(数字，万元为单位)，roi（转换为小数）， buildingSize(总建筑面积, 小数, 去掉单位), landSize(总土地面积, 小数, 去掉单位), address, nearestStation(去掉线路, 只保留车站名), nearestStationWalkMinute(徒步分数, 数字, 如果需要巴士用999), buildingMaterial(木造，铁骨造, 或者RC),  landRight(借地権、底地権、所有権), buildingBuiltYear

只返回json，不要返回其他内容。
`;

export async function aiAnalyzePdf({ base64 }: { base64: string }): Promise<ActionResponse<any>> {
  try {
    const buffer = Buffer.from(base64.replace(/^data:application\/pdf;base64,/, ""), 'base64');
    const data = await pdf(buffer);

    console.log('data', data);


    const response = await openaiInstance.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt,
            },
            {
              type: "text",
              text: data.text,
            },
          ],
        },
      ],
    });

    // 提取内容字段
    let content = response.choices[0].message.content;
    logger.info('content', content);
    content = content ? content.replace(/```json|```/g, "").trim() : "";
    const parsedJson = content ? JSON.parse(content) : {};

    return {
      success: true,
      data: {
        ...parsedJson,
      },
    };
  } catch (err: any) {
    logger.error("上传文件时出错:", err);
    return {
      success: false,
      message: "上传文件时出错",
      errors: err,
    };
  }
}