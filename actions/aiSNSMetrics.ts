"use server";

import { AiSnsContentProps, AiSnsMetricsProps } from "@/lib/definitions";
import { ActionResponse } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import isoWeek from "dayjs/plugin/isoWeek";
dayjsWithTz.extend(isoWeek); // Extend Day.js with the isoWeek plugin

export const getAiSnsMetrics = async (): Promise<ActionResponse<AiSnsMetricsProps[]>> => {
  try {
    const aiSnsMetrics = await prisma.aiSNSMetric.findMany({
      orderBy: {
        recordDate: "desc",
      },
    });
    return {
      success: true,
      data: aiSnsMetrics,
      message: "AI SNS メトリクスを取得しました。",
    };
  } catch (error) {
    logger.error("error", error);
    return {
      success: false,
      data: null,
      message: "AI SNS メトリクスを取得できませんでした。",
    };
  }
};

export const createAiSnsMetrics = async (data: AiSnsMetricsProps): Promise<ActionResponse<AiSnsMetricsProps>> => {
  try {
    if (data.recordDate) {
      const existingAiSnsMetrics = await prisma.aiSNSMetric.findUnique({
        where: { recordDate: data.recordDate },
      });
      if (existingAiSnsMetrics) {
        return {
          success: false,
          data: null,
          message: "AI SNS メトリクスが既に存在します。",
        };
      }
    }

    let dateObject = dayjsWithTz(data.recordDate).tz("Asia/Tokyo").toDate();
    const aiSnsMetrics = await prisma.aiSNSMetric.create({
      data: {
        ...data,
        isoWeekNumber: dayjsWithTz(dateObject).isoWeek(),
        day: dayjsWithTz(dateObject).date(),
        month: dayjsWithTz(dateObject).month() + 1,
        year: dayjsWithTz(dateObject).year(),
      },
    });

    return {
      success: true,
      data: aiSnsMetrics,
      message: "AI SNS メトリクスを作成しました。",
    };
  } catch (error) {
    logger.error("error", error);
    return {
      success: false,
      data: null,
      message: "AI SNS メトリクスを作成できませんでした。",
    };
  }
};

export const deleteAiSnsMetrics = async (id: string): Promise<ActionResponse<AiSnsMetricsProps>> => {
  try {
    const aiSnsMetrics = await prisma.aiSNSMetric.delete({
      where: { id },
    });

    return {
      success: true,
      data: aiSnsMetrics,
      message: "AI SNS メトリクスを削除しました。",
    };
  } catch (error) {
    logger.error("error", error);
    return {
      success: false,
      data: null,
      message: "AI SNS メトリクスを削除できませんでした。",
    };
  }
} 