"use server";

import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { ActionResponse } from "@/lib/definitions";
import { auth } from "@/lib/auth";
import bcrypt from 'bcrypt';
import { TllUserProps, TllUserFormSchema, TllUserActivationTokenType, TllUserActivationTokenProps } from "@/lib/definitions/tllUser";
import resend from "@/lib/thirdParty/resend";
import { sendPasswordChangedEmail, sendPasswordResetLinkEmail, sendNewlySetUserWelcomeEmail } from "@/lib/emails/user";
import { Action } from "@radix-ui/react-alert-dialog";

export async function fuzzySearchUserAction(searchTerm: string): Promise<ActionResponse<TllUserProps[]>> {
  console.log("🔥[Users] searchTerm is", searchTerm);

  try {
    const users = await prisma.tllUser.findMany({
      where: {
        OR: [
          { name: { contains: searchTerm, mode: "insensitive" } },
          { email: { contains: searchTerm, mode: "insensitive" } },
        ],
      },
    });

    return {
      success: true,
      data: users,
    };
  } catch (error) {
    console.error("error", error);
    return {
      success: false,
      message: 'Failed to fuzzy search user',
    };
  }
}
export async function createUserAction(data: TllUserProps): Promise<ActionResponse<TllUserProps>> {
  if (!data.password || !data.email) {
    return {
      success: false,
      message: 'Missing Fields. Failed to Create User.',
    };
  }

  const findMatch = await prisma.tllUser.findUnique({
    where: {
      email: data.email,
    },
  });

  if (findMatch) {
    return {
      success: false,
      message: 'User already exists',
    };
  }

  const user = await prisma.tllUser.create({
    data: {
      source: data.source || '',
      name: data.name || '',
      email: data.email,
      password: await bcrypt.hash(data.password, 10),
      imageUrl: data.imageUrl || '',
      accessLevel: data.accessLevel || 1,
      // userSetting: {
      //   dailyEmailNotification: true
      // }
    },
  });

  await sendNewlySetUserWelcomeEmail(user as TllUserProps);

  if (!user) {
    return {
      success: false,
      message: 'Failed to create user',
    };
  }

  return {
    success: true,
    data: user,
  };
}

export async function getUsersAction(): Promise<ActionResponse<TllUserProps[]>> {
  const users = await prisma.tllUser.findMany({
    orderBy: {
      createdAt: 'desc',
    },
  });

  return {
    success: true,
    data: users,
  };
}

export async function getUserById(id: string): Promise<ActionResponse<TllUserProps>> {
  const user = await prisma.tllUser.findUnique({
    where: {
      id,
    },
    omit: {
      password: true,
    },
  });

  return {
    success: true,
    data: user,
  };
}

export async function updateUserAction({ id, data, sendEmail = false }: { id: string, data: z.infer<typeof TllUserFormSchema>, sendEmail?: boolean }): Promise<ActionResponse<TllUserProps>> {
  const dataToSave = {
    name: data.name,
    email: data.email,
    ...(data.source && { source: data.source }),
    ...(data.accessLevel && { accessLevel: data.accessLevel }),
    ...(data.password && { password: await bcrypt.hash(data.password, 10) })
  };

  console.log("dataToSave", dataToSave);

  console.log("sendEmail", sendEmail);

  const user = await prisma.tllUser.update({
    where: { id },
    data: dataToSave,
  });

  if (sendEmail) {
    await sendPasswordChangedEmail({
      ...user,
      password: dataToSave.password
    } as TllUserProps);
  }

  if (!user) {
    return {
      success: false,
      message: 'Failed to update user',
    };
  }

  return {
    success: true,
    data: user,
  };
}

export async function deleteUserAction(id: string): Promise<ActionResponse<TllUserProps>> {
  const user = await prisma.tllUser.delete({
    where: { id },
  });

  if (!user) {
    return {
      success: false,
      message: 'Failed to delete user',
    };
  }

  return {
    success: true
  };
}

export async function changePasswordAction({ currentPassword, newPassword }: { currentPassword: string, newPassword: string }): Promise<ActionResponse<TllUserProps>> {

  const session = await auth();
  const userId = session?.user?.id;

  if (!userId) {
    return {
      success: false,
      message: 'User not found',
    };
  }

  try {
    const matchedUser = await prisma.tllUser.findUnique({
      where: { id: userId },
    }) as any;

    if (!matchedUser) {
      return {
        success: false,
        message: 'User not found',
      };
    }

    const passwordsMatch = await bcrypt.compare(currentPassword, matchedUser.password);

    if (!passwordsMatch) {
      return {
        success: false,
        message: '現在のパスワードが間違っています',
      };
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10); // 使用 bcrypt 加密新密码
    const user = await prisma.tllUser.update({
      where: { id: userId },
      data: { password: hashedPassword }, // 更新为加密后的密码
    });

    if (!user) {
      return {
        success: false,
        message: 'Failed to change password',
      };
    }

    return {
      success: true,
      data: user,
    };
  } catch (error) {
    return {
      success: false,
      message: 'Failed to change password',
    };
  }
}

export async function sendResetPasswordEmail(email: string): Promise<ActionResponse<TllUserProps>> {
  try {
    let matchedUser = await prisma.tllUser.findUnique({
      where: { email },
    });

    if (!matchedUser) {
      return {
        success: false,
        message: 'ユーザーが見つかりません',
      };
    }

    let tokenToUse = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24); // 24 hours

    await prisma.tllUserActivationToken.create({
      data: { email, token: tokenToUse, expiresAt, tokenType: TllUserActivationTokenType.PASSWORD_RESET },
    });

    await sendPasswordResetLinkEmail({
      email: matchedUser.email as string,
      token: tokenToUse,
    });


    // await sendPasswordChangedEmail({
    //   ...updatedUser,
    //   password: newPassword
    // } as TllUserProps);

    return {
      success: true,
      message: 'Email sent',
    };
  } catch (error) {
    console.error("error", error);
    return {
      success: false,
      message: 'Failed to send reset password email',
    };
  }
}

export async function verifyResetPasswordToken(token: string): Promise<ActionResponse<TllUserActivationTokenProps>> {
  try {
    const tokenEntry = await prisma.tllUserActivationToken.findUnique({
      where: { token },
    });

    if (!tokenEntry || tokenEntry.used || tokenEntry.expiresAt < new Date()) {
      return { success: false, message: 'Invalid activation token.' };
    }

    return {
      success: true,
      data: tokenEntry,
    };
  } catch (error) {
    console.error("error", error);
    return {
      success: false,
      message: 'Failed to verify reset password token',
    };
  }
}

export async function resetPasswordWithTokenAction({ token, newPassword }: { token: string, newPassword: string }): Promise<ActionResponse<TllUserProps>> {
  try {
    const tokenEntry = await prisma.tllUserActivationToken.findUnique({
      where: { token },
    });

    if (!tokenEntry || tokenEntry.used || tokenEntry.expiresAt < new Date()) {
      return { success: false, message: 'Invalid activation token.' };
    }

    await prisma.tllUserActivationToken.update({
      where: { token },
      data: { used: true },
    });

    const user = await prisma.tllUser.update({
      where: { email: tokenEntry.email },
      data: { password: await bcrypt.hash(newPassword, 10) },
    });

    return { success: true, message: 'Password reset successful.' };
  } catch (error) {
    console.error("error", error);
    return { success: false, message: 'Failed to reset password.' };
  }
}
