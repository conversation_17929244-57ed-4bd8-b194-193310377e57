"use server";

import { prisma } from "@/lib/prisma";
import { NextApiRequest, NextApiResponse } from "next";
import { ActionResponse } from "@/lib/definitions";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { padWithLink } from "./dashboard";
import dayjs from "dayjs";

export async function getTopResultsFromIds({
  distanceXId,
  forRecommendation = false,
}: {
  distanceXId: {
    [key: string]: number; // id: distance
  }
  forRecommendation?: boolean,
}): Promise<ActionResponse<UserLambdaRecordProps[]>> {

  if (!distanceXId || Object.keys(distanceXId).length === 0) return {
    success: true,
    data: [],
  };

  let rawRes = await prisma.tllUserLambdaRecord.findMany({
    where: {
      id: {
        in: Object.keys(distanceXId),
      },
    },
    include: {
      priceChanges: {
        include: {
          company: true,
        },
      },
      nearestStationGroup: true,
      propertyAnalysisResult: true,
    },
    omit: {
      analysisSimulationConfig: true,
      analysisSimulationResults: true,
    },
  });



  if (forRecommendation) {
    let filtered = rawRes
      .filter((r: any) => r.propertyAnalysisResult?.overallStarLevel > 0 && r.createdAt > dayjs().subtract(1, 'month').toDate())
      .sort((a: any, b: any) => {
        const aScore = a.propertyAnalysisResult?.overallStarLevel || 0;
        const bScore = b.propertyAnalysisResult?.overallStarLevel || 0;
        return bScore - aScore; // 降序排列,分数高的在前面
      });
    return {
      success: true,
      data: (await padWithLink({ records: filtered.slice(0, 6) as UserLambdaRecordProps[] })).data,
    };
  }

  let res = rawRes.map((r) => ({
    ...r,
    distance: distanceXId[r.id],
  })).sort((a, b) => a.distance - b.distance);

  return {
    success: true,
    data: res,
  };
};