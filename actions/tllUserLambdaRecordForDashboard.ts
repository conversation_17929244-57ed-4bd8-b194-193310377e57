"use server";

import { prisma } from '@/lib/prisma';
import dayjsWithTz from '@/lib/thirdParty/dayjsWithTz';
import { ActionResponse } from '@/lib/definitions';
import { logger } from '@/lib/logger';
import dayjs from '@/lib/thirdParty/dayjsWithTz';
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

// 获取所有用户记录
export async function getUserLambdaRecordForAdminDashboard(data: any): Promise<ActionResponse<UserLambdaRecordProps[]>> {
  let whereParams = {};

  if (Object.keys(data).length === 0) {
    return {
      success: false,
      message: "No data provided, must get a filter",
    }
  }

  if (data) {
    const { dateOfRecord } = data;
    const startOfDay = dayjs(dateOfRecord).startOf('day').toDate();
    const endOfDay = dayjs(dateOfRecord).endOf('day').toDate();

    // let allRecentlyUpdatedRecordIds: string[] = [];
    // if (dateOfRecord) {
    //   const startOfDay = dayjs(dateOfRecord).endOf('day').toDate();
    //   // const endOfDay = dayjs(dateOfRecord).endOf('day').toDate();
    //   console.log(`startOfDay: ${startOfDay}`);

    //   // FIXME: this does not work on local... as it will get you the day before data (as it is using JST in BE)// 
    //   let recentPriceChanges =
    //     await prisma.tllUserLambdaRecordPriceChange.findMany({
    //       where: {
    //         recordDate: startOfDay,
    //       },
    //     });

    //   allRecentlyUpdatedRecordIds = recentPriceChanges.map(
    //     (v: any) => v.recordId
    //   ).filter((v: any) => v !== null);
    // }
    // if (allRecentlyUpdatedRecordIds.length === 0) {
    //   return {
    //     success: true,
    //     data: [],
    //   }
    // }
    // this should be consistent with the notification.ts
    whereParams = {
      OR: [
        { recordType: "BUILDING" },
        { prefectureCode: 13 },
      ],
      price: {
        gte: 3000,
        lte: 100000,
      },
      recordType: {
        not: "MANSION"
      },
      // id: { in: allRecentlyUpdatedRecordIds }
      updatedAt: {
        gte: startOfDay,
        lte: endOfDay,
      },
    }
  }


  if (Object.keys(whereParams).length === 0) {
    return {
      success: true,
      data: [],
    }
  }

  console.log(`whereParams: ${JSON.stringify(whereParams)}`);

  const records = await prisma.tllUserLambdaRecord.findMany({
    where: {
      ...whereParams,
      // FIXME: might be outdated
    },
    include: {
      // FIXME:foreigner key 
      // userLambda: true,
      priceChanges: {
        include: {
          company: true,
        },
      },
      bids: {
        select: {
          id: true, // 只选择 bids 的 id
        },
      },
      nearestStationGroup: true,
      propertyAnalysisResult: true,
    },
  });

  return {
    success: true,
    data: records,
  };
}