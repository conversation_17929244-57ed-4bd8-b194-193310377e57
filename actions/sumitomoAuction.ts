"use server"

import { prisma } from "@/lib/prisma";
import { SumitomoAuctionProps, TllUserLambdaRecordPriceChangeSourceType } from "@/lib/definitions";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { ActionResponse } from "@/lib/definitions";
import { getLatAndLngFromAddress } from "@/lib/thirdParty/google";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz"
import pdf from "pdf-parse";
import { logger } from "@/lib/logger";
import { updateLatLngFromGoogleResult } from "./geoPostalCodes";
import { auctionTypeMapper } from "@/app/api/cron/sumitomoAuction/utility/auctionTypeMapper";
import { unstable_cache } from 'next/cache';

// 🔥 Cached database query function
const getCachedSumitomoAuctions = unstable_cache(
  async (twoDaysAgo: string) => {
    return await prisma.proRawSumitomoAuction.findMany({
    where: {
      OR: [
        {
          createdAt: {
            gte: twoDaysAgo,
          },
        },
        {
          isFav: 1,
        },
      ],
    },
    include: {
      lambdaRecord: {
        include: {
          priceChanges: {
            select: {
              price: true,
              recordDate: true,
            },
            orderBy: {
              recordDate: 'desc'
            },
            take: 5 // 🔥 Limit price changes to last 5
          },
          bids: {
            select: {
              id: true,
              biddingPrice: true,
              createdAt: true,
            },
            take: 3 // 🔥 Limit bids to last 3
          },
          propertyAnalysisResult: {
            select: {
              overallStarLevel: true,
              analysisDate: true,
            }
          },
        }
      } as any,
      building: {
        select: {
          id: true,
          tllUserLambdaRecords: {
            select: {
              price: true,
              buildingSize: true,
              recordValues: true,
            },
            take: 10 // 🔥 Limit related records
          },
          mansionRents: {
            select: {
              feeRent: true,
              feeManagement: true,
              unitSize: true,
            },
            take: 5 // 🔥 Limit rent records
          },
        }
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
    take: 1000, // 🔥 Limit total records
  }) as SumitomoAuctionProps[];
  },
  ['sumitomo-auctions'], // 🔥 Cache key
  {
    revalidate: 300, // 🔥 Cache for 5 minutes
    tags: ['sumitomo-auctions']
  }
);

export async function getSumitomoAuctionAction(): Promise<ActionResponse<SumitomoAuctionProps[]>> {
  const twoDaysAgo = dayjsWithTz().subtract(2, 'day').toISOString(); // 🔥 Changed from 14 days to 2 days

  // 🔥 Use cached query
  const sumitomoAuctions = await getCachedSumitomoAuctions(twoDaysAgo);

  logger.debug("🔥 sumitomoAuctions loaded from cache", sumitomoAuctions.length);

  return {
    success: true,
    data: sumitomoAuctions,
  };
}

export async function getSumitomoAuctionForSearchAction({ nearestStation, recordType, postalCode, buildingId }: { nearestStation?: string, recordType: string, postalCode?: string, buildingId?: string }): Promise<ActionResponse<SumitomoAuctionProps[]>> {
  try {
    let whereFilter = {
      ...(nearestStation && { nearestStation }),
      ...(postalCode && { postalCode: postalCode }),
      ...(buildingId && { buildingId }),
      // recordType: recordType as TllUserLambdaRecordRecordType,
    } as any;

    logger.debug("🔥 whereFilter for sumitomo", whereFilter);

    if (Object.keys(whereFilter).length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    const sumitomoAuctions = await prisma.proRawSumitomoAuction.findMany({
      where: whereFilter,
      orderBy: {
        createdAt: 'desc',
      },
    });

    return {
      success: true,
      data: sumitomoAuctions,
    };
  } catch (error) {
    logger.error("Error getting sumitomo auctions for search", error);
    return {
      success: false,
      data: [],
    };
  }
}

export async function getSumitomoAuctionByIdAction(id: string): Promise<ActionResponse<SumitomoAuctionProps>> {
  const sumitomoAuction = await prisma.proRawSumitomoAuction.findUnique({
    where: {
      id,
    },
    include: {
      lambdaRecord: {
        include: {
          propertyAnalysisResult: true,
        },
      },
    },
  });

  return {
    success: true,
    data: sumitomoAuction,
  };
}

export async function getSumitomoAuctionWithKeywords(): Promise<ActionResponse<SumitomoAuctionProps>> {
  try {
    const sumitomoAuctions = await prisma.proRawSumitomoAuction.findMany({
      where: {
        commentsKeywordsCount: {
          gt: 0,
        },
        bidEndDate: {
          gte: dayjsWithTz().subtract(12, 'month').toISOString(),
        },
      },
      include: {
        lambdaRecord: {
          select: {
            id: true,
            propertyAnalysisResult: {
              select: {
                overallStarLevel: true,
              },
            },
          },
        },
      },
      orderBy: [
        {
          bidEndDate: 'desc',
        },
      ],
    });

    return {
      success: true,
      data: sumitomoAuctions,
    };
  } catch (error) {
    logger.error("Error getting sumitomo auction with links", error);
    return {
      success: false,
      data: null,
    };
  }
}

// 新增更新拍卖记录的函数，只允许更新 isFav 字段
export async function updateSumitomoAuctionAction({ id, data }: { id: string, data: any }): Promise<ActionResponse<SumitomoAuctionProps>> {
  const updatedAuction = await prisma.proRawSumitomoAuction.update({
    where: { id },
    data,
  });

  return {
    success: true,
    data: updatedAuction,
  };
}

export async function updateNewlyAddedDataAction(id?: string): Promise<ActionResponse<SumitomoAuctionProps>> {
  let recordsMatched = 0;

  let whereFilter = {} as any;

  if (id) {
    whereFilter.id = id;
  } else {
    whereFilter = {
      createdAt: {
        gte: dayjsWithTz().tz('Asia/Tokyo').subtract(2, "day").toDate(),
      },
      lambdaRecordId: null,
    }
  }

  const recentlyAddedAuctionRecords =
    await prisma.proRawSumitomoAuction.findMany({
      where: whereFilter,
    });

  let index = 1;
  for (let r of recentlyAddedAuctionRecords) {
    // ------------------------------
    /// 1 - Check to see if there is 相続 or 債務弁済
    // ------------------------------
    let materialLinks = r.materialLinks as Record<string, string>;
    if (!materialLinks) {
      continue;
    }

    let keys = Object.keys(materialLinks);
    let matchedKey = null;

    for (let k of keys) {
      // All new should have qna however some might be old dated
      if (k.indexOf("qna") > -1 || k.indexOf("Q&A") > -1 || k.indexOf("Q＆A") > -1) {
        matchedKey = k;
      }
    }

    if (matchedKey !== null) {
      let matchedUrl = materialLinks[matchedKey];
      // Fetch the PDF from the public S3 URL
      const response = await fetch(matchedUrl);

      try {
        // 🔹 Convert response to Buffer
        const buffer = Buffer.from(await response.arrayBuffer());

        // 🔹 Extract text from the PDF
        const data = await pdf(buffer);

        // Check if the keyword exists in the text
        let keywords = ["債務", "返済", "相続", "離婚", "施設", "月内"];

        const foundKeywords = new Set(); // 使用 Set 来存储已找到的关键词
        for await (let keyword of keywords) {
          const hasKeyword = data.text.includes(keyword);
          if (hasKeyword) {
            foundKeywords.add(keyword); // 只记录找到的关键词
            // logger.info(`The keyword "${keyword}" was found in the PDF.`);
          }
        }

        // 只在找到关键词且不在 r.comments 中时更新一次
        if (foundKeywords.size > 0 && r.comments) {
          const existingKeywords = new Set(r.comments.match(/\[(.*?)\]/g)?.map(k => k.replace(/\[|\]/g, '')) || []);
          const newKeywords = Array.from(foundKeywords).filter(k => !existingKeywords.has(k as string));
          if (newKeywords.length > 0) {
            const newComments = newKeywords.map(k => `[${k}]`).join('') + r.comments;
            await prisma.proRawSumitomoAuction.update({
              where: { id: r.id },
              data: {
                comments: newComments,
                commentsKeywordsCount: newKeywords.length,
              },
            });
          }
        }
      } catch (error) {
        logger.error("Error reading the PDF from the public S3 URL:", error);
      }
    }

    // ------------------------------
    /// 2 - Update match id
    // ------------------------------
    let dataToUpdate = {} as {
      lambdaRecordId?: string;
      postalCode?: string;
      latitude?: number;
      longitude?: number;
      price?: number;
    };
    logger.info(
      `🔥 [Sumifu Keiabi] Updating... ${index++}/${recentlyAddedAuctionRecords.length}, id is ${r.id
      }`
    );

    // 1.1 Fill Postal Id if not exist
    let postalCode = r.postalCode;
    if (postalCode === null && r.address) {
      let matchedAddress = await prisma.geoAddress.findFirst({
        where: {
          address: r.address,
        },
      });

      console.log("🔥 matchedAddress", matchedAddress);

      if (matchedAddress) {
        postalCode = matchedAddress.pcode.toString();
      } else {
        // logger.info("🔥 getting latitude and longitude from address as postal code is null", r.address);
        // Will not be accurate but roughly should be fine 
        let res = await getLatAndLngFromAddress({ address: r.address });

        if (res && res.length) {
          let addressFull = res[res.length - 1].formatted_address; // '日本、〒153-0063 東京都目黒区目黒１丁目２−９ シティハウス目黒',
          let postalCodeMatch = addressFull?.match(/\d{3}-\d{4}/g);
          if (postalCodeMatch?.length) {
            postalCode = postalCodeMatch[0].replace("-", "");
          }

          dataToUpdate["latitude"] = res[res.length - 1].geometry.location.lat;
          dataToUpdate["longitude"] = res[res.length - 1].geometry.location.lng;
          await updateLatLngFromGoogleResult(res);
        }

        if (postalCode) {
          dataToUpdate["postalCode"] = postalCode.toString(); // this is a number
        }
      }
    }
    // 1.2 Get whereFilter
    let dataToMatch = {
      ...r,
      postalCode,
    } as any;

    // console.log("🔥 dataToMatch", dataToMatch);
    let match = await findMatchedLambdaRecord(dataToMatch);

    if (match.length) {
      dataToUpdate["lambdaRecordId"] = match[0].id;
      if (match[0].price !== r.price && r.price !== null) {
        dataToUpdate["price"] = r.price;
      }
      recordsMatched += 1;
    }

    if (Object.keys(dataToUpdate).length > 0) {
      await matchAndSaveRecord(r, dataToUpdate);
    }

    console.log("🔥 r.recordType", r.recordType);
    if (r.recordType === UserLambdaRecordType.MANSION) {
      await matchAndSaveBuilding(r);
    }
  }

  return {
    success: true,
    data: {
      recordsMatched,
    },
  };
}

async function findMatchedLambdaRecord(r: any): Promise<any[]> {
  let whereFilter = {
    // postalCode: r.postalCode,
    price: r.price,
  } as any;

  // Do not match anything if landPrice is not the same
  // There are bugs where price down and become same as another 
  if (!r["landSize"]) {
    return [];
  }

  ["landSize", "nearestStation", "postalCode", "buildingSize"].forEach((i) => {
    if (r[i] !== undefined && r[i] !== null && r[i] !== "") {
      whereFilter[i] = r[i];
    }
  });

  // Sometimes postal code is null, then check address
  // Do not check address by default, because 有时候我们吧地址改成了详细地址


  whereFilter["recordType"] = auctionTypeMapper[r.type as keyof typeof auctionTypeMapper] || UserLambdaRecordType.BUILDING;


  if (
    // whereFilter.postalCode === undefined &
    r.address !== undefined &&
    r.address !== null
  ) {
    whereFilter["address"] = r.address;
    delete whereFilter.postalCode;
  } else if (whereFilter.postalCode !== undefined) {
    whereFilter.postalCode = parseInt(whereFilter.postalCode);
  }

  // edge case, if building and land both existing, can ignore price
  if (whereFilter.buildingSize !== undefined && whereFilter.landSize !== undefined) {
    delete whereFilter.price;
    delete whereFilter.nearestStation;
  }

  console.log("🔥 whereFilter", whereFilter);

  return await prisma.tllUserLambdaRecord.findMany({
    where: whereFilter,
  });
}

const matchAndSaveRecord = async (rawActionRecord: any, dataToUpdate: {
  lambdaRecordId?: string;
  postalCode?: string;
  price?: number;
}) => {
  await prisma.proRawSumitomoAuction.update({
    where: { id: rawActionRecord.id },
    data: dataToUpdate,
  });

  if (dataToUpdate.lambdaRecordId) {
    await prisma.tllUserLambdaRecordPriceChange.create({
      data: {
        recordId: dataToUpdate.lambdaRecordId as string,
        recordDate: dayjsWithTz(rawActionRecord.infoStartDate).toDate(),
        source: TllUserLambdaRecordPriceChangeSourceType.KEIBAI_SUMITOMO,
        status: "公開中",
        price: rawActionRecord.price,
        yearlyIncome: rawActionRecord.yearlyIncome,
        chirashiLink: JSON.stringify(rawActionRecord.materialLinks),
        comments: `[ID: ${rawActionRecord.id}] [comments: ${rawActionRecord.comments}][担当名: ${rawActionRecord.pocName} | ${rawActionRecord.pocNumber} | ${rawActionRecord.pocEmail}]`, // fixme: this should be removed
      }
    });

    await prisma.tllUserLambdaRecord.update({
      where: { id: dataToUpdate.lambdaRecordId },
      data: {
        updatedAt: new Date(),
      },
    });
  }
}

export const saveRecordDirectly = async (rawActionRecord: any, matchedLambdaRecordId: string): Promise<ActionResponse<SumitomoAuctionProps>> => {
  let matchedLambdaRecord = await prisma.tllUserLambdaRecord.findUnique({
    where: { id: matchedLambdaRecordId },
  });

  if (!matchedLambdaRecord) {
    return {
      success: false,
      data: null,
    };
  }

  await prisma.proRawSumitomoAuction.update({
    where: { id: rawActionRecord.id },
    data: {
      lambdaRecordId: matchedLambdaRecordId,
    },
  });

  if (matchedLambdaRecordId) {
    await prisma.tllUserLambdaRecordPriceChange.create({
      data: {
        recordId: matchedLambdaRecordId as string,
        recordDate: dayjsWithTz(rawActionRecord.infoStartDate).toDate(),
        source: TllUserLambdaRecordPriceChangeSourceType.KEIBAI_SUMITOMO,
        status: "公開中",
        price: rawActionRecord.price,
        yearlyIncome: rawActionRecord.yearlyIncome,
        chirashiLink: JSON.stringify(rawActionRecord.materialLinks),
        comments: `[ID: ${rawActionRecord.id}] [comments: ${rawActionRecord.comments}][担当名: ${rawActionRecord.pocName} | ${rawActionRecord.pocNumber} | ${rawActionRecord.pocEmail}]`, // fixme: this should be removed
      }
    });

    await prisma.tllUserLambdaRecord.update({
      where: { id: matchedLambdaRecordId },
      data: {
        updatedAt: new Date(),
      },
    });
  }

  return {
    success: true,
    data: matchedLambdaRecord,
  };
}

export async function matchAndSaveBuilding(r: any) {
  let matchedBuilding = await prisma.proBuilding.findFirst({
    where: {
      nameJa: r.name,
    },
  });

  if (matchedBuilding && matchedBuilding.id) {
    await prisma.proRawSumitomoAuction.update({
      where: { id: r.id },
      data: { buildingId: matchedBuilding.id },
    });
  }

  logger.debug(`🔥 ${r.address} matching`, matchedBuilding ? "matched, id is " + matchedBuilding.id : "not matched");

  return {
    success: true,
    data: matchedBuilding,
  };
}