"use server"

import { z } from "zod"
import { ReferralCodeProps } from "@/lib/definitions/referralCode"
import { prisma } from "@/lib/prisma";
import { auth } from "@/lib/auth"
import { ActionResponse } from "@/lib/definitions";
import dayjs from "dayjs";
import { SystemUserActivityEventTypeEnum } from "@prisma/client";

function generateReferralCode(length = 8): string {
  const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // 去除易混淆字符
  let code = '';
  for (let i = 0; i < length; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return code;
}


export async function createReferralCode(): Promise<ActionResponse<ReferralCodeProps>> {

  try {
    const session = await auth()
    const currentUser = session?.user

    if (!currentUser || !currentUser?.id) {
      return {
        success: false,
        message: "ユーザーが見つかりません",
      };
    }

    // set all active code to inactive
    await prisma.referralCode.updateMany({
      where: {
        createdByUserId: currentUser.id,
        isActive: true,
      },
      data: {
        isActive: false,
      },
    });

    const code = generateReferralCode();

    const expiresAt = new Date();
    expiresAt.setMonth(expiresAt.getMonth() + 1); // 有效期1个月

    const res = await prisma.referralCode.create({
      data: {
        code,
        createdByUserId: currentUser.id,
        expiresAt,
      },
    });

    return {
      success: true,
      message: "紹介コードが作成されました",
      data: res,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "紹介コードの作成に失敗しました",
    };
  }
}

export async function getReferralCode(id: string): Promise<ActionResponse<ReferralCodeProps>> {
  try {
    const res = await prisma.referralCode.findUnique({
      where: { id },
      include: {
        referredUsers: {
          include: {
            systemUserActivity: {
              where: {
                eventType: "BUTTON_CLICK",
                route: "/ex/search"
              },
            },
          },
        },
      },
    });

    return {
      success: true,
      message: "紹介コードが取得されました",
      data: res,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "紹介コードの取得に失敗しました",
    };
  }
}

export async function getMyReferralCode(): Promise<ActionResponse<ReferralCodeProps[]>> {

  try {
    const session = await auth()
    const currentUser = session?.user

    if (!currentUser || !currentUser?.id) {
      return {
        success: false,
        message: "ユーザーが見つかりません",
      };
    }

    const res = await prisma.referralCode.findMany({
      where: {
        createdByUserId: currentUser.id,
      },
      include: {
        referredUsers: true,
      },
    });

    return {
      success: true,
      message: "紹介コードが取得されました",
      data: res,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "紹介コードの取得に失敗しました",
    };
  }
}

export async function updateReferralCode(id: string, data: {
  isActive: boolean,
}): Promise<ActionResponse<ReferralCodeProps>> {
  try {
    const res = await prisma.referralCode.update({
      where: { id },
      data: {
        isActive: data.isActive,
      }
    });

    return {
      success: true,
      message: "紹介コードが更新されました",
      data: res,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "紹介コードの更新に失敗しました",
    };
  }
}

export async function useReferralCode(code: string): Promise<ActionResponse<ReferralCodeProps>> {
  try {
    const session = await auth()
    const currentUser = session?.user

    if (!currentUser || !currentUser?.id) {
      return {
        success: false,
        message: "ユーザーが見つかりません",
      };
    }

    const res = await prisma.referralCode.findUnique({
      where: {
        code,
        isActive: true,
        expiresAt: {
          gt: new Date(),
        },
      },
    });

    if (!res) {
      return {
        success: false,
        message: "紹介コードが有効ではありません",
      };
    }

    let currentUserFull = await prisma.tllUser.findUnique({
      where: { id: currentUser.id },
    });

    if (currentUserFull?.referralCodeId) {
      return {
        success: false,
        message: "すでに紹介コードが利用されています",
      };
    }

    if (currentUserFull?.accessLevel && currentUserFull?.accessLevel >= 10) {
      return {
        success: false,
        message: "すでにPlus以上のアクセスレベルのユーザーは紹介コードを利用できません",
      };
    }

    await prisma.tllUser.update({
      where: { id: currentUser.id },
      data: {
        referralCodeId: res.id,
        accessLevel: 10,
        referralCodeExpiresAt: dayjs().add(1, "month").toDate(), // default will be one month from now.. 
      },
    });

    return {
      success: true,
      message: "紹介コードが利用されました",
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: "紹介コードの利用に失敗しました",
    };
  }
}