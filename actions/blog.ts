"use server"

import { prisma } from "@/lib/prisma";
import { ActionResponse, BlogProps } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { headers } from "next/headers";
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import openaiInstance from "@/lib/instances/openai";

function capitalizeFirstLetter(str: string) {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export async function incrementBlogViewCount(blogId: string, lang: string): Promise<ActionResponse<BlogProps>> {
  try {
    if (blogId.indexOf("drafts") === 0) {
      return { success: true, message: "Skipped draft" };
    }

    // 🧠 获取 Referer 头部
    const referer = (await headers()).get("referer") || "";
    const isLocalhost = referer.includes("localhost") || referer.includes("127.0.0.1");

    if (isLocalhost) {
      return { success: true, message: "Skipped localhost" };
    }

    const viewField = `view${capitalizeFirstLetter(lang)}` as keyof BlogProps;

    let blog = await prisma.blog.findUnique({ where: { id: blogId } });

    if (!blog) {
      // 创建新记录，初始化所有 view 字段为 0，再设置当前语言为 1
      blog = await prisma.blog.create({
        data: {
          id: blogId,
          [viewField]: 1, // 初始化当前语言为 1
        } as any, // 如果 TS 报错，你可以用 `as any` 或构建完整数据结构
      });
    } else {
      // 更新当前语言字段
      await prisma.blog.update({
        where: { id: blogId },
        data: {
          [viewField]: { increment: 1 },
        },
      });
    }

    return {
      success: true,
      data: blog,
    };
  } catch (error) {
    logger.error(error);
    return { success: false, message: "Failed to update blog view" };
  }
}

export async function getBlogs(filter: { creatorUserId?: string, buildingId?: string, postalCodeId?: string, stationGroupId?: string, areaCode?: number } | null): Promise<ActionResponse<BlogProps[]>> {
  try {
    // 🔥 Use executeWithRetry to handle connection issues
    const { executeWithRetry } = await import('@/lib/prismaUtils');

    const blogs = await executeWithRetry(async () => {
      return await prisma.blog.findMany({
        ...(filter ? { where: filter } : {}),
        include: {
          creatorUser: true,
          building: true,
          postalCode: true,
          stationGroup: true,
          area: true,
        },
      });
    });

    return { success: true, data: blogs };
  } catch (error) {
    logger.error('🔥 Error in getBlogs:', error);
    return { success: false, message: "Failed to get blogs" };
  }
}

export async function getBlog(blogId: string): Promise<ActionResponse<BlogProps>> {
  try {
    // 🔥 Use executeWithRetry to handle connection issues
    const { executeWithRetry } = await import('@/lib/prismaUtils');

    const blog = await executeWithRetry(async () => {
      return await prisma.blog.findUnique({
        where: { id: blogId },
        include: {
          building: true,
          postalCode: true,
          stationGroup: true,
          area: true,
        }
      });
    });

    return { success: true, data: blog };
  } catch (error) {
    logger.error('🔥 Error in getBlog:', error);
    return { success: false, message: "Failed to get blog" };
  }
}

export async function updateBlog({ blogId, creatorUserId, buildingId, postalCodeId, stationGroupId, areaCode }: { blogId: string, creatorUserId: string, buildingId?: string | null, postalCodeId?: string | null, stationGroupId?: string | null, areaCode?: number | null }): Promise<ActionResponse<BlogProps>> {
  try {
    let data = {
      creatorUserId,
      ...(buildingId !== undefined ? { buildingId } : {}),
      ...(postalCodeId !== undefined ? { postalCodeId } : {}),
      ...(stationGroupId !== undefined ? { stationGroupId } : {}),
      ...(areaCode !== undefined ? { areaCode } : {}),
    } as any;

    console.log("🔥 data", data);

    const blog = await prisma.blog.update({
      where: { id: blogId },
      data: data as any
    });
    return { success: true, data: blog };
  } catch (error) {
    logger.error(error);
    return { success: false, message: "Failed to update blog" };
  }
}


async function translateTextOpenAI(text: string, targetLang: "en" | "ja") {
  const langName = targetLang === "en" ? "English" : "Japanese";

  const prompt = `You are a translation engine. Translate the text below into ${langName}. 
Return ONLY the translated result. Do not include any preamble, commentary, explanation, or formatting.
Do not say anything like "Here is the translation" or "Certainly". Just return the raw translated content.

Text:
${text}
`;

  const chatCompletion = await openaiInstance.chat.completions.create({
    model: "gpt-4o",
    messages: [{ role: "user", content: prompt }],
  });

  return chatCompletion.choices[0].message.content?.trim() || "";
}


function extractTranslatableBlocks(body: any[]) {
  return body.filter(block => block._type === "block" && Array.isArray(block.children));
}

async function translateBodyBlocks(body: any[], lang: "en" | "ja") {
  const translatableBlocks = extractTranslatableBlocks(body);
  const translations = await Promise.all(
    translatableBlocks.map(block =>
      translateTextOpenAI(
        block.children.map((child: any) => child.text).join(""),
        lang
      )
    )
  );

  let i = 0;
  return body.map(block => {
    if (block._type !== "block" || !Array.isArray(block.children)) return block;
    return {
      ...block,
      children: [
        {
          _type: "span",
          marks: [],
          text: translations[i++] ?? "",
        },
      ],
    };
  });
}

function generateSlug(title: string, lang: "en" | "zh" | "ja"): string {
  let res = title;
  if (lang === "ja" || lang === "zh") {
    res = title
      .trim()
      .replace(/[^\p{L}\p{N}]+/gu, "") // 仅移除非文字和非数字字符
  }

  res = title
    .toLowerCase()
    .replace(/[’'"]/g, "")           // 移除撇号、引号
    .replace(/[^a-z0-9\s-]/g, "")    // 移除其他特殊字符
    .trim()
    .replace(/\s+/g, "-");           // 空格转 -

  return res.replaceAll('"', "");
}

export async function translateBlog({ blogId, targetLang }: { blogId: string, targetLang: "en" | "ja" }): Promise<ActionResponse<any>> {
  try {
    const post = await sanityClient.fetch(`
      *[_type == "post" && _id == $blogId][0]{
        _id, title_zh, body_zh, title_en, body_en, title_ja, body_ja
      }
    `, { blogId });

    if (!post) {
      return { success: false, message: "Blog not found" };
    }

    const updatedIds: string[] = [];
    const patch: Record<string, any> = {};

    logger.info('🔥 processing post, id is ', post._id, "titles are ", post.title_zh, post.title_en, post.title_ja);

    if (post.title_zh) {
      if (!post[`title_${targetLang}`]) {
        logger.info('🔥 translate post for en title', post._id);
        patch[`title_${targetLang}`] = await translateTextOpenAI(post.title_zh, targetLang);
        patch[`slug_${targetLang}`] = generateSlug(patch[`title_${targetLang}`], targetLang);
      }
    }

    if (post.body_zh) {
      if (!post[`body_${targetLang}`]) {
        logger.info('🔥 translate post for en body', post._id);
        patch[`body_${targetLang}`] = await translateBodyBlocks(post.body_zh, targetLang);
      }
    }

    if (Object.keys(patch).length === 0) {
      return { success: false, message: "No changes to update" };
    }

    await sanityClient.patch(post._id).set(patch).commit();
    updatedIds.push(post._id);

    return { success: true, data: updatedIds };
  } catch (error) {
    logger.error('🔥 Error translating blog:', error);
    return { success: false, message: "Failed to translate blog" };
  }
}
