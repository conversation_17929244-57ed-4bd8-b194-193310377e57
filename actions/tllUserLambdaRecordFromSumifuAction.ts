"use server";

import { getCompositeTitle } from "@/app/api/cron/reins/utility/getCompositeTitle";
import { fillRecordAnalysis } from "@/app/api/cron/reinsFill/recordAnalysis/fillRecordAnalysis";
import { fillPrefectureAreaPostalCode } from "./geoPrefecture";
import { auth } from "@/lib/auth";
import { UserLambdaRecordCreateBuildingHouseSchema, UserLambdaRecordCreateLandSchema } from "@/lib/definitions";
import { UserLambdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { UserLambdaRecordProps, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { ActionResponse } from "@/lib/definitions";
import { SumitomoAuctionProps } from "@/lib/definitions";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import dayjs from "dayjs";
import { fillNearestStationGroupIdWalkMinute } from "./geoRailwayStationGroups";

import { updateSumitomoAuctionAction } from "./sumitomoAuction";
import { TllUserLambdaRecordPriceChangeSourceType, TllUserLambdaRecordRecordType } from "@prisma/client";

export async function createUserLambdaRecordFromSumitomoAuctionAction(data: SumitomoAuctionProps): Promise<ActionResponse<UserLambdaRecordProps>> {
  const session = await auth(); // Fetch authenticated session
  const userId = session?.user?.id; // 从 next-auth 获取 userId

  if (!userId) {
    return {
      success: false,
      message: "ユーザーはログインしていません", // 使用日语
    };
  }

  let dataParsed = (data.recordType === UserLambdaRecordType.BUILDING || data.recordType === UserLambdaRecordType.HOUSE) ? UserLambdaRecordCreateBuildingHouseSchema.parse(data) : UserLambdaRecordCreateLandSchema.parse(data);

  let newRecord: UserLambdaRecordProps;
  try {
    if (data.recordType === undefined || data.recordType === null) {
      return {
        success: false,
        message: "住友競売の物件はビルやハウスではありません",
      };
    }

    // FIXME: note this wont work for mansion
    let compositeTitle = getCompositeTitle(data, data.recordType, "SUMIFU");

    let record = await prisma.tllUserLambdaRecord.findUnique({
      where: {
        compositeTitle_recordType: {
          compositeTitle: compositeTitle,
          recordType: data.recordType as TllUserLambdaRecordRecordType,
        },
      },
    });

    if (record) {
      return {
        success: false,
        message: "この物件はすでに存在します, id is " + record.id,
      };
    }

    newRecord = await prisma.tllUserLambdaRecord.create({
      data: {
        ...dataParsed,
        sourceData: "住友競売",
        compositeTitle: compositeTitle,
        salesComments: `状況: ${data.propertyStatus || "不明"} | pocName: ${data.pocName} /  ${data.pocEmail} / ${data.pocNumber} || hp: ${data.hpUrl} || auctionUrl: ${data.auctionUrl}`,
      }
    }) as UserLambdaRecordProps;
    logger.debug("🔥 newRecord created, id is ", newRecord.id);

    let newPriceChange = await prisma.tllUserLambdaRecordPriceChange.create({
      data: {
        recordId: newRecord.id,
        recordDate: dayjs(data.infoStartDate).toDate(),
        source: TllUserLambdaRecordPriceChangeSourceType.KEIBAI_SUMITOMO,
        status: "公開中",
        price: data.price,
        yearlyIncome: data.yearlyIncome,
        chirashiLink: JSON.stringify(data.materialLinks), // fixme: this should be removed
        comments: `[ID: ${data.id}] [comments: ${data.comments}][担当名: ${data.pocName} | ${data.pocNumber} | ${data.pocEmail}]`, // fixme: this should be removed
      }
    }) as UserLambdaRecordPriceChangeProps;
    logger.debug("🔥 tllUserLambdaRecordPriceChange created, id is ", newPriceChange.id);

    await updateSumitomoAuctionAction({
      id: data.id as string,
      data: {
        lambdaRecordId: newRecord.id,
      }
    });

    await fillPrefectureAreaPostalCode(newRecord.id);
    await fillNearestStationGroupIdWalkMinute(newRecord.id);
    await fillRecordAnalysis({
      recordId: newRecord.id,
    });

    // logger.debug("🔥 sumitomoAuction with id ", data.id, " updated, lambdaRecordId is ", newRecord.id);

    return {
      success: true,
      data: newRecord,
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to create user lambda record from sumitomo auction",
    };
  }
}

export async function getListOfAgentUserAction(): Promise<ActionResponse<UserLambdaRecordProps>> {
  const agentUsers = await prisma.tllUser.findMany({
    where: {
      accessLevel: {
        gte: 30,
      },
    },
    orderBy: {
      name: "asc",
    },
    select: {
      id: true,
      name: true,
      email: true
    },
  });

  return {
    success: true,
    data: agentUsers,
  };
}

export async function getListOfTllAgentUserAction(): Promise<ActionResponse<UserLambdaRecordProps>> {
  const agentUsers = await prisma.tllUser.findMany({
    where: {
      accessLevel: {
        gte: 30,
      },
    },
    orderBy: {
      name: "asc",
    },
    select: {
      id: true,
      name: true,
      email: true
    },
  });

  return {
    success: true,
    data: agentUsers,
  };
}