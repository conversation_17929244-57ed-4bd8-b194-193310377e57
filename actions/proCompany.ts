"use server"

import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";
import { ProCompanyProps } from "@/lib/definitions/proCompany";

export const getFavCompaniesAction = async (): Promise<ActionResponse<ProCompanyProps[]>> => {
  const companies = await prisma.proCompany.findMany({
    where: {
      isFav: 1,
    },
    orderBy: {
      fullName: "asc",
    },
  });

  return {
    success: true,
    data: companies,
  };
}

export const updateCompanyAction = async (id: string, data: { comments: string, isFav: number }): Promise<ActionResponse<ProCompanyProps>> => {
  try {
    const company = await prisma.proCompany.findUnique({
      where: {
        id: id,
      },
    });

    if (!company) {
      return {
        success: false,
        message: "企業が見つかりません",
      };
    }

    const updatedCompany = await prisma.proCompany.update({
      where: {
        id: id,
      },
      data: {
        comments: data.comments,
        isFav: data.isFav,
      },
    });

    return {
      success: true,
      message: "企業のお気に入りを切り替えました",
      data: updatedCompany,
    };
  } catch (error) {
    return {
      success: false,
      message: "企業のお気に入りを切り替えに失敗しました",
    };
  }
}

export const fuzzySearchCompaniesAction = async (search: string): Promise<ActionResponse<ProCompanyProps[]>> => {
  if (!search) {
    return {
      success: false,
      message: "検索文字を入力してください",
    };
  }

  try {
    const companies = await prisma.proCompany.findMany({
      where: {
        fullName: { contains: search, mode: "insensitive" },
      },
      orderBy: {
        isFav: "asc",
      },
    });

    return {
      success: true,
      data: companies,
    };
  } catch (error) {
    return {
      success: false,
      message: "検索に失敗しました",
    };
  }
}

export const getCompanyDetailsAction = async (id: string): Promise<ActionResponse<ProCompanyProps>> => {
  try {
    const company = await prisma.proCompany.findUnique({
      where: {
        id: id,
      },
      include: {
        priceChanges: {
          include: {
            tllUserLambdaRecord: {
              include: {
                propertyAnalysisResult: true,
              }
            },
          },
        },
      },
    });

    return {
      success: true,
      data: company,
    };
  } catch (error) {
    return {
      success: false,
      message: "業者情報の取得に失敗しました",
    };
  }
}