"use server";
import { auth } from "@/lib/auth";
import { ActionResponse } from "@/lib/definitions";
import { TllUserLambdaRecordSearchHistoryProps } from "@/lib/definitions";
import { prisma } from "@/lib/prisma";

export const getUserLambdaRecordSearchHistory = async ({
  dateFrom,
}: {
  dateFrom?: string;
}): Promise<ActionResponse<TllUserLambdaRecordSearchHistoryProps[]>> => {
  const session = await auth();

  if (!session?.user?.id) {
    return {
      success: false,
      message: "User not found",
    };
  }

  const searchHistory = await prisma.tllUserLambdaRecordSearchHistory.findMany({
    where: {
      userId: session?.user?.id,
      ...(dateFrom && { searchDate: { gte: dateFrom } }),
    },
    orderBy: {
      createdAt: "desc",
    },
    include: {
      tllUserLambdaRecord: {
        include: {
          propertyAnalysisResult: true,
          priceChanges: true,
        }
      }
      //  {
      //   include: {
      //     priceChanges: true,
      //   },
      // },
    },
  });

  return {
    success: true,
    data: searchHistory,
  };
};

export const getIfRecordIsViewedBefore = async ({ recordId }: { recordId: string }): Promise<ActionResponse<boolean>> => {
  const session = await auth();

  if (!session?.user?.id) {
    return {
      success: false,
      message: "User not found",
    };
  }

  const searchHistory = await prisma.tllUserLambdaRecordSearchHistory.findFirst({
    where: {
      userId: session?.user?.id,
      recordId: recordId,
    },
  });

  return {
    success: true,
    data: searchHistory ? true : false,
  };
};

export const getDistinctSearchHistoryCountPerUserPerDay = async (): Promise<
  ActionResponse<
    {
      userId: string;
      searchDate: string;
      count: number;
      access: string | null;
      subscriptionPlan: string | null;
    }[]
  >
> => {
  const grouped = await prisma.tllUserLambdaRecordSearchHistory.groupBy({
    by: ["userId", "searchDate"],
    _count: {
      id: true,
    },
    orderBy: [
      { userId: "asc" },
      { searchDate: "desc" },
    ],
  });

  // 🧠 获取所有涉及的 userIds
  const userIds = [...new Set(grouped.map((r) => r.userId))];

  const users = await prisma.tllUser.findMany({
    where: { id: { in: userIds } },
    select: {
      id: true,
      accessLevel: true,
      subscriptionPlan: true,
      email: true,
    },
  });

  // 🧬 用户信息映射表
  const userMap = Object.fromEntries(users.map((u) => [u.id, u]));

  // 🧩 组装最终结构
  const data = grouped.map((r) => ({
    userId: r.userId,
    dateOfRecord: r.searchDate.toISOString().slice(0, 10),
    count: r._count.id,
    accessLevel: userMap[r.userId]?.accessLevel ?? null,
    subscriptionPlan: userMap[r.userId]?.subscriptionPlan ?? null,
    email: userMap[r.userId]?.email ?? null,
  }));

  return {
    success: true,
    data,
  };
};