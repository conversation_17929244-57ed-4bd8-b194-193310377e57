{"name": "urbalytics", "version": "1.9.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:turbo": "next build --experimental-turbo", "build:fast": "SKIP_ENV_VALIDATION=true NEXT_TELEMETRY_DISABLED=1 next build", "build:optimize": "node scripts/build-optimize.js && pnpm run build:fast", "build:clean": "rm -rf .next && rm -f tsconfig.tsbuildinfo && pnpm run build", "type-check": "tsc --noEmit", "start": "next start", "lint": "next lint", "checklink": "pnpm exec tsc --noEmit --pretty"}, "dependencies": {"@aws-sdk/client-s3": "^3.740.0", "@googlemaps/google-maps-services-js": "^3.4.0", "@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.0.1", "@mendable/firecrawl-js": "^1.16.0", "@next/mdx": "^15.1.3", "@portabletext/react": "^3.2.1", "@prisma/client": "6.4.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.7", "@radix-ui/react-visually-hidden": "^1.1.2", "@react-email/components": "0.0.34", "@react-email/render": "^1.0.5", "@sanity/image-url": "^1.1.0", "@sentry/nextjs": "^9.3.0", "@sparticuz/chromium": "^132.0.0", "@supabase/supabase-js": "^2.48.1", "@tanstack/react-table": "^8.20.6", "@types/jspdf": "^2.0.0", "@types/mdx": "^2.0.13", "@vercel/analytics": "^1.5.0", "@vercel/postgres": "^0.10.0", "add": "^2.0.6", "bcrypt": "^5.1.1", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookies-next": "^5.1.0", "crypto": "^1.0.1", "cuid": "^3.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.5.0", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "import-in-the-middle": "^1.13.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lodash-es": "^4.17.21", "lucide-react": "^0.474.0", "mime-types": "^2.1.35", "motion": "^12.4.10", "mysql2": "^3.12.0", "next": "15.1.6", "next-auth": "5.0.0-beta.25", "next-intl": "^3.26.3", "next-pwa": "^5.6.0", "next-sanity": "^9.10.6", "next-sitemap": "^4.2.3", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "openai": "^4.82.0", "p-limit": "^6.2.0", "pdf-parse": "^1.1.1", "puppeteer": "^24.2.0", "puppeteer-core": "^24.2.0", "puppeteer-extra-plugin-recaptcha": "^3.6.8", "random-useragent": "^0.5.0", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-markdown": "^9.0.3", "react-pdf": "^9.2.1", "react-select": "^5.10.0", "recharts": "^2.15.1", "require-in-the-middle": "^7.5.2", "resend": "^4.2.0", "rimraf": "^6.0.1", "sharp": "^0.33.5", "simple-statistics": "^7.8.7", "stripe": "^17.7.0", "swr": "^2.3.0", "tailwind-merge": "^2.6.0", "typed.js": "^2.1.0", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "web-push": "^3.6.7", "wechaty": "^1.20.2", "xlsx": "^0.18.5", "yet-another-react-lightbox": "^3.23.2", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/upgrade": "^4.1.11", "@types/bcrypt": "^5.0.2", "@types/leaflet": "^1.9.17", "@types/lodash-es": "^4.17.12", "@types/mime-types": "^2.1.4", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/web-push": "^3.6.4", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "prettier": "^3.6.2", "prisma": "6.4.1", "tailwindcss": "^4.1.11", "typescript": "^5"}, "pnpm": {"overrides": {"prettier": "3.6.2"}}}