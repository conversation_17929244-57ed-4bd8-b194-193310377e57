'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { verifyToken, setUserPassword, authenticate } from '@/actions/auth';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { signIn } from 'next-auth/react';
import { TriangleAlert } from 'lucide-react';
import { createNewSystemUserActivityAction } from '@/actions/systemUserActivity';
import { useAuthStore } from '@/store/auth';
import { LARK_URLS, sendLark } from '@/lib/thirdParty/lark';

export default function ActivatePage() {
  const { setCurrentUser } = useAuthStore();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [verified, setVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();

  useEffect(() => {
    if (!token) return;

    (async () => {
      const res = await verifyToken(token);
      if (res.success) {
        setEmail(res.data.email);
        setVerified(true);
      } else {
        setError('このアクティベーションリンクは無効または期限切れです。');
      }
    })();

  }, [token]);

  async function signInUser() {
    createNewSystemUserActivityAction({
      data: {
        eventType: "LOGIN",
        route: "/login",
        eventMetadata: {
          loginType: "credentials",
          formValues: { email, password: undefined },
        },
      },
    });

    let res = await authenticate({ email, password } as any)
    console.log("🔥res loggin in ///", res);

    if (res.success) {
      fetch('/api/auth')
        .then(response => response.json())
        .then(data => {
          console.log("🔥data", data);
          setCurrentUser(data);
          router.push('/ex/search')
        })
        .catch(error => {
          console.error("获取用户失败:", error, "🔥");
        })
        .finally(() => {
          sendLark({
            message: `[🔐][ログイン]${JSON.stringify({ email, password })}, result is ${res.success}`,
            url: LARK_URLS.USER_AQUISITION_CHANNEL,
          });
        })
    } else {
      toast({
        title: "エラーが発生しました",
        description: res.message || '何かがうまくいきませんでした。',
      })
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    setIsLoading(true);
    e.preventDefault();

    if (!/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,}$/.test(password)) {
      setPasswordError("新しいパスワードが英数字の組み合わせで入力してください");
      setIsLoading(false);
      return
    }

    try {
      const res = await setUserPassword({ email, password });
      if (res.success) {
        toast({
          description: "アカウントが正常に作成されました。ログイン中...",
          duration: 2000,
        })

        await new Promise(resolve => setTimeout(resolve, 2000));
        await signInUser();
      } else {
        setError(res.message || '何かがうまくいきませんでした。');
      }
    } catch (error) {
      toast({
        title: "エラーが発生しました",
        description: error instanceof Error ? error.message : '不明なエラーが発生しました',
      })
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <div className="w-full max-w-sm">
        <div className="flex flex-col gap-6">
          {error ? <div className="text-red-500 text-lg p-4 rounded-md bg-red-50 flex items-center gap-2">
            <TriangleAlert className="w-10 h-10" />
            {error}</div> : (
            verified ? (
              <form onSubmit={handleSubmit} className="space-y-4">
                <p><strong>{email}</strong>のパスワードを設定してください</p>
                <p className="text-sm text-muted-foreground">新しいパスワードが英数字の組み合わせで入力してください</p>
                <Input type="password" value={password} onChange={(e) => setPassword(e.target.value)} required />
                <Button type="submit" disabled={isLoading}>{isLoading ? 'アカウントを作成中...' : 'アカウントを作成'}</Button>
                {passwordError && <p className="text-red-500 text-sm">{passwordError}</p>}
              </form>
            ) : (
              <p>トークンを確認中...</p>
            )
          )}
        </div>
      </div>
    </div>
  )
}