"use client";

import { Separator } from "@/components/ui/separator";
import { useParams } from "next/navigation";
import data from "../data.json";
import { useLocale } from "next-intl";

type GlossaryItem = {
  id: string;
  term_en?: string;
  term_zh?: string;
  term_ja?: string;
  definition_en?: string;
  definition_zh?: string;
  definition_ja?: string;
  term?: string;
  definition?: string;
};

export default function GlossaryItemPage() {
  const { slug } = useParams();
  const currentLocale = useLocale();

  const map = {
    en: "English",
    zh: "中文",
    ja: "日本語",
  };

  const currentItem = data.find((item: GlossaryItem) => item.id === slug);

  if (!currentItem) {
    return (
      <div className="pt-(--header-height) p-4">
        <div className="text-center text-neutral-500 tracking-widest mb-1 font-bold mt-8">
          GLOSSARY
        </div>
        <h2 className="text-center text-3xl tracking-wide">Term not found</h2>
      </div>
    );
  }

  const getTerm = (item: GlossaryItem, locale: string) => {
    return item[`term_${locale}` as keyof GlossaryItem] || item.term || "";
  };

  const getDefinition = (item: GlossaryItem, locale: string) => {
    return item[`definition_${locale}` as keyof GlossaryItem] || item.definition || "";
  };

  return (
    <div className="pt-(--header-height) p-4">
      <div className="text-center text-neutral-500 tracking-widest mb-1 font-bold mt-8">
        GLOSSARY
      </div>

      <h2 className="text-center text-3xl tracking-wide">
        {getTerm(currentItem, currentLocale)}
      </h2>

      <Separator className="my-8" />

      <section className="container mx-auto pb-12 pt-4 px-4 flex flex-col gap-6">
        {Object.keys(map).map((key) => (
          <div
            key={key}
            className={`flex flex-col gap-1 p-4 ${key === currentLocale ? "bg-neutral-100" : ""
              }`}
          >
            <div className="text-sm text-neutral-500">
              {map[key as keyof typeof map]}
            </div>
            <div className="text-lg text-neutral-700 font-bold">
              {getTerm(currentItem, key)}
            </div>
            <div className="text-sm text-neutral-700">
              {getDefinition(currentItem, key)}
            </div>
          </div>
        ))}
      </section>
    </div>
  );
}   