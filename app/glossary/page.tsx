"use client";

import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import data from "./data.json";
import Link from "next/link";
import { useLocale } from "next-intl";
import FooterForNonLogin from "@/components/footerForNonLogin";

export default function GlossaryPage() {
  const [search, setSearch] = useState("");
  const currentLocale = useLocale()

  return <div className="pt-(--header-height) p-4">
    <div className=" max-w-[1200px] mx-auto">
      <div className="text-center text-neutral-500 tracking-widest mb-1 font-bold mt-8">
        GLOSSARY
      </div>

      <h2 className="text-center text-3xl tracking-wide">
        用語集
      </h2>


      <Separator className="my-8" />

      <section className="container mx-auto pb-12 px-4 flex flex-col gap-2">
        <Input
          placeholder="検索..."
          className="w-full h-12 text-lg"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />

        {/* <div className="flex flex-row gap-4">
          <div className="flex flex-col gap-2">
            <div className="text-sm text-neutral-500">
              {search}
            </div>
          </div>
        </div> */}

        <div className="text-sm text-neutral-500 p-2">
          合計: {data.filter((item: any) => item[`term_${currentLocale}`].toLowerCase().includes(search.toLowerCase())).length}件
        </div>

        <div className="flex flex-col gap-6">
          {data.filter((item: any) => item[`term_${currentLocale}`].toLowerCase().includes(search.toLowerCase())).map((item: any) => (
            <div key={item.id} className="hover:no-underline hover:bg-neutral-100 p-2 rounded-lg">
              <Link href={`/glossary/${item.id}`} >
                <h3 className="text-lg font-bold ">{item[`term_${currentLocale}`]}</h3>
                <p className="text-sm text-neutral-500">{item[`definition_${currentLocale}`]}</p>
              </Link>
            </div>
          ))}
        </div>
      </section>
    </div>

    <FooterForNonLogin />
  </div>
}