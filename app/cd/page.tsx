"use client";

import { Separator } from "@/components/ui/separator";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@/components/ui/table";
import FooterForNonLogin from "@/components/footerForNonLogin";
import Link from "next/link";

export default function CD() {
  return (
    <div className="pt-(--header-height) p-4">
      <div className="flex flex-row justify-between pt-4">
        <div className="text-2xl font-bold py-4">特定商取引法に基づく表記</div>
      </div>

      <Separator className="mb-4" />

      <Table className="w-full text-left bg-neutral-100 border border-neutral-200 rounded-lg p-4 border-dashed">
        <TableBody>
          <TableRow>
            <TableCell className="font-medium text-bold">販売業社の名称</TableCell>
            <TableCell>TLL合同会社 (TLL LLC)</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium text-bold">所在地</TableCell>
            <TableCell>〒150-0045 東京都渋谷区神泉町 11-11 イーデンビル3F</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">電話番号</TableCell>
            <TableCell>03-4566-3208
              <br />
              受付時間 9:30-18:00（水日祝を除く）
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">メールアドレス</TableCell>
            <TableCell><EMAIL></TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">運営統括責任者</TableCell>
            <TableCell>譚　深</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">販売URL</TableCell>
            <TableCell>https://urbalytics.jp
            </TableCell>
          </TableRow>

          <TableRow>
            <TableCell className="font-medium">サービスの価格</TableCell>
            <TableCell>
              <Link href="/pricing" className="text-blue-500 underline">料金プラン</Link>で確認してください。
            </TableCell>
          </TableRow>

          <TableRow>
            <TableCell className="font-medium">商品代金以外の必要料金</TableCell>
            <TableCell>消費税、インターネット接続に伴う通信費（お客様負担）</TableCell>
          </TableRow>

          <TableRow>
            <TableCell className="font-medium">支払い方法</TableCell>
            <TableCell>クレジットカード決済</TableCell>
          </TableRow>

          <TableRow>
            <TableCell className="font-medium">支払い時期</TableCell>
            <TableCell>初回申込時および各更新日の当日</TableCell>
          </TableRow>

          <TableRow>
            <TableCell className="font-medium">サービス提供時期</TableCell>
            <TableCell>決済完了後、即時にご利用可能です</TableCell>
          </TableRow>

          <TableRow>
            <TableCell className="font-medium">解約・キャンセルについて	</TableCell>
            <TableCell>解約はマイページよりいつでも可能です。契約期間中の途中解約による返金は承っておりません。
            </TableCell>
          </TableRow>

          <TableRow>
            <TableCell className="font-medium">返品・返金について	</TableCell>
            <TableCell>デジタルコンテンツの性質上、返品・返金は原則として対応しておりません。</TableCell>
          </TableRow>

          <TableRow>
            <TableCell className="font-medium">動作環境</TableCell>
            <TableCell>最新版の Google Chrome / Safari にてご利用ください。</TableCell>
          </TableRow>
        </TableBody>
      </Table>

      <FooterForNonLogin />
    </div>
  );
}