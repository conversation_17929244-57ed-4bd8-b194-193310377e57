'use client';

import Image from "next/image";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

export default function Loading() {
  const t = useTranslations('Common');

  return (
    <motion.div
      className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-white dark:bg-black bg-black"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <Image
        src="/icon-192x192.png"

        // <Image
        //   src="/icon-512x512.png"
        alt="Loading Icon"
        width={80}
        height={80}
        className="animate-pulse mb-4"
      />
      <p className="text-base text-neutral-500 dark:text-neutral-400">
        {t("loading")}
      </p>
    </motion.div>
  );
}