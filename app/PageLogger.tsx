// app/layout.tsx (or any layout file)

'use client'

import { useAuthStore } from '@/store/auth'
import { usePathname } from 'next/navigation'
import { useEffect, useRef } from 'react'

export function PageLogger() {
  const pathname = usePathname()
  const prevPath = useRef<string | null>(null)
  const { currentUser } = useAuthStore()

  useEffect(() => {
    if (!pathname || pathname === prevPath.current || currentUser === undefined) return

    prevPath.current = pathname;

    let data = JSON.stringify({
      eventType: 'PAGE_VIEW',
      route: pathname,
    });

    fetch('/api/log', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: data,
    }).catch(console.error)
  }, [pathname])

  return null
}