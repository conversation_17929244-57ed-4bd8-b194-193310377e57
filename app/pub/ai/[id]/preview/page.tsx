"use client";

import { useParams } from "next/navigation";
import { getAiSnsContentById } from "@/actions/aiSnsContents";
import { useEffect, useState } from "react";
import { AiSnsContentProps } from "@/lib/definitions";
import { Separator } from "@/components/ui/separator";
import Markdown from "react-markdown";
import Image from 'next/image'; // 引入 Next.js 的 Image 组件
import { useSearchParams } from "next/navigation";

export default function AiPage() {
  const { id } = useParams(); // 读取 source 参数
  const [aiSnsContent, setAiSnsContent] = useState<AiSnsContentProps | null>(null);
  const searchParams = useSearchParams();
  const source = searchParams.get("source");

  useEffect(() => {
    const fetchAiSnsContent = async () => {
      const aiSnsContent = await getAiSnsContentById(id as string);
      if (aiSnsContent.success) {
        console.log('🔥aiSnsContent', aiSnsContent.data);
        setAiSnsContent(aiSnsContent.data);
      }
    };
    fetchAiSnsContent();
  }, [id]);

  return <div className="bg-neutral-800 flex flex-col justify-center items-center">
    <div className="grid grid-cols-1 gap-4 bg-white max-w-3xl mx-auto">
      <div className="w-full h-auto">
        <Image
          src={aiSnsContent?.imageUrlWithTitleByOpenAiOnSupabase ? aiSnsContent?.imageUrlWithTitleByOpenAiOnSupabase as string : aiSnsContent?.imageUrlByOpenAiOnSupabase as string}
          alt="画像"
          className="w-full h-auto"
          width={500} // 设置合适的宽度
          height={300} // 设置合适的高度
        />
      </div>

      <div className="flex flex-col gap-2 p-4">
        <Markdown>{source === 'deepseek' ? aiSnsContent?.contentByDeepSeek : aiSnsContent?.contentByOpenAi}</Markdown>
      </div>
    </div>
  </div>;
}
