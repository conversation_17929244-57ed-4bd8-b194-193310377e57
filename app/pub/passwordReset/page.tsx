"use client"

import { useEffect, useState } from "react";
import Image from "next/image"
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import router from "next/router";
import { useSearchParams } from "next/navigation";
import { verifyResetPasswordToken } from "@/actions/users";
import { TllUserActivationTokenProps } from "@/lib/definitions/tllUser";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import { resetPasswordWithTokenAction } from "@/actions/users";
import { Loader2 } from "lucide-react";

export default function PasswordResetPage() {
  const [newPassword, setNewPassword] = useState("");
  const [newPasswordConfirm, setNewPasswordConfirm] = useState("");
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const [tokenObject, setTokenObject] = useState<TllUserActivationTokenProps | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (token) {
      verifyResetPasswordToken(token).then((response) => {
        if (response.success) {
          setTokenObject(response.data);
        } else {
          toast({
            title: "トークンが無効です。",
            variant: "destructive",
            duration: 5000,
            description: "トークンが無効です。ログイン画面に戻ってください。",
          });

          // router.push("/login");
        }
      });
    } else {
      toast({
        title: "トークンが見つかりませんでした。",
        variant: "destructive",
        duration: 5000,
        description: "トークンが見つかりませんでした.. ログイン画面に戻ってください。",
      });

      // router.push("/login");
    }
  }, [token]);


  const sendResetPasswordEmail = async () => {
    if (newPassword === "" || newPasswordConfirm === "") {
      toast({
        title: "パスワードを入力してください",
        variant: "destructive",
      })
      return
    }

    if (newPassword !== newPasswordConfirm) {
      toast({
        title: "新しいパスワードが一致しません",
        variant: "destructive",
      })
      return
    }

    if (newPassword.length < 8) {
      toast({
        title: "新しいパスワードが短すぎます",
        variant: "destructive",
      })
      return
    }

    if (!/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,}$/.test(newPassword)) {
      toast({
        title: "新しいパスワードが英数字の組み合わせで入力してください",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true);
    let response = await resetPasswordWithTokenAction({ token: tokenObject?.token as string, newPassword })
    setIsLoading(false);

    if (response.success) {
      createNewSystemUserActivityAction({
        data: {
          eventType: "FORM_SUBMIT",
          route: "/pub/passwordReset",
        },
      });

      toast({
        title: "パスワードを変更しました... ログイン画面からログインしなおしてください",
        description: "ログイン画面からログインしなおしてください",
        duration: 2000,
      })

      await new Promise(resolve => setTimeout(resolve, 2000));
      router.push('/login');
    } else {
      toast({
        title: "パスワードを変更できませんでした",
        description: response.message,
        variant: "destructive",
        duration: 2000,
      })

      await new Promise(resolve => setTimeout(resolve, 2000));
      router.push('/login');
    }
  }

  return (
    <div className="relative w-full h-screen">
      <Image
        src="/assets/fuzzy/bg-common.jpg"
        alt="bg-common"
        className="w-full h-full object-cover blur-[1px]"
        width={1000}
        height={1000}
      />

      <div className="absolute top-0 left-0 w-full h-full bg-black/50"></div>

      <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center">
        <div className="bg-white p-4 w-[320px] h-[320px] rounded-lg">
          <div className="flex flex-col items-center justify-center gap-2">
            <h1 className="text-2xl font-bold mb-4">パスワード変更</h1>

            {tokenObject ? (
              <div>
                <div className="flex flex-col gap-2 w-full">
                  <label htmlFor="password">新しいパスワード</label>
                  <Input type="password" id="password" value={newPassword} onChange={(e) => setNewPassword(e.target.value)} />
                </div>

                <div className="flex flex-col gap-2 w-full">
                  <label htmlFor="password">新しいパスワード（確認）</label>
                  <Input type="password" id="password" value={newPasswordConfirm} onChange={(e) => setNewPasswordConfirm(e.target.value)} />
                </div>

                <div className="flex flex-col mt-4 gap-2 w-full">
                  <Button onClick={() => {
                    sendResetPasswordEmail();
                  }}>
                    {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "変更"}
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                <p>トークンは無効です</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}