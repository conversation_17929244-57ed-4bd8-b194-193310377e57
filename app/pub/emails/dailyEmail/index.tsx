import { Html, Head, Preview, Body, Container, Section, Row, Column, Heading, Text, Link, Img } from "@react-email/components";
import { Property } from "../common/property";
import dayjs from "dayjs";

export const DailyEmail = ({ dataPriceDown, dataHighYield, changedFavRecord }: { dataPriceDown: any[], dataHighYield: any[], changedFavRecord: any[] }) => {

  let totalCount = dataPriceDown.length + dataHighYield.length + changedFavRecord.length;

  return (
    <Html>
      <Head />
      <Preview>Urbalytics 今日の新規おすすめ物件は{totalCount.toLocaleString()}件</Preview>
      <Body>
        <Container style={{ backgroundColor: "#f3f4f6", borderRadius: "8px", margin: "0 auto", maxWidth: "960px", overflow: "hidden", padding: "8px" }}>
          <Link href="https://urbalytics.jp" style={{ display: "flex", alignItems: "center", padding: "16px" }}>
            <Img src="https://www.urbalytics.jp/_next/image?url=%2Ficon-192x192.png&w=64&q=75" alt="ロゴ" width={32} height={32} /> {/* アイコンを追加 */}
            <span style={{ fontSize: "18px", color: "black", marginLeft: "8px" }}>Urbalytics</span>
          </Link>

          <Section style={{ borderBottom: "1px solid #e5e7eb" }}>
            <Row style={{ backgroundColor: "#f3f4f6", borderSpacing: "24px", margin: "0", display: "table", width: "100%" }}>
              <Column style={{ paddingLeft: "12px" }}>
                <Text style={{ color: "black", fontSize: "24px", margin: "0", padding: "4px 8px", borderRadius: "4px" }}>
                  {dayjs().format('YYYY/MM/DD')}
                </Text>
                <Heading
                  as="h1"
                  style={{ color: "black", fontSize: "28px", fontWeight: "bold", marginBottom: "10px" }}
                >
                  新規おすすめ物件は{totalCount}件
                </Heading>
              </Column>
            </Row>
          </Section>

          {changedFavRecord.length > 0 && <Section style={{ marginBottom: "12px" }}>
            <Text style={{ color: "black", fontSize: "14px", margin: "0", backgroundColor: "#30ff68", padding: "4px 8px", borderRadius: "4px" }}>
              お気に入り物件変更: <span style={{ fontWeight: "bold", fontSize: "18px" }}>
                {changedFavRecord.length}件
              </span>
            </Text>

            {changedFavRecord.map((record, index) => (
              <Property record={record} key={index} />
            ))}
          </Section>}


          <Section style={{ marginBottom: "12px" }}>
            <Text style={{ color: "black", fontSize: "14px", margin: "0", backgroundColor: "#c7ffd6", padding: "4px 8px", borderRadius: "4px" }}>
              １割以上値下げ物件: <span style={{ fontWeight: "bold", fontSize: "18px" }}>
                {dataPriceDown.length}件
              </span>
            </Text>

            {dataPriceDown.map((record, index) => (
              <Property record={record} key={index} />
            ))}
          </Section>

          <Section style={{ marginBottom: "12px", borderTop: "1px dashed", paddingTop: "12px" }}>
            <Text style={{ color: "black", fontSize: "14px", margin: "0", backgroundColor: "#c7ffd6", padding: "4px 8px", borderRadius: "4px" }}>
              利回り７％以上物件: <span style={{ fontWeight: "bold", fontSize: "18px" }}>
                {dataHighYield.length}件
              </span>
            </Text>

            {dataHighYield.map((record, index) => (
              <Property record={record} key={index} />
            ))}
          </Section>

          <footer style={{ marginTop: "8px", padding: "4px 0", width: "100%", textAlign: "center", color: "#1f2937", display: "flex", flexDirection: "column", alignItems: "center", gap: "4px" }}>
            <div style={{ fontSize: "14px", display: "flex", flexDirection: "column", gap: "8px", justifyContent: "center", alignItems: "center", width: "100%" }}>
              <div style={{ display: "flex", flexDirection: "row", gap: "8px", justifyContent: "center", alignItems: "center" }}>
                <Link href="https://urbalytics.com/tos" style={{ color: "#1f2937" }}>利用規約</Link>
                <Link href="https://urbalytics.com/pp" style={{ color: "#1f2937" }}>個人情報保護方針</Link>
                <Link href="https://urbalytics.com/app" style={{ color: "#1f2937" }}>アプリ</Link>
              </div>
            </div>

            <Link href="https://urbalytics.jp" style={{ textDecoration: "" }}>
              <div style={{ fontSize: "18px", color: "#1f2937" }}>©2025 Urbalytics</div>
            </Link>

            <div style={{ fontSize: "14px", color: "#6b7280", width: "100%", padding: "0 40px", display: "flex", flexDirection: "column", gap: "8px" }}>
              <div style={{ textAlign: "center", color: "#6b7280", fontSize: "12px" }}>
                メール配信解除は<Link href="https://urbalytics.jp/my/account?tab=push" style={{ textDecoration: "underline" }}>こちら</Link>
              </div>
            </div>
          </footer>
        </Container>
      </Body>
    </Html>
  );
};
