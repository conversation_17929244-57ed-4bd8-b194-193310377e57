import { Html, Head, Preview, Body, Container, Text, Link } from "@react-email/components";

export const ActivateEmail = ({ activationLink }: { activationLink: string }) => {
  return (
    <Html>
      <Head />
      <Preview>Activate your account</Preview>
      <Body style={{ backgroundColor: "#f6f9fc", padding: "20px" }}>
        <Container style={{ backgroundColor: "#ffffff", padding: "20px", borderRadius: "8px" }}>
          <Text style={{ fontSize: "16px" }}>Urbalyticsにようこそ 👋<br />Welcome to Urbalytics 👋</Text>

          <Text>
            アカウントを作成するには、以下のリンクをクリックしてください。<br />
            To complete your account registration, please click the link below.
          </Text>

          <Link href={activationLink} style={{ color: "#0070f3", fontWeight: "bold" }}>
            アカウントを有効にする / Activate Account
          </Link>

          <Text style={{ fontSize: "12px", color: "#888" }}>
            このリンクは24時間有効です。<br />
            This link is valid for 24 hours.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};