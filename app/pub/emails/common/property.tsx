import { Section, Img, Text, <PERSON><PERSON>, <PERSON>, Heading, Column } from "@react-email/components";
import dayjs from "dayjs";

export const Property = ({ record }: { record: any }) => {
  const mapper = {
    "BUILDING": "一棟収益物件",
    "HOUSE": "戸建て",
    "LAND": "土地",
    "MANSION": "マンション",
  }

  let sortedPrice = record.priceChanges.sort((a: any, b: any) => dayjs(b.recordDate).diff(dayjs(a.recordDate)));
  let prevRecordPrice = sortedPrice[1]?.price;

  return (
    <Section style={{ margin: "16px 0" }}>
      <table style={{ width: "100%" }}>
        <tbody style={{ width: "100%", textAlign: "center" }}>
          <tr style={{ width: "90%", margin: "0 auto" }}>
            <td style={{ boxSizing: "border-box", width: "40%", paddingRight: "4px" }}>
              <Img
                alt="物件画像"
                style={{ width: "100%", borderRadius: "4px", objectFit: "cover", height: "240px" }}
                src={record.link || "https://placehold.co/600x400?text=No+Image"}
              />
            </td>

            <td style={{ width: "50%", verticalAlign: "baseline", textAlign: "left" }}>
              <Text style={{
                fontSize: "18px",
                lineHeight: "24px",
                marginBottom: "2px",
                color: "#111827",
                textAlign: "left",
              }}>
                {record.address} {mapper[record.recordType as keyof typeof mapper]}
              </Text>

              <Text style={{ textAlign: "left", margin: "2px 0", fontSize: "16px", lineHeight: "32px", color: "#111827" }}>

                <span style={{ fontSize: "24px", marginTop: "4px", marginBottom: "4px" }}>
                  {record.price}万円
                </span>


                {prevRecordPrice && prevRecordPrice !== record.price && <span style={{ marginLeft: "4px", opacity: 0.5, color: "#6B7280", textDecoration: "line-through" }}>
                  {prevRecordPrice}万円
                </span>}

                {/* <span style={{ color: "red", fontWeight: "bold" }}>
                  {prevRecordPrice && (record.price - prevRecordPrice) / prevRecordPrice * 100 > 0 ? "+" : ""}
                  {(record.price - prevRecordPrice) / prevRecordPrice * 100}%
                </span> */}
              </Text>

              {record.yearlyIncome > 0 && <Text style={{ textAlign: "left", margin: "2px 0", fontSize: "14px", lineHeight: "24px", color: "#111827" }}>
                <strong> 年間収入: </strong> {record.yearlyIncome}万円 |
                <span style={{ color: "red", fontWeight: "bold" }}>利回り: {(record.yearlyIncome / record.price * 100).toFixed(2)}%</span>
              </Text>}

              <Text style={{ textAlign: "left", margin: "2px 0", fontSize: "14px", lineHeight: "24px", color: "#111827" }}>
                <strong> 土地: </strong>
                面積{record.landSize}m2 | {record.landRight}
              </Text>

              <Text style={{ textAlign: "left", margin: "2px 0", fontSize: "14px", lineHeight: "24px", color: "#111827" }}>
                <strong> 延床: </strong> 面積{record.buildingSize}m2 | {record.buildingBuiltYear || "築不明"}年 | {record.buildingMaterial || "材料不明"}
              </Text>

              <Text style={{ textAlign: "left", fontSize: "14px", color: "#6B7280", margin: "2px 0" }}>
                {record.transport}
                徒歩{record.nearestStationWalkMinute}分
              </Text>

              <Button
                style={{ float: "left", textAlign: "center", width: "90%", borderRadius: "2px", backgroundColor: "white", padding: "4px 0px", fontWeight: "600", color: "black", fontSize: "18px", marginTop: "16px" }}
                href={`http://urbalytics.jp/ex/search/${record.id}`}
              >
                詳細を確認
              </Button>
            </td>
          </tr>
        </tbody>
      </table>
    </Section>
  )
}