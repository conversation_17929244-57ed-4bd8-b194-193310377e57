'use client';

import { useEffect, useState } from "react";
import { getUserLambdaRecordMetaAction } from "@/actions/tllUserLambdaRecords";
import { useParams, useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";

export default function RecPreviewPage() {
  const params = useParams();
  const id = params.id;
  const [userLambdaRecordMeta, setUserLambdaRecordMeta] = useState<any>(null);
  const router = useRouter();
  const [dots, setDots] = useState('.');

  useEffect(() => {
    // const fetchUserLambdaRecordMeta = async () => {
    //   const response = await getUserLambdaRecordMetaAction(id as string);
    //   console.log('🔥userLambdaRecordMeta', response);
    //   setUserLambdaRecordMeta(response.data);
    // };

    // fetchUserLambdaRecordMeta();
    // FIXME: turn this one after testing 
    // 在2秒后重定向到主页面
    setTimeout(() => {
      router.push(`/ex/search/${id}`);
    }, 2000);

    const interval = setInterval(() => {
      setDots(prev => prev.length < 3 ? prev + '.' : '');
    }, 300);

    return () => clearInterval(interval);
  }, []);

  // console.log('🔥userLambdaRecordMeta', userLambdaRecordMeta);

  // THIS IS FOR TESTING PURPOSES
  // In production there should be no content, the swap should be immediate
  return (
    <div className="bg-neutral-800 flex flex-col justify-center items-center h-full">
      <div className="grid grid-cols-1 gap-4 text-white max-w-3xl mx-auto">
        {/* {JSON.stringify(userLambdaRecordMeta)} */}
        Redirecting{dots} {/* 动画效果，点数变化 */}
      </div>
    </div>
  );
}