import { getUserLambdaRecordMetaAction } from "@/actions/tllUserLambdaRecords";
import type { Metadata } from "next";
import React from 'react';


const defaultMetadata: Metadata = {
  title: "Property Details",
  description: "物件詳細ページ",
};

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const id = (await params).id

  const pageData = await getUserLambdaRecordMetaAction(id); // Fetch metadata dynamically

  if (pageData.success) {
    let { data } = pageData;

    let starRatingIcon = data.propertyAnalysisResult?.overallStarLevel < 1 ? "⭐" : data.propertyAnalysisResult?.overallStarLevel <= 3 ? "🌟" : "✨";

    let title = `[${data.recordType}][${data.price}万円][${data.propertyAnalysisResult?.overallStarLevel}${starRatingIcon}] ${data.address}`;
    let descriptionTransport = `${data.transport}${data.nearestStationWalkMinute}分`;
    let descriptionLand = `${data.landSize}㎡/${data.landRight}/${data.landType}`;
    let descriptionBuilding = `${data.buildingSize}㎡/${data.buildingMaterial}/${data.buildingBuiltYear}`;

    let placeholderTextRow1 = data.propertyAnalysisResult?.overallStarLevel > 0 ? Array(data.propertyAnalysisResult?.overallStarLevel).fill("★").join("") : '';

    let placeholderTextRow2 = `${data.recordType} - ${data.price}万円`;
    let placeholderTextRow3 = `${data.address}`;
    let placeholderTextRow4 = `${data.transport}${data.nearestStationWalkMinute}分`;
    let placeholderTextRow5 = `土地${data.landSize} | 建物${data.buildingSize}`;
    // let placeholderUrl = `https://placehold.co/1200x630?text=${placeholderTextRow1}\\n${placeholderTextRow2}\\n${placeholderTextRow3}&font=Poppins`;
    let placeholderUrl = `https://placehold.jp/64/000000/ffffff/1200x630.jpg?text=${placeholderTextRow1}%0A${placeholderTextRow2}%0A${placeholderTextRow3}%0A${placeholderTextRow4}%0A${placeholderTextRow5}`;
    console.log('🔥placeholderUrl', placeholderUrl);

    return {
      title: title,
      description: `${descriptionTransport} | 土地 ${descriptionLand} | 建物 ${descriptionBuilding}`,
      openGraph: {
        title: title,
        description: `${descriptionTransport} | 土地 ${descriptionLand} | 建物 ${descriptionBuilding}`,
        url: `https://urbalytics.jp/ex/search/${id}`,
        images: [{ url: placeholderUrl, width: 1200, height: 630 }],
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: title,
        description: `${descriptionTransport} | 土地 ${descriptionLand} | 建物 ${descriptionBuilding}`,
        images: [placeholderUrl],
      },
      // ✅ Add WeChat-Specific Meta
      other: {
        "wechat:title": title,
        "wechat:description": `${descriptionTransport} | 土地 ${descriptionLand} | 建物 ${descriptionBuilding}`,
        "wechat:image": placeholderUrl,
      },
    };
  }

  return defaultMetadata;
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
    </>
  );
}
