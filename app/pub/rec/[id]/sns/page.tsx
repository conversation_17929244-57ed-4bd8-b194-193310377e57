"use client";

import { useParams } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import { getUserLambdaRecordForSnsAction } from "@/actions/tllUserLambdaRecords";
import { UserLambdaRecordProps, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import PriceChangeHistoryChart from "@/app/(cp)/ex/search/[id]/(details)/(priceChange)/PriceChangeHistoryChart";
import PriceChangeHistory from "@/app/(cp)/ex/search/[id]/(details)/(priceChange)/PriceChangeHistory";
import { priceChangeColumns } from "@/app/(cp)/ex/search/[id]/(details)/(priceChange)/priceChangeColumns";
import { DataTable } from "@/components/ui/data-table";
import PropertyInfoCommon from "@/app/(cp)/ex/search/[id]/(details)/(propertyInfo)/PropertyInfoCommon";
import PropertyInfoLand from "@/app/(cp)/ex/search/[id]/(details)/(propertyInfo)/PropertyInfoLand";
import PropertyInfoBuilding from "@/app/(cp)/ex/search/[id]/(details)/(propertyInfo)/PropertyInfoBuilding";
import NearbyProperties from "@/app/(cp)/ex/search/[id]/(details)/NearbyProperties";
import { getTopResultsFromIds } from "@/actions/tllUserLambdaRecordNearbyRecords";
import { userlambdaRecordTableColumnsCommonSearch } from "@/components/userLambdaRecord/userlambdaRecordTableColumnsCommon";
import { Button } from "@/components/ui/button";
import { FileDownIcon, Loader2 } from "lucide-react";
import html2canvas from "html2canvas";
import { jsPDF } from "jspdf";
import PriceCfCalculation from "@/app/(cp)/ex/search/[id]/(details)/PriceCfCalculation";
import CFSection from "@/app/(cp)/it/cf/CFSection";
import NearbyProjects from "@/app/(cp)/ex/search/[id]/(details)/NearbyProjects";
import RentUpside from "@/app/(cp)/ex/search/[id]/(details)/RentUpside";
import TransactionRecords from "@/app/(cp)/ex/search/[id]/(details)/(transactionRecords)/TransactionRecords";
import { UserCircle, Phone, Mail, Building2 } from "lucide-react";
import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";
import dayjs from "dayjs";
import PriceInfoRanking from "@/app/(cp)/ex/search/[id]/(details)/PriceInfoRanking";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useAuthStore } from "@/store/auth";
import PropertyInfoPhotoPublic from "@/app/(cp)/ex/search/[id]/(details)/(propertyInfo)/PropertyInfoPhotoPublic";
import PropertyInfoPhotoPublicCollage from "@/app/(cp)/ex/search/[id]/(details)/(propertyInfo)/PropertyInfoPhotoPublicCollage";
import NearbyPropertiesExport from "@/app/(cp)/ex/search/[id]/(details)/NearbyPropertiesExport";

export default function SnsPage() {
  const { id } = useParams();
  const [selectedRecord, setSelectedRecord] = useState<UserLambdaRecordProps | null>(null);
  const [nearbyRecords, setNearbyRecords] = useState<UserLambdaRecordProps[]>([]);
  const [isExportingAll, setIsExportingAll] = useState(false);
  const { currentUser } = useAuthStore();

  const priceChangeRef = useRef<HTMLDivElement>(null);
  const nearbyPublicRef = useRef<HTMLDivElement>(null);
  const propertyInfoRef = useRef<HTMLDivElement>(null);
  const photoRef = useRef<HTMLDivElement>(null);
  const nearbyDataRef = useRef<HTMLDivElement>(null);
  const valuationRef = useRef<HTMLDivElement>(null);
  const cfRef = useRef<HTMLDivElement>(null);
  const devRef = useRef<HTMLDivElement>(null);
  const rentRef = useRef<HTMLDivElement>(null);
  const coverRef = useRef<HTMLDivElement>(null);
  const contactRef = useRef<HTMLDivElement>(null);

  const renderFooter = (currentPage: number) => {
    return (
      <>
        <img src="/assets/urba-full.png" alt="logo" width={160} height={40} className="absolute top-4 right-4 opacity-90 bg-white" />

        <div className="w-full border-t border-neutral-200 absolute bottom-0 right-0 px-4 py-2 bg-white">
          <div className="text-sm text-neutral-500 text-right">{currentPage}/{sections.length - 1}</div>
        </div>

      </>
    )
  }

  const sections = [
    { ref: coverRef, title: "表紙" },
    {
      ref: propertyInfoRef, title: "物件情報", content: <>
        <div className="border-b border-neutral-200 pb-4 mb-2">
          <h2 className="text-xl font-semibold">物件情報</h2>
        </div>

        <div className="flex flex-col gap-2">
          <div className="grid grid-cols-3 gap-2">
            <div className="p-2 bg-neutral-50 rounded-lg">
              <div className="text-sm text-neutral-500 mb-1">物件価格</div>
              <div className="text-2xl font-bold text-neutral-900">
                {selectedRecord?.price}
                <span className="text-sm text-neutral-500 ml-1">万円</span>
              </div>
            </div>
            <div className="p-2 bg-neutral-50 rounded-lg">
              <div className="text-sm text-neutral-500 mb-1">年収入</div>
              <div className="text-2xl font-bold text-neutral-900">
                {selectedRecord?.yearlyIncome ? selectedRecord?.yearlyIncome.toFixed(2) : "-"}
                <span className="text-sm text-neutral-500 ml-1">万円</span>
              </div>
              <div className="text-sm text-neutral-500 mt-1">
                月: {selectedRecord?.yearlyIncome ? (selectedRecord?.yearlyIncome / 12).toFixed(2) : "-"}万円
              </div>
            </div>
            <div className="p-2 bg-neutral-50 rounded-lg">
              <div className="text-sm text-neutral-500 mb-1">表面利回り</div>
              <div className="text-2xl font-bold text-neutral-900">
                {selectedRecord?.yearlyIncome ? (selectedRecord?.yearlyIncome / selectedRecord?.price * 100).toFixed(2) : "-"}%
              </div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-2">
            <div className="col-span-2">
              <PropertyInfoCommon selectedProperty={selectedRecord as UserLambdaRecordProps} />
            </div>

            <div className="col-span-1 flex flex-col gap-2">
              <PropertyInfoLand selectedProperty={selectedRecord as UserLambdaRecordProps} />
              <PropertyInfoBuilding selectedProperty={selectedRecord as UserLambdaRecordProps} />
            </div>
          </div>
        </div>
      </>
    },
    {
      ref: photoRef, title: "物件写真", content: <>
        <div className="border-b border-neutral-200 pb-4 mb-2">
          <h2 className="text-xl font-semibold">物件写真</h2>
        </div>

        <PropertyInfoPhotoPublicCollage selectedProperty={selectedRecord as UserLambdaRecordProps} currentUser={currentUser as TllUserProps} />
      </>
    },
    {
      ref: priceChangeRef, title: "価格改正",
      content: <>
        {/* <div className="border-b border-neutral-200 pb-4 mb-4">
          <h2 className="text-xl font-semibold">価格変更履歴</h2>
        </div>

        {selectedRecord?.priceChanges && selectedRecord.priceChanges.length > 0 && (
          <div className="grid grid-cols-3 gap-4">
            <div className="col-span-1">
              <PriceChangeHistoryChart currentUserLambdaRecord={selectedRecord} />
            </div>

            <div className="col-span-2">
              <DataTable
                columns={priceChangeColumnsForSNS}
                data={selectedRecord?.priceChanges?.sort((a, b) => new Date(a.recordDate || "").getTime() < new Date(b.recordDate || "").getTime() ? 1 : -1) || []}
                showFooter={false}
              />
            </div>
          </div>
        )} */}
        <PriceChangeHistory currentUserLambdaRecord={selectedRecord as UserLambdaRecordProps} currentUser={currentUser as TllUserProps} recordId={id as string} />
      </>
    },
    {
      ref: nearbyDataRef, title: "周辺データ", content: <>
        <div className="border-b border-neutral-200 pb-4 mb-2">
          <h2 className="text-xl font-semibold">近隣売買履歴</h2>
        </div>

        <DataTable
          columns={userlambdaRecordTableColumnsCommonSearch}
          data={nearbyRecords}
          defaultPageSize={10}
        />
      </>
    },
    {
      ref: rentRef, title: "賃料アップサイド",
      content: <>
        <RentUpside currentUserLambdaRecord={selectedRecord as UserLambdaRecordProps} nearbyRecords={nearbyRecords} />
      </>
    },
    {
      ref: cfRef, title: "CF試算",
      content: <>
        <CFSection currentUserLambdaRecord={selectedRecord} />
      </>
    },
    {
      ref: devRef, title: "近隣開発",
      content: <>
        <NearbyProjects currentUserLambdaRecord={selectedRecord} />
      </>
    },
    {
      ref: nearbyPublicRef, title: "近隣公示成約事例",
      content: <>
        <TransactionRecords currentUserLambdaRecord={selectedRecord} />
      </>
    },
    {
      ref: contactRef, title: "お問い合わせ", content: <>
        <div className="border-b border-neutral-200 pb-4 mb-8">
          <h2 className="text-2xl font-bold text-neutral-900">お問い合わせ</h2>
          <p className="text-sm text-neutral-500 mt-1">ご質問・ご相談は下記担当者までお気軽にお問い合わせください</p>
        </div>

        <div className="flex flex-col items-center justify-center h-[calc(100%-10rem)]">
          <div className="grid grid-cols-2 gap-20 mb-16 w-full max-w-4xl">
            <div className="p-10 bg-linear-to-br from-neutral-50 to-white rounded-xl shadow-md border border-neutral-100 hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center justify-center mb-6">
                <div className="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center">
                  <UserCircle className="w-8 h-8 text-blue-600" />
                </div>
              </div>
              <h3 className="text-xl font-bold text-center mb-6 text-neutral-900">営業担当</h3>
              <div className="space-y-4 text-neutral-700">
                <p className="text-lg font-semibold text-center mb-6 text-neutral-900">山田 太郎</p>
                <div className="space-y-3">
                  <p className="flex items-center gap-3 text-neutral-600">
                    <Phone className="w-4 h-4 text-blue-600" />
                    <span className="font-medium">電話:</span>
                    <a href="tel:0312345678" className="hover:text-blue-600 transition-colors">03-1234-5678（直通）</a>
                  </p>
                  <p className="flex items-center gap-3 text-neutral-600">
                    <Mail className="w-4 h-4 text-blue-600" />
                    <span className="font-medium">メール:</span>
                    <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors"><EMAIL></a>
                  </p>
                </div>
              </div>
            </div>

            <div className="p-10 bg-linear-to-br from-neutral-50 to-white rounded-xl shadow-md border border-neutral-100 hover:shadow-lg transition-shadow duration-300">
              <div className="flex items-center justify-center mb-6">
                <div className="w-16 h-16 bg-green-50 rounded-full flex items-center justify-center">
                  <Building2 className="w-8 h-8 text-green-600" />
                </div>
              </div>
              <h3 className="text-xl font-bold text-center mb-6 text-neutral-900">物件担当</h3>
              <div className="space-y-4 text-neutral-700">
                <p className="text-lg font-semibold text-center mb-6 text-neutral-900">鈴木 花子</p>
                <div className="space-y-3">
                  <p className="flex items-center gap-3 text-neutral-600">
                    <Phone className="w-4 h-4 text-green-600" />
                    <span className="font-medium">電話:</span>
                    <a href="tel:0312345679" className="hover:text-green-600 transition-colors">03-1234-5679（直通）</a>
                  </p>
                  <p className="flex items-center gap-3 text-neutral-600">
                    <Mail className="w-4 h-4 text-green-600" />
                    <span className="font-medium">メール:</span>
                    <a href="mailto:<EMAIL>" className="hover:text-green-600 transition-colors"><EMAIL></a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    }
  ];

  const exportAllToPdf = async () => {
    if (!coverRef.current || !valuationRef.current || !priceChangeRef.current || !propertyInfoRef.current || !nearbyDataRef.current || !cfRef.current || !devRef.current || !contactRef.current) return;

    setIsExportingAll(true);
    try {
      // 创建 PDF 文档
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      // 依次处理每个部分
      for (let i = 0; i < sections.length; i++) {
        const section = sections[i];

        // 如果不是第一页，添加新页面
        if (i > 0) {
          pdf.addPage();
        }

        // 将当前部分转换为 canvas
        const canvas = await html2canvas(section.ref.current!, {
          scale: 2,
          useCORS: true,
          logging: false,
          backgroundColor: '#ffffff'
        });

        // // 将 canvas 转换为图片数据
        const imgData = canvas.toDataURL('image/jpeg', 1.0);

        // // 计算图片尺寸以适应 A4 横向页面
        // const imgWidth = 297; // A4 横向宽度
        // const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // // 添加图片到 PDF
        // pdf.addImage(imgData, 'JPEG', 0, 0, imgWidth, imgHeight);

        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();

        const imgProps = pdf.getImageProperties(imgData);
        const imgRatio = imgProps.width / imgProps.height;

        let imgWidth = pageWidth;
        let imgHeight = pageWidth / imgRatio;

        // 如果图片高度超出页面，则等比缩小
        if (imgHeight > pageHeight) {
          imgHeight = pageHeight;
          imgWidth = pageHeight * imgRatio;
        }

        // 居中位置
        const x = (pageWidth - imgWidth) / 2;
        const y = (pageHeight - imgHeight) / 2;

        pdf.addImage(imgData, 'JPEG', x, y, imgWidth, imgHeight);
      }

      // 保存 PDF
      pdf.save(`${dayjs().format("YYYYMMDD")}_${selectedRecord?.address || "物件情報"}_${mapper[selectedRecord?.recordType as UserLambdaRecordType]?.nameFull}_物件提案書.pdf`);

    } catch (error) {
      console.log("🔥 PDF export error:", error);
    } finally {
      setIsExportingAll(false);
    }
  };

  const fetchPropertyInfo = async () => {
    const propertyInfo = await getUserLambdaRecordForSnsAction(id as string);
    if (propertyInfo.success) {
      setSelectedRecord(propertyInfo.data);
      const res = await getTopResultsFromIds({ distanceXId: propertyInfo.data?.propertyAnalysisResult?.nearbyRecordIdAndDistance || {} });
      let nearbyRecords = res.data;
      setNearbyRecords(nearbyRecords);
    }
  };

  useEffect(() => {
    fetchPropertyInfo();
  }, [id]);


  return <div className="min-h-screen bg-neutral-600 flex flex-col items-center py-8">
    {/* 顶部工具栏 - 保持在原始大小 */}
    <div className="w-full max-w-[297mm] mb-6 flex justify-between items-center sticky top-0 bg-neutral-600 py-6 z-10">
      <div className="flex items-center gap-2">
        <h1 className="text-2xl font-bold text-white">物件情報</h1>
        <span className="text-sm text-white">
          {selectedRecord?.address || "物件情報"}
          {mapper[selectedRecord?.recordType as UserLambdaRecordType]?.nameFull}物件提案書
        </span>
      </div>

      <Button
        variant="default"
        size="lg"
        onClick={exportAllToPdf}
        disabled={isExportingAll}
        className="bg-blue-600 hover:bg-blue-700"
      >
        <FileDownIcon className="w-5 h-5" />
        {isExportingAll ? <Loader2 className="w-4 h-4 animate-spin" /> : "PDF出力"}
      </Button>
    </div>

    {/* 移除缩放容器，使用原始大小显示 */}
    <div className="grid grid-cols-1 gap-8 bg-neutral-300 p-8 rounded-lg shadow-lg">
      {/* 表紙 */}
      <div ref={coverRef} className="w-[297mm] h-[210mm] bg-white p-8 relative overflow-hidden border border-neutral-200 rounded-lg shadow-xs">
        <img src="/assets/urba-full.png" alt="logo" width={200} height={50} className="absolute top-8 right-8 opacity-90" />

        <div className="h-full flex flex-col items-left justify-center text-left">

          <h1 className="text-6xl font-bold mb-8 text-neutral-900 leading-tight">
            {selectedRecord?.address || "物件情報"}
            <br />
            {mapper[selectedRecord?.recordType as UserLambdaRecordType]?.nameFull}
          </h1>
          <div className="text-2xl mb-4">査定レポート</div>
          <div className="text-lg mb-4">作成日: {new Date().toLocaleDateString('ja-JP', { year: 'numeric', month: 'long', day: 'numeric' })}</div>
        </div>
      </div>


      {sections.filter((section: any) => section.content).map((section: any, index: number) => (
        <div key={index} ref={section.ref as React.RefObject<HTMLDivElement>} className="w-[297mm] h-[210mm] bg-white p-4 relative overflow-hidden rounded-lg shadow-xs">
          <div className="w-full h-full border-neutral-200 overflow-hidden">
            {section.content}
            {renderFooter(index + 1)}
          </div>
        </div>
      ))}
    </div>
  </div>;
}