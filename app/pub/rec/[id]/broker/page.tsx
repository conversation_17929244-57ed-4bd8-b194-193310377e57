"use client";

import { getTopResultsFromIds } from "@/actions/tllUserLambdaRecordNearbyRecords";
import { getUserLambdaRecordForSnsAction } from "@/actions/tllUserLambdaRecords";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { useParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/ui/data-table";
import { nearbySeiyakuColumnForBroker } from "@/components/userLambdaRecord/userlambdaRecordTableColumnsBroker";
import dayjs from "dayjs";
import { getLatestChangeValue } from "@/components/userLambdaRecord/priceChangeUtilities";

export default function BrokerPage() {
  const { id } = useParams();
  const [selectedRecord, setSelectedRecord] = useState<UserLambdaRecordProps | null>(null);
  const [nearbyRecords, setNearbyRecords] = useState<UserLambdaRecordProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchPropertyInfo = async () => {
    setIsLoading(true);
    const propertyInfo = await getUserLambdaRecordForSnsAction(id as string);
    if (propertyInfo.success) {
      setSelectedRecord(propertyInfo.data);
      const res = await getTopResultsFromIds({ distanceXId: propertyInfo.data?.propertyAnalysisResult?.nearbyRecordIdAndDistance || [] });
      let nearbyRecords = res.data;
      setNearbyRecords(nearbyRecords);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchPropertyInfo();
  }, [id]);

  return <div className="bg-neutral-800 flex flex-col justify-center items-center h-screen p-6 bg-white" style={{
    fontFamily: 'Noto Sans JP',
  }}>
    <div className="w-[210mm] h-[297mm] bg-white p-8">
      <h1 className="text-2xl text-left p-2">
        オーナー様
      </h1>

      <div className="p-2 text-lg">
        突然のお手紙をお読み頂きありがとうございました。
        <br />
        私はTLLの小林と申します。
        <br />
        弊社は不動産会社でありながら、不動産投資家でもあります。
        <br />
        現在、オーナー様はご所有の物件の販売活動をされてるかと思います。私たちはインターネット上にあるデータを<b>AIを駆使して不動産の販売状況</b>等を確認しております。
        <br />
        その中で価格の変動、媒介契約の種類や仲介会社様の変更を確認しご連絡させていただきました。
        <br />
        弊社は一棟収益を専門で取り扱っている会社のため、一般的な仲介会社様とは<b>データ量の取り扱いに圧倒的に差</b>があると自負しております。
        大手の仲介会社様からも査定依頼を頂くほどですので是非、一度お話だけでもお伺いさせて頂けると幸いです。
      </div>

      <Separator />
      <h1 className="text-2xl text-red-500 font-bold text-left border-neutral-200 p-2 pb-0">
        弊社の査定が信頼される３つのポイント
      </h1>

      <div className="mb-2 p-2 font-bold text-xl border-b border-neutral-200">
        ✅ ビッグデータ×AI解析でリアルタイム査定
        <br />
        ✅ ネット上の売買動向を即座に反映し、最適な価格を算出
        <br />
        ✅ 経験と勘ではなく、"市場データ"に基づく査定を実施！
      </div>

      <div className="bg-neutral-100 pb-4">
        <h1 className="text-2xl text-center border-neutral-200 p-2">
          データサンプル
        </h1>


        <div className="p-2 text-lg">
          オーナー様ご所有の近隣の売買履歴
        </div>

        <div className="p-2">
          <Suspense fallback={<div>Loading...</div>}>
            <DataTable columns={nearbySeiyakuColumnForBroker} data={nearbyRecords.filter((record) => new Set(record.priceChanges?.map((priceChange) => priceChange.price)).size > 1).sort((a, b) => dayjs(getLatestChangeValue(b, 'recordDate')).diff(dayjs(getLatestChangeValue(a, 'recordDate')))).slice(0, 6)} showFooter={false} isLoading={isLoading} />
          </Suspense>
        </div>
      </div>


      {/* <div className="text-lg font-bold my-2">
        あなたの不動産の最新査定額は…
      </div>

      <div className="text-4xl font-bold text-center relative flex flex-row items-center justify-center">
        <div className="text-red-500">
          【査定額
        </div>

        <div className="relative blur-lg hover:blur-none transition duration-300  text-red-500">
          <div>7898.3212万円</div> 】
        </div>

        <div className="text-red-500">
          】
        </div>

        <div>
          円
        </div>
      </div> */}
      <div className="text-lg font-bold pt-4 mb-4">
        詳細を直接お届け（無料）しますので是非一度お話にお伺いさせてください。
      </div>
    </div>
  </div >
}