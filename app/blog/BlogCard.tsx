"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import { SanityImageSource } from "@sanity/image-url/lib/types/types";
import imageUrlBuilder from "@sanity/image-url";
import { Aperture } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { urlFor, extractPreviewText, findWriter } from "./blogUtility";
const { projectId, dataset } = sanityClient.config();

export default function BlogCard({ post, currentLocale, postLocalDb }: { post: any, currentLocale: string, postLocalDb: any }) {
  const postImageUrl = (post: any) => post?.image
    ? urlFor(post.image)?.width(550)?.height(310)?.url()
    : null;

  const urlFor = (source: SanityImageSource) =>
    projectId && dataset ? imageUrlBuilder({ projectId, dataset }).image(source) : null;

  return <div>
    <Link href={`/blog/${currentLocale}/${post[`slug_${currentLocale}`]?.current}`}>
      <div className="p-4 border border-neutral-200 rounded-lg hover:bg-neutral-100 transition flex flex-col gap-2">
        {postImageUrl(post) !== null ? (
          <div className="w-full h-[180px] overflow-hidden rounded-md bg-neutral-200">
            <img
              src={postImageUrl(post) || ""}
              alt={post[`title_${currentLocale}`] || "Thumbnail"}
              className="w-full h-full object-cover"
            />
          </div>
        ) : <div className="w-full h-[400px] flex flex-col items-center justify-center bg-gray-50 gap-4">
          <p className="text-4xl text-neutral-300">画像準備中</p>
          <Aperture className="w-12 h-12 text-neutral-300" />
        </div>}

        <h2 className="text-lg font-semibold mb-1 line-clamp-1 overflow-hidden text-ellipsis whitespace-nowrap text-left">
          {post[`title_${currentLocale}`] || "Untitled"}
        </h2>

        <p className="text-sm text-neutral-600 mb-2 line-clamp-3 h-[60px] text-left">
          {extractPreviewText({
            content: post[`body_${currentLocale}`],
          }) || "No preview text available"}
        </p>

        <div className="flex flex-row items-center justify-between">
          <Badge variant="outline">
            {post.category?.[`name_${currentLocale}`] || "Uncategorized"}
          </Badge>
        </div>

        <div className="text-sm flex flex-row gap-2 items-center mt-2 justify-between">
          <div className="flex flex-row gap-2 items-center flex-1">
            <Avatar className="w-6 h-6">
              <AvatarImage src={findWriter(postLocalDb?.creatorUserId)?.photo} />
              <AvatarFallback className="bg-red-white border border-neutral-400">
                {findWriter(postLocalDb?.creatorUserId)?.name?.[currentLocale].slice(0, 2) || "UT"}
              </AvatarFallback>
            </Avatar>
            {findWriter(postLocalDb?.creatorUserId)?.name?.[currentLocale] || "Urbalytics Team"}
          </div>

          <div className="text-sm">
            {new Date(post.publishedAt).toLocaleDateString()}
          </div>
        </div>
      </div>
    </Link>

  </div>
}