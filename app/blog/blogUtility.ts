import { PortableTextComponents } from "@portabletext/react";
import { SanityImageSource } from "@sanity/image-url/lib/types/types";
import imageUrlBuilder from "@sanity/image-url";
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import { blogWriter } from "@/lib/constants/blogWriter";
const { projectId, dataset } = sanityClient.config();

export function findWriter(userId: string) {
  return blogWriter.find((writer: any) => writer.userId === userId) || null;
}

export function getDescription(post: any, locale: string) {
  // combine first 3 items
  let defaultDescription = {
    "en": "Read the latest insights on Japanese real estate.",
    "ja": "日本不動産の最新の洞察を読む。",
    "zh": "阅读日本房地产的最新见解。"
  };

  let t = post?.[`body_${locale}`]?.[0]?.children?.[0]?.text
  let t2 = post?.[`body_${locale}`]?.[1]?.children?.[0]?.text
  let t3 = post?.[`body_${locale}`]?.[2]?.children?.[0]?.text

  return `${t}${t2}${t3}`.slice(0, 160) || defaultDescription[locale as keyof typeof defaultDescription]
}

export function estimateReadingTime(portableTextBody: any[]): { wordCount: number, minutes: number } {
  let totalText = "";

  for (const block of portableTextBody) {
    if (block._type === 'block' && Array.isArray(block.children)) {
      totalText += block.children.map((child: any) => child.text || "").join(" ");
    }
  }

  const wordCount = totalText.trim().length; // split(/\s+/).filter(Boolean).length;
  const averageWPM = 500; // 中文阅读速度平均 300 字/分钟
  const minutes = Math.ceil(wordCount / averageWPM);

  return { wordCount, minutes };
}

export const urlFor = (source: SanityImageSource) =>
  projectId && dataset ? imageUrlBuilder({ projectId, dataset }).image(source) : null;

export function extractPreviewText({ content, maxWords = 80 }: { content: any[], maxWords?: number }): string {
  if (!Array.isArray(content)) return '';
  for (const block of content) {
    if (block._type === 'block' && Array.isArray(block.children)) {
      const text = block.children.map((child: any) => child.text).join('');
      if (text) return text.slice(0, maxWords);
    }
  }
  return '';
}