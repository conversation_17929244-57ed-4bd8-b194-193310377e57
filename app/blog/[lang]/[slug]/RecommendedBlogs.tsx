import { sanityClient } from "@/lib/thirdParty/sanity/client";
import BlogCard from "../../BlogCard";
import { motion } from "framer-motion";
import { getTranslations } from "next-intl/server";
import { getBlog, getBlogs } from "@/actions/blog";

export default async function RecommendedBlogs({
  currentBlogId,
  currentLocale
}: {
  currentBlogId: string;
  currentLocale: string;
}) {
  const t = await getTranslations("BlogPost");

  const POSTS_QUERY = `*[
      _type == "post" &&
      !(_id in path("drafts.**")) &&
      defined(title_${currentLocale}) &&
      count(body_${currentLocale}) > 0 && _id != "${currentBlogId}"
    ]|order(publishedAt desc)[0...3]{
      _id, title_${currentLocale}, slug_${currentLocale}, publishedAt, body_${currentLocale}, image,
      category->{_id, name_${currentLocale}},
      "author": coalesce(author->name, "Urbalytics Team")
    }`;
  const postsRes = await sanityClient.fetch(POSTS_QUERY);

  const postsLocalDb = await getBlogs({});

  return <section className="max-w-[1200px] mx-auto relative text-white py-24 px-4 text-center overflow-hidden flex flex-col gap-4">
    <div className="text-black text-2xl font-bold py-4 mt-4 text-left">
      {t("relatedPosts")}
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-black">
      {postsRes
        .map((post: any) => (
          <BlogCard post={post} currentLocale={currentLocale} key={post._id} postLocalDb={postsLocalDb?.data?.find((p: any) => p.id === post._id)} />
        ))}
    </div>
  </section>;
} 