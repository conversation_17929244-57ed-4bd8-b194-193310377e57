"use client"

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { Linkedin, Mail, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";


export function WriterCard({ writer, locale }: { writer: any, locale: string }) {
  let name = writer?.name?.[locale as keyof typeof writer.name] || "Urbalytics Team";
  let title = writer?.title?.[locale as keyof typeof writer.title] || "Urbalytics Team";
  let description = writer?.description?.[locale as keyof typeof writer.description] || "-";
  let imageUrl = writer.photo;
  let mailUrl = writer.email;
  let twitterUrl = writer.twitter;
  let linkedinUrl = writer.linkedin;
  let wechatImage = writer.wechatImage;
  const router = useRouter();


  return (
    <Card className="flex flex-col sm:flex-row items-center gap-8 bg-linear-to-br from-neutral-50 to-white rounded-2xl shadow-lg p-8 mt-8">
      <div className="flex flex-col items-center shrink-0">
        <div className="w-[140px] h-[140px] overflow-hidden rounded-full">
          <Image
            src={imageUrl}
            alt={name}
            width={140}
            height={140}
            className="w-full h-full object-cover transition-transform duration-200 hover:scale-105"
          />
        </div>

        <div className="flex gap-3 mt-5">
          {mailUrl && (
            <Button
              asChild
              size="icon"
              variant="outline"
              className="rounded-full hover:bg-neutral-200"
            >
              <a href={`mailto:${mailUrl}`} aria-label="メールを送信">
                <Mail className="w-5 h-5 text-neutral-600" />
              </a>
            </Button>
          )}

          {wechatImage && (
            <Dialog>
              <VisuallyHidden>
                <DialogTitle>WeChat QR</DialogTitle>
              </VisuallyHidden>
              <DialogTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  className="rounded-full hover:bg-neutral-100"
                  onClick={e => {
                    e.stopPropagation();
                  }}
                >
                  <span aria-label="WeChat" className="flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="32" height="32">
                      <path data-name="XMLID 501 -1" d="M408.67 298.53a21 21 0 1120.9-21 20.85 20.85 0 01-20.9 21m-102.17 0a21 21 0 1120.9-21 20.84 20.84 0 01-20.9 21m152.09 118.86C491.1 394.08 512 359.13 512 319.51c0-71.08-68.5-129.35-154.41-129.35s-154.42 58.27-154.42 129.35 68.5 129.34 154.42 129.34c17.41 0 34.83-2.33 49.92-7 2.49-.86 3.48-1.17 4.64-1.17a16.67 16.67 0 018.13 2.34L454 462.83a11.62 11.62 0 003.48 1.17 5 5 0 004.65-4.66 14.27 14.27 0 00-.77-3.86c-.41-1.46-5-16-7.36-25.27a18.94 18.94 0 01-.33-3.47 11.4 11.4 0 015-9.35" />

                      <path data-name="XMLID 505 -7" d="M246.13 178.51a24.47 24.47 0 010-48.94c12.77 0 24.38 11.65 24.38 24.47 1.16 12.82-10.45 24.47-24.38 24.47m-123.06 0A24.47 24.47 0 11147.45 154a24.57 24.57 0 01-24.38 24.47M184.6 48C82.43 48 0 116.75 0 203c0 46.61 24.38 88.56 63.85 116.53C67.34 321.84 68 327 68 329a11.38 11.38 0 01-.66 4.49C63.85 345.14 59.4 364 59.21 365s-1.16 3.5-1.16 4.66a5.49 5.49 0 005.8 5.83 7.15 7.15 0 003.49-1.17L108 351c3.49-2.33 5.81-2.33 9.29-2.33a16.33 16.33 0 015.81 1.16c18.57 5.83 39.47 8.16 60.37 8.16h10.45a133.24 133.24 0 01-5.81-38.45c0-78.08 75.47-141 168.35-141h10.45C354.1 105.1 277.48 48 184.6 48" />
                    </svg>
                  </span>
                </Button>
              </DialogTrigger>
              <DialogContent className="flex flex-col items-center">
                <div className="text-lg font-bold mb-2">WeChat QRコード</div>
                <Image
                  src={wechatImage}
                  alt="WeChat QR"
                  width={200}
                  height={200}
                  className="rounded-lg"
                />
              </DialogContent>
            </Dialog>
          )}
          {twitterUrl && (
            <Button
              asChild
              size="icon"
              variant="ghost"
              className="rounded-full hover:bg-neutral-100"
            >
              <a href={twitterUrl} target="_blank" rel="noopener noreferrer" aria-label="X (Twitter)">
                <X className="w-5 h-5 text-neutral-600" />
              </a>
            </Button>
          )}
          {linkedinUrl && (
            <Button
              asChild
              size="icon"
              variant="ghost"
              className="rounded-full hover:bg-neutral-100"
            >
              <a href={linkedinUrl} target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                <Linkedin className="w-5 h-5 text-neutral-600" />
              </a>
            </Button>
          )}
        </div>
      </div>
      <div className="flex flex-col justify-center items-center sm:items-start text-center sm:text-left cursor-pointer" onClick={() => {
        router.push(`/blog/writer/${writer.userId}`);
      }}>
        <div className="font-extrabold text-2xl sm:text-3xl text-neutral-800 mb-1">{name}</div>
        <div className="text-neutral-500 text-lg mb-3">{title}</div>
        <div className="text-gray-700 leading-relaxed text-base sm:text-lg">{description}</div>
      </div>
    </Card>
  );
}