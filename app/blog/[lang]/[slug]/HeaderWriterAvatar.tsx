"use client"

import { redirect, usePathname } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useRouter } from "next/navigation";


export default function HeaderWriterAvatar({ writer, locale }: { writer: any, locale: string }) {
  const router = useRouter();

  return <div className="flex flex-row gap-2 items-center flex-1 cursor-pointer" onClick={() => {
    router.push(`/blog/writer/${writer?.userId}`);
  }}>
    <Avatar className="w-12 h-12">
      <AvatarImage src={writer?.photo} />
      <AvatarFallback className="bg-red-white border border-neutral-400">
        {writer?.name?.[locale as keyof typeof writer.name]?.slice(0, 1) || "UT"}
      </AvatarFallback>
    </Avatar>
    <div className="flex flex-col gap-1">
      <div className="text-sm font-bold">
        {writer?.name?.[locale as keyof typeof writer.name] || "Urbalytics Team"}
      </div>
      <div className="text-xs text-neutral-800">
        {writer?.title?.[locale as keyof typeof writer.title] || "Urbalytics Team"}
      </div>
    </div>
  </div>
}