

"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Link, Share, Check, Loader2, Linkedin, Facebook } from "lucide-react"
import { useState } from "react";
import { getDescription } from "@/app/blog/blogUtility";


export default function ShareButton({ locale, post }: { locale: string, post: any }) {
  const [isCopied, setIsCopied] = useState(false);
  async function handleNativeShare() {
    if (navigator.share) {
      try {
        const description = getDescription(post, locale)

        navigator.share({
          title: document.title,      // 页面标题
          text: description,          // 使用摘要
          url: window.location.href,  // 当前网址
        });
        console.log('分享成功');
      } catch (err) {
        console.error('分享失败', err);
      }
    } else {
      alert('当前浏览器不支持原生分享功能');
    }
  }

  async function copyLink() {
    await navigator.clipboard.writeText(window.location.href);
    setIsCopied(true);
  }

  async function handleLinkedinShare() {
    if (typeof window !== "undefined") {
      const title = post?.[`title_${locale}`] || "Untitled";
      const description = getDescription(post, locale)

      window.open(`https://www.linkedin.com/shareArticle?mini=true&url=${window.location.href}&title=${title}&summary=${description}&source=Urbalytics`, '_blank');
    }
  }

  async function handleFacebookShare() {
    if (typeof window !== "undefined") {
      window.open(
        `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`,
        '_blank',
        'width=600,height=400,noopener,noreferrer'
      );
    }
  }

  return <div className="flex items-center gap-2">
    <Button variant="outline" size="icon" onClick={async () => {
      await handleLinkedinShare();
    }} className="rounded-full hover:bg-neutral-200">
      <Linkedin className="w-8 h-8" />
    </Button>

    <Button variant="outline" size="icon" onClick={async () => {
      await handleFacebookShare();
    }} className="rounded-full hover:bg-neutral-200">
      <Facebook className="w-8 h-8" />
    </Button>

    <Button variant="outline" size="icon" onClick={async () => {
      await handleNativeShare();
    }} className="rounded-full hover:bg-neutral-200">
      <Share className="w-8 h-8" />
    </Button>

    <Button variant="outline" size="icon" onClick={async () => {
      await copyLink();
    }} className="rounded-full hover:bg-neutral-200">
      {isCopied ? <Check className="w-8 h-8" /> : <Link className="w-8 h-8" />}
    </Button>
  </div>
}