// app/blog/[slug]/page.tsx
import { PortableText } from '@portabletext/react';
import Link from "next/link";
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import FooterForNonLogin from "@/components/footerForNonLogin";
import { Separator } from "@/components/ui/separator";

import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import { Metadata } from "next";
import { Badge } from '@/components/ui/badge';
import { urlFor, estimateReadingTime } from "../../blogUtility";
import { components } from "../../blogComponent";
import RecommendedBlogs from "./RecommendedBlogs";
import ShareButton from "./ShareButton";
import { incrementBlogViewCount, getBlog } from "@/actions/blog";
import { blogWriter } from '@/lib/constants/blogWriter';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getDescription } from "@/app/blog/blogUtility";
import { WriterCard } from './WriterCard';
import HeaderWriterAvatar from './HeaderWriterAvatar';
import Cta from '@/app/blog/Cta';
import { BreadcrumbNavigation } from "@/components/seo/BreadcrumbSchema";
// import { redirect } from 'next/navigation';

async function fetchPost(locale: string, slug: string) {
  const decodedSlug = decodeURIComponent(slug);
  return await sanityClient.fetch(
    `*[_type == "post" && slug_${locale}.current == $slug][0]{
      title_${locale},
      body_${locale},
      image,
      _id,
      tags[]->{
        _id,
        name_${locale}
      },
      publishedAt,
      "author": coalesce(author->name, "Urbalytics Team")
    }`,
    { slug: decodedSlug }
  );
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ lang: string; slug: string }>;
}): Promise<Metadata> {
  const { lang, slug } = await params;
  const slugDecoded = decodeURIComponent(slug);
  const post = await fetchPost(lang, slugDecoded);

  const title = post?.[`title_${lang}`] || "Untitled";
  const description = getDescription(post, lang);
  const imageUrl = post?.image ? urlFor(post.image)?.width(1200)?.height(630)?.url() : undefined;

  const baseUrl = "https://www.urbalytics.jp"; // 替换成你自己的网站域名
  const canonicalUrl = `${baseUrl}/blog/${lang}/${slugDecoded}`;

  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "BlogPosting",
    "headline": title,
    "image": imageUrl || "",
    "author": {
      "@type": "Person",
      "name": post.author || "Urbalytics Team",
    },
    "publisher": {
      "@type": "Organization",
      "name": "Urbalytics",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.urbalytics.jp/icon-192x192.png", // ← 替换为实际 logo
      },
    },
    "datePublished": post.publishedAt,
    "description": description,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": canonicalUrl
    }
  }

  return {
    title,
    description,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      type: "article",
      publishedTime: post?.publishedAt,
      title,
      description,
      images: imageUrl ? [imageUrl] : [],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: imageUrl ? [imageUrl] : [],
    },
    // ✅ 新增常规 meta 标签
    metadataBase: new URL(baseUrl),
    other: {
      "og:title": title,
      "og:description": description,
      "og:image": imageUrl || "",
      "og:url": canonicalUrl,
      "og:type": "article",
      "twitter:title": title,
      "twitter:description": description,
      "twitter:image": imageUrl || "",
      "twitter:card": "summary_large_image",
      'wechat:title': title,
      'wechat:description': description,
      'wechat:image': imageUrl || "",
      "ld+json": JSON.stringify(jsonLd), // ✅ 注入结构化数据
    },
  };
}

export default async function PostPage({ params }: { params: Promise<{ lang: string, slug: string }> }) {
  const { lang, slug } = await params;
  const locale = lang;
  const t = await getTranslations();
  const decodedSlug = decodeURIComponent(slug);
  const post = await fetchPost(locale, decodedSlug);

  console.log("🔥 post", post);

  const postInDb = await incrementBlogViewCount(post?._id, locale);
  const postLocalDb = (await getBlog(post?._id))?.data || null;
  const writer = blogWriter.find((writer: any) => writer.userId === postLocalDb?.creatorUserId) || null;

  const postImageUrl = post?.image
    ? urlFor(post.image)?.auto("format")?.fit("max")?.url()
    : null;

  function generateToc(body: any[]) {
    return body
      .filter(block => block._type === 'block' && ['h2', 'h3', 'h4'].includes(block.style))
      .map(block => ({
        level: block.style,
        text: block.children?.map((child: any) => child.text).join(''),
        id: block._key, // used for anchor
      }));
  }

  const toc = post ? generateToc(post[`body_${locale}`]) : [];
  const readStats = estimateReadingTime(post[`body_${locale}`]);

  const capitalizeFirstLetter = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  return (
    <>
      <div className="container mx-auto max-w-[1200px] p-8 flex flex-col gap-4 min-h-[calc(100vh-var(--header-height)-var(--footer-height))] pt-(--header-height) mb-[120px]">

        {/* Breadcrumb Navigation */}
        <BreadcrumbNavigation
          items={[
            { name: t("Homepage.homepage"), url: 'https://www.urbalytics.jp' },
            { name: t("Homepage.blog"), url: 'https://www.urbalytics.jp/blog' },
            { name: post[`title_${locale}`] || "記事", url: `https://www.urbalytics.jp/blog/${locale}/${(await params)?.slug}` }
          ]}
        />

        {/* <div className="flex flex-row gap-2 justify-between items-center mt-2 ">
          <Link href="/blog" className="hover:underline pt-4 text-sm text-neutral-500 flex-1">
            {t("Blog.goBack")}
          </Link>
        </div> */}

        {post ? (
          <div className="flex flex-col gap-4 relative">
            <div className="flex flex-col gap-2 h-full justify-start items-start pt-4 mb-4">
              <div className="flex flex-col sm:flex-row gap-2 w-full items:start sm:items-end justify-start">
                <h1 className="text-4xl font-bold text-left text-black leading-snug flex-1 w-full sm:max-w-[90%] text-left">
                  {post[`title_${locale}`] || "Untitled"}
                </h1>

                <ShareButton locale={locale} post={post} />
              </div>

              <div className="flex flex-row justify-between items-center gap-2 border-t border-neutral-200 border-b py-4 w-full mt-4">


                {/* <div className="text-sm text-neutral-400">
                    {new Date(post.publishedAt).toLocaleDateString()}
                  </div> */}

                <HeaderWriterAvatar writer={writer} locale={locale} />
                <time className="text-sm text-neutral-500 uppercase tracking-wide text-left">
                  {/* {t("Blog.author")}: {post?.author || "Urbalytics Team"} ・ */}
                  {t("Blog.published")}:{" "}
                  {new Date(post?.publishedAt).toLocaleDateString()}
                </time>
              </div>
              {/* <Separator /> */}
            </div>

            {postImageUrl && (
              <div className="w-full h-[300px] overflow-hidden rounded-md relative">
                <Image
                  src={postImageUrl}
                  alt={post[`title_${locale}`] || "Untitled"}
                  fill
                  className="object-cover object-center rounded-md"
                  priority
                />
              </div>
            )}

            <div className="flex flex-col lg:flex-row gap-8 relative  overflow-x-hidden overflow-y-auto">
              {/* Main content */}
              <div className="lg:max-w-[calc(100%-16rem)] flex-1">
                <article className="prose prose-neutral max-w-none prose-img:rounded-md mt-4 flex flex-col gap-2">
                  <p className="text-sm text-neutral-500 text-left mb-4">
                    {t("BlogPost.wordCount")}: {readStats.wordCount} | {t("BlogPost.readingTime")}: {readStats.minutes} {t("BlogPost.minutes")} | {t("BlogPost.views")}: {postInDb.data?.[`view${capitalizeFirstLetter(locale)}`] || 0}
                  </p>

                  {Array.isArray(post[`body_${locale}`]) && (
                    <PortableText value={post[`body_${locale}`]} components={components} />
                  )}

                  {post?.tags?.length > 0 && (
                    <div className="flex flex-row gap-2 my-2">
                      {post?.tags?.map((tag: any) => (
                        <Badge key={tag._id} variant="outline" className="text-base">
                          # {tag[`name_${locale}`]?.toLowerCase()}
                        </Badge>
                      ))}
                    </div>
                  )}

                  <div className="text-sm text-neutral-500 text-left mt-[24px] mb-4">
                    {t("BlogPost.copyright")}
                  </div>

                  {writer && <WriterCard
                    writer={writer}
                    locale={locale}
                  />}
                </article>
              </div>

              {/* Sticky TOC */}
              <aside className="hidden lg:block w-64 sticky top-[calc(var(--header-height)+1rem)] h-[calc(100vh-var(--header-height))] text-sm text-gray-700 bg-white/80 backdrop-blur-xs rounded-lg p-4 shadow-xs">
                <h4 className="font-bold mb-4 text-base text-black">{t("Blog.toc")}</h4>
                <ul className="space-y-2 overflow-y-auto max-h-[calc(100vh-var(--header-height)-6rem)]">
                  {toc.map((item) => {
                    const indentClass =
                      item.level === 'h2' ? 'ml-0 font-medium' :
                        item.level === 'h3' ? 'ml-4 text-neutral-600' :
                          item.level === 'h4' ? 'ml-8 text-neutral-500' : '';
                    return (
                      <li key={item.id} className={`${indentClass} leading-relaxed hover:text-black transition-colors`}>
                        <a href={`#${item.id}`} className="hover:underline block">
                          {item.text}
                        </a>
                      </li>
                    );
                  })}
                </ul>
              </aside>
            </div>
          </div>
        ) : (
          <div>
            <h1>Post not found</h1>
          </div>
        )}
      </div>

      <Separator className="dark:border-neutral-700" />

      {post && <RecommendedBlogs currentBlogId={post?._id} currentLocale={locale} />}

      <Cta />

      <Separator className="dark:border-neutral-700" />

      <FooterForNonLogin hideLanguageSwitcher={false} />


    </>
  );
}