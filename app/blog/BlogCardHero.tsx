"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import { SanityImageSource } from "@sanity/image-url/lib/types/types";
import imageUrlBuilder from "@sanity/image-url";
import { Aperture } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { urlFor, extractPreviewText, findWriter } from "./blogUtility";
const { projectId, dataset } = sanityClient.config();

export default function BlogCard({ post, currentLocale, postLocalDb }: { post: any, currentLocale: string, postLocalDb: any }) {
  const pathname = usePathname()

  const postImageUrl = (post: any) => post?.image
    ? urlFor(post.image)?.width(550)?.height(310)?.url()
    : null;

  const urlFor = (source: SanityImageSource) =>
    projectId && dataset ? imageUrlBuilder({ projectId, dataset }).image(source) : null;

  return <Link
    href={`/blog/${currentLocale}/${post[`slug_${currentLocale}`]?.current}`}
    className="block w-full max-w-[1200px] mx-auto mt-4 mb-8"
  >
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border border-neutral-200 rounded-lg overflow-hidden hover:shadow-md transition hover:bg-neutral-100">
      <div className="w-full h-[260px] md:h-auto">
        <img
          src={postImageUrl(post) || ""}
          alt={post[`title_${currentLocale}`] || "Thumbnail"}
          className="w-full h-full object-cover"
        />
      </div>

      <div className="p-6 flex flex-col justify-between">
        <div className="flex flex-col gap-2">
          <Badge variant="outline" className="w-fit">
            {post.category?.[`name_${currentLocale}`] || "Uncategorized"}
          </Badge>

          <h2 className="text-2xl font-bold leading-tight line-clamp-2">
            {post[`title_${currentLocale}`]}
          </h2>

          <p className="text-sm text-neutral-600 line-clamp-5 leading-relaxed">
            {extractPreviewText({
              content: post[`body_${currentLocale}`],
              maxWords: 360
            })}
          </p>
        </div>

        <div className="mt-4 flex items-center justify-between text-base">
          <div className="flex items-center gap-2">
            <Avatar className="w-10 h-10">
              <AvatarImage src={findWriter(postLocalDb?.creatorUserId)?.photo} className="" />
              <AvatarFallback className="bg-red-white border">
                {findWriter(postLocalDb?.creatorUserId)?.name?.[currentLocale].slice(0, 2) || "UT"}
              </AvatarFallback>
            </Avatar>
            {findWriter(postLocalDb?.creatorUserId)?.name?.[currentLocale] || "Urbalytics Team"}
          </div>

          <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  </Link>
}