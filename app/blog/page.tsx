// app/blog/page.tsx
"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import FooterForNonLogin from "@/components/footerForNonLogin";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AnimatePresence, motion } from "framer-motion";
import { useTranslations, useLocale } from "next-intl";
import { Aperture, Loader2 } from "lucide-react";
import { useUIStore } from "@/store/ui";
import BlogCard from "./BlogCard";
import { urlFor, extractPreviewText, findWriter } from "./blogUtility";
import { getBlogs } from "@/actions/blog";
import BlogCardHero from "./BlogCardHero";
import { usePathname, useSearchParams } from "next/navigation";

export default function BlogPage() {
  const currentLocale = useLocale(); // 🔥 Use next-intl's useLocale instead of useUIStore
  const t = useTranslations("Blog");
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const category = searchParams.get("category");

  const [isLoading, setIsLoading] = useState(true);
  const [posts, setPosts] = useState<any[]>([]);
  const [postsLocalDb, setPostsLocalDb] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>(category || "");
  const [hydratedPosts, setHydratedPosts] = useState(false);

  const fetchData = async () => {
    setIsLoading(true);
    const POSTS_QUERY = `*[
      _type == "post" &&
       !(_id in path("drafts.**")) &&
      defined(title_${currentLocale}) &&
      count(body_${currentLocale}) > 0
    ]|order(publishedAt desc){
      _id, title_${currentLocale}, slug_${currentLocale}, publishedAt, body_${currentLocale}, image,
      category->{_id, name_${currentLocale}},
      "author": coalesce(author->name, "Urbalytics Team")
    }`;

    const CATEGORIES_QUERY = `*[_type == "category"]{_id, name_ja, name_en, name_zh}`;

    const postsRes = await sanityClient.fetch(POSTS_QUERY);
    const categoriesRes = await sanityClient.fetch(CATEGORIES_QUERY);

    await setPosts(postsRes);
    await setCategories(categoriesRes);
    setIsLoading(false);
  };

  useEffect(() => {
    const unsub = useUIStore.persist.onFinishHydration(() => {
      setHydratedPosts(true);
    });
    setHydratedPosts(useUIStore.persist.hasHydrated()); // 如果已经 ready

    return () => {
      unsub(); // 清理监听器
    };
  }, []);

  useEffect(() => {
    if (hydratedPosts) {
      fetchData();

      getBlogs({}).then((res) => {
        setPostsLocalDb(res?.data);
      });
    }
  }, [hydratedPosts, currentLocale]);

  const postImageUrl = (post: any) => post?.image
    ? urlFor(post.image)?.width(550)?.height(310)?.url()
    : null;

  return (
    <>
      <div className="pt-(--header-height) min-h-[calc(100vh-var(--header-height)-var(--footer-height))] w-full max-w-[1200px] mx-auto px-4 mb-40">
        <div className="text-4xl font-bold py-4 mt-4">
          {t("title")}
        </div>

        {!isLoading && posts.length > 0 && <BlogCardHero post={posts[0]} postLocalDb={postsLocalDb?.find((postLocalDb: any) => postLocalDb.id === posts[0]._id)} currentLocale={currentLocale} />}

        <div className="flex flex-wrap gap-2 mt-20 mb-6">
          <Link href="/blog">
            <button className={`px-4 py-2 rounded-md font-semibold ${!selectedCategory ? "bg-black text-white" : "bg-neutral-100"}`} onClick={() => setSelectedCategory("")}>
              {t("allPosts")}
            </button>
          </Link>
          {categories.map((cat) => (
            <Link key={cat._id} href={`/blog?category=${cat._id}`}>
              <button
                className={`px-4 py-2 rounded-md font-semibold ${selectedCategory === cat._id ? "bg-black text-white" : "bg-neutral-100"
                  }`}
                onClick={() => setSelectedCategory(cat._id)}
              >
                {cat[`name_${currentLocale}`] || "Untitled"}
              </button>
            </Link>
          ))}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 min-h-[50vh]">
          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <Loader2 className="w-10 h-10 animate-spin" />
            </div>
          ) : (
            <AnimatePresence mode="popLayout">
              {posts
                .slice(1)
                .filter((post) => selectedCategory === "" || post?.category?._id === selectedCategory)
                .map((post) => (
                  <motion.div
                    key={post._id}
                    layout // <== 关键：启用自动 layout 动画
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <BlogCard post={post} postLocalDb={postsLocalDb?.find((postLocalDb: any) => postLocalDb.id === post._id)} currentLocale={currentLocale} />
                  </motion.div>
                ))}
            </AnimatePresence>
          )}
        </div>
      </div>

      <FooterForNonLogin />
    </>
  );
}