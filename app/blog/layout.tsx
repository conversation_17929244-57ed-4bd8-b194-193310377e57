import type { Metadata } from "next";

export const metadata: Metadata = {
  title: 'ブログ - 不動産投資の最新情報とインサイト',
  description: '不動産投資に関する最新のトレンド、市場分析、投資戦略、AI技術の活用方法など、専門家による質の高い記事をお届けします。日本の不動産市場の動向を深く理解し、投資判断に役立つ情報を提供。',
  keywords: '不動産投資ブログ, 市場分析, 投資戦略, 不動産トレンド, AI不動産, プロパティテック, 日本不動産市場',
  openGraph: {
    title: 'ブログ - 不動産投資の最新情報とインサイト | Urbalytics',
    description: '不動産投資に関する最新のトレンド、市場分析、投資戦略など、専門家による質の高い記事をお届けします。',
    url: 'https://www.urbalytics.jp/blog',
    siteName: 'Urbalytics',
    images: [
      {
        url: 'https://www.urbalytics.jp/og-image-blog.jpg',
        width: 1200,
        height: 630,
        alt: 'Urbalytics ブログ - 不動産投資の最新情報',
      },
    ],
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ブログ - 不動産投資の最新情報とインサイト | Urbalytics',
    description: '不動産投資に関する最新のトレンド、市場分析、投資戦略など、専門家による質の高い記事をお届けします。',
    images: ['https://www.urbalytics.jp/og-image-blog.jpg'],
    creator: '@urbalytics',
  },
  alternates: {
    canonical: 'https://www.urbalytics.jp/blog',
    languages: {
      'ja-JP': 'https://www.urbalytics.jp/blog',
      'en-US': 'https://www.urbalytics.jp/blog?lang=en',
      'zh-CN': 'https://www.urbalytics.jp/blog?lang=zh',
    },
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const blogSchema = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "Urbalytics ブログ",
    "description": "不動産投資、市場分析、AI技術に関する最新情報とインサイトを提供するブログ。",
    "url": "https://www.urbalytics.jp/blog",
    "publisher": {
      "@type": "Organization",
      "name": "Urbalytics",
      "url": "https://www.urbalytics.jp"
    },
    "inLanguage": ["ja", "en", "zh"],
    "about": {
      "@type": "Thing",
      "name": "Real Estate Investment"
    }
  };

  return (
    <>
      {/* ブログ構造化データ */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(blogSchema)
        }}
      />

      {children}
    </>
  );
}
