import { PortableTextComponents } from "@portabletext/react";
import Link from "next/link";
import { LinkIcon, QuoteIcon } from "lucide-react";
import { ImageRenderer } from "./BlogImageRenderer";

export const components: PortableTextComponents = {
  types: {
    image: ({ value }) => <ImageRenderer value={value} />,
    code: ({ value }) => (
      <pre className="relative mt-6 mb-6 p-4 bg-neutral-100 rounded-lg overflow-x-auto text-sm font-mono leading-relaxed">
        <code>{value.code}</code>
      </pre>
    ),
  },

  marks: {
    link: ({ children, value }) => {
      const target = (value?.href || '').startsWith('http') ? '_blank' : undefined;
      return (
        <Link
          href={value?.href}
          target={target}
          rel={target === '_blank' ? 'noopener noreferrer' : undefined}
          className="text-blue-600 hover:text-blue-800 underline decoration-dotted inline-flex items-center gap-1 transition-colors"
        >
          {children}
          <LinkIcon className="w-4 h-4" />
        </Link>
      );
    },
    code: ({ children }) => (
      <code className="px-1 py-0.5 bg-neutral-100 rounded text-sm font-mono text-pink-700">
        {children}
      </code>
    ),
  },

  block: {
    normal: ({ children }) => (
      <p className="leading-relaxed text-base text-neutral-800 mb-1">{children}</p>
    ),
    h2: ({ children, value }) => (
      <h2
        id={value._key}
        className="text-3xl font-bold mt-12 mb-4 scroll-mt-24 border-b border-neutral-200 pb-2"
      >
        {children}
      </h2>
    ),
    h3: ({ children, value }) => (
      <h3
        id={value._key}
        className="text-2xl font-semibold text-neutral-800 mt-10 mb-3 scroll-mt-24"
      >
        {children}
      </h3>
    ),
    h4: ({ children, value }) => (
      <h4
        id={value._key}
        className="text-xl font-medium text-neutral-800 mt-8 mb-2 scroll-mt-24"
      >
        {children}
      </h4>
    ),
    blockquote: ({ children }) => (
      <blockquote className="relative my-4 p-5 bg-neutral-50 border-l-4 border-blue-300">
        <QuoteIcon className="absolute left-4 top-2 w-4 h-4 text-blue-300" />
        <div className="text-neutral-700 italic pl-2">{children}</div>
      </blockquote>
    ),
  },

  list: {
    bullet: ({ children }) => (
      <ul className="list-disc pl-6 space-y-1 text-neutral-800 leading-relaxed">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="list-decimal pl-6 space-y-1 text-neutral-800 leading-relaxed">
        {children}
      </ol>
    ),
  },

  listItem: {
    bullet: ({ children }) => <li className="ml-1">{children}</li>,
    number: ({ children }) => <li className="ml-1">{children}</li>,
  },
};