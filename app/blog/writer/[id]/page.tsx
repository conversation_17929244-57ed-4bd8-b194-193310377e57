"use client"

import { blogWriter } from "@/lib/constants/blogWriter";
import { useParams } from "next/navigation";
import { WriterCard } from "@/app/blog/[lang]/[slug]/WriterCard";
import { useLocale } from "next-intl";
import { getBlogs } from "@/actions/blog";
import { useEffect } from "react";
import { useState } from "react";
import { BlogProps } from "@/lib/definitions";
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import BlogCard from "../../BlogCard";
import BlogCardHero from "../../BlogCardHero";
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";

export default function WriterPage() {
  const currentLocale = useLocale(); // 🔥 Use next-intl's useLocale instead of useUIStore

  const [loading, setLoading] = useState(true);

  const params = useParams();
  const writer = blogWriter.find((writer) => writer.userId === params.id);

  const [blogsInLocalDb, setBlogsInLocalDb] = useState<BlogProps[]>([]);
  const [blogsInSanity, setBlogsInSanity] = useState<any[]>([]);

  const findBlogInLocalDb = (blogId: string) => {
    return blogsInLocalDb.find((blog) => blog.id === blogId);
  }

  const fetchBlogs = async () => {
    setLoading(true);
    const res = await getBlogs({ creatorUserId: params.id as string });
    setBlogsInLocalDb(res.data);

    let blogIds = res.data.map((blog: any) => blog.id);

    const blogs = await sanityClient.fetch(
      `*[_type == "post" && _id in $ids &&
       !(_id in path("drafts.**")) &&
      defined(title_${currentLocale}) &&
      count(body_${currentLocale}) > 0
      ]|order(publishedAt desc){
        _id,
        title_${currentLocale},
        slug_${currentLocale},
        body_${currentLocale},
        image,
        category->{
          _id,
          name_${currentLocale}
        },
        publishedAt,
        tags[]->{
          _id,
          name_${currentLocale}
        }
      }`,
      { ids: blogIds }
    );
    setBlogsInSanity(blogs);
    setLoading(false);
  }

  useEffect(() => {
    fetchBlogs();
  }, [params.id, currentLocale]);


  return (
    <div className="p-4 mt-16 max-w-(--breakpoint-lg) mx-auto">
      <WriterCard writer={writer} locale={currentLocale} />

      <div className="mt-16">
        <h2 className="text-2xl font-bold">Blog一覧</h2>
        <Separator className="my-4" />

        {loading ? <div className="flex justify-center items-center h-full">
          <Loader2 className="w-[40px] h-[40px] mt-6 animate-spin" />
        </div> : <div className="flex flex-col gap-2">
          {blogsInSanity.map((blogInSanity, index) => (
            <BlogCardHero key={index} post={blogInSanity} currentLocale={currentLocale} postLocalDb={findBlogInLocalDb(blogInSanity._id)} />
          ))}
        </div>}
      </div>
    </div>
  );
}