"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import { SanityImageSource } from "@sanity/image-url/lib/types/types";
import imageUrlBuilder from "@sanity/image-url";
import { Aperture } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { urlFor, extractPreviewText, findWriter } from "./blogUtility";
import { getBlog } from "@/actions/blog";
import { useEffect, useState } from "react";

const { projectId, dataset } = sanityClient.config();

export default function BlogCardSmall({ postId, currentLocale }: { postId: string, currentLocale: string }) {
  const [post, setPost] = useState<any>(null)
  const [postLocalDb, setPostLocalDb] = useState<any>(null)

  const fetchData = async () => {
    const post = await sanityClient.fetch(
      `*[_type == "post" && _id == "${postId}"][0]`
    )
    setPost(post)
  }

  useEffect(() => {
    if (postId) {
      fetchData()

      getBlog(postId).then((res) => {
        setPostLocalDb(res.data)
      })
    }
  }, [])

  const postImageUrl = (post: any) => post?.image
    ? urlFor(post.image)?.width(550)?.height(310)?.url()
    : null;

  const urlFor = (source: SanityImageSource) =>
    projectId && dataset ? imageUrlBuilder({ projectId, dataset }).image(source) : null;

  return <div className="max-w-[240px] overflow-hidden border border-neutral-200 rounded-lg">
    {post && postLocalDb ? <Link href={`/blog/${currentLocale}/${post[`slug_${currentLocale}`]?.current}`} target="_blank">
      <div className="p-2 bg-white border border-neutral-200 rounded-lg hover:bg-neutral-100 transition flex flex-row gap-2 overflow-hidden">
        <div className="flex flex-col gap-1 w-[80px]">
          {postImageUrl(post) !== null ? (
            <div className="overflow-hidden rounded-md bg-neutral-200">
              <img
                src={postImageUrl(post) || ""}
                alt={post[`title_${currentLocale}`] || "Thumbnail"}
                className="w-full h-full object-cover"
              />
            </div>
          ) : <div className="w-full h-[80px] flex flex-col items-center justify-center bg-gray-50 gap-4">
            <p className="text-base text-neutral-300">画像準備中</p>
            <Aperture className="w-12 h-12 text-neutral-300" />
          </div>}
        </div>

        <div className="text-xs mb-1 whitespace-wrap line-clamp-3 overflow-hidden text-left max-w-[140px]">
          {post[`title_${currentLocale}`] || "Untitled"}
        </div>
      </div>
    </Link> : <div>Loading...</div>}
  </div>
}