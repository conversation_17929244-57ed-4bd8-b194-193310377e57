"use client "

import { useTranslations } from "next-intl"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"

export default function Cta() {
  const t = useTranslations()

  return <section className="relative bg-linear-to-b from-neutral-900 to-neutral-800 text-white pt-24 px-4 text-center overflow-hidden ">
    {/* 背景装饰 */}
    <div className="absolute inset-0 opacity-10">
      <div className="absolute top-0 left-1/4 w-64 h-64 bg-[#4AC1FF] rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 w-64 h-64 bg-[#30ff68] rounded-full blur-3xl" />
    </div>

    {/* Motion Wrapper */}
    <div
      // whileHover={{ scale: 1.02 }}
      // transition={{ type: "spring", stiffness: 100, damping: 10 }}
      className="max-w-4xl mx-auto flex flex-col items-center gap-8 relative z-10"
    >
      <h2 className="text-4xl md:text-5xl font-bold leading-tight">
        {t("BlogPost.getStarted")}
      </h2>

      <div className="space-y-2 max-w-4xl">
        <p className="text-lg md:text-xl text-white/90 leading-relaxed md:leading-loose">
          {t("BlogPost.getStartedDetails1")}
        </p>
      </div>

      <Link
        href="/login?tab=signUp"
        className="group relative inline-flex items-center justify-center px-16 py-4 text-lg font-medium text-black bg-white rounded-full transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-white/20 mb-16"
      >
        <span className="relative z-10">
          {t("BlogPost.getStartedButton")}
        </span>
        <div className="absolute inset-0 bg-linear-to-r from-white to-neutral-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      </Link>

      {/* <motion.div
        whileHover={{ scale: 1.05 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        className="w-2/3"
      > */}
      <Image
        src="/assets/hp/cta.png"
        alt="get started"
        width={600}
        height={240}
        className="w-full max-w-[600px] mx-auto cursor-pointer rounded-t-lg shadow-lg transition-transform duration-300 hover:scale-105"
      />
      {/* </motion.div> */}
    </div>
  </section>;
}   