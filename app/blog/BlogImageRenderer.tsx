'use client';

import { useState } from "react";
import Image from "next/image";
import { urlFor } from "./blogUtility";
import Lightbox from "yet-another-react-lightbox";
import Zoom from "yet-another-react-lightbox/plugins/zoom";
import "yet-another-react-lightbox/styles.css";

export function ImageRenderer({ value }: { value: any }) {
  const [open, setOpen] = useState(false);
  const imageUrl = urlFor(value)?.width(1600).url();
  if (!imageUrl) return null;

  return (
    <div className="bg-neutral-50 p-2">
      <div
        onClick={() => setOpen(true)}
        className="mt-2 w-full relative overflow-hidden border border-neutral-200 cursor-zoom-in"
      >
        <Image
          src={imageUrl}
          alt={value.alt || 'image'}
          className="w-full max-h-[500px] object-cover"
          width={0}
          height={0}
          sizes="100vw"
          priority
        />
      </div>
      {value.alt && (
        <p className="mt-1 text-sm text-neutral-600 text-left">{value.alt}</p>
      )}

      {/* Lightbox */}
      <Lightbox
        open={open}
        close={() => setOpen(false)}
        slides={[{ src: imageUrl }]}
        plugins={[Zoom]}
        carousel={{ finite: true }}
        animation={{ fade: 250 }}
        render={{
          buttonPrev: () => null,   // ✅ 移除左箭头
          buttonNext: () => null    // ✅ 移除右箭头
        }}
      />
    </div>
  );
}