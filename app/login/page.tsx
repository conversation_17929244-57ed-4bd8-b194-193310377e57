import type { Metadata } from "next";
import LoginClient from "./LoginClient";

interface LoginPageProps {
  searchParams: Promise<{ tab?: string; callbackUrl?: string }>;
}

export async function generateMetadata({ searchParams }: LoginPageProps): Promise<Metadata> {
  const params = await searchParams;
  const isSignUp = params?.tab === 'signUp';

  if (isSignUp) {
    return {
      title: 'アカウント作成 - Urbalytics',
      description: 'Urbalyticsアカウントを作成して、日本最先端の不動産データプラットフォームをご利用ください。AI駆動の市場分析で不動産投資を最適化しましょう。無料でアカウント作成できます。',
      keywords: 'アカウント作成, 新規登録, サインアップ, 不動産投資, Urbalytics, 会員登録, 無料登録, 不動産データ, AI分析',
      authors: [{ name: 'Urbalytics Team' }],
      creator: 'Urbalytics',
      publisher: 'Urbalytics',
      openGraph: {
        title: 'アカウント作成 - Urbalytics | 日本最先端の不動産データプラットフォーム',
        description: 'Urbalyticsアカウントを作成して、日本最先端の不動産データプラットフォームをご利用ください。AI駆動の市場分析で不動産投資を最適化しましょう。',
        url: 'https://www.urbalytics.jp/login?tab=signUp',
        siteName: 'Urbalytics',
        locale: 'ja_JP',
        type: 'website',
        images: [
          {
            url: '/og-image.jpg',
            width: 1200,
            height: 630,
            alt: 'Urbalytics アカウント作成 - 日本最先端の不動産データプラットフォーム',
          },
        ],
      },
      twitter: {
        card: 'summary_large_image',
        title: 'アカウント作成 - Urbalytics',
        description: 'Urbalyticsアカウントを作成して、日本最先端の不動産データプラットフォームをご利用ください。',
        images: ['/og-image.jpg'],
        creator: '@urbalytics',
      },
      alternates: {
        canonical: 'https://www.urbalytics.jp/login?tab=signUp',
        languages: {
          'ja-JP': 'https://www.urbalytics.jp/login?tab=signUp',
          'en-US': 'https://www.urbalytics.jp/login?tab=signUp&lang=en',
          'zh-CN': 'https://www.urbalytics.jp/login?tab=signUp&lang=zh',
        },
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
        },
      },
    };
  }

  // Default login meta
  return {
    title: 'ログイン - Urbalytics',
    description: 'Urbalyticsにログインして、日本最先端の不動産データプラットフォームをご利用ください。AI駆動の市場分析で不動産投資を最適化しましょう。',
    keywords: 'ログイン, サインイン, 不動産投資, Urbalytics, 会員ログイン, 不動産データ, AI分析',
    authors: [{ name: 'Urbalytics Team' }],
    creator: 'Urbalytics',
    publisher: 'Urbalytics',
    openGraph: {
      title: 'ログイン - Urbalytics | 日本最先端の不動産データプラットフォーム',
      description: 'Urbalyticsにログインして、日本最先端の不動産データプラットフォームをご利用ください。AI駆動の市場分析で不動産投資を最適化しましょう。',
      url: 'https://www.urbalytics.jp/login',
      siteName: 'Urbalytics',
      locale: 'ja_JP',
      type: 'website',
      images: [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: 'Urbalytics ログイン - 日本最先端の不動産データプラットフォーム',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: 'ログイン - Urbalytics',
      description: 'Urbalyticsにログインして、日本最先端の不動産データプラットフォームをご利用ください。',
      images: ['/og-image.jpg'],
      creator: '@urbalytics',
    },
    alternates: {
      canonical: 'https://www.urbalytics.jp/login',
      languages: {
        'ja-JP': 'https://www.urbalytics.jp/login',
        'en-US': 'https://www.urbalytics.jp/login?lang=en',
        'zh-CN': 'https://www.urbalytics.jp/login?lang=zh',
      },
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
      },
    },
  };
}

export default async function LoginPage({ searchParams }: LoginPageProps) {
  const params = await searchParams;
  return <LoginClient searchParams={params} />;
}
