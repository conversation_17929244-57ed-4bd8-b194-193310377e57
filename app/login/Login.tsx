"use client"

import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Loader } from "lucide-react"
import { useRouter } from "next/navigation"
import { useTranslations } from "next-intl"
import { useSearchParams } from "next/navigation"
import { useState } from "react"
import { signIn } from "next-auth/react"
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity"
import { Drawer, DrawerTrigger } from "@/components/ui/drawer"
import ForgetPasswordModal from "./ForgetPasswordModal"
import { TriangleAlert } from "lucide-react"
import { authenticate, getGoogleLoginRedirectUrl, checkEmailAndSendEmail } from '@/actions/auth';
import { useAuthStore } from '@/store/auth';
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";

export default function Login() {
  const t = useTranslations("Login");
  const searchParams = useSearchParams();
  const [isPending, setIsPending] = useState(false);
  const [responseMessage, setResponseMessage] = useState({ success: true, message: '' });
  const callbackUrl = searchParams.get('callbackUrl') || '/ex/search';
  const router = useRouter();
  const { setCurrentUser } = useAuthStore();

  const formSubmit = async (e: any) => {
    setResponseMessage({ success: true, message: '' });
    e.preventDefault();
    let formValues = Object.fromEntries(new FormData(e.target as HTMLFormElement)) as any;

    createNewSystemUserActivityAction({
      data: {
        eventType: "LOGIN",
        route: "/login",
        eventMetadata: {
          loginType: "credentials",
          formValues: { ...formValues, password: undefined },
        },
      },
    });

    setIsPending(true)
    let res = await authenticate(formValues)
    setResponseMessage(res as any)

    if (res.success) {
      fetch('/api/auth')
        .then(response => response.json())
        .then(data => {
          setCurrentUser(data);
          router.push(formValues.redirectTo as string)
        })
        .catch(error => {
          console.error("获取用户失败:", error, "🔥");
        })
        .finally(() => {
          setIsPending(false)
          sendLark({
            message: `[🔐][ログイン]${JSON.stringify(formValues)}, result is ${res.success}`,
            url: LARK_URLS.USER_AQUISITION_CHANNEL,
          });
        })

    } else {
      setIsPending(false)
      sendLark({
        message: `[🔐][ログイン🔥 ERROR]${JSON.stringify(formValues)}, result is ${res.success}`,
        url: LARK_URLS.USER_AQUISITION_CHANNEL,
      });
    }
  }

  return <>
    <form onSubmit={async (e) => {
      await formSubmit(e);
    }} className="space-y-3">
      <div className="flex flex-col gap-6">

        <div className="flex flex-col gap-4">
          <div className="grid gap-2">
            <Label htmlFor="email">{t("email")}</Label>
            <Input
              id="email"
              type="email"
              name="email"
              placeholder={t("emailPlaceholder")}
              required
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="password">{t("password")}</Label>
            <Input
              id="password"
              type="password"
              name="password"
              placeholder={t("passwordPlaceholder")}
              required
            />
          </div>
          <input type="hidden" name="redirectTo" value={callbackUrl} />
          <Button type="submit" className="w-full" disabled={isPending} aria-disabled={isPending}>
            {isPending ? <Loader className="animate-spin h-5 w-5" /> : t("login")}
          </Button>

          <div className="flex items-center justify-start rounded-md text-xs text-muted-foreground underline underline-offset-4 mt-1 ml-1">
            <Drawer>
              <DrawerTrigger>
                {t("forgotPassword")}
              </DrawerTrigger>
              <ForgetPasswordModal />
            </Drawer>
          </div>

          {!responseMessage.success && (
            <div
              className="flex flex-row items-center gap-2 h-4"
              aria-live="polite"
              aria-atomic="true"
            >
              <TriangleAlert className="h-6 w-6 text-red-500" />
              <p className="text-sm text-red-500">{responseMessage.message}</p>
            </div>
          )}
        </div>
      </div>
    </form>

    <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
      <span className="relative z-10 bg-background px-2 text-muted-foreground">
        {t("or")}
      </span>
    </div>

    <div className="grid gap-4 sm:grid-cols-1">
      <Button variant="outline" className="w-full" onClick={async () => {
        try {
          // let res = await getGoogleLoginRedirectUrl()
          // console.log("🔥 res", res);
          // if (res?.url) {
          //   window.location.href = res.url;
          //   // redirect(res.url); // Redirect to Google login page
          // }
          // console.log("🔥 res", res);

          createNewSystemUserActivityAction({
            data: {
              eventType: "LOGIN",
              route: "/login",
              eventMetadata: {
                loginType: "google",
              },
            },
          });

          const callbackUrl = searchParams.get("callbackUrl") || "/ex/search";
          signIn('google', { callbackUrl }); // 自动重定向
          // No need update login, it is updated in the auth.ts
        } catch (error) {
          console.error("获取用户失败:", error, "🔥");
        }
      }}>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
          <path
            d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
            fill="currentColor"
          />
        </svg>
        {t("continueWithGoogle")}
      </Button>
    </div>
  </>
}