"use client"

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader } from "lucide-react";
import { useState } from "react";
import { checkEmailAndSendEmail } from "@/actions/auth";
import { useTranslations } from "next-intl";
import { CheckCircle } from "lucide-react";
import { TriangleAlert } from "lucide-react";
import { signIn } from "next-auth/react";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import { useSearchParams } from "next/navigation";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";

export default function SignUp() {
  const t = useTranslations("Login");
  const [emailAddress, setEmailAddress] = useState("");
  const [sendVerificationEmail, setSendVerificationEmail] = useState(false);
  const [responseMessage, setResponseMessage] = useState({ success: true, message: '' });
  const [isPending, setIsPending] = useState(false);
  const searchParams = useSearchParams();

  function isInWeChatBrowser() {
    return /micromessenger/i.test(navigator.userAgent);
  }

  return !sendVerificationEmail ?
    <div className="flex flex-col gap-6">
      {/* <div className="text-sm text-muted-foreground">
    {t("noAccountNote")}
  </div> */}
      <div className="grid gap-2">
        <Label htmlFor="email">{t("email")}</Label>
        <Input type="email" name="email" placeholder={t("emailPlaceholder")} required onChange={(e) => {
          setEmailAddress(e.target.value)
        }} />
      </div>

      {!responseMessage.success && (
        <div
          className="flex h-4 items-end space-x-1"
          aria-live="polite"
          aria-atomic="true"
        >
          <TriangleAlert className="h-5 w-5 text-red-500" />
          <p className="text-sm text-red-500">{responseMessage.message}</p>
        </div>
      )}

      <Button className="w-full" disabled={isPending} aria-disabled={isPending} onClick={async (e) => {
        sendLark({
          message: `新規ユーザー登録 by email: ${emailAddress}`,
          url: LARK_URLS.USER_AQUISITION_CHANNEL,
        })
        e.preventDefault();
        if (!emailAddress || !emailAddress.includes("@")) {
          setResponseMessage({ success: false, message: 'メールアドレスを入力してください。' });
          return;
        }

        setIsPending(true)
        let res = await checkEmailAndSendEmail(emailAddress);
        setIsPending(false)

        if (res.success) {
          setSendVerificationEmail(true)
        } else {
          setResponseMessage(res as any)
        }
      }}>
        {isPending ? <Loader className="animate-spin h-5 w-5" /> : t("confirm")}
      </Button>

      <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
        <span className="relative z-10 bg-background px-2 text-muted-foreground">
          {t("or")}
        </span>
      </div>

      <div className="grid gap-4 sm:grid-cols-1">
        <Button variant="outline" className="w-full" onClick={async () => {
          if (isInWeChatBrowser()) {
            alert("WeChat browser is not supported for google registration/login for now. Please use other browsers. 无法在微信浏览器中完成谷歌注册/登录。请点击右上角“在浏览器中打开”（Safari / Chrome）并完成注册。");
            return;
          }

          try {
            // let res = await getGoogleLoginRedirectUrl()
            // console.log("🔥 res", res);
            // if (res?.url) {
            //   window.location.href = res.url;
            //   // redirect(res.url); // Redirect to Google login page
            // }
            // console.log("🔥 res", res);

            createNewSystemUserActivityAction({
              data: {
                eventType: "LOGIN",
                route: "/login",
                eventMetadata: {
                  loginType: "google",
                },
              },
            });

            const callbackUrl = searchParams.get("callbackUrl") || "/ex/search";
            signIn('google', { callbackUrl }); // 自动重定向
            // No need update login, it is updated in the auth.ts
          } catch (error) {
            console.error("获取用户失败:", error, "🔥");
          }
        }}>
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path
              d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
              fill="currentColor"
            />
          </svg>
          {t("continueWithGoogle")}
        </Button>
      </div>

    </div> : <div className="flex flex-col gap-2 text-center flex-col items-center justify-center">
      <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
      <div className="text-sm text-muted-foreground">
        {t("signupEmailSent")}
      </div>
      <div className="text-sm text-muted-foreground">
        {t("signupEmailSentDescription")}
      </div>
    </div>
}