"use client"
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer"
import { But<PERSON> } from "@/components/ui/button"
import { Loader, RotateCcwIcon } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useState } from "react"
import { sendResetPasswordEmail } from "@/actions/users"
import { toast } from "@/hooks/use-toast"


export default function ForgetPasswordModal() {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  const handleSubmit = async () => {
    setError("")
    setSuccess("")

    if (!validateEmail(email)) {
      setError("有効なメールアドレスを入力してください。")
      return
    }

    setIsLoading(true)

    try {
      console.log("Resetting password for:", email)

      // 模拟异步操作，例如 API 请求
      let res = await sendResetPasswordEmail(email)

      if (res.success) {
        await new Promise((resolve) => setTimeout(resolve, 1000))
        setSuccess("パスワードリセットのリンクを送信しました。メールを確認してください。")
      } else {
        // toast({
        //   title: "エラー",
        //   description: res.message || "エラーが発生しました。もう一度お試しください。",
        //   variant: "destructive"
        // })
        setError(res.message || "エラーが発生しました。もう一度お試しください。")
      }
    } catch (err) {
      setError("エラーが発生しました。もう一度お試しください。")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <DrawerContent className="h-2/3 text-center">
      <DrawerHeader className="flex flex-col items-center justify-center">
        <RotateCcwIcon className="w-36 h-36" />
        <DrawerTitle className="mt-2 text-2xl font-bold">パスワードをお忘れですか？</DrawerTitle>
        <DrawerDescription className="mt-2">
          Urbalyticsにログインするメールアドレスを入力してください。
        </DrawerDescription>
      </DrawerHeader>

      <div className="flex flex-col items-center justify-center p-4 gap-2 w-1/3 mx-auto">
        <Input
          type="email"
          placeholder="Email"
          className="w-full"
          onChange={(e) => setEmail(e.target.value)}
          value={email}
        />

        {error && <p className="text-red-500 text-sm">{error}</p>}
        {success && <p className="text-green-600 text-sm">{success}</p>}

        <Button className="w-full mt-2" onClick={handleSubmit} disabled={isLoading}>
          {isLoading ? <Loader className="animate-spin h-5 w-5" /> : "パスワードをリセット"}
        </Button>
      </div>
    </DrawerContent>
  )
}