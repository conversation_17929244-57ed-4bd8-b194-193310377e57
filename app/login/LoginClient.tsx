'use client';

import Link from "next/link";
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { useRouter } from "next/navigation";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import Image from "next/image";
import Login from "./Login";
import SignUp from "./SignUp";

interface LoginClientProps {
  searchParams: { tab?: string; callbackUrl?: string };
}

export default function LoginClient({ searchParams: serverSearchParams }: LoginClientProps) {
  const { setCurrentUser } = useAuthStore();
  const t = useTranslations("Login");
  const searchParams = useSearchParams();
  const [isPending, setIsPending] = useState(false);
  const [responseMessage, setResponseMessage] = useState({ success: true, message: '' });
  const callbackUrl = searchParams.get('callbackUrl') || serverSearchParams.callbackUrl || '/ex/search';
  const router = useRouter();
  const [tab, setTab] = useState(searchParams.get("tab") || serverSearchParams.tab || "login");

  // Update tab when URL changes
  useEffect(() => {
    const currentTab = searchParams.get("tab") || "login";
    setTab(currentTab);
  }, [searchParams]);

  // JSON-LD structured data for better SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": tab === "signUp" ? "アカウント作成 - Urbalytics" : "ログイン - Urbalytics",
    "description": tab === "signUp"
      ? "Urbalyticsアカウントを作成して、日本最先端の不動産データプラットフォームをご利用ください。"
      : "Urbalyticsにログインして、日本最先端の不動産データプラットフォームをご利用ください。",
    "url": `https://www.urbalytics.jp/login${tab === "signUp" ? "?tab=signUp" : ""}`,
    "isPartOf": {
      "@type": "WebSite",
      "name": "Urbalytics",
      "url": "https://www.urbalytics.jp"
    },
    "potentialAction": {
      "@type": tab === "signUp" ? "RegisterAction" : "LoginAction",
      "target": `https://www.urbalytics.jp/login${tab === "signUp" ? "?tab=signUp" : ""}`
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-background p-6 md:p-10 relative">
      <Image
        src="/assets/fuzzy/bg-common.jpg"
        alt="bg-common"
        className="w-full h-full object-cover absolute blur-[1px] top-0 left-0"
        width={1000}
        height={1000}
      />

      <div className="absolute top-0 left-0 w-full h-full bg-black/50"></div>

      <div className="w-full max-w-sm z-10 bg-white p-4 px-6 rounded-lg">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col items-center gap-2">
            <a
              href="#"
              className="flex flex-col items-center gap-2 font-medium"
            >
              <span className="sr-only">Urbalytics.</span>
            </a>
            <h1 className="text-xl font-bold">{t("welcome")}</h1>
            <div className="text-center text-sm mt-4 mx-2">
              <Tabs id="loginTabs" value={tab} onValueChange={(value) => {
                router.push(`/login?tab=${value}${callbackUrl ? `&callbackUrl=${callbackUrl}` : ""}`);
                setTab(value);
                setResponseMessage({ success: true, message: '' });

                createNewSystemUserActivityAction({
                  data: {
                    eventType: "BUTTON_CLICK",
                    route: "/login",
                    eventMetadata: {
                      buttonName: "loginTabSwitcher",
                      tab: value,
                    },
                  },
                });
              }} className="w-[300px] w-max-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="login">{t("login")}</TabsTrigger>
                  <TabsTrigger value="signUp">{t("signUp")}</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          {tab === "login" && (
            <Login />
          )}

          {tab === "signUp" && (
            <SignUp />
          )}

          <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 [&_a]:hover:text-primary">
            {t.rich("termsAgreement", {
              tos: (chunks) => <Link href="/tos" className="underline">{chunks}</Link>,
              pp: (chunks) => <Link href="/pp" className="underline">{chunks}</Link>,
            })}
          </div>
        </div>
      </div>
    </div>
    </>
  )
}
