// app/wiki/layout.tsx
import { ReactNode } from 'react'
import FileTree from './components/FileTree'
import TableOfContents from './components/TableOfContents'
import WikiNavigation from './components/WikiNavigation'
import WikiShareButton from './components/WikiShareButton'

interface WikiLayoutProps {
  children: ReactNode
}

export default function WikiLayout({ children }: WikiLayoutProps) {
  return (
    <div className="bg-background min-h-screen">
      <div className="flex max-w-7xl mx-auto">
        <aside className="hidden lg:block flex-shrink-0 w-64">
          <div className="sticky top-4 h-[calc(100vh-2rem)] overflow-y-auto">
            <FileTree />
          </div>
        </aside>

        <main className="flex-1 min-w-0 px-6">
          <div className="max-w-4xl mx-auto py-4">
            <article className="bg-card rounded-lg p-4">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1"></div>
                <WikiShareButton />
              </div>
              <div className="prose prose-lg max-w-none prose-headings:scroll-mt-20 dark:prose-invert">
                {children}
              </div>
              <WikiNavigation />
            </article>
          </div>
        </main>

        <aside className="hidden xl:block flex-shrink-0 w-64">
          <div className="sticky top-4 h-[calc(100vh-2rem)] overflow-y-auto">
            <TableOfContents />
          </div>
        </aside>
      </div>
    </div>
  )
}