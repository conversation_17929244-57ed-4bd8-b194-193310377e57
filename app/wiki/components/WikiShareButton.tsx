"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Share, Check, Linkedin, Facebook } from "lucide-react"
import { useState } from "react"
import { usePathname } from "next/navigation"

export default function WikiShareButton() {
  const pathname = usePathname()

  // Extract locale from wiki path
  const pathSegments = pathname.split('/')
  const locale = pathSegments.includes('en') ? 'en' :
                 pathSegments.includes('ja') ? 'ja' :
                 pathSegments.includes('zh') ? 'zh' : 'ja'
  const [isCopied, setIsCopied] = useState(false)

  const getDescription = () => {
    const descriptions = {
      ja: "Urbalyticsプラットフォームの包括的なドキュメント",
      en: "Comprehensive documentation for the Urbalytics platform",
      zh: "Urbalytics 平台的综合文档"
    }
    return descriptions[locale as keyof typeof descriptions] || descriptions.ja
  }

  async function handleNativeShare() {
    if (navigator.share) {
      try {
        const title = document.title
        const description = getDescription()

        await navigator.share({
          title: title,
          text: description,
          url: window.location.href,
        })
        console.log('🔥 分享成功')
      } catch (err) {
        console.error('🔥 分享失败', err)
      }
    } else {
      alert('当前浏览器不支持原生分享功能')
    }
  }

  async function copyLink() {
    try {
      await navigator.clipboard.writeText(window.location.href)
      setIsCopied(true)
      setTimeout(() => setIsCopied(false), 2000)
    } catch (err) {
      console.error('🔥 复制链接失败', err)
    }
  }

  async function handleLinkedinShare() {
    if (typeof window !== "undefined") {
      const title = document.title || "Urbalytics Documentation"
      const description = getDescription()

      window.open(
        `https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(title)}&summary=${encodeURIComponent(description)}&source=Urbalytics`,
        '_blank'
      )
    }
  }

  async function handleFacebookShare() {
    if (typeof window !== "undefined") {
      window.open(
        `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`,
        '_blank',
        'width=600,height=400,noopener,noreferrer'
      )
    }
  }

  return (
    <div className="flex items-center gap-2">
      <Button 
        variant="outline" 
        size="icon" 
        onClick={handleLinkedinShare}
        className="rounded-full hover:bg-neutral-200"
        title="LinkedIn で共有"
      >
        <Linkedin className="w-4 h-4" />
      </Button>

      <Button 
        variant="outline" 
        size="icon" 
        onClick={handleFacebookShare}
        className="rounded-full hover:bg-neutral-200"
        title="Facebook で共有"
      >
        <Facebook className="w-4 h-4" />
      </Button>

      <Button 
        variant="outline" 
        size="icon" 
        onClick={handleNativeShare}
        className="rounded-full hover:bg-neutral-200"
        title="ネイティブ共有"
      >
        <Share className="w-4 h-4" />
      </Button>

      <Button 
        variant="outline" 
        size="icon" 
        onClick={copyLink}
        className="rounded-full hover:bg-neutral-200"
        title="リンクをコピー"
      >
        {isCopied ? <Check className="w-4 h-4" /> : <Link className="w-4 h-4" />}
      </Button>
    </div>
  )
}
