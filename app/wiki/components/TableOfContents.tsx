'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { Hash, GitPullRequest, Bug, ArrowUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import path from 'path'

interface TocItem {
  id: string
  text: string
  level: number
}

interface TableOfContentsProps {
  className?: string
  currentLang?: string
}

export default function TableOfContents({ className = '', currentLang = 'ja' }: TableOfContentsProps) {
  const [tocItems, setTocItems] = useState<TocItem[]>([])
  const [activeId, setActiveId] = useState<string>('')
  const pathname = usePathname()

  // 🔥 Get language-specific text
  const getTexts = (lang: string) => {
    const texts = {
      ja: {
        title: '目次',
        backToTop: 'ページトップへ',
        filePR: 'このページを編集',
        addIssue: '問題を報告'
      },
      en: {
        title: 'Table of Contents',
        backToTop: 'Back to Top',
        filePR: 'Edit this page',
        addIssue: 'Report an issue'
      },
      zh: {
        title: '目录',
        backToTop: '返回顶部',
        filePR: '编辑此页面',
        addIssue: '报告问题'
      }
    }
    return texts[lang as keyof typeof texts] || texts.ja
  }

  const texts = getTexts(currentLang)

  // 🔥 Generate GitHub URLs
  const getGitHubUrls = () => {
    // Extract the file path from the current pathname
    const filePath = pathname.replace('/wiki/', 'app/wiki/') + '/page.mdx'

    const baseUrl = 'https://github.com/tan-c/urv2'
    const editUrl = `${baseUrl}/edit/main/${filePath}`
    const issueUrl = `${baseUrl}/issues/new?title=Issue with ${pathname}&body=Issue found on page: ${pathname}%0A%0ADescription:%0A`

    return { editUrl, issueUrl }
  }

  const { editUrl, issueUrl } = getGitHubUrls()

  useEffect(() => {
    // 🔥 Extract headings from the page
    const headings = document.querySelectorAll('h2, h3, h4, h5, h6')
    const items: TocItem[] = []

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1))
      const text = heading.textContent || ''
      let id = heading.id

      // Generate ID if not present
      if (!id) {
        id = text
          .toLowerCase()
          .replace(/[^\w\s-]/g, '')
          .replace(/\s+/g, '-')
          .trim()
        
        // Add index to ensure uniqueness
        if (document.getElementById(id)) {
          id = `${id}-${index}`
        }
        
        heading.id = id
      }

      items.push({ id, text, level })
    })

    setTocItems(items)

    // 🔥 Use scroll event for active section tracking
    const handleScroll = () => {
      const headings = document.querySelectorAll('h2, h3, h4, h5, h6')
      let currentActiveId = ''
      let closestDistance = Infinity

      // Find the heading closest to the top of the viewport
      headings.forEach((heading) => {
        const rect = heading.getBoundingClientRect()
        const distanceFromTop = Math.abs(rect.top - 120) // 120px offset for header

        // Only consider headings that are visible or just above the viewport
        if (rect.top <= 120 && rect.bottom >= 0 && distanceFromTop < closestDistance) {
          closestDistance = distanceFromTop
          currentActiveId = heading.id
        }
      })

      // If no heading is in the target zone, find the first visible one
      if (!currentActiveId) {
        headings.forEach((heading) => {
          const rect = heading.getBoundingClientRect()
          if (rect.top >= 0 && rect.top <= window.innerHeight && !currentActiveId) {
            currentActiveId = heading.id
          }
        })
      }

      // Only update if activeId has changed
      if (currentActiveId !== activeId) {
        setActiveId(currentActiveId)
      }
    }

    // Set up scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Delay initial call to ensure page is fully rendered
    const timeoutId = setTimeout(() => {
      handleScroll()
    }, 100)

    return () => {
      window.removeEventListener('scroll', handleScroll)
      clearTimeout(timeoutId)
    }
  }, [currentLang, pathname])

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id)
    if (element) {
      // 🔥 Calculate offset to account for fixed header
      const headerOffset = 80 // Adjust this value based on your header height
      const elementPosition = element.offsetTop
      const offsetPosition = elementPosition - headerOffset

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      })
    }
  }

  if (tocItems.length === 0) {
    return null
  }

  return (
    <div className={`w-full bg-card ${className} sticky top-0`}>
      <div className="pt-4">
        <nav className="space-y-1">
          {tocItems.map((item, index) => (
            <div
              key={index}
              onClick={() => scrollToHeading(item.id)}
              className={`w-full cursor-pointer justify-start text-left h-auto py-2 font-normal ${
                activeId === item.id ? 'bg-accent font-medium' : ''
              }`}
              style={{
                paddingLeft: `${(item.level - 1) * 12 + 8}px`
              }}
            >
              <span className="truncate block text-xs">{item.text}</span>
            </div>
          ))}
        </nav>

        {/* 🔥 Additional navigation actions */}
        <div className="mt-6 pt-4">
          <Separator className="mb-4" />
          <div className="space-y-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="w-full justify-start h-auto py-2 text-xs"
            >
              <ArrowUp className="w-3 h-3 mr-2" />
              {texts.backToTop}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              asChild
              className="w-full justify-start h-auto py-2 text-xs"
            >
              <a
                href={editUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center"
              >
                <GitPullRequest className="w-3 h-3 mr-2" />
                {texts.filePR}
              </a>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              asChild
              className="w-full justify-start h-auto py-2 text-xs"
            >
              <a
                href={issueUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center"
              >
                <Bug className="w-3 h-3 mr-2" />
                {texts.addIssue}
              </a>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
