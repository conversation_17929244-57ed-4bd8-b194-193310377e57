// components/LanguageSyncClient.tsx
'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import { useEffect } from 'react'
import { getCookie, setCookie } from 'cookies-next'

export default function LanguageSyncClient() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const pathSegments = pathname.split('/')
  const pathLang = pathSegments.includes('en') ? 'en' :
                   pathSegments.includes('ja') ? 'ja' :
                   pathSegments.includes('zh') ? 'zh' : null

  const urlLang = searchParams.get('lang')
  const cookieLang = getCookie('locale') as string
  const detectedLang = pathLang || urlLang

  useEffect(() => {
    if (detectedLang && detectedLang !== cookieLang) {
      console.log('🔥 [LanguageSyncClient] Updating locale cookie from', cookieLang, 'to', detectedLang)
      setCookie('locale', detectedLang, { path: '/' })
    }
  }, [pathLang, urlLang, cookieLang])

  return null
}