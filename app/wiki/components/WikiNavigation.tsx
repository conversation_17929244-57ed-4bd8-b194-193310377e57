'use client'

import { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'

interface WikiPage {
  name: string
  path: string
  type: 'file' | 'folder'
  children?: WikiPage[]
}

interface WikiNavigationProps {
  currentLang?: string
}

// 🔥 Get language-specific navigation text
const getNavigationTexts = (lang: string) => {
  const texts = {
    ja: {
      previous: '前のページ',
      next: '次のページ'
    },
    en: {
      previous: 'Previous',
      next: 'Next'
    },
    zh: {
      previous: '上一页',
      next: '下一页'
    }
  }
  return texts[lang as keyof typeof texts] || texts.ja
}

// 🔥 Flatten nested file structure into a linear array
const flattenPages = (pages: WikiPage[]): WikiPage[] => {
  const result: WikiPage[] = []

  const traverse = (items: WikiPage[]) => {
    for (const item of items) {
      if (item.type === 'file') {
        result.push(item)
      }
      if (item.children) {
        traverse(item.children)
      }
    }
  }

  traverse(pages)
  return result
}

export default function WikiNavigation({ currentLang = 'ja' }: WikiNavigationProps) {
  const pathname = usePathname()
  const [pages, setPages] = useState<WikiPage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const texts = getNavigationTexts(currentLang)

  // 🔥 Fetch file tree and flatten it for navigation
  useEffect(() => {
    const fetchPages = async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/wiki/file-tree?lang=${currentLang}`)
        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            const flatPages = flattenPages(result.data)
            setPages(flatPages)
          }
        }
      } catch (error) {
        console.error('🔥 [WikiNavigation] Error fetching pages:', error)
        // Fallback to static structure
      } finally {
        setIsLoading(false)
      }
    }

    fetchPages()
  }, [currentLang])

  // Don't render while loading
  if (isLoading) {
    return null
  }

  // Find current page index
  const currentIndex = pages.findIndex(page => page.path === pathname)

  if (currentIndex === -1) {
    // Current page not found in navigation structure
    return null
  }

  const prevPage = currentIndex > 0 ? pages[currentIndex - 1] : null
  const nextPage = currentIndex < pages.length - 1 ? pages[currentIndex + 1] : null

  // Don't show navigation if there's no prev or next page
  if (!prevPage && !nextPage) {
    return null
  }
  
  return (
    <div className="mt-8">
      <Separator className="mb-8" />
      <nav className="flex justify-between items-center">
        <div className="flex-1">
          {prevPage && (
            <Button variant="ghost" asChild className="h-auto p-4 justify-start">
              <Link href={prevPage.path} className="group flex items-center space-x-3">
                <ChevronLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
                <div className="text-left">
                  <div className="text-xs uppercase tracking-wide text-muted-foreground">{texts.previous}</div>
                  <div className="font-medium text-foreground">{prevPage.name}</div>
                </div>
              </Link>
            </Button>
          )}
        </div>

        <div className="flex-1 flex justify-end">
          {nextPage && (
            <Button variant="ghost" asChild className="h-auto p-4 justify-end">
              <Link href={nextPage.path} className="group flex items-center space-x-3">
                <div className="text-right">
                  <div className="text-xs uppercase tracking-wide text-muted-foreground">{texts.next}</div>
                  <div className="font-medium text-foreground">{nextPage.name}</div>
                </div>
                <ChevronRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          )}
        </div>
      </nav>
    </div>
  )
}
