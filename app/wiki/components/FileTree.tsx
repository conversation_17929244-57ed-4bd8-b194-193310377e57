'use client'

import { useState, useEffect } from 'react'
import { ChevronRight, ChevronDown, FileText, Folder, FolderOpen } from 'lucide-react'
import Link from 'next/link'

interface FileNode {
  name: string
  path: string
  type: 'file' | 'folder'
  children?: FileNode[]
}

interface FileTreeNodeProps {
  node: FileNode
  level: number
  currentPath?: string
}

function FileTreeNode({ node, level, currentPath }: FileTreeNodeProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const isActive = currentPath === node.path
  const hasChildren = node.children && node.children.length > 0

  const handleToggle = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded)
    }
  }

  return (
    <div className=''>
      <div
        className={`flex items-center py-2.5 px-2 rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors ${
          isActive ? 'bg-accent text-accent-foreground font-medium' : 'text-muted-foreground'
        }`}
        style={{ paddingLeft: `${level * 16 + 12}px` }}
      >
        {hasChildren ? (
          <button onClick={handleToggle} className="mr-2 p-0.5 hover:bg-muted rounded">
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </button>
        ) : (
          <div className="w-6 mr-2" />
        )}

        <div className="flex items-center mr-2">
          {node.type === 'folder' ? (
            isExpanded ? (
              <FolderOpen className="w-4 h-4 text-primary" />
            ) : (
              <Folder className="w-4 h-4 text-primary" />
            )
          ) : (
            <FileText className="w-4 h-4 text-muted-foreground" />
          )}
        </div>

        <Link
          href={node.path}
          className={`flex-1 text-sm truncate hover:text-foreground transition-colors ${
            isActive ? 'font-medium text-foreground' : ''
          }`}
        >
          {node.name}
        </Link>
      </div>

      {hasChildren && isExpanded && (
        <div className="space-y-1 mt-1">
          {node.children!.map((child, index) => (
            <FileTreeNode
              key={index}
              node={child}
              level={level + 1}
              currentPath={currentPath}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface FileTreeProps {
  currentPath?: string
  currentLang?: string
}

export default function FileTree({ currentPath, currentLang = 'ja' }: FileTreeProps) {
  const [wikiStructure, setWikiStructure] = useState<FileNode[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchFileTree = async () => {
    setIsLoading(true)
    setError(null)

    try {
      console.log(`🔥 [FileTree] Fetching file tree for language: ${currentLang}`)
      const response = await fetch(`/api/wiki/file-tree?lang=${currentLang}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch file tree: ${response.statusText}`)
      }

      const result = await response.json()

      if (result.success) {
        setWikiStructure(result.data)
        if (result.fallback) {
          console.log(`🔥 [FileTree] Using fallback structure for ${currentLang}`)
        }
      } else {
        throw new Error(result.error || 'Failed to load file tree')
      }
    } catch (err) {
      console.error(`🔥 [FileTree] Error fetching file tree:`, err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsLoading(false)
    }
  }

  // 🔥 Fetch file tree from API
  useEffect(() => {
    fetchFileTree()
  }, [currentLang])

  // 🔥 Get language-specific header
  const getHeader = (lang: string) => {
    const headers = {
      ja: '📚 ドキュメント',
      en: '📚 Documentation',
      zh: '📚 文档'
    }
    return headers[lang as keyof typeof headers] || headers.ja
  }

  return (
    <div className="w-full bg-card border-neutral-200">
      {/* <div className="pb-4 border-b border-neutral-200">
        <h2 className="text-lg font-semibold text-foreground">
          {getHeader(currentLang)}
        </h2>
      </div> */}
      <div className="pt-4 space-y-2">
        {isLoading ? (
          <div className="animate-pulse space-y-3 px-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
            <div className="h-4 bg-muted rounded w-2/3"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
          </div>
        ) : error ? (
          <div className="text-sm text-destructive px-2 py-4">
            Error loading file tree: {error}
          </div>
        ) : wikiStructure.length > 0 ? (
          wikiStructure.map((node, index) => (
            <FileTreeNode
              key={index}
              node={node}
              level={0}
              currentPath={currentPath}
            />
          ))
        ) : (
          <div className="text-sm text-muted-foreground px-2 py-4">
            No documentation available for {currentLang}
          </div>
        )}
      </div>
    </div>
  )
}
