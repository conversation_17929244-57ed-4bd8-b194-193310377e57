"use client";

import { useState, useEffect } from "react";
import dayjs from "dayjs";

interface LastUpdatedProps {
  lang?: string;
  className?: string;
}

export default function LastUpdated({
  lang = "ja",
  className = "",
}: LastUpdatedProps) {
  const [mounted, setMounted] = useState(false);

  // Only render on client side to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // 🔥 Get language-specific format and text
  const getDateFormat = (language: string) => {
    const formats = {
      ja: {
        format: "YYYY年M月D日",
        text: "最終更新",
      },
      en: {
        format: "MMMM D, YYYY",
        text: "Last updated",
      },
      zh: {
        format: "YYYY年M月D日",
        text: "最后更新",
      },
    };
    return formats[language as keyof typeof formats] || formats.ja;
  };

  const { format, text } = getDateFormat(lang);

  // Don't render anything until mounted on client
  if (!mounted) {
    return (
      <div
        className={`border-t border-neutral-200 pt-4 text-sm text-muted-foreground text-right mt-8 pt-2 ${className}`}
      >
        {text}: ...
      </div>
    );
  }

  const formattedDate = dayjs().format(format);

  return (
    <div
      className={`border-t border-neutral-200 pt-4 text-sm text-muted-foreground text-right mt-8 pt-2 ${className}`}
    >
      {text}: {formattedDate}
    </div>
  );
}
