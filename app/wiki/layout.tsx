import FooterForNonLogin from '@/components/footerForNonLogin'
import WikiLayout from './WikiLayout'
import type { Metadata } from "next";
import { generateWikiMetadata } from './utils/wikiMetadata';

// 🔥 Enhanced metadata generation using the new utility
export const metadata: Metadata = generateWikiMetadata('ja', '');

export default function RootLayout({ children } : { children: React.ReactNode }) {
  // 🔥 Import the enhanced structured data utility
  const { generateWikiStructuredData } = require('./utils/wikiMetadata');
  const knowledgeBaseSchema = generateWikiStructuredData('ja', '');

  return (
    <div className='pt-(--header-height) w-full'>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(knowledgeBaseSchema)
        }}
      />

      <WikiLayout>
        {children}
      </WikiLayout>
      <FooterForNonLogin />
    </div>
  )
}