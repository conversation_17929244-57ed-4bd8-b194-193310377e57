import { promises as fs } from 'fs'
import path from 'path'

export interface FileNode {
  name: string
  path: string
  type: 'file' | 'folder'
  children?: FileNode[]
}

// 🔥 Extract title from MDX file's first content line (skip imports)
async function extractTitleFromMDX(filePath: string): Promise<string | null> {
  try {
    const content = await fs.readFile(filePath, 'utf-8')

    // Get the first non-empty line that's not an import statement
    const lines = content.split('\n')
    for (const line of lines) {
      const trimmedLine = line.trim()

      // Skip empty lines and import statements
      if (trimmedLine.length === 0 ||
          trimmedLine.startsWith('import ') ||
          trimmedLine.startsWith('export ') ||
          trimmedLine.startsWith('//') ||
          trimmedLine.startsWith('/*') ||
          trimmedLine.startsWith('---')) {
        continue
      }

      // Extract title and clean it up
      let title = trimmedLine

      // Remove markdown heading syntax if present
      if (title.startsWith('# ')) {
        title = title.substring(2).trim()
      } else if (title.startsWith('## ')) {
        title = title.substring(3).trim()
      } else if (title.startsWith('### ')) {
        title = title.substring(4).trim()
      }

      // Remove common emoji patterns at the start
      title = title.replace(/^[🔥🔌❓📚🛠️🏠📊🔧⚡️💡🎯✨🚀📝📋📊📈📉💻🖥️📱⌨️🖱️💾💿📀💽💻🖨️📠☎️📞📟📺📻🎵🎶🎤🎧📢📣📯🔔🔕🎼🎹🥁🎷🎺🎸🪕🎻🎲🎯🎳🎮🕹️🎰🎲]+\s*/, '')

      return title || null
    }

    return null
  } catch (error) {
    console.error(`🔥 [extractTitleFromMDX] Error reading file ${filePath}:`, error)
    return null
  }
}

// 🔥 Fallback display names for directories and when file reading fails
const getFallbackDisplayNames = (lang: string) => {
  const displayNames = {
    ja: {
      'api': 'API リファレンス',
      'faq': 'FAQ',
      'development': '開発ガイド',
      'tutorials': 'チュートリアル',
      'auth': '認証',
      'properties': 'プロパティ',
      'users': 'ユーザー',
      'setup': 'セットアップ',
      'deployment': 'デプロイ',
      'testing': 'テスト',
      'quickstart': 'クイックスタート',
      'basics': '基本的な使い方',
      'advanced': '高度な機能'
    },
    en: {
      'api': 'API Reference',
      'faq': 'FAQ',
      'development': 'Development Guide',
      'tutorials': 'Tutorials',
      'auth': 'Authentication',
      'properties': 'Properties',
      'users': 'Users',
      'setup': 'Setup',
      'deployment': 'Deployment',
      'testing': 'Testing',
      'quickstart': 'Quick Start',
      'basics': 'Basics',
      'advanced': 'Advanced'
    },
    zh: {
      'api': 'API 参考',
      'faq': '常见问题',
      'development': '开发指南',
      'tutorials': '教程',
      'auth': '身份验证',
      'properties': '属性',
      'users': '用户',
      'setup': '设置',
      'deployment': '部署',
      'testing': '测试',
      'quickstart': '快速开始',
      'basics': '基础知识',
      'advanced': '高级功能'
    }
  }

  return displayNames[lang as keyof typeof displayNames] || displayNames.ja
}

// 🔥 Generate file tree from actual filesystem
export async function generateFileTree(lang: string): Promise<FileNode[]> {
  const wikiPath = path.join(process.cwd(), 'app', 'wiki', lang)
  const fallbackDisplayNames = getFallbackDisplayNames(lang)
  
  try {
    // Check if language directory exists
    await fs.access(wikiPath)
  } catch {
    // If language directory doesn't exist, return empty array
    console.log(`🔥 [generateFileTree] Language directory not found: ${wikiPath}`)
    return []
  }
  
  async function buildTree(dirPath: string, urlPath: string): Promise<FileNode[]> {
    const items: FileNode[] = []
    
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true })
      
      // Sort entries: directories first, then files
      const sortedEntries = entries.sort((a, b) => {
        if (a.isDirectory() && !b.isDirectory()) return -1
        if (!a.isDirectory() && b.isDirectory()) return 1
        return a.name.localeCompare(b.name)
      })
      
      for (const entry of sortedEntries) {
        const fullPath = path.join(dirPath, entry.name)
        const urlFullPath = `${urlPath}/${entry.name}`
        
        if (entry.isDirectory()) {
          // Handle directories
          const children = await buildTree(fullPath, urlFullPath)

          if (children.length > 0) {
            items.push({
              name: fallbackDisplayNames[entry.name as keyof typeof fallbackDisplayNames] || entry.name,
              path: urlFullPath,
              type: 'folder',
              children
            })
          }
        } else if (entry.name.endsWith('.mdx') || entry.name.endsWith('.md')) {
          // Handle MDX/MD files - extract title from file content
          let displayName = entry.name
          let filePath = urlFullPath

          // Try to extract title from the MDX file
          const extractedTitle = await extractTitleFromMDX(fullPath)
          if (extractedTitle) {
            displayName = extractedTitle
          } else {
            // Fallback to filename without extension
            displayName = entry.name.replace(/\.mdx?$/, '')
          }

          // Special handling for page.mdx files
          if (entry.name === 'page.mdx') {
            // For root page.mdx, use the parent directory path
            filePath = urlPath
          } else {
            // Remove .mdx extension from path
            filePath = urlFullPath.replace(/\.mdx?$/, '')
          }

          items.push({
            name: displayName,
            path: filePath,
            type: 'file'
          })
        }
      }
    } catch (error) {
      console.error(`🔥 [generateFileTree] Error reading directory ${dirPath}:`, error)
    }
    
    return items
  }
  
  const tree = await buildTree(wikiPath, `/wiki/${lang}`)
  
  // Handle root page.mdx specially - it should be the first item
  const rootPageIndex = tree.findIndex(item => item.path === `/wiki/${lang}`)
  if (rootPageIndex > 0) {
    const rootPage = tree.splice(rootPageIndex, 1)[0]
    tree.unshift(rootPage)
  }
  
  return tree
}

// 🔥 Client-side fallback structure for when filesystem access is not available
export function getFallbackFileTree(lang: string): FileNode[] {
  const langPath = `/wiki/${lang}`
  const fallbackDisplayNames = getFallbackDisplayNames(lang)
  
  const structures = {
    ja: [
      { name: 'はじめに', path: langPath, type: 'file' as const },
      {
        name: fallbackDisplayNames['api'],
        path: `${langPath}/api`,
        type: 'folder' as const,
        children: [
          { name: fallbackDisplayNames['auth'], path: `${langPath}/api/auth`, type: 'file' as const },
          { name: fallbackDisplayNames['properties'], path: `${langPath}/api/properties`, type: 'file' as const },
          { name: fallbackDisplayNames['users'], path: `${langPath}/api/users`, type: 'file' as const }
        ]
      },
      { name: fallbackDisplayNames['faq'], path: `${langPath}/faq`, type: 'file' as const }
    ],
    en: [
      { name: 'Getting Started', path: langPath, type: 'file' as const },
      {
        name: fallbackDisplayNames['api'],
        path: `${langPath}/api`,
        type: 'folder' as const,
        children: [
          { name: fallbackDisplayNames['auth'], path: `${langPath}/api/auth`, type: 'file' as const },
          { name: fallbackDisplayNames['properties'], path: `${langPath}/api/properties`, type: 'file' as const },
          { name: fallbackDisplayNames['users'], path: `${langPath}/api/users`, type: 'file' as const }
        ]
      },
      { name: fallbackDisplayNames['faq'], path: `${langPath}/faq`, type: 'file' as const }
    ],
    zh: [
      { name: '入门指南', path: langPath, type: 'file' as const }
    ]
  }
  
  return structures[lang as keyof typeof structures] || structures.ja
}
