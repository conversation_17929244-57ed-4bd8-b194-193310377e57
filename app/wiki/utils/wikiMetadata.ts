import { Metadata, Viewport } from "next"

export interface WikiPageMetadata {
  title: string
  description: string
  keywords: string
  ogTitle?: string
  ogDescription?: string
  canonicalPath?: string
}

// 🔥 Enhanced metadata configurations for different wiki pages
export const wikiPageMetadata: Record<string, Record<string, WikiPageMetadata>> = {
  // Root wiki pages
  "": {
    ja: {
      title: "Documentation Wiki - Urbalytics開発者向けドキュメント",
      description: "Urbalyticsプラットフォームの包括的なドキュメント。API仕様、開発ガイド、技術資料、使用方法など、開発者が必要とする全ての情報を提供します。不動産投資・分析に特化したプロパティテックソリューション。",
      keywords: "Urbalytics ドキュメント, API仕様, 開発ガイド, 技術資料, 不動産API, プロパティテック開発, 不動産投資, 市場分析, データ分析, 開発者リソース",
      ogTitle: "Urbalytics Documentation Wiki - 不動産投資プラットフォームの開発者ドキュメント",
      ogDescription: "不動産投資・分析に特化したUrbalyticsプラットフォームの包括的なドキュメント。API仕様から実装例まで、開発者が必要とする全ての情報を提供。"
    },
    en: {
      title: "Documentation Wiki - Urbalytics Developer Documentation",
      description: "Comprehensive documentation for the Urbalytics platform. API specifications, development guides, technical resources, and everything developers need for real estate investment and analysis solutions.",
      keywords: "Urbalytics documentation, API specs, development guide, technical resources, real estate API, proptech development, property investment, market analysis, data analytics, developer resources",
      ogTitle: "Urbalytics Documentation Wiki - Real Estate Investment Platform Developer Docs",
      ogDescription: "Complete developer documentation for Urbalytics real estate investment and analysis platform. From API specifications to implementation examples."
    },
    zh: {
      title: "文档 Wiki - Urbalytics 开发者文档",
      description: "Urbalytics 平台的综合文档。API 规范、开发指南、技术资源以及开发者所需的一切信息。专注于房地产投资和分析的PropTech解决方案。",
      keywords: "Urbalytics 文档, API 规范, 开发指南, 技术资源, 房地产API, 房产科技开发, 房地产投资, 市场分析, 数据分析, 开发者资源",
      ogTitle: "Urbalytics 文档 Wiki - 房地产投资平台开发者文档",
      ogDescription: "专注于房地产投资和分析的Urbalytics平台综合开发者文档。从API规范到实现示例的完整指南。"
    }
  },
  
  // User guide pages
  "user-guide": {
    ja: {
      title: "ユーザーガイド - Urbalytics使用方法",
      description: "Urbalyticsプラットフォームの詳細な使用方法。物件検索、価格分析、投資判断のための実践的なガイドとチュートリアル。",
      keywords: "Urbalytics 使用方法, ユーザーガイド, 物件検索, 価格分析, 投資判断, 不動産投資ガイド, 操作方法",
      canonicalPath: "/user-guide"
    },
    en: {
      title: "User Guide - How to Use Urbalytics",
      description: "Detailed user guide for the Urbalytics platform. Practical guides and tutorials for property search, price analysis, and investment decision-making.",
      keywords: "Urbalytics user guide, how to use, property search, price analysis, investment decisions, real estate investment guide, tutorial",
      canonicalPath: "/user-guide"
    },
    zh: {
      title: "用户指南 - 如何使用Urbalytics",
      description: "Urbalytics平台的详细使用指南。房产搜索、价格分析、投资决策的实用指南和教程。",
      keywords: "Urbalytics 用户指南, 使用方法, 房产搜索, 价格分析, 投资决策, 房地产投资指南, 教程",
      canonicalPath: "/user-guide"
    }
  },

  // API documentation
  "api": {
    ja: {
      title: "API仕様 - Urbalytics開発者API",
      description: "Urbalytics APIの詳細仕様。RESTful API、GraphQLエンドポイント、認証方法、レスポンス形式など、開発者向けの完全なAPIリファレンス。",
      keywords: "Urbalytics API, API仕様, RESTful API, GraphQL, API認証, 開発者API, 不動産データAPI",
      canonicalPath: "/api"
    },
    en: {
      title: "API Specifications - Urbalytics Developer API",
      description: "Detailed Urbalytics API specifications. Complete API reference for developers including RESTful API, GraphQL endpoints, authentication methods, and response formats.",
      keywords: "Urbalytics API, API specifications, RESTful API, GraphQL, API authentication, developer API, real estate data API",
      canonicalPath: "/api"
    },
    zh: {
      title: "API 规范 - Urbalytics 开发者API",
      description: "Urbalytics API的详细规范。包括RESTful API、GraphQL端点、认证方法、响应格式等完整的开发者API参考。",
      keywords: "Urbalytics API, API 规范, RESTful API, GraphQL, API 认证, 开发者API, 房地产数据API",
      canonicalPath: "/api"
    }
  },

  // Development guide
  "development": {
    ja: {
      title: "開発ガイド - Urbalytics統合開発",
      description: "Urbalyticsプラットフォームとの統合開発ガイド。SDK使用方法、実装例、ベストプラクティス、トラブルシューティング。",
      keywords: "Urbalytics 開発ガイド, SDK, 統合開発, 実装例, ベストプラクティス, 開発者向け",
      canonicalPath: "/development"
    },
    en: {
      title: "Development Guide - Urbalytics Integration",
      description: "Integration development guide for the Urbalytics platform. SDK usage, implementation examples, best practices, and troubleshooting.",
      keywords: "Urbalytics development guide, SDK, integration development, implementation examples, best practices, developer guide",
      canonicalPath: "/development"
    },
    zh: {
      title: "开发指南 - Urbalytics 集成开发",
      description: "Urbalytics平台的集成开发指南。SDK使用方法、实现示例、最佳实践和故障排除。",
      keywords: "Urbalytics 开发指南, SDK, 集成开发, 实现示例, 最佳实践, 开发者指南",
      canonicalPath: "/development"
    }
  }
}

// 🔥 Generate comprehensive metadata for wiki pages
export function generateWikiMetadata(
  locale: string = 'ja',
  pagePath: string = '',
  customMetadata?: Partial<WikiPageMetadata>
): Metadata {
  // Extract page key from path (handle static structure)
  const pageKey = pagePath.replace(/^\/wiki\/[a-z]{2}\/?/, '').replace(/^\//, '') || ''

  // Get base metadata for the page
  const pageMetadata = wikiPageMetadata[pageKey]?.[locale] || wikiPageMetadata[''][locale]

  // Merge with custom metadata if provided
  const metadata = { ...pageMetadata, ...customMetadata }

  const baseUrl = 'https://www.urbalytics.jp'

  // 🔥 Build canonical URL based on static structure
  let canonicalUrl: string
  if (pageKey) {
    // For sub-pages, use the language-specific path
    canonicalUrl = `${baseUrl}/wiki/${locale}${metadata.canonicalPath || `/${pageKey}`}`
  } else {
    // For root pages, use language-specific URLs
    canonicalUrl = locale === 'ja' ? `${baseUrl}/wiki/ja` : `${baseUrl}/wiki/${locale}`
  }
  
  // 🔥 Language-specific URLs for static structure
  const languageUrls = {
    'ja-JP': pageKey ? `${baseUrl}/wiki/ja${metadata.canonicalPath || `/${pageKey}`}` : `${baseUrl}/wiki/ja`,
    'en-US': pageKey ? `${baseUrl}/wiki/en${metadata.canonicalPath || `/${pageKey}`}` : `${baseUrl}/wiki/en`,
    'zh-CN': pageKey ? `${baseUrl}/wiki/zh${metadata.canonicalPath || `/${pageKey}`}` : `${baseUrl}/wiki/zh`
  }

  return {
    title: metadata.title,
    description: metadata.description,
    keywords: metadata.keywords,
    authors: [{ name: 'Urbalytics Team' }],
    creator: 'Urbalytics',
    publisher: 'Urbalytics',
    applicationName: 'Urbalytics',
    referrer: 'origin-when-cross-origin',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    verification: {
      google: process.env.GOOGLE_SITE_VERIFICATION,
    },
    openGraph: {
      title: metadata.ogTitle || metadata.title,
      description: metadata.ogDescription || metadata.description,
      url: canonicalUrl,
      siteName: 'Urbalytics',
      images: [
        {
          url: `${baseUrl}/og-image-wiki.jpg`,
          width: 1200,
          height: 630,
          alt: metadata.ogTitle || metadata.title,
          type: 'image/jpeg',
        },
        {
          url: `${baseUrl}/og-image-wiki-square.jpg`,
          width: 600,
          height: 600,
          alt: metadata.ogTitle || metadata.title,
          type: 'image/jpeg',
        },
      ],
      locale: locale === 'ja' ? 'ja_JP' : locale === 'en' ? 'en_US' : 'zh_CN',
      type: pageKey ? 'article' : 'website',
      ...(pageKey && {
        section: 'Documentation',
        publishedTime: new Date().toISOString(),
        modifiedTime: new Date().toISOString(),
        authors: ['Urbalytics Team'],
        tags: metadata.keywords.split(', '),
      }),
    },
    twitter: {
      card: 'summary_large_image',
      title: metadata.ogTitle || metadata.title,
      description: metadata.ogDescription || metadata.description,
      images: {
        url: `${baseUrl}/og-image-wiki.jpg`,
        alt: metadata.ogTitle || metadata.title,
      },
      creator: '@urbalytics',
      site: '@urbalytics'
    },
    alternates: {
      canonical: canonicalUrl,
      languages: languageUrls,
    },
    robots: {
      index: true,
      follow: true,
      nocache: false,
      googleBot: {
        index: true,
        follow: true,
        noimageindex: false,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    category: 'technology',
    classification: 'Real Estate Technology Documentation',
    other: {
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'apple-mobile-web-app-title': 'Urbalytics Wiki',
      'mobile-web-app-capable': 'yes',
      'msapplication-TileColor': '#ffffff',
      'msapplication-config': '/browserconfig.xml',
      'theme-color': '#ffffff',
    },
  }
}

// 🔥 Generate structured data for wiki pages
export function generateWikiStructuredData(
  locale: string = 'ja',
  pagePath: string = '',
  pageTitle?: string
) {
  const baseUrl = 'https://www.urbalytics.jp'
  const pageKey = pagePath.replace(/^\/wiki\/[a-z]{2}\/?/, '').replace(/^\//, '') || ''

  // 🔥 Build canonical URL for static structure
  let canonicalUrl: string
  if (pageKey) {
    canonicalUrl = `${baseUrl}/wiki/${locale}/${pageKey}`
  } else {
    canonicalUrl = `${baseUrl}/wiki/${locale}`
  }
  
  const localeTexts = {
    ja: {
      siteName: 'Urbalytics Wiki',
      description: '不動産投資に関する包括的な知識ベース。用語解説、投資手法、市場分析方法などを詳しく解説。',
      about: 'Real Estate Investment Knowledge'
    },
    en: {
      siteName: 'Urbalytics Wiki',
      description: 'Comprehensive knowledge base for real estate investment. Detailed explanations of terminology, investment methods, market analysis techniques.',
      about: 'Real Estate Investment Knowledge'
    },
    zh: {
      siteName: 'Urbalytics Wiki',
      description: '房地产投资综合知识库。详细解释术语、投资方法、市场分析技术等。',
      about: 'Real Estate Investment Knowledge'
    }
  }
  
  const texts = localeTexts[locale as keyof typeof localeTexts] || localeTexts.ja
  
  const baseSchema = {
    "@context": "https://schema.org",
    "@type": pageKey ? "TechArticle" : "WebSite",
    "name": pageTitle || texts.siteName,
    "headline": pageTitle || texts.siteName,
    "description": texts.description,
    "url": canonicalUrl,
    "inLanguage": locale,
    "isAccessibleForFree": true,
    "publisher": {
      "@type": "Organization",
      "name": "Urbalytics",
      "url": "https://www.urbalytics.jp",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`,
        "width": 200,
        "height": 60
      },
      "sameAs": [
        "https://twitter.com/urbalytics",
        "https://www.linkedin.com/company/urbalytics",
        "https://github.com/urbalytics"
      ]
    },
    "about": [
      {
        "@type": "Thing",
        "name": texts.about
      },
      {
        "@type": "Thing",
        "name": "PropTech"
      },
      {
        "@type": "Thing",
        "name": "Real Estate API"
      }
    ],
    "audience": [
      {
        "@type": "Audience",
        "audienceType": "Real Estate Investors"
      },
      {
        "@type": "Audience",
        "audienceType": "Software Developers"
      },
      {
        "@type": "Audience",
        "audienceType": "Property Analysts"
      }
    ],
    "keywords": pageKey ? "real estate, API, documentation, proptech, investment analysis" : "real estate, documentation, wiki, API, developer guide",
    ...(pageKey && {
      "dateModified": new Date().toISOString(),
      "datePublished": new Date().toISOString(),
      "author": {
        "@type": "Organization",
        "name": "Urbalytics Team",
        "url": "https://www.urbalytics.jp"
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": canonicalUrl
      },
      "image": {
        "@type": "ImageObject",
        "url": `${baseUrl}/og-image-wiki.jpg`,
        "width": 1200,
        "height": 630
      }
    })
  }

  // Add breadcrumb for sub-pages
  if (pageKey) {
    const pathParts = pageKey.split('/')
    const breadcrumbList = {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Wiki",
          "item": `${baseUrl}/wiki/${locale}`
        }
      ]
    }

    pathParts.forEach((part, index) => {
      const position = index + 2
      const itemPath = pathParts.slice(0, index + 1).join('/')
      breadcrumbList.itemListElement.push({
        "@type": "ListItem",
        "position": position,
        "name": part.charAt(0).toUpperCase() + part.slice(1).replace(/-/g, ' '),
        "item": `${baseUrl}/wiki/${locale}/${itemPath}`
      })
    })

    return [baseSchema, breadcrumbList]
  }

  return baseSchema
}

// 🔥 Generate viewport configuration for wiki pages
export function generateWikiViewport(): Viewport {
  return {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
    colorScheme: 'light dark',
    themeColor: [
      { media: '(prefers-color-scheme: light)', color: '#ffffff' },
      { media: '(prefers-color-scheme: dark)', color: '#000000' }
    ],
  }
}
