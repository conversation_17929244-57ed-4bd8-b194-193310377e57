import { Metada<PERSON>, Viewport } from 'next'
import { generateWikiMetadata, generateWikiStructuredData, generateWikiViewport } from '../utils/wikiMetadata'

// 🔥 Generate metadata for English wiki pages
export const metadata: Metadata = generateWikiMetadata('en', '')

// 🔥 Generate viewport for English wiki pages
export const viewport: Viewport = generateWikiViewport()

interface EnWikiLayoutProps {
  children: React.ReactNode
}

export default function EnWikiLayout({ children }: EnWikiLayoutProps) {
  // Generate structured data for English pages
  const structuredData = generateWikiStructuredData('en', '')

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      {children}
    </>
  )
}
