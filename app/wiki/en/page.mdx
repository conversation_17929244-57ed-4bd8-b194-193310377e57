import LastUpdated from '../components/LastUpdated'

# Urbalytics Wiki

Welcome to the comprehensive documentation for the Urbalytics platform!

## Overview
This documentation system provides the following information:
- **User Guide**: End-user operation procedures
- **Partner Guide**: Guide for business partners and integrators
- **Development Guide**: Platform usage and implementation examples
- **Technical Resources**: Architecture and design philosophy
- **API Specifications**: Detailed API reference for developers

## Key Features

### 🏠 Real Estate Data Analysis
- Market trend visualization
- Price prediction models
- Regional statistical information

### 📊 Valuation Report Generation
- Customizable dashboards
- PDF export functionality
- Real-time data updates

### 🔧 Developer Tools
- RESTful API
- GraphQL endpoints
- SDKs and libraries

{/* ## Navigation

- [API Reference](/wiki/en/api)
- [Development Guide](/wiki/en/development)
- [Tutorials](/wiki/en/tutorials)
- [FAQ](/wiki/en/faq) */}




<LastUpdated lang="en" />