import { Metadata, Viewport } from 'next'
import { generateWikiMetadata, generateWikiStructuredData, generateWikiViewport } from '../utils/wikiMetadata'

// 🔥 Generate metadata for Chinese wiki pages
export const metadata: Metadata = generateWikiMetadata('zh', '')

// 🔥 Generate viewport for Chinese wiki pages
export const viewport: Viewport = generateWikiViewport()

interface ZhWikiLayoutProps {
  children: React.ReactNode
}

export default function ZhWikiLayout({ children }: ZhWikiLayoutProps) {
  // Generate structured data for Chinese pages
  const structuredData = generateWikiStructuredData('zh', '')

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      {children}
    </>
  )
}
