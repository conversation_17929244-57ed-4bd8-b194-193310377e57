import { Metadata, Viewport } from 'next'
import { generateWikiMetadata, generateWikiStructuredData, generateWikiViewport } from '../../utils/wikiMetadata'

// 🔥 Generate metadata for Chinese user guide pages
export const metadata: Metadata = generateWikiMetadata('zh', '/user-guide', {
  title: '用户指南 - 如何使用Urbalytics识别投资机会',
  description: '通过真实案例学习如何使用Urbalytics平台进行房地产投资分析。5步识别「买了就亏」的房产，掌握价格分析和投资决策技巧。',
  keywords: 'Urbalytics 用户指南, 房地产投资分析, 价格分析, 投资决策, 房产评估, 实例教程, 投资技巧',
  ogTitle: '用户指南 - Urbalytics房地产投资分析实战教程',
  ogDescription: '通过真实案例学习房地产投资分析技巧，掌握如何识别优质投资机会，避免投资陷阱。',
  canonicalPath: '/user-guide'
})

// 🔥 Generate viewport for Chinese user guide pages
export const viewport: Viewport = generateWikiViewport()

interface ZhUserGuideLayoutProps {
  children: React.ReactNode
}

export default function ZhUserGuideLayout({ children }: ZhUserGuideLayoutProps) {
  // Generate structured data for Chinese user guide pages
  const structuredData = generateWikiStructuredData('zh', '/user-guide', '用户指南 - 如何使用Urbalytics')

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      {children}
    </>
  )
}
