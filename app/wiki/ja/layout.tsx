import { Metadata, Viewport } from 'next'
import { generateWikiMetadata, generateWikiStructuredData, generateWikiViewport } from '../utils/wikiMetadata'

// 🔥 Generate metadata for Japanese wiki pages
export const metadata: Metadata = generateWikiMetadata('ja', '')

// 🔥 Generate viewport for Japanese wiki pages
export const viewport: Viewport = generateWikiViewport()

interface JaWikiLayoutProps {
  children: React.ReactNode
}

export default function JaWikiLayout({ children }: JaWikiLayoutProps) {
  // Generate structured data for Japanese pages
  const structuredData = generateWikiStructuredData('ja', '')

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      {children}
    </>
  )
}
