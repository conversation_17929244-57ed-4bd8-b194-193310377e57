'use client'

import { redirect } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useHomepageCounts } from "@/hooks/use-homepage-counts";
import { CACHE_CONFIG, formatCacheExpiry } from "@/lib/cache-config";
import { RefreshCw, Trash2, Clock, Database, Settings, Bug } from "lucide-react";

export default function DevToolsPage() {
  // 🚀 Only allow access in development environment
  if (process.env.NODE_ENV === 'production') {
    redirect('/');
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl pt-(--header-height)">
      <div className="mb-6">
        <h1 className="text-3xl font-bold flex items-center gap-2">
          <Settings className="h-8 w-8" />
          🛠️ Development Tools
        </h1>
        <p className="text-gray-600 mt-2">
          Internal tools for debugging and testing (Development only)
        </p>
      </div>

      <Tabs defaultValue="cache" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="cache">Cache Testing</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="debug">Debug Info</TabsTrigger>
        </TabsList>

        <TabsContent value="cache">
          <CacheTestingTab />
        </TabsContent>

        <TabsContent value="database">
          <DatabaseTab />
        </TabsContent>

        <TabsContent value="debug">
          <DebugInfoTab />
        </TabsContent>
      </Tabs>
    </div>
  );
}

function CacheTestingTab() {
  const {
    data: countsData,
    isLoading,
    error,
    refetch,
    clearCache
  } = useHomepageCounts();

  const config = CACHE_CONFIG.HOMEPAGE_COUNTS;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Homepage Counts Cache Test
          {isLoading && <RefreshCw className="h-4 w-4 animate-spin" />}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Data Display */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {countsData.newlyCreatedCount.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">新規作成数</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {countsData.priceChangeCount.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">価格変更数</div>
          </div>
        </div>

        {/* Status */}
        <div className="flex gap-2">
          <Badge variant={isLoading ? "default" : "secondary"}>
            {isLoading ? "Loading..." : "Cached"}
          </Badge>
          {error && (
            <Badge variant="destructive">
              Error: {error.message}
            </Badge>
          )}
        </div>

        {/* Cache Info */}
        <div className="text-xs text-gray-500 space-y-1 bg-gray-50 p-3 rounded">
          <div className="flex items-center gap-2">
            <Clock className="h-3 w-3" />
            Cache Key: {config.key}
          </div>
          <div>⏰ Cache Expiry: {formatCacheExpiry(config.expiry)}</div>
          <div>💾 Storage: localStorage</div>
          <div>📝 Description: {config.description}</div>
          <div>
            📅 Last Fetch: {
              typeof window !== 'undefined' 
                ? localStorage.getItem(`${config.key}-timestamp`) 
                  ? new Date(parseInt(localStorage.getItem(`${config.key}-timestamp`)!)).toLocaleString()
                  : 'Never'
                : 'N/A'
            }
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button 
            onClick={() => refetch()} 
            disabled={isLoading}
            size="sm"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Force Refresh
          </Button>
          <Button 
            onClick={() => {
              clearCache();
              window.location.reload();
            }} 
            variant="outline"
            size="sm"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Cache
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

function DatabaseTab() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Database Tools
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600">Database debugging tools will be added here...</p>
      </CardContent>
    </Card>
  );
}

function DebugInfoTab() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bug className="h-5 w-5" />
          Debug Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm space-y-2">
          <div><strong>Environment:</strong> {process.env.NODE_ENV}</div>
          <div><strong>Next.js Version:</strong> {process.env.NEXT_PUBLIC_VERSION || 'Unknown'}</div>
          <div><strong>Build Time:</strong> {process.env.NEXT_PUBLIC_BUILD_TIME || 'Unknown'}</div>
          <div><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent : 'N/A'}</div>
        </div>
      </CardContent>
    </Card>
  );
}
