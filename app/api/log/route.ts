import { NextResponse, userAgent } from "next/server";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { SystemUserActivityEventTypeEnum } from "@prisma/client";
import { auth } from "@/lib/auth";
import { normalizeRoute } from "@/actions/systemUserActivity";
import { Router } from "lucide-react";

export async function POST(req: Request) {
  const referer = req.headers.get('referer')
  const origin = new URL(referer || '').origin

  const allowedOrigins = ['http://localhost:3000', "https://www.urbalytics.jp"];

  if (!allowedOrigins.includes(origin)) {
    return new Response('Forbidden', { status: 403 })
  }

  try {
    const body = await req.json();
    const { route, eventType, eventMetadata } = body;

    const session = await auth();

    const userId = session?.user?.id || null;

    if (!eventType || !Object.values(SystemUserActivityEventTypeEnum).includes(eventType) || !route) {
      return NextResponse.json({ message: "Invalid request" }, { status: 400 });
    }

    const log = await prisma.systemUserActivity.create({
      data: {
        userId,
        route: route,
        routeNormalized: await normalizeRoute(route),
        eventType,
        ...(eventMetadata ? { eventMetadata } : {}),
        userAgent: req.headers.get('user-agent') ?? undefined,
        referer: req.headers.get('referer') ?? undefined,
        recordDate: new Date(),
        createdAt: new Date(),
      },
    })

    return NextResponse.json({ message: 'success' }, { status: 200 })
  } catch (err: any) {
    logger.error('🔥 Activity logging error 🔥', err)
    return NextResponse.json({ error: 'Failed to insert activity log' }, { status: 500 })
  }
}