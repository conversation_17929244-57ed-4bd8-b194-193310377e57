import dayjs from "dayjs";
// FIMXE: this wont work out of the box.. because the event wont be passed directly

module.exports.run = async (event: any, context: any) => {
  const phoneFrom =
    event["Details"]["ContactData"]["CustomerEndpoint"]["Address"];
  const phoneTo = event["Details"]["ContactData"]["SystemEndpoint"]["Address"];

  console.log('🔥  event["Details"]["ContactData"]🔥 🔥');
  console.log(event["Details"]["ContactData"]);

  const input = event["Details"]["Parameters"]["input"];
  const type = event["Details"]["Parameters"]["type"];

  let text = null;

  if (type === "時間外") {
    text = `[時間外][着信履歴][${dayjs()
      .add(9, "hours")
      .format("YYYY-MM-DD HH:mm:ss")}]\n${phoneFrom}から${phoneTo}`;
  } else {
    console.log("🔥 input🔥 🔥");
    console.log(input);

    const phoneType: { [key: string]: { number: string; poc: string } } = {
      1: {
        number: "TLL管理",
        poc: "李さん(入居者)",
      },
      2: {
        number: "TLL",
        poc: "李さん(物件確認)",
      },
      0: {
        number: "TLL",
        poc: "李さん(そのほか)",
      },
      default: {
        number: "TLL",
        poc: "李さん",
      },
    };
    let pocData = phoneType[input];
    if (input === undefined) pocData = phoneType["default"];

    console.log("🔥 pocData🔥 🔥");
    console.log(pocData);

    text = `[着信履歴][${dayjs()
      .add(9, "hours")
      .format("YYYY-MM-DD HH:mm:ss")}]\n${phoneFrom}から${phoneTo}(${pocData["number"]
      })に電話をしました,${pocData["poc"]
      }ご確認ください, ユーザー入力は${input}です`;
  }

  console.log("🔥 text for the thingy 🔥 🔥");
  console.log(text);

  try {
    await fetch("https://open.larksuite.com/open-apis/bot/v2/hook/1bdb27a1-14c4-41b9-880d-c20756f13a11", {
      method: "POST",
      // mode: "cors", // no-cors, *cors, same-origin
      // cache: "no-cache", // *default, no-cache, reload, force-cache, only-if-cached
      // credentials: "same-origin", // include, *same-origin, omit
      headers: {
        "Content-Type": "application/json",
        // token: "99bcb745-279a-42a9-97f0-8c2c9724934a",
        // 'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: JSON.stringify({
        msg_type: "text",
        content: { text: text },
      }),

      // redirect: "follow", // manual, *follow, error
      // referrerPolicy: "no-referrer", // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
      // body: JSON.stringify(data), // 本文のデータ型は "Content-Type" ヘッダーと一致させる必要があります
    });
  } catch (err: any) {
    console.log("🔥 err 🔥");
    console.log(err, err.stack); // an error occurred
  }
};
