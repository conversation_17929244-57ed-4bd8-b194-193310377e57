import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  let page, browser;

  try {
    browser = await getChromeBrowser();
    page = await getPageWithRandomUserAgent(browser);

    // *********************************************************
    // *********************************************************
    // Step 1:: login in
    // *********************************************************

    await page.goto("https://accounts.craigslist.org/login", {
      waitUntil: "networkidle2",
    });

    const USERNAME = "<EMAIL>";
    const PASSWORD = "vnh4vfb@kjn@AKU@gzn";

    await page.waitForSelector("#inputEmailHandle");
    await page.type("#inputEmailHandle", USERNAME);
    await page.type("#inputPassword", PASSWORD);
    await new Promise(resolve => setTimeout(resolve, 1000));

    await page.click("#login");
    await new Promise(resolve => setTimeout(resolve, 1000));

    // *********************************************************
    // Step 2:: post
    // *********************************************************

    await page.goto("https://tokyo.craigslist.org/", {
      waitUntil: "networkidle2",
    });
    await new Promise(resolve => setTimeout(resolve, 1000));
    const postButotn = "a.cl-goto-post";
    await page.waitForSelector(postButotn);
    await page.click(postButotn);
    await new Promise(resolve => setTimeout(resolve, 1000));

    const houseOfferButton = "li.start-of-grouping:nth-child(4) input";
    await page.waitForSelector(houseOfferButton);
    await page.click(houseOfferButton);
    await page.click("button.pickbutton");
    await new Promise(resolve => setTimeout(resolve, 4000));

    const apartmentButton = "label:nth-child(2) input";
    await page.waitForSelector(apartmentButton);
    await page.click(apartmentButton);
    // await page.click("button.pickbutton");
    await new Promise(resolve => setTimeout(resolve, 4000));

    const data = {
      title: "⭐NO gift NO deposit NO agency fee | Jujo station 15min | 16-17m2",
      city: "kita-ku",
      description:
        "No key money, no deposit, no agency / 26.5K JPY initial guarantee company fees one time + 20k fire insurance,２Year / monthly rent at 53K(including management fee) + 440JPY (24H life support. 📍Jyujyo/higashi jyujyo station 15 min walk (Saikyo line/Keihintohoku line) | Ikebukuro station 5 min by train | Shinjuku station 11min  🏠 2 level Wood apartment | built in 1996 | 1K | 18.6m2 🌳 Located in residential area,  quiet street 🗺️ Convenience store 400m, supermarket 350m, Hospital 680m  ✅ Picture is actual building / room with staging | 写真は実際の建物/部屋  ✅ Foreigner friendly, student can apply, can apply while overseas | 外国人可,留学生可, 海外申請可 ❗ Penalty less than 2 year, no room sharing, no pets 📲DM us for any inquiries (Vacancy inquiry, house tour, move-in etc)! We are available in JPN/ENG/CHN",
      rent: "53000",
    };

    // Posting title
    const title = "#PostingTitle";
    await page.type(title, data["title"]);

    // City or neighborhood
    const area = "#geographic_area";
    await page.type(area, data["city"]);

    // Description
    const description = "#PostingBody";
    await page.type(description, data["description"]);

    // Rent
    const rent = "div.posting-attributes .price input";
    await page.type(rent, data["rent"]);

    // rent period
    const rentPeriod = "div.posting-attributes .rent_period select";
    await page.select(rentPeriod, "3"); // month

    // Per
    // let sqft =
    //   "fieldset:nth-child(4) div:nth-child(1) label:nth-child(3) input.json-form-input";
    // await page.type(sqft, data["sqft"]);

    // Laundry
    // fieldset:nth-child(4)  div:nth-child(2) > div:nth-child(2) label:nth-child(2) select
    const laundarySelected = ".laundry select";
    await page.select(laundarySelected, "1");
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Parking
    const parkingSelected = ".parking select";
    await page.select(parkingSelected, "7"); // NO parking
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Bedrooms
    const bedRoomSelected = ".bedrooms select";
    await page.select(bedRoomSelected, "1");

    // Bathrooms
    const bathroomSelected = ".bathrooms select";
    await page.select(bathroomSelected, "3"); // 1 bath room

    // Submit
    const submitButton = ".submit-buttons div:nth-child(1) button";
    await page.click(submitButton);
    await new Promise(resolve => setTimeout(resolve, 4000));

    // Click publish
    const publishButton = "form#publish_top button";
    await page.waitForSelector(publishButton);
    await page.click(publishButton);
    await new Promise(resolve => setTimeout(resolve, 4000));
  } catch (err: any) {
    console.error(err);
    sendLark({
      message: `[Craislgist Fresh] Error: ${err}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return;
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }
  }
};
