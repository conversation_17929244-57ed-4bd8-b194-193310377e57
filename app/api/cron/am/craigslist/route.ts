import { prisma } from "@/lib/prisma";
import fs from "fs";
import path from "path";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { getChromeBrowser, getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import dayjs from "dayjs";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  const { CRAIGSLIST_USERNAME, CRAIGSLIST_PASSWORD, DB_USERNAME, DB_PASSWORD } =
    process.env;

  if (!CRAIGSLIST_USERNAME || !CRAIGSLIST_PASSWORD) {
    throw new Error("CRAIGSLIST_USERNAME or CRAIGSLIST_PASSWORD is not set");
  }

  // TODO: tllRentRooms does not exist
  const rows = [] as any;
  // const rows = await prisma.tllRentRoom.findMany({
  //   where: {
  //     isActive: 1,
  //   },
  // });

  if (!rows.length) {
    console.log("🔥 no results, leave🔥 🔥");
    return;
  }

  let browser;
  let page;

  try {
    browser = await getChromeBrowser();
    page = await getPageWithRandomUserAgent(browser);

    // ***********************************************
    // Step 1: Log in
    const ADMIN_LOGIN_URL = "https://accounts.craigslist.org/login";

    await page.goto(ADMIN_LOGIN_URL);
    await new Promise(resolve => setTimeout(resolve, 3000));

    await page.type("#inputEmailHandle", CRAIGSLIST_USERNAME);
    await new Promise(resolve => setTimeout(resolve, 2000));
    await page.type("#inputPassword", CRAIGSLIST_PASSWORD);

    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.click("#login");
    await new Promise(resolve => setTimeout(resolve, 3000));

    // ***********************************************
    // Step 2: Go to the page for posting

    // if craigslist ask you to repose form previous post.. then click skip
    for (const r of rows) {
      const values = {
        title: r["sns_title_en"],
        location: r["address_en"],
        content: r["sns_content_en"],
        postal_code: JSON.stringify(r["postal_code"]),
        price: JSON.stringify(r["price"] + r["misc_fee"]),
        period: "3",
        bath: "4",
        parking: "7",
        bedroom: "1",
        toilet: "2",
        imageLinks: r["image_links"],
        hookImageLinks: r["hook_image_link"],
        email: "<EMAIL>",
      } as any;

      // *************** Image Step **************************
      // download the image
      const downloadPath =
        process.env.NODE_ENV === "development"
          ? path.resolve(__dirname, "downloaded")
          : "/tmp"; // has to be /tmp on lambda due to permission issue
      console.log(`🔥 [Post Craigslist] downloading path is ${downloadPath}`);

      const downloadedPaths = [] as any;

      for (const l of [
        ...values["imageLinks"]?.split(","),
        values["hookImageLinks"],
      ]) {
        const downloadedPath = `${downloadPath}/${dayjs().format(
          "YYYY-MM-DD-HH-mm-ss"
        )}_image_${l.split("/").pop()}`;

        console.log("🔥 downloadedPath🔥 🔥");
        console.log(downloadedPath);

        const viewSource = await page.goto(l);

        if (viewSource) {
          // await fs.writeFile(downloadedPath, await viewSource.buffer());
          // console.log(`🔥 Image Downloaded!, path is ${downloadedPath} 🔥`);
          // downloadedPaths.push(downloadedPath);
        }
      }

      ////// THEN GO TO THE MAIN PAGE
      const POST_URL = "https://tokyo.craigslist.org/";
      await page.goto(POST_URL);
      await new Promise(resolve => setTimeout(resolve, 3000));

      await page.click(".cl-goto-post");
      await new Promise(resolve => setTimeout(resolve, 3000));

      console.log("🔥 go to main page 🔥 🔥");

      const element = await page.$('button[name="brand_new_post"]');
      if (element === null) {
        console.log("🔥 🔥  Element not found.");
      } else {
        console.log("🔥 can find element 🔥 🔥");
        // Performing actions on the element
        await element.click();
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 継続
        await page.click(".pickbutton");
        await new Promise(resolve => setTimeout(resolve, 3000));
      }

      await page.click("li:nth-child(4) input"); //住居の提供
      await new Promise(resolve => setTimeout(resolve, 3000));

      await page.click("label:nth-child(2) input"); // apartment / housing for rent
      await new Promise(resolve => setTimeout(resolve, 3000));

      // ***********************************************
      // Step 3: Getting data from Lark for posting

      const fields = {
        title: {
          selector: "#PostingTitle",
          type: "input",
        },
        location: {
          selector: "#geographic_area",
          type: "input",
        },
        postal_code: {
          selector: "#postal_code",
          type: "input",
        },
        content: {
          selector: "#PostingBody",
          type: "input",
        },
        price: {
          selector: 'input[name="price"]',
          type: "input",
        },
        period: {
          selector: "select[name='rent_period']", // 3 month
          type: "select",
        },
        bath: {
          selector: "select[name='laundry']", // "4" 洗濯機・乾燥機の取り付け場所あり
          type: "select",
        },
        parking: {
          selector: "select[name='parking']", // 7 駐車不可
          type: "select",
        },

        bedroom: {
          selector: "select[name='bedrooms']", // 1-1
          type: "select",
        },
        toilet: {
          selector: "select[name='bathrooms']", // 2 分離
          type: "select",
        },
      } as any;

      for (const field of Object.keys(fields)) {
        if (fields[field].type === "input") {
          await page.type(fields[field].selector, values[field]);
        } else if (fields[field].type === "select") {
          await page.select(fields[field].selector, values[field]);
        }
      }

      const emailElement = await page.$(
        'input[value = "<EMAIL>"]'
      );
      if (emailElement !== null) {
        console.log("🔥 filled in email, do nothing.");
      } else {
        console.log("🔥 no email, fill it in 🔥 🔥");
        // Performing actions on the element
        await page.type('input[name="FromEMail"]', "<EMAIL>");
      }

      await new Promise(resolve => setTimeout(resolve, 1000));

      await page.click('button[type = "submit"]');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // upload the file
      const input = await page.$("input[type=file]");
      for (const p of downloadedPaths) {
        await input?.uploadFile(p);
      }
      await new Promise(resolve => setTimeout(resolve, 10000));

      console.log("🔥 Pressing dowe with photos 🔥");

      await page.waitForSelector("#doneWithImages");
      await page.click("#doneWithImages");
      await new Promise(resolve => setTimeout(resolve, 3000));

      console.log("🔥 Pressing submit🔥 🔥");

      // 公開する
      await page.click('#publish_top button[name = "go"]');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // ***********************************************
      // Step 4:: make updates
      // Push to lark on the post
      console.log("🔥 updating the sql🔥 🔥");
      // FIXME: bring it back
      // var sql =
      //   "UPDATE `tll_rent_rooms` SET posting_date_craigslist=? WHERE id=?";
      // await connection.execute(sql, [
      //   moment(new Date()).format("YYYY-MM-DD"),
      //   r.id,
      // ]);

      // Updaing the record
      await sendLark({
        message: `[Posting records posted to craigslsit for,  [${r["address_en"]}][${r["room_number"]}]${r["sns_title_en"]} updated`,
        url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
      });
    }

    return new Response("", { status: 200 });
  } catch (err: any) {
    console.log("🔥err🔥");
    console.log(err);
    await sendLark({
      message: "[POSTING CRAISLIST]" + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return;
  } finally {
    console.log("🔄 Closing page and browser!");
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
