import { prisma } from "@/lib/prisma";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";
import { mean } from "lodash-es";
import { removeOutlier } from "@/lib/helper/stats";
import { REFILL_TIMESTAMP } from "../../constants";
import { GeoPostalCodeProps } from "@/lib/definitions";

export async function GET(req: Request) {
  // Get recently updated properties

  const recentlyUpdated = await prisma.tllUserLambdaRecord.findMany({
    where: {
      updatedAt: {
        gt: new Date(Date.now() - REFILL_TIMESTAMP),
      },
      recordType: "BUILDING",
      yearlyIncome: {
        gt: 0,
      },
    },
    select: {
      postalCode: true,
    },
  });

  console.log("🔥 Getting recently updated properties, total: ", recentlyUpdated.length, "🔥");

  // Dedup their postal codes
  function getUniquePostalCodes(records: any[]) {
    return [...new Set(records.map((record: any) => parseInt(record.postalCode)) as number[])];
  }

  const uniqPostalCodes = getUniquePostalCodes(recentlyUpdated);
  const postalCodeRecords = await prisma.geoPostalCode.findMany({
    where: {
      postalCode: {
        in: uniqPostalCodes.map(code => code.toString()) as string[],
      },
    },
  }) as GeoPostalCodeProps[];

  let index = 0;

  for (const postalCodeRecord of postalCodeRecords) {
    console.log(`🔥 Calculating ${index++} of ${postalCodeRecords.length}🔥 🔥`);
    const matchedRecords = await prisma.tllUserLambdaRecord.findMany({
      where: {
        postalCode: parseInt(postalCodeRecord.postalCode ?? "0"),
      },
    });

    const res = {
      buildingRecordCount: 0,
      buildingAveragePrice: 0,
      buildingRecordRoi: 0,
      buildingPrices: [] as number[],
      buildingRois: [] as number[],
      houseRecordCount: 0,
      houseAveragePrice: 0,
      housePrices: [] as number[],
      landRecordCount: 0,
      landAveragePrice: 0,
      landPrices: [] as number[],
      mansionRecordCount: 0,
      mansionAveragePrice: 0,
      mansionPrices: [] as number[],
    };

    matchedRecords.forEach((r: any) => {
      if (r.recordType === "BUILDING") {
        res.buildingRecordCount++;
        if (r.price) {
          res.buildingPrices.push(r.price);
          if (r.yearlyIncome) {
            res.buildingRois.push(r.yearlyIncome / r.price);
          }
        }
      } else if (r.recordType === "house") {
        res.houseRecordCount++;
        if (r.price) {
          res.housePrices.push(r.price);
        }
      } else if (r.recordType === "land") {
        res.landRecordCount++;
        if (r.price) {
          res.landPrices.push(r.price);
        }
      } else if (r.recordType === "mansion") {
        res.mansionRecordCount++;
        if (r.price) {
          res.mansionPrices.push(r.price);
        }
      }
    });

    res.buildingAveragePrice = res.buildingPrices.length
      ? mean(res.buildingPrices)
      : 0;
    res.buildingRecordRoi = res.buildingRois.length
      ? mean(removeOutlier(res.buildingRois))
      : 0;
    res.houseAveragePrice = res.housePrices.length
      ? mean(res.housePrices)
      : 0;
    res.landAveragePrice = res.landPrices.length ? mean(res.landPrices) : 0;
    res.mansionAveragePrice = res.mansionPrices.length
      ? mean(res.mansionPrices)
      : 0;


    await prisma.geoPostalCode.update({
      where: {
        id: postalCodeRecord.id,
      },
      data: {
        buildingAveragePrice: res.buildingAveragePrice,
        buildingRecordCount: res.buildingRecordCount,
        buildingRecordRoi: res.buildingRecordRoi,
        houseAveragePrice: res.houseAveragePrice,
        houseRecordCount: res.houseRecordCount,
        landAveragePrice: res.landAveragePrice,
        landRecordCount: res.landRecordCount,
        mansionAveragePrice: res.mansionAveragePrice,
        mansionRecordCount: res.mansionRecordCount,
      }
    });
  }

  await sendLark({
    message: `[BACKFILL POSTAL CODE]Updated Postal Codes Aggregate count is ${uniqPostalCodes.length}`,
    url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
  });

  return NextResponse.json({ uniqPostalCodes });
}