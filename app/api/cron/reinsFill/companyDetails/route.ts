import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({
      status: "unauthorized"
    });
  }

  if (req.method !== "GET") {
    return NextResponse.json({
      status: "method_not_allowed"
    });
  }

  let page, browser;

  try {
    browser = await getChromeBrowser();
    page = await getPageWithRandomUserAgent(browser);

    // Step 1: go page
    await page.goto("https://system.reins.jp/login/main/KG/GKG001200", {
      waitUntil: "networkidle2",
    });

    const { REINS_USERNAME, REINS_PASSWORD } = process.env;
    if (!REINS_USERNAME || !REINS_PASSWORD) {
      throw new Error("REINS_USERNAME or REINS_PASSWORD is not set");
    }

    await page.waitForSelector("#__BVID__13");
    await page.type("#__BVID__13", REINS_USERNAME);
    await page.type("#__BVID__16", REINS_PASSWORD);
    await new Promise(resolve => setTimeout(resolve, 1000));

    await page.click("#__BVID__20");
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.click(".p-3.large.btn-primary");
    logger.info("🔥 Logged in and page loaded ..");
    await new Promise(resolve => setTimeout(resolve, 4000));

    // *********************************************************
    // Step 1.1:: Go to recent ビル page
    // *********************************************************
    // Step 1.1 Go to search page
    const searchButtonSelector =
      ".card:nth-child(2) > div > div > div:nth-child(1) button";
    await page.waitForSelector(searchButtonSelector);
    await page.click(searchButtonSelector);
    logger.info("🔥 Go to search page ..");
    await new Promise(resolve => setTimeout(resolve, 4000));

    const typeSelector =
      ".card:nth-child(7) .card-body .row:nth-child(2) .col-sm-4:nth-child(1) select";

    // 2.1 pick a type, and be filling in other info
    await page.waitForSelector(typeSelector);
    await page.select(typeSelector, "04"); // 03 - マンション 04 - 売外全(住宅以外建物全部), 02 一戸建て, 01 土地

    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(4) .row:nth-child(2) input",
      "東京都"
    );

    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(4) .row:nth-child(3) input",
      "23区"
    );

    // new items yesterday
    await page.click(
      `.card:nth-child(13) .card-body .container:nth-child(5) > .row:nth-child(1) .custom-radio:nth-child(6) input`
    );

    // recent change yesterday
    await page.click(
      `.card:nth-child(13) .card-body .container:nth-child(5) > .row:nth-child(2) .custom-radio:nth-child(6) input`
    );

    await page.click(".p-frame-bottom div:nth-child(4) .btn-block.px-0");

    // *********************************************************
    // Step 22: For each company, check if data exist
    // *********************************************************
    let RECORDS_TOTAL = 0;
    let RECORDS_UPDATED = 0;

    const RESULT_COUNT_SELECTOR = ".text-dark.ml-3";
    await page.waitForSelector(RESULT_COUNT_SELECTOR);
    const PAGINATION_COUNT = 50;
    RECORDS_TOTAL = parseInt(
      await page.evaluate(
        (sel) =>
          document
            .querySelector(sel)
            ?.textContent?.trim()
            ?.split("／")[1]
            ?.trim()
            ?.replace("件", "") ?? "0",
        RESULT_COUNT_SELECTOR
      ),
      10
    );
    const pageCount = Math.ceil(RECORDS_TOTAL / PAGINATION_COUNT);

    logger.info(
      `🔥 Total result count is ${RECORDS_TOTAL}, with ${pageCount} pages`
    );

    for (let pageIndex = 1; pageIndex <= pageCount; pageIndex++) {
      const ROW_SELECTOR = ".p-table-body-row";

      const listLength = await page.evaluate(
        (sel) => document.querySelectorAll(sel).length,
        ROW_SELECTOR
      );

      for (let i = 1; i <= listLength; i += 1) {
        logger.info(`------------🔥 Processing ${i} / ${listLength} 🔥----------`);

        const companyInfo: Record<string, string> = {};
        const COMPANY_SELECTOR = `${ROW_SELECTOR}:nth-child(${i}) .p-table-body-item:nth-child(19) a`;

        const companyName = await page.evaluate((sel) => {
          const res = document.querySelector(sel);
          if (res !== null) {
            return res.textContent?.replace(/(\r\n\t|\n|\r\t|\s)/gm, "") ?? "";
          }
          return res;
        }, COMPANY_SELECTOR);
        await new Promise(resolve => setTimeout(resolve, 2000));

        const rows = await prisma.proCompany.findMany({
          where: {
            fullName: companyName,
          },
        });
        logger.debug(`🔥 companyName is ${companyName}, with matches ${rows.length}`);

        // Skip items that does not exist
        if (rows.length) {
          const match = rows[0];
          const { address } = match;

          if (address === null) {
            logger.info(
              "🔥 [Case 1][No address]match address is null, go to dp to get details🔥 🔥"
            );

            await page.click(COMPANY_SELECTOR);
            await new Promise(resolve => setTimeout(resolve, 4000));

            const companyDetailsSelector = {
              companyName:
                ".p-paper-contents table:nth-child(3) tr:nth-child(3) td",
              branchName:
                ".p-paper-contents table:nth-child(3) tr:nth-child(4) td",
              address:
                ".p-paper-contents table:nth-child(7) tr:nth-child(3) td",
              addressDetails:
                ".p-paper-contents table:nth-child(7) tr:nth-child(4) td", // 建物名
              repName:
                ".p-paper-contents table:nth-child(9) tr:nth-child(3) td",
              faxNumber:
                ".p-paper-contents table:nth-child(9) tr:nth-child(4) td:nth-child(4)",
              email:
                ".p-paper-contents table:nth-child(9) tr:nth-child(5) td:nth-child(2)",
              url: ".p-paper-contents table:nth-child(9) tr:nth-child(6) td:nth-child(2)",
              licenseNumber:
                ".p-paper-contents table:nth-child(5) tr:nth-child(2) td:nth-child(2) ",
            };

            for (const field of Object.keys(companyDetailsSelector)) {
              const selector = companyDetailsSelector[field as keyof typeof companyDetailsSelector];
              const value = await page.evaluate((sel) => {
                const res = document.querySelector(sel);
                if (res !== null) {
                  return res.textContent?.replace(/(\r\n\t|\n|\r\t|\s)/gm, "") ?? "";
                }
                return res;
              }, selector);

              companyInfo[field as keyof typeof companyInfo] = value ?? "";
            }

            companyInfo[
              "address"
            ] = `${companyInfo["address"]} ${companyInfo["addressDetails"]}`;

            delete companyInfo["addressDetails"];
            logger.debug(`🔥 companyInfo is ${JSON.stringify(companyInfo)}`);

            await prisma.proCompany.update({
              where: {
                id: rows[0].id,
              },
              data: companyInfo,
            });
            RECORDS_UPDATED++;

            logger.info(
              `🔥 [Case 1][Updated] updating record that is ${rows[0].id}, item count ${i}, pageIndex ${pageIndex}🔥 🔥`
            );

            await page.goBack();
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            logger.info(
              `🔥 [Case 2][Exist with address]skipping because already already exist for is ${rows[0].id}🔥 🔥`
            );
          }
        } else {
          logger.info("🔥 [Case 3][Not exist]company not exist.. skipping for now🔥 🔥");
        }
      }

      if (pageIndex < pageCount) {
        await page.click(".row:nth-child(1) .p-pagination-next-icon");
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    sendLark({
      message: `[⚙️][FILL Company] Updating company, total record is ${RECORDS_TOTAL}, total updated record is ${RECORDS_UPDATED}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return NextResponse.json({
      status: "success"
    });
  } catch (err: any) {
    logger.error("🔥 err", err);

    await sendLark({
      message: `[⚙️][❌][FILL Company] ${JSON.stringify(err)}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return NextResponse.json({
      status: "error",
      message: err
    });
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
