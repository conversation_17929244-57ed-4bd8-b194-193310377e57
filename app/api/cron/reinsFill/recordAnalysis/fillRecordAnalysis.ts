"use server";
import { recordAnalysis } from "@/lib/userLambdaRecord/recordAnalysis";

import { prisma } from "@/lib/prisma";
import { ValuationRecordProps } from "@/lib/definitions";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { REFILL_TIMESTAMP } from "../../constants";
import { logger } from "@/lib/logger";
import { PropertyAnalysisResultsProps } from "@/lib/definitions/propertyAnalysisResults";
import { recordCfAnalysis } from "@/lib/userLambdaRecord/recordCfAnalysis";
import { getNearbyMaxMin80Percentile } from "@/lib/userLambdaRecord/getNearbyMaxMin80Percentile";
import pLimit from "p-limit";
import { recordAnalysisMansion } from "@/lib/userLambdaRecord/recordAnalysisMansion";
import { getTopResultsFromIds } from "@/actions/tllUserLambdaRecordNearbyRecords";

export async function fillRecordAnalysis({
  recordId,
  model = "tllUserLambdaRecord",
}: {
  recordId?: string,
  model?: "valuationRecord" | "tllUserLambdaRecord",
}): Promise<{
  totalCount: number;
  updatedCount: number;
}> {
  logger.debug("fillRecordAnalysis", {
    recordId,
    model,
  });

  let index = 0;
  const batchSize = 5;
  let processedCount = 0;

  let recordWhereParams = {} as any;

  if (recordId) {
    recordWhereParams.id = recordId;
  } else {
    let whereParams = {} as any;
    whereParams.createdAt = {
      gt: new Date(Date.now() - REFILL_TIMESTAMP),
    }

    let matchChanges = await prisma.tllUserLambdaRecordPriceChange.findMany({
      where: whereParams,
      select: { recordId: true },
    })

    logger.debug("matchChanges", matchChanges.length);

    recordWhereParams = {
      id: {
        in: matchChanges.map((v: any) => v.recordId)
      },
      longitude: {
        not: null,
      },
      latitude: {
        not: null,
      },
      OR: [
        {
          propertyAnalysisResult: null,
        },
        {
          propertyAnalysisResult: {
            analysisDate: {
              lt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            },
          },
        },
      ],
      // recordType: {
      //   not: "MANSION",
      // },
    }
  }

  let recentlyUpdatedRecords: any[] = [];

  // logger.debug("recordWhereParams", recordWhereParams);
  recentlyUpdatedRecords = await (prisma[model as "valuationRecord" | "tllUserLambdaRecord"] as any).findMany({
    where: recordWhereParams,
    ...(model === "tllUserLambdaRecord" && {
      include: {
        building: true, // for mansion lat lng using building calculations
      }
    })
  });

  const limit = pLimit(10);

  for (let i = 0; i < recentlyUpdatedRecords.length; i += batchSize) {
    const chunk = recentlyUpdatedRecords.slice(i, i + batchSize);
    await Promise.all(
      chunk.map(async (r: any) => limit(async () => {
        logger.info(
          `🔥 Processing local ${++index} of ${recentlyUpdatedRecords.length}, id is ${r.id} 🔥`
        );
        const propertyAnalysisResult = await createAnalysisAndSimulation({
          model,
          r
        });
        if (!propertyAnalysisResult) {
          return;
        }
        await updateOneRecordSimulation(model, r, propertyAnalysisResult);
      })));
  }

  processedCount = recentlyUpdatedRecords.length;

  return {
    totalCount: recentlyUpdatedRecords.length,
    updatedCount: index,
  };
}

async function createAnalysisAndSimulation({
  model,
  r,
}: {
  model: "valuationRecord" | "tllUserLambdaRecord",
  r: ValuationRecordProps,
}): Promise<PropertyAnalysisResultsProps | undefined> {

  const propertyAnalysisResult = r.recordType === "MANSION" ? await recordAnalysisMansion({
    record: r as UserLambdaRecordProps,
  }) as PropertyAnalysisResultsProps : await recordAnalysis({
    record: r as UserLambdaRecordProps,
  }) as PropertyAnalysisResultsProps;

  if (!propertyAnalysisResult) {
    return;
  }

  let upsertData = {
    where: {},
    update: propertyAnalysisResult,
    create: propertyAnalysisResult,
  }

  if (model === "valuationRecord") {
    upsertData.where = {
      valuationRecordId: r.id,
    }
    propertyAnalysisResult.valuationRecordId = r.id;
    delete propertyAnalysisResult.recordId;
  } else {
    upsertData.where = {
      recordId: r.id,
    }
  }

  let upsertedPropertyAnalysisResults = await prisma.propertyAnalysisResult.upsert(upsertData as any);

  logger.info("upsertedPropertyAnalysisResults id is", upsertedPropertyAnalysisResults.id);

  return propertyAnalysisResult;
}

async function updateOneRecordSimulation(model: "valuationRecord" | "tllUserLambdaRecord", r: ValuationRecordProps, propertyAnalysisResult: PropertyAnalysisResultsProps) {
  let topResultsPadded = await getTopResultsFromIds({
    distanceXId: propertyAnalysisResult?.nearbyRecordIdAndDistance as {
      [key: string]: number; // id: distance
    },
  });

  const { eightyPercentile, avg } = getNearbyMaxMin80Percentile(topResultsPadded.data, r.recordType as string);
  // logger.info("eightyPercentile", eightyPercentile);

  // // FIXME: take this oout too as a separate data
  const {
    optimalBiddingPriceCalulation,
    cfSimulationParams,
  } = await recordCfAnalysis({ propertyAnalysisResult, record: r as UserLambdaRecordProps, eightyPercentile, avg });

  await (prisma[model as "valuationRecord" | "tllUserLambdaRecord"] as any).update({
    where: {
      id: r.id,
    },
    data: {
      analysisSimulationConfig: cfSimulationParams,
      analysisSimulationResults: {
        optimalBiddingPriceCalulation,
      },
      updatedAt: r.updatedAt || new Date(),
    },
  });
}
