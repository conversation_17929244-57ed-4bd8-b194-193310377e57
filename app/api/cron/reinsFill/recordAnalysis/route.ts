import { NextResponse } from "next/server";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { fillRecordAnalysis } from "./fillRecordAnalysis";
import { max } from "lodash-es";
import { logger } from "@/lib/logger";
// import { fillRecordSendLark } from "./fillRecordSendLark";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz"
import duration from "dayjs/plugin/duration";
dayjsWithTz.extend(duration);
import { prisma } from "@/lib/prisma";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  const { searchParams } = new URL(req.url);

  let res: any = {};
  let startTime, endTime;

  try {
    startTime = dayjsWithTz();

    res.updatePriceChangeCount = await fillRecordAnalysis({}); // will exclude 28 by default

    return NextResponse.json(res);
  } catch (err: any) {
    console.error(err);
    await sendLark({
      message: `[⚙️][❌][FILL RECORD ANALYSIS] ${JSON.stringify(err)}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return NextResponse.json(err);
  } finally {
    endTime = dayjsWithTz();
    const duration = dayjsWithTz.duration(endTime.diff(startTime));

    await sendLark({
      message: `[⚙️][FILL RECORD ANALYSIS][🕒${duration.minutes()}:${duration.seconds()}] Total: ${res.updatePriceChangeCount?.totalCount} | updated: ${res.updatePriceChangeCount?.updatedCount}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    await prisma.$disconnect(); // prevent long hanging connection
  }
}
