import { prisma } from "@/lib/prisma";
import { getLatAndLngFromAddress } from "@/lib/thirdParty/google";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";
import { GetStationName } from "@/lib/railwayStation";
import { REFILL_TIMESTAMP } from "../../constants";
import { REINS_TASKS } from "../../reins/constants/reinsTasks";
import { logger } from "@/lib/logger";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { getMansionRecordOrCreateNewForName } from "./getMansionRecordOrCreateNewForName";
import { getMansionRecordOrCreateNewForNameFromMansionRent } from "./getMansionRecordOrCreateNewForNameFromMansionRent";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  // ************ 1 Create new mansion for recent items
  // Step 1: Getting records for mansion
  try {
    let buildingAddedCount = 0;
    let buildingUpdatedCount = 0;

    let recentChanges = await prisma.tllUserLambdaRecordPriceChange.findMany({
      where: {
        createdAt: {
          gt: new Date(Date.now() - REFILL_TIMESTAMP),
        },
      },
    });

    const mansionRecordsToFill = await prisma.tllUserLambdaRecord.findMany({
      where: {
        buildingId: null,
        recordType: "MANSION",
        id: {
          in: recentChanges.map((v) => v.recordId).filter((v) => v !== null) as string[],
        },
      },
    }) as UserLambdaRecordProps[];

    let index = 0;
    for (const record of mansionRecordsToFill) {
      logger.info("🔥 filling building ", index, " out of ", mansionRecordsToFill.length);
      index += 1;

      if (record.recordValues?.title && record.address) {
        const {
          recordId,
          isNew,
        } = await getMansionRecordOrCreateNewForName({
          isKubun: true,
          currentRecord: record,
          nameJa: record.recordValues?.title || "",
          address: record.address,  // wont be "" as it is a must have field 
          nearestStation: record.nearestStation || "",
          nearestStationGroupId: record.nearestStationGroupId || "",
        }) as any;

        if (recordId === null) {
          continue;
        }

        logger.info("🔥 filling id dbecause record.recordValues?.title and record.address are not empty");
        await prisma.tllUserLambdaRecord.update({
          where: { id: record.id },
          data: {
            buildingId: recordId,
            updatedAt: record.updatedAt, // do not update the updatedAt, laeve it for change
          },
        });

        if (isNew) {
          buildingAddedCount += 1;
        } else {
          buildingUpdatedCount += 1;
        }
      }
    }


    // // 2 Rent records to fill
    const mansionRentRecordsToFill = await prisma.proMansionRent.findMany({
      where: {
        updatedAt: {
          gt: new Date(Date.now() - REFILL_TIMESTAMP),
        },
        buildingId: null,
        buildingName: {
          not: "",
        },
      },
    }) as ProMansionRentProps[];

    console.log(
    );

    index = 0;
    for await (const rec of mansionRentRecordsToFill as ProMansionRentProps[]) {
      logger.debug(`🔥 Filling rent, currently at ${index++} out of ${mansionRentRecordsToFill.length}`);
      let stationName = null;
      if (rec.transport !== null && rec.transport !== "" && rec.transport !== undefined) {
        stationName = await GetStationName(
          rec.transport
        );
      }

      const { recordId, isNew } = await getMansionRecordOrCreateNewForNameFromMansionRent({
        isKubun: true,
        name: rec.buildingName || "",
        address: rec.buildingAddress || "",
        nearestStation: stationName || "",
      }) as {
        recordId: string,
        isNew: boolean,
      };

      console.log("buildingId", recordId);

      await prisma.proMansionRent.update({
        where: { id: rec.id },
        data: {
          buildingId: recordId,
          nearestStation: stationName,
        }
      });

      if (isNew) {
        buildingAddedCount += 1;
      } else {
        buildingUpdatedCount += 1;
      }
    }

    const info = {
      buildingUpdatedCount,
      buildingAddedCount,
    };

    await sendLark({
      message: `[⚙️][FILL Mansion Building] ${JSON.stringify(info)}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return NextResponse.json(info);
  } catch (error) {
    console.error(error);
    await sendLark({
      message: `[⚙️][❌][FILL Mansion Building] ${JSON.stringify(error)}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return NextResponse.json({ error: "Internal Server Error" }, { status: 500 });
  }
}
