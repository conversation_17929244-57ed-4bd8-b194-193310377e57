import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { getLatAndLngFromAddress } from "@/lib/thirdParty/google";

export async function getMansionRecordOrCreateNewForName({ isKubun, currentRecord, nameJa, address, nearestStation, nearestStationGroupId }: { isKubun: boolean, currentRecord: UserLambdaRecordProps, nameJa: string, address: string, nearestStation: string, nearestStationGroupId: string }): Promise<{
  recordId: string,
  isNew: boolean,
} | null> {
  // FIXME: do not match for postal code here now, as this will call getLatAndLngFromAddress everytime...
  if (nameJa === "") {
    return null;
  }

  let parsedAddress = address.split("丁目")[0] + "丁目"

  const matchedRecord = await prisma.proBuilding.findUnique({
    where: {
      nameJa_address: {
        nameJa: nameJa,
        address: parsedAddress,
      },
    },
  });

  // Case 1: match exist
  if (matchedRecord) {
    logger.debug("[Building name Case 1] Has match, maybe updating");

    if (matchedRecord.longitude == null || matchedRecord.latitude == null || matchedRecord.postalCode == null || matchedRecord.nearestStation == null || matchedRecord.nearestStationGroupId == null) {

      // Fill record wehre possible
      // Should not be needed after a while
      let dataToUpdate = {
        ...(currentRecord.longitude ? { longitude: currentRecord.longitude } : {}),
        ...(currentRecord.latitude ? { latitude: currentRecord.latitude } : {}),
        ...(currentRecord.postalCode ? { postalCode: currentRecord.postalCode.toString() } : {}),
        ...(currentRecord.nearestStation ? { nearestStation: currentRecord.nearestStation } : {}),
        ...(currentRecord.nearestStationGroupId ? { nearestStationGroupId: currentRecord.nearestStationGroupId } : {}),
        ...(address.length > parsedAddress.length ? { addressDetailed: address } : {}),
      } as any;

      await prisma.proBuilding.update({
        where: { id: matchedRecord.id },
        data: dataToUpdate,
      });

      return {
        recordId: matchedRecord.id,
        isNew: false,
      };
    }

    return {
      recordId: matchedRecord.id,
      isNew: false,
    };
  }

  // Case 2: match no exist
  // let locationData = await (name, ctx);
  logger.debug("[Building name Case 2] No match, getting location and save and create new ");
  const locationData = await getLatAndLngFromAddress({ address: isKubun ? nameJa : address }); // use address for non-kubun because it tends to be more accurate

  let location;
  let postalCode;
  if (locationData && locationData.length) {
    location = locationData[locationData.length - 1].geometry.location;
    const addressFull = locationData[locationData.length - 1].formatted_address; // '日本、〒153-0063 東京都目黒区目黒１丁目２−９ シティハウス目黒',
    const postalCodeMatch = addressFull?.match(/\d{3}-\d{4}/g);
    if (postalCodeMatch?.length) {
      postalCode = postalCodeMatch[0].replace("-", "");
    }
  }

  const newRecord = await prisma.proBuilding.create({
    data: {
      isKubun: isKubun,
      nameJa: nameJa,
      address: parsedAddress,
      addressDetailed: address,
      postalCode,
      nearestStation: nearestStation,
      nearestStationGroupId: nearestStationGroupId,
      latitude: location === undefined ? null : location.lat,
      longitude: location === undefined ? null : location.lng,
    }
  });

  return {
    recordId: newRecord.id,
    isNew: true,
  };
}