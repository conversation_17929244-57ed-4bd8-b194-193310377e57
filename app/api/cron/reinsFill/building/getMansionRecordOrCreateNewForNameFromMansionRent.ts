import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { getLatAndLngFromAddress } from "@/lib/thirdParty/google";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";

export async function getMansionRecordOrCreateNewForNameFromMansionRent({ isKubun, name, address, nearestStation, buildingBuiltYear, transport, nearestStationWalkMinute, nearestStationGroupId }: { isKubun: boolean, name: string, address: string, nearestStation: string, buildingBuiltYear?: number, transport?: string, nearestStationWalkMinute?: number, nearestStationGroupId?: string }): Promise<{
  recordId: string,
  isNew: boolean,
  record?: ProBuildingProps,
} | null> {
  // FIXME: do not match for postal code here now, as this will call getLatAndLngFromAddress everytime...
  if (name === "") {
    return null;
  }

  let parsedAddress = address.split("丁目")[0] + "丁目"

  const matchedRecord = await prisma.proBuilding.findUnique({
    where: {
      nameJa_address: {
        nameJa: name,
        address: parsedAddress,
      },
    },
  });

  // Case 1: match exist
  if (matchedRecord) {
    logger.debug("[Building name Case 1] Has match, maybe updating");

    await prisma.proBuilding.update({
      where: { id: matchedRecord.id },
      data: {
        ...(buildingBuiltYear && buildingBuiltYear > 0 ? { builtYear: buildingBuiltYear } : {}),
        ...(nearestStationGroupId && nearestStationGroupId !== "" ? { nearestStationGroupId: nearestStationGroupId } : {}),
        ...(nearestStationWalkMinute && nearestStationWalkMinute > 0 ? { nearestStationWalkMinute: nearestStationWalkMinute } : {}),
        ...(transport && transport !== "" ? { transport: transport } : {}),
        ...(address.length > parsedAddress.length ? { addressDetailed: address } : {}),
      },
    });

    return {
      recordId: matchedRecord.id,
      isNew: false,
    };
  }

  // Case 2: match no exist
  // let locationData = await (name, ctx);
  logger.debug("[Building name Case 2] No match, getting location and save and create new ");
  const locationData = await getLatAndLngFromAddress({
    address: name,
  });

  let location;
  let postalCode;
  if (locationData && locationData.length) {
    location = locationData[locationData.length - 1].geometry.location;
    const addressFull = locationData[locationData.length - 1].formatted_address; // '日本、〒153-0063 東京都目黒区目黒１丁目２−９ シティハウス目黒',
    const postalCodeMatch = addressFull?.match(/\d{3}-\d{4}/g);
    if (postalCodeMatch?.length) {
      postalCode = postalCodeMatch[0].replace("-", "");
    }
  }

  const newRecord = await prisma.proBuilding.create({
    data: {
      isKubun: isKubun,
      nameJa: name,
      address: parsedAddress,
      addressDetailed: address,
      postalCode,
      nearestStation: nearestStation,
      transport: transport,
      ...(nearestStationWalkMinute && nearestStationWalkMinute > 0 ? { nearestStationWalkMinute: nearestStationWalkMinute } : {}),
      ...(buildingBuiltYear && buildingBuiltYear > 0 ? { builtYear: buildingBuiltYear } : {}),
      ...(nearestStationGroupId && nearestStationGroupId !== "" ? { nearestStationGroupId: nearestStationGroupId } : {}),
      latitude: location === undefined ? null : location.lat,
      longitude: location === undefined ? null : location.lng,
    }
  });

  return {
    recordId: newRecord.id,
    isNew: true,
    record: newRecord as ProBuildingProps,
  };
}