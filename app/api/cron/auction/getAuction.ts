import { sendLark, sendLarkCard } from "@/lib/thirdParty/lark";
import { getAuctionDetails } from "./getAuctionDetails.js";
import RecaptchaPlugin from "puppeteer-extra-plugin-recaptcha";
import { prisma } from "@/lib/prisma";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import dayjs from "dayjs";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  const currentDate = dayjs().utc().format("YYYY-MM-DD");
  const browser = null;
  const page = null;

  // try {
  //   browser = await getChromeBrowser();
  //   page = await getPageWithRandomUserAgent(browser);

  //   let recaptcha = RecaptchaPlugin({
  //     provider: {
  //       id: "2captcha",
  //       token: "e9b0688797d452a46e058b4a2366018c", // REPLACE THIS WITH YOUR OWN 2CAPTCHA API KEY ⚡
  //     },
  //     visualFeedback: true, // colorize reCAPTCHAs (violet = detected, green = solved)
  //   });

  //   // puppeteer.use(recaptcha);

  //   // ****************************************
  //   // Step 1: Go page, and basic checks
  //   await page.goto(
  //     "https://981.jp/ftl/searchRes.do?s.pr=13,14,11,12&s.rg=3&size=100&sort=resDays&locale=ja",
  //     {
  //       waitUntil: "networkidle2",
  //     }
  //   );
  //   console.log("🔥  go to properties page  ... 🔥 🔥");

  //   let TOTAL_COUNT_PAGINATION_SELECTOR =
  //     ".clearfix:nth-child(6) .clearfix .paging li.w3-hide-small"; // selector when there is pagination
  //   let TOTAL_COUNT_SELECTOR = ".clearfix:nth-child(6) .clearfix .paging li";
  //   // await page.waitForSelector(TOTAL_COUNT_SELECTOR);

  //   // Case 1 with pagination
  //   // Get the total number of bukken and send to Lark
  //   let TOTAL_COUNT;
  //   // if (process.env.NODE_ENV === "development") {
  //   //   await page.screenshot({
  //   //     path: `debug/${moment().utc().format("MM-DD HH:mm:ss")}.png`,
  //   //   });
  //   // }

  //   TOTAL_COUNT = await page.evaluate(
  //     (sel, sel_pagination) => {
  //       let res = document.querySelector(sel_pagination) as any;

  //       // e.g. "101件中 1 - 100件目"
  //       if (res !== null) {
  //         return parseInt(
  //           res.textContent.replace(/(\r\n\t|\n|\r\t|\s)/gm, "")?.split("件")[0]
  //         );
  //       }

  //       res = document.querySelector(sel);
  //       if (res !== null) {
  //         return parseInt(
  //           res.textContent
  //             .replace(/(\r\n\t|\n|\r\t|\s)/gm, "")
  //             .replace("件", "")
  //         );
  //       }

  //       return res;
  //     },
  //     TOTAL_COUNT_SELECTOR,
  //     TOTAL_COUNT_PAGINATION_SELECTOR
  //   );

  //   // Case 2 without pagination
  //   // TODO: Only read first 100, and minue last 1 because it is PR
  //   let allDataResults = [];
  //   let PAGE_COUNT = Math.ceil(TOTAL_COUNT / 100);
  //   console.log(
  //     `🔥 TOTAL_COUNT is ${TOTAL_COUNT}, PAGE_COUNT is ${PAGE_COUNT}🔥 🔥`
  //   );
  //   for (let p = 0; p < PAGE_COUNT; p++) {
  //     for (
  //       let i = 0;
  //       i < ((p + 1) * 100 > TOTAL_COUNT ? TOTAL_COUNT - p * 100 : 100);
  //       i++
  //     ) {
  //       // ********** STEP 1 *************
  //       // Get the building info
  //       let dataResult = {};
  //       let bukkenSelector = `.search-list > tbody`;
  //       // `.resultbox:nth-child(${
  //       //   i + 1
  //       // })`;

  //       console.log("🔥 i🔥 🔥");
  //       console.log(i);

  //       let BLOCK_SELECTOR = {
  //         title: `tr:nth-child(${2 * i + 1}) td > a`,
  //         auctionType: `tr:nth-child(${2 * i + 1}) i`,
  //         link: `tr:nth-child(${2 * i + 1}) td > a`,
  //         type: `tr:nth-child(${2 * i + 1}) th > .resGroup`,
  //         subType: `tr:nth-child(${2 * i + 1}) th`, // need to get from ()
  //         imageLink: `tr:nth-child(${2 * i + 2}) .object-image img`,
  //         bidPrice: `tr:nth-child(${2 * i + 2}) .object-price b`,
  //         address: `tr:nth-child(${2 * i + 2}) .object-address`, // first part
  //         bidIdentifier: `tr:nth-child(${2 * i + 2}) .object-address`, // second part
  //         builtDate: `tr:nth-child(${2 * i + 2}) .object-age`,
  //         buildingSize: `tr:nth-child(${2 * i + 2}) .object-sqm div`,
  //       };

  //       for (let key of Object.keys(BLOCK_SELECTOR)) {
  //         let selector = `${bukkenSelector} ${BLOCK_SELECTOR[key]}`;

  //         let currentValue = await page.evaluate(
  //           (sel, key) => {
  //             const res = document.querySelector(sel);
  //             if (res === null) return res;
  //             if (res.textContent === undefined) return res;

  //             console.log("🔥 res.textContent🔥 🔥");
  //             console.log(res.textContent);

  //             if (key === "link") {
  //               return `https://981.jp${res.getAttribute("href")}`;
  //             } else if (key === "subType") {
  //               return res.textContent?.split("(")[1]?.split(")")[0];
  //             } else if (key === "imageLink") {
  //               return res.getAttribute("src");
  //             } else if (key === "bidPrice") {
  //               return parseInt(res.textContent.replace(/,/g, ""));
  //             } else if (key === "buildingSize") {
  //               return parseInt(res.textContent.replace(/㎡/g, ""));
  //             } else if (key === "address") {
  //               return res.textContent?.split("\n")[1];
  //             } else if (key === "bidIdentifier") {
  //               return res.textContent?.split("\n")[2].trim();
  //             } else if (key === "builtDate") {
  //               return res.textContent
  //                 ? `${res.textContent
  //                   .replace(/(\r\n\t|\n|\r\t|\s)/gm, "")
  //                   .replace("/", "-")}-01`
  //                 : null;
  //             } else if (res !== null) {
  //               return res.textContent.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
  //             }

  //             return res;
  //           },
  //           selector,
  //           key
  //         );

  //         dataResult[key] = currentValue;
  //       }

  //       // console.log("🔥 dataResult🔥 🔥");
  //       // console.log(dataResult);

  //       allDataResults.push(dataResult);
  //     }

  //     // Go to next page
  //     if (p < PAGE_COUNT - 1) {
  //       await page.click(`.paging li:nth-child(${p + 2}) a`);
  //       console.log(`🔥  go to page ${p + 2}🔥 🔥`);
  //       await new Promise(resolve => setTimeout(resolve, 3000));
  //     }
  //   }

  //   console.log("🔥 allDataResults size🔥 🔥");
  //   console.log(allDataResults.length);

  //   // // ********** STEP 2:: Insertion *************
  //   // // For each, if there is already record, update, else insert

  //   const { DB_USERNAME, DB_PASSWORD } = process.env;

  //   let createdRecords = [];
  //   let updatedRecords = [];
  //   let index = 0;

  //   for (let rec of allDataResults) {
  //     console.log(`🔥 processing ${++index} / ${allDataResults.length} 🔥 🔥`);

  //     let rows = await prisma.trAuctionRecord.findMany({
  //       where: {
  //         bidIdentifier: rec.bidIdentifier,
  //         subType: rec.subType,
  //       },
  //     });

  //     if (rows.length > 0) {
  //       // ======================================
  //       // HAS MATCH CASE
  //       // ======================================
  //       let matchedRecord = rows[0];
  //       if (
  //         matchedRecord["bid_price"] === rec.bidPrice &&
  //         matchedRecord["auction_type"] === rec.auctionType &&
  //         matchedRecord["sub_type"] === rec.subType
  //       ) {
  //         // ======================================
  //         // HAS MATCH - DO NOTHING CASE
  //         // ======================================
  //         console.log("🔥 skipping because same data ");
  //       } else {
  //         // ======================================
  //         // HAS MATCH - UPDATING CASE
  //         // ======================================
  //         console.log("🔥 adding detailed data ");
  //         rec = await getAuctionDetails(rec, page) as any;

  //         console.log("🔥 rec🔥 🔥");
  //         console.log(rec);

  //         let record_changes = matchedRecord["record_changes"];
  //         record_changes[currentDate] = {
  //           bidPrice: rec.bidPrice,
  //           subType: rec.subType,
  //           auctionType: rec.auctionType,
  //           auctionBidStartViewDate: rec.auctionBidStartViewDate,
  //           auctionBidStartDate: rec.auctionBidStartDate,
  //           auctionBidEndDate: rec.auctionBidEndDate,
  //           auctionBidReleaseDate: rec.auctionBidReleaseDate,
  //           auctionBidDecideDate: rec.auctionBidDecideDate,
  //           auctionBidSpecialAuctionStartDate:
  //             rec.auctionBidSpecialAuctionStartDate,
  //           auctionBidSpecialAuctionEndDate:
  //             rec.auctionBidSpecialAuctionEndDate,
  //           imageLink: rec.imageLink,
  //           link: rec.link,
  //         } as any;

  //         await prisma.trAuctionRecord.update({
  //           where: { id: matchedRecord.id },
  //           data: {
  //             bidPrice: rec.bidPrice,
  //             auctionType: rec.auctionType,
  //             subType: rec.subType,
  //             recordChanges: record_changes,
  //             updatedAt: new Date(),
  //           },
  //         });

  //         updatedRecords.push(rec);
  //       }
  //     } else {
  //       // ======================================
  //       // NO MATCH - CREATING CASE
  //       // ======================================
  //       // STEP 2.2 Getting more detailed data
  //       // GETTTING DETAILS DATA
  //       rec = await getAuctionDetails(rec, page) as any;

  //       await prisma.trAuctionRecord.create({
  //         data: {
  //           title: rec.title,
  //           bidPrice: rec.bidPrice,
  //           auctionType: rec.auctionType,
  //           bidIdentifier: rec.bidIdentifier,
  //           bidAuthority: rec.bidAuthority,
  //           type: rec.type,
  //           subType: rec.subType,
  //           address: rec.address,
  //           access: rec.access,
  //           accessExtra: rec.accessExtra,
  //           builtDate: rec.builtDate,
  //           buildingSize: rec.buildingSize,
  //           biddingDetails: rec.biddingDetails,
  //           recordChanges: {
  //             [currentDate]: {
  //               bidPrice: rec.bidPrice,
  //               subType: rec.subType,
  //               auctionType: rec.auctionType,
  //               auctionBidStartViewDate: rec.auctionBidStartViewDate,
  //               auctionBidStartDate: rec.auctionBidStartDate,
  //               auctionBidEndDate: rec.auctionBidEndDate,
  //               auctionBidReleaseDate: rec.auctionBidReleaseDate,
  //               auctionBidDecideDate: rec.auctionBidDecideDate,
  //               auctionBidSpecialAuctionStartDate:
  //                 rec.auctionBidSpecialAuctionStartDate,
  //               auctionBidSpecialAuctionEndDate:
  //                 rec.auctionBidSpecialAuctionEndDate,
  //               imageLink: rec.imageLink,
  //               link: rec.link,
  //             },
  //           }
  //         },
  //       });
  //       createdRecords.push(rec);
  //     }
  //   }

  //   // // ********** STEP 3 *************
  //   let LARK_URL =
  //     "https://open.larksuite.com/open-apis/bot/v2/hook/746ea496-4515-482b-8ebb-c9a0e08fdb0d"; // TLL仲介

  //   await sendLark(
  //     `[AQUSITION][競売物件] Total: ${allDataResults.length}, updated: ${updatedRecords.length}, saved: ${createdRecords.length}`,
  //     LARK_URL
  //   );

  //   if (updatedRecords.length) {
  //     let larkTitle = `[${moment().format(
  //       "YYYY-MM-DD"
  //     )}] 新規競売物件 Pick Up (更新物件数  ${updatedRecords.length})`;
  //     let larkElements = [];
  //     updatedRecords.forEach((r) => {
  //       larkElements.push({
  //         tag: "markdown",
  //         content: `[**【${r["title"]}】[${r["type"]}][${r["subType"]}][${r["auctionType"]
  //           }]**](${r["link"]}) 📍${r["address"]} 💰${r["bidPrice"] / 10000
  //           }万円 | ${r["builtDate"]} | ${r["buildingSize"]}m2 \n`,
  //       });

  //       larkElements.push({
  //         tag: "hr",
  //       });
  //     });
  //     await sendLarkCard(larkTitle, larkElements, LARK_URL);
  //   }

  //   if (createdRecords.length) {
  //     let larkTitle = `[${moment().format(
  //       "YYYY-MM-DD"
  //     )}] 新規競売物件 Pick Up (新規物件数  ${createdRecords.length})`;
  //     let larkElements = [];
  //     createdRecords.forEach((r) => {
  //       larkElements.push({
  //         tag: "markdown",
  //         content: `[**【${r["title"]}】[${r["type"]}][${r["subType"]}][${r["auctionType"]
  //           }]**](${r["link"]}) 📍${r["address"]} 💰${r["bidPrice"] / 10000
  //           }万円 | ${r["builtDate"]} | ${r["buildingSize"]}m2 \n`,
  //       });

  //       larkElements.push({
  //         tag: "hr",
  //       });
  //     });
  //     await sendLarkCard(larkTitle, larkElements, LARK_URL);
  //   }

  //   // Also be sending new incremental info on Lark

  //   return;
  // } catch (err: any) {
  //   if (page) {
  //     await page.close();
  //   }

  //   if (browser) {
  //     await browser.disconnect();
  //   }

  //   return;
  // } finally {
  //   if (page) {
  //     await page.close();
  //   }

  //   if (browser) {
  //     await browser.close();
  //   }
  // }
};

// fu();
