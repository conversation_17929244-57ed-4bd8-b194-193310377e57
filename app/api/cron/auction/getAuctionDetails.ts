export async function getAuctionDetails(rec: any, page: any) {
  console.log("🔥 Go to property details page ..");
  await page.goto(rec["link"], {
    waitUntil: "networkidle2",
  });

  const DETAILS_SELECTOR = {
    address: ".detail-box:nth-child(6) tr:nth-child(1) td",
    access: ".detail-box:nth-child(6) tr:nth-child(2) td",
    accessExtra: ".detail-box:nth-child(6) tr:nth-child(3) td",
    propertyDetails: ".detail-box:nth-child(13)",
    biddingDetails: ".detail-box:nth-child(8)",
    // auctionStartViewDate: `tr:nth-child(${2 * i + 1}) td > a`,
    // auctionStartDate: `tr:nth-child(${2 * i + 1}) td > a`,
    // auctionEndDate: `tr:nth-child(${2 * i + 1}) th > .resGroup`,
    // auctionBidDate: `tr:nth-child(${2 * i + 1}) th`, // need to get from ()
  } as any;

  for (const key of Object.keys(DETAILS_SELECTOR)) {
    const currentValue = await page.evaluate(
      (sel: any, key: any) => {
        const res = document.querySelector(sel);
        if (res === null) return res;

        if (key === "propertyDetails" || key === "biddingDetails") {
          return res.textContent.split("\n").filter((n: any) => n.length);
        } else if (res !== null) {
          return res.textContent.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
        }

        return res;
      },
      DETAILS_SELECTOR[key],
      key
    );

    if (key === "propertyDetails") {
      // ['事件番号', '令和05年（ヌ）第99号', '公告日', '2024/02/13', '管轄裁判所', '東京地方裁判所立川支部', '閲覧開始日', '2024/02/13', '買受方法', '期間入札 (1回目)', '入札期間', '2024/02/28 ～ 2024/03/06', '農地', '-', '開札期日', '2024/03/12', '売却決定期日', '2024/03/27', '特別売却期間', '2024/03/13 ～ 2024/03/15']

      const mapper = {
        公告日: "auctionPublicDate", // new
        農地: "isFarmLand",
        管轄裁判所: "bidAuthority",
        閲覧開始日: "auctionBidStartViewDate", // 2 week before
        入札期間: "auctionBidPeriod", // 2 week:: TODO break into start and END
        開札期日: "auctionBidReleaseDate", // ususally 6 day after
        売却決定期日: "auctionBidDecideDate", // 2 week after release
        特別売却期間: "auctionBidSpecialAuctionPeriod", // 2-3 days after,  bidding ends // TODO: break into sart and end
      } as any;

      Object.keys(mapper).forEach((k) => {
        if (currentValue.indexOf(k) !== -1) {
          if (k === "入札期間") {
            rec["auctionBidStartDate"] =
              currentValue[currentValue.indexOf(k) + 1].split("～")[0];
            rec["auctionBidEndDate"] =
              currentValue[currentValue.indexOf(k) + 1].split("～")[1];
          } else if (k === "特別売却期間") {
            rec["auctionBidSpecialAuctionStartDate"] =
              currentValue[currentValue.indexOf(k) + 1].split("～")[0];
            rec["auctionBidSpecialAuctionEndDate"] =
              currentValue[currentValue.indexOf(k) + 1].split("～")[1];
          } else {
            rec[mapper[k]] = currentValue[currentValue.indexOf(k) + 1] || "";
          }
        }
      });
    } else {
      rec[key] = currentValue;
    }
  }

  // // **********************************
  // // Also save the 3点セット
  // function sleep(milliSeconds) {
  //   return new Promise((resolve, reject) => {
  //     setTimeout(resolve, milliSeconds);
  //   });
  // }

  // // async function autoScroll(page) {
  // //   await page.evaluate(async () => {
  // //     await new Promise((resolve) => {
  // //       var totalHeight = 0;
  // //       var distance = 100;
  // //       var timer = setInterval(() => {
  // //         var scrollHeight = document.body.scrollHeight;
  // //         window.scrollBy(0, distance);
  // //         totalHeight += distance;

  // //         if (totalHeight >= scrollHeight - window.innerHeight) {
  // //           clearInterval(timer);
  // //           resolve();
  // //         }
  // //       }, 100);
  // //     });
  // //   });
  // // }

  // let SANTENSET_SELECTOR = ".btn-download"; //".download-direct" somehow is not added

  // await page.click(SANTENSET_SELECTOR);
  // await sleep(10000); // FIXME: Giving a bit more buffer while the item is being downloaded
  // console.log(`🔥 start downloading ... `);

  // const downloadPath =
  //   process.env.NODE_ENV === "development"
  //     ? path.resolve(__dirname, "downloaded")
  //     : "/tmp"; // has to be /tmp on lambda due to permission issue

  // console.log("🔥 downloadPath🔥 🔥");
  // console.log(downloadPath);

  // function getMostRecentFileName(dir) {
  //   var files = fs.readdirSync(dir);
  //   // use underscore for max()
  //   return _.maxBy(files, function (f) {
  //     var fullpath = path.join(dir, f);

  //     // ctime = creation time is used
  //     // replace with mtime for modification time
  //     return fs.statSync(fullpath).ctime;
  //   });
  // }

  // let filename = await (async () => {
  //   let filename;
  //   while (!filename || filename.endsWith(".crdownload")) {
  //     filename = getMostRecentFileName(downloadPath);

  //     await sleep(3000);
  //   }
  //   return filename;
  // })();
  // console.log("🔥 filename🔥 🔥");
  // console.log(filename);

  // console.log(`🔥 file name is retrived - it is ${filename}🔥 🔥`);
  // // On reins - [REINS] Path to search is /var/task/task/utility/download, file name is 100112887108.pdf
  // console.log(
  //   `🔥 File names for the file downloaded is ${filename}, total path name is ${downloadPath}/${filename}`
  // );
  // rec["linkFileName"] = `${downloadPath}/${filename}`;
  // rec["newFileName"] = `${rec["bidIdentifier"]}_${moment
  //   .utc()
  //   .format("YYYY-MM-DD-hh-mm-ss")}.pdf`; // rec["linkFileName"];

  return rec;
}