export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }
  // Refill the data
  // POST /api/geo/railway/station/usersCountRefill
  // Result: {updatedRecordsCount : 0}
  try {
    // TODO: this will also fill the links ... should separate them in the next iterations
    // let fillAuction = `${process.env.NODE_ENV === "development"
    //   ? "http://localhost:7001"
    //   : "https://api.tll.jp"
    //   }/api/tr/auction/records/createAuctionChangeRecords`;

    // console.log(`sending to url ${fillAuction}`);
    // const resUrlFillRosenka = await postRequest(fillAuction);
    // console.log(`success sending to url ${resUrlFillRosenka}`);
  } catch (err: any) {
    console.log("🔥 err 🔥");
    console.log(err, err.stack); // an error occurred
  }
};
