

import { getChromeBrowser, getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import dayjs from "dayjs";
import { LARK_URLS, sendLark, sendLarkCard } from "@/lib/thirdParty/lark";

// {"type": "04", "prefecture":"東京都","priceFrom":"4000","priceTo":"20000","yearFrom":"1985","landAreaFrom":"100","buildingAreaFrom":"160"}
// {"type": "04", "prefecture":"東京都","priceFrom":"4000","priceTo":"20000","yearFrom":"1985","landAreaFrom":"100","buildingAreaFrom":"160"}

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  let browser = null;
  let page = null;

  // let fu = async (event, context) => {
  try {
    browser = await getChromeBrowser();
    page = await getPageWithRandomUserAgent(browser);


    const allResults = [];

    // ****************************************
    // Step 1: Go page, and check if there are any results
    await page.goto("https://www.bit.courts.go.jp/app/top/pt001/h01", {
      waitUntil: "networkidle2",
    });

    const RESULT_BLOCK_SELECTOR =
      "div:nth-child(2) > .bit__top_infoListContainer .bit__flexbox_flexbasis_auto";

    const TOTAL_COUNT = await page.evaluate((sel_pagination) => {
      const res = document.querySelectorAll(sel_pagination);

      return res.length;
    }, RESULT_BLOCK_SELECTOR);

    console.log(`🔥 TOTAL_COUNT for current page is ${TOTAL_COUNT} 🔥`);

    if (!TOTAL_COUNT) {
      await sendLark({
        message: `[競売結果][No Results]`,
        url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
      });
      return;
    }

    for (let i = 0; i < TOTAL_COUNT; i++) {
      // Get the text, only look at 東京、神奈川、千葉、さいたま

      const selector = `div:nth-child(2) > .bit__top_infoListContainer .bit__flexbox_flexbasis_auto:nth-child(${i + 1
        })`;

      const text = await page.evaluate((sel) => {
        const res = document.querySelector(sel) as HTMLElement;

        if (res !== null)
          return res?.innerText?.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
        return res;
      }, selector);

      if (
        text !== null &&
        (text.indexOf("東京") > -1 ||
          text.indexOf("神奈川") > -1 ||
          text.indexOf("千葉") > -1 ||
          text.indexOf("さいたま") > -1)
      ) {
        // Click and get details
        await page.click(selector);
        console.log(`🔥 Go to page for ${text}, index is ${i + 1} 🔥`);
        await new Promise(resolve => setTimeout(resolve, 4000));

        // Get total count
        const TOTAL_COUNT_SELECTOR = ".bit__numberOfResult_totalNumber";
        const TOTAL_PAGES_COUNT = await page.evaluate((sel_pagination) => {
          const res = document.querySelector(sel_pagination) as HTMLElement;
          if (res !== null)
            return res.innerText.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
          return res;
        }, TOTAL_COUNT_SELECTOR) as string;

        const PAGE_COUNT = Math.ceil(parseInt(TOTAL_PAGES_COUNT) / 10);
        console.log(
          `🔥 Total result count is, ${TOTAL_PAGES_COUNT} page count is ${PAGE_COUNT}  🔥`
        );

        for (let i = 0; i < PAGE_COUNT; i++) {
          console.log(`🔥  Current at page ${i + 1}🔥 🔥`);

          const resultTypes = text.indexOf("特売") > -1 ? "tokubai" : "normal";
          // Getting count for results

          const RESULT_COUNT = await page.evaluate(() => {
            const res = document.querySelectorAll(
              ".bit__currentSearchCondition_regionBox"
            );

            return res.length;
          });

          // No bidder count because first come first serve
          const selectorForTokubai = {
            id: ".bit__currentSearchCondition_regionHeader .col-12:nth-child(2)",
            type: ".bit__currentSearchCondition_regionHeader .col-12:nth-child(1) span",
            resultPrice:
              ".bit__currentSearchCondition_regionBody > .col-12:nth-child(1) > div >  .col-12:nth-child(1)  .bit__text_orange",
            resultType: ".bit__result_InfoList li:nth-child(2) .px-2",
            resultWinnerType: ".bit__result_InfoList li:nth-child(3) .px-2",
          };

          const selectorForNormal = {
            id: ".bit__currentSearchCondition_regionHeader .col-12:nth-child(2)",
            type: ".bit__currentSearchCondition_regionHeader .col-12:nth-child(1) span",
            resultPrice:
              ".bit__currentSearchCondition_regionBody > .col-12:nth-child(1) > div >  .col-12:nth-child(1)  .bit__text_orange",
            resultType: ".bit__result_InfoList li:nth-child(2) .px-2",
            resultBiddingCount: ".bit__result_InfoList li:nth-child(3) .px-2",
            resultWinnerType: ".bit__result_InfoList li:nth-child(4) .px-2",
          };

          const selectors =
            resultTypes === "tokubai" ? selectorForTokubai : selectorForNormal as any;

          for (let i = 0; i < RESULT_COUNT; i++) {
            const res = {} as any;
            const selectorPrefix = `#resultDetailForm > .row.mx-0:nth-child(${24 + i
              })`;

            for (const k of Object.keys(selectors)) {
              const combinedSelector = `${selectorPrefix} ${selectors[k]}`;

              const v = await page.evaluate(
                (sel, k) => {
                  const res = document.querySelector(sel) as HTMLElement;
                  if (res !== null)
                    return res.innerText.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");

                  return res;
                },
                combinedSelector,
                k
              );

              res[k] = v;
            }

            if (res.type !== "土地" && res.type !== "マンション") {
              allResults.push({
                ...res,
                bidAuthority: text,
              });
            }
          }

          const NEXT_PAGE = `#resultDetailForm > div:nth-child(23).bit__resultList_pagerArea .pagination .page-item:nth-child(${PAGE_COUNT + 3
            }) .page-link`;
          page.click(NEXT_PAGE);
          await new Promise(resolve => setTimeout(resolve, 4000));
        }

        const postAuction = `${process.env.NODE_ENV === "development"
          ? "http://localhost:7001"
          : "https://api.tll.jp"
          }/api/tr/auction/records/saveResults`;
        console.log(`sending to url ${postAuction}`);

        const dataToSend = JSON.stringify({
          data: allResults,
          resultSet: text,
        });

        // TODO: uncomment this
        // await postRequest(postAuction, dataToSend);

        await page.goto("https://www.bit.courts.go.jp/app/top/pt001/h01", {
          waitUntil: "networkidle2",
        });

        console.log(`🔥 back to hp 🔥`);
        await new Promise(resolve => setTimeout(resolve, 4000));
      }
    }

    if (allResults.length) {
      const larkTitle = `[${dayjs().format(
        "YYYY-MM-DD"
      )}] 競売結果 Saved (物件数  ${allResults.length})`;
      const larkElements = [] as any;

      allResults.forEach((r) => {
        larkElements.push({
          tag: "markdown",
          content: `${r.id} | [${r.type}] | ${r.resultPrice} | ${r.resultType} | ${r.resultBiddingCount} | ${r.resultWinnerType} | ${r.resultSet}\n`,
        });

        larkElements.push({
          tag: "hr",
        });
      });

      await sendLarkCard(
        larkTitle,
        larkElements
      );
    }
    return;
  } catch (err: any) {
    console.log("🔥 Error in big loop 🔥");
    console.error(err);

    await sendLark({
      message: `[競売結果][ERR][${JSON.stringify(err)}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    // Make sure to log out on this
    // Else the session would be hanging //

    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.disconnect();
    }

    return;
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.disconnect();
      await browser.close();
    }

    return;
  }
};

// fu();
