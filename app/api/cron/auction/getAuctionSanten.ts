// 

// // const { getChrome } = require("../utility/getChrome");
// const chromium = require("chrome-aws-lambda");
// const path = require("path");
// const fs = require("fs");
// const randomUseragent = require("random-useragent");
// const { sendLark, sendLarkCard } = require("../utility/lark.js");
// const mysql = require("mysql2/promise");
// const moment = require("moment");
// const { saveToS3 } = require("../utility/saveToS3.js");
// const { match } = require("assert");

// let browser = null;
// let page = null;
// let connection = null;

// export async function GET(req: Request) {
// if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
//   return new Response("Unauthorized", { status: 401 });
// }

// if (req.method !== "GET") {
//   return new Response("Method Not Allowed", { status: 405 });
// }
//   const { DB_USERNAME, DB_PASSWORD } = process.env;

//   // let fu = async (event, context) => {
//   try {
//     connection = await mysql.createConnection({
//       host: "tll-database.chfmdnhyqg9c.ap-northeast-1.rds.amazonaws.com",
//       user: DB_USERNAME,
//       password: DB_PASSWORD,
//       database: "urbalytics",
//     });

//     browser = await chromium.puppeteer.launch({
//       args: chromium.args,
//       defaultViewport: chromium.defaultViewport,
//       executablePath: await chromium.executablePath,
//       headless: true, // process.env.NODE_ENV !== "dev",
//       ignoreHTTPSErrors: true,
//     });

//     page = await browser.newPage();

//     // Randomize the user agent to bypass anti-scraping on homes as well as athome
//     let dt = [];
//     let randomS = randomUseragent.getRandom(function (ua) {
//       if (dt.indexOf(ua.deviceType) === -1) {
//         dt.push(ua.deviceType);
//       }

//       return ua.deviceType === ""; // this is default, i think it means desktop
//     });

//     console.log(`🔥 Creating random agent, is ${randomS}`);
//     await page.setUserAgent(randomS);

//     // ********** STEP 1 *************
//     // Go To Page
//     await page.goto("https://www.bit.courts.go.jp/app/top/pt001/h01", {
//       waitUntil: "networkidle2",
//     });

//     let RESULT_BLOCK_SELECTOR =
//       "div:nth-child(1) > .bit__top_infoListContainer .bit__flexbox_flexbasis_auto";

//     const TOTAL_COUNT = await page.evaluate((sel_pagination) => {
//       let res = document.querySelectorAll(sel_pagination);

//       return res.length;
//     }, RESULT_BLOCK_SELECTOR);

//     console.log(`🔥 TOTAL_COUNT for current page is ${TOTAL_COUNT} 🔥`);

//     let updatedRecords = [];

//     for (let i = 0; i < TOTAL_COUNT; i++) {
//       // Get the text, only look at 東京、神奈川、千葉、さいたま

//       let selector = `div:nth-child(1) > .bit__top_infoListContainer .bit__flexbox_flexbasis_auto:nth-child(${
//         i + 1
//       })`;

//       let text = await page.evaluate((sel) => {
//         let res = document.querySelector(sel);

//         if (res !== null)
//           return res.innerText.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
//         return res;
//       }, selector);

//       // 東京地方裁判所本庁;
//       // 東京地方裁判所立川支部;
//       // さいたま地方裁判所本庁;さいたま地方裁判所川越支部;さいたま地方裁判所熊谷支部;さいたま地方裁判所越谷支部;
//       // 横浜地方裁判所本庁;横浜地方裁判所川崎支部;横浜地方裁判所横須賀支部;横浜地方裁判所小田原支部; 横浜地方裁判所相模原支部;
//       // 千葉地方裁判所本庁; 千葉地方裁判所松戸支部;

//       // ********** STEP 2 *************
//       // Continue only if ittousanken
//       if (
//         text !== null &&
//         (text.indexOf("東京") > -1 ||
//           text.indexOf("横浜") > -1 ||
//           text.indexOf("千葉") > -1 ||
//           text.indexOf("さいたま") > -1)
//       ) {
//         await page.click(selector);
//         console.log(`🔥 Go to page for ${text}, index on HP is ${i + 1} 🔥`);
//         await new Promise(resolve => setTimeout(resolve, 4000));

//         // ********** STEP 2.1 *************
//         // Get total results for this 裁判所
//         let TOTAL_COUNT_SELECTOR = ".bit__numberOfResult_totalNumber";
//         const TOTAL_PAGES_COUNT = await page.evaluate((sel_pagination) => {
//           let res = document.querySelector(sel_pagination);
//           if (res !== null)
//             return res.innerText.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
//           return res;
//         }, TOTAL_COUNT_SELECTOR);

//         let PAGE_COUNT = Math.ceil(TOTAL_PAGES_COUNT / 10);
//         console.log(
//           `🔥 Total result property count is ${TOTAL_PAGES_COUNT},  page count is ${PAGE_COUNT}  🔥`
//         );

//         for (let i = 0; i < PAGE_COUNT; i++) {
//           console.log(`🔥  Current at page ${i + 1}🔥 🔥`);

//           // ********** STEP 2.2 *************
//           // Get result blocks in a page
//           let RESULT_COUNT = await page.evaluate(() => {
//             let res = document.querySelectorAll(
//               ".bit__currentSearchCondition_regionBox"
//             );

//             return res.length;
//           });

//           console.log("🔥 RESULT_COUNT🔥 🔥");
//           console.log(RESULT_COUNT);

//           for (let i = 0; i < RESULT_COUNT; i++) {
//             let res = {};

//             const TITLE_SELECTOR = `.bit__searchResult:nth-child(${
//               i + 2
//             }) .bit__text_normal.font-weight-bold.mb-1 a`;

//             let titleContent = await page.evaluate((sel) => {
//               let res = document.querySelector(sel);
//               return res.innerText.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
//             }, TITLE_SELECTOR);

//             // ********** STEP 2.3 *************
//             // Getting the title of the property, pase and see if it exist in our DB
//             console.log("🔥 titleContent🔥 🔥");
//             console.log(titleContent);

//             let combinedIdentifier = titleContent
//               .replace("地方裁判所", "")
//               .replace("支部", "")
//               .replace("令和", "R")
//               .replace("(", "")
//               .replace(")第", "")
//               .replace("年", "")
//               .replace("　", "")
//               .split("号")[0];

//             let query = `SELECT * FROM tr_auction_records WHERE bid_identifier = "${combinedIdentifier}" AND santen_link IS NULL`;
//             console.log("🔥 query🔥 🔥");
//             console.log(query);

//             const [rows, fields] = await connection.execute(query, []);
//             if (rows.length) {
//               let matchedRecord = rows[0];

//               console.log(`🔥 result id is ${matchedRecord.id} 🔥`);
//               // ********** STEP 2.4 *************
//               // If exist then go and download 3 ten set

//               const [target] = await Promise.all([
//                 new Promise((resolve) =>
//                   browser.once("targetcreated", resolve)
//                 ),
//                 page.click(TITLE_SELECTOR),
//               ]);

//               let newPage = await target.page();
//               await newPage.bringToFront();
//               await newPage.screenshot({
//                 path: `debug/${moment().utc().format("MM-DD HH-mm-ss")}.png`,
//               });

//               const SANTEN_BUTTON = "#threeSetPDF";
//               await newPage.waitForSelector(SANTEN_BUTTON);

//               const downloadPath =
//                 process.env.NODE_ENV === "development"
//                   ? path.resolve(__dirname, "downloaded")
//                   : "/tmp"; // has to be /tmp on lambda due to permission issue
//               console.log(`🔥 [SANTEN] downloading path is ${downloadPath}`);

//               await newPage._client.send("Browser.setDownloadBehavior", {
//                 behavior: "allow",
//                 downloadPath,
//                 // eventsEnabled: true,
//               });

//               function getMostRecentFileName(dir) {
//                 var files = fs.readdirSync(dir);
//                 // use underscore for max()
//                 return _.maxBy(files, function (f) {
//                   var fullpath = path.join(dir, f);

//                   // ctime = creation time is used
//                   // replace with mtime for modification time
//                   return fs.statSync(fullpath).ctime;
//                 });
//               }

//               function sleep(milliSeconds) {
//                 return new Promise((resolve, reject) => {
//                   setTimeout(resolve, milliSeconds);
//                 });
//               }

//               await newPage.click(SANTEN_BUTTON);
//               await sleep(5000);

//               let filename = await (async () => {
//                 let filename;
//                 while (!filename || filename.endsWith(".crdownload")) {
//                   filename = getMostRecentFileName(downloadPath);

//                   await sleep(3000);
//                 }
//                 return filename;
//               })();

//               let newFileName = `[AUCTION]_${combinedIdentifier}_${filename}`; // All are pdf by default

//               console.log(
//                 `🔥 file name is retrived - it is old: ${filename} / new: ${newFileName}🔥 🔥`
//               );

//               // ********** STEP 2.4 *************
//               // Then upload and be updating the Database
//               // Adding counter and be sending to Lark

//               let saveToS3Res = {};
//               saveToS3Res = await saveToS3(
//                 `${downloadPath}/${filename}`,
//                 newFileName
//               );

//               // TODO/FIXME: this does not always get to save the link //
//               // saveToS3Res["Location"];

//               var sql =
//                 "UPDATE `tr_auction_records` SET santen_link=?  WHERE id=?";
//               await connection.execute(sql, [
//                 saveToS3Res["Location"],
//                 matchedRecord.id,
//               ]);

//               console.log(
//                 `🔥 [Auctuon] Saving s3 with id ${matchedRecord["id"]} identifier ${combinedIdentifier} link is ${saveToS3Res["Location"]}🔥`
//               );

//               updatedRecords.push({
//                 ...matchedRecord,
//                 santenLink: saveToS3Res["Location"],
//               });

//               await newPage.close();
//             } else {
//               console.log("🔥  no results 🔥 🔥");
//             }
//           }

//           await page.bringToFront();
//           let NEXT_PAGE = `#propertyResultForm .pagination .page-item:nth-child(${
//             PAGE_COUNT + 3
//           }) .page-link`;

//           await page.click(NEXT_PAGE);
//           await new Promise(resolve => setTimeout(resolve, 4000));
//         }

//         // await sendLark(
//         //   `[競売結果][${text}] ${JSON.stringify(allResults)}`,
//         //   "https://open.larksuite.com/open-apis/bot/v2/hook/746ea496-4515-482b-8ebb-c9a0e08fdb0d"
//         // );

//         await page.goto("https://www.bit.courts.go.jp/app/top/pt001/h01", {
//           waitUntil: "networkidle2",
//         });

//         console.log(`🔥 back to hp 🔥`);
//         await new Promise(resolve => setTimeout(resolve, 4000));
//       }
//     }

//     let larkTitle = `[${moment().format(
//       "YYYY-MM-DD"
//     )}] 競売3点セット Saved (物件数  ${updatedRecords.length})`;
//     let larkElements = [];

//     updatedRecords.forEach((r) => {
//       larkElements.push({
//         tag: "markdown",
//         content: `${r.id} | [${r.santenLink}] `,
//       });

//       larkElements.push({
//         tag: "hr",
//       });
//     });

//     await sendLarkCard(
//       larkTitle,
//       larkElements,
//       "https://open.larksuite.com/open-apis/bot/v2/hook/746ea496-4515-482b-8ebb-c9a0e08fdb0d"
//     );

//     return;
//   } catch (err: any) {
//     console.log("🔥 Error in big loop 🔥");
//     console.error(err);

//     await sendLark(
//       `[競売結果][ERR][${JSON.stringify(err)}`,
//       "https://open.larksuite.com/open-apis/bot/v2/hook/746ea496-4515-482b-8ebb-c9a0e08fdb0d"
//     );

//     // Make sure to log out on this
//     // Else the session would be hanging //

//     if (connection) {
//       await connection.close();
//     }

//     if (page) {
//       await page.close();
//     }

//     if (browser) {
//       await browser.disconnect();
//     }

//     return;
//   } finally {
//     if (connection) {
//       await connection.close();
//     }

//     if (page) {
//       await page.close();
//     }

//     if (browser) {
//       await browser.disconnect();
//       await browser.close();
//     }

//     return;
//   }
// };

// // fu();
