import { NextResponse } from "next/server";
import { randomUUID } from "crypto";
import goDp from "./goDp";
import { prisma } from "@/lib/prisma";
import { LARK_URLS, sendLark, sendLarkCard } from "@/lib/thirdParty/lark";

import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz"
import { updateNewlyAddedDataAction } from "@/actions/sumitomoAuction";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { logger } from "@/lib/logger";
import duration from "dayjs/plugin/duration";
import { auctionTypeMapper } from "./utility/auctionTypeMapper";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
dayjsWithTz.extend(duration);

const MAX_RECORDS = 100;

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  let browser;
  let page;
  logger.debug("process.env.NODE_ENV", process.env.NODE_ENV);

  let currentMinute = dayjsWithTz().tz('Asia/Tokyo').minute();
  let useSecondAccount = currentMinute > 30;

  const { SIMITOMO_AUCTION_USERNAME, SIMITOMO_AUCTION2_USERNAME, SIMITOMO_AUCTION_PASSWORD, SIMITOMO_AUCTION2_PASSWORD } = process.env;
  if (!SIMITOMO_AUCTION_USERNAME || !SIMITOMO_AUCTION_PASSWORD || !SIMITOMO_AUCTION2_USERNAME || !SIMITOMO_AUCTION2_PASSWORD) {
    throw new Error("SIMITOMO_AUCTION_USERNAME or SIMITOMO_AUCTION_PASSWORD is not set");
  }

  let RECORDS_VIEWED = 0;
  let RECORDS_CREATED_DATA: any[] = [];

  try {
    browser = await getChromeBrowser();

    let retries = 3;
    let startTime, endTime;

    for (let i = 0; i < retries; i++) {
      page = await getPageWithRandomUserAgent(browser);
      RECORDS_CREATED_DATA = [];
      RECORDS_VIEWED = 0;

      startTime = dayjsWithTz();
      try {
        const ADMIN_LOGIN_URL =
          "https://www.sa-step.jp/sumitomo_bid_manager/template/login_bid.php";

        await page.goto(ADMIN_LOGIN_URL, { waitUntil: "networkidle2" });

        // Capture browser console messages
        page.on("console", (msg: any) => logger.debug("BROWSER LOG:", msg.text()));

        await page.goto(ADMIN_LOGIN_URL);

        await page.waitForSelector("#login_name");
        await page.type("#login_name", useSecondAccount ? SIMITOMO_AUCTION2_USERNAME : SIMITOMO_AUCTION_USERNAME);
        await page.type("#pwd", useSecondAccount ? SIMITOMO_AUCTION2_PASSWORD : SIMITOMO_AUCTION_PASSWORD);
        await page.click("input.btn_medium_c:nth-child(3)");

        // ***********************************************
        // Step 2: Gettting data needed
        let goToDPFlag = true;
        for (let i = 1; i <= MAX_RECORDS; i++) {
          // Only need to reset this if you go to the DP pages, then need to restart 
          logger.info(`preparing to get data ${i}/${MAX_RECORDS}`);
          if (goToDPFlag) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // ✅ Works in all versions
            await page.goto(
              "https://www.sa-step.jp/sumitomo_bid_manager/template/bid_bukkenlistTableView.php?debug_mode=",
              {
                waitUntil: "networkidle2",
              }
            );
            logger.debug("📄 Navigated to new page! Waiting for 2s");
            await new Promise(resolve => setTimeout(resolve, 1000)); // ✅ Works in all versions

            const SORT_SELECTOR =
              "body > form > table:nth-child(1) tr:nth-child(8) input:nth-child(7)";
            await page.waitForSelector(SORT_SELECTOR);
            logger.debug("🔄 Clicking sort selector!");
            await page.click(SORT_SELECTOR);

            logger.debug("🔄 Clicking show box!");
            await page.click(".showbox.btn_medium");
            await new Promise(resolve => setTimeout(resolve, 2000)); // ✅ Works in all versions
            await page.select("#page1 #limit", "100");
            await new Promise(resolve => setTimeout(resolve, 2000)); // ✅ Works in all versions*
          }

          const todayString = dayjsWithTz().tz('Asia/Tokyo').format("YYYY-MM-DD");
          const ydayString = dayjsWithTz().tz('Asia/Tokyo').add(-1, "day").format("YYYY-MM-DD");
          const rowSelectors = "#table_div tr";
          const newDataSelectors: Record<string, string> = {
            type: "td:nth-child(1)",
            name: "td:nth-child(2)",
            auction_url: "td:nth-child(2) a",
            price: "td:nth-child(3)",
            land_size: "td:nth-child(4)",
            info_start_date: "td:nth-child(5)",
            bid_end_date: "td:nth-child(6)",
          };

          let currentResult: Record<string, any> = { rawLinks: [], material_links: {} };
          for (const field of Object.keys(newDataSelectors)) {
            const selector = `${rowSelectors}:nth-child(${1 + i}) ${newDataSelectors[field] as string}`;
            let currentValue;

            currentValue = await page.evaluate(
              (sel: string, field: string) => {
                const res = document.querySelector(sel);

                if (res !== null) {
                  if (field === "auction_url") {
                    const href = res.getAttribute("href");
                    if (href) {
                      return `https://www.sa-step.jp/sumitomo_bid_manager/template/bid_welcom_detail.php?bukken_open_key=${href.split("=")[1]
                        }`;
                    }
                  } else {
                    return res.textContent?.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
                  }
                }
                return res;
              },
              selector,
              field
            );
            currentResult[field as keyof typeof currentResult] = currentValue;
          }

          if (
            currentResult?.info_start_date !== ydayString &&
            currentResult?.info_start_date !== todayString
          ) {
            logger.warn("Breaking because already past 2 days right date");
            // await new Promise(resolve => setTimeout(resolve, 1000)); // ✅ Works in all versions
            continue;
          }

          RECORDS_VIEWED++;

          currentResult["price"] =
            parseInt(currentResult["price"]?.replace(/,/g, "")) / 10000;

          const whereClause = {
            bidEndDate: currentResult.bid_end_date ? dayjsWithTz(currentResult.bid_end_date).tz('Asia/Tokyo').startOf('day').utc().toDate() : null,
            name: currentResult.name,
            price: currentResult.price
          }

          const existing = await prisma.proRawSumitomoAuction.findFirst({
            where: whereClause
          });

          if (existing) {
            logger.info("skipping because already existing");
            goToDPFlag = false;
            continue;
          } else {
            goToDPFlag = true;
          }

          currentResult = await goDp(page, currentResult);

          // ***********************************************
          // Step 3.2: Insert into the DB
          logger.info("[Creating] currentResult.. and saving to db", JSON.stringify(currentResult));

          const createData = await prisma.proRawSumitomoAuction.create({
            data: {
              id: randomUUID(),
              sumitomoId: currentResult.sumitomo_id,
              type: currentResult.type,

              recordType: auctionTypeMapper[currentResult.type as keyof typeof auctionTypeMapper] || UserLambdaRecordType.BUILDING,

              name: currentResult.name,
              auctionUrl: currentResult.auction_url,
              hpUrl: currentResult.hp_url,
              price: currentResult.price,
              landSize: currentResult.land_size || null,
              infoStartDate: currentResult.info_start_date ? dayjsWithTz(currentResult.info_start_date).tz('Asia/Tokyo').startOf('day').utc().toDate() : null,
              bidEndDate: currentResult.bid_end_date ? dayjsWithTz(currentResult.bid_end_date).tz('Asia/Tokyo').startOf('day').utc().toDate() : null,
              address: currentResult.address,
              pocName: currentResult.poc_name,
              pocNumber: currentResult.poc_number,
              pocEmail: currentResult.poc_email,
              propertyStatus: currentResult.property_status,
              nearestStation: currentResult.nearest_station?.slice(0, 10), // sometimes the processing might be wrng and you get a lot of data
              comments: currentResult.comments,
              materialLinks: currentResult.material_links,
              yearlyIncome: currentResult.yearlyIncome || null,
            }
          });
          logger.debug("createDataNewSumitomoAuction", createData.id);
          RECORDS_CREATED_DATA.push(createData);
        }
        break;
      } catch (error) {
        logger.error("Error in cron job", error);
        await new Promise(resolve => setTimeout(resolve, 5000));
        await sendLark({
          message: `[❌${i >= 1 ? "❌" : ""}${i >= 2 ? "❌" : ""}][住友競売][Error saving sumitomo keibai]` + error,
          url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
        });

        if (page) {
          await page.close();
        }
        logger.info("Creating a new page for new attempt ..");
        page = await getPageWithRandomUserAgent(browser);
      } finally {
        endTime = dayjsWithTz();
        const duration = dayjsWithTz.duration(endTime.diff(startTime));
        // ***********************************************
        // Step 4: Pushign data to Lark
        // ***********************************************
        const larkTitle = `【✨v2✨新規すみふ競売】[${dayjsWithTz().tz('Asia/Tokyo').format(
          "YYYY-MM-DD HH:mm:ss"
        )}]  (物件数  ${RECORDS_CREATED_DATA.length} / ${RECORDS_VIEWED})`;

        const larkElements = [] as any[];
        RECORDS_CREATED_DATA.forEach((r) => {
          larkElements.push({
            tag: "markdown",
            content: `【[${r["name"]}](${r["auctionUrl"]})】${r["type"]} | 📍${r["address"]} | 💰${r["price"]}万円 | [HP](${r["hpUrl"]}) | ${r["landSize"]}m2 \n`,
          });

          larkElements.push({
            tag: "hr",
          });
        });

        // ***********************************************
        // Step 5: Adding a job to fill in some more details
        // ***********************************************
        let res = await updateNewlyAddedDataAction();
        await sendLarkCard(larkTitle, larkElements as any[]);
        await sendLark({
          message: `[住友競売][${duration.minutes()}:${duration.seconds()}][${RECORDS_CREATED_DATA.length} records created] ${res.data.recordsMatched} records matched and updated`,
          url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
        });
      }
    }

    return NextResponse.json({ message: "Serverless Chrome executed!", title: "Serverless Chrome executed!" });
  } catch (error) {
    logger.error("Error in cron job", error);
    await sendLark({
      message: `[Main Loop][Error saving sumitomo keibai]` + error,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return NextResponse.json({ message: "error", error: error });
  } finally {
    logger.info("Closing page and browser!");
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
}