import { logger } from "@/lib/logger";
import { getDownloadPathInFunction, afterClickDownloadFileAndGetFilename } from "@/lib/thirdParty/chromeBrowser";
import { saveToS3 } from "@/lib/thirdParty/s3";

export default async function goDp(page: any, currentResult: any) {
  await page.goto(currentResult["auction_url"], {
    waitUntil: "networkidle2",
  });

  await new Promise(resolve => setTimeout(resolve, 3000)); // ✅ Works in all versions

  const dpDataSelectors = {
    sumitomo_id: "#table_div tr:nth-child(1) td:nth-child(3)",
    appeal_point: "#table_div tr:nth-child(3) td:nth-child(3)",
    address: "#table_div tr:nth-child(6) td:nth-child(3)",
    poc_name: "#table_div tr:nth-child(9) td:nth-child(3)",
    poc_number: "#table_div tr:nth-child(10) td:nth-child(3)",
    poc_email: "#table_div tr:nth-child(11) td:nth-child(3)",
    property_status: "#table_div tr:nth-child(13) td:nth-child(3)",
    hp_url: "#table_div tr:nth-child(14) td:nth-child(3)",
    comment_special: "#table_div tr:nth-child(15) td:nth-child(3)",
    comment_tokuchou: "#table_div tr:nth-child(17) td:nth-child(3)",
  };


  for (const field of Object.keys(dpDataSelectors)) {
    const currentValue = await page.evaluate(
      (sel: string, field: string) => {
        const res = document.querySelector(sel);
        if (res !== null) {
          return res.textContent?.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
        }
        return res;
      },
      dpDataSelectors[field as keyof typeof dpDataSelectors],
      field
    );
    currentResult[field as keyof typeof currentResult] = currentValue;
  }

  // ***********************************************
  // Step 1 Parsing the data
  // ***********************************************

  currentResult["comments"] = (currentResult.comment_special || "")
    .concat(";")
    .concat(currentResult.comment_tokuchou || "")
    .concat(";")
    .concat(currentResult.appeal_point || "");

  if (currentResult.land_size?.indexOf("㎡") > -1) {
    currentResult.land_size = parseFloat(
      currentResult.land_size?.split("㎡")[0]
    );
  }

  currentResult["nearest_station"] = null;
  if (currentResult.comments?.indexOf("駅") > -1) {
    if (currentResult.comments.indexOf("『") > -1) {
      currentResult["nearest_station"] = currentResult.comments
        .split("『")[1]
        .split("』")[0];
    } else if (currentResult.comments.indexOf("「") > -1) {
      currentResult["nearest_station"] = currentResult.comments
        .split("「")[1]
        .split("」")[0];
    } else if (currentResult.comments.indexOf("線") > -1) {
      currentResult["nearest_station"] = currentResult.comments
        .split("線")[1]
        .split("駅")[0];
    }
  }

  if (
    currentResult.comments?.indexOf("利回り") > -1 &&
    currentResult.comments?.indexOf("％") > -1
  ) {
    const sign = (currentResult["roi"] = parseFloat(
      currentResult.comments
        .split("利回り")[1]
        .split("％")[0]
        .replace(/[^\d.-]+/g, "") // remove things like ":"
    ));
    currentResult["yearlyIncome"] =
      (currentResult["price"] * currentResult["roi"]) / 10000 / 100;
  }

  // ***********************************************
  // Step 2: Getting the material
  // ***********************************************
  const linkSelectors = {
    materials: "#table_div tr:nth-child(19) td:nth-child(3)",
    zumen: "#table_div tr:nth-child(20) td:nth-child(3)",
    qna: "#table_div tr:nth-child(21) td:nth-child(3)",
  };

  const downloadPath = await getDownloadPathInFunction(page);

  for (const k of Object.keys(linkSelectors)) {
    const linksForThisSection = await page.$$(`${linkSelectors[k as keyof typeof linkSelectors]} a`);
    logger.info(`${linksForThisSection.length} results for key ${k}`);

    for (const link of linksForThisSection) {
      try {
        logger.debug("Clicked on the download link... wait for 2000s");
        // This is hardcode, but should be fine because the url is something like this 
        // https://www.sa-step.jp/sumitomo_bid_manager/template/bukken_download_for_bid.php?bukken_open_key=5c353a5bfd23d0b89f24887a95ff5046&file_id=586725
        await new Promise(resolve => setTimeout(resolve, 2000));
        const {
          localFilePath,
          oldFileName,
          newFileName
        } = await afterClickDownloadFileAndGetFilename(page, "download", k, downloadPath, {
          type: "selfClicker",
          selector: link,
        });
        const s3Url = await saveToS3(localFilePath, newFileName);
        logger.info(
          `[Sumitomo] Saving s3 with name ${newFileName}, link is ${s3Url}`
        );
        // if use new name will be prefixed with the datetimestamp and it is too noisye
        currentResult.material_links[oldFileName] = s3Url;
      } catch (error) {
        // Catch tthis error, do not need to stop the process .. just do not save it 
        logger.error("Error in downloading the file", error);
      }

      logger.info("currentResult.material_links", currentResult.material_links);
    }
  }

  return currentResult;
}

