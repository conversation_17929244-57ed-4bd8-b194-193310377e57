import { prisma } from "@/lib/prisma";
import { getChromeBrowser, getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { REINS_TASKS } from "../../reins/constants/reinsTasks";
import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { ProMansionRentProps, ProMansionRentRecordType } from "@/lib/definitions/proMansionRent";
import { getMansionRecordOrCreateNewForNameFromMansionRent } from "../../reinsFill/building/getMansionRecordOrCreateNewForNameFromMansionRent";
import { getSelectedDataForeinsRentSold } from "./getSelectedDataForeinsRentSold";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { getSelectedDataForeinsRentSoldBuildingPart } from "./getSelectedDataForeinsRentSoldBuildingPart";
import duration from "dayjs/plugin/duration";
dayjsWithTz.extend(duration);

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({
      status: "unauthorized"
    });
  }

  let browser = null;
  let page = null;
  let retries = 3;

  let dayOfMonth = dayjsWithTz().day();

  let tasks = [
    {
      title: "東京都, 18+,  today",
      url: { type: "03", prefecture: "東京都", soldRentFrom: "8" },
      type: "MANSION"
    },
    {
      title: "東京都, ~8,  today",
      url: { type: "03", prefecture: "東京都", soldRentTo: "7.99" },
      type: "MANSION"
    },
    {
      title: "神奈川県, 18+,  today",
      url: { type: "03", prefecture: "神奈川県", soldRentFrom: "8" },
      type: "MANSION"
    },
    {
      title: "神奈川県, ~8,  today",
      url: { type: "03", prefecture: "神奈川県", soldRentTo: "7.99" },
      type: "MANSION"
    },
    {
      title: "千葉県, 18+,  today",
      url: { type: "03", prefecture: "千葉県", soldRentFrom: "8" },
      type: "MANSION"
    },
    {
      title: "千葉県, ~8,  today",
      url: { type: "03", prefecture: "千葉県", soldRentTo: "7.99" },
      type: "MANSION"
    },
    {
      title: "埼玉県, 18+,  today",
      url: { type: "03", prefecture: "埼玉県", soldRentFrom: "8" },
      type: "MANSION"
    },
    {
      title: "埼玉県, ~8,  today",
      url: { type: "03", prefecture: "埼玉県", soldRentTo: "7.99" },
      type: "MANSION"
    }
  ]

  let tasksForBuildingPart = [
    {
      title: "東京都, ビル一部,  today",
      url: { type: "05", prefecture: "東京都" },
      type: "BUILDING_PART"
    },
    {
      title: "神奈川県, ビル一部,  today",
      url: { type: "05", prefecture: "神奈川県" },
      type: "BUILDING_PART"
    },
    {
      title: "千葉県, ビル一部,  today",
      url: { type: "05", prefecture: "千葉県" },
      type: "BUILDING_PART"
    },
    {
      title: "埼玉県, ビル一部,  today",
      url: { type: "05", prefecture: "埼玉県" },
      type: "BUILDING_PART"
    }
  ]

  try {
    let taskIndex = 0;
    let startTime, endTime;

    for (let task of [
      ...tasks,
      ...(dayOfMonth % 3 === 0 ? tasksForBuildingPart : []),
    ]) {
      taskIndex++;
      startTime = dayjsWithTz();

      // FIXME: should not need new browser for each new tasks
      let allRecordsCralwed = [] as ProMansionRentProps[];

      try {
        for (let i = 0; i < retries; i++) {
          try {
            if (!browser) {
              browser = await getChromeBrowser();
            }

            page = await getPageWithRandomUserAgent(browser);

            allRecordsCralwed = task.type === "MANSION" ? await getSelectedDataForeinsRentSold({
              page,
              task,
              selectors: REINS_TASKS.MANSION_RENT.selectorsSold
            }) as ProMansionRentProps[] : await getSelectedDataForeinsRentSoldBuildingPart({
              page,
              task,
              selectors: REINS_TASKS.BUILDING_PART_RENT.selectorsSold
            }) as ProMansionRentProps[];

            break; // Do not trigge retry
          } catch (err: any) {
            console.log("🔥 Error during getting selector data 🔥");
            console.error(err);

            await sendLark({
              message: `❌${i >= 1 ? "❌" : ""}${i >= 2 ? "❌" : ""}[Mansion rent task][${taskIndex}/${tasks.length}][Error getting selector data, retry]` +
                err,
              url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
            });

            if (page) {
              await page.close();
            }

            if (browser) {
              await browser.close();
              browser = null; // Ensure a fresh browser instance is used next retry
            }
          }
        }
      } catch (err: any) {
        console.log("🔥 Error during getting selector data 🔥");
        console.error(err);

        await sendLark({
          message: `[Mansion/Building Part rent task][${taskIndex}/${tasks.length}][Error getting selector data]` +
            err,
          url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
        });
      }

      // 保存结果到 output.json 文件
      // await writeFile(`downloaded/${dayjsWithTz().format("YYYY-MM-DD HH:mm:ss")}-${task.title}[${allRecordsCralwed.length}].json`, JSON.stringify(allRecordsCralwed, null, 2));
      // console.log("🔥 Results saved to output.json, total: ", allRecordsCralwed.length, "🔥");

      let index = 0;
      let createdCount = 0;

      for (const recCrawled of allRecordsCralwed) { // 
        // Fixme: price might drop wth same reins number
        let matchedRecord = await prisma.proMansionRent.findFirst({
          where: {
            brokerReinsNumber: recCrawled.brokerReinsNumber,
            brokerStatus: "成約",
            createdAt: {
              gte: dayjsWithTz().subtract(1, "day").toDate(),
            }
          },
        });

        logger.info(`🔥 saving ${++index} of ${allRecordsCralwed.length}, ${matchedRecord?.id ? "has match so skip" : "no match so create...."} 🔥`);

        if (matchedRecord === null) {
          const matchRailwayStationGroup = await prisma.geoRailwayStation.findMany({
            where: {
              name: recCrawled.nearestStation,
              prefectureCode: { in: [11, 12, 13, 14] }, // sometimes property in 埼玉 but nearest station is in 東京
            },
          });

          matchedRecord = await prisma.proMansionRent.create({
            data: {
              recordType: task.type,
              propertyType: recCrawled.propertyType,

              brokerReinsNumber: recCrawled.brokerReinsNumber, // do not save the same number 
              brokerType: recCrawled.brokerType,
              brokerStatus: "成約",

              feeRent: recCrawled.feeRent,
              feeManagement: recCrawled.feeManagement,
              feeUtility: recCrawled.feeUtility,
              feeGiftMoney: recCrawled.feeGiftMoney,
              feeDepositMoney: recCrawled.feeDepositMoney,

              landType: recCrawled.landType,
              unitSize: recCrawled.unitSize,
              unitLayout: recCrawled.unitLayout,
              unitLevel: recCrawled.unitLevel || null,

              buildingBuiltYear: recCrawled.buildingBuiltYear || null,
              buildingAddress: recCrawled.buildingAddress,
              buildingName: recCrawled.buildingName,

              nearestStation: recCrawled.nearestStation || null,
              transport: recCrawled.transport,
              nearestStationWalkMinute: recCrawled.nearestStationWalkMinute,
              ...(matchRailwayStationGroup.length > 0 ? { nearestStationGroupId: matchRailwayStationGroup[0].stationGroupId } : {}),

              roadConnection: recCrawled.roadConnection,
              roadConnectionFirstFacing: recCrawled.roadConnectionFirstFacing,
            } as any,
          });
          createdCount++;
          logger.info(`🔥 [Case 1] new record ... creating, id is ${matchedRecord.id}`);
        } else {
          logger.info(`🔥 [Case 2] matched record ... skip, id is ${matchedRecord.id}`);
        }

        if (recCrawled.buildingName) {
          const matches =
            recCrawled.nearestStationWalkMinute?.match(/徒歩(\d+)分/);
          let nearestStationWalkMinute = 0;

          if (
            recCrawled.nearestStationWalkMinute !== undefined &&
            matches !== undefined &&
            matches?.length === 2
          ) {
            nearestStationWalkMinute = parseInt(matches[1], 10);
          }

          const { recordId, isNew, record } = await getMansionRecordOrCreateNewForNameFromMansionRent({
            isKubun: task.type === "MANSION",
            name: recCrawled.buildingName || "",
            address: recCrawled.buildingAddress || "", // wont be "" as it is a must have field 
            nearestStation: recCrawled.nearestStation || "",
            ...(recCrawled.buildingBuiltYear && recCrawled.buildingBuiltYear > 0 ? { buildingBuiltYear: recCrawled.buildingBuiltYear } : {}),
            transport: recCrawled.transport || "",
            ...(nearestStationWalkMinute > 0 ? { nearestStationWalkMinute: nearestStationWalkMinute } : {}),
          }) as {
            recordId: string,
            isNew: boolean,
            record?: ProBuildingProps,
          };

          if (recordId) {
            await prisma.proMansionRent.update({
              where: { id: matchedRecord.id },
              data: {
                buildingId: recordId,
                ...(record?.longitude && { locationLongitude: record.longitude }),
                ...(record?.latitude && { locationLatitude: record.latitude }),
                ...(record?.postalCode && { locationPostalCode: record.postalCode.toString() }),
              },
            });
          }
        }
      }

      endTime = dayjsWithTz();
      const duration = dayjsWithTz.duration(endTime.diff(startTime));

      await sendLark({
        message: `[⚙️][${task.type === "MANSION" ? "Mansion" : "Building Part"} Rent Sold][🕒${duration.minutes()}:${duration.seconds()}][${task.title}][${taskIndex} / ${tasks.length}] ${createdCount} / ${allRecordsCralwed.length} created`,
        url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
      });
    }

    return NextResponse.json({
      status: "success"
    });
  } catch (err: any) {
    logger.error("🔥 err", err);

    await sendLark({
      message: `[⚙️][❌][FILL Mansion Rent] ${JSON.stringify(err)}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return NextResponse.json({
      status: "error",
      message: err
    });
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
