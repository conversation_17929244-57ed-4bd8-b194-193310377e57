import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { logger } from "@/lib/logger";
import { login } from "../../reins/utility/reinsLogin";
import { getSelectorRentDataForOneBlock } from "../../reins/rentMansion/getSelectorRentDataForOneBlock";

export async function getSelectedDataForeinsRentSoldBuildingPart({
  page,
  task,
  selectors
}: {
  page: any,
  task: any,
  selectors: any
}) {

  let criteria = task.url;

  console.log(`🔥 [REINS] Getting data for ${JSON.stringify(criteria)}`);

  await login({ page, account: null });

  // Go to search 賃貸物件
  let searchButtonSelector =
    ".card:nth-child(2) > div > div > div:nth-child(2) button";
  await page.waitForSelector(searchButtonSelector);
  await page.click(searchButtonSelector);

  console.log("🔥 Go to search page ..");
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Step 2 Fill in critieral
  let typeSelector =
    ".card:nth-child(7) .card-body .row:nth-child(2) .col-sm-4:nth-child(1) select";
  await page.waitForSelector(typeSelector);
  await page.select(typeSelector, criteria["type"]); // 03 - マンション 04 - 売外全(住宅以外建物全部), 02 一戸建て, 01 土地

  // Select 成約
  await page.click(
    ".card:nth-child(7) .card-body .row:nth-child(1) .col-sm-4:nth-child(1) .custom-radio:nth-child(2) input"
  );

  // Location
  if (criteria["prefecture"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(2) .row:nth-child(2) input",
      criteria["prefecture"]
    );
  }

  if (criteria["location"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(2) .row:nth-child(3) input",
      criteria["location"]
    );
  }

  if (criteria["soldRentFrom"] !== undefined) {
    await page.type(
      ".card:nth-child(11) .card-body .row:nth-child(1)  .mt-3.col-sm-6 .row .col-4:nth-child(1) input",
      criteria["soldRentFrom"]
    );
  }

  if (criteria["soldRentTo"] !== undefined) {
    await page.type(
      ".card:nth-child(11) .card-body .row:nth-child(1)  .mt-3.col-sm-6 .row .col-4:nth-child(4) input",
      criteria["soldRentTo"]
    );
  }

  // 成約年月日 - 指定なし
  await page.click(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .custom-radio:nth-child(1) input`
  );


  // 成約登録年月日 - WITHIN A WEEK
  await page.click(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .custom-radio:nth-child(3) input`
  );
  await new Promise(resolve => setTimeout(resolve, 2000));

  // No changed, only show new incremental data
  // await page.click(
  //   ".card:nth-child(13) .card-body .container:nth-child(5) > .row:nth-child(2) .custom-radio:nth-child(7) input"
  // );

  await new Promise(resolve => setTimeout(resolve, 1000));
  // 2.2 Click to go next page
  await page.click(".p-frame-bottom div:nth-child(4) .btn-block.px-0");

  // 3.1 get total pages
  const RESULT_COUNT_SELECTOR = ".text-dark.ml-3";

  try {
    await page.waitForSelector(RESULT_COUNT_SELECTOR);
  } catch {
    // 3.1.1 if does not exist, then return empty
    // Not clicking return, simply enter new URL
    logger.info("🔥 Cannot find results, return and go to next task 🔥 🔥");
    return [];
  }

  const PAGINATION_COUNT = 50;
  const totalResultCount = parseInt(
    await page.evaluate(
      (sel: any) =>
        document
          .querySelector(sel)
          .textContent.trim()
          .split("\n")[1]
          .trim()
          .replace("件", ""),
      RESULT_COUNT_SELECTOR
    ),
    10
  );

  let pageCount = Math.ceil(totalResultCount / PAGINATION_COUNT);

  logger.info(
    `🔥 Total result count is ${totalResultCount}, with ${pageCount} pages`
  );

  let allRecords = [];

  // 3.2 Get data for each page
  for (let pageIndex = 1; pageIndex <= pageCount; pageIndex++) {
    await page.waitForSelector(".p-table-body");
    let BLOCK_SELECTOR = ".p-table-body-row:nth-child(INDEX)";
    const listLength = await page.evaluate(
      (sel: any) => document.querySelectorAll(sel).length,
      BLOCK_SELECTOR.replace(":nth-child(INDEX)", "")
    );

    // Note that first one should not be counted
    logger.info("🔥 listLength for target block is: ", listLength);

    // Skip the first item which is placeholder
    for (let i = 1; i <= listLength; i += 1) {
      let currentRecord = await getSelectorRentDataForOneBlock(
        {
          page,
          selectors,
          index: i,
        }
      );

      // Skip items that does not exist
      if (currentRecord !== null) {
        allRecords.push(currentRecord);
      }
    }

    logger.info(
      `🔥 Complete collecting, total of ${allRecords.length} records`
    );

    if (pageIndex < pageCount) {
      await page.click(".row:nth-child(1) .p-pagination-next-icon");
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return allRecords;
};
