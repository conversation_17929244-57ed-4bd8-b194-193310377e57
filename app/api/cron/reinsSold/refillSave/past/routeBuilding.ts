import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { logger } from "@/lib/logger";
import { TllUserLambdaRecordPriceChangeSourceType } from "@/lib/definitions";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { TllUserLambdaRecordRecordType } from "@prisma/client";
import dayjs from "dayjs";
import fs from 'fs';
import path from 'path';
import { fillNearestStationGroupIdWalkMinute } from "@/actions/geoRailwayStationGroups";
import { fillPrefectureAreaPostalCode } from "@/actions/geoPrefecture";
import { getCompositeTitle } from "@/app/api/cron/reins/utility/getCompositeTitle";


// 读取所有 JSON 文件并合并数据
const dataDirectory = path.join(process.cwd(), 'downloaded/house');
const data = fs.readdirSync(dataDirectory)
  .filter(file => file.endsWith('.json'))
  .flatMap(file => {
    const filePath = path.join(dataDirectory, file);
    const fileData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    return fileData;
  }).slice(31100);


let batchSize = 50;


console.log("🔥 data length is", data.length);

let convertDateToDateProject = (record: any): Date => {
  // if 平成4年5月1日 - convert to 1993/05/01, do the same for 令和
  //  "transactionDate": "平成26年6月23日",
  // e.g. 26 
  const eraMap = {
    '平成': (year: number) => 1988 + year,
    '令和': (year: number) => 2018 + year,
  } as any;

  const regex = /(\D+)(\d+)年(\d+)月(\d+)日/;
  const match = record.transactionDate.match(regex);

  if (match) {
    const era = match[1];
    const year = parseInt(match[2], 10);
    const month = match[3];
    const day = match[4];

    const convertedYear = eraMap[era](year);
    return dayjs(`${convertedYear}-${month}-${day}`).toDate();
  }

  throw new Error("Invalid date format, " + JSON.stringify(record));
}
let case1 = 0;
let case2 = 0;
let case3 = 0;
let index = 0;

let existingCompositeTitle: string[] = [];

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  try {
    const promises = [];

    for (let currentRecord of data) {
      promises.push(refillOne({
        currentRecord,
        data
      }));

      // 每10个记录执行一次
      if (promises.length === batchSize) {
        await Promise.all(promises);
        promises.length = 0; // 清空 promises 数组
        existingCompositeTitle = [];
      }
    }

    // 处理剩余的记录
    if (promises.length > 0) {
      await Promise.all(promises);
    }

    return NextResponse.json({ message: "Batch processing completed!", case1, case2, case3 });
  } catch (err: any) {
    logger.error("🔥 err 🔥");
    logger.error(err, err.stack); // an error occurred
    return NextResponse.json({ message: "Batch processing error!" });
  }
}


const refillOne = async ({
  currentRecord,
  data,
}: {
  currentRecord: any, data: any
}) => {
  let referenceDate = convertDateToDateProject(currentRecord);

  let getDataChangeReord = (matchid: string) => {
    return {
      recordId: matchid,
      price: currentRecord.price,
      recordDate: dayjs(referenceDate).toDate(),
      source: TllUserLambdaRecordPriceChangeSourceType.REINS,
      status: "成約",
      brokerType: currentRecord.brokerType !== undefined ? currentRecord.brokerType : "",
      reinsNumber: currentRecord.reinsNumber !== undefined ? currentRecord.reinsNumber : "",
      createdAt: referenceDate.toISOString(),
      updatedAt: referenceDate.toISOString(),
    }
  }

  const compositeTitle = getCompositeTitle(currentRecord, UserLambdaRecordType.HOUSE, "REINS");
  logger.debug(`🔥 FILL -- ${index++}/${data.length} for  ${compositeTitle}`);

  if (existingCompositeTitle.includes(compositeTitle)) {
    logger.info("🔥 compositeTitle already exists, skipping");
    return;
  }

  existingCompositeTitle.push(compositeTitle);

  let match = await prisma.tllUserLambdaRecord.findUnique({
    where: {
      compositeTitle_recordType: {
        compositeTitle: compositeTitle,
        recordType: UserLambdaRecordType.HOUSE as TllUserLambdaRecordRecordType,
      },
    },
  });

  if (match) {
    let priceChanges = await prisma.tllUserLambdaRecordPriceChange.findFirst({
      where: {
        recordId: match.id,
        price: currentRecord.price,
        recordDate: {
          gte: dayjs(referenceDate).subtract(1, "month").toISOString(),
          lte: dayjs(referenceDate).add(1, "month").toISOString(),
        },
        status: "成約",
      },
    });

    if (priceChanges) {
      logger.info(" [Case 3] Record exist, nothing is done");
      case3++;
    } else {
      // adding price change
      logger.info("[Case 2] Price change does not exist, creating a new record");
      let newChangeRecord = await prisma.tllUserLambdaRecordPriceChange.create({
        data: getDataChangeReord(match.id),
      });

      await prisma.tllUserLambdaRecord.update({
        where: { id: match.id },
        data: {
          updatedAt: new Date(),
        },
      });

      case2++;
      logger.info("Price change created, id is", newChangeRecord.id);
    }
  } else {
    logger.info("[Case 1] record does not exist, creating a new record...");
    case1++;

    const valueToCreate =
      {
        sourceData: "REINS",

        recordType: "HOUSE",
        recordSubType: currentRecord.propertyType || null, // using old name because that is how it was defined in the job

        price: currentRecord.price,
        compositeTitle: compositeTitle,
        address: currentRecord.address,
        landType: currentRecord.landType || null,
        landSize: currentRecord.landSize || null,
        landRight: "所有権",
        roadConnection: currentRecord.roadConnection || null,
        roadConnectionFirstFacing: currentRecord.roadConnectionFirstFacing || null,

        transport: currentRecord.transport || null,
        nearestStation: currentRecord.nearestStation || null,

        recordValues: currentRecord,

        buildingSize: currentRecord.buildingSize || currentRecord.unitArea || null,
        buildingBuiltYear: currentRecord.buildingBuiltYear || null,
        buildingLayout: currentRecord.buildingLayout || null,
        createdAt: referenceDate,
        updatedAt: referenceDate,
      } as any

    let newRecord = await prisma.tllUserLambdaRecord.create({
      data: valueToCreate,
    });

    await fillPrefectureAreaPostalCode(newRecord.id);
    await fillNearestStationGroupIdWalkMinute(newRecord.id);

    logger.info("🔥 creating a new price change record..id is ", newRecord.id);
    let newData = await prisma.tllUserLambdaRecordPriceChange.create({
      data: getDataChangeReord(newRecord.id),
    });

    await prisma.tllUserLambdaRecord.update({
      where: { id: newRecord.id },
      data: {
        updatedAt: new Date(),
      },
    });

    logger.info("🔥 creating a new price change record. record id is ", newData.id);

  }
}