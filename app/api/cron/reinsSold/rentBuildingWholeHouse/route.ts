import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { logger } from "@/lib/logger";
import dayjs from "dayjs";
import fs, { writeFile } from 'fs';
import { getSelectedDataForeinsRentSold } from "../rentMansionBuildingPart/getSelectedDataForeinsRentSold";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { REINS_TASKS } from "../../reins/constants/reinsTasks";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import { getSelectedDataForReinsRent } from "../../reins/rentMansion/getSelectedDataForReinsRent";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  const url = new URL(req.url); // 创建 URL 对象
  const taskIndexFromUrl = url.searchParams.get("taskIndex"); // 获取查询参数
  let browser = null;
  let page = null;

  let rawTasks = [
    {
      title: "東京都, HOUSE,  today",
      url: { type: "02", prefecture: "東京都" },
      type: "HOUSE",
      index: 0
    },
    {
      title: "神奈川県, HOUSE,  today",
      url: { type: "02", prefecture: "神奈川県" },
      type: "HOUSE",
      index: 1
    },
    {
      title: "埼玉県, HOUSE,  today",
      url: { type: "02", prefecture: "埼玉県" },
      type: "HOUSE",
      index: 2
    },
    {
      title: "千葉県, HOUSE,  today",
      url: { type: "02", prefecture: "千葉県" },
      type: "HOUSE",
      index: 3
    },
    {
      title: "東京都, BUILDING,  today",
      url: { type: "04", prefecture: "東京都" },
      type: "BUILDING",
      index: 0
    },
    {
      title: "神奈川県, BUILDING,  today",
      url: { type: "04", prefecture: "神奈川県" },
      type: "BUILDING",
      index: 1
    },
    {
      title: "埼玉県, BUILDING,  today",
      url: { type: "04", prefecture: "埼玉県" },
      type: "BUILDING",
      index: 2
    },
    {
      title: "千葉県, BUILDING,  today",
      url: { type: "04", prefecture: "千葉県" },
      type: "BUILDING",
      index: 3
    },
  ] as {
    title: string;
    url: { type: string; prefecture: string };
    type: "BUILDING" | "HOUSE";
    index: number;
  }[];


  let isGettingYdayKoukaiData = true;
  // taskIndexFromUrl !== null && taskIndexFromUrl !== undefined ? true : dayjs().hour() === 0 || dayjs().hour() === 9 ? true : false;
  let index = taskIndexFromUrl !== null && taskIndexFromUrl !== undefined ? parseInt(taskIndexFromUrl || "0") : Math.floor(dayjs().minute() / 15);
  console.log("🔥 isGettingYdayKoukaiData is: ", dayjs().hour(), isGettingYdayKoukaiData);

  if (!browser) {
    browser = await getChromeBrowser();
  }

  let tasks = isGettingYdayKoukaiData ? rawTasks.filter(task => task.index === index) : index === 0 ? rawTasks : []; // if morning, then fill all past tasks, if at night then fill all future tasks


  let taskIndex = 0;

  try {
    for (let task of tasks) {
      // FIX 20250303: mvoe it in, because if not in then retry always fail
      page = await getPageWithRandomUserAgent(browser);

      let dataCralwed = isGettingYdayKoukaiData ? await getSelectedDataForReinsRent({
        page,
        task,
        selectors: task.type === "HOUSE" ? REINS_TASKS.HOUSE_RENT.selectors : REINS_TASKS.BUILDING_RENT.selectors,
        yday: true // only get yday data
      }) as ProBuildingHouseRentProps[] : await getSelectedDataForeinsRentSold({
        page,
        task,
        selectors: task.type === "HOUSE" ? REINS_TASKS.HOUSE_RENT.selectorsSold : REINS_TASKS.BUILDING_RENT.selectorsSold
      }) as ProBuildingHouseRentProps[];


      console.log("🔥 dataCralwed lenght is: ", dataCralwed.length);
      await writeFile(`downloaded/${dayjs().format("YYYY-MM-DD HH:mm:ss")}-SUUMO-${dataCralwed.length}.json`, JSON.stringify(dataCralwed, null, 2), (err) => {
        if (err) {
          console.error("🔥 Error writing file 🔥", err);
        }
      });
      console.log("🔥 Results saved to output.json, total: ", dataCralwed.length, "🔥");

      index = 0;
      for (const recCrawled of dataCralwed) { // 
        // Fixme: price might drop wth same reins number
        let matchedRecord = await prisma.proBuildingHouseRent.findFirst({
          where: {
            brokerReinsNumber: recCrawled.brokerReinsNumber,
            brokerStatus: isGettingYdayKoukaiData ? "公開中" : "成約"
          },
        });

        logger.info(`🔥 saving ${++index} of ${dataCralwed.length}, ${matchedRecord?.id ? "has match so skip" : "no match so create...."} 🔥`);

        if (matchedRecord === null) {
          let nearestStationGroupId;
          if (recCrawled.nearestStation) {
            const match = await prisma.geoRailwayStation.findMany({
              where: {
                name: recCrawled.nearestStation,
                prefectureCode: { in: [11, 12, 13, 14] }, // sometimes property in 埼玉 but nearest station is in 東京
              },
            });

            if (match.length > 0) {
              nearestStationGroupId = match[0].stationGroupId;
            }
          }

          let addressMatch = null;
          if (recCrawled.address) {
            addressMatch = await prisma.geoAddress.findFirst({
              where: {
                address: recCrawled.address.split("丁目")[0] + "丁目",
              },
            });
          }

          matchedRecord = await prisma.proBuildingHouseRent.create({
            data: {
              recordType: task.type,
              propertyType: recCrawled.propertyType,

              brokerReinsNumber: recCrawled.brokerReinsNumber, // do not save the same number 
              brokerType: recCrawled.brokerType,
              brokerStatus: isGettingYdayKoukaiData ? "公開中" : "成約",
              brokerListingCompany: recCrawled.brokerListingCompany,
              brokerListingCompanyNumber: recCrawled.brokerListingCompanyNumber,

              feeRent: recCrawled.feeRent,
              feeManagement: recCrawled.feeManagement,
              feeUtility: recCrawled.feeUtility,
              feeGiftMoney: recCrawled.feeGiftMoney,
              feeDepositMoney: recCrawled.feeDepositMoney,

              landType: recCrawled.landType,
              landSize: recCrawled.landSize,
              buildingBuiltYear: recCrawled.buildingBuiltYear || null,
              buildingSize: recCrawled.buildingSize,
              buildingName: recCrawled.buildingName,
              buildingLayout: recCrawled.buildingLayout,

              address: recCrawled.address || "",
              transport: recCrawled.transport,
              nearestStation: recCrawled.nearestStation,
              ...(nearestStationGroupId && nearestStationGroupId !== "" ? { nearestStationGroupId: nearestStationGroupId } : {}),
              nearestStationWalkMinute: recCrawled.nearestStationWalkMinute,

              roadConnection: recCrawled.roadConnection,
              roadConnectionFirstFacing: recCrawled.roadConnectionFirstFacing,

              // locationLongitude: recCrawled.locationLongitude,
              // locationLatitude: recCrawled.locationLatitude,
              // locationPrefectureCode: recCrawled.locationPrefectureCode,

              ...(addressMatch && { locationPostalCode: addressMatch.pcode?.toString() }),
            },
          });

          // FIXME: also update the postal code and long and lat,, without using google

          logger.info(`🔥 [Case 1] new record ... creating, id is ${matchedRecord.id}`);
        } else {
          logger.info(`🔥 [Case 2] matched record ... skip, id is ${matchedRecord.id}`);
        }
      }

      await sendLark({
        message: `[⚙️][Building Whole House Rent ${isGettingYdayKoukaiData ? "ydayKoukai" : "ydaySeiyaku"}][${task.title}][${++taskIndex} / ${tasks.length}], total record count is ${dataCralwed.length}`,
        url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
      });
    }

    return NextResponse.json({ message: "Batch processing completed!" });
  } catch (err: any) {
    logger.error("🔥 err 🔥");
    logger.error(err, err.stack); // an error occurred
    return NextResponse.json({ message: "Batch processing error!" });
  } finally {
    console.log("🔥 finally 🔥");

    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }
  }
}

