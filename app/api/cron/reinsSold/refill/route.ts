import { getSelectedDataForReinsSoldForRefill } from "./getSelectedDataForReinsSoldForRefill";
import { writeFile } from 'fs/promises'; // 引入文件写入模块

import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { NextResponse } from "next/server";
import duration from "dayjs/plugin/duration";
import { logger } from "@/lib/logger";
import { REINS_TASKS } from "../../reins/constants/reinsTasks";
import { login } from "../../reins/utility/reinsLogin";
import { getSelectedDataForReinsRentSoldForRefill } from "./getSelectedDataForReinsRentSoldForRefill";
import { prisma } from "@/lib/prisma";
export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  let browser = null;
  let page = null;
  let currentHour = dayjsWithTz().hour();
  const url = new URL(req.url); // 创建 URL 对象
  const pref = url.searchParams.get("prefecture"); // 获取查询参数

  try {
    browser = await getChromeBrowser();
    let reinsTasks: any[] = [];
    // "埼玉県", "  " // 神奈川県
    [pref].forEach((prefecture) => {
      // reinsTasks.push({
      //   title: prefecture + "HOUSE",
      //   selector: REINS_TASKS["HOUSE"].selectors,
      //   blockSelector: ".p-table-body-row:nth-child(INDEX)",
      //   recordType: "HOUSE",
      //   reinsSelector: "02",
      //   saleRecord: true, // override status to 成約
      //   input: prefecture,
      //   id: "90", // user lambda id, just a mock number
      //   getChirashi: true,
      //   start1: "H",
      //   start2: "2",
      //   start3: "1",
      //   start4: "1",
      //   end1: "H",
      //   end2: "5",
      //   end3: "12",
      //   end4: "31",
      // });

      // for (let i = 6; i <= 30; i += 2) {
      //   reinsTasks.push({
      //     title: prefecture + "HOUSE",
      //     selector: REINS_TASKS["HOUSE"].selectors,
      //     blockSelector: ".p-table-body-row:nth-child(INDEX)",
      //     recordType: "HOUSE",
      //     reinsSelector: "02",
      //     saleRecord: true, // override status to 成約
      //     input: prefecture,
      //     id: "90", // user lambda id, just a mock number
      //     getChirashi: true,
      //     start1: "H",
      //     start2: i.toString(),
      //     start3: "1",
      //     start4: "1",
      //     end1: "H",
      //     end2: i.toString(),
      //     end3: "12",
      //     end4: "31",
      //   });
      // }


      // reinsTasks.push({
      //   title: prefecture + "HOUSE",
      //   selector: REINS_TASKS["HOUSE"].selectors,
      //   blockSelector: ".p-table-body-row:nth-child(INDEX)",
      //   recordType: "HOUSE",
      //   reinsSelector: "02",
      //   saleRecord: true, // override status to 成約
      //   input: prefecture,
      //   // input2: "埼玉県",
      //   // input3: "千葉県",
      //   id: "90", // user lambda id, just a mock number
      //   getChirashi: true,
      //   start1: "H",
      //   start2: "31",
      //   start3: "1",
      //   start4: "1",
      //   end1: "R",
      //   end2: "1",
      //   end3: "12",
      //   end4: "31",
      // });

      let t = REINS_TASKS["HOUSE_RENT"].selectorsSold;
      console.log(t);

      for (let i = 2; i <= 7; i += 1) {
        for (let j = 1; j <= 12; j += 1) {
          const daysInMonth = new Date(2018 + i, j, 0).getDate(); // 获取当前月份的天数
          // reinsTasks.push({
          //   title: prefecture + "HOUSE_RENT",
          //   selector: REINS_TASKS["HOUSE_RENT"].selectorsSold,
          //   blockSelector: ".p-table-body-row:nth-child(INDEX)",
          //   recordType: "HOUSE_RENT",
          //   reinsSelector: "04",
          //   saleRecord: true, // override status to 成約
          //   input: prefecture,
          //   id: "90", // user lambda id, just a mock number
          //   start1: "R",
          //   start2: i.toString(),
          //   start3: j.toString(),
          //   start4: "1",
          //   end1: "R",
          //   end2: i.toString(),
          //   end3: j.toString(),
          //   end4: "15",
          // });

          reinsTasks.push({
            title: prefecture + "HOUSE_RENT",
            selector: REINS_TASKS["HOUSE_RENT"].selectorsSold,
            blockSelector: ".p-table-body-row:nth-child(INDEX)",
            recordType: "HOUSE_RENT",
            reinsSelector: "02",
            saleRecord: true, // override status to 成約
            input: prefecture,
            id: "90", // user lambda id, just a mock number
            start1: "R",
            start2: i.toString(),
            start3: j.toString(),
            start4: "1",
            end1: "R",
            end2: i.toString(),
            end3: (j).toString(),
            end4: daysInMonth.toString(),
          });

          // reinsTasks.push({
          //   title: prefecture + "LAND",
          //   selector: REINS_TASKS["LAND"].selectorsSold,
          //   blockSelector: ".p-table-body-row:nth-child(INDEX)",
          //   recordType: "LAND",
          //   reinsSelector: "01",
          //   saleRecord: true, // override status to 成約
          //   input: prefecture,
          //   id: "90", // user lambda id, just a mock number
          //   start1: "R",
          //   start2: i.toString(),
          //   start3: j.toString(),
          //   start4: "16", // 下半期
          //   end1: "R",
          //   end2: i.toString(),
          //   end3: j.toString(),
          //   end4: daysInMonth.toString(), // 根据月份的天数设置结束日期
          // });
        }
      }
    });

    page = await getPageWithRandomUserAgent(browser);

    await login({
      page,
      account: null
    });

    let taskIndex = 0;

    for (let reinsTask of reinsTasks) {
      taskIndex += 1;
      try {
        let results = await getSelectedDataForReinsRentSoldForRefill(
          page,
          reinsTask,
        );

        // 保存结果到 output.json 文件
        // await writeFile(`downloaded/${reinsTask.title}---${reinsTask.recordType}-${reinsTask.start1}${reinsTask.start2}-${reinsTask.start3}-${reinsTask.start4}----${reinsTask.end1}${reinsTask.end2}-${reinsTask.end3}-${reinsTask.end4}-total[${results.length}].json`, JSON.stringify(results, null, 2));
        // console.log("🔥 Results saved to output.json, total: ", results.length, "🔥");
      } catch (err: any) {
        logger.error("Error in cron job", err);
        await new Promise(resolve => setTimeout(resolve, 4000));
        await sendLark({
          message: `❌[Reins Sold][Error getting selector data]` + err,
          url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
        });


      }
    }

    return NextResponse.json({
      status: "success"
    });
  } catch (err: any) {
    console.error("🔥 Error in cron job getting reins sold data 🔥", err);
    await sendLark({
      message: `[Error getting reins sold data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return NextResponse.json({ message: "error", error: err });
  } finally {
    console.log("🔄 Closing page and browser!");

    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
