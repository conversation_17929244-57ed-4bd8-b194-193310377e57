import { REINS_TASKS } from "../../reins/constants/reinsTasks";
import { getDataForOneBlock } from "../../reins/getDataForOneBlock";
import { getSelectorRentDataForOneBlock } from "../../reins/rentMansion/getSelectorRentDataForOneBlock";
import { saveUserLambdaResult } from "../../reins/saveUserLambdaResult";

export async function getSelectedDataForReinsRentSoldForRefill(page: any, reinsTask: any) {
  await page.goto("https://system.reins.jp/main/KG/GKG003100", {
    waitUntil: "networkidle2",
  });

  console.log("🔥 Go to search page with task ..", JSON.stringify(reinsTask, null, 2));

  // Go to search 賃貸物件
  // Go to search 賃貸物件
  let searchButtonSelector =
    ".card:nth-child(2) > div > div > div:nth-child(2) button";
  await page.waitForSelector(searchButtonSelector);
  await page.click(searchButtonSelector);

  console.log("🔥 Go to search page ..");
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Step 2 Fill in critieral
  let typeSelector =
    ".card:nth-child(7) .card-body .row:nth-child(2) .col-sm-4:nth-child(1) select";

  // Pick a type, and be filling in other info
  await page.waitForSelector(typeSelector);
  await page.select(typeSelector, reinsTask["reinsSelector"]); // 03 - マンション 04 - 売外全(住宅以外建物全部), 02 一戸建て, 01 土地

  // Select 成約
  await page.click(
    ".card:nth-child(7) .card-body .row:nth-child(1) .col-sm-4:nth-child(1) .custom-radio:nth-child(2) input"
  );

  // Location
  console.log("🔥 Fill in location ..", reinsTask["input"]);
  await page.type(
    ".card:nth-child(9) .card-body .container:nth-child(2) .row:nth-child(2) input",
    reinsTask["input"]
  );


  // 成約年月日 - 指定なし
  await page.click(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .custom-radio:nth-child(1) input`
  );

  // await page.select(".card:nth-child(13) .card-body .container:nth-child(8)  > .row:nth-child(1) .col-sm-auto:nth-child(1) select", reinsTask["start1"]); // 03 - マン


  // await page.select(".card:nth-child(13) .card-body .container:nth-child(8)  > .row:nth-child(1) .col-sm-auto:nth-child(3) select", reinsTask["end1"]); // 03 - マン
  // await new Promise(resolve => setTimeout(resolve, 2000));

  // await page.type(
  //   `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .col-sm-auto:nth-child(1) .p-gdatebox-input input:nth-child(2)`,
  //   reinsTask["start2"]
  // );
  // await page.type(
  //   `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .col-sm-auto:nth-child(1) .p-gdatebox-input input:nth-child(3)`,
  //   reinsTask["start3"]
  // );
  // await page.type(
  //   `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .col-sm-auto:nth-child(1) .p-gdatebox-input input:nth-child(4)`,
  //   reinsTask["start4"]
  // );
  // await page.type(
  //   `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .col-sm-auto:nth-child(3) .p-gdatebox-input input:nth-child(2)`,
  //   reinsTask["end2"]
  // );
  // await page.type(
  //   `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .col-sm-auto:nth-child(3) .p-gdatebox-input input:nth-child(3)`,
  //   reinsTask["end3"]
  // );
  // await page.type(
  //   `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .col-sm-auto:nth-child(3) .p-gdatebox-input input:nth-child(4)`,
  //   reinsTask["end4"]
  // );


  // 成約登録年月日 - 三日以内
  await page.click(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .custom-radio:nth-child(6) input`
  );
  await new Promise(resolve => setTimeout(resolve, 2000));

  await page.select(".card:nth-child(13) .card-body .container:nth-child(8)  > .row:nth-child(2) .col-sm-auto:nth-child(1) select", reinsTask["start1"]); // 03 - マン
  await page.select(".card:nth-child(13) .card-body .container:nth-child(8)  > .row:nth-child(2) .col-sm-auto:nth-child(3) select", reinsTask["end1"]); // 03 - マン
  await page.type(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .col-sm-auto:nth-child(1) .p-gdatebox-input input:nth-child(2)`,
    reinsTask["start2"]
  );
  await page.type(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .col-sm-auto:nth-child(1) .p-gdatebox-input input:nth-child(3)`,
    reinsTask["start3"]
  );
  await page.type(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .col-sm-auto:nth-child(1) .p-gdatebox-input input:nth-child(4)`,
    reinsTask["start4"]
  );
  await page.type(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .col-sm-auto:nth-child(3) .p-gdatebox-input input:nth-child(2)`,
    reinsTask["end2"]
  );
  await page.type(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .col-sm-auto:nth-child(3) .p-gdatebox-input input:nth-child(3)`,
    reinsTask["end3"]
  );
  await page.type(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .col-sm-auto:nth-child(3) .p-gdatebox-input input:nth-child(4)`,
    reinsTask["end4"]
  );


  await new Promise(resolve => setTimeout(resolve, 1000));

  // 2.2 Click to go next page
  await page.click(".p-frame-bottom div:nth-child(4) .btn-block.px-0");

  // 3.1 get total pages
  const RESULT_COUNT_SELECTOR = ".text-dark.ml-3";


  try {
    await page.waitForSelector(RESULT_COUNT_SELECTOR);
  } catch {
    // 3.1.1 if does not exist, then return empty
    // Not clicking return, simply enter new URL
    console.log("🔥 Cannot find results, return and go to next task 🔥 🔥");
    return [];
  }

  let totalRecordsCount = 0;

  const PAGINATION_COUNT = 50;
  totalRecordsCount = parseInt(
    await page.evaluate(
      (sel: string) =>
        document
          .querySelector(sel)
          ?.textContent?.trim()
          ?.split("\n")[1]
          ?.trim()
          ?.replace("件", ""),
      RESULT_COUNT_SELECTOR
    ),
    10
  );
  let pageCount = Math.ceil(totalRecordsCount / PAGINATION_COUNT);

  console.log(
    `🔥 Total result count is ${totalRecordsCount}, with ${pageCount} pages`
  );

  let allRecords = [] as any[];

  // 3.2 Get data for each page
  for (let pageIndex = 1; pageIndex <= pageCount; pageIndex++) {
    await page.waitForSelector(".p-table-body");
    let BLOCK_SELECTOR = ".p-table-body-row:nth-child(INDEX)";

    const listLength = await page.evaluate(
      (sel: string) => document.querySelectorAll(sel).length,
      BLOCK_SELECTOR.replace(":nth-child(INDEX)", "")
    );

    for (let i = 1; i <= listLength; i += 1) {
      console.log(`---🔥 Process ${i} / ${listLength} sold 🔥------`);
      let currentRecord = await getSelectorRentDataForOneBlock({
        // recordType: reinsTask["recordType"],
        selectors: REINS_TASKS["BUILDING_RENT"].selectorsSold,
        page,
        index: i,
      });

      if (currentRecord !== null) {
        allRecords.push(currentRecord);
      }
    }


    if (pageIndex < pageCount) {
      console.log("🔥 Go to next page ..");
      await page.click(".row:nth-child(1) .p-pagination-next-icon");
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return allRecords;
};
