import { getSelectedDataForReinsSold } from "./getSelectedDataForReinsSold";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { NextResponse } from "next/server";
import duration from "dayjs/plugin/duration";
import { logger } from "@/lib/logger";
import { REINS_TASKS } from "../reins/constants/reinsTasks";
dayjsWithTz.extend(duration);
import { prisma } from "@/lib/prisma";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  let browser = null;
  let page = null;
  let currentMinute = dayjsWithTz().minute();

  try {
    browser = await getChromeBrowser();
    let reinsTasks: any[] = [];

    ["東京都", "神奈川県", "千葉県", "埼玉県"].forEach((prefecture) => {
      if (Math.floor(currentMinute / 30) === 0) {
        reinsTasks.push({
          title: prefecture + "一棟収益",
          selector: REINS_TASKS["BUILDING"].selectors,
          blockSelector: ".p-table-body-row:nth-child(INDEX)",
          recordType: "BUILDING",
          reinsSelector: "04",
          saleRecord: true, // override status to 成約
          input: prefecture,
          id: "90", // user lambda id, just a mock number
          getChirashi: true,
        });

        reinsTasks = reinsTasks.concat([
          {
            title: prefecture + "一戸建て",
            selector: REINS_TASKS["HOUSE"].selectors,
            blockSelector: ".p-table-body-row:nth-child(INDEX)",
            recordType: "HOUSE",
            reinsSelector: "02",
            saleRecord: true, // override status to 成約
            input: prefecture,
            id: "90", // user lambda id, just a mock number
            getChirashi: false,
          },
        ]);

        reinsTasks = reinsTasks.concat([
          {
            title: prefecture + "土地",
            selector: REINS_TASKS["LAND"].selectorsSold, // Use a different selector
            blockSelector: ".p-table-body-row:nth-child(INDEX)",
            recordType: "LAND",
            reinsSelector: "01",
            saleRecord: true, // override status to 成約
            input: prefecture,
            id: "90", // user lambda id, just a mock number
            getChirashi: false,
          },
        ]);
      }

      if (Math.floor(currentMinute / 30) === 1) {
        reinsTasks = reinsTasks.concat([
          {
            title: prefecture + "マンション",
            selector: REINS_TASKS["MANSION"].selectors,
            blockSelector: ".p-table-body-row:nth-child(INDEX)",
            recordType: "MANSION",
            reinsSelector: "03",
            saleRecord: true, // override status to 成約
            input: prefecture,
            id: "90", // user lambda id, just a mock number
            getChirashi: false,
          },
        ]);
      }
    });

    let taskIndex = 0;
    let retries = 3;
    let startTime, endTime;
    for (let reinsTask of reinsTasks) {
      startTime = dayjsWithTz();
      taskIndex += 1;

      let createdRecordsCount = 0;
      let createdRecordsS3SavedCount = 0;
      let updatedRecordsCount = 0;
      let totalRecordsCount = 0;

      for (let i = 0; i < retries; i++) {
        page = await getPageWithRandomUserAgent(browser);

        logger.info(`🔥[${i < retries - 1 ? "First" : "Retry"}] Starting task... ${taskIndex}/${reinsTasks.length}... task id is`, reinsTask.id);

        try {
          [
            createdRecordsCount,
            createdRecordsS3SavedCount,
            updatedRecordsCount,
            totalRecordsCount,
          ] = await getSelectedDataForReinsSold(
            page,
            reinsTask,
          );

          break;
        } catch (err: any) {
          logger.error("Error in cron job", err);
          await new Promise(resolve => setTimeout(resolve, 4000));
          await sendLark({
            message: `❌${i >= 1 ? "❌" : ""}${i >= 2 ? "❌" : ""}[Reins Sold][Error getting selector data]` + err,
            url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
          });

          if (page) {
            await page.close();
          }
          logger.info("🔥 Creating a new page for new attempt ..");
          page = await getPageWithRandomUserAgent(browser);
        } finally {
          endTime = dayjsWithTz();
          const duration = dayjsWithTz.duration(endTime.diff(startTime));

          let message = `✅[Reins Sold][${duration.minutes()}:${duration.seconds()}][${reinsTask.recordType}][${taskIndex}/${reinsTasks.length}][${reinsTask.id}][${reinsTask.title}][${dayjsWithTz()
            .utc()
            .format(
              "YYYY-MM-DD HH:mm:ss"
            )}] ${totalRecordsCount} total | ${updatedRecordsCount} updated | ${createdRecordsCount} created | ${createdRecordsS3SavedCount} s3 saved`;
          logger.info(message);
          await sendLark({
            message,
            url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
          });
        }
      }
    }

    return NextResponse.json({
      status: "success"
    });
  } catch (err: any) {
    console.error("🔥 Error in cron job getting reins sold data 🔥", err);
    await sendLark({
      message: `[Error getting reins sold data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return NextResponse.json({ message: "error", error: err });
  } finally {
    console.log("🔄 Closing page and browser!");

    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
