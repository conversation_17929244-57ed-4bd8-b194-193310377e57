import { getDataForOneBlock } from "../reins/getDataForOneBlock";
import { saveUserLambdaResult } from "../reins/saveUserLambdaResult";
import { login } from "../reins/utility/reinsLogin";

export async function getSelectedDataForReinsSold(page: any, reinsTask: any) {
  await page.goto("https://system.reins.jp/login/main/KG/GKG001200", {
    waitUntil: "networkidle2",
  });

  await login({
    page,
    account: reinsTask["account"]
  });

  // Step 1.1 Go to search page
  let searchButtonSelector =
    ".card:nth-child(2) > div > div > div:nth-child(1) button";
  await page.waitForSelector(searchButtonSelector);
  await page.click(searchButtonSelector);

  console.log("🔥 Go to search page ..");
  await new Promise(resolve => setTimeout(resolve, 4000));

  // Step 2 Fill in critieral
  let typeSelector =
    ".card:nth-child(7) .card-body .row:nth-child(2) .col-sm-4:nth-child(1) select";
  // Pick a type, and be filling in other info
  await page.waitForSelector(typeSelector);
  await page.select(typeSelector, reinsTask["reinsSelector"]); // 03 - マンション 04 - 売外全(住宅以外建物全部), 02 一戸建て, 01 土地

  // Select 成約
  await page.click(
    ".card:nth-child(7) .card-body .row:nth-child(1) .col-sm-4:nth-child(1) .custom-radio:nth-child(2) input"
  );

  // Location
  await page.type(
    ".card:nth-child(9) .card-body .container:nth-child(4) .row:nth-child(2) input",
    reinsTask["input"]
  );

  if (reinsTask["input2"]) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(4) .row:nth-child(3) input",
      reinsTask["input2"]
    );
  }

  // Fixme: price from will be a different selector (成約価格※成約のみ有効)
  // if (reinsTask["priceFrom"]) {
  //   await page.type(
  //     ".card:nth-child(11) .card-body .row:nth-child(1) .col-sm-6 .row .col-4:nth-child(1) input",
  //     reinsTask["priceFrom"]
  //   );
  // }

  // 成約年月日 - 指定なし
  await page.click(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(1)  .custom-radio:nth-child(1) input`
  );

  // 成約登録年月日 - 三日以内
  await page.click(
    `.card:nth-child(13) .card-body .container:nth-child(8) > .row:nth-child(2)  .custom-radio:nth-child(2) input`
  );

  // 2.2 Click to go next page
  await page.click(".p-frame-bottom div:nth-child(4) .btn-block.px-0");

  // 3.1 get total pages
  const RESULT_COUNT_SELECTOR = ".text-dark.ml-3";

  try {
    await page.waitForSelector(RESULT_COUNT_SELECTOR);
  } catch {
    // 3.1.1 if does not exist, then return empty
    // Not clicking return, simply enter new URL
    console.log("🔥 Cannot find results, return and go to next task 🔥 🔥");
    return [];
  }

  let createdRecordsCount = 0;
  let createdRecordsS3SavedCount = 0;
  let updatedRecordsCount = 0;
  let totalRecordsCount = 0;

  const PAGINATION_COUNT = 50;
  totalRecordsCount = parseInt(
    await page.evaluate(
      (sel: string) =>
        document
          .querySelector(sel)
          ?.textContent?.trim()
          ?.split("\n")[1]
          ?.trim()
          ?.replace("件", ""),
      RESULT_COUNT_SELECTOR
    ),
    10
  );
  let pageCount = Math.ceil(totalRecordsCount / PAGINATION_COUNT);

  console.log(
    `🔥 Total result count is ${totalRecordsCount}, with ${pageCount} pages`
  );

  // 3.2 Get data for each page
  for (let pageIndex = 1; pageIndex <= pageCount; pageIndex++) {
    await page.waitForSelector(".p-table-body");
    let BLOCK_SELECTOR = ".p-table-body-row:nth-child(INDEX)";

    const listLength = await page.evaluate(
      (sel: string) => document.querySelectorAll(sel).length,
      BLOCK_SELECTOR.replace(":nth-child(INDEX)", "")
    );

    for (let i = 1; i <= listLength; i += 1) {
      console.log(`---🔥 Process ${i} / ${listLength} sold 🔥------`);
      let currentRecord = await getDataForOneBlock({
        recordType: reinsTask["recordType"],
        page,
        index: i,
        getChirashi: reinsTask["getChirashi"] ?? false,
        getDetailsForNewRecord: false,
        isSoldOnly: true
      });

      // Skip items that does not exist
      if (currentRecord !== null) {
        let [a, b, c] = await saveUserLambdaResult({
          recordType: reinsTask["recordType"],
          currentRecord,
          soldOnly: true
        });
        createdRecordsCount += a;
        createdRecordsS3SavedCount += b;
        updatedRecordsCount += c;
      }
    }

    if (pageIndex < pageCount) {
      console.log("🔥 Go to next page ..");
      await page.click(".row:nth-child(1) .p-pagination-next-icon");
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return [
    createdRecordsCount,
    createdRecordsS3SavedCount,
    updatedRecordsCount,
    totalRecordsCount,
  ];
};
