DEPLOYMENT TIME 
10:00 ~ 12:00PM (1-3)
14:00 ~ 18:00PM (5-9)
23PM~ (14)


## REINS
✅ getPriceReinsBuilding	是	REINS	物件取得		cron(0 3,12 * * ? *)
✅ getPriceReinsHouse	是	REINS	物件取得		cron(5 3,12 * * ? *)
✅ getPriceReinsMansion	是	REINS	物件取得		cron(5 3,12 * * ? *)
✅ getPriceReinsLand	是	REINS	物件取得		cron(5 3,12 * * ? *)

✅ getPriceReinsBuildingYday	是	REINS	物件取得		cron(0 2 * * ? *)
✅ getPriceReinsHouseYday	是	REINS	物件取得		cron(0 2 * * ? *)
✅ getPriceReinsMansionYday	是	REINS	物件取得		cron(0 2 * * ? *)
✅ getPriceReinsLandYday	是	REINS	物件取得		cron(0 2 * * ? *)
✅  getPriceReinsSold	是	REINS	データ		cron(5 12 * * ? *)	

### POST RENS PROCESSING
✅ fillBuilding	是	REINS	集計		cron(30 3,12 * * ? *)	get mansion must end first before this
✅ fillRecord	是	REINS	集計		 cron(35 3,12 * * ? *)	will call fillRecordSendLark at end // 
✅ fillCompanyDetails	是	REINS	物件取得		cron(35 12 * * ? *)	
<!-- ❌ fillAggregateRecentUpdatedBuildings	是	REINS	集計		cron(55 3,12 * * ? *)	 -->

### OTHERS
❌ getMansionRent	是	REINS	データ		cron(20 3,12 * * ? *)	Getting data used for rent calculation


## 管理
❌ showPhoneCalls2 (on demand) 
❌ refreshReins　cron(0 0 * * ? *)　

## 集計
❌ siteMetricsReins	是	サイト	集計	Y	cron(0 0 * * ? *)	Will get both 賃貸 and 売買 data
🛑 siteMetricsRakumachi	是	REINS	集計	Y	cron(0 0 * * ? *)	Will get Rakumachi 売買 data 
🛑 siteMetricsKenbiya	是	サイト	集計	Y	cron(0 0 * * ? *)	Will get Kenbiya 売買 dat


## 一般競売
❌ getAuction cron(45 12 * * ? *)
❌ getAuctionResults cron(45 12 * * ? *)	一般競売 結果　集計
(NOT DEPLOYED) getAuctionSanten cron(0 13 * * ? *)	Get 3点セット
❌ createAuctionChangeRecords	是	一般競売	集計		cron(55 12 * * ? *)	!!! SHould be removed same as above - this is when auction multiple times 

## SUMIFU競売
✅ getSumitomoAuctions - cron(45 3,12 * * ? *)

## 物件取得 (5 sites) 
### will call pro/company/sites/jobSaveAll ❌

✅ getMistui - cron(45 12 * * ? *) Tokyo Only, pass othe areas because very little changes 
https://www.rehouse.co.jp/buy/tohshi/prefecture/13/city/13101/?cityCode=13102,13103,13104,13105,13106,13107,13108,13109,13110,13111,13112,13113,13114,13115,13116,13117,13118,13119,13120,13121,13122,13123,13201,13202,13203,13204,13205,13206,13207,13208,13209,13210,13211,13212,13213,13214,13215,13218,13219,13220,13221,13222,13223,13224,13225,13227,13228,13229,13303&otherRequirements=1&buildingTypes=4,3,2,1,6
✅ getSumitomo - cron(45 12 * * ? *) All areas
https://www.stepon.co.jp/pro/ca_1_001/100_1/?isShinchaku=on&smk=111001111011
✅ getLivable - cron(45 12 * * ? *)  
東京都 - https://www.livable.co.jp/fudosan-toushi/tatemono-tokyo-select-area/a13000/conditions-use=mansion-itto,apart,building-itto,store-office,factory-warehouse,company-housing-dormitory,other&price-from=3000&new&price-down/
神奈川 - https://www.livable.co.jp/fudosan-toushi/tatemono-kanagawa-select-area/a14108,a14111,a14109,a14101,a14110,a14104,a14103,a14105,a14137,a14131,a14132,a14134,a14135,a14136,a14153/conditions-use=mansion-itto,apart,building-itto,store-office,factory-warehouse,company-housing-dormitory,other&price-from=3000&new&price-down/ 
✅ getMitsubishi - cron(45 12 * * ? *)  All areas 東京23区（4） 東京都市部（1） 神奈川県（5） 千葉県（3） 埼玉県（1） 
https://www.sumai1.com/buyers/investor/tod_131/?inv_bukken_shumoku%5B%5D=2&inv_bukken_shumoku%5B%5D=3&inv_bukken_shumoku%5B%5D=4&inv_bukken_shumoku%5B%5D=5&inv_bukken_shumoku%5B%5D=8&inv_bukken_shumoku%5B%5D=99&todofuken_cd%5B%5D=131&todofuken_cd%5B%5D=132&todofuken_cd%5B%5D=14&todofuken_cd%5B%5D=12&todofuken_cd%5B%5D=11&new=1
✅ getNomu - cron(45 12 * * ? *)  All areas
https://www.nomu.com/pro/search/?area_ids[]=1311&area_ids[]=1312&area_ids[]=1313&area_ids[]=1314&area_ids[]=1315&area_ids[]=1316&area_ids[]=1411&area_ids[]=1412&area_ids[]=1414&area_ids[]=1413&area_ids[]=1111&area_ids[]=1112&area_ids[]=1211&area_ids[]=1212&type_ids[]=2&type_ids[]=4&type_ids[]=3&type_ids[]=7&type_ids[]=5&type_ids[]=8&type_ids[]=9&type_ids[]=10&type_ids[]=11&price_down=3000&new_period=1&sp=new_1day_all&order=0&rosen_tab_id=-1&bukken_class=build
