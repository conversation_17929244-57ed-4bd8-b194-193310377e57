import { TllUserSubscriptionStatus } from "@/lib/definitions/tllUser";
import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";

export async function expireUserStatus() {
  const users = await prisma.tllUser.findMany({
    where: {
      subscriptionStatus: TllUserSubscriptionStatus.ACTIVE,
    },
  });

  let updatedUser = [];

  for (const user of users) {
    const subscription = await prisma.tllUserSubscription.findFirst({
      where: {
        userId: user.id,
        subscriptionStatus: TllUserSubscriptionStatus.ACTIVE,
      },
    });

    if (subscription && subscription.subscriptionEndAt) {
      const subscriptionEndDate = dayjsWithTz(subscription.subscriptionEndAt);
      // Give a day buffer for timezone thingy
      if (subscriptionEndDate.isBefore(dayjsWithTz().subtract(1, 'day').toISOString())) {
        await prisma.tllUserSubscription.update({
          where: { id: subscription.id },
          data: {
            subscriptionStatus: TllUserSubscriptionStatus.CANCELED,
          },
        });

        await prisma.tllUser.update({
          where: { id: user.id },
          data: {
            subscriptionStatus: TllUserSubscriptionStatus.FREE, // FIXME: should this be cancelled or free?
            subscriptionPlan: null,
            accessLevel: 1,
          },
        });

        updatedUser.push(user.id);
      }
    }
  }

  sendLark({
    message: `[🛠️][Expire User Status] ${updatedUser.length} users updated, ids are ${updatedUser.join(', ')} `,
    url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
  });

  return NextResponse.json({ success: true, message: "Success" });
}