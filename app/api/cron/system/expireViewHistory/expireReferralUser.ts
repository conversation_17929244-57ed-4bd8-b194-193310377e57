import { TllUserSubscriptionStatus } from "@/lib/definitions/tllUser";
import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";

export async function expireReferralUser() {
  // do not expire if there is a status 
  const users = await prisma.tllUser.findMany({
    where: {
      referralCodeExpiresAt: {
        lt: dayjsWithTz().toISOString(),
      },
      accessLevel: 10,
      subscriptionStatus: TllUserSubscriptionStatus.FREE,
    },
  });

  for (const user of users) {
    await prisma.tllUser.update({
      where: { id: user.id },
      data: {
        accessLevel: 1,
      },
    });
  }

  sendLark({
    message: `[🛠️][Expire Referral User] ${users.length} users updated`,
    url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
  });

  return NextResponse.json({ success: true, message: "Success" });
}