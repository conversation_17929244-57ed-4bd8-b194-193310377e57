import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { logger } from "@/lib/logger";
import { insertValues } from "./insertValues";
import { expireUserStatus } from "./explreUserStatus";
import { expireReferralUser } from "./expireReferralUser";
export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({ success: false, message: "Unauthorized" });
  }

  try {
    const expiredViewHistory = await prisma.tllUserLambdaRecordSearchHistory.updateMany({
      where: {
        createdAt: {
          lt: dayjsWithTz().subtract(7, 'day').toISOString(),
        },
        isValid: true,
      },
      data: {
        isValid: false,
      },
    });

    logger.info("[Expired View History] " + expiredViewHistory.count);

    const expiredReportViewHistory = await prisma.systemReportViewHistory.updateMany({
      where: {
        createdAt: {
          lt: dayjsWithTz().subtract(7, 'day').toISOString(),
        },
        isValid: true,
      },
      data: {
        isValid: false,
      },
    });

    logger.info("[Expired Report View History] " + expiredReportViewHistory.count);

    sendLark({
      message: "[🛠️][Expired View | Report] " + expiredViewHistory.count + " | " + expiredReportViewHistory.count,
      url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
    });

    await insertValues();

    await expireUserStatus();

    await expireReferralUser();

    return NextResponse.json({ success: true, message: "Success" });
  } catch (error) {
    console.error(error);
    await sendLark({
      message: "[🛠️][Expired View | Report] " + error,
      url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
    });
    return NextResponse.json({ success: false, message: "Error" });
  } finally {
    await prisma.$disconnect(); // prevent long hanging connection
  }
}