import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";

export async function insertValues() {
  const today = dayjsWithTz().toISOString();

  const recordTypes = ["BUILDING", "HOUSE", "MANSION", "LAND"];

  // 使用 Promise.all 来并行处理所有记录类型
  await Promise.all(recordTypes.map(async (recordType) => {
    const count = await prisma.tllUserLambdaRecord.count({
      where: { recordType: recordType as any },
    });

    const findMatch = await prisma.systemMetric.findFirst({
      where: {
        recordDate: today,
        key: `TOTAL_PROPERTY_${recordType}_RECORD_COUNT` as any,
      },
    });

    if (findMatch) {
      await prisma.systemMetric.update({
        where: { id: findMatch.id },
        data: { value: count.toString() },
      });
    } else {
      await prisma.systemMetric.create({
        data: {
          recordDate: today,
          key: `TOTAL_PROPERTY_${recordType}_RECORD_COUNT` as any,
          value: count.toString(),
        },
      });
    }
  }));

  const TOTAL_PROPERTY_CHANGE_RECORD_COUNT = await prisma.tllUserLambdaRecordPriceChange.count();
  await updateOrCreateMetric(today, "TOTAL_PROPERTY_CHANGE_RECORD_COUNT", TOTAL_PROPERTY_CHANGE_RECORD_COUNT);

  const rentRecordTypes = ["BUILDING", "HOUSE"] as ("BUILDING" | "HOUSE")[];
  await Promise.all(rentRecordTypes.map(async (recordType) => {
    const value = await prisma.proBuildingHouseRent.count({
      where: { recordType: recordType as any },
    });
    await updateOrCreateMetric(today, `TOTAL_PROPERTY_${recordType}_RENT_RECORD_COUNT`, value);
  }));

  const TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT = await prisma.proMansionRent.count();
  await updateOrCreateMetric(today, "TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT", TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT);

  const TOTAL_USER_COUNT = await prisma.tllUser.count();
  await updateOrCreateMetric(today, "TOTAL_USER_COUNT", TOTAL_USER_COUNT);
  const TOTAL_USER_PAID_COUNT = await prisma.tllUser.count({
    where: {
      subscriptionStatus: {
        not: "FREE",
      },
    },
  });
  await updateOrCreateMetric(today, "TOTAL_USER_PAID_COUNT", TOTAL_USER_PAID_COUNT);

  const TOTAL_VALUATION_RECORD_COUNT = await prisma.valuationRecord.count();
  await updateOrCreateMetric(today, "TOTAL_VALUATION_RECORD_COUNT", TOTAL_VALUATION_RECORD_COUNT);

  const TOTAL_USER_DAU_COUNT = await prisma.systemUserActivity.findMany({
    where: {
      recordDate: today,
      userId: {
        not: null, // ✅ exclude nulls
      },
    },
    distinct: ['userId'] as never,
    select: {
      userId: true,
    },
  });
  await updateOrCreateMetric(today, "TOTAL_USER_DAU_COUNT", TOTAL_USER_DAU_COUNT.length);

  const TOTAL_USER_WAU_COUNT = await prisma.systemUserActivity.findMany({
    where: {
      recordDate: {
        lte: dayjsWithTz().startOf('day').toISOString(),
        gte: dayjsWithTz().startOf('day').subtract(7, 'day').toISOString(),
      },
      userId: {
        not: null, // ✅ exclude nulls
      },
    },
    distinct: ['userId'] as never,
    select: {
      userId: true,
    },
  });
  await updateOrCreateMetric(today, "TOTAL_USER_WAU_COUNT", TOTAL_USER_WAU_COUNT.length);

  const TOTAL_USER_MAU_COUNT = await prisma.systemUserActivity.findMany({
    where: {
      recordDate: {
        lte: dayjsWithTz().startOf('day').toISOString(),
        gte: dayjsWithTz().startOf('day').subtract(30, 'day').toISOString(),
      },
      userId: {
        not: null, // ✅ exclude nulls
      },
    },
    distinct: ['userId'] as never,
    select: {
      userId: true,
    },
  });
  await updateOrCreateMetric(today, "TOTAL_USER_MAU_COUNT", TOTAL_USER_MAU_COUNT.length);

  let rec = await prisma.tllUserSubscription.findMany({
    where: {
      subscriptionStatus: "ACTIVE",
    },
  });
  let totalMrr = 0;
  for (const item of rec) {
    if (item.interval === "MONTHLY") {
      totalMrr += item.amount;
    } else if (item.interval === "YEARLY") {
      totalMrr += item.amount / 12;
    }
  }
  await updateOrCreateMetric(today, "TOTAL_MRR", totalMrr);

  await sendLark({
    message: `[🛠️][System Metric] ${today} filed records`,
    url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
  });
}

async function updateOrCreateMetric(date: string, key: string, value: number) {
  const findMatch = await prisma.systemMetric.findFirst({
    where: { recordDate: date, key: key as any },
  });

  if (findMatch) {
    await prisma.systemMetric.update({
      where: { id: findMatch.id },
      data: { value: value.toString() },
    });
  } else {
    await prisma.systemMetric.create({
      data: {
        recordDate: date,
        key: key as any,
        value: value.toString(),
      },
    });
  }
}
