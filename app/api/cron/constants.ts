import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

const PAST_HOUR_TO_TEST = process.env.NODE_ENV === "development" ? 24 : 12; // 12H back
const REFILL_TIMESTAMP = PAST_HOUR_TO_TEST * 60 * 60 * 1000;

const VALUATION_MAX_YEAR_RETRIEVAL = 3;

export { REFILL_TIMESTAMP, PAST_HOUR_TO_TEST, VALUATION_MAX_YEAR_RETRIEVAL };

export const coef = (record: any) => {
  // TODO: adding coef for 再建築不可, sharehouse. etc
  if (!record || !record.landRight) return 1;

  if (record.landRight === "所有権") return 1;
  if (record.landRight === "所有権(再建築不可)") return 0.7;

  let index = 0.7

  // Else business 0.8-0.9 (use 0.8), residential is 0.6-0.7 (use 0.7)
  if (
    record !== undefined &&
    record.landType !== undefined &&
    record.landType !== null &&
    record.landType?.indexOf("商") > -1
  ) {
    index = 0.8;
  }

  return record.landRight === "底地権" ? parseFloat((1 - index).toFixed(2)) : parseFloat(index.toFixed(2));
};

export const colors = {
  urbaBlue: "#4AC1FF",
};
