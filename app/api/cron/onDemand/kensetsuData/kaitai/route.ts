// pages/api/scrape-projects.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import puppeteer from 'puppeteer';
import { parse, format, subMonths, isAfter } from 'date-fns';
import { prisma } from '@/lib/prisma';
import { writeFileSync } from 'fs';

export async function GET(req: Request) {
  const endDate = new Date('2020-03-01');
  const startDate = new Date('2025-04-01');
  let currentDate = startDate;
  let totalProcessed = 0;
  let totalSkipped = 0;

  let browser = null;
  let page = null;

  try {
    browser = await puppeteer.launch({ headless: false });
    page = await browser.newPage();

    while (isAfter(currentDate, endDate)) {
      const targetDate = format(currentDate, 'yyyyMM');
      const url = `https://www.kensetsu-databank.co.jp/kaitai-tokyo/osirase/month.php?target=${targetDate}`;
      console.log(`🔥 Processing date: ${targetDate}`);

      await page.goto(url, { waitUntil: 'networkidle2' });

      const projects = await page.evaluate(() => {
        const rows = Array.from(document.querySelectorAll('.ichi tr')).slice(1);
        return rows.map((row) => {
          const cols = Array.from(row.querySelectorAll('td')).map(td => td.textContent?.trim() || '');
          const parseIntSafe = (v: string) => parseInt(v.replace(/\D/g, '') || '0', 10);
          const parseFloatSafe = (v: string) => parseFloat(v.replace(/,|\u33a1|\s|m2|\u00a0/g, '') || '0');

          let type = 'DEMOLITION';

          return {
            compositeTitle: `${cols[2]}[${type}][${cols[6]}][${cols[7]}]`,
            recordCreatedAt: cols[0],
            name: row.querySelector('a')?.textContent?.trim() || '',
            address: cols[2],
            type,
            levelAboveGround: parseIntSafe(cols[4]),
            levelBelowGround: parseIntSafe(cols[5]),
            constructionArea: parseFloatSafe(cols[6]),
            projectStartDate: cols[7],
          };
        });
      });

      console.log(`🔥 Found ${projects.length} projects for ${targetDate}`);

      const batchSize = 1;
      for (let i = 0; i < projects.length; i += batchSize) {
        const batch = projects.slice(i, i + batchSize);
        console.log(`🔥 [${targetDate}] Processing batch ${i / batchSize + 1} of ${Math.ceil(projects.length / batchSize)}`);

        const batchPromises = batch.map(async (project) => {
          const existingProject = await prisma.proProject.findUnique({
            where: { compositeTitle: project.compositeTitle }
          });

          if (existingProject) {
            console.log(`🔥 Skipping existing project: ${project.compositeTitle}`);
            totalSkipped++;
            return;
          }

          const data = {
            compositeTitle: project.compositeTitle,
            name: project.name,
            address: project.address,
            type: project.type,
            levelAboveGround: project.levelAboveGround,
            levelBelowGround: project.levelBelowGround,
            constructionArea: project.constructionArea,
            projectStartDate: project.projectStartDate ? parse(project.projectStartDate, 'yyyy/MM/dd', new Date()) : null,
            recordCreatedAt: project.recordCreatedAt ? parse(project.recordCreatedAt, 'yyyy/MM/dd', new Date()) : null,
          };

          return prisma.$transaction(async (tx) => {
            await tx.proProject.create({ data: data as any });
          });
        });

        await Promise.all(batchPromises);
        totalProcessed += batch.length;
      }

      // writeFileSync('kaitai.json', JSON.stringify(projects, null, 2));

      currentDate = subMonths(currentDate, 1);
    }



    return new Response(JSON.stringify({
      success: true,
      count: totalProcessed,
      skipped: totalSkipped,
      dateRange: {
        start: format(startDate, 'yyyy/MM'),
        end: format(endDate, 'yyyy/MM')
      }
    }), { status: 200 });
  } catch (error: any) {
    console.error('\uD83D\uDD25 Error scraping projects:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error.message,
      dateRange: {
        start: format(startDate, 'yyyy/MM'),
        end: format(endDate, 'yyyy/MM')
      }
    }), { status: 500 });
  } finally {
    if (browser) await browser.close();
  }
}