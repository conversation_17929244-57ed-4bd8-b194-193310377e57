import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";

import { TllCustomerNeedCriteriaType } from "@/lib/definitions";
import { TllUserLambdaRecordRecordType } from "@prisma/client";
import { getPostalCodeByAddressAction } from "@/actions/geoPostalCodes";
import { getGeocodeInfo } from "@/lib/thirdParty/google";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const dataToFill = await prisma.proProject.findMany({
      where: {
        postalCode: null,
        projectStartDate: {
          gte: new Date("2022-01-01"),
        }
      },
      // take: 50
      // take: 500,
    });

    let index = 0;

    for (let d of dataToFill) {
      console.log(`------- filling ${index++}/${dataToFill.length}  id uis ${d.id} -------`);

      let addressToUse = d.address || d.addressChiban;
      if (!addressToUse) {
        continue;
      }

      let match = await prisma.geoAddress.findFirst({
        where: {
          address: addressToUse,
        },
      });

      if (match && match.pcode && match.longitude && match.latitude) {
        console.log("[case 1][project record] updating from geo address record", d.id);

        let matchPostalCode = await prisma.geoPostalCode.findFirst({
          where: {
            postalCode: match.pcode.toString(),
          },
        });

        await prisma.proProject.update({
          where: { id: d.id },
          data: {
            postalCode: match.pcode?.toString(),
            longitude: match.longitude,
            latitude: match.latitude,
            ...(matchPostalCode ? {
              prefectureCode: matchPostalCode.prefectureCode,
              areaCode: matchPostalCode.areaCode,
            } : {}),
          } as any,
        });
      } else {
        const res = await getGeocodeInfo(d.address);

        if (res.postalCode) {
          console.log("[case 2][project record] updating from geocoding", d.id);

          let matchPostalCode = await prisma.geoPostalCode.findFirst({
            where: {
              postalCode: res.postalCode.toString(),
            },
          });

          await prisma.proProject.update({
            where: { id: d.id },
            data: {
              postalCode: res.postalCode.toString(),
              longitude: res.lng,
              latitude: res.lat,
              ...(matchPostalCode ? {
                prefectureCode: matchPostalCode.prefectureCode,
                areaCode: matchPostalCode.areaCode,
              } : {}),
            } as any,
          });

          if (!match) {
            let newlyCreated = await prisma.geoAddress.create({
              data: {
                address: addressToUse,
                pcode: res.postalCode,
                longitude: res.lng,
                latitude: res.lat,
              },
            });

            console.log("[2a] creating new geo address record, record is", newlyCreated.id, "address is", newlyCreated.address);
          } else {
            console.log("[2b] updating existing geo address record", match.id);
            await prisma.geoAddress.update({
              where: { id: match.id },
              data: {
                pcode: res.postalCode,
                longitude: res.lng,
                latitude: res.lat,
              },
            });
          }
        }
      }
    }

    return NextResponse.json({ message: "success" });
  } catch (err: any) {
    logger.error("🔥 PDF parsing error 🔥", err);
    return NextResponse.json({ message: "PDF parsing error!" });
  }
}