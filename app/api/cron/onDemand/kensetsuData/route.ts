// pages/api/scrape-projects.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import puppeteer from 'puppeteer';
import { parse, format, subMonths, isAfter } from 'date-fns';
import { prisma } from '@/lib/prisma';
import { write, writeFileSync } from 'fs';
import dayjs from 'dayjs';

export async function GET(req: Request) {
  const endDate = new Date('2012-01-01');
  const startDate = new Date('2023-09-01'); // Start from most recent
  let currentDate = startDate;
  let totalProcessed = 0;
  let totalSkipped = 0;

  let browser = null;
  let page = null;

  try {
    browser = await puppeteer.launch({ headless: false });
    page = await browser.newPage();

    while (isAfter(currentDate, endDate)) {
      const targetDate = format(currentDate, 'yyyyMM');
      const url = `https://www.kensetsu-databank.co.jp/osirase/month.php?target=${targetDate}`;

      console.log(`🔥 Processing date: ${targetDate}`);

      await page.goto(url, { waitUntil: 'networkidle2' });

      const projects = await page.evaluate(() => {
        const rows = Array.from(document.querySelectorAll('.ichi tr')).slice(1);
        return rows.map((row) => {
          const cols = Array.from(row.querySelectorAll('td')).map(td => td.textContent?.trim() || '');

          const parseIntSafe = (v: string) => parseInt(v.replace(/\D/g, '') || '0', 10);
          const parseFloatSafe = (v: string) => parseFloat(v.replace(/,|\u33a1|\s/g, '') || '0');

          let address = cols[3];
          let addressChiban = cols[2];

          let type = 'CONSTRUCTION';
          if (cols[5] === '増築') {
            type = 'ADDITION';
          }

          let constructionArea = parseFloatSafe(cols[8])
          let startDate = cols[9];

          let projectEndDate = cols[10];
          let projectEndDateYear = projectEndDate ? dayjs(projectEndDate).year() : null;
          let projectEndDateMonth = projectEndDate ? dayjs(projectEndDate).month() + 1 : null;

          return {
            compositeTitle: `${address || addressChiban}[${type}][${constructionArea}][${startDate}]`,
            recordCreatedAt: cols[0],
            name: row.querySelector('a')?.textContent?.trim() || '',
            addressChiban,
            address,
            type,
            projectUsage: cols[4],
            levelAboveGround: parseIntSafe(cols[6]),
            levelBelowGround: parseIntSafe(cols[7]),
            constructionArea,
            projectStartDate: startDate,
            projectEndDate: projectEndDate,
            projectEndDateYear: projectEndDateYear,
            projectEndDateMonth: projectEndDateMonth,
          };
        });
      });

      console.log(`🔥 Found ${projects.length} projects for ${targetDate}`);

      // Process projects in batches of 10
      const batchSize = 30;
      for (let i = 0; i < projects.length; i += batchSize) {
        const batch = projects.slice(i, i + batchSize);

        // 去重：同一个 batch 内只保留 compositeTitle 首次出现的项目
        const seenTitles = new Set();
        const uniqueBatch = batch.filter(p => {
          if (seenTitles.has(p.compositeTitle)) return false;
          seenTitles.add(p.compositeTitle);
          return true;
        });

        console.log(`🔥 [${targetDate}] Processing batch ${i / batchSize + 1} of ${Math.ceil(projects.length / batchSize)} with ${uniqueBatch.length} unique entries`);

        const batchPromises = uniqueBatch.map(async (project) => {
          try {
            // First check if the project already exists
            const existingProject = await prisma.proProject.findUnique({
              where: { compositeTitle: project.compositeTitle }
            });

            if (existingProject) {
              console.log(`🔥 Skipping existing project: ${project.compositeTitle}`);
              totalSkipped++;
              return;
            }

            let projectEndDate = project.projectEndDate ? parse(project.projectEndDate, 'yyyy/MM/dd', new Date()) : null;
            let projectEndDateYear = projectEndDate ? dayjs(projectEndDate).year() : null;
            let projectEndDateMonth = projectEndDate ? dayjs(projectEndDate).month() + 1 : null;

            const data = {
              compositeTitle: project.compositeTitle,
              name: project.name,
              addressChiban: project.addressChiban,
              address: project.address,
              type: project.type,
              projectUsage: project.projectUsage,
              levelAboveGround: project.levelAboveGround,
              levelBelowGround: project.levelBelowGround,
              constructionArea: project.constructionArea,
              projectStartDate: project.projectStartDate ? parse(project.projectStartDate, 'yyyy/MM/dd', new Date()) : null,
              projectEndDate: projectEndDate,
              projectEndDateYear: projectEndDateYear,
              projectEndDateMonth: projectEndDateMonth,
              recordCreatedAt: project.recordCreatedAt ? parse(project.recordCreatedAt, 'yyyy/MM/dd', new Date()) : null,
            };

            await prisma.$transaction(async (tx) => {
              await tx.proProject.create({ data: data as any });
            });

            totalProcessed++;
          } catch (err) {
            console.error(`❌ Failed to insert project: ${project.compositeTitle}`, err);
          }
        });

        await Promise.all(batchPromises);
      }

      currentDate = subMonths(currentDate, 1); // Go backwards in time
    }

    return new Response(JSON.stringify({
      success: true,
      count: totalProcessed,
      skipped: totalSkipped,
      dateRange: {
        start: format(startDate, 'yyyy/MM'),
        end: format(endDate, 'yyyy/MM')
      }
    }), { status: 200 });
  } catch (error: any) {
    console.error('🔥 Error scraping projects:', error);
    return new Response(JSON.stringify({
      success: false,
      message: error.message,
      dateRange: {
        start: format(startDate, 'yyyy/MM'),
        end: format(endDate, 'yyyy/MM')
      }
    }), { status: 500 });
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}
