import { prisma } from "@/lib/prisma";
import { getChromeBrowser, getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import dayjs from "dayjs";
import { sendLark } from "@/lib/thirdParty/lark";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  const ended = false;

  const browser = null;
  const page = null;

  // try {
  //   // Overall here https://www.homes.co.jp/chintai/price/
  //   let LIST_TO_CRAWL = {
  //     JR中央本線: "https://www.homes.co.jp/chintai/tokyo/chuohonsen-line/price/",
  //     JR中央線: "https://www.homes.co.jp/chintai/tokyo/chuo-line/price/",
  //     JR五日市線: "https://www.homes.co.jp/chintai/tokyo/itsukaichi-line/price/",
  //     "JR京浜東北・根岸線":
  //       "https://www.homes.co.jp/chintai/tokyo/keihintohokunegishi-line/price/",
  //     JR京葉線: "https://www.homes.co.jp/chintai/tokyo/keiyo-line/price/",
  //     JR八高線: "https://www.homes.co.jp/chintai/tokyo/hachiko-line/price/",
  //     JR南武線: "https://www.homes.co.jp/chintai/tokyo/nambu-line/price/",
  //     JR埼京線: "https://www.homes.co.jp/chintai/tokyo/saikyo-line/price/",
  //     JR山手線: "https://www.homes.co.jp/chintai/tokyo/yamanote-line/price/",
  //     JR常磐線: "https://www.homes.co.jp/chintai/tokyo/joban-line/price/",
  //     JR東北本線:
  //       "https://www.homes.co.jp/chintai/tokyo/tohokuhonsen-line/price/",
  //     JR東海道本線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokaidohonsen-line/price/",
  //     JR横浜線: "https://www.homes.co.jp/chintai/tokyo/yokohama-line/price/",
  //     JR横須賀線: "https://www.homes.co.jp/chintai/tokyo/yokosuka-line/price/",
  //     JR武蔵野線: "https://www.homes.co.jp/chintai/tokyo/musashino-line/price/",
  //     JR湘南新宿ライン宇須:
  //       "https://www.homes.co.jp/chintai/tokyo/shonanshinjukulineutsunomiyayokosuka-line/price/",
  //     JR湘南新宿ライン高海:
  //       "https://www.homes.co.jp/chintai/tokyo/shonanshinjukulinetakasakitokaido-line/price/",
  //     JR総武本線: "https://www.homes.co.jp/chintai/tokyo/sobuhonsen-line/price/",
  //     JR総武線: "https://www.homes.co.jp/chintai/tokyo/sobu-line/price/",
  //     JR青梅線: "https://www.homes.co.jp/chintai/tokyo/ome-line/price/",
  //     JR高崎線: "https://www.homes.co.jp/chintai/tokyo/takasaki-line/price/",
  //     つくばエクスプレス:
  //       "https://www.homes.co.jp/chintai/tokyo/tsukubaexpress-line/price/",
  //     京急本線: "https://www.homes.co.jp/chintai/tokyo/keikyuhonsen-line/price/",
  //     京急空港線: "https://www.homes.co.jp/chintai/tokyo/keikyukuko-line/price/",
  //     京成押上線:
  //       "https://www.homes.co.jp/chintai/tokyo/keiseioshiage-line/price/",
  //     京成本線: "https://www.homes.co.jp/chintai/tokyo/keiseihonsen-line/price/",
  //     京成金町線:
  //       "https://www.homes.co.jp/chintai/tokyo/keiseikanamachi-line/price/",
  //     京王井の頭線:
  //       "https://www.homes.co.jp/chintai/tokyo/keioinokashira-line/price/",
  //     京王動物園線:
  //       "https://www.homes.co.jp/chintai/tokyo/keiodobutsuen-line/price/",
  //     京王相模原線:
  //       "https://www.homes.co.jp/chintai/tokyo/keiosagamihara-line/price/",
  //     京王競馬場線:
  //       "https://www.homes.co.jp/chintai/tokyo/keiokeibajo-line/price/",
  //     京王線: "https://www.homes.co.jp/chintai/tokyo/keio-line/price/",
  //     京王高尾線: "https://www.homes.co.jp/chintai/tokyo/keiotakao-line/price/",
  //     北総鉄道: "https://www.homes.co.jp/chintai/tokyo/hokusotetsudo-line/price/",
  //     埼玉高速鉄道:
  //       "https://www.homes.co.jp/chintai/tokyo/saitamakosokutetsudo-line/price/",
  //     多摩都市モノレール:
  //       "https://www.homes.co.jp/chintai/tokyo/tamatoshimonorail-line/price/",
  //     小田急多摩線:
  //       "https://www.homes.co.jp/chintai/tokyo/odakyutama-line/price/",
  //     小田急小田原線:
  //       "https://www.homes.co.jp/chintai/tokyo/odakyuodawara-line/price/",
  //     成田スカイアクセス:
  //       "https://www.homes.co.jp/chintai/tokyo/naritasukaiakusesu-line/price/",
  //     新交通ゆりかもめ:
  //       "https://www.homes.co.jp/chintai/tokyo/shinkotsuyurikamome-line/price/",
  //     日暮里舎人ライナー:
  //       "https://www.homes.co.jp/chintai/tokyo/nipporitoneriliner-line/price/",
  //     東京メトロ丸ノ内線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometromarunochi-line/price/",
  //     東京メトロ副都心線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometrofukutoshin-line/price/",
  //     東京メトロ千代田線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometrochiyoda-line/price/",
  //     東京メトロ半蔵門線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometrohanzomon-line/price/",
  //     東京メトロ南北線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometronamboku-line/price/",
  //     東京メトロ日比谷線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometrohibiya-line/price/",
  //     東京メトロ有楽町線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometroyurakucho-line/price/",
  //     東京メトロ東西線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometrotozai-line/price/",
  //     東京メトロ銀座線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyometroginza-line/price/",
  //     東京モノレール:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyomonorail-line/price/",
  //     東京臨海高速鉄道りんかい線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyorinkaikosokutetsudorinkai-line/price/",
  //     東急世田谷線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyusetagaya-line/price/",
  //     東急多摩川線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyutamagawa-line/price/",
  //     東急大井町線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyuoimachi-line/price/",
  //     東急東横線: "https://www.homes.co.jp/chintai/tokyo/tokyutoyoko-line/price/",
  //     東急池上線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyuikegami-line/price/",
  //     東急田園都市線:
  //       "https://www.homes.co.jp/chintai/tokyo/tokyudenentoshi-line/price/",
  //     東急目黒線: "https://www.homes.co.jp/chintai/tokyo/tokyumeguro-line/price/",
  //     東武亀戸線: "https://www.homes.co.jp/chintai/tokyo/tobukameido-line/price/",
  //     東武伊勢崎線:
  //       "https://www.homes.co.jp/chintai/tokyo/tobuisesaki-line/price/",
  //     東武大師線: "https://www.homes.co.jp/chintai/tokyo/tobudaishi-line/price/",
  //     東武東上線: "https://www.homes.co.jp/chintai/tokyo/tobutojo-line/price/",
  //     西武国分寺線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibukokubunji-line/price/",
  //     西武多摩川線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibutamagawa-line/price/",
  //     西武多摩湖線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibutamako-line/price/",
  //     西武山口線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibuyamaguchi-line/price/",
  //     西武拝島線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibuhaijima-line/price/",
  //     西武新宿線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibushinjuku-line/price/",
  //     西武有楽町線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibuyurakucho-line/price/",
  //     西武池袋線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibuikebukuro-line/price/",
  //     西武西武園線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibuseibuen-line/price/",
  //     西武豊島線:
  //       "https://www.homes.co.jp/chintai/tokyo/seibutoshima-line/price/",
  //     都営三田線: "https://www.homes.co.jp/chintai/tokyo/toeimita-line/price/",
  //     都営大江戸線: "https://www.homes.co.jp/chintai/tokyo/toeioedo-line/price/",
  //     都営新宿線:
  //       "https://www.homes.co.jp/chintai/tokyo/toeishinjuku-line/price/",
  //     都営浅草線: "https://www.homes.co.jp/chintai/tokyo/toeiasakusa-line/price/",
  //     都電荒川線:
  //       "https://www.homes.co.jp/chintai/tokyo/todenarakawa-line/price/",
  //   } as any;

  //   // Find the line to start with
  //   let LINE_NAME = null;
  //   let LINE_URL = null;
  //   for await (let line_name of Object.keys(LIST_TO_CRAWL)) {
  //     let rows = [];
  //     // await prisma.geoRailwayStationRent.findMany({
  //     //   where: {
  //     //     railwayLineName: line_name,
  //     //     propertyType: "APARTMENT",
  //     //     roomLayoutType: "1R/1K/1DK",
  //     //     roomSizeRange: "50-60",
  //     //     stationDistanceMinuteRange: "10-15",
  //     //     builtYearRange: "20-30",
  //     //   },
  //     // });
  //     return;

  //     if (!rows.length) {
  //       LINE_NAME = line_name;
  //       LINE_URL = LIST_TO_CRAWL[line_name];
  //       console.log(`🔥 Start with ${line_name}, for URL ${LINE_URL} 🔥`);

  //       do {
  //         try {
  //           browser = await getChromeBrowser();
  //           page = await getPageWithRandomUserAgent(browser);
  //           // BASE_URL: https://www.homes.co.jp/chintai/price/
  //           // COMPLETED LIST: ["西武池袋線"]

  //           // Step 1: Go to the page
  //           await page.goto(LINE_URL, { waitUntil: "networkidle2" });

  //           // Step 2: Loop
  //           // Step 2.1 Pick type - Mansion of Apart
  //           let selectors = {
  //             property_type: {
  //               MANSION: "#cond_bukken_group_2",
  //               APARTMENT: "#cond_bukken_group_1",
  //               // HOUSE: "#cond_bukken_group_3",
  //             },
  //             room_size_range: {
  //               "10-15": "12.5",
  //               "15-20": "17.5",
  //               "20-25": "22.5",
  //               "25-30": "27.5",
  //               "30-35": "32.5",
  //               "35-40": "37.5",
  //               "40-45": "42.5",
  //               "45-50": "47.5",
  //               "50-60": "55",
  //               // "60-70": "65",
  //               // "70-80": "75",
  //               // "80-90": "85",
  //               // "90-100": "95",
  //             },
  //             station_distance_minute_range: {
  //               "0-5": "2.5",
  //               "5-10": "7.5",
  //               "10-15": "12.5",
  //               // "15-20": "17.5",
  //             },
  //             built_year_range: {
  //               // "0-1": "1",
  //               "0-5": "2.5",
  //               "5-10": "7.5",
  //               "10-15": "12.5",
  //               "15-20": "17.5",
  //               "20-30": "25",
  //             },
  //             room_layout_type: {
  //               "1R/1K/1DK": ".madoriList li:nth-child(1)",
  //               // "1LDK/2K/2DK": ".madoriList li:nth-child(2)",
  //               // "2LDK/3K/3DK": ".madoriList li:nth-child(3)",
  //               // "3LDK/4K/4DK": ".madoriList li:nth-child(4)",
  //             },
  //           };

  //           for (let propertyType of Object.keys(selectors["property_type"])) {
  //             for (let roomSize of Object.keys(selectors["room_size_range"])) {
  //               for (let stationDistance of Object.keys(
  //                 selectors["station_distance_minute_range"]
  //               )) {
  //                 for (let builtYear of Object.keys(
  //                   selectors["built_year_range"]
  //                 )) {
  //                   for (let layout of Object.keys(
  //                     selectors["room_layout_type"]
  //                   )) {
  //                     const rows = await prisma.geoRailwayStationRent.findMany({
  //                       where: {
  //                         railwayLineName: LINE_NAME,
  //                         propertyType: propertyType,
  //                         roomLayoutType: layout,
  //                         roomSizeRange: roomSize,
  //                         stationDistanceMinuteRange: stationDistance,
  //                         builtYearRange: builtYear,
  //                       },
  //                     });

  //                     if (rows.length) {
  //                       continue;
  //                     }

  //                     // Step 2.1 Pick type
  //                     await page.click(selectors["property_type"][propertyType]);

  //                     // Step 2.2 Pick area
  //                     await page.select(
  //                       "select#cond_housearea_range",
  //                       selectors["room_size_range"][roomSize]
  //                     );

  //                     // Step 2.3 Pick station
  //                     await page.select(
  //                       "select#cond_walkminutes_range",
  //                       selectors["station_distance_minute_range"][
  //                       stationDistance
  //                       ]
  //                     );

  //                     // Step 2.4 Pick year
  //                     await page.select(
  //                       "select#cond_houseage_range",
  //                       selectors["built_year_range"][builtYear]
  //                     );

  //                     // Step 2.5 Pick the layout
  //                     await page.click(selectors["room_layout_type"][layout]);

  //                     await new Promise(resolve => setTimeout(resolve, 2000));

  //                     let results = [];

  //                     // Step 3 Scrawling the values

  //                     let RESULT_COUNT_SELECTOR = "tbody .station";

  //                     let BLOCK_SELECTOR = "tbody tr:nth-child(INDEX)";
  //                     let BLOCK = {
  //                       station: ".station",
  //                       price: ".price .money .num",
  //                     };

  //                     await page.waitForSelector(RESULT_COUNT_SELECTOR);

  //                     let RESULT_COUNT = (await page.$$(RESULT_COUNT_SELECTOR))
  //                       .length;

  //                     for (let index = 1; index <= RESULT_COUNT; index++) {
  //                       const currentSelector = BLOCK_SELECTOR.replace(
  //                         "INDEX",
  //                         index
  //                       );

  //                       let currentRecord = {};
  //                       for await (const field of Object.keys(BLOCK)) {
  //                         const selector = `${currentSelector} ${BLOCK[field]}`;

  //                         let currentValue = "";

  //                         currentValue = await page.evaluate(
  //                           (sel, field) => {
  //                             let res = document.querySelector(sel);
  //                             if (res === null) {
  //                               return null;
  //                             }
  //                             res = res.textContent;

  //                             // FIXME: wont work for the other lines
  //                             if (field === "price") res = parseFloat(res);
  //                             return res;
  //                           },
  //                           selector,
  //                           field
  //                         );

  //                         currentRecord[field] = currentValue;
  //                       }

  //                       results.push(currentRecord);
  //                     }

  //                     for (let rec of results) {
  //                       await prisma.geoRailwayStationRent.create({
  //                         data: {
  //                           stationName: rec["station"],
  //                           railwayLineName: LINE_NAME,
  //                           propertyType: recordSubType,
  //                           roomLayoutType: layout,
  //                           roomSizeRange: roomSize,
  //                           stationDistanceMinuteRange: stationDistance,
  //                           builtYearRange: builtYear,
  //                           price: rec["price"],
  //                         },
  //                       });
  //                     }
  //                   }
  //                 }
  //               }
  //             }
  //           }
  //           ended = true;
  //         } catch (err: any) {
  //           await page.screenshot({
  //             path: `debug/${dayjs().utc().format("MM-DD HH-mm-ss")}.png`,
  //           });

  //           console.log("🔥 err 🔥");
  //           console.log(err, err.stack); // an error occurred

  //           // Make waiting time longer before restart
  //           console.log("🔥 Restarting... 🔥");
  //           await new Promise(resolve => setTimeout(resolve, 20000));
  //         }
  //       } while (!ended);

  //       console.log(`🔥 Completed ${line_name}, for URL ${LINE_URL} 🔥`);
  //       // await web.chat.postMessage({
  //       //   channel: "C028R2Z6NKS",
  //       //   text: `🔥 Completed ${line_name}, for URL ${LINE_URL} 🔥`,
  //       // });
  //       await sendLark(`🔥 Completed ${line_name}, for URL ${LINE_URL} 🔥`);
  //     } else {
  //       console.log(`🔥 Skipping ${line_name}, due to have existing data🔥`);
  //       // await web.chat.postMessage({
  //       //   channel: "C028R2Z6NKS",
  //       //   text: `🔥 Skipping ${line_name}, due to have existing data🔥`,
  //       // });
  //       await sendLark(
  //         `🔥 Skipping ${line_name}, due to have existing data🔥`
  //       );
  //     }
  //   }

  //   if (page) {
  //     await page.close();
  //   }

  //   if (browser) {
  //     await browser.close();
  //   }

  //   return;
  // } catch (err: any) {
  //   console.log("🔥 err 🔥");
  //   console.log(err, err.stack); // an error occurred
  // } finally {
  //   if (page) {
  //     await page.close();
  //   }

  //   if (browser) {
  //     await browser.close();
  //   }
  // }
};
