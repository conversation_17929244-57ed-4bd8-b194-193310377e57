import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import pdf from "pdf-parse";
import fs from "fs";
import path from "path";

interface Entry {
  id: number;
  received_date: string;
  registration_type: string;
  type: string;

  // Row 2 content
  property_type?: string;
  property_new_type?: string;
  address?: string;
}

function extractEntries(lines: string[]): Entry[] {
  const entries: Entry[] = [];

  const toHalfWidth = (str: string): string =>
    str.replace(/[！-～]/g, ch =>
      String.fromCharCode(ch.charCodeAt(0) - 0xFEE0)
    )
      .replace(/　/g, ' ')
      .replace(/[（）]/g, ch => (ch === '（' ? '(' : ch === '）' ? ')' : ch))
      .replace(/[【】]/g, '')
      .replace(/[│┃]/g, '')
      .trim();

  for (let i = 0; i < lines.length; i++) {
    const l1 = toHalfWidth(lines[i] || "");
    const l2 = toHalfWidth(lines[i + 1] || "");

    if (!l1.includes('第') || l1.includes('＊＊＊＊＊') || l2.includes('物件なし')) continue;

    const mainMatch = l1.match(/第\s*(\d+)号.*?(\d{1,2})月\s*(\d{1,2})日受付\s*\((.+?)\)\s*(.+)?/);
    if (!mainMatch) continue;

    const id = parseInt(mainMatch[1]);
    const received_date = `令和7年${mainMatch[2]}月${mainMatch[3]}日`;
    const registration_type = mainMatch[4].trim();
    const type = (mainMatch[5] || "").trim();

    const assetMatch = l2.match(/^(新|既)[)）]?\s*(区建|土地|建物|共担)?\s*(.+)?$/);

    const property_new_type = assetMatch?.[1] ? `${assetMatch[1]}）` : undefined;
    const property_type = assetMatch?.[2]?.trim() || undefined;
    const address = assetMatch?.[3]?.trim() || undefined;

    let address_ward: string | undefined = undefined;
    if (address) {
      const wardMatch = address.match(/([^\s]{1,5}区)/); // e.g., 渋谷区、中野区
      if (wardMatch) address_ward = wardMatch[1];
    }

    entries.push({
      id,
      received_date,
      registration_type,
      type,
      ...(property_new_type ? { property_new_type } : {}),
      ...(property_type ? { property_type } : {}),
      ...(address ? { address } : {}),
      ...(address_ward ? { address_ward } : {}),
    });

    i += 1;
  }

  return entries;
}

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const pdfUrl = "https://waqahdtjadldhstauanr.supabase.co/storage/v1/object/public/change-records/2503.pdf"; // 远程 PDF URL
    console.log("🔥 PDF URL is", pdfUrl);

    const response = await fetch(pdfUrl); // 使用 fetch 从远程获取 PDF
    const buffer = Buffer.from(await response.arrayBuffer());

    const data = await pdf(buffer);

    const text = data.text;
    console.log("🔥 text is", text.slice(0, 500));

    const lines = text.split('\n').map(line => line.trim()).filter(Boolean);
    const entries = extractEntries(lines);

    console.log("🔥 entries is", entries.length);

    fs.writeFileSync(path.join(process.cwd(), 'output.json'), JSON.stringify(entries, null, 2), 'utf8');

    // You then convert this into excel etc // 

    console.log('✅ PDF Extraction completed.');
    return NextResponse.json({ message: "PDF parsed and saved." });

  } catch (err: any) {
    logger.error("🔥 PDF parsing error 🔥", err);
    return NextResponse.json({ message: "PDF parsing error!" });
  }
}