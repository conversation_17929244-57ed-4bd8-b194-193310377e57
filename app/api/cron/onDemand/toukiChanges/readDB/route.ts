import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import fs from "fs";
import path from "path";
import { prisma } from "@/lib/prisma";
import { Prisma } from "@prisma/client";
import { add } from "date-fns";

// 定义 data.json 的数据类型
interface ToukiData {
  id: string;
  received_date: string;
  registration_type: string;
  type: string;
  property_new_type: string;
  property_type: string;
  address: string;
  address_ward: string;
}

// Sample Data
// {
//   "id": "6028",
//   "received_date": "令和7年3月3日",
//   "registration_type": "単独",
//   "type": "所有権移転売買",
//   "property_new_type": "既）",
//   "property_type": "区建",
//   "address": "港区赤坂9丁目101-3-557",
//   "address_ward": "港区"
// },

// I think 50 is still very slow you might be able to get more // 

// 处理地址中的额外信息
function processAddressAndType(address: string, type: string): { address: string; type: string } {
  // 查找超过3个连续空格的位置
  const tripleSpaceIndex = address.indexOf('   ');
  if (tripleSpaceIndex === -1) {
    return { address, type };
  }

  // 分割地址和额外信息
  const mainAddress = address.substring(0, tripleSpaceIndex).trim();
  const extraInfo = address.substring(tripleSpaceIndex).trim();

  // console.log('🔥 地址处理:', {
  //   originalAddress: address,
  //   mainAddress,
  //   extraInfo,
  //   originalType: type
  // });

  // 如果类型为空，使用额外信息作为类型
  if (!type || type === '') {
    return { address: mainAddress, type: extraInfo };
  }

  // 如果类型不为空，将额外信息添加到类型后面
  return {
    address: mainAddress,
    type: `${type} ${extraInfo}`
  };
}

async function readAndProcessData() {
  try {
    // 读取 data.json 文件
    const dataPath = path.join(process.cwd(), 'data.json');
    const fileContent = fs.readFileSync(dataPath, 'utf-8');
    const data: ToukiData[] = JSON.parse(fileContent);

    console.log('🔥 开始处理数据，共', data.length, '条记录');

    const areaCodeMapper = {
      "中野区": 13114,
      "新宿区": 13104,
      "世田谷区": 13112,
      "杉並区": 13115,
      "渋谷区": 13113,
      "品川区": 13109,
      "目黒区": 13110,
      "港区": 13103,
    };

    const coninutationTypeMapper = {
      "連先": "CONTINUATION_BEFORE",
      "連続": "CONTINUATION_AFTER",
      "単独": "SINGLE",
    };

    const propertyTypeMapper = {
      "区建": "MANSION",
      "建物": "BUILDING_HOUSE",
      "土地": "LAND",
    };

    function warekiReiwaToDate(warekiDate: string): Date | null {
      const match = warekiDate.match(/^令和([0-9０-９元]+)年([0-9０-９]+)月([0-9０-９]+)日/);
      if (!match) return null;
      let [, yearStr, monthStr, dayStr] = match;
      let year = yearStr === "元" ? 1 : parseInt(yearStr.replace(/[０-９]/g, s => String.fromCharCode(s.charCodeAt(0) - 65248)));
      let month = parseInt(monthStr.replace(/[０-９]/g, s => String.fromCharCode(s.charCodeAt(0) - 65248)));
      let day = parseInt(dayStr.replace(/[０-９]/g, s => String.fromCharCode(s.charCodeAt(0) - 65248)));
      const seirekiYear = 2018 + year;
      return new Date(seirekiYear, month - 1, day);
    }

    async function findPostalCodeFromAddress(address: string): Promise<any | null> {
      let curbed = address.indexOf("丁目") === -1 ? address : address.split("丁目")[0] + "丁目";
      curbed = curbed.replace("(", "（").replace(")", "）").replace(/[A-Za-z0-9]/g, (s) =>
        String.fromCharCode(s.charCodeAt(0) + 0xfee0)
      );
      let findMatch = await prisma.geoAddress.findFirst({
        where: {
          address: {
            contains: curbed
          },
        },
      });
      if (!findMatch) return null;
      return await prisma.geoPostalCode.findFirst({
        where: {
          postalCode: findMatch.pcode.toString()
        },
      });
    }

    // === 批量处理 ===
    const batchSize = 50;
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      console.log(`🔥 处理第 ${i + 1} ~ ${i + batch.length} 条`);

      await Promise.all(
        batch.map(async (record) => {
          try {
            if (!areaCodeMapper[record.address_ward as keyof typeof areaCodeMapper]) {
              console.log('❌ 地址不存在:', record.address_ward);
              return;
            }
            const { address: processedAddress, type: processedType } = processAddressAndType(record.address, record.type);
            const postalCodes = await findPostalCodeFromAddress(processedAddress);

            const recordData: any = {
              localId: Number(record.id),
              areaCode: areaCodeMapper[record.address_ward as keyof typeof areaCodeMapper],
              registrationContinuationType: coninutationTypeMapper[record.registration_type as keyof typeof coninutationTypeMapper],
              registrationType: processedType,
              propertyIsNew: record.property_new_type.indexOf("新") !== -1,
              propertyType: propertyTypeMapper[record.property_type as keyof typeof propertyTypeMapper],
              addressChiban: processedAddress,
              recordDate: warekiReiwaToDate(record.received_date),
              ...(postalCodes ? {
                postalCode: postalCodes.postalCode?.toString(),
                longitude: postalCodes.longitude,
                latitude: postalCodes.latitude,
              } : {})
            };

            // 注意：模型名应为 proRegistraionRecord（不是 proRegistrationRecord）
            const existingRecord = await prisma.proRegistrationRecord.findFirst({
              where: {
                areaCode: recordData.areaCode,
                localId: Number(recordData.localId),
              },
            });

            if (existingRecord && existingRecord.id) {
              console.log('✅ Exist, skip', recordData.areaCode, recordData.localId);
            } else {
              await prisma.proRegistrationRecord.create({
                data: recordData,
              });
              // console.log('✅ 创建新记录:', recordData.areaCode, recordData.localId);
            }
          } catch (error) {
            console.error('❌ 处理记录失败:', record.id, error);
          }
        })
      );

      console.log(`🔥 完成批次 ${i / batchSize + 1}/${Math.ceil(data.length / batchSize)}`);
    }

    console.log('✅ 所有数据处理完成');
    return { success: true, message: `成功处理 ${data.length} 条记录` };
  } catch (error) {
    console.error('❌ 数据处理失败:', error);
    throw error;
  }
}

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const result = await readAndProcessData();
    return NextResponse.json(result);
  } catch (err: any) {
    logger.error("🔥 数据处理错误 🔥", err);
    return NextResponse.json({
      success: false,
      message: "数据处理错误!",
      error: err.message
    }, { status: 500 });
  }
}