
import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { ActionResponse } from "@/lib/definitions";
import { sendNotificationsToAll } from "@/actions/tllUserPushSubscriptions";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { sum, uniqBy } from "lodash-es";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({ success: false, message: "Unauthorized" });
  }

  try {
    let subscriptions = await prisma.tllUserPushSubscription.findMany({
      where: {
        user: {
          accessLevel: {
            gte: 90 // 只选择访问级别大于等于90的用户
          }
        }
      },
      include: {
        user: true,
      }
    });

    let todayUserLambdaRecordsAll = await prisma.tllUserLambdaRecordPriceChange.findMany({
      where: {
        createdAt: {
          gte: dayjsWithTz().add(-6, 'hours').toDate(),
        },
      },
    });

    let uniqRecordIds = uniqBy(todayUserLambdaRecordsAll, 'recordId')?.map((record: any) => record.recordId);
    let uniqCount = uniqRecordIds?.length;
    let dataToPush: { title: string, message: string, subscription: any }[] = [];
    let titleBase = `[${dayjsWithTz().tz('Asia/Tokyo').format('MM-DD HH:mm')}]新規物件${uniqCount}件`;

    let userForFavMatchCountData: {
      [key: string]: {
        totalCount: number,
        favCount: number,
        // mihariCount: number,
      }
    } = {};

    const favCountPromises = subscriptions.map(async (sub: any) => {
      let user = sub.user;
      let favCount = await prisma.tllUsersUserLambdaRecordsFavMap.count({
        where: {
          userId: user.id,
          userLambdaRecordId: {
            in: uniqRecordIds,
          },
        },
      });

      // let totalCountForEachNeed = sum(Object.values(todayCountForEachNeed.data));

      userForFavMatchCountData[user.id] = {
        totalCount: uniqCount,
        favCount: favCount,
        // mihariCount: totalCountForEachNeed,
      };
      return favCount;
    });
    await Promise.all(favCountPromises);


    console.log("🔥 userForFavMatchCountData", userForFavMatchCountData);

    subscriptions.forEach((sub: any) => {
      if (userForFavMatchCountData[sub.userId]?.favCount > 0) {
        dataToPush.push({
          title: titleBase,
          message: `気に入った物件変更: ${userForFavMatchCountData[sub.userId]?.favCount}件`,
          subscription: sub.subscription,
        });
      }
    });

    let res = await sendNotificationsToAll({
      url: "https://www.urbalytics.jp/ex/fav",
      subAndInfos: dataToPush
    }) as ActionResponse<any>;

    sendLark({
      message: `[PUSH][${titleBase}][${res.data.filter((result: any) => result.status === 'fulfilled' && result.value.success).length} / ${res.data.length}] のプッシュ通知が送信されました`,
      url: LARK_URLS.USER_ACTIVITY_CHANNEL,
    });

    if (res.success) {
      return NextResponse.json({ success: true, res: res.data });
    } else {
      return NextResponse.json({ success: false, res: res.data });
    }
  } catch (error) {
    console.error("🚨 error", error);
    sendLark({
      message: `🚨[PUSH][User][${dayjsWithTz().format('YYYY-MM-DD')}][エラー]プッシュ通知が送信されました`,
      url: LARK_URLS.USER_ACTIVITY_CHANNEL,
    });
    return NextResponse.json({ success: false, message: error instanceof Error ? error.message : '未知错误' }); // 使用日本語
  } finally {
    await prisma.$disconnect(); // prevent long hanging connection
  }
}
