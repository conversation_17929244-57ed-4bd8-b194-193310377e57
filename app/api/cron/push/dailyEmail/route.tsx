import { NextResponse } from "next/server";
import { DailyEmail } from "@/app/pub/emails/dailyEmail";
import { render } from "@react-email/components";
import { Resend } from "resend";
import dayjs from "dayjs";
import { getRecommendationPriceDown, getRecommendation<PERSON>ighYield, padWithLink } from "@/actions/dashboard";
import { prisma } from "@/lib/prisma";
import { logger } from "@/lib/logger";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { TllUserProps } from "@/lib/definitions/tllUser";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { uniqBy } from "lodash-es";

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({ success: false, message: "Unauthorized" });
  }

  const url = new URL(req.url); // 创建 URL 对象
  const isDebug = url.searchParams.get("debug"); // 获取查询参数

  let mailingUserList = [];

  try {
    if (isDebug === "1") {
      mailingUserList = [{
        email: "<EMAIL>",
        id: "1",
      }];
    } else {
      let allUsers = await prisma.tllUser.findMany({
        where: {
          accessLevel: {
            gte: 30
          }
        },
      }) as TllUserProps[];

      mailingUserList = allUsers.filter((user) => user.email && user.userSetting?.dailyEmailNotification);
    }

    let dataPriceDown = await getRecommendationPriceDown();
    let dataHighYield = await getRecommendationHighYield();

    let paddedDataPriceDown = await padWithLink({ records: dataPriceDown.data });
    let paddedDataHighYield = await padWithLink({ records: dataHighYield.data });

    const resend = new Resend(process.env.RESEND_API_KEY);

    // await resend.emails.send({
    //   from: '<EMAIL>',
    //   to: '<EMAIL>',
    //   subject: 'Hello World',
    //   html: '<p>Congrats on sending your <strong>first email</strong>!</p>'
    // });

    for (let m of mailingUserList.filter((user) => user.email !== null)) {
      /// FIX: getting records for fav watched // 
      let todayUserLambdaRecordsAll = await prisma.tllUserLambdaRecordPriceChange.findMany({
        where: {
          recordDate: dayjs().startOf('day').toDate(),
        },
      });
      let uniqRecordIds = uniqBy(todayUserLambdaRecordsAll, 'recordId')?.map((record: any) => record.recordId);
      let changedFavRecords = await prisma.tllUsersUserLambdaRecordsFavMap.findMany({
        where: {
          userId: m.id,
          userLambdaRecordId: {
            in: uniqRecordIds,
          },
        },
      });

      logger.info(`🔥 changedFavRecords`, changedFavRecords.length);
      let changedFavRecordIds = changedFavRecords.map((record) => record.userLambdaRecordId);
      let changedFavRecord = await prisma.tllUserLambdaRecord.findMany({
        where: {
          id: { in: changedFavRecordIds as string[] },
        },
        include: {
          priceChanges: true
        },
      });

      const html = await render(
        <DailyEmail dataPriceDown={paddedDataPriceDown.data} dataHighYield={paddedDataHighYield.data} changedFavRecord={changedFavRecord} />
      )

      resend.emails.send({
        from: 'Urbalytics <<EMAIL>>',
        to: m.email as string,
        replyTo: '<EMAIL>',
        subject: `${dayjs().format("YYYY-MM-DD")} 新規おすすめ物件${dataPriceDown.data.length + dataHighYield.data.length + changedFavRecord.length}件`,
        html: html,
      })

      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    sendLark({
      message: `Sending daily email to ${mailingUserList.length} users`,
      url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL
    });

    return NextResponse.json({ success: true, message: "Success" });
  } catch (error) {
    console.error("🔥 error", error);

    sendLark({
      message: `Error sending daily email: ${error}`,
      url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL
    });

    return NextResponse.json({ success: false, message: "Error" });
  }
}