
import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import webpush from "@/lib/thirdParty/webpush";
import { prisma } from "@/lib/prisma";
import { ActionResponse, CustomerNeedProps } from "@/lib/definitions";
import { sendNotificationsToAll } from "@/actions/tllUserPushSubscriptions";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { sum, uniqBy } from "lodash-es";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import { getRecordMatchSimpleForNeedIdsAction } from "@/actions/customerNeeds";

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({ success: false, message: "Unauthorized" });
  }

  try {
    let userWithCustomers = await prisma.tllUser.findMany({
      where: {
        accessLevel: {
          gte: 30 // 只选择访问级别大于等于90的用户
        }
      },
      include: {
        customers: true
      }
    });

    logger.info("🔥 userWithCustomers", userWithCustomers);

    const userIds = userWithCustomers
      .filter((user: any) => user && user.customers && user.customers.length > 0)
      .map((user: any) => user.id);
    console.log("🔥 用户ID列表:", userIds);

    let subscriptions = await prisma.tllUserPushSubscription.findMany({
      where: {
        userId: {
          in: userIds
        }
      },
      include: {
        user: {
          include: {
            customers: {
              include: {
                needs: true
              }
            }
          }
        }
      }
    });

    let dataToPush: { title: string, message: string, subscription: any }[] = [];

    let titleBase = `[${dayjsWithTz().format('YYYY-MM-DD')}]顧客ニーズマーチング`;


    // Key is the tll user id (user that you will push to)
    let userForMatchCountData: {
      [key: string]: {
        matchCount: number,
        needDescription: string,
      }
    } = {};

    // 使用并发 Promise
    const matchCountPromises = subscriptions.map(async (sub: any) => {
      const user = sub.user;
      const needIds = user?.customers?.map((customer: any) => customer.needs.map((need: any) => need.id)).flat();
      const res = await getRecordMatchSimpleForNeedIdsAction({ needIds: needIds as string[] }) as any;

      // Key is the need id 
      let needAndMatchCount = res.data as {
        [key: string]: {
          matchCount: number,
          need: CustomerNeedProps,
        }
      };

      let uniqueCustomerIds = [] as string[];
      for (const entries of Object.values(needAndMatchCount)) {
        if (entries.need.customerUserId && !uniqueCustomerIds.includes(entries.need.customerUserId)) {
          uniqueCustomerIds.push(entries.need.customerUserId);
        }
      }

      let matchCountForUniqueCustomer = uniqueCustomerIds.map((customerId: string) => {
        return {
          customerName: Object.values(needAndMatchCount).find((item: any) => item.need.customerUserId === customerId)?.need?.customer?.name,
          matchCount: sum(Object.values(needAndMatchCount).filter((item: any) => item.need.customerUserId === customerId).map((item: any) => item.matchCount)),
        }
      }).filter((item: any) => item.matchCount > 0);
      logger.info("🔥 matchCountForUniqueCustomer", matchCountForUniqueCustomer);

      userForMatchCountData[user.id] = {
        matchCount: sum(Object.values(needAndMatchCount).map((item: any) => item.matchCount)),
        needDescription: matchCountForUniqueCustomer.map((item: any) => `${item.customerName}:${item.matchCount}件`).join("| "),
      };
    });

    await Promise.all(matchCountPromises);

    logger.info("🔥 userForMatchCountData", userForMatchCountData);

    subscriptions.filter((sub: any) => userForMatchCountData[sub.userId]?.matchCount > 0).forEach((sub: any) => {
      dataToPush.push({
        title: titleBase + `${userForMatchCountData[sub.userId]?.matchCount}件`,
        message: userForMatchCountData[sub.userId]?.needDescription,
        subscription: sub.subscription,
      });
    });

    let res = await sendNotificationsToAll({
      url: "https://www.urbalytics.jp/pa/customer",
      subAndInfos: dataToPush
    }) as ActionResponse<any>;

    sendLark({
      message: `[PUSH][${titleBase}][${res.data.filter((result: any) => result.status === 'fulfilled').length} / ${res.data.length}] のプッシュ通知が送信されました`,
      url: LARK_URLS.USER_ACTIVITY_CHANNEL,
    });

    if (res.success) {
      return NextResponse.json({ success: true, res: res.data });
    } else {
      return NextResponse.json({ success: false, res: res.data });
    }
  } catch (error) {
    console.error("🚨 error", error);
    sendLark({
      message: `[🚨[PUSH][Customer Need Match][${dayjsWithTz().format('YYYY-MM-DD')}][エラー] のプッシュ通知が送信されました ${error instanceof Error ? error.message : '未知错误'}`,
      url: LARK_URLS.USER_ACTIVITY_CHANNEL,
    });
    return NextResponse.json({ success: false, message: error instanceof Error ? error.message : '未知错误' }); // 使用日本語
  } finally {
    await prisma.$disconnect(); // prevent long hanging connection
  }
}
