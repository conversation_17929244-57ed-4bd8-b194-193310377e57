// import { getChromeBrowser, getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
// import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";

// export async function GET(req: Request) {
//   if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
//     return new Response("Unauthorized", { status: 401 });
//   }

//   if (req.method !== "GET") {
//     return new Response("Method Not Allowed", { status: 405 });
//   }

//   let page, browser;

//   const { REINS_USERNAME, REINS_PASSWORD } = process.env;
//   if (!REINS_USERNAME || !REINS_PASSWORD) {
//     throw new Error("REINS_USERNAME or REINS_PASSWORD is not set");
//   }

//   try {
//     browser = await getChromeBrowser();
//     page = await getPageWithRandomUserAgent(browser);

//     // Step 1: go page
//     await page.goto("https://system.reins.jp/login/main/KG/GKG001200", {
//       waitUntil: "networkidle2",
//     });

//     await page.waitForSelector("#__BVID__13");
//     await page.type("#__BVID__13", REINS_USERNAME);
//     await page.type("#__BVID__16", REINS_PASSWORD);
//     await new Promise(resolve => setTimeout(resolve, 1000));

//     await page.click("#__BVID__20");
//     await new Promise(resolve => setTimeout(resolve, 1000));
//     console.log("🔥 Login form filled up ..");
//     await page.click(".p-3.large.btn-primary");
//     console.log("🔥 Logged in and page loaded ..");
//     await new Promise(resolve => setTimeout(resolve, 4000));

//     // *********************************************************
//     // Step 1.1:: Go to our property page
//     // *********************************************************
//     let ownPropertyList = ".btn.p-button.py-3";
//     await page.waitForSelector(ownPropertyList);
//     await page.click(ownPropertyList);
//     await new Promise(resolve => setTimeout(resolve, 4000));

//     // *********************************************************
//     // Step 2: For each item, update the thingy
//     // *********************************************************

//     // Get number of bukken
//     const RESULT_COUNT_SELECTOR = ".text-dark.ml-3";
//     await page.waitForSelector(RESULT_COUNT_SELECTOR);
//     const PAGINATION_COUNT = 50;
//     const totalResultCount = parseInt(
//       await page.evaluate(
//         (sel) =>
//           document?.querySelector(sel)?.textContent?.trim()?.split("／")[1]?.trim()?.replace("件", ""),
//         RESULT_COUNT_SELECTOR
//       ),
//       10
//     );
//     let pageCount = Math.ceil(totalResultCount / PAGINATION_COUNT);

//     console.log(
//       `🔥 Total result count is ${totalResultCount}, with ${pageCount} pages`
//     );

//     // FIXME: assuming only 1 page for now
//     for (let i = 1; i <= totalResultCount; i += 1) {
//       console.log(`🔥  refreshing ${i} / ${totalResultCount}🔥 for 更新🔥`);
//       // 1 Click on 詳細
//       const shosaiButton = `.p-table-body-row:nth-child(${i}) .p-table-body-item:nth-child(28)`;
//       await page.waitForSelector(shosaiButton);
//       await page.click(shosaiButton);

//       // 2 Click on 物件更新
//       const changeButton = ".p-frame-bottom .row div:nth-child(5) button";
//       await new Promise(resolve => setTimeout(resolve, 5000));
//       await page.waitForSelector(changeButton);
//       await page.click(changeButton);
//       await new Promise(resolve => setTimeout(resolve, 2000));
//       console.log("🔥  clicked 物件更新 🔥 🔥");

//       // 3 click 確認
//       const confirmButton = ".p-frame-bottom .row button";
//       await page.waitForSelector(confirmButton);
//       await page.click(confirmButton);
//       await new Promise(resolve => setTimeout(resolve, 2000));
//       console.log("🔥  clciked 確認🔥 🔥");

//       // 4 click 変更
//       const changeConfirmButton = ".p-frame-bottom .row button";
//       await page.waitForSelector(changeConfirmButton);
//       await page.click(changeConfirmButton);
//       await new Promise(resolve => setTimeout(resolve, 2000));

//       // 5 click 自社登録物件一覧へ
//       const returnHpPage = ".p-frame-bottom .row div:nth-child(2) button";
//       await page.waitForSelector(returnHpPage);
//       await page.click(returnHpPage);
//       await new Promise(resolve => setTimeout(resolve, 4000));

//       // Redo this same thing but for 変更
//       const henkoubutton = ".p-frame-bottom .row div:nth-child(8) button";
//       console.log(`🔥  refreshing ${i} / ${totalResultCount}🔥 for 変更🔥`);
//       await page.waitForSelector(shosaiButton);
//       await page.click(shosaiButton);
//       await new Promise(resolve => setTimeout(resolve, 5000));
//       await page.waitForSelector(henkoubutton);
//       await page.click(henkoubutton);
//       await new Promise(resolve => setTimeout(resolve, 2000));
//       console.log("🔥  clicked 物件変更 🔥 🔥");
//       await page.waitForSelector(confirmButton);
//       await page.click(confirmButton);
//       await new Promise(resolve => setTimeout(resolve, 2000));
//       await page.waitForSelector(changeConfirmButton);
//       await page.click(changeConfirmButton);
//       await new Promise(resolve => setTimeout(resolve, 2000));
//       await page.waitForSelector(returnHpPage);
//       await page.click(returnHpPage);
//       await new Promise(resolve => setTimeout(resolve, 4000));
//     }

//     sendLark("Reins 更新 and 変更 completed", LARK_URLS.ADMIN_ACTIVITY_CHANNEL);
//     // redirect: "follow", // manual, *follow, error
//     // referrerPolicy: "no-referrer", // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
//     // body: JSON.stringify(data), // 本文のデータ型は "Content-Type" ヘッダーと一致させる必要があります
//   } catch (err: any) {
//     console.log("🔥 err 🔥");
//     console.log(err, err.stack); // an error occurred

//     sendLark("Error Reins 更新 and 変更 completed", LARK_URLS.ADMIN_ACTIVITY_CHANNEL);
//   } finally {
//     if (page) {
//       await page.close();
//     }

//     if (browser) {
//       await browser.disconnect();
//       await browser.close();
//     }

//     return;
//   }
// };
