import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { prisma } from "@/lib/prisma";
import { logger } from "@/lib/logger";

export async function getDataAndSave(page: any): Promise<any> {
  try {
    // Get number of bukken
    const RESULT_COUNT_SELECTOR = ".text-dark.ml-3";
    await page.waitForSelector(RESULT_COUNT_SELECTOR);
    const PAGINATION_COUNT = 50;
    const totalResultCount = parseInt(
      await page.evaluate(
        (sel: string) =>
          document
            .querySelector(sel)
            ?.textContent?.trim()
            .split("／")[1]
            .trim()
            .replace("件", ""),
        RESULT_COUNT_SELECTOR
      ) || "0",
      10
    );
    let pageCount = Math.ceil(totalResultCount / PAGINATION_COUNT);

    logger.info(
      `🔥 Total result count is ${totalResultCount}, with ${pageCount} pages`
    );

    let ROW_SELECTOR = ".p-table-body-row";
    let DATA_SELECTOR = {
      reinsId: ".p-table-body-item:nth-child(2)",

      listingTitle: ".p-table-body-item:nth-child(9)",
      listingSubtype: ".p-table-body-item:nth-child(7)",
      listingType: ".p-table-body-item:nth-child(3)", // e.g. 売り物件 etc

      address: ".p-table-body-item:nth-child(5)",
      roomNumber: ".p-table-body-item:nth-child(10)",
      price: ".p-table-body-item:nth-child(14)", // 12,600.00万円

      registrationDate: ".p-table-body-item:nth-child(21)", // 令和 6年10月 5日
      changeDate: ".p-table-body-item:nth-child(22)",
      downloadDetailsCount: ".p-table-body-item:nth-child(25)",
      checkDetailsCount: ".p-table-body-item:nth-child(19)", //  15件／11件／-／26件, yday, yydya, 3 day, all time
    };

    let results = [];

    // FIXME: assuming only 1 page for now
    for (let i = 1; i <= totalResultCount; i += 1) {
      logger.info(`🔥  getting data for  ${i} / ${totalResultCount}🔥 🔥`);

      let currentRecord = {} as any;
      for (const field of Object.keys(DATA_SELECTOR)) {
        const selector = `${ROW_SELECTOR}:nth-child(${i}) ${DATA_SELECTOR[field as keyof typeof DATA_SELECTOR]}`;

        logger.info("🔥 selector🔥", selector);

        let currentValue = "" as any;

        currentValue = await page.evaluate(
          (sel: string, field: string) => {
            const res = document.querySelector(sel);

            if (res !== null) {
              if (field === "price") {
                return parseFloat(
                  res.textContent?.replace("万円", "").replace(/,/g, "") || "0"
                );
              }

              if (
                field === "checkDetailsCount" ||
                field === "downloadDetailsCount"
              ) {
                return parseInt(
                  res.textContent?.split("／")[0] === "-"
                    ? "0"
                    : res.textContent?.split("／")[0].replace("件", "") || "0"
                );
              }

              if (field === "reinsId") {
                return parseInt(res.textContent || "0");
              }

              if (field === "registrationDate" || field === "changeDate") {
                let raw = res.textContent
                  ?.replace(/(\r\n\t|\n|\r\t|\s)/gm, "")
                  .replace("令和7年", "2025-")
                  .replace("月", "-")
                  .replace("日", "");

                if (raw === "-" || !raw) {
                  return raw;
                }

                return new Date(raw).toISOString();
              }

              return res.textContent?.replace(/(\r\n\t|\n|\r\t|\s)/gm, "") || "";
            }

            return res;
          },
          selector,
          field
        );

        currentRecord[field] = currentValue;
      }

      ["roomNumber", "changeDate"].forEach((field) => {
        if (currentRecord[field] === "-") {
          currentRecord[field] = null;
        }
      });

      results.push(currentRecord);
    }

    await prisma.tllReinsMetric.createMany({
      data: results.map((result) => ({
        ...result,
        recordDate: new Date(),
      })),
    });


    return results;
  } catch (err: any) {
    logger.error("🔥 err 🔥");
    logger.error(err, err.stack); // an error occurred

    await sendLark({
      message: `[REINS Metrics Error] ${JSON.stringify(err)}`,
      url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
    });

    return []
  }
};
