import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";
import { getDataAndSave } from "./getDataAndSave";
import { logger } from "@/lib/logger";
import { login } from "../../reins/utility/reinsLogin";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { prisma } from "@/lib/prisma";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }
  let page, browser;

  const { REINS_USERNAME, REINS_PASSWORD } = process.env;
  if (!REINS_USERNAME || !REINS_PASSWORD) {
    throw new Error("REINS_USERNAME or REINS_PASSWORD is not set");
  }

  try {
    browser = await getChromeBrowser();
    page = await getPageWithRandomUserAgent(browser);

    await login({ page, account: null });
    // *********************************************************
    // Step 1: For each item, get the data and save it
    // *********************************************************
    let ownPropertyList = ".btn.p-button.py-3";
    await page.waitForSelector(ownPropertyList);
    await page.click(ownPropertyList);
    await new Promise(resolve => setTimeout(resolve, 4000));


    let currentMinute = dayjsWithTz().minute();
    if (Math.floor(currentMinute / 30) !== 1) {
      const results = await getDataAndSave(page);
      sendLark({
        message: `[REINS REFRESH] ${results.length} metric results updated`,
        url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
      });

      sendLark({
        message: `[REINS REFRESH] not posting because not 0,30`,
        url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
      });

      return NextResponse.json({ message: "success" }, { status: 200 });
    }

    let isoDate = dayjsWithTz().format("d");
    if (!["1", "4", "6"].includes(isoDate)) {
      // *********************************************************
      // Step 2: For each item, update the thingy
      // *********************************************************
      sendLark({
        message: `[REINS REFRESH] not posting because not 1,4,6`,
        url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
      });
      return NextResponse.json({ message: "success" }, { status: 200 });
    }

    // *********************************************************
    // Step 2: For each item, update the thingy
    // *********************************************************
    // Get number of bukken
    // page = await getPageWithRandomUserAgent(browser);

    // await login({ page, account: null });

    const RESULT_COUNT_SELECTOR = ".text-dark.ml-3";
    await page.waitForSelector(RESULT_COUNT_SELECTOR);
    const PAGINATION_COUNT = 50;
    let totalResultCount = parseInt(
      await page.evaluate(
        (sel) =>
          document
            ?.querySelector(sel)
            ?.textContent?.trim()
            ?.split("／")[1]
            ?.trim()
            ?.replace("件", "") || "0",
        RESULT_COUNT_SELECTOR
      ),
      10
    );
    let pageCount = Math.ceil(totalResultCount / PAGINATION_COUNT);

    logger.debug(
      `🔥 Total result count is ${totalResultCount}, with ${pageCount} pages`
    );

    // FIXME: assuming only 1 page for now
    for (let i = 0; i < totalResultCount; i += 1) {
      // let newMapperToAdd = {};

      logger.debug(`🔥  refreshing ${i + 1} / ${totalResultCount}🔥 for 更新🔥`);
      // const titleSelector = `.p-table-body-row:nth-child(${totalResultCount}) .p-table-body-item:nth-child(5)`;
      // const idSelector = `.p-table-body-row:nth-child(${totalResultCount}) .p-table-body-item:nth-child(2)`;

      // newMapperToAdd.oldId = parseInt(
      //   await page.evaluate(
      //     (sel) => document.querySelector(sel).textContent.trim(),
      //     idSelector
      //   ),
      //   10
      // );

      // newMapperToAdd.title = await page.evaluate(
      //   (sel) => document.querySelector(sel).textContent.trim(),
      //   titleSelector
      // );

      // 1 Click on 詳細 for the last item
      const shosaiButton = `.p-table-body-row:nth-child(${totalResultCount}) .p-table-body-item:nth-child(28)`;
      await page.waitForSelector(shosaiButton);
      await page.click(shosaiButton);

      // 2 Click on コピー登録
      const changeButton = ".p-frame-bottom .row div:nth-child(4) button";
      await new Promise(resolve => setTimeout(resolve, 5000));
      await page.waitForSelector(changeButton);
      await page.click(changeButton);
      await new Promise(resolve => setTimeout(resolve, 2000));
      logger.debug("🔥  clicked コピー登録 🔥 🔥");

      // 3 click 確認
      const confirmButton = ".p-frame-bottom .row button.btn-primary";
      await page.waitForSelector(confirmButton);
      await page.click(confirmButton);
      await new Promise(resolve => setTimeout(resolve, 2000));
      logger.debug("🔥  clciked 確認🔥 🔥");

      // 3.1 click 確認 popup
      const confirmAgainButton = ".modal-footer .btn-primary";
      await page.waitForSelector(confirmAgainButton);
      await page.click(confirmAgainButton);
      await new Promise(resolve => setTimeout(resolve, 2000));
      logger.debug("🔥  clciked 確認 pop up🔥 🔥");

      // 4 click 登録
      const changeConfirmButton = ".p-frame-bottom .row button";
      await page.waitForSelector(changeConfirmButton);
      await page.click(changeConfirmButton);
      await new Promise(resolve => setTimeout(resolve, 2000));
      logger.debug("🔥  clciked 登録 🔥 🔥");

      // 4.1 click 確認 popup
      const changeConfirmPopUpButton = ".modal-footer .btn-primary";
      await page.waitForSelector(changeConfirmPopUpButton);
      await page.click(changeConfirmPopUpButton);
      await new Promise(resolve => setTimeout(resolve, 2000));
      logger.debug("🔥  clciked 登録 pop up🔥 🔥");

      // 5 click 自社登録物件一覧へ
      const returnHpPage = ".p-frame-bottom .row div:nth-child(2) button";
      await page.waitForSelector(returnHpPage);
      await page.click(returnHpPage);
      await new Promise(resolve => setTimeout(resolve, 4000));

      // last complete the mapper
      // newMapperToAdd.newId = parseInt(
      //   await page.evaluate(
      //     (sel) => document.querySelector(sel).textContent.trim(),
      //     ".p-table-body-row:nth-child(1) .p-table-body-item:nth-child(2)"
      //   ),
      //   10
      // );

      //////////////////////////////////////////////////////
      ////////////////// REMOVE LAST ONE /////////////////////////////
      // 1 Click on 詳細 for the last item
      const buttonForOldItem = `.p-table-body-row:nth-child(${totalResultCount + 1
        }) .p-table-body-item:nth-child(28)`;
      await page.waitForSelector(buttonForOldItem);
      await page.click(buttonForOldItem);

      let typeOfProperty = await page.evaluate(
        (sel) => document?.querySelector(sel)?.textContent?.trim(),
        `.p-table-body-row:nth-child(${totalResultCount + 1
        }) .p-table-body-item:nth-child(3)`
      );
      logger.debug("🔥 typeOfProperty🔥 🔥");
      logger.debug(typeOfProperty);

      // 2 Click on 削除登録
      const deleteButton = ".p-frame-bottom .row div:nth-child(6) button";
      await new Promise(resolve => setTimeout(resolve, 5000));
      await page.waitForSelector(deleteButton);
      await page.click(deleteButton);
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("🔥  clicked 削除登録 🔥 🔥");

      // 3 click 削除
      // 3.1 click 削除 popup
      if (typeOfProperty === "売物件") {
        // for 売り物件のみ
        // 20250104 new pop up to warn you that you should enter 成約 instead of 削除
        const delete2PopUpButton = ".modal-footer .btn-primary";
        await page.waitForSelector(delete2PopUpButton);
        await page.click(delete2PopUpButton);
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log("🔥  clciked 削除 pop up🔥 🔥");
      }

      const delete2Button = ".p-button.btn-secondary";
      await page.waitForSelector(delete2Button);
      await page.click(delete2Button);
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("🔥  clicked 削除🔥 🔥");

      const delete3PopUpButton = ".modal-footer .btn-primary";
      await page.waitForSelector(delete3PopUpButton);
      await page.click(delete3PopUpButton);
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log("🔥  clciked 実行する pop up🔥 🔥");

      // 4 click go back //
      await page.waitForSelector(returnHpPage);
      await page.click(returnHpPage);
      await new Promise(resolve => setTimeout(resolve, 4000));
    }

    totalResultCount = parseInt(
      await page.evaluate(
        (sel: string) =>
          document
            ?.querySelector(sel)
            ?.textContent?.trim()
            ?.split("／")[1]
            ?.trim()
            ?.replace("件", "") || "0",
        RESULT_COUNT_SELECTOR
      ),
      10
    );

    sendLark({
      message: `[REINS REFRESH] new items is completed, current total count is ${totalResultCount}`,
      url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
    });

    return NextResponse.json({ message: "success" }, { status: 200 });
  } catch (err: any) {
    sendLark({
      message: "Reins 更新 and 変更 error " + JSON.stringify(err),
      url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
    });

    return NextResponse.json({ message: "error" }, { status: 500 });
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.disconnect();
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
