import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz"


export async function GET(req: Request) {
  const url = new URL(req.url); // 创建 URL 对象
  const param = url.searchParams.get("param"); // 获取查询参数

  console.log("🔥 cron job executed 🔥");
  console.log("🔥 req.headers.get('Authorization') 🔥", req.headers.get('Authorization'));

  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  if (req.method !== "GET") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  // Simulated Task (Replace this with your real logic)
  const data = {
    message: "Hello from Vercel Cron!",
    param,
    defaultTimezone: dayjsWithTz.tz.guess(),
    timestamp: {
      "dayjsWithTz().startOf('day').format()": dayjsWithTz().startOf('day'),
      "dayjsWithTz(2025-02-12).tz('Asia/Tokyo').startOf('day').format()": dayjsWithTz("2025-02-12").tz('Asia/Tokyo').startOf('day').utc().format(),
      "dayjsWithTz.tz('Asia/Tokyo').startOf('day').utc().format()": dayjsWithTz().tz('Asia/Tokyo').startOf('day').utc().format(),
      "dayjsWithTz.utc().startOf('day').format()": dayjsWithTz.utc().startOf('day'),
      "dayjsWithTz().format()": dayjsWithTz().format(),
      "dayjsWithTz().tz('Asia/Tokyo').format()": dayjsWithTz().tz("Asia/Tokyo").format(),
      "dayjsWithTz().utc().tz('Asia/Tokyo').format()": dayjsWithTz().utc().tz('Asia/Tokyo').format()
    }
  }; // 使用当前时区时间

  // Return response
  return Response.json(data);
}