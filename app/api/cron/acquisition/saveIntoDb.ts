import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { getCompositeTitle } from "../reins/utility/getCompositeTitle";
import dayjs from "dayjs";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { TllUserLambdaRecordPriceChangeSourceType, TllUserLambdaRecordRecordType } from "@prisma/client";
import { fillNearestStationGroupIdWalkMinute } from "@/actions/geoRailwayStationGroups";
import { fillPrefectureAreaPostalCode } from "@/actions/geoPrefecture";
import { uploadImageToSupabasePublicFromUrl } from "@/actions/helper/supabase";
import { toFullWidthAddress } from "../reins/utility/getCompositeTitle";

export async function saveIntoDb(data: any) {
  try {
    let TOTAL_COUNT = data.length;
    let CREATE_COUNT = 0;
    let UPDATE_COUNT = 0;
    let SUPABSE_UPLOAD_COUNT = 0;

    let filtered = data.filter(
      (d: any) => d.type?.indexOf("区分") === -1
    );

    let index = 0;
    for (let d of filtered) {
      logger.info(`🔥 process ---  ${++index} / ${filtered.length} (${TOTAL_COUNT}) 🔥`);
      let recordType = "BUILDING";

      if (d.type.indexOf("戸建") > -1) {
        recordType = "HOUSE";
      } else if (d.type?.indexOf("土地") > -1) {
        recordType = "LAND";
      }

      let compositeTitle = getCompositeTitle(d, recordType as UserLambdaRecordType, "REINS");

      // Assuming that there is no MANSION 
      let filter = {
        landSize: d.landSize,
        nearestStation: d.nearestStation,
        recordType,
      } as any;

      if (d.buildingSize !== undefined) {
        filter["buildingSize"] = d.buildingSize;
      }

      let match = await prisma.tllUserLambdaRecord.findUnique({
        where: {
          compositeTitle_recordType: {
            compositeTitle: compositeTitle,
            recordType: recordType as TllUserLambdaRecordRecordType,
          },
        },
      });

      // ********* STEP 1.1 ******************
      // If no match found, create a new record
      if (!match) {
        // First be creating a matched item ...
        logger.info("[Case 1] no match, creating a matched record .. compositeTitle is " + compositeTitle + " recordType is " + recordType + "🔥");

        CREATE_COUNT++;
        let rec = {
          price: d.price,
          yearlyIncome: d.yearlyIncome,
          sourceData: d.sourceSite,
          compositeTitle: compositeTitle,
          address: toFullWidthAddress(d.address),

          recordType,
          recordSubType: d.type,
          transport: d.transport,
          nearestStation: d.nearestStation,
          nearestStationWalkMinute: d.nearestStationWalkMinute,

          landSize: d.landSize,
          landRight: d.type.indexOf("底地") > -1 ? "底地権" : (d.type.indexOf("借地") > -1 || d.salesComments?.indexOf("借地") > -1 || d.title?.indexOf("借地") > -1 || d.description?.indexOf("借地") > -1) ? "借地権" : "所有権",

          buildingSize: d.buildingSize,
          buildingMaterial: d.buildingMaterial,
          buildingBuiltYear: d.buildingBuiltYear,
          buildingLayout: d.layout,

          salesComments: `${d.salesComments || ""} - ${d.title}, ${d.description
            }`,
          recordValues: {},
        } as UserLambdaRecordProps;

        let newlyCreated = await prisma.tllUserLambdaRecord.create({
          data: rec as any
        });

        await prisma.tllUserLambdaRecordPriceChange.create({
          data: {
            recordId: newlyCreated.id.toString(),
            recordDate: dayjs().toDate(),
            price: d.price,
            yearlyIncome: d.yearlyIncome,
            source: `SITE_${d.sourceSite}`,
            status: `公開中`,
            chirashiLink: d.hpUrl,
            comments: `${d.title} - ${d.description}`, // fixme: this should be removed
          } as any
        });

        logger.info(`🔥  creating new data... id is ${newlyCreated.id}🔥 🔥`);

        await fillPrefectureAreaPostalCode(newlyCreated.id);
        await fillNearestStationGroupIdWalkMinute(newlyCreated.id);

        if (d.imageLinks && d.imageLinks.length > 0) {
          for (let path of d.imageLinks) {
            let res = await uploadImageToSupabasePublicFromUrl({ url: path, recordId: newlyCreated.id });
            SUPABSE_UPLOAD_COUNT++;
            logger.info(`🔥 Uploaded image to Supabase: from ${path} to ${res.data?.fullPath || ""}`);
          }
        }
      } else {
        // For サイト if price is the same then it will not be re-created
        // However sometimes the prices are inconsistent between various sites.. so that leads to multiple saving data
        // So we will not save if there is a P3M change records

        let priceChange = await prisma.tllUserLambdaRecordPriceChange.findMany({
          where: {
            recordDate: {
              gte: dayjs().add(-90, "days").toDate(),
            },
            recordId: match.id.toString(),
            status: `公開中`,
            source: `SITE_${d.sourceSite}` as TllUserLambdaRecordPriceChangeSourceType,
            price: d.price,
          },
        });

        if (priceChange.length > 0) {
          logger.info(`[Case 2] Got match, but there is a P3M price change(${priceChange.length}, ids are ${priceChange.map(p => p.id).join(", ")}), do nothing ... 🔥`);
        } else {
          logger.info(`[Case 3] Got match match id is ${match.id}, no P3M price change, saving change record and update status ... 🔥`);

          UPDATE_COUNT++;

          let newPriceChange = await prisma.tllUserLambdaRecordPriceChange.create({
            data: {
              recordId: match.id.toString(),
              recordDate: dayjs().toDate(),
              price: d.price,
              yearlyIncome: d.yearlyIncome,
              source: `SITE_${d.sourceSite}`,
              status: `公開中`,
              chirashiLink: d.hpUrl,
              comments: `${d.title} - ${d.description}`,
            } as any
          });

          await prisma.tllUserLambdaRecord.update({
            where: { id: match.id.toString() },
            data: {
              updatedAt: new Date(),
            },
          });

          logger.info(`🔥  creating new price change record... id is ${newPriceChange.id} 🔥`);

          if (d.imageLinks && d.imageLinks.length > 0) {
            for (let path of d.imageLinks) {
              let res = await uploadImageToSupabasePublicFromUrl({ url: path, recordId: match.id });
              SUPABSE_UPLOAD_COUNT++;
              logger.info(`🔥 Uploaded image to Supabase: from ${path} to ${res.data?.fullPath || ""}`);
            }
          }

          if (match.price !== d.price || (match.yearlyIncome !== d.yearlyIncome && d.yearlyIncome !== null)) {
            await prisma.tllUserLambdaRecord.update({
              where: { id: match.id.toString() },
              data: {
                price: d.price,
                ...(d.yearlyIncome && { yearlyIncome: d.yearlyIncome }),
                // FOR Acquisition, so WILL save updated 
              }
            });
          }
        }
      }
    }

    await sendLark({
      message: `[ACQUISITION][${data[0].sourceSite}]${JSON.stringify({
        TOTAL_COUNT,
        CREATE_COUNT,
        UPDATE_COUNT,
        SUPABSE_UPLOAD_COUNT,
      })}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return;
  } catch (error) {
    logger.error("🔥 Error in cron job 🔥", error);
    await sendLark({
      message: `[🔥Error saving data]` + error,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return;
  }
}
