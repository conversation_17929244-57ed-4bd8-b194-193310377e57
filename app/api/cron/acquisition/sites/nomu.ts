import { logger } from "@/lib/logger";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { saveIntoDb } from "../saveIntoDb";
import { writeFileSync } from "fs";
import dayjs from "dayjs";
import { getStationWalkMinuteFromAddress } from "@/lib/helper/geo";

export async function getNomuData(page: any) {
  try {
    // *********************************************************
    // Step 1: go page
    // Tokyo, all investment types except 区分店舗
    await page.goto(
      "https://www.nomu.com/pro/search/?area_ids[]=1311&area_ids[]=1312&area_ids[]=1313&area_ids[]=1314&area_ids[]=1315&area_ids[]=1316&area_ids[]=1411&area_ids[]=1412&area_ids[]=1414&area_ids[]=1413&area_ids[]=1111&area_ids[]=1112&area_ids[]=1211&area_ids[]=1212&type_ids[]=2&type_ids[]=4&type_ids[]=3&type_ids[]=7&type_ids[]=5&type_ids[]=8&type_ids[]=9&type_ids[]=10&type_ids[]=11&new_period=3&sp=new_1day_all&order=0&rosen_tab_id=-1&bukken_class=build", // 3 day chagne
      { waitUntil: "domcontentloaded" }
    );

    // FIXME* somehow below will hang

    // await new Promise(resolve => setTimeout(resolve, 3000));

    const COUNT_SELECTOR = ".c_bldg_box__horizontal_l";
    await page.waitForSelector(".hit_num .u_color__primary");

    // *********************************************************
    // Step 2: Reading data as needed

    // Step 2.1 Get total number of bukken
    const totalItemCount = await page.evaluate((sel: any) => {
      const res = document.querySelectorAll(sel).length;
      return res;
    }, COUNT_SELECTOR);
    logger.info(`🔥 Total item count is ${totalItemCount}..`);

    // Step 3.2 Reading details
    const results = [];
    const newDataSelectors = {
      type: ".tag__category",
      title: ".c_bldg_box__head .name",
      description: ".point dd",

      hpUrl: "> a",
      price: ".price",
      roi: ".yield .num", // convert to yearlyIncome

      address: ".address dd",
      transport: ".transport dd",

      landSize: ".dim .info:nth-child(2) dd",
      layout: ".dim .info:nth-child(3) dd",

      buildingSize: ".dim .info:nth-child(1) dd",
      buildingBuiltYear: ".dim .info:nth-child(4) dd",
      buildingMaterial: ".dim .info:nth-child(5) dd",
      buildingLevel: ".dim .info:nth-child(6) dd",

      imageLinks: ".pic img",
    } as any;

    for (let i = 0; i < totalItemCount; i++) {
      logger.info(`🔥 Getting data for the ${i + 1}th result 🔥`);

      let currentResult = {} as any;

      for (const field of Object.keys(newDataSelectors)) {
        const selector = `${COUNT_SELECTOR}:nth-child(${i + 1}) ${newDataSelectors[field]
          }`;

        const newField = await page.evaluate(
          (sel: any, field: any) => {

            if (field === "imageLinks") {
              // convert https://image.nomu.com/f=webp:auto,w=450,q=95/imageflux/images/04/R61/0684/R6150684_0101_24.jpg?202311131745
              // to https://image.nomu.com/f=webp:auto,q=95/imageflux/images/04/R61/0684/R6150684_0101_25.jpg?202311131745
              return {
                [`${field}`]: Array.from(document?.querySelectorAll(sel))
                  .filter((img: any) => img.getAttribute('src') !== null)
                  .map((img: any) => img.getAttribute('src').replace("w=450,", ""))
                  .filter((src) => !!src && src.startsWith('http'))
              };
            }


            const res = document.querySelector(sel);
            if (res !== null && res !== undefined) {
              if (field === "hpUrl") {
                return {
                  [`${field}`]: res.getAttribute("href"),
                };
              }

              if (
                (field === "landSize" || field === "buildingSize") &&
                (res?.textContent?.indexOf("m²") !== -1 || res?.textContent?.indexOf("m2") !== -1)
              ) {
                return {
                  [`${field}`]: res?.textContent?.indexOf("m²") !== -1 ? parseFloat(res?.textContent?.split("m²")[0] || "0") : parseFloat(res?.textContent?.split("m2")[0] || "0"),
                };
              }

              return {
                [`${field}`]: res?.textContent?.replace(
                  /(\r\n\t|\n|\r\t|\s)/gm,
                  ""
                ) || "",
              };
            }

            return null;
          },
          selector,
          field
        );

        currentResult = {
          ...newField,
          ...currentResult,
        };
      }

      currentResult["sourceSite"] = "NOMU";

      if (currentResult["price"] !== undefined) {
        if (currentResult["price"].indexOf("億円") !== -1) {
          currentResult["price"] =
            parseInt(currentResult["price"].split("億円")[0], 10) * 10000;
        } else if (currentResult["price"].indexOf("億") !== -1) {
          const okuCount = currentResult["price"].split("億")[0];
          const manCount = currentResult["price"].split("億")[1].replace(",", "");
          currentResult["price"] =
            parseInt(okuCount, 10) * 10000 + parseInt(manCount, 10);
        } else {
          currentResult["price"] = parseInt(
            currentResult["price"].replace(",", ""),
            10
          );
        }

        if (currentResult["roi"] !== undefined && !isNaN(parseFloat(currentResult["roi"]))) {
          currentResult["yearlyIncome"] = parseFloat(((currentResult["price"] * parseFloat(currentResult["roi"])) / 100).toFixed(0));
        }
      }

      if (currentResult["transport"] !== undefined) {
        currentResult["nearestStation"] = currentResult["transport"]
          .split("「")[1]
          .split("」")[0];

        currentResult["nearestStationWalkMinute"] = await getStationWalkMinuteFromAddress(currentResult["address"]);
      }

      if (currentResult["buildingBuiltYear"] !== undefined) {
        currentResult["buildingBuiltYear"] = parseInt(
          currentResult["buildingBuiltYear"].split("年")[0]
        );
      }

      logger.info("🔥 now processing results ...🔥");

      logger.info("🔥 currentResult🔥", currentResult);
      results.push(currentResult);
    }
    logger.info("🔥 Complete reading results..., length is " + results.length);

    // *********************************************************
    // Step 3: Saving data
    // *********************************************************

    // writeFileSync(`nomu-${dayjs().format("YYYYMMDDHHmmss")}.json`, JSON.stringify(results, null, 2));
    await saveIntoDb(results);

    return results;
  } catch (err: any) {
    await sendLark({
      message: `[Nomu][🔥 Error getting data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    throw err;
  }
};
