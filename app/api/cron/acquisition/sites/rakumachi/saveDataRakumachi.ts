import { logger } from "@/lib/logger";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import dayjs from "dayjs";
import { uploadImageToSupabasePublicFromUrl } from "@/actions/helper/supabase";
import { fillPrefectureAreaPostalCode } from "@/actions/geoPrefecture";
import { buildingRentColumns } from "@/app/(cp)/an/mansion/[id]/mansionRentColumns";
import { TllUserLambdaRecordPriceChangeSourceType } from "@/lib/definitions";
import { toFullWidthAddress } from "../../../reins/utility/getCompositeTitle";

function parseNameToFullWidth(str: string): string {
  str = str.replace("(", "（").replace(")", "）");

  return str.replace(/[A-Za-z0-9]/g, (s) =>
    String.fromCharCode(s.charCodeAt(0) + 0xfee0)
  );
}

async function getCompanyId({ rec }: { rec: any }) {
  if (rec.realEstateName === undefined) {
    logger.info(`🔥[RAKUMACHI][PriceChange] rec.realEstateName is undefined, do nothing 🔥`);
    return null;
  }

  const rows = await prisma.proCompany.findMany({
    where: {
      fullName: parseNameToFullWidth(rec.realEstateName),
    },
  });

  return rows.length ? rows[0].id : null;
}

async function getCompanyIdAndSaveChangeRecords({ matchUserLambdaRecordId, rec }: { matchUserLambdaRecordId: string, rec: any }) {
  let companyId = await getCompanyId({ rec });

  if (!companyId && rec.realEstateName !== undefined) {
    const parsedname = parseNameToFullWidth(rec.realEstateName);

    const result = await prisma.proCompany.create({
      data: {
        fullName: parsedname,
        contactNumber: rec.realEstateNumber || null,
      },
    });
    companyId = result.id;
  }

  const result = await prisma.tllUserLambdaRecordPriceChange.create({
    data: {
      recordId: matchUserLambdaRecordId,
      companyId: companyId,
      recordDate: dayjs().toDate(),
      price: rec.price,
      source: TllUserLambdaRecordPriceChangeSourceType.RAKUMACHI,
      status: "公開中",
      comments: `[${rec.propertyTitle}] ${rec.fullAddress} ${rec.propertyDescription || ""} [Company at ${rec.realEstateName || ""}]`,
      chirashiLink: rec.link,
    }
  });

  await prisma.tllUserLambdaRecord.update({
    where: { id: matchUserLambdaRecordId },
    data: {
      updatedAt: new Date(),
    },
  });

  logger.info("🔥 [RAKUMACHI][PriceChange] change created new, id is", result.id);
}

export async function saveDataRakumachi(allData: any[]): Promise<{ createdRecordsCount: number, updatedRecordsCount: number, savedSupabaseFileCount: number }> {
  let createdRecordsCount = 0;
  let savedSupabaseFileCount = 0;
  let updatedRecordsCount = 0;

  let overallIndex = 0;

  const getStationGroupId = async (station: string) => {
    const match = await prisma.geoRailwayStation.findMany({
      where: {
        name: station,
        prefectureCode: { in: [11, 12, 13, 14] },
      },
    });
    return match.length > 0 ? match[0].stationGroupId : null;
  };

  const processBatch = async (batch: any[]) => {
    for (const data of batch) {
      const { compositeTitle } = data;
      console.log(`--- 🔥 processing ${compositeTitle} at ${overallIndex++}/${allData.length} 🔥 ----- `);
      const matchedRecord = await prisma.tllUserLambdaRecord.findUnique({
        where: {
          compositeTitle_recordType: {
            compositeTitle: compositeTitle,
            recordType: data.recordType || "BUILDING"
          },
        },
      });

      if (!matchedRecord) {
        logger.info(`-------- [Case 1][New] No value for ${compositeTitle}, creating new record...`);
        const valueToCreate = {
          sourceData: "RAKUMACHI",
          compositeTitle: compositeTitle,
          price: data.price,
          yearlyIncome: data.yearlyIncome,
          transport: data.transport || null,
          nearestStation: data.nearestStation || null,
          nearestStationWalkMinute: data.nearestStationWalkMinute,
          nearestStationGroupId: data.nearestStation ? await getStationGroupId(data.nearestStation) : null,
          recordType: data.recordType || "BUILDING",
          recordSubType: data.propertySubType,
          address: toFullWidthAddress(data.address),
          recordValues: data,
          landRight: "所有権",
          landSize: data.landSize,
          salesComments: `[${data.propertyTitle}] ${data.fullAddress} ${data.propertyDescription || ""} - ${data.realEstateName || ""}`,
          buildingSize: data.buildingSize,
          buildingLayout: data.buildingLayout,
          buildingLevel: data.buildingLevel,
          buildingBuiltYear: data.buildingBuiltYear,
          buildingRoomCount: data.buildingRoomCount,
          buildingMaterial: data.buildingMaterial,
        } as any;

        console.log("🔥 valueToCreate", valueToCreate);

        const result = await prisma.tllUserLambdaRecord.create({ data: valueToCreate });
        await getCompanyIdAndSaveChangeRecords({ matchUserLambdaRecordId: result.id, rec: data });
        logger.debug("🔥 created new record, id is", result.id);
        await fillPrefectureAreaPostalCode(result.id);
        createdRecordsCount += 1;

        for (let path of data.allImages) {
          let res = await uploadImageToSupabasePublicFromUrl({ url: path, recordId: result.id });
          logger.info(`🔥 Uploaded image to Supabase: from ${path} to ${res.data?.fullPath || ""}`);
          savedSupabaseFileCount += 1;
        }
      } else {
        logger.info("🔥 [RAKUMACHI PriceChange][Case 2 & 3][Existing] record found, id is", matchedRecord.id);
        const priceChanges = await prisma.tllUserLambdaRecordPriceChange.findMany({
          where: {
            recordDate: {
              gte: dayjs().add(-90, "days").toDate(),
            },
            recordId: matchedRecord.id,
            status: "公開中",
            price: data.price,
            companyId: await getCompanyId({ rec: data }),
          },
        });

        if (priceChanges.length > 0) {
          logger.info(`-------- [Case 2] Got match, but there is a P3M price change(${priceChanges.length}, ids are ${priceChanges.map(p => p.id).join(", ")}), thus do nothing ... 🔥`);
        } else {
          logger.info("-------- [Case 3] Got match, no P3M price change, saving change record and update status ... 🔥");
          await getCompanyIdAndSaveChangeRecords({ matchUserLambdaRecordId: matchedRecord.id, rec: data });

          let dataToUpdate: any = {};
          ["buildingLayout", "nearestStation", "nearestStationWalkMinute", "landSize", "buildingSize", "buildingBuiltYear", "transport"].forEach(key => {
            if (data[key] !== undefined && matchedRecord[key as keyof typeof matchedRecord] === null) {
              dataToUpdate[key] = data[key];
            }
          });

          if (Object.keys(dataToUpdate).length > 0 || data.price !== matchedRecord.price) {
            let dataToUpdateWithStatus = { price: data.price, ...dataToUpdate };
            logger.info(`🔥 [RAKUMACHI][PriceChange] updating record with ${Object.keys(dataToUpdateWithStatus).length} keys to update, data is ${JSON.stringify(dataToUpdateWithStatus)}`);
            await prisma.tllUserLambdaRecord.update({
              where: { id: matchedRecord.id },
              data: dataToUpdateWithStatus
            });
          }

          for (let path of data.allImages) {
            let res = await uploadImageToSupabasePublicFromUrl({ url: path, recordId: matchedRecord.id });
            logger.info(`🔥 Uploaded image to Supabase: from ${path} to ${res.data?.fullPath || ""}`);
            savedSupabaseFileCount += 1;
          }
          updatedRecordsCount += 1;
        }
      }
    }
  };

  const batchSize = 10;
  for (let i = 0; i < allData.length; i += batchSize) {
    const batch = allData.slice(i, i + batchSize);
    await processBatch(batch);
  }

  return { createdRecordsCount, updatedRecordsCount, savedSupabaseFileCount };
}
