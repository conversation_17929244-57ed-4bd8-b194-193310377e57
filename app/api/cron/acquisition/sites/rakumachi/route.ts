import { logger } from "@/lib/logger";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { writeFile } from "fs/promises";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import duration from "dayjs/plugin/duration";
dayjsWithTz.extend(duration);
import { getCompositeTitleForSuumo } from "../../../reins/utility/getCompositeTitle";
import { record } from "zod";
import dayjs from "@/lib/thirdParty/dayjsWithTz";
import { saveDataRakumachi } from "./saveDataRakumachi";

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  let browser = await getChromeBrowser();
  let page: any;

  async function autoScroll(page: any) {
    await page.evaluate(async () => {
      await new Promise((resolve) => {
        let totalHeight = 0;
        const distance = 200;
        const timer = setInterval(() => {
          window.scrollBy(0, distance);
          totalHeight += distance;

          if (totalHeight >= document.body.scrollHeight) {
            clearInterval(timer);
            resolve(void 0);
          }
        }, 100);
      });
    });
  }

  const retryTaskIfFail = async (task: () => Promise<any[]>, retries = 2) => {
    let pass = false;
    for (let i = 0; i < retries; i++) {
      if (pass) {
        break;
      }

      try {
        let res = await task();
        pass = true;
        return res;
      } catch (err) {
        logger.error("🔥 Error in cron job.. retrying once 🔥", err);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    return [];
  };

  try {
    page = await getPageWithRandomUserAgent(browser);

    let currentMinute = dayjsWithTz().minute();

    const shutoken_3d_url = "https://www.rakumachi.jp/syuuekibukken/area/prefecture/dimAll/?sort=property_updated_at&sort_type=desc&area[]=11&area[]=12&area[]=14&area[]=13&newly=3&price_from=&price_to=&gross_from=&gross_to=&dim[]=1001&dim[]=1002&dim[]=1003&dim[]=1004&dim[]=1005&year_from=&year_to=&b_area_from=&b_area_to=&houses_ge=&houses_le=&min=&l_area_from=&l_area_to=&keyword="

    await page.goto(shutoken_3d_url);

    const TOTAL_COUNT_SELECTOR = ".searchResult .PropertiesCount strong";

    await page.waitForSelector(TOTAL_COUNT_SELECTOR);

    const TOTAL_COUNT = await page.evaluate((sel: any) => {
      const pageCount = document.querySelector(sel)?.textContent;
      return pageCount ? parseInt(pageCount.replace(/,/g, "")) : 0;
    }, TOTAL_COUNT_SELECTOR);

    logger.info("🔥 TOTAL_COUNT", TOTAL_COUNT);

    const UNIT_SELECTOR = ".propertyBlock";

    let PAGE_COUNT = Math.ceil(TOTAL_COUNT / 20);

    let allData = [];

    const fieldSelector = {
      propertyTitle: ".propertyBlock__name",
      propertyDescription: ".ut-notification--point",
      propertySubType: ".propertyBlock__dimension",
      propertyDetails: ".propertyBlock__contents",

      imageLink: ".propertyBlock__photo img",
      realEstateName: ".propertyBlock__realtorName"
    }

    const dataInDetails = {
      price: "価格",
      yield: "利回り",
      fullAddress: "所在地",
      transport: "交通",
      buildingBuiltTime: "築年月",
      buildingRoomCount: "総戸数",
      buildingMaterial: "建物構造",
      landAndBuildingSize: "面積",
      buildingLevel: "階数",
    };

    const extractField = (label: string, rawText: string) => {
      const regex = new RegExp(`${label}\\s*([\\s\\S]*?)(?=\\n\\s*\\S|$)`, "i");
      const match = rawText?.match(regex);
      return match ? match[1].trim().replace(/\s+/g, " ") : null;
    };

    await new Promise(resolve => setTimeout(resolve, 2000));


    // PAGE COUNT
    for (let currentPage = 0; currentPage < PAGE_COUNT; currentPage++) {
      await autoScroll(page);
      console.log(" ----- 🔥 current page at ", currentPage, "of", PAGE_COUNT, " -----");
      allData = [];

      let startTime = dayjsWithTz(); // Job start at 15,30,45,00
      const CURRENT_PAGE_COUNT = await page.evaluate((sel: any) => {
        return document.querySelectorAll(sel)?.length;
      }, UNIT_SELECTOR);
      logger.info("🔥 CURRENT_PAGE_COUNT🔥", CURRENT_PAGE_COUNT);

      // scroll to the bottom of the page for lazy koading
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });

      await new Promise(resolve => setTimeout(resolve, 2000));

      // IMPORTANT: remove ads
      // This will just remvoe data.. so we can filter it out 
      await page.evaluate((sel: any) => {
        const elements = document.querySelectorAll(sel);
        for (let i = 0; i < elements.length; i++) {
          elements[i]?.parentNode?.removeChild(elements[i]);
        }
      }, ".propertyBlock__contentAd");

      await page.evaluate((sel: any) => {
        const elements = document.querySelectorAll(sel);
        for (let i = 0; i < elements.length; i++) {
          elements[i]?.parentNode?.removeChild(elements[i]);
        }
      }, ".box_function");

      // await new Promise(resolve => setTimeout(resolve, 5000));
      let blocks = await page.$$(UNIT_SELECTOR);
      for (let block of blocks) {

        let currentData = {} as any;

        ["propertyTitle", "propertyDescription", "realEstateName", "propertySubType"].forEach(async (key: any) => {
          currentData[key] = await block.evaluate((el: any, sel: any) => {
            return el.querySelector(sel)?.textContent.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
          }, fieldSelector[key as keyof typeof fieldSelector]) as string;
        });


        // currentData.link = "https://suumo.jp" + await page.evaluate((sel: any) => {
        //   return document.querySelector(sel)?.getAttribute("href");
        // }, `${UNIT_SELECTOR}:nth-child(${i + 1}) ${suumoSelector.link}`) as string;

        currentData.detailsRawText = await block.evaluate((el: any, sel: any) => {
          return el.querySelector(sel)?.textContent;
        }, fieldSelector.propertyDetails) as string;


        currentData.link = await block.evaluate((el: any, sel: any) => {
          return "https://www.rakumachi.jp" + el.querySelector(sel)?.getAttribute("href");
        }, ".propertyBlock__mainArea > a") as string;

        Object.keys(dataInDetails).forEach((key: any) => {
          currentData[key] = extractField(dataInDetails[key as keyof typeof dataInDetails], currentData.detailsRawText);
        });

        if (!currentData.price) {
          continue;
        }

        function parseJapanesePrice(priceStr: string): number {
          // 只取“・”或“、”前的部分
          const trimmed = priceStr.split(/[・、]/)[0];

          const 億Match = trimmed.match(/([0-9.]+)億/);
          const 万Match = trimmed.match(/([0-9.]+)万円/);

          const 億 = 億Match ? parseFloat(億Match[1]) * 10000 : 0;
          const 万 = 万Match ? parseFloat(万Match[1]) : 0;

          return 億 + 万;
        }

        currentData.price = parseJapanesePrice(currentData.price);

        const buildingMatch = currentData.landAndBuildingSize.match(/建物\s*([\d.]+)㎡/);
        const landMatch = currentData.landAndBuildingSize.match(/土地\s*([\d.]+)㎡/);

        currentData.landSize = landMatch ? parseFloat(landMatch[1]) : null
        currentData.buildingSize = buildingMatch ? parseFloat(buildingMatch[1]) : null;
        currentData.recordType = currentData.propertySubType === "戸建賃貸" ? "HOUSE" : "BUILDING";

        const stationMatch =
          currentData.transport.match(/「(.+?)」駅/) ||     // 优先匹配括号站名
          currentData.transport.match(/([^\s「」]+?)駅/);   // 次选：非括号形式

        const walkMatch = currentData.transport.match(/徒歩(\d+)分/);
        currentData.nearestStation = stationMatch ? stationMatch[1] : null;
        currentData.nearestStationWalkMinute = walkMatch ? parseInt(walkMatch[1], 10) : null;

        currentData.buildingBuiltYear = currentData.buildingBuiltTime.indexOf("年") > -1 ? parseInt(currentData.buildingBuiltTime.split("年")[0]) : null;

        function normalizeChomeEnding(address: string): string {
          const kanjiToNumber: { [key: string]: number } = {
            "一": 1, "二": 2, "三": 3, "四": 4, "五": 5,
            "六": 6, "七": 7, "八": 8, "九": 9, "十": 10
          };

          const numberToFullWidth = (num: number): string =>
            num.toString().replace(/[0-9]/g, d => String.fromCharCode(d.charCodeAt(0) + 0xFEE0));

          // 漢数字丁目 → 阿拉伯数字 → 全角数字
          address = address.replace(/([一二三四五六七八九十]+)丁目$/, (_, kanji) => {
            let num = 0;
            if (kanji.length === 1) {
              num = kanjiToNumber[kanji];
            } else if (kanji.length === 2 && kanji[0] === "十") {
              num = 10 + (kanjiToNumber[kanji[1]] || 0);
            } else if (kanji.length === 2 && kanji[1] === "十") {
              num = (kanjiToNumber[kanji[0]] || 0) * 10;
            } else if (kanji.length === 3 && kanji[1] === "十") {
              num = (kanjiToNumber[kanji[0]] || 0) * 10 + (kanjiToNumber[kanji[2]] || 0);
            }
            return `${numberToFullWidth(num)}丁目`;
          });

          // 半角数字丁目 → 全角数字丁目
          address = address.replace(/([0-9]+)丁目$/, (_, num) => {
            return `${numberToFullWidth(parseInt(num))}丁目`;
          });

          // ✅ 自动添加「丁目」
          // 如果最后是半角数字或全角数字，并且后面没有「丁目」
          if (/(?:[0-9０-９]+)$/.test(address) && !address.endsWith("丁目")) {
            const match = address.match(/([0-9０-９]+)$/);
            if (match) {
              const fullWidthNum = match[1].replace(/[0-9]/g, d =>
                String.fromCharCode(d.charCodeAt(0) + 0xFEE0)
              );
              address = address.replace(/([0-9０-９]+)$/, `${fullWidthNum}丁目`);
            }
          }

          return address;
        }

        currentData.address = normalizeChomeEnding(currentData.fullAddress);
        currentData.compositeTitle = getCompositeTitleForSuumo({ rec: currentData, recordType: currentData.recordType });

        if (currentData.yield && currentData.yield.indexOf("%") > -1) {
          currentData.yearlyIncome = currentData.price * parseFloat(currentData.yield.replace("%", "").trim()) / 100;
        }

        currentData.allImages = await block.evaluate((el: any, sel: any) => {
          return Array.from(el.querySelectorAll(sel))
            .map((img: any) =>
              img.getAttribute('data-original') || img.getAttribute('src') || ''
            )
            .filter((src: any) => !!src && src.startsWith('http'));
        }, fieldSelector.imageLink);


        currentData.buildingLevel = currentData.buildingLevel;

        currentData.buildingRoomCount = currentData.buildingRoomCount?.indexOf("戸") > -1 ? parseInt(currentData.buildingRoomCount.replace("戸", "").trim()) : null;

        delete currentData.detailsRawText;
        // Ads will be null and not having data
        console.log("🔥 currentData", currentData);

        allData.push(currentData);
      }

      let { createdRecordsCount, updatedRecordsCount, savedSupabaseFileCount } = await saveDataRakumachi(allData);

      console.log("🔥 createdRecordsCount, updatedRecordsCount, savedSupabaseFileCount ", createdRecordsCount, updatedRecordsCount, savedSupabaseFileCount);

      let endTime = dayjsWithTz();
      const duration = dayjsWithTz.duration(endTime.diff(startTime));
      sendLark({
        message: `[Rakumachi][🕒${duration.minutes()}:${duration.seconds()}][${currentPage + 1}/${PAGE_COUNT}] createdRecordsCount: ${createdRecordsCount}, updatedRecordsCount: ${updatedRecordsCount}, savedSupabaseFileCount: ${savedSupabaseFileCount}`,
        url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
      });

      if (currentPage < PAGE_COUNT - 1) {
        const paginationParts = await page.$$(".PagerBottom #pagination_next_bottom");

        if (paginationParts.length > 0) {
          await paginationParts[0].click(); // 点击第一个
          await page.waitForNavigation({ waitUntil: 'networkidle0' }); // 可选：等待页面加载
        }
      }
    }

    // await writeFile(`downloaded/${dayjs().format("YYYY-MM-DD HH:mm:ss")}-RAKUMACHI-${allData.length}.json`, JSON.stringify(allData, null, 2));
    return NextResponse.json({ message: "Success" }, { status: 200 });
  } catch (err: any) {
    logger.error("🔥 Error in cron job 🔥", err);

    await sendLark({
      message: `[Suumo][Error getting data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return NextResponse.json({ error: err }, { status: 500 });
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
