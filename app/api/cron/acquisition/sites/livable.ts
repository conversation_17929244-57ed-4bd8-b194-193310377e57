import { logger } from "@/lib/logger";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { saveIntoDb } from "../saveIntoDb";
import { writeFileSync } from "fs";
import dayjs from "dayjs";
import { getStationWalkMinuteFromAddress } from "@/lib/helper/geo";
export async function getLivableData(page: any) {
  const results = [];

  try {
    const urls = {
      tokyo:
        "https://www.livable.co.jp/fudosan-toushi/tatemono-tokyo-select-area/a13000/conditions-use=mansion-itto,apart,building-itto,store-office,factory-warehouse,company-housing-dormitory,other&new&price-down/",
      kanagawa:
        "https://www.livable.co.jp/fudosan-toushi/tatemono-kanagawa-select-area/a14117,a14102,a14111,a14109,a14101,a14104,a14103,a14105,a14137,a14131,a14132,a14134,a14135,a14133,a14136,a14215,a14216,a14205,a14213/conditions-use=mansion-itto,apart,building-itto,store-office,factory-warehouse,company-housing-dormitory,other&new&price-down/",
      saitama: "https://www.livable.co.jp/fudosan-toushi/tatemono-saitama-select-area/a11103,a11105,a11227,a11327,a11214,a11215,a11221,a11229/conditions-use=mansion-itto,apart,building-itto,store-office,factory-warehouse,company-housing-dormitory,other&new&price-down/",
      chiba: "https://www.livable.co.jp/fudosan-toushi/tatemono-chiba-select-area/a12103,a12101,a12203,a12206,a12216,a12204,a12207/conditions-use=mansion-itto,apart,building-itto,store-office,factory-warehouse,company-housing-dormitory,other&new&price-down/"
    };

    for (const url of Object.values(urls)) {
      // *********************************************************
      // Step 1: go page
      // 検索条件	物件種別 ： マンション（一棟）、ビル（一棟）、アパート（一棟）、投資用戸建、工場・倉庫、社宅・寮、ホテル・旅館、その他建物、土地　価格 ： 下限なし ～ 上限なし　専有面積 ： 下限なし ～ 上限なし　建物面積 ： 下限なし ～ 上限なし　土地面積 ： 下限なし ～ 上限なし　駅徒歩：指定なし　築年数：指定なし　利回り ： 指定なし
      await page.goto(url);

      const totalCountSelector =
        ".m-filter-condition__result .m-filter-condition__result-number";
      const totalCountSelectorCurrentPage = "li.o-product-list__item";

      await page.waitForSelector(totalCountSelector);

      // *********************************************************
      // Step 2: Reading data as needed
      // Step 2.1 Get total number of bukken
      const totalItemCount = await page.evaluate((sel: any) => {
        const res = parseInt(document?.querySelector(sel)?.textContent || "0");
        return res;
      }, totalCountSelector);
      logger.info(`🔥 Total item count is ${totalItemCount}..`);

      const newDataSelectors = {
        title: ".o-product-list__headline-text",
        type: ".o-product-list__category", // should remove the tag that looks like NEW 8/29
        price: ".o-product-list__info-body > .a-price__number",
        roi: ".o-product-list__info-body > .a-price__yield",
        address: ".o-product-list__info-list dl:nth-child(1) dd", // 東京都大田区千鳥３丁目
        transport: ".o-product-list__info-list dl:nth-child(2) dd", // 東急多摩川線「下丸子」駅 徒歩1分,東急池上線「千鳥町」駅 徒歩7分
        buildingSize: ".m-contact-property__info-details dl:nth-child(1) dd",
        landSize: ".m-contact-property__info-details dl:nth-child(2) dd",
        buildingBuiltYear:
          ".m-contact-property__info-details dl:nth-child(3) dd",
        buildingMaterial:
          ".m-contact-property__info-details dl:nth-child(4) dd", // 鉄骨造9階地上9階建
        hpUrl:
          ".o-product-list__more .o-product-list__more-details .o-product-list__more-link",
        imageLinks: ".o-product-list__slider img"
      } as any;

      let totalItemCountCurrentPage = await page.evaluate((sel: any) => {
        const res = document.querySelectorAll(sel).length;
        return res;
      }, totalCountSelectorCurrentPage);

      const pageCount =
        totalItemCount <= 30
          ? 1
          : Math.ceil(totalItemCount / totalItemCountCurrentPage); // e.g. 3
      logger.debug(`🔥 Page count is ${pageCount}..`);

      // For lazy loading image //
      async function autoScroll(page: any) {
        await page.evaluate(async () => {
          await new Promise((resolve) => {
            let totalHeight = 0;
            const distance = 200;
            const timer = setInterval(() => {
              window.scrollBy(0, distance);
              totalHeight += distance;

              if (totalHeight >= document.body.scrollHeight) {
                clearInterval(timer);
                resolve(void 0);
              }
            }, 100);
          });
        });
      }

      for (let i = 1; i <= pageCount; i++) {
        totalItemCountCurrentPage = await page.evaluate((sel: any) => {
          const res = document.querySelectorAll(sel).length;
          return res;
        }, totalCountSelectorCurrentPage);

        logger.info(
          `🔥 Total item count for current page ${i}/${pageCount} is ${totalItemCountCurrentPage}..`
        );

        // await autoScroll(page); // 触发 lazyload
        // await new Promise(resolve => setTimeout(resolve, 11000)); // 给图片一点加载时间

        for (let j = 0; j < totalItemCountCurrentPage; j++) {
          let currentResult = {} as any;

          for (const field of Object.keys(newDataSelectors)) {
            const selector = `${totalCountSelectorCurrentPage}:nth-child(${j + 1
              }) ${newDataSelectors[field]}`;

            const newField = await page.evaluate(
              (sel: any, field: any) => {

                if (field === "imageLinks") {
                  return {
                    [`${field}`]: Array.from(document?.querySelectorAll(sel))
                      .map((img: any) => img.getAttribute('data-src') || '')
                      .filter((src) => !!src && src.startsWith('http'))
                  };
                }

                let res = null;
                res = document.querySelector(sel);
                if (res !== null && res !== undefined) {
                  if (field === "hpUrl") {
                    return {
                      [`${field}`]: `https://www.livable.co.jp${res.getAttribute(
                        "href"
                      )}`,
                    };
                  }

                  if (
                    (field === "landSize" || field === "buildingSize") &&
                    res?.textContent?.indexOf("m2") !== -1
                  ) {
                    return {
                      [`${field}`]: parseFloat(res?.textContent?.split("m2")[0] || "0"),
                    };
                  }

                  if (field === "roi") {
                    return {
                      [`${field}`]: parseFloat(
                        res?.textContent?.replace("利回り：", "").replace("%", "") || "0"
                      ),
                    };
                  }

                  if (field === "type") {
                    return {
                      [`${field}`]: res?.textContent
                        ?.split("新価格")[0]
                        ?.split("NEW")[0]
                        ?.split("一棟売")[0]
                        ?.replace(/(\r\n\t|\n|\r\t|\s)/gm, ""),
                    };
                  }

                  return {
                    [`${field}`]: res?.textContent?.replace(
                      /(\r\n\t|\n|\r\t|\s)/gm,
                      ""
                    ),
                  };
                }

                return res;
              },
              selector,
              field
            );

            currentResult = {
              ...newField,
              ...currentResult,
            };
          }
          currentResult["sourceSite"] = "LIVABLE";

          // if (currentResult["type"] !== "その他建物（区分）") {
          //   // fix: その他建物 include both 区分 and 一棟
          //   console.log("🔥 currentResult🔥 🔥");
          //   results.push(currentResult);
          // }

          // TO FIX BELOW
          //       {
          // hpUrl: '/fudosan-toushi/CXI242G64/',
          // material: '鉄骨造2階地上2階建',
          // builtYear: '2007年7月',
          // landSize: '85.55m2',
          // buildingSize: '103.80m2',
          // transport: '東京メトロ副都心線「明治神宮前」駅徒歩6分',
          // address: '東京都渋谷区神宮前５丁目',
          // roi: '利回り：3.48%',
          // price: '2億5,800万円',
          // type: '店舗NEW9/2',
          // title: '東京都渋谷区神宮前５丁目',
          // sourceSite: 'LIVABLE'
          //       }

          if (currentResult["price"] !== undefined) {
            if (currentResult["price"].indexOf("億円") !== -1) {
              currentResult["price"] =
                parseInt(currentResult["price"].split("億円")[0], 10) * 10000;
            } else if (currentResult["price"].indexOf("億") !== -1) {
              const okuCount = currentResult["price"].split("億")[0];
              const manCount = currentResult["price"]
                .split("億")[1]
                .replace(",", "");
              currentResult["price"] =
                parseInt(okuCount, 10) * 10000 + parseInt(manCount, 10);
            } else {
              currentResult["price"] = parseInt(
                currentResult["price"].replace(",", ""),
                10
              );
            }
          }

          if (currentResult["roi"] !== undefined) {
            currentResult["yearlyIncome"] =
              currentResult["price"] * (currentResult["roi"] / 100);
          }

          if (currentResult["transport"] !== undefined) {
            currentResult["nearestStation"] = currentResult["transport"]
              .split("「")[1]
              .split("」")[0];

            currentResult["nearestStationWalkMinute"] = await getStationWalkMinuteFromAddress(currentResult["address"]);
          }

          if (currentResult["buildingBuiltYear"] !== undefined) {
            currentResult["buildingBuiltYear"] = parseInt(
              currentResult["buildingBuiltYear"].split("年")[0]
            );
          }

          if (currentResult["buildingMaterial"] !== undefined) {
            currentResult["salesComments"] = currentResult["buildingMaterial"];
            // buildingMaterial: ".m-contact-property__info-details dl:nth-child(4) dd", // 鉄骨造9階地上9階建
            currentResult["buildingMaterial"] =
              currentResult["buildingMaterial"]?.indexOf("造") > 1
                ? currentResult["buildingMaterial"].split("造")[0]
                : `${currentResult["buildingMaterial"]}造`;
          }

          if (currentResult["type"].indexOf("新価格") > -1) {
            currentResult["salesComments"] = "新価格";
          }

          logger.info("🔥 currentResult🔥 🔥");
          logger.info(currentResult);

          results.push(currentResult);
        }
        // TODO: if still less than current page.. then just go to next page

        if (i < pageCount) {
          logger.info("🔥 i🔥 🔥", i);
          await page.click(
            ".m-page-navigation__next.iconfont-livable-arrow_right"
          );
          await new Promise(resolve => setTimeout(resolve, 3000));
          logger.info("🔥  waiting for next page ... 🔥 🔥");
        }
      }

      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    logger.debug("🔥 Complete reading results..., length is " + results.length);

    // *********************************************************
    // Step 3: Saving data
    // *********************************************************
    // writeFileSync(`livable-${dayjs().format("YYYYMMDDHHmmss")}.json`, JSON.stringify(results, null, 2));
    await saveIntoDb(results);
    return results;
  } catch (err: any) {
    await sendLark({
      message: `[Livable][🔥 Error getting data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    throw err;
  }
};
