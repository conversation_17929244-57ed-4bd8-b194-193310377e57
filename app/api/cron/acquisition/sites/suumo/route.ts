import { logger } from "@/lib/logger";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { link, writeFile } from "fs/promises";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import duration from "dayjs/plugin/duration";
dayjsWithTz.extend(duration);
import { getCompositeTitleForSuumo } from "../../../reins/utility/getCompositeTitle";
import { saveData } from "./saveData";

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  if (req.method !== "GET") {
    return NextResponse.json({ error: "Method Not Allowed" }, { status: 405 });
  }

  const url = new URL(req.url); // 创建 URL 对象
  const taskIndex = url.searchParams.get("taskIndex"); // 获取查询参数

  let browser = await getChromeBrowser();
  let page: any;

  const retryTaskIfFail = async (task: () => Promise<any[]>, retries = 2) => {
    let pass = false;
    for (let i = 0; i < retries; i++) {
      if (pass) {
        break;
      }

      try {
        let res = await task();
        pass = true;
        return res;
      } catch (err) {
        logger.error("🔥 Error in cron job.. retrying once 🔥", err);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    return [];
  };


  try {
    page = await getPageWithRandomUserAgent(browser);

    let currentMinute = dayjsWithTz().minute();
    let currentHour = dayjsWithTz().hour();

    const SUUMO_3D_URLS = [{
      linkToGo: "https://suumo.jp/jj/bukken/ichiran/JJ012FC001/?ar=030&bs=021&cn=9999999&cnb=0&ekTjCd=&ekTjNm=&hb=0& ht=9999999&kb=1&kki=102&kt=9999999&ta=13&tb=0&tj=0&tt=9999999&po=1&pj=2&pc=100",
      linkTitle: "東京中古",
    }, {
      linkToGo: "https://suumo.jp/jj/bukken/ichiran/JJ012FC001/?ar=030&bs=021&cn=9999999&cnb=0&ekTjCd=&ekTjNm=&hb=0&ht=9999999&kb=1&kki=102&kt=9999999&ta=14&tb=0&tj=0&tt=9999999&po=1&pj=2&pc=100",
      linkTitle: "神奈川中古",
    }, {
      linkToGo: "https://suumo.jp/jj/bukken/ichiran/JJ012FC001/?ar=030&bs=021&cn=9999999&cnb=0&ekTjCd=&ekTjNm=&hb=0&ht=9999999&kb=1&kki=102&kt=9999999&ta=11&tb=0&tj=0&tt=9999999&po=1&pj=2&pc=100",
      linkTitle: "埼玉中古",
    },
    {
      linkToGo: "https://suumo.jp/jj/bukken/ichiran/JJ012FC001/?ar=030&bs=021&cn=9999999&cnb=0&ekTjCd=&ekTjNm=&hb=0&ht=9999999&kb=1&kki=102&kt=9999999&ta=12&tb=0&tj=0&tt=9999999&po=1&pj=2&pc=100",
      linkTitle: "千葉中古",
    },
    {
      linkToGo: "https://suumo.jp/jj/bukken/ichiran/JJ012FC001/?ar=030&bs=020&ekTjCd=&ekTjNm=&hb=0&ht=9999999&kb=1&kki=102&km=0&kt=9999999&kw=1&ta=13&tb=0&tj=0&tt=9999999&po=0&pj=1&pc=100",
      linkTitle: "東京新築",
    }, {
      linkToGo: "https://suumo.jp/jj/bukken/ichiran/JJ012FC001/?ar=030&bs=020&ekTjCd=&ekTjNm=&hb=0&ht=9999999&kb=1&kki=102&km=0&kt=9999999&kw=1&ta=14&tb=0&tj=0&tt=9999999&po=0&pj=1&pc=100",
      linkTitle: "神奈川新築",
    }, {
      linkToGo: "https://suumo.jp/jj/bukken/ichiran/JJ012FC001/?ar=030&bs=020&ekTjCd=&ekTjNm=&hb=0&ht=9999999&kb=1&kki=102&km=0&kt=9999999&kw=1&ta=11&tb=0&tj=0&tt=9999999&po=0&pj=1&pc=100",
      linkTitle: "埼玉新築",
    }, {
      linkToGo: "https://suumo.jp/jj/bukken/ichiran/JJ012FC001/?ar=030&bs=020&ekTjCd=&ekTjNm=&hb=0&ht=9999999&kb=1&kki=102&km=0&kt=9999999&kw=1&ta=12&tb=0&tj=0&tt=9999999&po=0&pj=1&pc=100",
      linkTitle: "千葉新築",
    }]


    let linkTitle = SUUMO_3D_URLS[0].linkTitle;
    let linkToGo = SUUMO_3D_URLS[0].linkToGo;

    let taskIndexInt = taskIndex ? parseInt(taskIndex) : Math.floor(currentHour / 12) * 4 + Math.floor(currentMinute / 15);
    linkTitle = SUUMO_3D_URLS[taskIndexInt].linkTitle;
    linkToGo = SUUMO_3D_URLS[taskIndexInt].linkToGo;

    let isChukou = taskIndex ? parseInt(taskIndex) < 4 : Math.floor(currentHour / 12) === 0;
    const PAGE_COUNT_SELECTOR = "ol.pagination-parts li:last-child a";
    await page.goto(linkToGo);
    await page.waitForSelector(PAGE_COUNT_SELECTOR);

    const PAGE_COUNT = await page.evaluate((sel: any) => {
      const pageCount = document.querySelector(sel)?.textContent;
      return pageCount ? parseInt(pageCount.replace(/,/g, "")) : 0;
    }, PAGE_COUNT_SELECTOR);

    logger.info("🔥 PAGE_COUNT🔥", PAGE_COUNT);

    const SUUMO_PROPERTY_UNIT_SELECTOR = ".property_unit";

    let allData = [];


    const suumoSelector = {
      propertyTitle: ".property_unit-title a",
      propertyLink: ".property_unit-title a",
      propertyDetails: ".property_unit-info",
      propertyAllImages: ".ui-media-object img", // 
      //     const imageUrls = Array.from(document.querySelectorAll('.property_unit:nth-child(1) .ui-media-object img'))
      // .map(img => img.src.trim())
      // .filter(Boolean);
      salesPoint: ".storecomment-txt",
      realEstateName: ".shopmore-title",
      realEstateNumber: ".makermore-tel",
    }

    const dataInDetails = {
      propertyName: "物件名",
      price: "販売価格",
      fullAddress: "所在地",
      transport: "沿線・駅",
      landSize: "土地面積",
      buildingLayout: "間取り",
      buildingSize: "建物面積",
      ...(isChukou && { buildingBuiltTime: "築年月" }),
    };

    const extractField = (label: any, rawText: any) => {
      const regex = new RegExp(`${label}\\s*([\\s\\S]*?)\\s{2,}`); // 宽松匹配后面内容直到多个空格/换行
      const match = rawText?.match(regex);
      return match ? match[1].trim() : null;
    };

    for (let currentPage = 0; currentPage < PAGE_COUNT; currentPage++) {
      allData = [];

      let startTime = dayjsWithTz(); // Job start at 15,30,45,00
      const CURRENT_PAGE_COUNT = await page.evaluate((sel: any) => {
        return document.querySelectorAll(sel)?.length;
      }, SUUMO_PROPERTY_UNIT_SELECTOR);
      logger.info("🔥 CURRENT_PAGE_COUNT🔥", CURRENT_PAGE_COUNT);

      // scroll to the bottom of the page for lazy koading
      await page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });

      // IMPORTANT: remove random inquiry 
      await page.evaluate((sel: any) => {
        const elements = document.querySelectorAll(sel);
        for (let i = 0; i < elements.length; i++) {
          elements[i]?.parentNode?.removeChild(elements[i]);
        }
      }, ".inquiry");

      // await new Promise(resolve => setTimeout(resolve, 5000));
      for (let i = 0; i < CURRENT_PAGE_COUNT; i++) {

        let currentData = {} as any;

        currentData.propertyTitle = await page.evaluate((sel: any) => {
          return document.querySelector(sel)?.textContent;
        }, `${SUUMO_PROPERTY_UNIT_SELECTOR}:nth-child(${i + 1}) ${suumoSelector.propertyTitle}`) as string;

        currentData.link = "https://suumo.jp" + await page.evaluate((sel: any) => {
          return document.querySelector(sel)?.getAttribute("href");
        }, `${SUUMO_PROPERTY_UNIT_SELECTOR}:nth-child(${i + 1}) ${suumoSelector.propertyLink}`) as string;

        currentData.detailsRawText = await page.evaluate((sel: any) => {
          return document.querySelector(sel)?.textContent;
        }, `${SUUMO_PROPERTY_UNIT_SELECTOR}:nth-child(${i + 1}) ${suumoSelector.propertyDetails}`) as string;

        Object.keys(dataInDetails).forEach((key: any) => {
          currentData[key] = extractField(dataInDetails[key as keyof typeof dataInDetails], currentData.detailsRawText);
        });

        currentData.landSize = parseFloat(currentData.landSize.split("m2")[0].trim());
        currentData.buildingSize = parseFloat(currentData.buildingSize.split("m2")[0].trim());

        function parseJapanesePrice(priceStr: string): number {
          // 只取“・”或“、”前的部分
          const trimmed = priceStr.split(/[・、]/)[0];

          const 億Match = trimmed.match(/([0-9.]+)億/);
          const 万Match = trimmed.match(/([0-9.]+)万円/);

          const 億 = 億Match ? parseFloat(億Match[1]) * 10000 : 0;
          const 万 = 万Match ? parseFloat(万Match[1]) : 0;

          return 億 + 万;
        }

        currentData.price = parseJapanesePrice(currentData.price);

        const stationMatch = currentData.transport.match(/「(.+?)」/);
        const walkMatch = currentData.transport.match(/徒歩(\d+)分/); // 严格匹配「徒歩」开头
        currentData.nearestStation = stationMatch ? stationMatch[1] : null;
        currentData.nearestStationWalkMinute = walkMatch ? parseInt(walkMatch[1], 10) : null;

        currentData.buildingBuiltYear = isChukou ? (currentData.buildingBuiltTime.indexOf("年") > -1 ? parseInt(currentData.buildingBuiltTime.split("年")[0]) : null) : 2025;

        const parseForAddress = (fullAddress: string) => {
          // 1.	如果地址以全角数字结尾 → 加 丁目 "東京都葛飾区新小岩３",
          // 2.	如果地址中含有 - 或 － → 提取 - 之前的部分，判断是否以数字结尾 → 加 丁目 "東京都羽村市羽加美３-１０－４４",
          // 统一处理半角/全角连字符
          const normalized = fullAddress.replace(/－/g, "-");

          // 情况 1：结尾是全角数字
          if (/[０-９]$/.test(normalized)) {
            return normalized + "丁目";
          }

          // 情况 2：包含 -
          const dashIndex = normalized.indexOf("-");
          if (dashIndex !== -1) {
            const beforeDash = normalized.slice(0, dashIndex);
            // 提取最后一个字符
            const lastChar = beforeDash.charAt(beforeDash.length - 1);
            if (/[０-９]/.test(lastChar)) {
              return beforeDash + "丁目";
            }
          }

          // 其他情况：不变
          return fullAddress;
        }

        currentData.address = parseForAddress(currentData.fullAddress);
        currentData.compositeTitle = getCompositeTitleForSuumo({ rec: currentData, recordType: "HOUSE" });

        currentData.allImages = await page.evaluate((sel: any) => {
          function convertSuumoImageUrl(thumbnailUrl: string): string {
            const match = thumbnailUrl.match(/src=gazo%2Fbukken%2F([^&]+)/);
            if (!match || !match[1]) return "";

            // 解码 URL 中的路径
            const decodedPath = decodeURIComponent(match[1]);
            return `https://suumo.jp/front/gazo/bukken/${decodedPath}`;
          }

          return Array.from(document.querySelectorAll(sel))
            .map((img: any) => convertSuumoImageUrl(img.getAttribute('src') || img.getAttribute('data-src') || ''))
            .filter((src) => !!src && src.startsWith('http'));
        }, `${SUUMO_PROPERTY_UNIT_SELECTOR}:nth-child(${i + 1}) ${suumoSelector.propertyAllImages}`);

        ["title", "salesPoint", "realEstateName", "realEstateNumber"].forEach(async (key: any) => {
          currentData[key] = await page.evaluate((sel: any) => {
            return document.querySelector(sel)?.textContent.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
          }, `${SUUMO_PROPERTY_UNIT_SELECTOR}:nth-child(${i + 1}) ${suumoSelector[key as keyof typeof suumoSelector]}`) as string;
        });

        delete currentData.detailsRawText;
        allData.push(currentData);
      }

      console.log("🔥 allData length now ", allData.length);

      // Some land are advertised as house, so we need to filter them out
      let { createdRecordsCount, updatedRecordsCount, savedSupabaseFileCount } = await saveData({ allData: allData.filter((rec: any) => rec.buildingSize > 0), recordType: "HOUSE" });

      console.log("🔥 createdRecordsCount, updatedRecordsCount, savedSupabaseFileCount ", createdRecordsCount, updatedRecordsCount, savedSupabaseFileCount);

      let endTime = dayjsWithTz();
      const duration = dayjsWithTz.duration(endTime.diff(startTime));
      sendLark({
        message: `[Suumo][${linkTitle}][🕒${duration.minutes()}:${duration.seconds()}][${currentPage + 1}/${PAGE_COUNT}] createdRecordsCount: ${createdRecordsCount}, updatedRecordsCount: ${updatedRecordsCount}, savedSupabaseFileCount: ${savedSupabaseFileCount}`,
        url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
      });

      let NEXT_PAGE_SELECTOR = "p.pagination-parts:last-child"; // note that there will be 2 of these //      

      if (currentPage < PAGE_COUNT - 1) {
        const paginationParts = await page.$$(NEXT_PAGE_SELECTOR);

        if (paginationParts.length > 0) {
          await paginationParts[0].click(); // 点击第一个
          await page.waitForNavigation({ waitUntil: 'networkidle0' }); // 可选：等待页面加载
        }
      }
    }

    // await writeFile(`downloaded/${dayjs().format("YYYY-MM-DD HH:mm:ss")}-SUUMO-${allData.length}.json`, JSON.stringify(allData, null, 2));
    // console.log("🔥 Results saved to output.json, total: ", allData.length, "🔥");

    return NextResponse.json({ message: "Success" }, { status: 200 });
  } catch (err: any) {
    logger.error("🔥 Error in cron job 🔥", err);

    await sendLark({
      message: `[Suumo][Error getting data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return NextResponse.json({ error: err }, { status: 500 });
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
