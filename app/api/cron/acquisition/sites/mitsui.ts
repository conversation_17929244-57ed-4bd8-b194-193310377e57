import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { saveIntoDb } from "../saveIntoDb";
import { logger } from "@/lib/logger";
import { NextResponse } from "next/server";
import dayjs from "dayjs";
import { writeFileSync } from "fs";

export async function getMitsuiData(page: any) {
  try {
    // *********************************************************
    // Step 1: go page
    // Tokyo, all investment types except 区分店舗

    let allUrls = {
      building_tokyo: {
        pref: "東京都",
        url: "https://www.rehouse.co.jp/buy/tohshi/prefecture/13/city/13101/?cityCode=13102,13103,13104,13105,13106,13107,13108,13109,13110,13111,13112,13113,13114,13115,13116,13117,13118,13119,13120,13121,13122,13123,13201,13202,13203,13204,13205,13206,13207,13208,13209,13210,13211,13212,13213,13214,13215,13218,13219,13220,13221,13222,13223,13224,13225,13227,13228,13229,13303&otherRequirements=1&buildingTypes=4,3,2,1,6",
      },

      // FIXME: still need to crawl to other pages, currently only look at first page 

      house_tokyo: {
        pref: "東京都",
        url: "https://www.rehouse.co.jp/buy/kodate/prefecture/13/city/13101/?cityCode=13102,13103,13104,13105,13106,13107,13108,13109,13110,13111,13112,13113,13114,13115,13116,13117,13118,13119,13120,13121,13122,13123,13201,13202,13203,13204,13205,13206,13207,13208,13209,13210,13211,13212,13213,13214,13215,13218,13219,13220,13221,13222,13223,13224,13225,13227,13228,13229&otherRequirements=1&order=firstReleaseDateDesc",
      },

      house_kanagawa: {
        pref: "神奈川県",
        url: "https://www.rehouse.co.jp/buy/kodate/prefecture/14/city/14101/?cityCode=14102,14103,14104,14105,14106,14107,14108,14109,14110,14111,14112,14113,14114,14115,14116,14117,14118,14131,14132,14133,14134,14135,14136,14137,14151,14152,14153,14201,14203,14204,14205,14206,14207,14208,14210,14211,14212,14213,14214,14215,14216,14218,14301,14321,14341,14342,14382,14384,14401&otherRequirements=1&order=firstReleaseDateDesc",
      },

      house_saitama: {
        pref: "埼玉県",
        url: "https://www.rehouse.co.jp/buy/kodate/prefecture/11/city/11101/?cityCode=11102,11103,11104,11105,11106,11107,11108,11109,11110,11201,11203,11206,11208,11209,11210,11211,11212,11214,11215,11217,11218,11219,11221,11222,11223,11224,11225,11227,11228,11229,11230,11231,11232,11233,11234,11235,11237,11238,11239,11241,11242,11243,11245,11246,11301,11324,11341,11343,11442,11464,11465&otherRequirements=1&order=firstReleaseDateDesc"
      },

      house_chiba: {
        pref: "千葉県",
        url: "https://www.rehouse.co.jp/buy/kodate/prefecture/12/city/12101/?cityCode=12102,12103,12104,12105,12106,12203,12204,12206,12207,12208,12210,12211,12212,12213,12216,12217,12219,12220,12221,12222,12224,12227,12228,12229,12231,12232,12233,12237,12239,12342&otherRequirements=1&order=firstReleaseDateDesc"
      }
    }

    let results: any[] = [];
    let PAGE_ITEMS_COUNT = 30;

    for (const urlKey of Object.keys(allUrls)) {
      results = [];

      await page.goto(allUrls[urlKey as keyof typeof allUrls].url);
      await page.waitForSelector(".count");

      // *********************************************************
      // Step 2.1 Get total number of bukken
      const spans = await page.$$eval('p.count span', (els: any) =>
        els.map((el: any) => el.textContent?.trim() || "")
      );

      const totalText = spans.find((text: any) => text.includes("/"));
      const TOTAL_COUNT = totalText ? parseInt(totalText.replace(/\D/g, ""), 10) : 0;

      logger.info(`🔥 Total item count is ${TOTAL_COUNT}..`);

      const PAGE_COUNT =
        TOTAL_COUNT <= 30
          ? 1
          : Math.ceil(TOTAL_COUNT / PAGE_ITEMS_COUNT); // e.g. 3

      for (let i = 1; i <= PAGE_COUNT; i++) {
        const totalItemCountForThisPage = await page.evaluate((sel: any) => {
          const res = document.querySelectorAll(sel).length;
          return res;
        }, ".property-index-card");
        logger.info(`🔥 Total item count for this page is ${totalItemCountForThisPage}..`);

        // Step 3.2 Reading details
        const newDataSelectors = {
          title: ".property-title",
          hpUrl: ".button-area a",
          price: ".price",
          type: ".main-image-box .tag-wrapper",
          details1: ".paragraph-body:nth-child(1)", // 世田谷区等々力４丁目 / 東急大井町線 尾山台駅 徒歩3分
          details2: ".paragraph-body:nth-child(2)", // 1K / 建物412.11㎡ / 土地203.57㎡ / 1987年02月築
          // roi: ".rimawari em",
          // address: ".info_wrapper dl:nth-child(4) dd",
          imageLinks: ".image-section img",
        } as any;

        // let mapper = {
        //   価格: "price",
        //   物件種別: "type",
        //   "想定利回り※": "roi",
        //   所在地: "address",
        //   交通: "transport",
        //   土地面積: "landSize",
        //   "間取り/建物面積": "layoutAndBuildingSize",
        //   築年月: "buildingBuiltYear",
        //   構造: "buildingMaterial",
        // };

        for (let i = 0; i < totalItemCountForThisPage; i++) {
          logger.info(`🔥 Getting data for the ${i + 1}th result 🔥`);

          let currentResult = {} as any;

          for (const field of Object.keys(newDataSelectors)) {
            const selector = `.property-index-card:nth-child(${i + 1}) ${newDataSelectors[field]
              }`;

            const newField = await page.evaluate(
              (sel: any, field: any) => {
                if (field === "imageLinks") {
                  return {
                    [`${field}`]: Array.from(document?.querySelectorAll(sel))
                      .filter((img: any) => img.getAttribute('src') !== null)
                      .map((img: any) => img.getAttribute('src'))
                      .filter((src) => !!src && src.startsWith('http'))
                  };
                }

                const res = document.querySelector(sel);
                if (res !== null && res !== undefined) {
                  if (field === "hpUrl") {
                    return {
                      [`${field}`]: `https://www.rehouse.co.jp${res.getAttribute(
                        "href"
                      )}`,
                    };
                  }

                  if (field === "details1") {
                    return {
                      [`${field}`]: res.textContent,
                    };
                  }

                  return {
                    [`${field}`]: res?.textContent?.replace(
                      /(\r\n\t|\n|\r\t|\s)/gm,
                      ""
                    ) || "",
                  };
                }

                return null;
              },
              selector,
              field
            );

            if (newField !== null) {
              currentResult = {
                ...newField,
                ...currentResult,
              };
            }
          }

          if (currentResult["price"] !== undefined) {
            currentResult["price"] = parseInt(
              currentResult["price"].replace(",", ""),
              10
            );
          }

          if (currentResult["type"] !== undefined) {
            if (currentResult["type"].indexOf("新価格") > -1) {
              currentResult["salesComments"] = "新価格";
            }

            currentResult["type"] = currentResult["type"]
              .replace("新価格", "")
              .replace("NEW", "");
          }

          if (currentResult["details1"] !== undefined) {
            // 世田谷区等々力４丁目 / 東急大井町線 尾山台駅 徒歩3分
            const res = currentResult["details1"].replace("\n", " ").split(" / ");
            currentResult["address"] = allUrls[urlKey as keyof typeof allUrls].pref + res[0];
            currentResult["transport"] = res[1];
            currentResult["nearestStation"] = res[1]
              .split(" ")[1]
              ?.replace("駅", "");
            const m = res[1].split(" ")[2];
            currentResult["nearestStationWalkMinute"] =
              m.indexOf("徒歩") > -1 ? parseInt(m.replace("徒歩", "")) : 999;
          }

          if (currentResult["details2"] !== undefined) {
            // 世田谷区等々力４丁目 / 東急大井町線 尾山台駅 徒歩3分
            const res = currentResult["details2"].split("/");

            currentResult["layout"] = res[0];
            currentResult["buildingSize"] = parseFloat(
              res[1].replace("建物", "").replace("㎡", "")
            );
            currentResult["landSize"] = parseFloat(
              res[2].replace("土地", "").replace("㎡", "")
            );

            currentResult["buildingBuiltYear"] =
              res[3].indexOf("築1年未満") > -1
                ? 2024
                : parseInt(res[3].split("年")[0]);
          }

          logger.info("🔥 now processing results ...🔥");
          currentResult["sourceSite"] = "MITSUI";

          results.push(currentResult);
        }

        logger.info("🔥 Complete reading results..., length is " + results.length);

        await saveIntoDb(results);
        results = [];

        if (i < PAGE_COUNT) {
          await page.click(
            ".property-index-pagination > a:last-child"
          );
          await new Promise(resolve => setTimeout(resolve, 3000));
          logger.info("🔥  waiting for next page ... 🔥 🔥");
        }
      }
    }

    // console.log("🔥 results🔥", results);

    // writeFileSync(`mitsui-${dayjs().format("YYYYMMDDHHmmss")}.json`, JSON.stringify(results, null, 2));


    return results;
  } catch (err: any) {
    await sendLark({
      message: `[Mitsui][🔥 Error getting data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    throw err;
  }
};
