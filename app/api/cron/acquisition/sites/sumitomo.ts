import { logger } from "@/lib/logger";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { saveIntoDb } from "../saveIntoDb";
import { writeFileSync } from "fs";
import dayjs from "dayjs";
import { getStationWalkMinuteFromAddress } from "@/lib/helper/geo";

export async function getSumitomoData(page: any) {
  try {
    const tasks = [
      {
        url: "https://www.stepon.co.jp/pro/ca_1_001/100_1/?isShinchaku=on&smk=111011111110",
        type: "toshi",
      },
      {
        url: "https://www.stepon.co.jp/search/list/?limit=100&pageNo=1&prefCityCd=13_103&prefCityCd=13_102&prefCityCd=13_104&prefCityCd=13_113&prefCityCd=13_105&prefCityCd=13_116&prefCityCd=13_117&prefCityCd=13_119&prefCityCd=13_108&prefCityCd=13_107&prefCityCd=13_106&prefCityCd=13_118&prefCityCd=13_123&prefCityCd=13_121&prefCityCd=13_122&prefCityCd=13_109&prefCityCd=13_110&prefCityCd=13_111&prefCityCd=13_112&prefCityCd=13_114&prefCityCd=13_115&prefCityCd=13_120&prefCityCd=13_207&prefCityCd=13_228&prefCityCd=13_225&prefCityCd=13_205&prefCityCd=13_221&prefCityCd=13_215&prefCityCd=13_210&prefCityCd=13_214&prefCityCd=13_211&prefCityCd=13_219&prefCityCd=13_202&prefCityCd=13_224&prefCityCd=13_208&prefCityCd=13_229&prefCityCd=13_201&prefCityCd=13_227&prefCityCd=13_222&prefCityCd=13_213&prefCityCd=13_220&prefCityCd=13_212&prefCityCd=13_206&prefCityCd=13_218&prefCityCd=13_209&prefCityCd=13_204&prefCityCd=13_203&prefCityCd=13_223&hanbaiKakakuMin=&hanbaiKakakuMax=&tatemonoMensekiMin=&tatemonoMensekiMax=&tochiMensekiMin=&tochiMensekiMax=&tohoTime=&chikunensu=&tochiKenriCd=&isShinchaku=on&searchType=area&prefCd=13&type=kodate",
        type: "house",
        prefecture: "tokyo"
      },
      {
        url: "https://www.stepon.co.jp/search/list/?limit=100&pageNo=1&prefCityCd=14_101&prefCityCd=14_102&prefCityCd=14_103&prefCityCd=14_104&prefCityCd=14_105&prefCityCd=14_106&prefCityCd=14_107&prefCityCd=14_108&prefCityCd=14_109&prefCityCd=14_110&prefCityCd=14_111&prefCityCd=14_112&prefCityCd=14_113&prefCityCd=14_114&prefCityCd=14_115&prefCityCd=14_116&prefCityCd=14_117&prefCityCd=14_118&prefCityCd=14_131&prefCityCd=14_132&prefCityCd=14_133&prefCityCd=14_134&prefCityCd=14_135&prefCityCd=14_136&prefCityCd=14_137&prefCityCd=14_151&prefCityCd=14_152&prefCityCd=14_153&prefCityCd=14_402&prefCityCd=14_212&prefCityCd=14_218&prefCityCd=14_214&prefCityCd=14_215&prefCityCd=14_206&prefCityCd=14_204&prefCityCd=14_321&prefCityCd=14_216&prefCityCd=14_208&prefCityCd=14_207&prefCityCd=14_341&prefCityCd=14_342&prefCityCd=14_211&prefCityCd=14_203&prefCityCd=14_205&prefCityCd=14_301&prefCityCd=14_213&prefCityCd=14_201&hanbaiKakakuMin=&hanbaiKakakuMax=&tatemonoMensekiMin=&tatemonoMensekiMax=&tochiMensekiMin=&tochiMensekiMax=&tohoTime=&chikunensu=&tochiKenriCd=&isShinchaku=on&searchType=area&prefCd=14&type=kodate",
        type: "house",
        prefecture: "kanagawa"
      },
      {
        url: "https://www.stepon.co.jp/search/list/?limit=100&pageNo=1&prefCityCd=11_101&prefCityCd=11_102&prefCityCd=11_103&prefCityCd=11_104&prefCityCd=11_105&prefCityCd=11_106&prefCityCd=11_107&prefCityCd=11_108&prefCityCd=11_109&prefCityCd=11_110&prefCityCd=11_219&prefCityCd=11_227&prefCityCd=11_324&prefCityCd=11_225&prefCityCd=11_214&prefCityCd=11_203&prefCityCd=11_201&prefCityCd=11_465&prefCityCd=11_232&prefCityCd=11_222&prefCityCd=11_239&prefCityCd=11_215&prefCityCd=11_228&prefCityCd=11_246&prefCityCd=11_221&prefCityCd=11_207&prefCityCd=11_208&prefCityCd=11_224&prefCityCd=11_230&prefCityCd=11_212&prefCityCd=11_235&prefCityCd=11_245&prefCityCd=11_237&prefCityCd=11_234&prefCityCd=11_243&prefCityCd=11_223&hanbaiKakakuMin=&hanbaiKakakuMax=&tatemonoMensekiMin=&tatemonoMensekiMax=&tochiMensekiMin=&tochiMensekiMax=&tohoTime=&chikunensu=&tochiKenriCd=&isShinchaku=on&searchType=area&prefCd=11&type=kodate",
        type: "house",
        prefecture: "saitama"
      },
      {
        url: "https://www.stepon.co.jp/search/list/?limit=100&pageNo=1&prefCityCd=11_101&prefCityCd=11_102&prefCityCd=11_103&prefCityCd=11_104&prefCityCd=11_105&prefCityCd=11_106&prefCityCd=11_107&prefCityCd=11_108&prefCityCd=11_109&prefCityCd=11_110&prefCityCd=11_219&prefCityCd=11_227&prefCityCd=11_324&prefCityCd=11_225&prefCityCd=11_214&prefCityCd=11_203&prefCityCd=11_201&prefCityCd=11_465&prefCityCd=11_232&prefCityCd=11_222&prefCityCd=11_239&prefCityCd=11_215&prefCityCd=11_228&prefCityCd=11_246&prefCityCd=11_221&prefCityCd=11_207&prefCityCd=11_208&prefCityCd=11_224&prefCityCd=11_230&prefCityCd=11_212&prefCityCd=11_235&prefCityCd=11_245&prefCityCd=11_237&prefCityCd=11_234&prefCityCd=11_243&prefCityCd=11_223&hanbaiKakakuMin=&hanbaiKakakuMax=&tatemonoMensekiMin=&tatemonoMensekiMax=&tochiMensekiMin=&tochiMensekiMax=&tohoTime=&chikunensu=&tochiKenriCd=&isShinchaku=on&searchType=area&prefCd=11&type=kodate",
        type: "house",
        prefecture: "saitama"
      },
      {
        url: "https://www.stepon.co.jp/search/list/?prefCityCd=12_101&prefCityCd=12_102&prefCityCd=12_103&prefCityCd=12_104&prefCityCd=12_105&prefCityCd=12_106&prefCityCd=12_222&prefCityCd=12_203&prefCityCd=12_219&prefCityCd=12_231&prefCityCd=12_227&prefCityCd=12_217&prefCityCd=12_224&prefCityCd=12_212&prefCityCd=12_232&prefCityCd=12_205&prefCityCd=12_233&prefCityCd=12_220&prefCityCd=12_216&prefCityCd=12_208&prefCityCd=12_226&prefCityCd=12_204&prefCityCd=12_207&prefCityCd=12_221&prefCityCd=12_228&hanbaiKakakuMin=&hanbaiKakakuMax=&tatemonoMensekiMin=&tatemonoMensekiMax=&tochiMensekiMin=&tochiMensekiMax=&tohoTime=&chikunensu=&tochiKenriCd=&isShinchaku=on&searchType=area&prefCd=12&type=kodate",
        type: "house",
        prefecture: "chiba"
      }
    ];

    for (const task of tasks) {
      console.log("🔥 task🔥", task);



      // *********************************************************
      // Step 1: go page
      // 検索条件	物件種別 ： マンション（一棟）、ビル（一棟）、アパート（一棟）、投資用戸建、工場・倉庫、社宅・寮、ホテル・旅館、その他建物、土地　価格 ： 下限なし ～ 上限なし　専有面積 ： 下限なし ～ 上限なし　建物面積 ： 下限なし ～ 上限なし　土地面積 ： 下限なし ～ 上限なし　駅徒歩 ： 指定なし　築年数 ： 指定なし　利回り ： 指定なし
      await page.goto(
        // urls.toshi,
        task.url,
        // {
        //   waitUntil: "networkidle2",
        // }
      );

      await page.waitForSelector(".numDisplay");
      // await new Promise(resolve => setTimeout(resolve, 3000));
      // await page.type("#__BVID__13", REINS_USERNAME);
      // await page.type("#__BVID__16", REINS_PASSWORD);
      // await new Promise(resolve => setTimeout(resolve, 1000));
      // await page.click("#__BVID__20");

      // *********************************************************
      // Step 2: Reading data as needed

      // Step 2.1 Get total number of bukken
      const totalItemCount = await page.evaluate((sel: any) => {
        const res = document.querySelectorAll(sel).length;
        return res;
      }, ".resultItem");
      logger.info(`🔥 Total item count is ${totalItemCount}..`);

      // Step 3.2 Reading details
      const results: any[] = [];
      let newDataSelectors = {
        title: ".name h2 a",
        hpUrl: ".link a",
        layoutLink: ".floor .image img",
        description: ".lead span",
        price: ".info_wrapper dl:nth-child(1) dd",
        type: ".info_wrapper dl:nth-child(2) dd",

        item3: ".info_wrapper dl:nth-child(3)",
        item4: ".info_wrapper dl:nth-child(4)",
        item5: ".info_wrapper dl:nth-child(5)",
        item6: ".info_wrapper dl:nth-child(6)",
        item7: ".info_wrapper dl:nth-child(7)",
        item8: ".info_wrapper dl:nth-child(8)",
        item9: ".info_wrapper dl:nth-child(9)",
        // roi: ".rimawari em",
        // address: ".info_wrapper dl:nth-child(4) dd",

        imageLinks: ".thumbnail_wrapp img"
      } as any;

      if (task.type === "house") {
        newDataSelectors = {
          title: ".name h2 a",
          hpUrl: ".link a",
          layoutLink: ".floor .image img",
          description: ".lead span",
          price: ".info_wrapper dl:nth-child(1) dd",

          item2: ".info_wrapper dl:nth-child(2)",
          item3: ".info_wrapper dl:nth-child(3)",
          item4: ".info_wrapper dl:nth-child(4)",
          item5: ".info_wrapper dl:nth-child(5)",
          item6: ".info_wrapper dl:nth-child(6)",
          // roi: ".rimawari em",
          // address: ".info_wrapper dl:nth-child(4) dd",

          imageLinks: ".thumbnail_wrapp img"
        }
      }

      const mapper = {
        価格: "price",
        物件種別: "type",
        "想定利回り※": "roi",
        所在地: "address",
        交通: "transport",
        土地面積: "landSize",
        "間取り/建物面積": "layoutAndBuildingSize",
        築年月: "buildingBuiltYear",
        構造: "buildingMaterial",
      } as any;

      for (let i = 0; i < totalItemCount; i++) {
        logger.info(`🔥 Getting data for the ${i + 1}th result 🔥`);

        let currentResult = {
          ...(task.type === "house" ? {
            type: "戸建"
          } : {})
        } as any;

        for (const field of Object.keys(newDataSelectors)) {
          const selector = `.resultItem:nth-child(${i + 1}) ${newDataSelectors[field]
            }`;

          const newField = await page.evaluate(
            (sel: any, field: any, mapper: any) => {

              if (field === "imageLinks") {
                // convert https://www.stepon.co.jp/pub/img/retail/114016/14162046/14162046_1.jpg
                // to https://www.stepon.co.jp/pub/img/retail/114016/14162046/14162046_1_l.webp
                return {
                  [`${field}`]: Array.from(document?.querySelectorAll(sel))
                    .filter((img: any) => img.getAttribute('data-src') !== null)
                    .map((img: any) => "https://www.stepon.co.jp" + img.getAttribute('data-src').replace("_l.", ".").replace(".jpg", "_l.webp") || '')
                    .filter((src) => !!src && src.startsWith('http'))
                };
              }

              let res;
              if (field.indexOf("item") === -1) {
                res = document.querySelector(sel);
                if (res !== null && res !== undefined) {
                  if (field === "hpUrl") {
                    return { [`${field}`]: res.getAttribute("href") };
                  }

                  if (field === "layoutLink") {
                    return {
                      [`${field}`]: `https://www.stepon.co.jp${res.getAttribute(
                        "src"
                      )}`,
                    };
                  }

                  return {
                    [`${field}`]: res?.textContent?.replace(
                      /(\r\n\t|\n|\r\t|\s)/gm,
                      ""
                    ) || "",
                  };
                }
              }

              const resTitle = document.querySelector(`${sel} dt`);
              if (resTitle !== null) {
                const resTitleParsed = resTitle?.textContent?.replace(
                  /(\r\n\t|\n|\r\t|\s)/gm,
                  ""
                );

                if (mapper[resTitleParsed as any] !== undefined) {
                  res = document.querySelector(`${sel} dd`);

                  if (res !== null && res !== undefined) {
                    return {
                      [`${mapper[resTitleParsed as any]}`]: res?.textContent?.replace(
                        /(\r\n\t|\n|\r\t|\s)/gm,
                        ""
                      ) || "",
                    };
                  }
                }
              }
            },
            selector,
            field,
            mapper
          );

          currentResult = {
            ...newField,
            ...currentResult,
          };
        }

        logger.info("🔥 now processing results ...🔥");
        currentResult["sourceSite"] = "SUMITOMO";

        if (currentResult["price"] !== undefined) {
          if (currentResult["price"].indexOf("億円") !== -1) {
            currentResult["price"] =
              parseInt(currentResult["price"].split("億円")[0], 10) * 10000;
          } else if (currentResult["price"].indexOf("億") !== -1) {
            const okuCount = currentResult["price"].split("億")[0];
            const manCount = currentResult["price"].split("億")[1].replace(",", "");
            currentResult["price"] =
              parseInt(okuCount, 10) * 10000 + parseInt(manCount, 10);
          } else {
            currentResult["price"] = parseInt(
              currentResult["price"].replace(",", ""),
              10
            );
          }
        }

        if (currentResult["roi"] !== undefined) {
          currentResult["yearlyIncome"] =
            (currentResult["price"] *
              parseFloat(currentResult["roi"].replace("%", ""))) /
            100;
        }

        if (currentResult["landSize"] !== undefined) {
          currentResult["landSize"] = parseFloat(
            currentResult["landSize"].split("m²")[0]
          );
        }

        if (currentResult["transport"] !== undefined) {
          currentResult["nearestStation"] = currentResult["transport"]
            .split("「")[1]
            .split("」")[0];

          currentResult["nearestStationWalkMinute"] = await getStationWalkMinuteFromAddress(currentResult["address"]);
        }

        if (currentResult["buildingBuiltYear"] !== undefined) {
          currentResult["buildingBuiltYear"] = parseInt(
            currentResult["buildingBuiltYear"].split("年")[0]
          );
        }

        if (currentResult["layoutAndBuildingSize"] !== undefined) {
          currentResult["layout"] =
            currentResult["layoutAndBuildingSize"].split("/")[0];

          currentResult["buildingSize"] = parseFloat(
            currentResult["layoutAndBuildingSize"].split("/")[1].split("m²")[0]
          );

          delete currentResult["layoutAndBuildingSize"];
        }

        if (currentResult["type"] !== "その他建物（区分）") {
          // fix: その他建物 include both 区分 and 一棟
          logger.info("🔥 currentResult🔥", currentResult);
        }

        results.push(currentResult);
      }

      // *********************************************************
      // Step 3: Saving data
      // *********************************************************
      // let url = "";

      // let res = await postRequest(url, JSON.stringify(results));
      // writeFileSync(`sumitomo-${dayjs().format("YYYYMMDDHHmmss")}.json`, JSON.stringify(results, null, 2));
      await saveIntoDb(results);
    }
  } catch (err: any) {
    await sendLark({
      message: `[Sumitomo][Error getting data]` + err,
      url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
    });
    throw err;
  }

  return [];
};
