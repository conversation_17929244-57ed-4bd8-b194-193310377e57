

import { getPageWithRandomUserAgent, getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { saveIntoDb } from "../saveIntoDb";
import { logger } from "@/lib/logger";
import { NextResponse } from "next/server";
import { writeFileSync } from "fs";
import dayjs from "dayjs";
import { getStationWalkMinuteFromAddress } from "@/lib/helper/geo";

async function gotoPageWithRetry(page: any, url: any, options: any, retries = 3) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      await page.goto(url, options);
      return;
    } catch (error: any) {
      logger.error(`Attempt ${attempt} failed: ${error.message}`);
      if (attempt === retries) throw error;
    }
  }
}

export async function getMitsubishiData(page: any): Promise<any[]> {
  try {
    // 使用重试机制访问页面
    await gotoPageWithRetry(
      page,
      "https://www.sumai1.com/buyers/investor/tod_131/?price_from=&price_to=&senyu_from=&senyu_to=&tochi_from=&tochi_to=&new=1&todofuken_cd%5B%5D=131&todofuken_cd%5B%5D=132&todofuken_cd%5B%5D=14&todofuken_cd%5B%5D=12&todofuken_cd%5B%5D=11&inv_bukken_shumoku%5B%5D=2&inv_bukken_shumoku%5B%5D=3&inv_bukken_shumoku%5B%5D=4&inv_bukken_shumoku%5B%5D=5&inv_bukken_shumoku%5B%5D=7&inv_bukken_shumoku%5B%5D=8&inv_bukken_shumoku%5B%5D=99",
      { waitUntil: "domcontentloaded" }
    );

    // FIXME* somehow below will hang

    // await new Promise(resolve => setTimeout(resolve, 3000));
    const COUNT_SELECTOR = ".mod-list-estate-body .list > ul > li";

    // *********************************************************
    // Step 2: Reading data as needed

    // Step 2.1 Get total number of bukken
    const totalItemCount = await page.evaluate((sel: any) => {
      const res = document.querySelectorAll(sel).length;
      return res;
    }, COUNT_SELECTOR);
    logger.info(`🔥 Total item count is ${totalItemCount}..`);

    // Step 3.2 Reading details
    const results = [];
    const newDataSelectors = {
      type: ".heading .category",
      title: ".heading h2",
      description: ".sentence",

      hpUrl: ".heading h2 > a",
      price: ".pc-tb-only table .price td:nth-child(2) strong",
      roi: ".pc-tb-only table .price td:nth-child(4) strong", // convert to yearlyIncome

      address: ".pc-tb-only table .place td",
      transport: ".pc-tb-only table .access td",
      buildingSize:
        ".pc-tb-only table:nth-child(2) tr:nth-child(1) td:nth-child(2)",
      landSize:
        ".pc-tb-only table:nth-child(2) tr:nth-child(2) td:nth-child(2)",
      // layout: ".dim .info:nth-child(3) dd",
      buildingBuiltYear:
        ".pc-tb-only table:nth-child(2) tr:nth-child(1) td:nth-child(4)",
      buildingMaterial:
        ".pc-tb-only table:nth-child(2) tr:nth-child(2) td:nth-child(4)",
      // buildingLevel: ".dim .info:nth-child(6) dd",

      imageLinks: ".photos:nth-child(2) ul > li img",
    } as any;
    // For lazy loading image //
    async function autoScroll(page: any) {
      await page.evaluate(async () => {
        await new Promise((resolve) => {
          let totalHeight = 0;
          const distance = 200;
          const timer = setInterval(() => {
            window.scrollBy(0, distance);
            totalHeight += distance;

            if (totalHeight >= document.body.scrollHeight) {
              clearInterval(timer);
              resolve(void 0);
            }
          }, 100);
        });
      });
    }

    // Lazy loaded images needscroll
    await autoScroll(page); // 触发 lazyload
    await new Promise(resolve => setTimeout(resolve, 2000)); // 给图片一点加载时间

    for (let i = 0; i < totalItemCount; i++) {
      logger.info(`🔥 Getting data for the ${i + 1}th result 🔥`);

      let currentResult = {} as any;

      for (const field of Object.keys(newDataSelectors)) {
        const selector = `${COUNT_SELECTOR}:nth-child(${i + 1}) ${newDataSelectors[field]
          }`;

        const newField = await page.evaluate(
          (sel: any, field: any) => {

            if (field === "imageLinks") {
              // convert https://www.stepon.co.jp/pub/img/retail/114016/14162046/14162046_1.jpg
              // to https://www.stepon.co.jp/pub/img/retail/114016/14162046/14162046_1_l.webp
              return {
                [`${field}`]: Array.from(document?.querySelectorAll(sel))
                  .filter((img: any) => img.getAttribute('src') !== null && img.getAttribute('src').indexOf("noimage") === -1)
                  .map((img: any) => "https://www.sumai1.com" + img.getAttribute('src') || '')
              };
            }

            const res = document.querySelector(sel);
            if (res !== null && res !== undefined) {
              if (field === "hpUrl") {
                return {
                  [`${field}`]: `https://www.sumai1.com${res.getAttribute(
                    "href"
                  )}`,
                };
              }

              if (
                (field === "landSize" || field === "buildingSize") &&
                res?.textContent?.indexOf("m²") !== -1
              ) {
                return {
                  [`${field}`]: parseFloat(res?.textContent?.split("m2")[0] || "0"),
                };
              }

              return {
                [`${field}`]: res?.textContent?.replace(
                  /(\r\n\t|\n|\r\t|\s)/gm,
                  ""
                ) || "",
              };
            }

            return null;
          },
          selector,
          field
        );

        currentResult = {
          ...newField,
          ...currentResult,
        } as any;
      }

      currentResult["sourceSite"] = "MITSUBISHI";

      if (currentResult["price"] !== undefined) {
        if (currentResult["price"].indexOf("億円") !== -1) {
          currentResult["price"] =
            parseInt(currentResult["price"].split("億円")[0], 10) * 10000;
        } else if (currentResult["price"].indexOf("億") !== -1) {
          const okuCount = currentResult["price"].split("億")[0];
          const manCount = currentResult["price"].split("億")[1].replace(",", "");
          currentResult["price"] =
            parseInt(okuCount, 10) * 10000 + parseInt(manCount, 10);
        } else {
          currentResult["price"] = parseInt(
            currentResult["price"].replace(",", ""),
            10
          );
        }

        if (currentResult["roi"] !== undefined) {
          currentResult["yearlyIncome"] = parseFloat(((currentResult["price"] * parseFloat(currentResult["roi"] || "0")) / 100).toFixed(0))
        }
      }

      if (currentResult["transport"] !== undefined) {
        currentResult["nearestStation"] = currentResult["transport"]
          .split("「")[1]
          .split("」")[0];

        currentResult["nearestStationWalkMinute"] = await getStationWalkMinuteFromAddress(currentResult["address"]);
      }

      if (currentResult["buildingBuiltYear"] !== undefined) {
        currentResult["buildingBuiltYear"] = parseInt(
          currentResult["buildingBuiltYear"].split("年")[0]
        );
      }

      if (
        currentResult["buildingMaterial"] !== undefined &&
        currentResult["buildingMaterial"].indexOf("地上") > -1
      ) {
        [currentResult["buildingMaterial"], currentResult["buildingLevel"]] =
          currentResult["buildingMaterial"].split("地上");
      }

      logger.info("🔥 now processing results ...🔥");

      logger.info("🔥 currentResult🔥", currentResult);
      results.push(currentResult);
    }
    logger.info("🔥 Complete reading results... length is " + results.length);

    // *********************************************************
    // Step 3: Saving data
    // *********************************************************
    // let url = "";

    // writeFileSync(`mitsubishi-${dayjs().format("YYYYMMDDHHmmss")}.json`, JSON.stringify(results, null, 2));
    await saveIntoDb(results);

    return results;
  } catch (err: any) {
    await sendLark({
      message: `[Mitsubushi][🔥 Error getting data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    throw err;
  }
};
