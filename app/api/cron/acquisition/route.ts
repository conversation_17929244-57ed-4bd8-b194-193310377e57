import { logger } from "@/lib/logger";
import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { NextResponse } from "next/server";
import { saveIntoDb } from "./saveIntoDb";
import { getSumitomoData } from "./sites/sumitomo";
import { getNomuData } from "./sites/nomu";
import { getLivableData } from "./sites/livable";
import { getMitsubishiData } from "./sites/mitsubishi";
import { getMitsuiData } from "./sites/mitsui";
import { prisma } from "@/lib/prisma";
import dayjs from "dayjs";

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  if (req.method !== "GET") {
    return NextResponse.json({ error: "Method Not Allowed" }, { status: 405 });
  }

  const url = new URL(req.url); // 创建 URL 对象
  const taskIndex = url.searchParams.get("taskIndex"); // 获取查询参数

  let browser = await getChromeBrowser();
  let page: any;

  const retryTaskIfFail = async (task: () => Promise<any[]>, retries = 2) => {
    let pass = false;
    for (let i = 0; i < retries; i++) {
      if (pass) {
        break;
      }

      try {
        let res = await task();
        pass = true;
        return res;
      } catch (err) {
        logger.error("🔥 Error in cron job.. retrying once 🔥", err);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    return [];
  };

  try {
    page = await getPageWithRandomUserAgent(browser);

    let currentMinute = dayjs().minute();

    let combinedIndex = taskIndex ? parseInt(taskIndex) : Math.floor(currentMinute / 10);

    if (combinedIndex === 0) {
      await retryTaskIfFail(() => getLivableData(page) as Promise<any[]>);
    } else if (combinedIndex === 1) {
      await retryTaskIfFail(() => getSumitomoData(page) as Promise<any[]>);
    } else if (combinedIndex === 2) {
      await retryTaskIfFail(() => getNomuData(page) as Promise<any[]>);
    } else if (combinedIndex === 3) {
      await retryTaskIfFail(() => getMitsubishiData(page) as Promise<any[]>);
    } else if (combinedIndex === 4) {
      await retryTaskIfFail(() => getMitsuiData(page) as Promise<any[]>);
    }

    return NextResponse.json({ status: "success" }, { status: 200 });
  } catch (err: any) {
    logger.error("🔥 Error in cron job 🔥", err);

    await sendLark({
      message: `[Sumitomo][Error getting data]` + err,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });

    return NextResponse.json({ error: err }, { status: 500 });
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
