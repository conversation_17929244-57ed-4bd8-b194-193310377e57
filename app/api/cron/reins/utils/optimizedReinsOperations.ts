import { browserPool } from "@/lib/thirdParty/browserPool";
import { retryWithBackoff, reinsRetryOptions, pageRetryOptions } from "@/lib/utils/retryWithBackoff";
import { logger } from "@/lib/logger";
import { login } from "../utility/reinsLogin";

interface ReinsOperationContext {
  browser: any;
  page: any;
  isLoggedIn: boolean;
  account: string | null;
}

export class OptimizedReinsOperations {
  private context: ReinsOperationContext | null = null;

  async initializeContext(account: string | null): Promise<void> {
    try {
      const { browser, page } = await browserPool.getBrowserInstance();
      
      this.context = {
        browser,
        page,
        isLoggedIn: false,
        account
      };

      logger.debug("🔥 Initialized REINS operation context");
    } catch (error) {
      logger.error("🔥 Failed to initialize REINS context:", error);
      throw error;
    }
  }

  async ensureLoggedIn(): Promise<void> {
    if (!this.context) {
      throw new Error("Context not initialized");
    }

    if (!this.context.isLoggedIn) {
      await retryWithBackoff(async () => {
        await login({
          page: this.context!.page,
          account: this.context!.account,
        });
        this.context!.isLoggedIn = true;
        logger.debug("🔥 Successfully logged in to REINS");
      }, reinsRetryOptions);
    }
  }

  async navigateToSearchPage(): Promise<void> {
    if (!this.context) {
      throw new Error("Context not initialized");
    }

    await retryWithBackoff(async () => {
      await this.context!.page.goto("https://system.reins.jp/main/KG/GKG003100", {
        waitUntil: "networkidle2",
        timeout: 30000
      });

      // Step 1.1 Go to search page
      const searchButtonSelector = ".card:nth-child(2) > div > div > div:nth-child(1) button";
      await this.context!.page.waitForSelector(searchButtonSelector, { timeout: 15000 });
      await this.context!.page.click(searchButtonSelector);
      logger.debug("🔥 Navigated to search page");
      
      // 添加延迟以确保页面完全加载
      await new Promise(resolve => setTimeout(resolve, 3000));
    }, pageRetryOptions);
  }

  async safePageOperation<T>(operation: () => Promise<T>, operationName: string): Promise<T> {
    if (!this.context) {
      throw new Error("Context not initialized");
    }

    return await retryWithBackoff(async () => {
      try {
        return await operation();
      } catch (error) {
        logger.warn(`🔥 ${operationName} failed, will retry:`, error);
        throw error;
      }
    }, pageRetryOptions);
  }

  async waitForSelector(selector: string, timeout: number = 15000): Promise<void> {
    await this.safePageOperation(async () => {
      await this.context!.page.waitForSelector(selector, { timeout });
    }, `waitForSelector(${selector})`);
  }

  async click(selector: string): Promise<void> {
    await this.safePageOperation(async () => {
      await this.context!.page.click(selector);
      // 点击后添加小延迟
      await new Promise(resolve => setTimeout(resolve, 500));
    }, `click(${selector})`);
  }

  async evaluate<T>(pageFunction: any, ...args: any[]): Promise<T> {
    return await this.safePageOperation(async () => {
      return await this.context!.page.evaluate(pageFunction, ...args);
    }, 'evaluate');
  }

  async addRandomDelay(min: number = 1000, max: number = 3000): Promise<void> {
    const delay = min + Math.random() * (max - min);
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  async cleanup(): Promise<void> {
    if (this.context) {
      try {
        // 释放浏览器实例回池中而不是关闭
        await browserPool.releaseBrowserInstance(this.context.browser);
        logger.debug("🔥 Released REINS operation context");
      } catch (error) {
        logger.error("🔥 Error during cleanup:", error);
      } finally {
        this.context = null;
      }
    }
  }

  async forceCleanup(): Promise<void> {
    if (this.context) {
      try {
        if (this.context.page) {
          await this.context.page.close();
        }
        if (this.context.browser) {
          await this.context.browser.close();
        }
        logger.debug("🔥 Force cleaned up REINS operation context");
      } catch (error) {
        logger.error("🔥 Error during force cleanup:", error);
      } finally {
        this.context = null;
      }
    }
  }

  getContext(): ReinsOperationContext | null {
    return this.context;
  }

  isContextValid(): boolean {
    return this.context !== null && this.context.browser && this.context.page;
  }
}

// 工厂函数用于创建优化的操作实例
export async function createOptimizedReinsOperation(account: string | null): Promise<OptimizedReinsOperations> {
  const operation = new OptimizedReinsOperations();
  await operation.initializeContext(account);
  return operation;
}
