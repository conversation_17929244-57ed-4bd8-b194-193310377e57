import { prisma } from "@/lib/prisma";
import { getChromeBrowser, getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { REINS_TASKS } from "../constants/reinsTasks";
import { login } from "../utility/reinsLogin";
import { getSelectedDataForReinsRent } from "./getSelectedDataForReinsRent";
import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { ProMansionRentProps, ProMansionRentRecordType } from "@/lib/definitions/proMansionRent";
import { getMansionRecordOrCreateNewForNameFromMansionRent } from "../../reinsFill/building/getMansionRecordOrCreateNewForNameFromMansionRent";
import { GetStationName } from "@/lib/railwayStation";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { writeFileSync } from "fs";

import dayjs from "@/lib/thirdParty/dayjsWithTz";

export async function GET(req: Request): Promise<NextResponse> {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return NextResponse.json({
      status: "unauthorized"
    });
  }

  if (req.method !== "GET") {
    return NextResponse.json({
      status: "method_not_allowed"
    });
  }

  // let RENT_TASK = REINS_TASKS.MANSION_RENT;
  // let RENT_TASK = REINS_TASKS.BUILDING_PART_RENT;

  let browser = null;
  let page = null;
  let retries = 3;

  const url = new URL(req.url); // 创建 URL 对象
  const recordType = url.searchParams.get("recordType"); // 获取查询参数

  let currentHour = dayjsWithTz().hour();
  let currentMinute = dayjsWithTz().minute();

  // Wont work on local but should work on remote
  // FIXME: this wont complete... there are just too many data //
  const finalRecordType = recordType ? recordType : currentHour === 13 ? "MANSION_RENT" : "BUILDING_PART_RENT";
  let RENT_TASK = REINS_TASKS[finalRecordType];
  let filteredTasks = RENT_TASK.tasks.filter((task: any) => task.taskIndex === Math.floor(currentMinute / 15));

  logger.info("🔥 finalRecordType is: ", finalRecordType);

  try {
    browser = await getChromeBrowser();
    page = await getPageWithRandomUserAgent(browser);

    let taskIndex = 0;
    for (let task of filteredTasks) {
      // FIXME: should not need new browser for each new tasks
      let allRecordsCralwed = [] as ProMansionRentProps[];

      try {
        for (let i = 0; i < retries; i++) {
          try {
            allRecordsCralwed = await getSelectedDataForReinsRent({
              page,
              task,
              selectors: RENT_TASK.selectors,
            }) as ProMansionRentProps[];

            break; // Do not trigge retry
          } catch (err: any) {
            console.log("🔥 Error during getting selector data 🔥");
            console.error(err);

            await sendLark({
              message: `❌${i >= 1 ? "❌" : ""}${i >= 2 ? "❌" : ""}[Mansion rent task][${++taskIndex}/${RENT_TASK.tasks.length}][Error getting selector data, retry]` +
                err,
              url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
            });
          }
        }
      } catch (err: any) {
        console.log("🔥 Error during getting selector data 🔥");
        console.error(err);

        await sendLark({
          message: `[Mansion rent task][${++taskIndex}/${RENT_TASK.tasks.length}][Error getting selector data]` +
            err,
          url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
        });
      }

      // writeFileSync(`111-${dayjs().format("YYYYMMDDHHmmss")}.json`, JSON.stringify(allRecordsCralwed, null, 2));

      let index = 0;
      let createdCount = 0;
      for (const recCrawled of allRecordsCralwed) {
        // Fixme: price might drop wth same reins number
        let matchedRecord = await prisma.proMansionRent.findFirst({
          where: {
            brokerReinsNumber: recCrawled.brokerReinsNumber,
          },
        });

        logger.info(`🔥 saving ${++index} of ${allRecordsCralwed.length}, ${matchedRecord?.id ? "has match so skip" : "no match so create...."} 🔥`);

        if (matchedRecord === null) {
          const matchRailwayStationGroup = await prisma.geoRailwayStation.findMany({
            where: {
              name: recCrawled.nearestStation,
              prefectureCode: { in: [11, 12, 13, 14] }, // sometimes property in 埼玉 but nearest station is in 東京
            },
          });

          matchedRecord = await prisma.proMansionRent.create({
            data: {
              recordType: finalRecordType === "MANSION_RENT" ? "MANSION" : "BUILDING_PART",
              propertyType: recCrawled.propertyType,

              brokerReinsNumber: recCrawled.brokerReinsNumber, // do not save the same number 
              brokerType: recCrawled.brokerType,
              brokerStatus: recCrawled.brokerStatus,

              brokerListingCompany: recCrawled.brokerListingCompany,
              brokerListingCompanyNumber: recCrawled.brokerListingCompanyNumber,

              feeRent: recCrawled.feeRent,
              feeManagement: recCrawled.feeManagement,
              feeUtility: recCrawled.feeUtility,
              feeGiftMoney: recCrawled.feeGiftMoney,
              feeDepositMoney: recCrawled.feeDepositMoney,

              landType: recCrawled.landType,
              unitSize: recCrawled.unitSize,
              unitLayout: recCrawled.unitLayout,
              unitLevel: recCrawled.unitLevel || null,

              buildingBuiltYear: recCrawled.buildingBuiltYear || null,
              buildingAddress: recCrawled.buildingAddress,
              buildingName: recCrawled.buildingName,

              ...(matchRailwayStationGroup.length > 0 ? { nearestStationGroupId: matchRailwayStationGroup[0].stationGroupId } : {}),

              transport: recCrawled.transport,
              nearestStation: recCrawled.nearestStation,
              nearestStationWalkMinute: recCrawled.nearestStationWalkMinute,

              roadConnection: recCrawled.roadConnection,
              roadConnectionFirstFacing: recCrawled.roadConnectionFirstFacing,
            } as any,
          });
          createdCount++;
        }

        if (recCrawled.buildingName) {
          const { recordId, isNew, record } = await getMansionRecordOrCreateNewForNameFromMansionRent({
            isKubun: finalRecordType === "MANSION_RENT" ? true : false,
            name: recCrawled.buildingName || "",
            address: recCrawled.buildingAddress || "", // wont be "" as it is a must have field 
            nearestStation: recCrawled.nearestStation || "",
          }) as {
            recordId: string,
            isNew: boolean,
            record?: ProBuildingProps,
          };

          let stationName = null;
          if (recCrawled.transport !== null && recCrawled.transport !== "" && recCrawled.transport !== undefined) {
            stationName = await GetStationName(
              recCrawled.transport
            );
          }

          if (recordId) {
            await prisma.proMansionRent.update({
              where: { id: matchedRecord.id },
              data: {
                buildingId: recordId,
                // FIXME:check to see if this can come from reins 
                ...(record?.longitude && { locationLongitude: record.longitude }),
                ...(record?.latitude && { locationLatitude: record.latitude }),
                ...(record?.postalCode && { locationPostalCode: record.postalCode.toString() }),
                nearestStation: stationName,
              },
            });
          }
        }
      }

      await sendLark({
        message: `[⚙️][${finalRecordType}][${task.title}][${++taskIndex} / ${filteredTasks.length}] ${allRecordsCralwed.length} scanned | ${createdCount} created`,
        url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
      });
    }

    return NextResponse.json({
      status: "success"
    });
  } catch (err: any) {
    logger.error("🔥 err", err);

    await sendLark({
      message: `[⚙️][❌][FILL Mansion Rent] ${JSON.stringify(err)}`,
      url: LARK_URLS.BOT_SHUUKEI_CHANNEL,
    });
    return NextResponse.json({
      status: "error",
      message: err
    });
  } finally {
    if (page) {
      await page.close();
    }

    if (browser) {
      await browser.close();
    }

    await prisma.$disconnect(); // prevent long hanging connection
  }
};
