import { logger } from "@/lib/logger";

export async function getSelectorRentDataForOneBlock({
  page,
  selectors,
  index
}: {
  page: any,
  selectors: any,
  index: any
}) {
  let BLOCK = selectors;
  const BLOCK_SELECTOR = ".p-table-body-row:nth-child(INDEX)";

  let currentRecord = {} as any;
  const currentSelector = BLOCK_SELECTOR.replace("INDEX", index); // First item is clearfix box_function

  // Step 1: Getting all detailed data
  for (const field of Object.keys(BLOCK)) {
    if (BLOCK[field] === "") continue;

    const selector = `${currentSelector} ${BLOCK[field]}`;
    let currentValue = null as any;

    currentValue = await page.evaluate(
      (sel: any, field: any) => {
        const res = document.querySelector(sel);
        if (res !== null) {
          if (field === "nearestStation") {
            if (res?.textContent && res?.textContent?.length > 0 && res?.textContent?.indexOf("　") > -1) {
              return res?.textContent?.split("　")[1] ?? "";
            }
          }

          return res.textContent.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
        }

        return res ?? "";
      },
      selector,
      field
    );

    // STEP 3.2 Clean Up
    if (currentValue !== null && currentValue !== "") {
      if (field === "landSize" || field === "buildingSize") {
        currentValue = parseFloat(currentValue?.replace("m2", "") ?? "");
      }

      if (field === "buildingName") {
        currentValue = currentValue.replace("１ＬＤＫ", "").replace("２ＬＤＫ", "").replace("３ＬＤＫ", "").replace("４ＬＤＫ", "").replace("５ＬＤＫ", "");
      }

      if (field === "feeRent") {
        currentValue = parseFloat(currentValue.replace(/,/g, '').replace("万円", ""));
      }

      if (field === "feeManagement" || field === "feeUtility") {
        currentValue =
          currentValue === "-" || currentValue === "なし"
            ? 0
            : parseInt(currentValue.replace("円", "").replace(",", ""));
      }

      if (field === "unitSize") {
        currentValue = parseFloat(currentValue.split("m2")[0]);
      }

      if (field === "buildingBuiltYear") {
        currentValue = parseInt(currentValue.split("年")[0]);
      }

      if (field === "unitLevel") {
        currentValue = parseInt(currentValue.split("階")[0]);
      }
    }

    currentRecord[field] = currentValue;
  }

  return currentRecord;
};
