import { prisma } from "@/lib/prisma";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { TllUserLambdaRecordPriceChangeSourceType } from "@/lib/definitions";
import { UserLambdaRecordProps, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { getCompositeTitle, toFullWidthAddress } from "./utility/getCompositeTitle";
import { logger } from "@/lib/logger";
import { localPathToBlob, uploadFileToPropertyMaterialsBucketFromCron } from "@/actions/helper/supabaseNode";
import { ReinsTaskConstantProps } from "./constants/reinsTasks";
import { TllUserLambdaRecordRecordType } from "@prisma/client";
import dayjs from "@/lib/thirdParty/dayjsWithTz";
import { fillPrefectureAreaPostalCode } from "@/actions/geoPrefecture";
import { fillRecordAnalysis } from "../reinsFill/recordAnalysis/fillRecordAnalysis";
import { fillNearestStationGroupIdWalkMinute } from "@/actions/geoRailwayStationGroups";

import { getMansionRecordOrCreateNewForName } from "../reinsFill/building/getMansionRecordOrCreateNewForName";

async function getCompanyIdAndSaveChangeRecords(
  matchUserLambdaRecordId: string,
  rec: any,
  status: string,
  soldOnly: boolean
) {

  let companyId = null;

  if (!soldOnly) {
    const rows = await prisma.proCompany.findMany({
      where: {
        fullName: rec.listingCompany,
      },
    });

    if (rows.length) {
      companyId = rows[0].id;
      logger.info(`🔥[Reins][PriceChange] using old company, id is ${companyId} 🔥`);
    } else {
      // Create new company, but not creating for 成約
      if (rec.listingCompany !== undefined) {
        const result = await prisma.proCompany.create({
          data: {
            fullName: rec.listingCompany,
            contactNumber: rec.listingCompanyPhoneNumber || null,
          },
        });
        if (result.id) {
          companyId = result.id;
          logger.info(`🔥[Reins][PriceChange] creating new company, id is ${companyId} 🔥`);
        }
      }
    }
  }

  const dataToInsert = {
    recordId: matchUserLambdaRecordId,
    companyId: companyId,
    recordDate: dayjs().toDate(),
    price: rec.price,
    yearlyIncome: rec.yearlyIncome || null,
    status: status,
    source: TllUserLambdaRecordPriceChangeSourceType.REINS,

    brokerType: rec.brokerType !== undefined ? rec.brokerType : "",
    reinsNumber: rec.reinsNumber !== undefined ? rec.reinsNumber : "",
    canAdvertise: rec.canAdvertise !== undefined ? rec.canAdvertise : "",
    chirashiLink: rec.link,
    chirashiType: rec.chirashiType || null,
    comments: `${rec.comments} [Company at ${rec.listingCompany !== undefined ? rec.listingCompany : ""
      }][HP: ${rec.listingCompanyPhoneNumber !== undefined
        ? rec.listingCompanyPhoneNumber
        : ""
      }]${rec.statusChangeDetails !== undefined
        ? `[${rec.statusChangeDetails}]`
        : ""
      }`,
  };

  const result = await prisma.tllUserLambdaRecordPriceChange.create({
    data: dataToInsert
  })

  logger.info("🔥[Reins][PriceChange] change record, dataToInsert is", dataToInsert, "created new, id is", result.id);
}

export async function saveUserLambdaResult({ recordType, currentRecord, soldOnly }: { recordType: string, currentRecord: any, soldOnly: boolean }) {
  let createdRecordsCount = 0;
  let createdRecordsS3SavedCount = 0;
  let updatedRecordsCount = 0;

  const compositeTitle = getCompositeTitle(currentRecord, recordType as UserLambdaRecordType, "REINS");

  console.log("🔥[Reins][PriceChange] recordType is", recordType);
  console.log("🔥[Reins][PriceChange] compositeTitle is", compositeTitle);

  const matchedRecord = await prisma.tllUserLambdaRecord.findUnique({
    where: {
      compositeTitle_recordType: {
        compositeTitle: compositeTitle,
        recordType: recordType as TllUserLambdaRecordRecordType,
      },
    },
  });

  if (!matchedRecord) {
    logger.info(
      `[Reins PriceChange][Case 1][New] No value for ${compositeTitle}, creating new record...`
    );

    const status = soldOnly ? "成約" : currentRecord.status;

    let getLandTypeFromPropertyType = (propertyType: string) => {
      if (propertyType.includes("底地")) return "底地権";
      if (propertyType.includes("借地")) return "借地権";

      return "所有権";
    }

    const getMinutes = (str: string) => {
      const matches = str.match(/徒歩(\d+)分/);
      if (matches && matches.length === 2) {
        return parseInt(matches[1], 10);
      }
      return null;
    }

    const getStationGroupId = async (station: string) => {
      const match = await prisma.geoRailwayStation.findMany({
        where: {
          name: station,
          prefectureCode: { in: [11, 12, 13, 14] }, // sometimes property in 埼玉 but nearest station is in 東京
        },
      });

      if (match.length > 0) {
        return match[0].stationGroupId;
      }

      return null;
    }

    const valueToCreate =
    {
      sourceData: "REINS",

      compositeTitle: compositeTitle,
      price: currentRecord.price,
      yearlyIncome: currentRecord.yearlyIncome === undefined ||
        currentRecord.yearlyIncome === null ||
        currentRecord.yearlyIncome === ""
        ? null
        : currentRecord.yearlyIncome,

      transport: currentRecord.transport || null,
      nearestStation: currentRecord.nearestStation || null,
      nearestStationWalkMinute: currentRecord.nearestStationWalkMinute && currentRecord.nearestStationWalkMinute !== "" ? getMinutes(currentRecord.nearestStationWalkMinute) : null,
      nearestStationGroupId: currentRecord.nearestStation && currentRecord.nearestStation !== "" ? await getStationGroupId(currentRecord.nearestStation) : null,

      recordType: recordType,
      recordSubType: currentRecord.propertyType || null, // using old name because that is how it was defined in the job

      address: currentRecord.address,
      recordValues: currentRecord,

      landRight: recordType === "LAND" ? getLandTypeFromPropertyType(currentRecord.propertyType) : currentRecord.landRight || "所有権",
      landSize: currentRecord.landSize || null,
      landFloorAreaRatio: currentRecord.landFloorAreaRatio !== undefined
        ? parseInt(currentRecord.landFloorAreaRatio)
        : null, // using old name because that is how it was defined in the job
      landBuildingCoverageRatio: currentRecord.landBuildingCoverageRatio !== undefined
        ? parseInt(currentRecord.landBuildingCoverageRatio)
        : null, // using old name because that is how it was defined in the job
      landType: currentRecord.landType || null,
      roadConnection: currentRecord.roadConnection || null,
      roadConnectionFirstType: currentRecord.roadConnectionFirstType || null,
      roadConnectionFirstFacing: currentRecord.roadConnectionFirstFacing || null,
      roadConnectionFirstWidth: currentRecord.roadConnectionFirstWidth || null,
      roadConnectionSecondType: currentRecord.roadConnectionSecondType || null,
      roadConnectionSecondFacing: currentRecord.roadConnectionSecondFacing || null,
      roadConnectionSecondWidth: currentRecord.roadConnectionSecondWidth || null,
      salesComments: currentRecord.comments || null,

      buildingSize: currentRecord.buildingSize || currentRecord.unitArea || null,
      buildingLayout: currentRecord.buildingLayout || null,
      buildingMaterial: currentRecord.buildingMaterial || null,
      buildingName: currentRecord.buildingName || null,
      buildingBuiltYear: currentRecord.buildingBuiltYear || null,
      buildingLevel: currentRecord.buildingLevel || currentRecord.unitLevel || null,
      buildingRoomCount: currentRecord.buildingRoomCount ? parseInt(currentRecord.buildingRoomCount) : null,
    }

    logger.debug("🔥 [Creating][REINS] valueToCreate is", valueToCreate);
    const result = await prisma.tllUserLambdaRecord.create({
      data: valueToCreate as any
    });

    if (currentRecord["linkFileName"] !== undefined) {
      let file = await localPathToBlob(currentRecord["linkFileName"]) as {
        success: boolean,
        data: {
          blob: Blob,
          name: string,
        },
      };
      logger.info("🔥 file is", file);

      if (file.success) {
        let res = await uploadFileToPropertyMaterialsBucketFromCron({
          file: file.data,
          recordId: result.id
        });
        if (res.success && res.data?.fullPath) {
          // This is not saved for currentRecord, instead it is saved for the changed records 
          currentRecord["link"] = `https://waqahdtjadldhstauanr.supabase.co/storage/v1/object/public/${res.data.fullPath}`;
          createdRecordsS3SavedCount = 1;
        }
      } else {
        throw new Error(`🚨 Error in uploadFileToPropertyMaterialsBucket: ${JSON.stringify(file)}`);
      }
    }

    if (result.id) {
      logger.debug(
        `🔥[Reins][PriceChange][Creating][REINS] Record created id is ${result.id}, next is creating changeRecord...`
      );

      await getCompanyIdAndSaveChangeRecords(
        result.id,
        currentRecord,
        status,
        soldOnly
      );

      await fillPrefectureAreaPostalCode(result.id);
      // await fillNearestStationGroupIdWalkMinute(result.id);

      if (recordType === "MANSION" && currentRecord.buildingName) {
        let updated = await prisma.tllUserLambdaRecord.findUnique({
          where: {
            id: result.id,
          },
        }) as UserLambdaRecordProps;

        const {
          recordId,
          isNew,
        } = await getMansionRecordOrCreateNewForName({
          isKubun: true,
          currentRecord: currentRecord,
          nameJa: updated.buildingName || updated.recordValues?.title || "",
          address: updated.address || "", // wont be "" as it is a must have field 
          nearestStation: updated.nearestStation || "",
          nearestStationGroupId: updated.nearestStationGroupId || "",
        }) as any;

        if (recordId) {
          await prisma.tllUserLambdaRecord.update({
            where: { id: updated.id },
            data: {
              buildingId: recordId,
            },
          });
        }
      }
    }

    createdRecordsCount = 1;
  }
  else {
    logger.info("🔥 [Reins PriceChange][Case 2 & 3][Existing] record found, id is", matchedRecord.id);

    const status = soldOnly ? "成約" : currentRecord.status;
    const priceChanges = await prisma.tllUserLambdaRecordPriceChange.findMany({
      where: {
        recordId: matchedRecord.id,
        price: currentRecord.price,
        reinsNumber: currentRecord.reinsNumber, // so essentially each company will be a different one 
      },
    });

    let mostRecentPriceChange = priceChanges.sort((a, b) => dayjs(b.recordDate).diff(dayjs(a.recordDate))).shift();

    // Will save in case of
    // 1) same reins but status changed e.g. from 申込あり to 公開中
    // 2) different reins, or different price
    // Ensure priceChanges exists before checking length
    if (
      priceChanges &&
      priceChanges.length > 0 &&
      mostRecentPriceChange?.status === status
    ) {
      // No changes detected
      // Fix: this will have a bug for seiyaku (as you get seiyaku for P3D, so if there is still a company,e.g. nomura thati s putting on their website, then it will have multiple records)
      logger.info(
        `🔥 [Reins PriceChange][case 2][Existing][Do nothing] record - no change - price / reinsNumber / recordStatus] record id is ${matchedRecord.id}, [Current] reins id = ${currentRecord.reinsNumber}, priceChange id is ${priceChanges.map(p => p.id)}🔥`
      );
    } else {
      logger.info(
        `🔥 [Reins PriceChange][case 3][Existing][Update Price ${matchedRecord.price} => ${currentRecord.price}] or reinsChange, or status change. Find value for ${compositeTitle} id is ${matchedRecord.id}, updating field instead and creating changeRecord...`
      );

      logger.info("🔥 currentRecord[linkFileName] is", currentRecord["linkFileName"]);

      if (currentRecord["linkFileName"] !== undefined) {
        logger.info("🔥 saving link");
        let file = await localPathToBlob(currentRecord["linkFileName"]) as {
          success: boolean,
          data: {
            blob: Blob,
            name: string,
          },
        };

        logger.info("🔥 file is", file);
        if (file.success) {
          let res = await uploadFileToPropertyMaterialsBucketFromCron({
            file: file.data,
            recordId: matchedRecord.id
          });
          logger.info("🔥 res is", res);

          if (res.success) {
            currentRecord["link"] = `https://waqahdtjadldhstauanr.supabase.co/storage/v1/object/public/${res.data.fullPath}`;
            createdRecordsS3SavedCount = 1;
          }
        } else {
          throw new Error(`🚨 Error in localPathToBlob: ${JSON.stringify(file)}`);
        }
      }

      await getCompanyIdAndSaveChangeRecords(
        matchedRecord.id,
        currentRecord,
        status,
        soldOnly
      );

      await prisma.tllUserLambdaRecord.update({
        where: { id: matchedRecord.id },
        data: {
          price: currentRecord.price,
          ...(currentRecord.yearlyIncome && { yearlyIncome: currentRecord.yearlyIncome }),
          // Do nothing about updated at, WILL BE UPDATED
        },
      });

      updatedRecordsCount = 1;
    }
  }

  return [createdRecordsCount, createdRecordsS3SavedCount, updatedRecordsCount];
}