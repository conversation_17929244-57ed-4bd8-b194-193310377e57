
import { Page } from "puppeteer";
import { afterClickDownloadFileAndGetFilename, getDownloadPathInFunction } from "@/lib/thirdParty/chromeBrowser";
import { logger } from "@/lib/logger";

export async function getDataForOneBlock<PERSON>hirashi(page: Page, currentRecord: any, currentSelector: string, BLOCK: any) {
  // Step 1: Downloading the file
  const downloadPath = await getDownloadPathInFunction(page);

  // Hard code
  let linkSelector = `${currentSelector} ${BLOCK["link"]}`;
  // const gaiyouSelector = `${currentSelector} ${BLOCK["link2"]}`;

  // First folow the link (which is ususally the 図面 button for REINS)
  let currentValue = await page.evaluate((sel) => {
    const res = document.querySelector(sel);
    return res === null || res === undefined ? "" : res.textContent;
  }, linkSelector);


  // Download the file if the button exist, prefer 図面, but if no existingm then get 概要
  if (currentValue?.length === 0 || currentValue !== "図面") {
    logger.warn("🔥 図面 Not exist, thus skipping... ");
    return currentRecord;
    // currentValue = await page.evaluate((sel) => {
    //   const res = document.querySelector(sel);
    //   return res === null || res === undefined ? "" : res.textContent;
    // }, gaiyouSelector);

    // if (currentValue && currentValue.length > 0 && currentValue === "概要") {
    //   linkSelector = gaiyouSelector;
    //   currentRecord["chirashiType"] = "概要";
    // }
  } else {
    logger.warn("🔥 図面 button exist, clicking on it...🔥");
    currentRecord["chirashiType"] = "図面";
  }
  logger.debug("🔥 linkSelector", linkSelector);

  const linksForThisSection = await page.$$(`${linkSelector} button`);
  if (linksForThisSection.length > 0) {
    // Remove the bottom floating bar, so as not to click on "印刷" by mistake
    await page.evaluate((sel) => {
      const elements = document.querySelectorAll(sel);
      for (let i = 0; i < elements.length; i++) {
        elements[i]?.parentNode?.removeChild(elements[i]);
      }
    }, ".p-frame-footer");
    await new Promise(resolve => setTimeout(resolve, 1000));
    logger.debug("🔥 Removed the bottom floating bar.. clicking on the link");

    const {
      localFilePath,
      newFileName
    } = await afterClickDownloadFileAndGetFilename(page, "reins", currentRecord["reinsNumber"] + "_" + currentRecord["chirashiType"], downloadPath, {
      type: "selfClicker",
      selector: linksForThisSection[0],
    });

    currentRecord["linkFileName"] = localFilePath;
    currentRecord["newFileName"] = newFileName;
  } else {
    console.log("🔥 No links found for this section🔥");
  }

  return currentRecord;
}