import { readFileSync } from "fs";
import { join } from "path";
import { logger } from "@/lib/logger";
import { Page } from "puppeteer";

export interface SelectorConfig {
  [fieldName: string]: string;
}

export interface FallbackSelectors {
  [fieldName: string]: string[];
}

export interface ValidationConfig {
  required: string[];
  optional: string[];
}

export interface RecordTypeConfig {
  selectors: SelectorConfig;
  selectorsSold?: SelectorConfig;
  fallbackSelectors?: FallbackSelectors;
  extraFieldsSelectors?: SelectorConfig;
  validation?: ValidationConfig;
}

export interface ConfigurationData {
  [recordType: string]: RecordTypeConfig;
}

export interface TaskConfig {
  title: string;
  url: any;
  getChirashi: boolean;
  getDetailsForNewRecord: boolean;
  taskIndex?: number;
  yday?: number;
}

export interface TaskConfigurationData {
  [recordType: string]: TaskConfig[];
}

export class ConfigurationManager {
  private static instance: ConfigurationManager;
  private config: ConfigurationData | null = null;
  private taskConfig: TaskConfigurationData | null = null;
  private configPath: string;
  private taskConfigPath: string;

  private constructor(configPath?: string, taskConfigPath?: string) {
    this.configPath = configPath || join(process.cwd(), "app/api/cron/reins/config/selectorConfig.json");
    this.taskConfigPath = taskConfigPath || join(process.cwd(), "app/api/cron/reins/config/taskConfig.json");
  }

  static getInstance(configPath?: string, taskConfigPath?: string): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager(configPath, taskConfigPath);
    }
    return ConfigurationManager.instance;
  }

  loadConfiguration(): ConfigurationData {
    if (this.config) {
      return this.config;
    }

    try {
      const configData = readFileSync(this.configPath, "utf-8");
      this.config = JSON.parse(configData);
      logger.debug("🔥 Configuration loaded successfully");
      return this.config!;
    } catch (error) {
      logger.error("🔥 Failed to load configuration:", error);
      throw new Error(`Failed to load configuration from ${this.configPath}: ${(error as Error).message}`);
    }
  }

  getRecordTypeConfig(recordType: string): RecordTypeConfig {
    const config = this.loadConfiguration();
    const recordConfig = config[recordType];

    if (!recordConfig) {
      throw new Error(`Configuration not found for record type: ${recordType}`);
    }

    return recordConfig;
  }

  getSelectors(recordType: string, isSold: boolean = false): SelectorConfig {
    const config = this.getRecordTypeConfig(recordType);
    
    if (isSold && config.selectorsSold) {
      return config.selectorsSold;
    }

    return config.selectors;
  }

  getFallbackSelectors(recordType: string): FallbackSelectors {
    const config = this.getRecordTypeConfig(recordType);
    return config.fallbackSelectors || {};
  }

  getValidationConfig(recordType: string): ValidationConfig {
    const config = this.getRecordTypeConfig(recordType);
    return config.validation || { required: [], optional: [] };
  }

  validateConfiguration(recordType: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      const config = this.getRecordTypeConfig(recordType);
      
      // Check if selectors exist
      if (!config.selectors || Object.keys(config.selectors).length === 0) {
        errors.push(`No selectors defined for record type: ${recordType}`);
      }

      // Validate required fields have selectors
      const validation = config.validation;
      if (validation?.required) {
        for (const requiredField of validation.required) {
          if (!config.selectors[requiredField]) {
            errors.push(`Required field '${requiredField}' has no selector defined`);
          }
        }
      }

      // Validate selector format (basic check)
      for (const [field, selector] of Object.entries(config.selectors)) {
        if (selector && typeof selector !== "string") {
          errors.push(`Invalid selector format for field '${field}': must be string`);
        }
      }

    } catch (error) {
      errors.push(`Configuration validation failed: ${(error as Error).message}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  loadTaskConfiguration(): TaskConfigurationData {
    if (this.taskConfig) {
      return this.taskConfig;
    }

    try {
      const taskConfigData = readFileSync(this.taskConfigPath, "utf-8");
      this.taskConfig = JSON.parse(taskConfigData);
      logger.debug("🔥 Task configuration loaded successfully");
      return this.taskConfig!;
    } catch (error) {
      logger.error("🔥 Failed to load task configuration:", error);
      throw new Error(`Failed to load task configuration from ${this.taskConfigPath}: ${(error as Error).message}`);
    }
  }

  getTasks(recordType: string): TaskConfig[] {
    const taskConfig = this.loadTaskConfiguration();
    const tasks = taskConfig[recordType];

    if (!tasks) {
      logger.warn(`🔥 No tasks found for record type: ${recordType}`);
      return [];
    }

    return tasks;
  }

  reloadConfiguration(): void {
    this.config = null;
    this.taskConfig = null;
    this.loadConfiguration();
    this.loadTaskConfiguration();
    logger.debug("🔥 Configuration reloaded");
  }
}

export class SelectorTester {
  static async testSelectors(
    page: Page,
    selectors: SelectorConfig,
    baseSelector: string = ""
  ): Promise<{ field: string; selector: string; found: boolean; value?: string }[]> {
    const results: { field: string; selector: string; found: boolean; value?: string }[] = [];

    for (const [field, selector] of Object.entries(selectors)) {
      if (!selector) continue;

      const fullSelector = baseSelector ? `${baseSelector} ${selector}` : selector;
      
      try {
        const result = await page.evaluate((sel) => {
          const element = document.querySelector(sel);
          return {
            found: !!element,
            value: element?.textContent?.trim() || ""
          };
        }, fullSelector);

        results.push({
          field,
          selector: fullSelector,
          found: result.found,
          value: result.value
        });
      } catch (error) {
        results.push({
          field,
          selector: fullSelector,
          found: false
        });
      }
    }

    return results;
  }

  static async validatePageStructure(
    page: Page,
    recordType: string,
    configManager: ConfigurationManager
  ): Promise<{ isValid: boolean; issues: string[]; suggestions: string[] }> {
    const issues: string[] = [];
    const suggestions: string[] = [];

    try {
      const selectors = configManager.getSelectors(recordType);
      const validation = configManager.getValidationConfig(recordType);
      
      const testResults = await this.testSelectors(page, selectors);
      
      // Check required fields
      for (const requiredField of validation.required) {
        const result = testResults.find(r => r.field === requiredField);
        if (!result?.found) {
          issues.push(`Required field '${requiredField}' not found on page`);
          
          // Try fallback selectors
          const fallbacks = configManager.getFallbackSelectors(recordType);
          if (fallbacks[requiredField]) {
            suggestions.push(`Try fallback selectors for '${requiredField}': ${fallbacks[requiredField].join(", ")}`);
          }
        }
      }

      // Check if page structure has changed
      const foundFields = testResults.filter(r => r.found).length;
      const totalFields = testResults.length;
      const successRate = foundFields / totalFields;

      if (successRate < 0.5) {
        issues.push(`Low selector success rate (${Math.round(successRate * 100)}%). Page structure may have changed.`);
        suggestions.push("Consider updating selector configuration or implementing dynamic selector detection");
      }

    } catch (error) {
      issues.push(`Page structure validation failed: ${(error as Error).message}`);
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions
    };
  }
}

export class DynamicSelectorDetector {
  static async detectTableStructure(page: Page): Promise<{
    rowSelector: string;
    columnCount: number;
    sampleData: string[][];
  }> {
    return await page.evaluate(() => {
      // Common table row selectors to try
      const possibleRowSelectors = [
        ".p-table-body-row",
        ".table-row",
        "tr",
        ".row-item",
        ".list-item"
      ];

      for (const rowSelector of possibleRowSelectors) {
        const rows = document.querySelectorAll(rowSelector);
        if (rows.length > 0) {
          const firstRow = rows[0];
          const cells = firstRow.querySelectorAll("td, .p-table-body-item, .cell, .column");
          
          if (cells.length > 0) {
            // Extract sample data from first few rows
            const sampleData: string[][] = [];
            const sampleSize = Math.min(3, rows.length);
            
            for (let i = 0; i < sampleSize; i++) {
              const row = rows[i];
              const rowCells = row.querySelectorAll("td, .p-table-body-item, .cell, .column");
              const rowData: string[] = [];
              
              for (let j = 0; j < rowCells.length; j++) {
                rowData.push(rowCells[j].textContent?.trim() || "");
              }
              sampleData.push(rowData);
            }

            return {
              rowSelector,
              columnCount: cells.length,
              sampleData
            };
          }
        }
      }

      return {
        rowSelector: "",
        columnCount: 0,
        sampleData: []
      };
    });
  }

  static suggestSelectors(
    tableStructure: { rowSelector: string; columnCount: number; sampleData: string[][] },
    _recordType?: string
  ): SelectorConfig {
    const suggestions: SelectorConfig = {};
    
    // Basic heuristics for common fields based on sample data
    const fieldHeuristics = {
      price: (text: string) => /\d+万円|\d+円|¥\d+/.test(text),
      address: (text: string) => /都|県|市|区|町|丁目/.test(text),
      reinsNumber: (text: string) => /^\d{8,}$/.test(text.replace(/\D/g, "")),
      buildingName: (text: string) => /マンション|ハイツ|コーポ|アパート/.test(text),
      station: (text: string) => /駅|線/.test(text),
      area: (text: string) => /\d+\.?\d*m2|\d+\.?\d*㎡/.test(text)
    };

    // Analyze sample data to suggest column mappings
    for (let colIndex = 0; colIndex < tableStructure.columnCount; colIndex++) {
      const columnData = tableStructure.sampleData.map(row => row[colIndex] || "");
      const sampleText = columnData.join(" ");

      for (const [fieldType, heuristic] of Object.entries(fieldHeuristics)) {
        if (heuristic(sampleText)) {
          const selector = `.p-table-body-item:nth-child(${colIndex + 1})`;
          suggestions[fieldType] = selector;
          break; // Only assign one field type per column
        }
      }
    }

    return suggestions;
  }
}
