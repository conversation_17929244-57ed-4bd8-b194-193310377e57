import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { record } from "zod";

export function toFullWidthAddress(input: string): string {
  return input.replace(/[0-9]/g, (char) =>
    String.fromCharCode(char.charCodeAt(0) + 0xfee0)
  );
}

export function getCompositeTitle(rec: any, RECORD_TYPE: UserLambdaRecordType, URL_SITE: "REINS" | "SUMIFU" = "REINS") {
  let address = toFullWidthAddress(rec.address);
  // prevent to have 2 東京都葛飾区金町5丁目 and 東京都葛飾区金町５丁目
  let compositeTitle = `${address}[${rec.landSize}][${rec.buildingSize}]`;

  // console.log("rec", rec);
  // console.log("🔥[Reins][PriceChange] rec is", rec, RECORD_TYPE, RECORD_TYPE == UserLambdaRecordType.LAND);

  if (RECORD_TYPE == UserLambdaRecordType.MANSION) {
    // FIXME: note this wont work for mansion
    compositeTitle = `${rec.buildingName}[${address}][${rec.unitLevel}][${rec.unitArea}]`;
  } else if (RECORD_TYPE == UserLambdaRecordType.HOUSE) {
    compositeTitle = `${rec.address}[${rec.landSize}][${rec.buildingSize}]`;
  } else if (RECORD_TYPE == UserLambdaRecordType.LAND) {
    compositeTitle = `${rec.address}[${rec.landSize}]`;
  }

  return compositeTitle;
}

export function getCompositeTitleForSuumo({
  rec,
  recordType
}: {
  rec: any,
  recordType: any
}) {
  if (recordType === "HOUSE") {
    return `${rec.address}[${rec.landSize}][${rec.buildingSize}]`;
  } else if (recordType === "LAND") {
    return `${rec.address}[${rec.landSize}]`;
  }

  return `${rec.address}[${rec.landSize}][${rec.buildingSize}]`;
}