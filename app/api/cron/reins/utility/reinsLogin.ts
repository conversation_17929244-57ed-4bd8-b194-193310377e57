import { logger } from "@/lib/logger";

export const login = async ({
  page,
  account,
}: {
  page: any;
  account: string | null;
}) => {
  const { REINS_USERNAME, REINS_PASSWORD, REINS_USERNAME_2, REINS_PASSWORD_2 } = process.env;

  await page.goto("https://system.reins.jp/login/main/KG/GKG001200", {
    waitUntil: "networkidle2",
  });

  await page.waitForSelector("#__BVID__13");
  await page.type("#__BVID__13", account === "2" ? REINS_USERNAME_2 : REINS_USERNAME);
  await page.type("#__BVID__16", account === "2" ? REINS_PASSWORD_2 : REINS_PASSWORD);
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Checkbox for TnC
  await page.click("#__BVID__20");
  await new Promise(resolve => setTimeout(resolve, 1000));
  logger.debug("🔥 Login form filled up ..");
  await page.click(".p-3.large.btn-primary");
  logger.debug("🔥 Logged in and page loaded ..");
  await new Promise(resolve => setTimeout(resolve, 3000));
}