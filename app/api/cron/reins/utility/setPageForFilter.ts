import { Page } from "puppeteer";
import { ReinsTaskConstantProps } from "../constants/reinsTasks";

export async function setPageForFilter(criteria: any, page: Page, userLambda: ReinsTaskConstantProps, YDAY: boolean) {
  // Step 2 Fill in critieral
  const typeSelector =
    ".card:nth-child(7) .card-body .row:nth-child(2) .col-sm-4:nth-child(1) select";

  // 2.1 pick a type, and be filling in other info
  await page.waitForSelector(typeSelector);
  await page.select(typeSelector, criteria["type"]); // 03 - マンション 04 - 売外全(住宅以外建物全部), 02 一戸建て, 01 土地

  // 所有権のみ for BUILDING
  // if (userLambda["recordType"] !== "BUILDING") {
  //   await page.click(
  //     ".card:nth-child(7) .card-body .row:nth-child(5) .custom-radio:nth-child(2) input"
  //   );
  // }

  // Location
  if (criteria["prefecture"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(4) .row:nth-child(2) input",
      criteria["prefecture"]
    );
  }

  if (criteria["location"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(4) .row:nth-child(3) input",
      criteria["location"]
    );
  }

  if (criteria["prefecture2"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(7) .row:nth-child(2) input",
      criteria["prefecture2"]
    );
  }

  if (criteria["location2"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(7)  .row:nth-child(3) input",
      criteria["location2"]
    );
  }

  if (criteria["prefecture3"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(10) .row:nth-child(2) input",
      criteria["prefecture3"]
    );
  }

  if (criteria["location3"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(10) .row:nth-child(3) input",
      criteria["location3"]
    );
  }

  // By Railways
  if (criteria["railwayLine"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(13) .row:nth-child(2) input",
      criteria["railwayLine"]
    );
  }

  if (criteria["railwayLineStart"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(13) > .row:nth-child(2) .col .row > div:nth-child(1) input",
      criteria["railwayLineStart"]
    );
  }

  if (criteria["railwayLineEnd"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(13) > .row:nth-child(2) .col .row > div:nth-child(3) input",
      criteria["railwayLineEnd"]
    );
  }

  if (criteria["railwayLineMinutes"] !== undefined) {
    await page.type(
      ".card:nth-child(9) .card-body .container:nth-child(13) > .row:nth-child(3) > div:nth-child(1) > .row:nth-child(2) input",
      criteria["railwayLineMinutes"]
    );

    await page.select(
      ".card:nth-child(9) .card-body .container:nth-child(13) > .row:nth-child(3) > div:nth-child(1) > .row:nth-child(2) select",
      "1" // default to "分"
    );
  }

  // 4000-20000
  if (criteria["priceFrom"] !== undefined) {
    await page.type(
      ".card:nth-child(11) .card-body .row:nth-child(1) .col-sm-6 .row .col-4:nth-child(1) input",
      criteria["priceFrom"]
    );
  }

  if (criteria["priceTo"] !== undefined) {
    await page.type(
      ".card:nth-child(11) .card-body .row:nth-child(1) .col-sm-6 .row .col-4:nth-child(4) input", criteria["priceTo"]
    );
  }

  if (criteria["yearFrom"] !== undefined) {
    // Must be within 35 years
    await page.select(
      ".card:nth-child(11) .card-body .row:nth-child(17) .col-sm-4.col-8:nth-child(1) select",
      criteria["yearFrom"]
    );
  }

  // 100m2 land, 160m2 usage
  if (criteria["landAreaFrom"] !== undefined) {
    await page.type(
      ".card:nth-child(11) .card-body .row:nth-child(4) .col-4:nth-child(1) input",
      criteria["landAreaFrom"]
    );
  }

  if (criteria["buildingAreaFrom"] !== undefined) {
    await page.type(
      ".card:nth-child(11) .card-body .row:nth-child(5) .col-4:nth-child(1) input",
      criteria["buildingAreaFrom"]
    );
  }

  if (criteria["mansionAreaFrom"] !== undefined) {
    await page.type(
      ".card:nth-child(11) .card-body .row:nth-child(6) .col-4:nth-child(1) input",
      criteria["mansionAreaFrom"]
    );
  }

  // recent today days new, (6) is yday, 7 is today
  await page.click(
    `.card:nth-child(13) .card-body .container:nth-child(5) > .row:nth-child(1) .custom-radio:nth-child(${YDAY ? 6 : 7
    }) input`
  );

  // recent today days change,  (6) is yday, 7 is today
  await page.click(
    `.card:nth-child(13) .card-body .container:nth-child(5) > .row:nth-child(2) .custom-radio:nth-child(${YDAY ? 6 : 7
    }) input`
  );
}