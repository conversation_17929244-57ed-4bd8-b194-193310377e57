import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { Page } from "puppeteer";
import { getDataForOneBlockReinsDp } from "./getDataForOneBlockReinsDp";
import { getDataForOne<PERSON><PERSON><PERSON>hirashi } from "./getDataForOneBlock<PERSON>hirashi";
import { prisma } from "@/lib/prisma";
import { getCompositeTitle } from "./utility/getCompositeTitle";
import { logger } from "@/lib/logger";
import { REINS_TASKS, ReinsTaskConstantProps } from "./constants/reinsTasks";
import { TllUserLambdaRecordRecordType } from "@prisma/client";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { processBuildingName } from "@/lib/userLambdaRecord/building";

export async function getDataForOneBlock({ recordType, page, index, getChirashi, getDetailsForNewRecord, isSoldOnly = false }: { recordType: string, page: Page, index: number, getChirashi: boolean, getDetailsForNewRecord: boolean, isSoldOnly?: boolean }): Promise<any> {
  let BLOCK = {} as any;

  if (isSoldOnly && REINS_TASKS[recordType as keyof typeof REINS_TASKS].selectorsSold) {
    BLOCK = REINS_TASKS[recordType as keyof typeof REINS_TASKS].selectorsSold;
  } else {
    BLOCK = REINS_TASKS[recordType as keyof typeof REINS_TASKS].selectors;
  }

  const BLOCK_SELECTOR = ".p-table-body-row:nth-child(INDEX)";
  const RECORD_TYPE = recordType;

  let currentRecord = {} as any;
  const currentSelector = BLOCK_SELECTOR?.replace("INDEX", index.toString()); // First item is clearfix box_function

  // Step 1: Getting all detailed data
  for (const field of Object.keys(BLOCK)) {
    if (BLOCK[field as keyof typeof BLOCK] === "") continue;

    const selector = `${currentSelector} ${BLOCK[field as keyof typeof BLOCK]}`;
    let currentValue = null as any;

    currentValue = await page.evaluate(
      (sel, field) => {
        const res = document.querySelector(sel);

        if (res !== null) {
          if (field === "imageUrl") {
            let rawData = "";
            if (res.hasAttribute("rel")) {
              rawData = res.getAttribute("rel") ?? "";
            } else if (res.hasAttribute("data-original")) {
              rawData = res.getAttribute("data-original") ?? "";
            } else {
              rawData = res.getAttribute(
                res.hasAttribute("data-src") ? "data-src" : "src"
              ) ?? "";
            }

            return rawData;
          }

          if (field === "nearestStation") {
            if (res?.textContent && res?.textContent?.length > 0 && res?.textContent?.indexOf("　") > -1) {
              return res?.textContent?.split("　")[1] ?? "";
            }
          }

          return res?.textContent?.replace(/(\r\n\t|\n|\r\t|\s)/gm, "") ?? "";
        }

        return res ?? "";
      },
      selector,
      field
    );

    // STEP 3.2 Clean Up
    if (currentValue !== null && currentValue !== "") {
      if (field === "landSize" || field === "buildingSize") {
        currentValue = parseFloat(currentValue?.replace("m2", "") ?? "");
      }

      if (field === "unitArea") {
        // e.g. "75.21m2（壁芯）"
        currentValue = parseFloat(currentValue.split("m2")[0]);
      }

      if (field === "buildingLevel" && currentValue !== null && currentValue !== "") {
        currentValue = currentValue?.replace("階", "");
      }

      if (field === "status") {
        // e.g. "75.21m2（壁芯）"
        currentValue = currentValue === "-" ? "公開中" : currentValue;
      }

      if (field === "price") {
        currentValue = currentValue
          .replace("万円", "")
          .replace("円", "")
          .replace(",", "");

        if (currentValue.indexOf("億") > -1) {
          const [oku, man] = currentValue.split("億");
          currentValue =
            parseInt(oku, 10) * 10000 +
            (man.length > 0 ? parseInt(man, 10) : 0);
        } else {
          currentValue = parseInt(currentValue, 10);
        }
      }

      if (field === "buildingName") {
        currentValue = currentValue.replace("１ＬＤＫ", "").replace("２ＬＤＫ", "").replace("３ＬＤＫ", "").replace("４ＬＤＫ", "").replace("５ＬＤＫ", "");
        currentValue = processBuildingName(currentValue);
      }

      if (field === "roi") {
        currentValue =
          parseFloat(currentValue.replace("利回り：", "").replace("%", "")) /
          100;
      }

      if (field === "buildingBuiltYear") {
        currentValue = currentValue.replace(/（.*）/, ""); // in case of having "（築31年）" at the bottom e.g. for Rakumachi amd reins
        currentValue = currentValue.replace(/[.*]/, ""); // in case of [新築]
        currentValue = parseInt(currentValue.replace("年", "/").replace("月", "").split("/")[0], 10);
      }

      if (field === "buildingTotalLevel") {
        const regex = /[0-9]階/g;
        const found = currentValue.match(regex);
        currentValue =
          found === null ? "" : parseInt(found[0].replace("階", ""), 10);
      }
    }

    currentRecord[field] = currentValue;
  }

  // Final type specific changes
  if (RECORD_TYPE === "LAND") {
    // 売地 / 借地権 / 底地権
    if (currentRecord.recordSubType === "売地") {
      currentRecord["landRight"] = "所有権";
    } else {
      currentRecord["landRight"] = currentRecord.recordSubType;
    }
  }

  // logger.debug("🔥 currentRecord is", currentRecord);
  const compositeTitle = getCompositeTitle(currentRecord, RECORD_TYPE as UserLambdaRecordType, "REINS");
  logger.info("ℹ️ compositeTitle/type for record is", compositeTitle, "/", RECORD_TYPE);

  if (currentRecord.reinsNumber === "") { // For sold properties, there is no reins...
    return null;
  }

  // 成約 only get new records, no need for change
  // If not 成約, get new and when price chang
  const matchedRecord = await prisma.tllUserLambdaRecord.findUnique({
    where: {
      compositeTitle_recordType: {
        compositeTitle: compositeTitle,
        recordType: RECORD_TYPE as TllUserLambdaRecordRecordType,
      },
    }
  }) as UserLambdaRecordProps;

  if (!matchedRecord) {
    logger.info(`🔥 [REINS][Case 1][New] record ${getChirashi ? "so getting" : "but without"} chirashi ...🔥`);
    // FIXME: for some reason MUST MUST get DP before chirashi, else it will hang (there will be some new resources being fetched that never ends)

    if (getDetailsForNewRecord && recordType !== "MANSION") {
      await new Promise(resolve => setTimeout(resolve, 1000));
      currentRecord = await getDataForOneBlockReinsDp({
        page,
        currentRecord,
        RECORD_TYPE,
        currentSelector
      });
    }

    if (getChirashi) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      currentRecord = await getDataForOneBlockChirashi(page, currentRecord, currentSelector, BLOCK);
    }
  }
  else if (matchedRecord.price !== currentRecord.price) {
    logger.info("🔥 [REINS][Case 2][Existing] price changed so getting chirashi, record id is", matchedRecord.id);

    const priceChanges = await prisma.tllUserLambdaRecordPriceChange.findMany({
      where: {
        recordId: matchedRecord.id,
        price: currentRecord.price,
      },
    });

    // This is to prevent when multiple brokers have same account
    if (priceChanges.length === 0 && getChirashi) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      currentRecord = await getDataForOneBlockChirashi(page, currentRecord, currentSelector, BLOCK);
    }
    // } else if ( matchedRecord.status !== currentRecord.status) {
    //   logger.info("🔥 [REINS][Case 3][Existing] status changed, getting dp status only, record id is", matchedRecord.id);

    // 2025-03-14 Stop this for now.. as we are expanding 詳細 check for more properties 
    // if (!soldOnly) {
    //   currentRecord = await getDataForOneBlockReinsDpStatusChangeDetailsOnly(page, currentRecord, RECORD_TYPE ?? "", currentSelector);
    //   await new Promise(resolve => setTimeout(resolve, 2000));
    // }
  } else {
    // Else will not download the file (note that the record will still be updated if price change,s see saveUserLambdaResults.js)
    logger.info("🔥 [REINS][Case 4][Existing] No change, skipping ...🔥");
    return null;
  }

  return currentRecord;
};
