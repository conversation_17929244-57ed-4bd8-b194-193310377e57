import mansionTasks from "./mansionTasks";
import mansionRentTask from "./mansionRentTask";

import houseTasks from "./houseTasks";
import landTasks from "./landTasks";
import buildingTasks from "./buildingTasks";
import houseRentTask from "./houseRentTask";
import buildingRentTask from "./buildingRentTask";
import buildingPartRentTask from "./buildingPartRentTask";

export type ReinsTaskConstantProps = {
  title: string;
  url: any;
  getChirashi: boolean;
  getDetailsForNewRecord: boolean;
  taskIndex?: number; // 0 - tokyo, 1 kanagata 2 saitama 3 chiba
  yday?: number;
}

export const REINS_TASKS = {
  "HOUSE": houseTasks,
  "HOUSE_RENT": houseRentTask,

  "LAND": landTasks,

  "BUILDING": buildingTasks,
  "BUILDING_RENT": buildingRentTask,
  "BUILDING_PART_RENT": buildingPartRentTask,

  "MANSION": mansionTasks,
  "MANSION_RENT": mansionRentTask,
} as {
  [key: string]: {
    selectors: {
      [key: string]: string;
    };
    selectorsSold?: {
      [key: string]: string;
    };
    extraFieldsSelectors: {
      [key: string]: string;
    };
    tasks: ReinsTaskConstantProps[] | any[];
  } | any;
}

