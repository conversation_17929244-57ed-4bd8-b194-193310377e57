import { ReinsTaskConstantProps } from "./reinsTasks";

let commonSelectors = {
  "propertyType": ".p-table-body-item:nth-child(5)", // 賃家

  "brokerReinsNumber": ".p-table-body-item:nth-child(4)",
  "brokerType": ".p-table-body-item:nth-child(8)",

  "feeRent": ".p-table-body-item:nth-child(9)",
  "feeGiftMoney": ".p-table-body-item:nth-child(16)",
  "feeDepositMoney": ".p-table-body-item:nth-child(22)",
  "feeManagement": ".p-table-body-item:nth-child(15)",
  "feeUtility": ".p-table-body-item:nth-child(21)",

  "landSize": ".p-table-body-item:nth-child(6)",
  "landType": ".p-table-body-item:nth-child(10)",

  "address": ".p-table-body-item:nth-child(7)",
  "transport": ".p-table-body-item:nth-child(18)",
  "nearestStation": ".p-table-body-item:nth-child(18)",
  "nearestStationWalkMinute": ".p-table-body-item:nth-child(19)",

  "roadConnection": ".p-table-body-item:nth-child(26)",
  "roadConnectionFirstFacing": ".p-table-body-item:nth-child(30)", // note this will actually combine 接道方向 and 接道幅

  "buildingSize": ".p-table-body-item:nth-child(11)",
  "buildingName": ".p-table-body-item:nth-child(12)",
  "buildingBuiltYear": ".p-table-body-item:nth-child(29)",
}

const buildingRentTask = {
  selectors: {
    ...commonSelectors,
    "brokerStatus": ".p-table-body-item:nth-child(14)",
    "brokerListingCompany": ".p-table-body-item:nth-child(24) span",
    "brokerListingCompanyNumber": ".p-table-body-item:nth-child(27)",
  },
  selectorsSold: {
    ...commonSelectors,
    "transactionDate": ".p-table-body-item:nth-child(14)", // 成約 only
  },
  extraFieldsSelectors: {},
  tasks: [] as ReinsTaskConstantProps[],
  // code is 04
};

export default buildingRentTask;