import { ReinsTaskConstantProps } from "./reinsTasks"

let buildingTask = {
  selectors: {
    "link": ".p-table-body-item:nth-child(26)",
    "link2": ".p-table-body-item:nth-child(24)",
    "price": ".p-table-body-item:nth-child(9)",
    "title": ".p-table-body-item:nth-child(12)",
    "status": ".p-table-body-item:nth-child(13)",
    "address": ".p-table-body-item:nth-child(7)",
    "landSize": ".p-table-body-item:nth-child(6)",
    "landType": ".p-table-body-item:nth-child(10)",
    "transport": ".p-table-body-item:nth-child(15)",
    "nearestStation": ".p-table-body-item:nth-child(15)",
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(16)",

    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(17)", // 成約 only

    "reinsNumber": ".p-table-body-item:nth-child(4)",
    "buildingName": ".p-table-body-item:nth-child(12)",
    "buildingSize": ".p-table-body-item:nth-child(11)",
    "propertyType": ".p-table-body-item:nth-child(5)",
    "landRightType": "",
    "listingCompany": ".p-table-body-item:nth-child(19) span",
    "buildingBuiltYear": ".p-table-body-item:nth-child(21)",
    "listingCompanyPhoneNumber": ".p-table-body-item:nth-child(23)",

    "roadConnection": ".p-table-body-item:nth-child(14)",
    "roadConnectionFirstFacing": ".p-table-body-item:nth-child(18)"　// note this will actually combine 接道方向 and 接道幅
  },
  selectorsSold: {
    "price": ".p-table-body-item:nth-child(9)",
    "title": ".p-table-body-item:nth-child(12)",
    "propertyType": ".p-table-body-item:nth-child(5)",

    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(17)", // 成約 only
    "reinsNumber": ".p-table-body-item:nth-child(4)",

    "address": ".p-table-body-item:nth-child(7)",
    "landSize": ".p-table-body-item:nth-child(6)",
    "landType": ".p-table-body-item:nth-child(10)",

    "transport": ".p-table-body-item:nth-child(15)",
    "nearestStation": ".p-table-body-item:nth-child(15)",
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(16)",

    "buildingName": ".p-table-body-item:nth-child(12)",
    "buildingSize": ".p-table-body-item:nth-child(11)",


    "buildingBuiltYear": ".p-table-body-item:nth-child(21)",

    "roadConnection": ".p-table-body-item:nth-child(14)",
    "roadConnectionFirstFacing": ".p-table-body-item:nth-child(18)"　// note this will actually combine 接道方向 and 接道幅
  },
  extraFieldsSelectors: {
    landRight:
      ".card:nth-child(38) .card-body > .row:nth-child(1) .row .col",
    canAdvertise: ".card:nth-child(5) .row:nth-child(3) .row .col",
    buildingMaterial:
      ".card:nth-child(23) .card-body > div:nth-child(2) .row .col",
    buildingLevel:
      ".card:nth-child(23) .card-body > div:nth-child(3) .row .col",
    buildingRoomType:
      ".card:nth-child(20) .row div:nth-child(1) .row:nth-child(2) .col",
    buildingRoomCount:
      ".card:nth-child(20) .row div:nth-child(2) .row:nth-child(2) .col",
    // けんぺい
    landBuildingCoverageRatio:
      ".card:nth-child(36)  .card-body > div:nth-child(5) > div:nth-child(1) .row .col", // kenpei
    //　容積
    landFloorAreaRatio:
      ".card:nth-child(36) .card-body > div:nth-child(5) > div:nth-child(2) .row .col",
    roadConnection:
      ".card:nth-child(43) .card-body > div:nth-child(1) > div:nth-child(1) .row .col",
    roadConnectionFirstType:
      ".card:nth-child(43)  .card-body > div:nth-child(5) > div:nth-child(1)  > div:nth-child(1) .row .col",
    roadConnectionFirstFacing:
      ".card:nth-child(43)  .card-body > div:nth-child(5) > div:nth-child(2)  > div:nth-child(2) .row .col ",
    roadConnectionFirstWidth:
      ".card:nth-child(43)  .card-body > div:nth-child(5) > div:nth-child(3)  > div:nth-child(1) .row .col",
    roadConnectionSecondType:
      ".card:nth-child(43)  .card-body > div:nth-child(8) > div:nth-child(1)  > div:nth-child(1) .row .col",
    roadConnectionSecondFacing:
      ".card:nth-child(43)  .card-body > div:nth-child(8) > div:nth-child(2)  > div:nth-child(2) .row .col ",
    roadConnectionSecondWidth:
      ".card:nth-child(43)  .card-body > div:nth-child(8) > div:nth-child(3)  > div:nth-child(1) .row .col",

    comments: ".card:nth-child(49)  .card-body",
  },
  tasks: [
    // {
    //   id: "12",
    //   title: "東京",
    //   url: { type: "04", prefecture: "東京都" },
    //   getChirashi: true,
    // },
    // {
    //   id: "15",
    //   title: "神奈川",
    //   url: { type: "04", prefecture: "神奈川県" },
    //   getChirashi: false,
    // },
    // {
    //   id: "16",
    //   title: "埼玉",
    //   url: { type: "04", prefecture: "埼玉県" },
    //   getChirashi: false,
    // },
    // {
    //   id: "19",
    //   title: "千葉",
    //   url: { type: "04", prefecture: "千葉県" },
    //   getChirashi: false,
    // },
    // {
    //   id: "28",
    //   recordType: "BUILDING",
    //   title: "大阪府, 2000+, today",
    //   url: { type: "04", priceFrom: "2000", prefecture: "大阪府" },
    // },
    // {
    //   id: "29",
    //   recordType: "BUILDING",
    //   title: "福岡県, 2000+, today",
    //   url: { type: "04", priceFrom: "2000", prefecture: "福岡県" },
    // },
  ] as ReinsTaskConstantProps[]
};

["東京都", "神奈川県", "埼玉県", "千葉県"].forEach((prefecture, index) => {
  buildingTask.tasks = buildingTask.tasks.concat([
    {
      title: `${prefecture},10000+`,
      url: { type: "04", prefecture, priceFrom: "10000" },
      taskIndex: index,
      getChirashi: true,
      getDetailsForNewRecord: true
    },
    {
      title: `${prefecture},5000-10000`,
      url: { type: "04", prefecture, priceFrom: "5000", priceTo: "10000" },
      taskIndex: index,
      getChirashi: true,
      getDetailsForNewRecord: true
    },
    {
      title: `${prefecture},5000`,
      url: { type: "04", prefecture, priceTo: "5000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: true
    },
  ])
})

export default buildingTask;
