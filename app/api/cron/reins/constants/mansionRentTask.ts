import { ReinsTaskConstantProps } from "./reinsTasks"

let commonStatus = {
  "propertyType": ".p-table-body-item:nth-child(5)", // 賃家

  "brokerReinsNumber": ".p-table-body-item:nth-child(4)",
  "brokerType": ".p-table-body-item:nth-child(8)",

  "feeRent": ".p-table-body-item:nth-child(9)",
  "feeManagement": ".p-table-body-item:nth-child(16)",
  "feeUtility": ".p-table-body-item:nth-child(22)",
  "feeGiftMoney": ".p-table-body-item:nth-child(17)",
  "feeDepositMoney": ".p-table-body-item:nth-child(23)",

  "landType": ".p-table-body-item:nth-child(10)",
  "unitSize": ".p-table-body-item:nth-child(6)",
  "unitLevel": ".p-table-body-item:nth-child(13)",
  "unitLayout": ".p-table-body-item:nth-child(14)",

  "buildingName": ".p-table-body-item:nth-child(12)",
  "buildingAddress": ".p-table-body-item:nth-child(7)",
  "buildingBuiltYear": ".p-table-body-item:nth-child(27)",

  "transport": ".p-table-body-item:nth-child(19)",
  "nearestStation": ".p-table-body-item:nth-child(19)",
  "nearestStationWalkMinute": ".p-table-body-item:nth-child(20)",

  "roadConnection": ".p-table-body-item:nth-child(24)",
  "roadConnectionFirstFacing": ".p-table-body-item:nth-child(28)",
}

const mansionRentTask = {
  selectors: {
    ...commonStatus,
    "brokerStatus": ".p-table-body-item:nth-child(15)",
    "brokerListingCompany": ".p-table-body-item:nth-child(25) span",
    "brokerListingCompanyNumber": ".p-table-body-item:nth-child(29)",
  },
  selectorsSold: {
    ...commonStatus,
    "transactionDate": ".p-table-body-item:nth-child(15)", // 成約 only
  },
  extraFieldsSelectors: {

  },
  tasks: [] as ReinsTaskConstantProps[],
};

const rentRanges = [
  { rentFrom: "50" },
  { rentFrom: "40", rentTo: "50" },
  { rentFrom: "35", rentTo: "40" },
  { rentFrom: "31", rentTo: "35" },
  { rentFrom: "29", rentTo: "31" },
  { rentFrom: "27", rentTo: "29" },
  { rentFrom: "25", rentTo: "27" },
  { rentFrom: "24", rentTo: "25" },
  { rentFrom: "23", rentTo: "24" },
  { rentFrom: "22", rentTo: "23" },
  { rentFrom: "21", rentTo: "22" },
  { rentFrom: "20", rentTo: "21" },
  { rentFrom: "19", rentTo: "20" },
  { rentFrom: "18", rentTo: "19" },
  { rentFrom: "17", rentTo: "18" },
  { rentFrom: "16", rentTo: "17" },
  { rentFrom: "15", rentTo: "16" },
  { rentFrom: "14", rentTo: "15" },
  { rentFrom: "13", rentTo: "14" }
];

mansionRentTask.tasks = mansionRentTask.tasks.concat(
  rentRanges.map(range => ({
    title: `東京都,${range.rentTo ? `${range.rentFrom}-${range.rentTo}` : range.rentFrom}`,
    url: { type: "03", prefecture: "東京都", ...range },
    taskIndex: 0,
    getChirashi: false,
    getDetailsForNewRecord: false
  }))
);

// No below 10
["神奈川県", "埼玉県", "千葉県"].forEach((prefecture, index) => {
  mansionRentTask.tasks = mansionRentTask.tasks.concat([
    {
      title: `${prefecture},30+`,
      url: { type: "03", prefecture, rentFrom: "30" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},20-30`,
      url: { type: "03", prefecture, rentFrom: "20", rentTo: "30" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},15-20`,
      url: { type: "03", prefecture, rentFrom: "15", rentTo: "20" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},10-15`,
      url: { type: "03", prefecture, rentFrom: "10", rentTo: "15" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},8-10`,
      url: { type: "03", prefecture, rentFrom: "8", rentTo: "10" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
  ])
})

export default mansionRentTask;