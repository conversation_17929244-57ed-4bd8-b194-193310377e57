import { ReinsTaskConstantProps } from "./reinsTasks"

const houseRentTask = {
  selectors: {
    "brokerReinsNumber": ".p-table-body-item:nth-child(4)",
    "brokerType": ".p-table-body-item:nth-child(8)",
    "brokerStatus": ".p-table-body-item:nth-child(14)",

    "brokerListingCompany": ".p-table-body-item:nth-child(24) span",
    "brokerListingCompanyNumber": ".p-table-body-item:nth-child(28)",


    "feeRent": ".p-table-body-item:nth-child(9)",
    "feeGiftMoney": ".p-table-body-item:nth-child(16)",
    "feeDepositMoney": ".p-table-body-item:nth-child(22)",
    "feeManagement": ".p-table-body-item:nth-child(15)",
    "feeUtility": ".p-table-body-item:nth-child(21)",

    "propertyType": ".p-table-body-item:nth-child(5)", // 賃家
    "buildingBuiltYear": ".p-table-body-item:nth-child(26)",
    "landType": ".p-table-body-item:nth-child(10)",
    "landSize": ".p-table-body-item:nth-child(6)",
    "buildingSize": ".p-table-body-item:nth-child(11)",
    "roadConnection": ".p-table-body-item:nth-child(17)",
    "roadConnectionFirstFacing": ".p-table-body-item:nth-child(23)", // note this will actually combine 接道方向 and 接道幅
    "address": ".p-table-body-item:nth-child(7)",
    "transport": ".p-table-body-item:nth-child(18)",
    "nearestStation": ".p-table-body-item:nth-child(18)",
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(19)",

    "buildingLayout": ".p-table-body-item:nth-child(13)", // This is actually unit layout
  },
  selectorsSold: {
    "brokerReinsNumber": ".p-table-body-item:nth-child(4)",
    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(13)", // 成約 only

    "propertyType": ".p-table-body-item:nth-child(5)", // 賃家

    "feeRent": ".p-table-body-item:nth-child(9)",
    "feeGiftMoney": ".p-table-body-item:nth-child(16)",
    "feeDepositMoney": ".p-table-body-item:nth-child(22)",
    "feeManagement": ".p-table-body-item:nth-child(15)",
    "feeUtility": ".p-table-body-item:nth-child(21)",

    "buildingBuiltYear": ".p-table-body-item:nth-child(26)",
    "buildingSize": ".p-table-body-item:nth-child(11)",

    "landType": ".p-table-body-item:nth-child(10)",
    "landSize": ".p-table-body-item:nth-child(6)",

    "roadConnection": ".p-table-body-item:nth-child(17)",
    "roadConnectionFirstFacing": ".p-table-body-item:nth-child(23)", // note this will actually combine 接道方向 and 接道幅
    "address": ".p-table-body-item:nth-child(7)",
    "transport": ".p-table-body-item:nth-child(18)",
    "nearestStation": ".p-table-body-item:nth-child(18)",
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(19)",

    "buildingLayout": ".p-table-body-item:nth-child(13)", // This is actually unit layout
  },
  extraFieldsSelectors: {}
};



export default houseRentTask;