import { ReinsTaskConstantProps } from "./reinsTasks";

let landTask = {
  selectors: {
    "link": ".p-table-body-item:nth-child(27)",
    "link2": ".p-table-body-item:nth-child(25)",
    "price": ".p-table-body-item:nth-child(9)",
    "title": ".p-table-body-item:nth-child(7)",
    "status": ".p-table-body-item:nth-child(13)",
    "address": ".p-table-body-item:nth-child(7)",
    "landSize": ".p-table-body-item:nth-child(6)",
    "landType": ".p-table-body-item:nth-child(10)",

    "transport": ".p-table-body-item:nth-child(16)",
    "nearestStation": ".p-table-body-item:nth-child(16)", // same, but will take out the space "　"
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(17)",

    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(18)", // 成約 only

    "reinsNumber": ".p-table-body-item:nth-child(4)",
    "buildingName": "",
    "propertyType": ".p-table-body-item:nth-child(5)",
    "listingCompany": ".p-table-body-item:nth-child(21) span",

    "landFloorAreaRatio": ".p-table-body-item:nth-child(19)",
    "landBuildingCoverageRatio": ".p-table-body-item:nth-child(14)",
    "listingCompanyPhoneNumber": ".p-table-body-item:nth-child(24)",

    "roadConnection": ".p-table-body-item:nth-child(20)",
    "roadConnectionFirstFacing": ".p-table-body-item:nth-child(23)"　// note this will actually combine 接道方向 and 接道幅
  },
  selectorsSold: {
    "price": ".p-table-body-item:nth-child(9)",
    "title": ".p-table-body-item:nth-child(7)",
    "address": ".p-table-body-item:nth-child(7)",
    "landSize": ".p-table-body-item:nth-child(6)",
    "landType": ".p-table-body-item:nth-child(10)",

    "transport": ".p-table-body-item:nth-child(12)",
    "nearestStation": ".p-table-body-item:nth-child(12)", // same, but will take out the space "　"
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(13)",

    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(18)", // 成約 only

    "reinsNumber": ".p-table-body-item:nth-child(4)",
    "propertyType": ".p-table-body-item:nth-child(5)",

    "landFloorAreaRatio": ".p-table-body-item:nth-child(19)",
    "landBuildingCoverageRatio": ".p-table-body-item:nth-child(15)",
    "roadConnection": ".p-table-body-item:nth-child(20)",
    "roadConnectionFirstFacing": ".p-table-body-item:nth-child(23)"　// note this will actually combine 接道方向 and 接道幅
  },
  extraFieldsSelectors: {
    canAdvertise: ".card:nth-child(5) .card-body > .row:nth-child(2) .row .col",
    roadConnection:
      ".card:nth-child(37) .card-body > div:nth-child(1) > div:nth-child(1) .row .col",
    roadConnectionFirstType:
      ".card:nth-child(37)  .card-body > div:nth-child(5) > div:nth-child(1)  > div:nth-child(1) .row .col",
    roadConnectionFirstFacing:
      ".card:nth-child(37)  .card-body > div:nth-child(5) > div:nth-child(2)  > div:nth-child(2) .row .col ",
    roadConnectionFirstWidth:
      ".card:nth-child(37)  .card-body > div:nth-child(5) > div:nth-child(3)  > div:nth-child(1) .row .col",
    comments: ".card:nth-child(43)  .card-body",
  },
  tasks: [
  ] as ReinsTaskConstantProps[]
};

["東京都", "神奈川県", "埼玉県", "千葉県"].forEach((prefecture, index) => {
  landTask.tasks = landTask.tasks.concat([
    {
      title: `${prefecture},10000`,
      url: { type: "01", prefecture, priceFrom: "10000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: prefecture === "東京都",
    },
    {
      title: `${prefecture},5000-10000`,
      url: { type: "01", prefecture, priceFrom: "5000", priceTo: "10000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: prefecture === "東京都"
    },
    {
      title: `${prefecture},3000-5000`,
      url: { type: "01", prefecture, priceFrom: "3000", priceTo: "5000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},~3000`,
      url: { type: "01", prefecture, priceTo: "3000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    }
  ])
});

export default landTask;
