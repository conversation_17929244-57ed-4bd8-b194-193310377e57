import { ReinsTaskConstantProps } from "./reinsTasks";

let commonStatus = {
  "propertyType": ".p-table-body-item:nth-child(5)", // 賃家

  "brokerReinsNumber": ".p-table-body-item:nth-child(4)",
  "brokerType": ".p-table-body-item:nth-child(8)",

  "feeRent": ".p-table-body-item:nth-child(9)",
  "feeManagement": ".p-table-body-item:nth-child(16)",
  "feeUtility": ".p-table-body-item:nth-child(22)",
  "feeGiftMoney": ".p-table-body-item:nth-child(17)",
  "feeDepositMoney": ".p-table-body-item:nth-child(23)",

  "landType": ".p-table-body-item:nth-child(10)",
  "unitSize": ".p-table-body-item:nth-child(6)",
  "unitLevel": ".p-table-body-item:nth-child(13)",
  "unitLayout": ".p-table-body-item:nth-child(14)",

  "buildingName": ".p-table-body-item:nth-child(12)",
  "buildingAddress": ".p-table-body-item:nth-child(7)",
  "buildingBuiltYear": ".p-table-body-item:nth-child(27)",

  "transport": ".p-table-body-item:nth-child(19)",
  "nearestStation": ".p-table-body-item:nth-child(19)",
  "nearestStationWalkMinute": ".p-table-body-item:nth-child(20)",

  "roadConnection": ".p-table-body-item:nth-child(24)",
  "roadConnectionFirstFacing": ".p-table-body-item:nth-child(28)",
}

const buildingPartRentTask = {
  selectors: {
    ...commonStatus,
    "brokerStatus": ".p-table-body-item:nth-child(15)",
    "brokerListingCompany": ".p-table-body-item:nth-child(25) span",
    "brokerListingCompanyNumber": ".p-table-body-item:nth-child(29)",
  },
  selectorsSold: {
    ...commonStatus,
    "transactionDate": ".p-table-body-item:nth-child(15)", // 成約 only
  },
  extraFieldsSelectors: {},
  tasks: [] as ReinsTaskConstantProps[],
  // code is 04
};

const rentRanges = [
  { rentFrom: "200" },
  { rentFrom: "150", rentTo: "200" },
  { rentFrom: "100", rentTo: "150" },
  { rentFrom: "75", rentTo: "100" },
  { rentFrom: "50", rentTo: "75" },
  { rentFrom: "40", rentTo: "50" },
  { rentFrom: "30", rentTo: "40" },
  { rentFrom: "20", rentTo: "30" },
  { rentFrom: "10", rentTo: "20" },
  { rentTo: "10" }
] as { rentFrom: string, rentTo?: string }[];

buildingPartRentTask.tasks = buildingPartRentTask.tasks.concat(
  rentRanges.map(range => ({
    title: `[Building Part Rent] 東京都,${range.rentTo ? `${range.rentFrom}-${range.rentTo}` : range.rentFrom}`,
    url: { type: "05", prefecture: "東京都", ...range },
    taskIndex: 0,
    getChirashi: false,
    getDetailsForNewRecord: false
  }))
);

// No below 10
["神奈川県", "埼玉県", "千葉県"].forEach((prefecture, index) => {
  buildingPartRentTask.tasks = buildingPartRentTask.tasks.concat([
    {
      title: `[Building Part Rent]  ${prefecture},30+`,
      url: { type: "05", prefecture, rentFrom: "30" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `[Building Part Rent]  ${prefecture},15-30`,
      url: { type: "05", prefecture, rentFrom: "15", rentTo: "30" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `[Building Part Rent]  ${prefecture},~15`,
      url: { type: "05", prefecture, rentTo: "15" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
  ])
})

export default buildingPartRentTask;