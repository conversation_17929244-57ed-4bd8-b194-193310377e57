"MANSION"
import { ReinsTaskConstantProps } from "./reinsTasks"

const mansionTasks = {
  selectors: {
    "link": ".p-table-body-item:nth-child(29)",
    "link2": ".p-table-body-item:nth-child(27)",
    "price": ".p-table-body-item:nth-child(9)",
    "title": ".p-table-body-item:nth-child(12)",
    "status": ".p-table-body-item:nth-child(15)",
    "address": ".p-table-body-item:nth-child(7)",
    "landSize": "",
    "landType": ".p-table-body-item:nth-child(10)",
    "unitArea": ".p-table-body-item:nth-child(6)",
    "transport": ".p-table-body-item:nth-child(19)",
    "nearestStation": ".p-table-body-item:nth-child(19)",
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(20)",

    "unitLevel": ".p-table-body-item:nth-child(13)",

    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(21)", // 成約 only

    "buildingLayout": ".p-table-body-item:nth-child(14)", // This is actually unit layout

    "reinsNumber": ".p-table-body-item:nth-child(4)",
    "buildingName": ".p-table-body-item:nth-child(12)",
    "propertyType": ".p-table-body-item:nth-child(5)",

    "listingCompany": ".p-table-body-item:nth-child(22) span",
    "buildingBuiltYear": ".p-table-body-item:nth-child(24)",
    "mansionManagementFee": ".p-table-body-item:nth-child(16)",
    "listingCompanyPhoneNumber": ".p-table-body-item:nth-child(26)"
  },
  selectorsSold: {
    "price": ".p-table-body-item:nth-child(9)",
    "title": ".p-table-body-item:nth-child(12)",
    "address": ".p-table-body-item:nth-child(7)",
    "unitArea": ".p-table-body-item:nth-child(6)",
    "landType": ".p-table-body-item:nth-child(10)",
    "transport": ".p-table-body-item:nth-child(19)",

    "nearestStation": ".p-table-body-item:nth-child(19)",
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(20)",
    "unitLevel": ".p-table-body-item:nth-child(13)",

    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(21)", // 成約 only
    "buildingLayout": ".p-table-body-item:nth-child(14)", // This is actually unit layout
    "reinsNumber": ".p-table-body-item:nth-child(4)",
    "buildingName": ".p-table-body-item:nth-child(12)",
    "propertyType": ".p-table-body-item:nth-child(5)",
    "buildingBuiltYear": ".p-table-body-item:nth-child(23)",
    "mansionManagementFee": ".p-table-body-item:nth-child(16)"
  },
  extraFieldsSelectors: {},
  tasks: [
    // {
    //   id: "27",
    //   title: "山手線浜松町-新宿, 15min, 50m2+, 50000万円, 1989",
    //   url: { type: "03", railwayLine: "山手線", railwayLineStart: "浜松町", railwayLineEnd: "新宿", railwayLineMinutes: "15", priceTo: "50000", mansionAreaFrom: "50", yearFrom: "1989" },
    // },
  ] as ReinsTaskConstantProps[]
};

["東京都"].forEach((prefecture, index) => {
  mansionTasks.tasks = mansionTasks.tasks.concat([
    {
      title: `${prefecture},20000+`,
      url: { type: "03", prefecture, priceFrom: "20000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},10000-20000`,
      url: { type: "03", prefecture, priceFrom: "10000", priceTo: "20000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},5000-10000`,
      url: { type: "03", prefecture, priceFrom: "5000", priceTo: "10000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},4000-5000`,
      url: { type: "03", prefecture, priceFrom: "4000", priceTo: "5000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},3000-4000`,
      url: { type: "03", prefecture, priceFrom: "3000", priceTo: "4000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},2000-3000`,
      url: { type: "03", prefecture, priceFrom: "2000", priceTo: "3000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},1000-2000`,
      url: { type: "03", prefecture, priceFrom: "1000", priceTo: "2000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},~1000`,
      url: { type: "03", prefecture, priceTo: "1000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
  ])
});

["神奈川県", "埼玉県", "千葉県"].forEach((prefecture, index) => {
  mansionTasks.tasks = mansionTasks.tasks.concat([
    {
      title: `${prefecture},10000~`,
      url: { type: "03", prefecture, priceFrom: "10000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},5000-10000`,
      url: { type: "03", prefecture, priceFrom: "5000", priceTo: "10000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},4000-5000`,
      url: { type: "03", prefecture, priceFrom: "4000", priceTo: "5000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},3000-4000`,
      url: { type: "03", prefecture, priceFrom: "3000", priceTo: "4000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},2000-3000`,
      url: { type: "03", prefecture, priceFrom: "2000", priceTo: "3000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},1000-2000`,
      url: { type: "03", prefecture, priceFrom: "1000", priceTo: "2000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},~1000`,
      url: { type: "03", prefecture, priceTo: "1000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
  ])
})

export default mansionTasks;
