import { ReinsTaskConstantProps } from "./reinsTasks"

let houseTask = {
  selectors: {
    "link": ".p-table-body-item:nth-child(27)",
    "link2": ".p-table-body-item:nth-child(25)",
    "price": ".p-table-body-item:nth-child(9)",
    "title": ".p-table-body-item:nth-child(7)",
    "status": ".p-table-body-item:nth-child(14)",
    "address": ".p-table-body-item:nth-child(7)",
    "landSize": ".p-table-body-item:nth-child(6)",
    "landType": ".p-table-body-item:nth-child(10)",
    "transport": ".p-table-body-item:nth-child(16)",
    "nearestStation": ".p-table-body-item:nth-child(16)",
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(17)",

    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(18)", // 成約 only

    "buildingLayout": ".p-table-body-item:nth-child(13)", // This is actually unit layout
    "reinsNumber": ".p-table-body-item:nth-child(4)",
    "buildingName": "",
    "buildingSize": ".p-table-body-item:nth-child(11)",
    "propertyType": ".p-table-body-item:nth-child(5)",
    "listingCompany": ".p-table-body-item:nth-child(20) span",

    "buildingBuiltYear": ".p-table-body-item:nth-child(22)",
    "listingCompanyPhoneNumber": ".p-table-body-item:nth-child(24)",

    "roadConnection": ".p-table-body-item:nth-child(15)",
    "roadConnectionFirstFacing": ".p-table-body-item:nth-child(19)"　// note this will actually combine 接道方向 and 接道幅
  },
  selectorsSold: {
    "price": ".p-table-body-item:nth-child(9)",
    "title": ".p-table-body-item:nth-child(7)",
    "address": ".p-table-body-item:nth-child(7)",
    "landSize": ".p-table-body-item:nth-child(6)",
    "landType": ".p-table-body-item:nth-child(10)",
    "transport": ".p-table-body-item:nth-child(16)",
    "nearestStation": ".p-table-body-item:nth-child(16)",
    "nearestStationWalkMinute": ".p-table-body-item:nth-child(17)",

    "brokerType": ".p-table-body-item:nth-child(8)",
    "transactionDate": ".p-table-body-item:nth-child(18)", // 成約 only

    "buildingLayout": ".p-table-body-item:nth-child(13)", // This is actually unit layout
    "reinsNumber": ".p-table-body-item:nth-child(4)",
    "buildingSize": ".p-table-body-item:nth-child(11)",
    "propertyType": ".p-table-body-item:nth-child(5)",


    "buildingBuiltYear": ".p-table-body-item:nth-child(22)",

    "roadConnection": ".p-table-body-item:nth-child(15)",
    "roadConnectionFirstFacing": ".p-table-body-item:nth-child(19)"　// note this will actually combine 接道方向 and 接道幅
  },
  extraFieldsSelectors: {
    landRight:
      ".card:nth-child(38) .card-body > .row:nth-child(1) .row .col",
    canAdvertise: ".card:nth-child(5) .card-body > .row:nth-child(2) .row .col",
    buildingMaterial:
      ".card:nth-child(23) .card-body > div:nth-child(2) .row .col",
    buildingLevel:
      ".card:nth-child(23) .card-body > div:nth-child(4) div:nth-child(1) .row .col",
    buildingRoomType:
      ".card:nth-child(20) .row div:nth-child(1) .row:nth-child(2) .col",
    buildingRoomCount:
      ".card:nth-child(20) .row div:nth-child(2) .row:nth-child(2) .col",
    landBuildingCoverageRatio:
      ".card:nth-child(36)  .card-body > div:nth-child(5) > div:nth-child(1) .row .col", // kenpei
    landFloorAreaRatio:
      ".card:nth-child(36) .card-body > div:nth-child(5) > div:nth-child(2) .row .col",
    roadConnection:
      ".card:nth-child(43) .card-body > div:nth-child(1) > div:nth-child(1) .row .col",
    roadConnectionFirstType:
      ".card:nth-child(43)  .card-body > div:nth-child(5) > div:nth-child(1)  > div:nth-child(1) .row .col",
    roadConnectionFirstFacing:
      ".card:nth-child(43)  .card-body > div:nth-child(5) > div:nth-child(2)  > div:nth-child(2) .row .col ",
    roadConnectionFirstWidth:
      ".card:nth-child(43)  .card-body > div:nth-child(5) > div:nth-child(3)  > div:nth-child(1) .row .col",
    roadConnectionSecondType:
      ".card:nth-child(43)  .card-body > div:nth-child(8) > div:nth-child(1)  > div:nth-child(1) .row .col",
    roadConnectionSecondFacing:
      ".card:nth-child(43)  .card-body > div:nth-child(8) > div:nth-child(2)  > div:nth-child(2) .row .col ",
    roadConnectionSecondWidth:
      ".card:nth-child(43)  .card-body > div:nth-child(8) > div:nth-child(3)  > div:nth-child(1) .row .col",
    comments: ".card:nth-child(49)  .card-body",
  },
  tasks: [] as ReinsTaskConstantProps[]
};


// Special to be used for chirashi
houseTask.tasks = houseTask.tasks.concat([
  {
    title: "Tokyo house > 100+",
    url: { type: "02", prefecture: "東京都", buildingAreaFrom: "100" },
    taskIndex: 0,
    getChirashi: true,
    getDetailsForNewRecord: true,
  },
]);

["東京都"].forEach((prefecture, index) => {
  houseTask.tasks = houseTask.tasks.concat([
    {
      title: `${prefecture},10000+`,
      url: { type: "02", prefecture, priceFrom: "10000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: prefecture === "東京都"
    },
    {
      title: `${prefecture},5000-10000`,
      url: { type: "02", prefecture, priceFrom: "5000", priceTo: "10000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: prefecture === "東京都"
    },
    {
      title: `${prefecture},4000-5000`,
      url: { type: "02", prefecture, priceFrom: "4000", priceTo: "5000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},3000-4000`,
      url: { type: "02", prefecture, priceFrom: "3000", priceTo: "4000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},2000-3000`,
      url: { type: "02", prefecture, priceFrom: "2000", priceTo: "3000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},1000-2000`,
      url: { type: "02", prefecture, priceFrom: "1000", priceTo: "2000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},1000`,
      url: { type: "02", prefecture, priceTo: "1000" },
      taskIndex: index,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
  ])
});

["神奈川県", "埼玉県", "千葉県"].forEach((prefecture, index) => {
  houseTask.tasks = houseTask.tasks.concat([
    {
      title: `${prefecture},6000+`,
      url: { type: "02", prefecture, priceFrom: "6000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},5000-6000`,
      url: { type: "02", prefecture, priceFrom: "5000", priceTo: "6000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},4000-5000`,
      url: { type: "02", prefecture, priceFrom: "4000", priceTo: "5000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},2000-4000`,
      url: { type: "02", prefecture, priceFrom: "2000", priceTo: "4000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
    {
      title: `${prefecture},~2000`,
      url: { type: "02", prefecture, priceTo: "2000" },
      taskIndex: index + 1,
      getChirashi: false,
      getDetailsForNewRecord: false
    },
  ])
})





export default houseTask;