
import { Page } from "puppeteer";
import { REINS_TASKS } from "./constants/reinsTasks";
import { logger } from "@/lib/logger";


// 2025-03-14 Stop this for now.. as we are expanding 詳細 check for more properties 
export async function getDataForOneBlockReinsDpStatusChangeDetailsOnly(page: Page, currentRecord: any, RECORD_TYPE: string, currentSelector: string) {
  logger.info("🔥  Going to DP because status is different 🔥");
  // If status change, then adding more into the comments
  const DETAILS＿SELECTOR = `${currentSelector} .p-table-body-item:nth-child(${RECORD_TYPE === "BUILDING" ? 25 : RECORD_TYPE === "MANSION" ? 28 : 26
    })`;

  await new Promise(resolve => setTimeout(resolve, 1000));
  await page.click(DETAILS＿SELECTOR);
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Getting details on 取引状況の補足
  const STATUS_CHANGE_DETAILS =
    ".card:nth-child(7) .card-body > div:nth-child(3) .row .col";
  currentRecord["statusChangeDetails"] = await page.evaluate((sel) => {
    const res = document.querySelector(sel);
    return res === null || res === undefined ? "" : res.textContent;
  }, STATUS_CHANGE_DETAILS);

  // E.g. sometimes people will explain what happened // 
  logger.info('🔥  currentRecord["statusChangeDetails"],', currentRecord["statusChangeDetails"]);

  // STEP 2.4 Go back to the last page
  const BACK_SELECTOR = ".p-frame-backer";
  await page.click(BACK_SELECTOR);
  logger.debug("ℹ️ Go back to property overview page ..");
  await new Promise(resolve => setTimeout(resolve, 1000));

  return currentRecord;
}

export async function getDataForOneBlockReinsDp({
  page,
  currentRecord,
  RECORD_TYPE,
  currentSelector
}: {
  page: Page, currentRecord: any, RECORD_TYPE: string, currentSelector: string,
}) {
  // Step 2: Click on 詳細 and be getting the ROI, use it to reverse calculate the yearlyIncome
  // DO NOT NEED FOR sale_record
  logger.debug("🔥 Go to property details page to get all details ..");

  const DETAILS＿SELECTOR = `${currentSelector} .p-table-body-item:nth-child(${RECORD_TYPE === "BUILDING" ? 25 : 26
    }) button`;



  await new Promise(resolve => setTimeout(resolve, 5000));

  // *********************************
  // STEP 2.1 Click details page
  await page.waitForSelector(DETAILS＿SELECTOR, { visible: true });
  logger.debug("🔥 Clicking on DETAILS＿SELECTOR, selector id is", DETAILS＿SELECTOR);
  // Remove the bottom floating bar, so as not to click on "印刷" by mistake
  await page.evaluate((sel) => {
    const elements = document.querySelectorAll(sel);
    for (let i = 0; i < elements.length; i++) {
      elements[i]?.parentNode?.removeChild(elements[i]);
    }
  }, ".p-frame-footer");
  logger.debug("🔥 Removed the bottom floating bar.. clicking on the link");

  await new Promise(resolve => setTimeout(resolve, 1000));
  await page.locator(DETAILS＿SELECTOR).click();
  await new Promise(resolve => setTimeout(resolve, 1000));

  const TITLE_SELECTOR = ".container > h2:nth-child(2)";
  await page.waitForSelector(TITLE_SELECTOR);

  // GETTING DATA: ROI
  if (RECORD_TYPE === "BUILDING") {
    // STEP 2.2 Getting the price for 想定利回り, but only for building
    const ROI_SELECTOR =
      ".card:nth-child(12) .container:nth-child(5) .row .row .col";

    const currentRecordRoi = await page.evaluate((sel) => {
      const res = document.querySelector(sel);
      return res === null || res === undefined ? "" : res.textContent;
    }, ROI_SELECTOR);

    if (
      currentRecord["roi"] !== undefined &&
      currentRecord["roi"] !== null
    ) {
      currentRecord.yearlyIncome =
        currentRecord.roi * currentRecord.price;
    }

    if (currentRecordRoi !== null && currentRecordRoi !== "") {
      currentRecord.roi = parseFloat(currentRecordRoi) / 100;
      currentRecord.yearlyIncome =
        currentRecord.roi * currentRecord.price;
    } else {
      currentRecord.yearlyIncome = 0; // default to 0, and they will not be used when getting the cap rate for nearby listing items
    }
  }

  const additionalSelectors = REINS_TASKS[RECORD_TYPE as keyof typeof REINS_TASKS].extraFieldsSelectors;

  for (const field of Object.keys(additionalSelectors)) {
    currentRecord[field] = await page.evaluate((sel) => {
      const res = document.querySelector(sel);
      return res === null || res === undefined ? "" : res.textContent;
    }, additionalSelectors[field]);
  }

  // STEP 2.3: Some final fixes
  // Padding buildingMaterial
  if (currentRecord["buildingMaterial"] === "ＲＣ") {
    currentRecord["buildingMaterial"] === "RC造";
  } else if (currentRecord["buildingMaterial"] === "軽量鉄骨") {
    currentRecord["buildingMaterial"] === "軽量鉄骨造";
  }

  // STEP 2.4 Go back to the last page
  const BACK_SELECTOR = ".p-frame-backer";
  await page.click(BACK_SELECTOR);
  logger.info("🔥 Go back to property overview page ..");
  await new Promise(resolve => setTimeout(resolve, 1000));

  logger.debug("🔥currentRecord.", JSON.stringify(currentRecord, null, 2));
  await new Promise(resolve => setTimeout(resolve, 5000));

  return currentRecord;
}