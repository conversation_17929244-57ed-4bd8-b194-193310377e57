

// import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
// import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
// import { sendLark } from "@/lib/thirdParty/lark";

// export async function GET(req: Request) {
//   if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
//     return new Response("Unauthorized", { status: 401 });
//   }

//   if (req.method !== "GET") {
//     return new Response("Method Not Allowed", { status: 405 });
//   }


//   let browser = null;
//   let page = null;

//   const { KENBIYA_USERNAME, KENBIYA_COMPANY_CODE, KENBIYA_PASSWORD } =
//     process.env;

//   if (!KENBIYA_USERNAME || !KENBIYA_COMPANY_CODE || !KENBIYA_PASSWORD) {
//     throw new Error("KENBIYA_USERNAME, KENBIYA_COMPANY_CODE, KENBIYA_PASSWORD is not set");
//   }

//   try {
//     browser = await getChromeBrowser();
//     page = await getPageWithRandomUserAgent(browser);

//     const ADMIN_LOGIN_URL = "https://www.kenbiya.com/menu";
//     await page.goto(ADMIN_LOGIN_URL);
//     await new Promise(resolve => setTimeout(resolve, 3000));

//     await page.type("form div dl dd:nth-child(2) input", KENBIYA_COMPANY_CODE);
//     await page.type("form div dl dd:nth-child(4) input", KENBIYA_USERNAME);
//     await page.type("form div dl dd:nth-child(6) input", KENBIYA_PASSWORD);

//     await page.click("form .comp_submit input");
//     await new Promise(resolve => setTimeout(resolve, 2000));

//     // *******************
//     // Step 2: Gettting data needed
//     // Actually default you will go to usage page, but just to click and go there once to get all data
//     const ADMIN_URL = "https://www.kenbiya.com/app/exe/compPropList";
//     await page.goto(ADMIN_URL);
//     await new Promise(resolve => setTimeout(resolve, 3000));

//     // Getting the count
//     // the total rows = 1 row on top + 4x number of items row //

//     // const totalTrCount = await page.evaluate(
//     //   (sel) => document.querySelectorAll(sel).length,
//     //   "table tr"
//     // );

//     const totalItemCount = await page.evaluate(
//       (sel) => document.querySelectorAll(sel).length,
//       "form .comp_listTable tbody"
//     );

//     console.log(`🔥 Total records is ${totalItemCount}🔥 🔥`);

//     let results = [];

//     let newDataSelectors = {
//       // property_uuid: ".propertyInfo div:nth-child(2) p:nth-child(1)", // 1st row
//       title: ".td05 a", // default to "Aggregate"
//       price: "tr:nth-child(1) .td06 span", // 1st row
//       roi: "tr:nth-child(2) .td06 span", // 1st row
//       metricViewReadAll: "tr:nth-child(1) .td08", // 1st row
//       // metric_fav_aggr: "tr:nth-child(2) .td08 span", // 1st row
//       metricInquiryAll: "tr:nth-child(2) .td08", // 1st row
//     };

//     let currentValue;
//     for (let i = 0; i < totalItemCount; i++) {
//       console.log(`🔥 Getting data for the ${i + 1}th result 🔥`);

//       // .comp_tbl_usage > ul: nth - child(1) > li: nth - child(2)

//       let currentResult = {};
//       for (const field of Object.keys(newDataSelectors)) {
//         let selector = `form .comp_listTable:nth-child(${4 + i}) tbody ${newDataSelectors[field]
//           }`;
//         console.log("🔥 selector🔥 🔥");
//         console.log(selector);

//         currentValue = await page.evaluate(
//           (sel, field) => {
//             let res = document.querySelector(sel) as any;
//             res = res.textContent.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");

//             if (res !== null) {
//               if (field === "price") {
//                 if (res.indexOf("億") > -1) {
//                   let [oku, man] = res.split("億");
//                   return (
//                     parseInt(oku, 10) * 10000 +
//                     (man.length > 0
//                       ? parseInt(man.replace("万円", "").replace(",", ""), 10)
//                       : 0)
//                   );
//                 }
//                 return parseInt(res.replace("万円", "").replace(",", ""), 10);
//               }

//               if (field === "roi") {
//                 return parseFloat(res.replace("%", ""));
//               }

//               // e.g. "No.26240251棟アパート21A02"
//               // if (field === "property_uuid") {
//               //   return parseInt(res.slice(3, 9), 10);
//               // }

//               if (
//                 ["metricViewReadAll", "metricInquiryAll"].indexOf(field) > -1
//               ) {
//                 // Sometimes the value is a value (0), some times it is a link
//                 return parseInt(res, 10);
//               }

//               // Default string
//               return res;
//             }
//             return res;
//           },
//           selector,
//           field
//         );

//         currentResult[field] = currentValue;
//       }

//       currentResult["source"] = "KENBIYA";
//       results.push(currentResult);
//     }

//     console.log("🔥 results🔥 🔥");
//     console.log(results);

//     // *********************************************************
//     // Step 3: Saving data
//     // *********************************************************
//     // let url = "";


//     // let res = await postRequest(url, JSON.stringify(results));
//   } catch (err: any) {
//     console.log("🔥err🔥");
//     console.log(err);

//     await sendLark(
//       `[KENBIYA Metrics Error]` + err,
//       "https://open.larksuite.com/open-apis/bot/v2/hook/031715f0-6815-4e10-8cea-df82ded6fd0b"
//     );

//     if (page) {
//       await page.close();
//     }
//   } finally {
//     if (page) {
//       await page.close();
//     }

//     if (browser) {
//       await browser.disconnect();
//       await browser.close();
//     }

//     return;
//   }
// };
