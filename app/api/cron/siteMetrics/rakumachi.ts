

// import { getChromeBrowser } from "@/lib/thirdParty/chromeBrowser";
// import { getPageWithRandomUserAgent } from "@/lib/thirdParty/chromeBrowser";
// import { sendLark } from "@/lib/thirdParty/lark";

// export async function GET(req: Request) {
//   if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
//     return new Response("Unauthorized", { status: 401 });
//   }

//   if (req.method !== "GET") {
//     return new Response("Method Not Allowed", { status: 405 });
//   }

//   let browser;
//   let page;

//   const { RAKUMACHI_PASSWORD, RAKUMACHI_USERNAME } = process.env;

//   if (!RAKUMACHI_USERNAME || !RAKUMACHI_PASSWORD) {
//     throw new Error("RAKUMACHI_USERNAME or RAKUMACHI_PASSWORD is not set");
//   }

//   try {
//     browser = await getChromeBrowser();
//     page = await getPageWithRandomUserAgent(browser);

//     const ADMIN_LOGIN_URL = "https://www.rakumachi.jp/realtor.php";
//     await page.goto(ADMIN_LOGIN_URL);
//     await new Promise(resolve => setTimeout(resolve, 3000));

//     await page.type("#login_e_mail", RAKUMACHI_USERNAME);
//     await page.type("#login_password", RAKUMACHI_PASSWORD);

//     await page.click("#submit input");
//     await new Promise(resolve => setTimeout(resolve, 2000));

//     // ***********************************************
//     // Step 2: Gettting data needed
//     const ADMIN_URL = "https://www.rakumachi.jp/realtor.php/property";
//     await page.goto(ADMIN_URL);
//     await new Promise(resolve => setTimeout(resolve, 3000));
//     // await page.reload(); // reload because sometimes adds pop up
//     // await new Promise(resolve => setTimeout(resolve, 3000));

//     // Getting the count
//     // the total rows = 1 row on top + 4x number of items row //

//     const totalTrCount = await page.evaluate(
//       (sel) => document.querySelectorAll(sel).length,
//       "table tr"
//     );

//     const totalItemCount = (totalTrCount - 2) / 4; // 2 rows for header

//     console.log(`🔥 Total records is ${totalItemCount}🔥 🔥`);

//     let results = [];

//     // record_date: { type: STRING(50), allowNull: false },
//     // property_uuid: { type: UUID, allowNull: true },
//     // source: { type: STRING(50), allowNull: false },
//     // title: { type: STRING(250), allowNull: true },

//     // metric_view_read: { type: INTEGER, allowNull: true },
//     // metric_view_read_aggr: { type: INTEGER, allowNull: true },
//     // metric_fav: { type: INTEGER, allowNull: true },
//     // metric_fav_aggr: { type: INTEGER, allowNull: true },
//     // metric_inquiry: { type: INTEGER, allowNull: true },
//     // metric_inquiry_aggr: { type: INTEGER, allowNull: true },

//     let newDataSelectors = {
//       propertyId: ".propertyInfo div:nth-child(2) p:nth-child(1)", // 1st row
//       status: ".propertyList__border .table__center span:not(.no_disp) a",
//       title: ".propertyInfo div:nth-child(2) p:nth-child(2) a", // 1st row
//       address: ".propertyInfo div:nth-child(2) p:nth-child(3)", // 1st row
//       price: ".propertyInfo div:nth-child(2) p:nth-child(4) span:nth-child(1)", // 1st row
//       roi: ".propertyInfo div:nth-child(2) p:nth-child(4) span:nth-child(2)", // 1st row
//       metricViewReadAll: "td:nth-child(8)", // 1st row
//       metricFavAll: "td:nth-child(9)", // 1st row
//       metricInquiryAll: "td:nth-child(10)", // 1st row
//       registrationDate: "td:nth-child(4) li:nth-child(1)", //  [登]2024/10/08
//     };

//     let currentValue;
//     for (let i = 0; i < totalItemCount; i++) {
//       console.log(`🔥 Getting data for the ${i + 1}th result 🔥`);

//       let currentResult = {};
//       for (const field of Object.keys(newDataSelectors)) {
//         let selector = `tr:nth-child(${2 + i * 4 + 1}) ${newDataSelectors[field]
//           }`;

//         currentValue = await page.evaluate(
//           (sel, field) => {
//             let res = document.querySelector(sel);

//             if (res !== null) {
//               if (res.textContent !== null) {
//                 res = res.textContent.replace(/(\r\n\t|\n|\r\t|\s)/gm, "");
//               }

//               if (field === "price") {
//                 return parseInt(res.replace("万円", "").replace(",", ""), 10);
//               }

//               if (field === "roi") {
//                 return parseFloat(res.replace("%", ""));
//               }

//               // e.g. "No.26240251棟アパート21A02"
//               if (field === "propertyId") {
//                 return parseInt(res.slice(3, 9), 10);
//               }

//               if (
//                 [
//                   "metricViewReadAll",
//                   "metricFavAll",
//                   "metricInquiryAll",
//                 ].indexOf(field) > -1
//               ) {
//                 return parseInt(res, 10);
//               }

//               if (field === "registrationDate") {
//                 return res.replace("[登]", "");
//               }

//               // Default string
//               return res;
//             }
//             return res;
//           },
//           selector,
//           field
//         );

//         currentResult[field] = currentValue;
//         currentResult["source"] = "RAKUMACHI";
//       }

//       if (currentResult.status !== "非掲載") {
//         results.push(currentResult);
//       }
//     }

//     // *********************************************************
//     // Step 3: Saving data
//     // *********************************************************
//     // let url = "";

//     // if (process.env.NODE_ENV === "development") {
//     //   url = "http://localhost:7001/api/task/saveSiteMetrics";
//     //   // url = "https://api.tll.jp/api/user/simulations/email";
//     // } else {
//     //   url = "https://api.tll.jp/api/task/saveSiteMetrics";
//     // }

//     // let res = await postRequest(url, JSON.stringify(results));
//   } catch (err: any) {
//     console.log("🔥err🔥");
//     console.log(err);

//     if (page) {
//       await page.close();
//     }

//     await sendLark(
//       `[Rakumachi Metrics Error]` + err,
//       "https://open.larksuite.com/open-apis/bot/v2/hook/031715f0-6815-4e10-8cea-df82ded6fd0b"
//     );
//   } finally {
//     if (page) {
//       await page.close();
//     }

//     if (browser) {
//       await browser.disconnect();
//       await browser.close();
//     }

//     return;
//   }
// };
