// import puppeteer from "puppeteer";

// async function fetchXiaohongshuPosts() {
//   const url =
//     "https://www.xiaohongshu.com/user/profile/585731be5e87e74511f79c14";
//   const browser = await puppeteer.launch({ headless: true });
//   const page = await browser.newPage();

//   // 随机化用户代理
//   const userAgent = randomUseragent.getRandom();
//   console.log(`使用的用户代理: ${userAgent}`);
//   await page.setUserAgent(userAgent);

//   try {
//     await page.goto(url, { waitUntil: "networkidle2" });

//     // 获取帖子信息
//     const posts = await page.evaluate(() => {
//       const postElements = document.querySelectorAll(".note-item");
//       const postData: any[] = [];

//       postElements.forEach((post) => {
//         const titleElement = post.querySelector(".note-title");
//         const linkElement = post.querySelector("a");
//         const likeElement = post.querySelector(".like-count");

//         const title = titleElement ? titleElement.innerText : "无标题";
//         const link = linkElement ? linkElement.href : "无链接";
//         const likeCount = likeElement ? parseInt(likeElement.innerText) : 0;

//         postData.push({ title, link, likeCount });
//       });

//       return postData;
//     });

//     console.log("获取的帖子信息:", posts);
//   } catch (error) {
//     console.error("获取帖子信息时出错:", error);
//   } finally {
//     await browser.close();
//   }
// }

// fetchXiaohongshuPosts();
