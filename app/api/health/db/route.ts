// app/api/health/db/route.ts
import { NextResponse } from 'next/server';
import { checkDatabaseConnection } from '@/lib/prismaUtils';
import { logger } from '@/lib/logger';

export async function GET() {
  try {
    const isHealthy = await checkDatabaseConnection();
    
    if (isHealthy) {
      return NextResponse.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        database: 'connected'
      });
    } else {
      return NextResponse.json({ 
        status: 'unhealthy', 
        timestamp: new Date().toISOString(),
        database: 'disconnected'
      }, { status: 503 });
    }
  } catch (error) {
    logger.error('🔥 Database health check failed:', error);
    return NextResponse.json({ 
      status: 'error', 
      timestamp: new Date().toISOString(),
      error: 'Database health check failed'
    }, { status: 500 });
  }
}
