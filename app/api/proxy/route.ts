import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  try {
    // Extract the target URL from the query
    const { searchParams } = new URL(req.url);
    const targetUrl = searchParams.get("url");

    if (!targetUrl) {
      return NextResponse.json({ error: "Missing URL parameter" }, { status: 400 });
    }

    if (!targetUrl.startsWith("http")) {
      return NextResponse.json({ error: "Invalid URL" }, { status: 400 });
    }

    // Fetch the target URL
    const response = await fetch(targetUrl, {
      headers: {
        ...req.headers, // Forward request headers
        "User-Agent": "Mozilla/5.0", // Mimic a real browser
      },
    });

    if (!response.ok) {
      return NextResponse.json({ error: "Failed to fetch the target URL" }, { status: response.status });
    }

    // Get response headers & content type
    const contentType = response.headers.get("content-type");

    // Handle binary data (images, fonts, videos, etc.)
    if (contentType && (contentType.includes("image") || contentType.includes("font") || contentType.includes("video"))) {
      const buffer = await response.arrayBuffer();
      return new NextResponse(Buffer.from(buffer), {
        status: response.status,
        headers: {
          "Content-Type": contentType,
          "Cache-Control": "public, max-age=3600", // Cache for 1 hour
        },
      });
    }

    // Handle HTML content (rewrite relative URLs)
    let body = await response.text();
    if (contentType && contentType.includes("text/html")) {
      body = body.replace(/(href|src)="\/(.*?)"/g, `$1="${targetUrl}/$2"`);
    }

    // Return modified response
    return new NextResponse(body, {
      status: response.status,
      headers: {
        "Content-Type": contentType || "text/plain",
        "Cache-Control": "public, max-age=3600",
      },
    });
  } catch (error) {
    console.error("Proxy Error:", error);
    return NextResponse.json({ error: "Failed to load content" }, { status: 500 });
  }
}