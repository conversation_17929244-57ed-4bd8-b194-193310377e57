import { prisma } from "@/lib/prisma"
import { NextResponse } from "next/server"

export async function GET() {
    try {
        const projects = await prisma.$queryRaw`
      SELECT 
        id,
        composite_title as "compositeTitle",
        name_ja as name,
        address,
        address_chiban as "addressChiban",
        project_type as type,
        project_usage as "projectUsage",
        project_level_above_ground as "levelAboveGround",
        project_level_below_ground as "levelBelowGround",
        construction_area as "constructionArea",
        project_start_date as "projectStartDate",
        project_end_date as "projectEndDate",
        record_created_at as "recordCreatedAt"
      FROM pro_projects
      ORDER BY project_start_date DESC
      LIMIT 10
    `

        return NextResponse.json(projects)
    } catch (error) {
        console.error('🔥 Error fetching projects:', error)
        return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 })
    }
} 