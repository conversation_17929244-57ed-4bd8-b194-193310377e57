import { NextRequest, NextResponse } from 'next/server'
import { generateFileTree, getFallbackFileTree } from '@/app/wiki/utils/generateFileTree'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const lang = searchParams.get('lang') || 'ja'
  
  // Validate language
  const validLangs = ['ja', 'en', 'zh']
  if (!validLangs.includes(lang)) {
    return NextResponse.json(
      { error: 'Invalid language parameter' },
      { status: 400 }
    )
  }
  
  try {
    console.log(`🔥 [API] Generating file tree for language: ${lang}`)
    
    // Try to generate file tree from filesystem
    let fileTree = await generateFileTree(lang)
    
    // If no files found, use fallback
    if (fileTree.length === 0) {
      console.log(`🔥 [API] No files found for ${lang}, using fallback`)
      fileTree = getFallbackFileTree(lang)
    }
    
    return NextResponse.json({
      success: true,
      data: fileTree,
      language: lang
    })
  } catch (error) {
    console.error(`🔥 [API] Error generating file tree for ${lang}:`, error)
    
    // Return fallback structure on error
    const fallbackTree = getFallbackFileTree(lang)
    
    return NextResponse.json({
      success: true,
      data: fallbackTree,
      language: lang,
      fallback: true
    })
  }
}
