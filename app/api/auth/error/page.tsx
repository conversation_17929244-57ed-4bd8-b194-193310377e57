'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { AlertTriangle } from 'lucide-react';

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  const errorMessages: Record<string, string> = {
    AccessDenied: 'アクセスが拒否されました。Googleアカウントが未登録か、権限がありません。',
    CredentialsSignin: 'メールアドレスまたはパスワードが間違っています。',
    OAuthAccountNotLinked: 'このアカウントは別のログイン方法で登録されています。',
    default: 'ログイン中にエラーが発生しました。',
  };

  const message = error ? errorMessages[error] || errorMessages.default : errorMessages.default;

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background text-foreground">
      <div className="flex flex-col items-center p-6 rounded-lg shadow-sm bg-white dark:bg-neutral-900">
        <AlertTriangle className="h-10 w-10 text-red-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2">エラーが発生しました</h1>
        <p className="text-center text-sm text-gray-600 dark:text-gray-300 mb-4">{message}</p>
        <Link href="/" className="text-sm text-blue-500 underline">
          ホームに戻る
        </Link>
      </div>
    </div>
  );
}