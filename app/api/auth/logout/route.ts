import { auth, signOut } from "@/lib/auth";
import { LucideGripVertical } from "lucide-react";

export async function POST(req: Request) {
  const session = await auth(); // 获取当前会话
  if (!session?.user) return new Response("Unauthorized", { status: 401 });

  // 这里可以添加注销逻辑，例如清除会话
  console.log('🔥signOut backend');
  await signOut();

  return new Response("Logged out successfully", { status: 200 });
}
