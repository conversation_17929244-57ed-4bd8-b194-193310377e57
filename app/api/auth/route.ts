import { auth } from "@/lib/auth"; // Use NextAuth or custom auth logic
import { logger } from "@/lib/logger";
import { log } from "console";

export async function GET(req: Request) {
  const session = await auth(); // Fetch authenticated session
  // logger.debug("🔥 in the lib/auth route, the session is", session);
  if (!session?.user) return new Response("Unauthorized", { status: 401 });

  return Response.json(session.user);
}   