import { logger } from "@/lib/logger";
import { LARK_URLS, sendLark, sendLarkCard } from "@/lib/thirdParty/lark";

import crypto from 'crypto';
import dayjs from "dayjs";

function sha1(data: Buffer, secret: string): string {
  return crypto.createHmac('sha1', secret).update(data).digest('hex');
}

export async function POST(request: Request) {
  const { INTEGRATION_SECRET } = process.env;

  if (!INTEGRATION_SECRET) {
    throw new Error('No integration secret found');
  }

  const rawBody = await request.text();
  const rawBodyBuffer = Buffer.from(rawBody, 'utf-8');
  const bodySignature = sha1(rawBodyBuffer, INTEGRATION_SECRET);

  if (bodySignature !== request.headers.get('x-vercel-signature')) {
    await sendLark({
      message: "Sha check not successful, bodySignature is " + bodySignature,
      url: LARK_URLS.BOT_DEPLOYMENT_CHANNEL,
    });
    return Response.json({
      code: 'invalid_signature',
      error: "signature didn't match",
    });
  }

  const getCurrentTime = () => {
    return dayjs().add(9, 'hour').format('YYYY-MM-DD HH:mm:ss');
  }

  try {
    const json = JSON.parse(rawBodyBuffer.toString('utf-8'));
    const eventType = json.type;

    const message = `[${getCurrentTime()}][${eventType}]`;

    const deployment = json.payload.deployment;
    const commitMessage = deployment.meta.githubCommitMessage;
    const deploymentName = deployment.name;
    const deploymentUrl = json.payload.links.deployment;

    let formattedMessage = '';

    switch (eventType) {
      case "deployment.created":
        formattedMessage = `🚀 ${deploymentName} | commit: ${commitMessage} | 🔗 [View](${deploymentUrl})`;
        break;
      case "deployment.succeeded":
        formattedMessage = `✅ ${deploymentName} | commit: ${commitMessage} | 🔗 [View](${deploymentUrl})`;
        break;
      case "deployment.error":
        formattedMessage = `❌ ${deploymentName} | commit: ${commitMessage} | 🔗 [View](${deploymentUrl})`;
        break;
      case "deployment.promoted":
        formattedMessage = `🌐 ${deploymentName} | commit: ${commitMessage} | 🔗 [View](${deploymentUrl})`;
        break;
      default:
        console.warn("未知事件类型:", eventType); // 处理未知事件类型
        return Response.json({
          code: 'unknown_event_type',
          error: "未知事件类型",
        });
    }

    // 发送消息到 Lark
    await sendLark({
      message: `${message} ${formattedMessage}`,
      url: LARK_URLS.BOT_DEPLOYMENT_CHANNEL,
    });

    return Response.json({ success: true });
  } catch (error) {
    console.error("Webhook error:", error);
    return Response.json({
      code: 'internal_server_error',
      error: "内部服务器错误",
    });
  }
}