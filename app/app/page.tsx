import { Separator } from "@/components/ui/separator";
import { Download, Share } from "lucide-react"; // 引入 lucide 图标
import Image from "next/image";
import FooterForNonLogin from "@/components/footerForNonLogin";

export default function AppPage() {
  return (
    <div className="w-full h-screen mx-auto p-4 flex flex-col gap-4 bg-white p-8 overflow-y-auto pt-(--header-height)">
      <h1 className="text-2xl sm:text-3xl font-bold flex items-center w-full text-center justify-center mt-10">
        <Download className="mr-2" /> アプリのダウンロード
      </h1>

      <div className="text-sm text-gray-600">
        <strong>UrbalyticsはPWAアプリとしてインストールできます。</strong>
        PWAアプリは、デバイスにインストールできるウェブアプリです。ネイティブアプリとは異なり、ダウンロードやインストールは必要ありません。ただし、一部のブラウザはPWAをサポートしていません。サポートされているブラウザのリストは<a href="https://caniuse.com/pwa" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:text-blue-700">こちら</a>でご確認ください。
      </div>

      <Separator className="" />

      <div>
        <h2 className="text-xl font-semibold">✨ PWAアプリの利点</h2>
        <ul className="list-disc ml-5">
          <li>✅ ネイティブアプリのように動作します（プッシュ通知機能）</li>
          <li>✅ オフラインでも利用可能です</li>
          <li>✅ すぐにアクセスでき、ダウンロードは不要です</li>
          <li>✅ 高速なパフォーマンスを提供します</li>
        </ul>
      </div>

      <Separator className="" />

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div className="col-span-1 sm:col-span-2 flex flex-col gap-4">
          <div>
            <h2 className="text-xl font-semibold">📱 iPhone / iPad (Safari)</h2>
            <p>1. Safariで<a href="https://www.urbalytics.jp" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:text-blue-700">Urbalyticsのウェブサイト</a>を開きます。</p>
            <p>2. 画面下の <strong>共有ボタン (
              <Share className="inline-block" />
              )</strong> をタップします。</p>
            <p>3. <strong>「ホーム画面に追加」</strong> を選択します。</p>
            <p>4. <strong>「追加」</strong> ボタンを押すと、ホーム画面にアプリのアイコンが表示されます。</p>
          </div>

          <div>
            <h2 className="text-xl font-semibold">📱 Android (Chrome / Edge)</h2>
            <p>1. ChromeまたはEdgeでUrbalyticsを開きます。</p>
            <p>2. 画面下の <strong>「アプリをインストール」</strong> または「ホーム画面に追加」ボタンを押します。</p>
            <p>3. 確認画面が出たら「追加」を選択します。</p>
          </div>

          <div>
            <h2 className="text-xl font-semibold">🖥️ Windows / Mac (Chrome / Edge)</h2>
            <p>1. ChromeまたはEdgeでUrbalyticsを開きます。</p>
            <p>2. URLバーの右にある <strong>(⤓) アイコン</strong> をクリックします。</p>
            <p>3. 「インストール」ボタンを押します。</p>
          </div>
        </div>
        <div className="flex flex-col justify-center items-center">
          <Image src="/assets/pwa-ios.jpg" alt="PWAアプリのインストール" width={1000} height={400} />
          <div className="text-xs text-gray-600 mt-2">
            IOS (Safari) ホーム画面に追加
          </div>
        </div>
      </div>

      <FooterForNonLogin />
    </div>
  );
}