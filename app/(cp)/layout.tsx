"use client";

import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useUserStore } from "@/store/user";
import { getUsersAction } from "@/actions/users";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useAuthStore } from "@/store/auth";
import FooterForNonLogin from "@/components/footerForNonLogin";
import CommandMenu from "@/components/CommandMenu";
import { PwaInstallButton } from "@/components/PwaInstallButton";
import { SessionProvider } from "next-auth/react";
import {
  createUserPushSubscription,
  getUserCurrentActivePushSubscriptionsForDevice,
} from "@/actions/tllUserPushSubscriptions";
import { toast } from "@/hooks/use-toast";
import { ToastAction } from "@/components/ui/toast";
import {
  getDeviceId,
  urlBase64ToUint8Array,
} from "./my/account/PushNotificationManager";
import { AppSidebar } from "@/components/navBar/AppSidebar";
import {
  Breadcrumb,
  BreadcrumbLink,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { getDailyUsage } from "@/actions/dashboard";
import { useUserUsageStore } from "@/store/userUsage";
import Header from "@/components/header";
import FeedbackButton from "@/components/ui/FeedbackFloatBotton";

interface LayoutProps {
  headerTitle?: string; // Title of the header
  headerRight?: React.ReactNode; // Custom right-side buttons
  children: React.ReactNode; // Main content
}

export default function Layout({
  children,
  headerTitle,
  headerRight,
}: LayoutProps) {
  const pathname = usePathname();
  const pathSegments = pathname.split("/").filter(Boolean);

  const { setUser, users } = useUserStore();
  const { currentUser } = useAuthStore();

  // 获取用户信息并保存到用户状态管理
  const getUsers = async () => {
    try {
      const response = await getUsersAction();
      if (response.success) {
        setUser(response.data as TllUserProps[]);
      }
    } catch (error) {
      console.error("发生错误:", error);
    }
  };

  // ✅ Helper Function to Convert VAPID Key to Uint8Array
  // const urlBase64ToUint8Array = (base64String: string) => {
  //   const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
  //   const base64 = (base64String + padding).replace(/-/g, "+").replace(/_/g, "/");

  //   const rawData = atob(base64);
  //   const outputArray = new Uint8Array(rawData.length);
  //   for (let i = 0; i < rawData.length; i++) {
  //     outputArray[i] = rawData.charCodeAt(i);
  //   }
  //   return outputArray;
  // }

  // async function subscribeToPush() {
  //   if (typeof window === "undefined") return;

  //   if (!("serviceWorker" in navigator)) {
  //     console.warn("❌ Service Workers are not supported in this browser.");
  //     return;
  //   }

  //   // if (window.location.hostname === "localhost") {
  //   //   console.warn("❌ Service Workers are not supported in localhost.");
  //   //   return;
  //   // }

  //   try {
  //     // ✅ Check existing Service Worker registrations
  //     let registration;
  //     let registrations = await navigator.serviceWorker.getRegistrations();
  //     if (registrations.length === 0) {
  //       console.warn("❌ No Service Worker registered in localhost... regisering...");
  //       registration = await navigator.serviceWorker.register("/sw.js");
  //       console.log("✅ Service Worker Registered:", registration);

  //     } else {
  //       console.log("✅ Existing Service Workers:", registrations);
  //       registration = registrations[0];
  //     }

  //     // ✅ Ensure Service Worker is ready before subscribing
  //     const swReady = await navigator.serviceWorker.ready;
  //     console.log("✅ Service Worker is Ready:", swReady);

  //     // ✅ Get VAPID Public Key from environment variables
  //     const publicKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY;
  //     if (!publicKey) {
  //       console.error("❌ VAPID Public Key is missing! Check your .env.local file.");
  //       return;
  //     }

  //     console.log("🔥 Subscribing to Push Notifications...");

  //     // ✅ Convert public key to Uint8Array
  //     const applicationServerKey = urlBase64ToUint8Array(publicKey);

  //     // ✅ Subscribe to push notifications
  //     const subscription = await registration.pushManager.subscribe({
  //       userVisibleOnly: true,
  //       applicationServerKey,
  //     });

  //     console.log("🔔 Push Subscription:", subscription);

  //     // ✅ Send subscription to the server
  //     const response = await fetch("/api/cron/push", {
  //       method: "POST",
  //       headers: { "Content-Type": "application/json" },
  //       body: JSON.stringify({ subscription }),
  //     });

  //     if (!response.ok) {
  //       throw new Error("❌ Failed to send push subscription to server.");
  //     }

  //     console.log("✅ Push Subscription Registered Successfully.");
  //   } catch (error) {
  //     console.error("❌ Error in push subscription:", error);
  //   }
  // }

  const { setTodaySearchUsageData } = useUserUsageStore();

  const fetchDailyUsage = async () => {
    const response = await getDailyUsage();

    if (response.success) {
      setTodaySearchUsageData(response.data.todaySearchCount);
    }
  };

  const deviceId = getDeviceId();

  async function subscribeToPush() {
    try {
      const registration = await navigator.serviceWorker.ready;
      const sub = (await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          "BCzpwf2zq50OBW6OKa3mp3Kzcu9vIZJt-tehye2WBQovqw-tqhaBXrNpxH7BZHQWhjj3g37JzgjE_0ECkDG2Q1g",
        ),
      })) as any;

      let res = await createUserPushSubscription(sub.toJSON(), deviceId);
      if (res.success) {
        toast({
          title: "通知を受け取るようにしました",
        });
      } else {
        toast({
          title: "通知を受け取るようにできませんでした",
          description: res.message,
        });
      }
    } catch (error: any) {
      console.error("Error subscribing to push notifications:", error);
      toast({
        title: "通知を受け取るようにできませんでした",
        description: error.message,
      });
    }
  }

  useEffect(() => {
    if (!users.length) {
      getUsers();
    }
    // subscribeToPush();

    fetchDailyUsage();
  }, []); // 依赖于 setUser

  async function registerServiceWorker() {
    const registration = await navigator.serviceWorker.register("/sw.js", {
      scope: "/",
      updateViaCache: "none",
    });
    // const sub = await registration.pushManager.getSubscription() as any
    // setSubscription(sub)
  }

  useEffect(() => {
    if ("serviceWorker" in navigator && "PushManager" in window) {
      console.log("🔥 Service Worker is supported");
      registerServiceWorker();

      // 0707 disabling push for now
      // getUserCurrentActivePushSubscriptionsForDevice(deviceId).then(async (res: any) => {
      //   if (!res.success) {
      //     await new Promise(resolve => setTimeout(resolve, 1000 * 30));

      //     toast({
      //       title: "プッシュ通知を有効にしますか？",
      //       description: "お気に入り物件の更新情報をプッシュ通知で受け取れます。",
      //       duration: 1000 * 10,
      //       action: <ToastAction altText="On" className="" onClick={() => {
      //         subscribeToPush();
      //       }}>ONにする</ToastAction>
      //       // {/* <ToastAction altText="Off" className="bg-neutral-100 text-black border-none">キャンセル</ToastAction> */}
      //     })
      //     // setActiveSubscription(res.data)
      //   }
      // })
    }
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <>
      <Header isInBackendLayout={true} isShowingMidMenu={false} />
      <SidebarProvider
        style={{
          paddingTop: "var(--header-height)",
        }}
      >
        <AppSidebar />
        <SidebarInset className="">
          <SidebarTrigger className="fixed z-51 h-8 w-8 top-2 right-4 px-2 bg-white block sm:hidden" />

          {/* <header className="flex h-12 shrink-0 items-center gap-2 transition-[width,height] ease-linear border-b border-neutral-200">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />

            <Breadcrumb>
              <BreadcrumbList>
                {pathSegments.map((_, index) => {
                  const path = `/${pathSegments.slice(0, index + 1).join('/')}`;
                  let title = "";
                  // 如果path是navMain中的url，则直接使用title
                  if (renderSideBarMainData(currentUser).find((item: any) => item?.url === path)) {
                    title = renderSideBarMainData(currentUser).find((item: any) => item?.url === path)?.title || pathSegments[index].charAt(0).toUpperCase() + pathSegments[index].slice(1);
                  } else {
                    // 否则，使用navMain中的menus中的title
                    title = renderSideBarMainData(currentUser as any).flatMap((item: any) => item?.menus as any).find((menu: any) => menu?.url === path)?.title || pathSegments[index].charAt(0).toUpperCase() + pathSegments[index].slice(1);
                  }

                  return (
                    <React.Fragment key={index}>
                      <BreadcrumbItem>
                        <BreadcrumbLink href={path}>
                          {title.slice(0, 6)} {title.length > 6 && "..."}
                        </BreadcrumbLink>
                      </BreadcrumbItem>
                      {index < pathSegments.length - 1 && <BreadcrumbSeparator />}
                    </React.Fragment>
                  );
                })}
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header> */}

          <div
            className="min-h-[calc(100vh-var(--header-height))]"
            style={
              {
                // paddingTop: 'var(--header-height)',
              }
            }
          >
            {/* <div className="h-[calc(100vh-var(--header-height)-var(--footer-height))] overflow-y-auto"> */}
            {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && (
              <CommandMenu />
            )}
            {children}
            <PwaInstallButton />
            {/* <Button
            onClick={scrollToTop}
            className="fixed bottom-6 right-6 bg-gray-900 text-white p-2 px-2 rounded-full shadow-md transition-opacity duration-300 hover:bg-gray-700"
          >
            <ArrowUp className="w-5 h-5" />
          </Button>*/}
          </div>

          <FooterForNonLogin isInBackendLayout={true} />
        </SidebarInset>
      </SidebarProvider>

      <FeedbackButton />
    </>
  );
}
