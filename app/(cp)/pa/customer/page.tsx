"use client"

import { Suspense, useEffect, useState } from "react";
import { DataTable } from "@/components/ui/data-table"; // 假设你有一个数据表组件
import { CustomerNeedProps, CustomerProps } from "@/lib/definitions";
import { fetchCustomersAction } from "@/actions/customers";
import { needColumns, padNeedsWithMatch } from "./needColumns";
import { Button } from "@/components/ui/button";
import { Eye, Pencil } from "lucide-react";
import { useRouter } from "next/navigation";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuthStore } from "@/store/auth";
import { Badge } from "@/components/ui/badge";
import { getRecordMatchSimpleForNeedIdsAction } from "@/actions/customerNeeds";
import Link from "next/link";

export default function CustomerPage() {
  const [customers, setCustomers] = useState<CustomerProps[]>([]);
  const { currentUser } = useAuthStore();
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null);
  const [paddedColumns, setPaddedColumns] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const fetchCustomers = async () => {
    const response = await fetchCustomersAction();

    if (response.success) {
      setCustomers(response.data as CustomerProps[]);
    }
  };

  const fetchCustomerAndNeedMatches = async () => {
    setIsLoading(true);
    const res = await getRecordMatchSimpleForNeedIdsAction({ needIds: customers.filter((customer: CustomerProps) => customer.agentUser?.name === selectedAgent).map((customer: CustomerProps) => customer.needs?.map((need: CustomerNeedProps) => need.id)).flat() as string[] });

    setPaddedColumns(padNeedsWithMatch(res.data, needColumns));
    setIsLoading(false);
  };

  useEffect(() => {
    if (currentUser) {
      fetchCustomers();
      setSelectedAgent(currentUser?.name || null);
    }
  }, [currentUser]);

  useEffect(() => {
    if (selectedAgent && customers.length > 0) {
      fetchCustomerAndNeedMatches();
    }
  }, [selectedAgent, customers]);

  const distinctAgents = Array.from(new Set(customers.map((customer: CustomerProps) => customer.agentUser?.name))).filter(Boolean);

  return (
    <div className="">
      <div className="p-4 flex flex-row gap-4 justify-between items-center border-b border-neutral-200">
        <h1 className="text-2xl font-bold">顧客リスト</h1>

        <div className="flex flex-row gap-4">
          <Button variant="outline" onClick={() => {
            router.push("/pa/customer/create");
          }}>
            新規顧客登録
          </Button>
        </div>
      </div>

      {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && <div className="p-4">
        <Tabs value={selectedAgent || undefined} className="w-full" onValueChange={(value) => {
          setSelectedAgent(value);
        }}>
          <TabsList>
            {distinctAgents.map((agentName, index) => (
              <TabsTrigger key={index} value={agentName}>
                {agentName}
                <Badge variant="outline" className="ml-2">
                  {customers.filter((customer: CustomerProps) => customer.agentUser?.name === agentName).length}
                </Badge>
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>
      }

      <div className="px-4 flex flex-col gap-4">
        {customers.length ? customers.filter((customer: CustomerProps) => selectedAgent ? customer.agentUser?.name === selectedAgent : true).map((customer: CustomerProps) => (
          <div key={customer.id} className="bg-neutral-50 p-4">
            <div className="flex flex-row gap-4 justify-between items-center" >
              <div className="flex flex-col" >
                <div className="text-lg font-bold">{customer.name}</div>

                <div className="flex flex-row gap-4">
                  <div className="text-sm text-neutral-500">担当: {customer?.agentUser?.name}</div>
                  <div className="text-sm text-neutral-500">住所: {customer?.address || "-"}</div>
                  <div className="text-sm text-neutral-500">年齢: {customer?.age || "-"}</div>
                  <div className="text-sm text-neutral-500">収入: {customer?.income || "-"}</div>
                </div>
              </div>

              <div className="flex flex-row gap-2">
                <Link href={`/pa/customer/${customer.id}/edit`}>
                  <Button variant="outline" size="icon">
                    <Pencil />
                  </Button>
                </Link>

                <Link href={`/pa/customer/${customer.id}`}>
                  <Button variant="outline" size="icon">
                    <Eye />
                  </Button>
                </Link>
              </div>

            </div>

            <div className="mt-2">
              {customer.needs && customer.needs.length > 0 ? <DataTable data={customer.needs} columns={paddedColumns} searchColumn="name" showFooter={false} isLoading={isLoading} /> : <div className="text-neutral-500">需要がありません</div>}
            </div>
          </div>
        )) : <div className="text-neutral-500">顧客が存在しません</div>}
      </div>
    </div>
  );
};