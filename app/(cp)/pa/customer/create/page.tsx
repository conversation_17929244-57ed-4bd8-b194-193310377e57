"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { createCustomerAction } from "@/actions/customers";
import { CustomerFormSchema, CustomerProps } from "@/lib/definitions";
import { toast } from "@/hooks/use-toast";
import { useState } from "react";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";

export default function ProfileForm() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { currentUser } = useAuthStore();
  const form = useForm<z.infer<typeof CustomerFormSchema>>({
    resolver: zodResolver(CustomerFormSchema),
    defaultValues: {
      name: "",
      email: "",
      language: "ja"
    },
  })

  // 2. Define a submit handler.
  const onSubmit = async (values: CustomerProps) => {
    // Filter out empty fields
    const filteredValues = Object.fromEntries(
      Object.entries(values).filter(([_, v]) => v !== "" && v !== null && v !== undefined)
    );

    console.log("values", filteredValues);

    let res = await form.trigger();
    if (!res) {
      toast({
        title: 'エラーが発生しました',
        description: 'エラーが発生しました',
      });
      return;
    }

    console.log("values", values);
    setIsLoading(true);
    const response = await createCustomerAction(filteredValues as CustomerProps);

    if (response.success) {
      toast({
        title: "顧客を作成しました",
        description: "顧客を作成しました",
      });
      await sendLark({
        message: `[⚙️][顧客作成][${currentUser?.name}は、${filteredValues.name} さんを作成しました]`,
        url: LARK_URLS.USER_ACTIVITY_CHANNEL,
      });
      router.push(`/pa/customer`);
    } else {
      toast({
        title: "顧客を作成できませんでした",
        description: "顧客を作成できませんでした",
      });
    }
    setIsLoading(false);
  }

  return (
    <div className="">
      <div className="p-4 flex flex-row gap-4 justify-between items-center border-b border-neutral-200">
        <h1 className="text-2xl font-bold">新規顧客リスト</h1>
      </div>

      <div className="p-4">
        <Form {...form}>
          <form onSubmit={async (e) => {
            console.log("e", e);
            e.preventDefault();
            // let formCheck = await form.trigger();
            // console.log("formCheck", formCheck);
            // if (!formCheck) {
            //   return;
            // }
            onSubmit(form.getValues());
          }} className="space-y-8">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>名前</FormLabel>
                  <FormControl>
                    <Input placeholder="name" {...field} />
                  </FormControl>
                  <FormMessage>{form.formState.errors.name?.message as string}</FormMessage>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>メール</FormLabel>
                  <FormControl>
                    <Input placeholder="email" {...field} />
                  </FormControl>
                  <FormMessage>{form.formState.errors.email?.message as string}</FormMessage>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="language"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>言語</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Language" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem key="zh" value="zh">中国語</SelectItem>
                      <SelectItem key="ja" value="ja">日本語</SelectItem>
                      <SelectItem key="en" value="en">英語</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    You can manage email addresses in your{" "}
                    <Link href="/examples/forms">email settings</Link>.
                  </FormDescription>
                  <FormMessage>{form.formState.errors.language?.message as string}</FormMessage>
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isLoading}>{isLoading ? "Loading..." : "Submit"}</Button>
          </form>
        </Form>
      </div>
    </div>
  )
}
