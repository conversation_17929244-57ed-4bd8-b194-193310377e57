"use client";

import { useEffect, useState } from "react";
import { getCapRateAnalysisData } from "@/actions/capRateAnalysis";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { getNearbyMaxMin80Percentile } from "@/lib/userLambdaRecord/getNearbyMaxMin80Percentile";
import { DataTable } from "@/components/ui/data-table";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel, paddColumnWithDistance } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { useAuthStore } from "@/store/auth";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { Loader2 } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface CapRateStats {
  max: number;
  min: number;
  avg: number;
  eightyPercentile: number;
  twentyPercentile: number;
  comparatorType: string;
}

export default function CapRateAnalysis({ 
  selectedOption 
}: { 
  selectedOption: { label: string; value: number; type: string } 
}) {
  const [nearbyRecords, setNearbyRecords] = useState<UserLambdaRecordProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [capRateStats, setCapRateStats] = useState<CapRateStats | null>(null);
  const [columns, setColumns] = useState<any[]>([]);
  const currentUser = useAuthStore((state) => state.currentUser);

  const calculateCapRateStats = (records: UserLambdaRecordProps[]): CapRateStats => {
    const capRates = records
      .map((record) => {
        if (record.yearlyIncome && record.price && record.price > 0) {
          return (record.yearlyIncome / record.price) * 100;
        }
        return null;
      })
      .filter((rate): rate is number => rate !== null && !isNaN(rate))
      .sort((a, b) => a - b);

    if (capRates.length === 0) {
      return {
        max: 0,
        min: 0,
        avg: 0,
        eightyPercentile: 0,
        twentyPercentile: 0,
        comparatorType: "Cap Rate (%)",
      };
    }

    const min = Math.min(...capRates);
    const max = Math.max(...capRates);
    const avg = capRates.reduce((sum, rate) => sum + rate, 0) / capRates.length;
    const twentyPercentile = capRates[Math.floor(capRates.length * 0.2)] || 0;
    const eightyPercentile = capRates[Math.floor(capRates.length * 0.8)] || 0;

    return {
      max: parseFloat(max.toFixed(2)),
      min: parseFloat(min.toFixed(2)),
      avg: parseFloat(avg.toFixed(2)),
      eightyPercentile: parseFloat(eightyPercentile.toFixed(2)),
      twentyPercentile: parseFloat(twentyPercentile.toFixed(2)),
      comparatorType: "Cap Rate (%)",
    };
  };

  const fetchCapRateData = async () => {
    setIsLoading(true);
    // Clear previous data when starting new search
    setNearbyRecords([]);
    setCapRateStats(null);

    try {
      const response = await getCapRateAnalysisData({ selectedOption });
      if (response.success && response.data) {
        setNearbyRecords(response.data.nearbyRecords);
        const stats = calculateCapRateStats(response.data.nearbyRecords);
        setCapRateStats(stats);
      }
    } catch (error) {
      console.error("🔥 Failed to fetch cap rate data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (selectedOption?.value) {
      fetchCapRateData();
    } else {
      // Clear data when no option is selected
      setNearbyRecords([]);
      setCapRateStats(null);
    }
  }, [selectedOption?.value, selectedOption?.type]);

  useEffect(() => {
    if (currentUser && currentUser?.accessLevel) {
      setColumns(
        paddColumnWithDistance(
          getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({
            currentUser: currentUser as TllUserProps,
          })
        )
      );
    }
  }, [currentUser]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin mr-2" />
        <span>データを読み込み中...</span>
      </div>
    );
  }

  if (!capRateStats || nearbyRecords.length === 0) {
    return (
      <div className="text-center p-8 text-gray-500">
        該当する建物データが見つかりませんでした
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Cap Rate Statistics Bar */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Cap Rate 統計情報</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-sm text-gray-500">最小値</div>
              <Badge variant="outline" className="text-lg font-bold">
                {capRateStats.min}%
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">20%</div>
              <Badge variant="secondary" className="text-lg font-bold">
                {capRateStats.twentyPercentile}%
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">平均</div>
              <Badge variant="default" className="text-lg font-bold">
                {capRateStats.avg}%
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">80%</div>
              <Badge variant="secondary" className="text-lg font-bold">
                {capRateStats.eightyPercentile}%
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-500">最大値</div>
              <Badge variant="outline" className="text-lg font-bold">
                {capRateStats.max}%
              </Badge>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-600 text-center">
            検索範囲: 5km以内 | 対象件数: {nearbyRecords.length}件
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">近隣建物データ</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable 
            columns={columns} 
            data={nearbyRecords} 
            defaultPageSize={20} 
            showFooter={true} 
            isLoading={isLoading} 
          />
        </CardContent>
      </Card>
    </div>
  );
}
