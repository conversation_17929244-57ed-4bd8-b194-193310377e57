"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import SearchBarCombinedUI from "@/components/ui/SearchBarCombinedUI";
import CapRateAnalysis from "./CapRateAnalysis";

export default function CapRateSection() {
  const [selectedOption, setSelectedOption] = useState<{
    label: string;
    value: number;
    type: string;
  } | null>(null);

  console.log("🔥 CapRateSection selectedOption:", selectedOption);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Cap Rate 分析(Beta版)</CardTitle>
          <CardDescription>
            収益物件のキャップレート分析ツール - 一棟収益物件のみ対応
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="w-full">
              <div className="mb-2 text-sm text-gray-600">
                場所、駅、建物名、住所で検索してください（例：渋谷、新宿駅、六本木ヒルズ）
              </div>
              <SearchBarCombinedUI
                selectedOption={selectedOption}
                setSelectedOption={setSelectedOption}
                recordType="BUILDING"
              />
            </div>

            {!selectedOption && (
              <div className="text-center p-8 text-gray-500">
                上記の検索ボックスで場所を選択してください
              </div>
            )}

            {selectedOption && (
              <CapRateAnalysis
                key={`${selectedOption.type}-${selectedOption.value}`}
                selectedOption={selectedOption}
              />
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
