"use client"

import { Separator } from "@/components/ui/separator"
import { useEffect, useMemo, useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { fuzzySearchProjectsAction } from "@/actions/proProject"
import { toast } from "@/hooks/use-toast"
import LeafletMapSimple from "@/components/LeafletMapSimple"
import { ProProject } from "@/lib/definitions/proProject"
import { mapper } from "../../an/(common)/recordTypeMapper"
import Link from "next/link"
import dayjs from "dayjs"
import KensetsuChart from "./devplan/KensetsuChart"
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark"
import { useAuthStore } from "@/store/auth"
import { DataTable } from "@/components/ui/data-table"
import { columns } from "./devplan/ProjectTableColumns"
import MapFilter, { sortProjects } from "./devplan/MapFilter"
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { se } from "date-fns/locale"
import { Toggle } from "@/components/ui/toggle"
import { usePathname, useSearchParams } from "next/navigation"
import { useRouter } from "next/navigation"
import { useUIStore } from "@/store/ui"
import DevPlan from "./devplan/DevPlan"
import Souba from "./souba/Souba"

export default function KensetsuPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [mapDataType, setMapDataType] = useState<string>(searchParams.get("mapDataType") || "kensetsu")

  return (
    <div className="w-full h-full overflow-y-auto">
      <Select
        value={mapDataType}
        onValueChange={(value) => {
          setMapDataType(value)
          router.push(`/it/insight?mapDataType=${value}`, { scroll: false })
        }}
      >
        <SelectTrigger className="absolute top-2 left-2 z-10 bg-white w-36 text-black border-white bg-black text-center text-white">
          <SelectValue placeholder="マップデータタイプ" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="kensetsu">建設計画図</SelectItem>
          <SelectItem value="souba">相場売買物件</SelectItem>
        </SelectContent>
      </Select>

      {mapDataType === "kensetsu" && <DevPlan />}

      {mapDataType === "souba" && <Souba />}
    </div >
  )
}