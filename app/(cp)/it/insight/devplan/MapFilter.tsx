"use client"

import { Select } from "@/components/ui/select"
import { SelectTrigger } from "@/components/ui/select"
import { SelectContent } from "@/components/ui/select"
import { SelectItem } from "@/components/ui/select"
import { SelectValue } from "@/components/ui/select"
import { ProProject } from "@/lib/definitions/proProject"
import dayjs from "dayjs"
import { useState, useEffect } from "react"

export const sortProjects = (typeFilter: string, projects: ProProject[]) => {
  const isValidDate = (date: string | null | undefined) => {
    return date && dayjs(date).isValid();
  };

  if (typeFilter === "DEMOLITION") {
    return projects.sort((a: any, b: any) => {
      // 首先按类型排序
      if (a.type === "DEMOLITION" && b.type !== "DEMOLITION") return 1;
      if (a.type !== "DEMOLITION" && b.type === "DEMOLITION") return -1;

      // 然后按日期排序，无效日期放后面
      if (!isValidDate(a.projectEndDate) && isValidDate(b.projectEndDate)) return 1;
      if (isValidDate(a.projectEndDate) && !isValidDate(b.projectEndDate)) return -1;
      if (!isValidDate(a.projectEndDate) && !isValidDate(b.projectEndDate)) return 0;

      return dayjs(b.projectEndDate).diff(dayjs(a.projectEndDate), "day");
    });
  }

  return projects.sort((a: any, b: any) => {
    // 无效日期放后面
    if (!isValidDate(a.projectEndDate) && isValidDate(b.projectEndDate)) return 1;
    if (isValidDate(a.projectEndDate) && !isValidDate(b.projectEndDate)) return -1;
    if (!isValidDate(a.projectEndDate) && !isValidDate(b.projectEndDate)) return 0;

    return dayjs(b.projectEndDate).diff(dayjs(a.projectEndDate), "day");
  });
}

export default function MapFilter({ typeFilter, setTypeFilter, areaFilter, setAreaFilter, usageFilter, setUsageFilter, yearFilter, setYearFilter }: { typeFilter: string, setTypeFilter: (typeFilter: string) => void, areaFilter: string, setAreaFilter: (areaFilter: string) => void, usageFilter: string, setUsageFilter: (usageFilter: string) => void, yearFilter: string, setYearFilter: (yearFilter: string) => void }) {
  return <>
    <div className="w-[100px] flex-1 bg-white rounded-lg" >
      <Select
        value={typeFilter || "all"}
        onValueChange={(value) => setTypeFilter(value as string)}
      >
        <SelectTrigger className="h-8 text-xs rounded-lg">
          <SelectValue placeholder="施工種類" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">施工種類</SelectItem>
          <SelectItem value="CONSTRUCTION">新築</SelectItem>
          <SelectItem value="ADDITION">増改築</SelectItem>
          <SelectItem value="DEMOLITION">解体</SelectItem>
        </SelectContent>
      </Select>
    </div>

    <div className="w-[100px] flex-1 bg-white rounded-lg" >
      <Select
        value={areaFilter || "all"}
        onValueChange={(value) => setAreaFilter(value as string)}
      >
        <SelectTrigger className="h-8 text-xs rounded-lg">
          <SelectValue placeholder="施工面積" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">施工面積</SelectItem>
          <SelectItem value="5000">5000以上</SelectItem>
          <SelectItem value="10000">10000以上</SelectItem>
        </SelectContent>
      </Select>
    </div>

    <div className="w-[100px] flex-1 bg-white rounded-lg" >
      <Select
        disabled={typeFilter === "DEMOLITION"}
        value={usageFilter || "all"}
        onValueChange={(value) => setUsageFilter(value as string)}
      >
        <SelectTrigger className="h-8 text-xs rounded-lg">
          <SelectValue placeholder="用途" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">用途</SelectItem>
          <SelectItem value="共同住宅">共同住宅</SelectItem>
          <SelectItem value="店舗">店舗</SelectItem>
          <SelectItem value="事務所">事務所</SelectItem>
          <SelectItem value="工場">工場</SelectItem>
          <SelectItem value="ホテル">ホテル</SelectItem>
          <SelectItem value="高齢者施設">高齢者施設</SelectItem>
          <SelectItem value="文教施設">文教施設</SelectItem>
        </SelectContent>
      </Select>
    </div>

    <div className="w-[80px] flex-1 bg-white rounded-lg" >
      <Select
        value={yearFilter || "all"}
        onValueChange={(value) => setYearFilter(value as string)}
      >
        <SelectTrigger className="h-8 text-xs rounded-lg">
          <SelectValue placeholder="竣工年" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">竣工年</SelectItem>
          <SelectItem value="2025">2025以降</SelectItem>
          <SelectItem value="2026">2026以降</SelectItem>
          <SelectItem value="2027">2027以降</SelectItem>
          <SelectItem value="2028">2028以降</SelectItem>
        </SelectContent>
      </Select>
    </div>
  </>
}

