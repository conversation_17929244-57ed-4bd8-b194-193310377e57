"use clinet"

import LeafletMapSimple from "@/components/LeafletMapSimple";
import dayjs from "dayjs";

export default function LeftletMap({ mapType, projects, filterProjects }: { mapType: string, projects: any[], filterProjects: (project: any) => boolean }) {

  return <LeafletMapSimple
    height="100%"
    zoom={14}
    mapStyle={mapType}
    data={projects
      .filter((record: any) => record.longitude > 0 && record.latitude > 0).filter(filterProjects).map((record: any) => ({
        name: record.compositeTitle,
        latitude: record.latitude as number,
        longitude: record.longitude as number,
        id: record.id,
        coordinate: {
          lat: record.latitude as number,
          lng: record.longitude as number,
        },
        popUpRenderFunction: () => {
          return <div className="flex flex-col gap-1">
            <div className="text-base text-gray-800">
              {record.name}
            </div>
            <div className="text-xs text-gray-500 flex flex-col gap-1">
              <div>
                施工面積: {record.constructionArea}m2
              </div>
              <div>
                用途: {record.projectUsage}
              </div>
              <div>
                竣工予定日: {dayjs(record.projectEndDate).format("YYYY/MM/DD")}
              </div>
              {/* <strong>{record.price}万円</strong>
                  |
                  土地 {record.landSize}m2
                  |
                  建物 {record.buildingSize}m2  </div> */}
            </div>
          </div>
        }
      }))}
  />
} 