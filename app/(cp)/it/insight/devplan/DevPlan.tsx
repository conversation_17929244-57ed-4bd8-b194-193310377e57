"use client"

import { Separator } from "@/components/ui/separator"
import { useEffect, useMemo, useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { fuzzySearchProjectsAction } from "@/actions/proProject"
import { toast } from "@/hooks/use-toast"
import { Bold, ListIcon, Loader2, MapPin, Moon, Sun } from "lucide-react"
import { ProProject } from "@/lib/definitions/proProject"
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark"
import { useAuthStore } from "@/store/auth"
import MapFilter, { sortProjects } from "./MapFilter"
import { Toggle } from "@/components/ui/toggle"
import { usePathname, useSearchParams } from "next/navigation"
import { useRouter } from "next/navigation"
import { useUIStore } from "@/store/ui"
import BlogCardSmall from "@/app/blog/BlogCardSmall"
import LeftletMap from "./LeftletMap"
import LeftListKensetsu from "./LeftListKensetsu"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

export default function KensetsuPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { selectedProejctIdForKensetsu } = useUIStore();

  const [projects, setProjects] = useState<any>([])
  const [searchName, setSearchName] = useState(searchParams.get("search") || "")
  const [isLoading, setIsLoading] = useState(false)
  const [showList, setShowList] = useState(false)
  const [yearFilter, setYearFilter] = useState<string>("all")
  const [usageFilter, setUsageFilter] = useState<string>("all")
  const [areaFilter, setAreaFilter] = useState<string>("all")
  const [typeFilter, setTypeFilter] = useState<string>("all")
  const { currentUser } = useAuthStore();
  const [mapType, setMapType] = useState<'light' | 'dark' | 'streets'>('light');

  const filterProjects = (project: ProProject) => {
    if (yearFilter !== "all" && project.projectEndDateYear < Number(yearFilter)) {
      return false
    }

    if (usageFilter !== "all" && project.projectUsage && !project.projectUsage.includes(usageFilter.toLowerCase())) {
      return false
    }

    if (areaFilter !== "all" && project.constructionArea && project.constructionArea < Number(areaFilter)) {
      return false
    }

    if (typeFilter !== "all" && project.type !== typeFilter) {
      return false
    }

    return true
  }

  const searchProjects = async () => {
    setIsLoading(true)

    try {
      const result = await fuzzySearchProjectsAction({
        name: searchName,
        limit: 50
      })

      if (result.success && Array.isArray(result.data)) {
        setProjects(sortProjects(typeFilter, result.data as any[]))

        // toast({
        //   title: "検索成功",
        //   description: `${result.data.length}件のプロジェクトが見つかりました`,
        // })
      } else {
        toast({
          title: "検索失敗",
          description: result.message,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('🔥 Error searching projects:', error)
      toast({
        title: "検索エラー",
        description: "プロジェクトの検索中にエラーが発生しました",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    // one time display only
    if (searchParams.get("search")) {
      setSearchName(searchParams.get("search") || "")
      searchProjects()
    }
  }, [])

  const getProjects = async () => {
    searchProjects()
    setShowList(true)

    const searchParams = new URLSearchParams(window.location.search);
    searchParams.set('search', searchName);
    router.push(`/it/insight?${searchParams.toString()}`, { scroll: false })

    sendLark({
      message: `[Kensetsu 検索]: ${currentUser?.name || currentUser?.email} が ${searchName} を検索しました`,
      url: LARK_URLS.USER_ACTIVITY_CHANNEL,
    })
  }

  return (
    <div className="grid grid-cols-1 w-full h-full overflow-y-auto">
      {showList && <LeftListKensetsu projects={projects} filterProjects={filterProjects} selectedProejctIdForKensetsu={selectedProejctIdForKensetsu} />}

      <div className={`col-span-1 bg-neutral-100 min-h-[300px] relative`}>
        <div className="flex flex-col absolute top-0 right-1 z-10 justify-end items-center flex-wrap">
          <div className="flex flex-row pt-2 gap-2 text-xs text-gray-400 w-full pb-2 justify-end flex-wrap items-center">
            <div className=" w-[320px] text-right">
              <Input
                className="bg-white w-full text-black"
                placeholder={`プロジェクト名、場所名、住所で検索...`}
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    getProjects();
                  }
                }}
              />
            </div>

            <Button variant={showList ? "default" : "outline"} size="sm" onClick={() => setShowList(!showList)} className="text-xs rounded-lg">
              <ListIcon className="w-4 h-4" />
            </Button>

            <Toggle aria-label="Toggle italic" onClick={() => setMapType(mapType === "light" ? "dark" : mapType === "dark" ? "streets" : "light")} className="border border-neutral-200 text-neutral-500 bg-white text-xs rounded-lg" size="sm">
              {mapType === "light" ? <Sun className="h-4 w-4" /> : mapType === "dark" ? <Moon className="h-4 w-4" /> : <MapPin className="h-4 w-4" />}
            </Toggle>
          </div>

          <div className="flex flex-row gap-2  z-10 justify-end items-center flex-wrap flex-1 w-full text-right">
            <MapFilter
              typeFilter={typeFilter}
              setTypeFilter={setTypeFilter}
              areaFilter={areaFilter}
              setAreaFilter={setAreaFilter}
              usageFilter={usageFilter}
              setUsageFilter={setUsageFilter}
              yearFilter={yearFilter}
              setYearFilter={setYearFilter}
            />
          </div>

        </div>

        <LeftletMap mapType={mapType} projects={projects} filterProjects={filterProjects} />

        <div className="text-xs text-gray-400 absolute bottom-0 left-0 w-full p-2 flex flex-col gap-2">
          <BlogCardSmall
            postId="ad193ad2-9d6c-4098-91d4-749135617e0e"
            currentLocale="ja"
          />

          <div>
            更新日: 2025/05/17
          </div>
        </div>
      </div>
    </div >
  )
}