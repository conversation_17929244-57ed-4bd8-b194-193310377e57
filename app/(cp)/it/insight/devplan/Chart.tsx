"use client";

import {
  <PERSON><PERSON>hart,
  Bar,
  CartesianGrid,
  Tooltip,
  XAxis,
  YAxis,
  ResponsiveContainer,
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// 生成一组可复用的颜色（可扩展）
const palette = [
  "#4AC1FF", "#FF6B6B", "#4CAF50", "#FFC107", "#9C27B0", "#FF9800", "#795548", "#607D8B",
  "#00B8A9", "#F6416C", "#43A047", "#F9A602", "#6A1B9A", "#F57C00", "#8D6E63", "#90A4AE",
  "#0081CF", "#F36265", "#388E3C", "#FFD600", "#7C4DFF", "#FF7043", "#A1887F", "#789262"
];

const rankLabels = ["1位", "2位", "3位", "4位", "5位"];

const getColorMap = (processed: any[]) => {
  // 收集所有出现过的 compositeName
  const names = new Set<string>();
  processed.forEach(row => {
    for (let i = 1; i <= 5; i++) {
      if (row[`top${i}Name`] && row[`top${i}Name`] !== "-") {
        names.add(row[`top${i}Name`]);
      }
    }
  });
  // 分配颜色
  const arr = Array.from(names);
  const colorMap: Record<string, string> = {};
  arr.forEach((name, idx) => {
    colorMap[name] = palette[idx % palette.length];
  });
  return colorMap;
};

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const maxBar = payload.reduce((max: any, cur: any) => (cur.value > max.value ? cur : max), payload[0]);
    return (
      <div className="rounded-lg border bg-background p-2 shadow-xs">
        <div className="flex flex-col gap-1">
          <span className="text-[0.70rem] uppercase text-muted-foreground">年</span>
          <span className="font-bold text-muted-foreground">{label}年</span>
          {payload.map((bar: any, idx: number) => (
            <div key={bar.dataKey} className="flex flex-row items-center gap-2">
              <span style={{ color: bar.fill, fontWeight: bar.value === maxBar.value ? 'bold' : 'normal' }}>{rankLabels[idx]}</span>
              <span className="font-bold">{bar.payload[`top${idx + 1}Name`]}</span>
              <span>{bar.value?.toLocaleString?.()} ㎡</span>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

export default function GroupedBarChart({ data }: { data: any[] }) {
  // 转换数据结构：每年一组，top1~top5
  const groupedByYear: Record<string, any[]> = {};

  data.forEach((item) => {
    if (!groupedByYear[item.year]) groupedByYear[item.year] = [];
    groupedByYear[item.year].push(item);
  });
  // 先生成所有compositeName的颜色映射
  const allNames = new Set<string>();
  Object.values(groupedByYear).forEach(items => {
    items.forEach(item => allNames.add(item.compositeName));
  });
  const arr = Array.from(allNames);
  const colorMap: Record<string, string> = {};
  arr.forEach((name, idx) => {
    colorMap[name] = palette[idx % palette.length];
  });

  // 处理每年top5，并把颜色写入数据
  const processed = Object.entries(groupedByYear).map(([year, items]) => {
    const sorted = items.sort((a, b) => b.totalArea - a.totalArea).slice(0, 5);
    const obj: any = { year };
    sorted.forEach((item, idx) => {
      obj[`top${idx + 1}Name`] = item.compositeName;
      obj[`top${idx + 1}Area`] = item.totalArea;
      obj[`top${idx + 1}Color`] = colorMap[item.compositeName];
    });
    for (let i = sorted.length; i < 5; i++) {
      obj[`top${i + 1}Name`] = "-";
      obj[`top${i + 1}Area`] = null;
      obj[`top${i + 1}Color`] = "#ccc";
    }
    return obj;
  });

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>年別 竣工建築面積 トップ5</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={processed}
              margin={{ top: 10, right: 30, left: 60, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="year"
                label={{ value: "年", position: "insideBottomRight", offset: 0 }}
              />
              <YAxis
                label={{ value: "面積 (㎡)", angle: -90, position: "insideLeft" }}
                tickFormatter={(value) => value?.toLocaleString?.()}
              />
              <Tooltip content={<CustomTooltip />} />
              {Array.from({ length: 5 }).map((_, idx) => (
                <Bar
                  key={`top${idx + 1}Area`}
                  dataKey={`top${idx + 1}Area`}
                  name={rankLabels[idx]}
                  barSize={20}
                  isAnimationActive={false}
                  fill={processed[0] ? processed[0][`top${idx + 1}Color`] : "#ccc"}
                // 颜色会在下面通过每个数据点的 color 字段覆盖
                // 但recharts只支持Bar整体的fill，所以每个Bar只能有一个颜色
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}