"use client"

import { ProProject } from "@/lib/definitions/proProject";
import { DataTable } from "@/components/ui/data-table";
import { columns } from "./ProjectTableColumns";
import KensetsuChart from "./KensetsuChart";

export default function LeftListKensetsu({ projects, filterProjects, selectedProejctIdForKensetsu }: { projects: ProProject[], filterProjects: (project: ProProject) => boolean, selectedProejctIdForKensetsu: string }) {
  return <div className="absolute top-10 left-10 h-1/2 w-1/2 border-2 m-2 rounded-lg z-10 bg-white border-neutral-200 p-2 overflow-y-auto max-h-[calc(100vh-100px)]">
    <div className="flex-1 flex flex-row gap-2 justify-start items-end mb-2">
      <div className="flex flex-row gap-1 justify-start items-end">
        <div className="text-2xl">
          {projects.filter(filterProjects).length.toLocaleString()}
        </div>

        <div className="text-sm text-gray-600">
          件該当プロジェクト
        </div>
      </div>
    </div>

    <DataTable
      columns={columns}
      data={projects.filter(filterProjects)}
      searchColumn="name"
      defaultPageSize={10}
      highlightedId={selectedProejctIdForKensetsu}
    />

    <KensetsuChart />
  </div>;
} 