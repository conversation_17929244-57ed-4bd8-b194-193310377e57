import { DataTable } from "@/components/ui/data-table";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { differenceInMonths, format } from "date-fns";
import { ja } from "date-fns/locale";
import Link from "next/link";
import { ProProject } from "@/lib/definitions/proProject";

export const columns: ColumnDef<ProProject>[] = [
  {
    accessorKey: "projectEndDate",
    header: "竣工予定日",
    cell: ({ row }) => {
      const date = row.original.projectEndDate;
      if (!date) return "-";

      const endDate = new Date(date);
      const today = new Date();
      const monthsToGo = differenceInMonths(endDate, today);

      return (
        <div className="flex flex-col items-center" >
          <span className={monthsToGo > 0 ? "text-green-600" : ""}>
            {format(endDate, "yyyy/MM/dd", { locale: ja })
            }
          </span>
          {monthsToGo > 0 && (
            <span className="text-xs text-green-600" >
              (+{monthsToGo}ヶ月)
            </span>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "name",
    header: "プロジェクト名",
    cell: ({ row }) => {
      const name = row.original.name;
      return <div className="flex flex-col" >
        <div className="" > {name.slice(0, 20)} </div>
        < div className="text-sm text-gray-500" >
          〒{row.original.postalCode} | {row.original.address} </div>
      </div>;
    },
  },
  {
    accessorKey: "type",
    header: "タイプ",
    cell: ({ row }) => {
      const type = row.original.type;
      const typeMap = {
        CONSTRUCTION: "新築",
        ADDITION: "増築",
        DEMOLITION: "解体",
      };
      return (
        <Badge variant={typeMap[type as keyof typeof typeMap] === "新築" ? "default" : "outline"} className="capitalize" >
          {typeMap[type as keyof typeof typeMap]}
        </Badge>
      );
    },
  },
  {
    accessorKey: "projectUsage",
    header: "用途",
    cell: ({ row }) => {
      const usage = row.original.projectUsage;
      return <div className="flex flex-col gap-1 text-xs" >
        {usage?.split("、").map((u, index) => <span key={index} className="" > {u} </span>)}
      </div>
    },
  },
  {
    accessorKey: "constructionArea",
    header: "施工面積(㎡)",
    cell: ({ row }) => {
      const area = row.original.constructionArea;
      return area?.toLocaleString();
    },
  },
  {
    accessorKey: "levelAboveGround",
    header: "階数",
    cell: ({ row }) => {
      const level = row.original.levelAboveGround;
      const levelUg = row.original.levelBelowGround;
      return <>
        {level ? level.toString() : ""}
        {levelUg ? "+" + levelUg.toString() + "(地下)" : ""}
      </>
    },
  },

  {
    accessorKey: "projectStartDate",
    header: "着工日",
    cell: ({ row }) => {
      const date = row.original.projectStartDate;
      return date ? format(new Date(date), "yyyy/MM/dd", { locale: ja }) : "-";
    },
  }
];
