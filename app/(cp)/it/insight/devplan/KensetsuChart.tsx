"use client"

import { groupPostalCodeByYearAction } from "@/actions/proProject"

import { useEffect, useState } from "react"
import { DataTable } from "@/components/ui/data-table"
import { Select, SelectItem, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

import Chart from "./Chart"


export default function KensetsuChart() {
  const [data, setData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [view, setView] = useState("year")
  const [unit, setUnit] = useState("postalCode")

  useEffect(() => {
    setIsLoading(true)

    groupPostalCodeByYearAction({ unit: unit as "postalCode" | "areaCode" | "prefectureCode" }).then((res) => {
      setData(res.data)
      setIsLoading(false)
    })
  }, [unit])

  return <div className="flex flex-col gap-2 bg-neutral-50 p-2">
    <div className="text-2xl">

      <div className="flex items-center gap-4 mb-4">
        <div className="flex items-center gap-2">
          <Tabs value={view} onValueChange={(value) => {
            setView(value as "year" | "month");
          }}>
            <TabsList>
              <TabsTrigger value="year">年</TabsTrigger>
              <TabsTrigger value="month" disabled={true}>月</TabsTrigger>
            </TabsList>
          </Tabs>

          <Tabs value={unit} onValueChange={(value) => {
            setUnit(value as "postalCode" | "areaCode" | "prefectureCode");
          }}>
            <TabsList>
              <TabsTrigger value="postalCode">郵便番号</TabsTrigger>
              <TabsTrigger value="areaCode">区市町村</TabsTrigger>
              {/* <TabsTrigger value="prefectureCode">都道府県</TabsTrigger> */}
            </TabsList>
          </Tabs>
        </div>
      </div>
    </div>

    <Chart data={data} />

    <DataTable
      columns={[{
        header: "住所",
        cell: ({ row }) => {
          return <div className="flex flex-col gap-1">
            <div>
              {row.original.compositeName}
            </div>
            {row.original.info.postalCode && <div className="text-sm text-muted-foreground">
              {row.original.info.postalCode}
            </div>}
          </div>
        }
      }, {
        header: "建設面積(㎡)",
        accessorKey: "totalArea",
        cell: ({ row }) => {
          return <div>
            {Number(row.original.totalArea?.toFixed(0)).toLocaleString()}
          </div>
        }
      }, {
        header: "年数",
        accessorKey: "year",
      }, {
        header: "件数",
        accessorKey: "count",
      }]}
      data={data}
      isLoading={isLoading}
      defaultPageSize={10}
    />
  </div>
}