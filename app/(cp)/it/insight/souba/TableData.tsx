"use client"

import { Loader2 } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";


export default function TableData({
  postalCodes,
  isLoading,
  selectedRecordType,
  tableColumn,
  selectedPostalCode,
  onPostalCodeSelect
}: {
  postalCodes: any[],
  isLoading: boolean,
  selectedRecordType: string,
  tableColumn: any[],
  selectedPostalCode?: any,
  onPostalCodeSelect?: (postalCode: any) => void
}) {

  console.log("🔥 postalCodes", postalCodes)

  // Enhanced table columns with row click handler
  const enhancedTableColumn = tableColumn.map((column, index) => {
    if (index === 0) { // First column (index)
      return {
        ...column,
        cell: ({ row }: { row: any }) => {
          return (
            <div
              className="cursor-pointer hover:bg-blue-50 p-2 rounded"
              onClick={() => {
                console.log("🔥 Row clicked:", row.original);
                onPostalCodeSelect?.(row.original);
              }}
            >
              {row.index + 1}
            </div>
          );
        }
      };
    }
    return column;
  });

  return <div className="h-full flex flex-col">
    <div className="flex flex-col sm:flex-row gap-2 items-left sm:items-center justify-left sm:justify-center w-full flex-wrap mb-4">
      <div className="text-sm text-gray-800 flex-1 flex-col gap-2">
        <div className="flex flex-row gap-2 items-center justify-start">
          合計:
          {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : postalCodes.length}
          件
        </div>
        <div className="text-xs text-gray-500">
          ＊以下のデータはすべて平均データです
        </div>
      </div>

      <div className="text-xs text-gray-800 flex flex-row gap-2 whitespace-nowrap">
        <div className="flex flex-row">
          <div className="text-xs text-green-500">新規</div>: 3日内新規物件
        </div>
        <div className="flex flex-row">
          <div className="text-xs text-blue-500">価変</div>: 3日内価格変更あり物件
        </div>
      </div>
    </div>

    <div className="text-sm text-gray-800 flex-1 overflow-hidden">
      <DataTable
        columns={enhancedTableColumn}
        data={postalCodes.sort((a, b) => selectedRecordType === "BUILDING" ? b.buildingAverageRoi > a.buildingAverageRoi ? 1 : -1 : selectedRecordType === "HOUSE" ? b.houseAveragePrice > a.houseAveragePrice ? 1 : -1 : selectedRecordType === "LAND" ? b.landAveragePrice > a.landAveragePrice ? 1 : -1 : selectedRecordType === "MANSION" ? b.mansionAveragePrice > a.mansionAveragePrice ? 1 : -1 : 0)}
        defaultPageSize={20}
        highlightedId={selectedPostalCode?.postalCode}
        // meta={{
        //   getRowId: (row: any) => row.postalCode
        // }}
      />
    </div>
  </div>;
}