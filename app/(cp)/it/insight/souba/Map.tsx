import React, { useEffect, useState } from 'react';
import * as echarts from 'echarts/core';
import { MapChart } from 'echarts/charts';
import { TooltipComponent, VisualMapComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import ReactECharts from 'echarts-for-react';

import tokyoGeoJson from './tokyo_5perc.json' assert { type: 'json' };

echarts.use([MapChart, TooltipComponent, VisualMapComponent, CanvasRenderer]);

export default function TokyoMapChart() {
  const [option, setOption] = useState<any>(null);

  useEffect(() => {
    // 注册地图
    echarts.registerMap('tokyo', tokyoGeoJson as any);

    setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}',
      },
      visualMap: {
        min: 0,
        max: 100,
        left: 'left',
        top: 'bottom',
        text: ['多', '少'],
        calculable: true,
      },
      series: [
        {
          name: 'Tokyo Area',
          type: 'map',
          map: 'tokyo',
          roam: true,
          layoutCenter: ['50%', '50%'], // 居中
          layoutSize: '100%', // 适配容器
          selectedMode: 'single', // ✅ 只允许点击一个
          label: {
            show: true,
            fontSize: 10,
          },
          emphasis: {
            label: {
              show: true,
              color: '#000',
            },
            itemStyle: {
              areaColor: '#ffd54f',
              borderColor: '#000',
              borderWidth: 1,
            },
          },
          data: [
            { name: '新宿区', value: 45 },
            { name: '渋谷区', value: 72 },
          ],
        },
      ],
    });
  }, []);

  if (!option) return <div>読み込み中...</div>;

  return <ReactECharts option={option} style={{ height: '80vh', width: '100%' }} />;
}