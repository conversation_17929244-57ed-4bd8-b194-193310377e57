import { Button } from "@/components/ui/button";
import { get } from "http";
import { ExternalLink } from "lucide-react";
import Link from "next/link";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";

export const getCountByType = (changes: any[], type: string) => {
  return changes?.filter((change: any) => change.recordType === type).length;
}

export const getCompositeAddress = (data: any) => {
  return `${data.cityName ? `${data.cityName}` : ""} ${data.areaName ? `${data.areaName}` : ""}${data.choumeName ? `${data.choumeName}` : ""}`
}

const areaTableColumn1 = [
  {
    header: "#",
    cell: ({ row }: { row: any }) => {
      return row.index + 1
    }
  },
  {
    header: "住所詳細",
    cell: ({ row }: { row: any }) => {

      return <div className="text-center flex flex-col items-center justify-center">
        <div>〒{row.original.postalCode}</div>
        <div className="text-xs text-gray-500 word-break-all text-wrap" >{getCompositeAddress(row.original)}</div>
      </div>;
    }
  },
];

const areaTableColumn2 = [
  {
    header: "操作",
    cell: ({ row }: { row: any }) => {
      return <div className="text-center flex flex-col items-center justify-center">
        <div className="flex flex-row gap-2 items-center justify-center">
          <Button variant="outline" size="sm">
            <Link href={`/an/area/${row.original.postalCode}`} target="_blank" onClick={() => {
              createNewSystemUserActivityAction({
                data: {
                  eventType: "BUTTON_CLICK",
                  route: "/an/area",
                  eventMetadata: {
                    buttonName: "goAnalysisAreaButton",
                    selectedArea: row.original.postalCode,
                  },
                },
              });

              sendLark({
                message: `[🏢][分析][Area]XXさんが${row.original.postalCode}を検索しました`,
                url: LARK_URLS.USER_ACTIVITY_CHANNEL,
              });
            }}>
              詳細分析
            </Link>
          </Button>
        </div>
      </div>;
    }
  },
]

export const getBuildingColumn = () => {
  return [
    ...areaTableColumn1,
    {
      header: "価格",
      cell: ({ row }: { row: any }) => {
        return <div className="text-center flex flex-col items-center justify-center">
          <div>{row.original.buildingAveragePrice ? row.original.buildingAveragePrice + "万円" : "-"}</div>
        </div >
      }
    },
    {
      header: "利回り(ROI)",
      cell: ({ row }: { row: any }) => {
        return <div>{row.original.buildingAverageRoi ? (row.original.buildingAverageRoi).toFixed(2) + "%" : "-"}</div>
      }
    },
    {
      header: "建築面積",
      cell: ({ row }: { row: any }) => {
        return <>
          <div>{row.original.buildingAverageArea ? row.original.buildingAverageArea + "㎡" : "-"}</div>
        </>
      }
    },
    {
      header: "物件数",
      cell: ({ row }: { row: any }) => {
        return <div className="text-center flex flex-col items-center justify-center">{row.original.buildingCount ? row.original.buildingCount : "-"}</div>
      }
    },
    {
      header: "新規物件",
      cell: ({ row }: { row: any }) => {
        return <Link href={`/ex/search?type=address&selectRecordType=BUILDING&searchValue=${getCompositeAddress(row.original)}&updatedTag=0.1`} target="_blank" className="flex flex-row gap-2 items-center justify-center underline underline-offset-2 decoration-gray-400">
          {row.original.recentlyCreatedChanges.length && getCountByType(row.original.recentlyCreatedChanges, "BUILDING") > 0 ? <div className="text-center flex flex-col items-center justify-center text-xs text-green-500">
            <div>新規+{getCountByType(row.original.recentlyCreatedChanges, "BUILDING")}</div>
          </div> : ""}

          {row.original.recentlyPriceChanged.length && getCountByType(row.original.recentlyPriceChanged, "BUILDING") > 0 ? <div className="text-center flex flex-col items-center justify-center text-xs text-blue-500">
            <div>価変+{getCountByType(row.original.recentlyPriceChanged, "BUILDING")}</div>
          </div> : ""}
        </Link>
      }
    },
    // {
    //   header: "見張り",
    //   cell: ({ row }: { row: any }) => {
    //     return <div className="text-center flex flex-col items-center justify-center">
    //       <div className="flex flex-row gap-2 items-center justify-center">
    //         {getMihari(propertyWatchedCriteria, row.original, UserLambdaRecordType.BUILDING)}
    //       </div>
    //     </div>;
    //   }
    // },
    ...areaTableColumn2,
  ]
}

export const getHouseColumn = () => {
  return [
    ...areaTableColumn1,
    {
      header: "価格",
      cell: ({ row }: { row: any }) => {
        return <div>{row.original.houseAveragePrice ? row.original.houseAveragePrice + "万円" : "-"}</div>
      }
    },
    {
      header: "建築面積",
      cell: ({ row }: { row: any }) => {
        return <div>{row.original.houseAverageArea ? row.original.houseAverageArea + "㎡" : "-"}</div>
      }
    },
    {
      header: "物件数",
      cell: ({ row }: { row: any }) => {
        return <div className="text-center flex flex-col items-center justify-center">{row.original.houseCount ? row.original.houseCount + "件" : "-"}</div>
      }
    },
    {
      header: "新規物件",
      cell: ({ row }: { row: any }) => {
        return <Link href={`/ex/search?type=address&selectRecordType=HOUSE&searchValue=${getCompositeAddress(row.original)}&updatedTag=0.1`} target="_blank" className="flex flex-row gap-2 items-center justify-center underline underline-offset-2 decoration-gray-400">
          {row.original.recentlyCreatedChanges.length && getCountByType(row.original.recentlyCreatedChanges, "HOUSE") > 0 ? <div className="text-center flex flex-col items-center justify-center text-xs text-green-500">
            <div>新規{getCountByType(row.original.recentlyCreatedChanges, "HOUSE")}件</div>
          </div> : ""}

          {row.original.recentlyPriceChanged.length && getCountByType(row.original.recentlyPriceChanged, "HOUSE") > 0 ? <div className="text-center flex flex-col items-center justify-center text-xs text-blue-500">
            <div>価変{getCountByType(row.original.recentlyPriceChanged, "HOUSE")}件</div>
          </div> : ""}
        </Link>
      }
    },
    // {
    //   header: "見張り",
    //   cell: ({ row }: { row: any }) => {
    //     return <div className="text-center flex flex-col items-center justify-center">
    //       {getMihari(propertyWatchedCriteria, row.original, UserLambdaRecordType.HOUSE)}
    //     </div>;
    //   }
    // },
    ...areaTableColumn2,
  ]
}

export const getLandColumn = () => {
  return [
    ...areaTableColumn1,
    {
      header: "価格",
      cell: ({ row }: { row: any }) => {
        return <div>{row.original.landAveragePrice ? row.original.landAveragePrice + "万円" : "-"}</div>
      }
    },
    {
      header: "面積",
      cell: ({ row }: { row: any }) => {
        return <div>{row.original.landAverageArea ? row.original.landAverageArea + "㎡" : "-"}</div>
      }
    },
    {
      header: "物件数",
      cell: ({ row }: { row: any }) => {
        return <div className="text-center flex flex-col items-center justify-center">{row.original.landCount ? row.original.landCount + "件" : "-"}</div>
      }
    },
    {
      header: "新規物件",
      cell: ({ row }: { row: any }) => {
        return <Link href={`/ex/search?type=address&selectRecordType=LAND&searchValue=${getCompositeAddress(row.original)}&updatedTag=0.1`} target="_blank" className="flex flex-row gap-2 items-center justify-center underline underline-offset-2 decoration-gray-400">
          {row.original.recentlyCreatedChanges.length && getCountByType(row.original.recentlyCreatedChanges, "LAND") > 0 ? <div className="text-center flex flex-col items-center justify-center text-xs text-green-500">
            <div>新規{getCountByType(row.original.recentlyCreatedChanges, "LAND")}件</div>
          </div> : ""}

          {row.original.recentlyPriceChanged.length && getCountByType(row.original.recentlyPriceChanged, "LAND") > 0 ? <div className="text-center flex flex-col items-center justify-center text-xs text-blue-500">
            <div>価変{getCountByType(row.original.recentlyPriceChanged, "LAND")}件</div>
          </div> : ""}
        </Link>
      }
    },
    // {
    //   header: "見張り",
    //   cell: ({ row }: { row: any }) => {
    //     return <div className="text-center flex flex-col items-center justify-center">
    //       {getMihari(propertyWatchedCriteria, row.original, UserLambdaRecordType.LAND)}
    //     </div>;
    //   }
    // },
    ...areaTableColumn2,
  ]
}

export const getMansionColumn = () => {
  return [
    ...areaTableColumn1,
    {
      header: "価格",
      cell: ({ row }: { row: any }) => {
        return <div>{row.original.mansionAveragePrice ? row.original.mansionAveragePrice + "万円" : "-"}</div>
      }
    },
    {
      header: "建築面積",
      cell: ({ row }: { row: any }) => {
        return <div>{row.original.mansionAverageArea ? row.original.mansionAverageArea + "㎡" : "-"}</div>
      }
    },
    {
      header: "物件数",
      cell: ({ row }: { row: any }) => {
        return <div className="text-center flex flex-col items-center justify-center">{row.original.mansionCount ? row.original.mansionCount + "件" : "-"}</div>
      }
    },
    {
      header: "新規物件",
      cell: ({ row }: { row: any }) => {
        return <Link href={`/ex/search?type=address&selectRecordType=MANSION&searchValue=${getCompositeAddress(row.original)}&updatedTag=0.1`} target="_blank" className="flex flex-row gap-2 items-center justify-center underline underline-offset-2 decoration-gray-400">
          {row.original.recentlyCreatedChanges.length && getCountByType(row.original.recentlyCreatedChanges, "MANSION") > 0 ? <div className="text-center flex flex-col items-center justify-center text-xs text-green-500">
            <div>新規{getCountByType(row.original.recentlyCreatedChanges, "MANSION")}件</div>
          </div> : ""}

          {row.original.recentlyPriceChanged.length && getCountByType(row.original.recentlyPriceChanged, "MANSION") > 0 ? <div className="text-center flex flex-col items-center justify-center text-xs text-blue-500">
            <div>価変{getCountByType(row.original.recentlyPriceChanged, "MANSION")}件</div>
          </div> : ""}
        </Link>
      }
    },
    // {
    //   header: "見張り",
    //   cell: ({ row }: { row: any }) => {
    //     return <div className="text-center flex flex-col items-center justify-center">
    //       {getMihari(propertyWatchedCriteria, row.original, UserLambdaRecordType.MANSION)}
    //     </div>;
    //   }
    // },
    ...areaTableColumn2,
  ];
}