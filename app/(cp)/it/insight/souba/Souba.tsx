"use client";

import { useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";
import SearchBarPostalCodeUI from "@/components/ui/SearchBarPostalCodeUI";
import LeafletMap from "@/components/LeafletMap";
import { getRecordsWithInRange } from "@/actions/geoPostalCodes";
import { getBuildingColumn, getHouseColumn, getLandColumn, getMansionColumn } from "./areaTableColumn";
import { DataTable } from "@/components/ui/data-table";
import ReactDOMServer from "react-dom/server";
import Link from "next/link";
import { ExternalLink, Loader2 } from "lucide-react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getCompositeAddress, getCountByType } from "./areaTableColumn";
import TableData from "./TableData";

export default function Area() {
  const [selectedPostalCode, setSelectedPostalCode] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [postalCodes, setPostalCodes] = useState<any[]>([]);
  const [tableColumn, setTableColumn] = useState<any[]>([]);
  const [selectedRecordType, setSelectedRecordType] = useState<string>("BUILDING");

  const [bounds, setBounds] = useState<any>({
    topLeft: null,
    bottomRight: null,
  });

  useEffect(() => {
    if (bounds.topLeft && bounds.bottomRight) {
      setIsLoading(true);

      getRecordsWithInRange({ topLeftLat: bounds.topLeft.lat, topLeftLng: bounds.topLeft.lng, bottomRightLat: bounds.bottomRight.lat, bottomRightLng: bounds.bottomRight.lng, type: "postalCode" }).then((res) => {
        setPostalCodes(res.data);
        setIsLoading(false);
      });
    }
  }, [bounds]);

  useEffect(() => {
    if (selectedRecordType === "BUILDING") {
      setTableColumn(getBuildingColumn());
    } else if (selectedRecordType === "HOUSE") {
      setTableColumn(getHouseColumn());
    } else if (selectedRecordType === "LAND") {
      setTableColumn(getLandColumn());
    } else if (selectedRecordType === "MANSION") {
      setTableColumn(getMansionColumn());
    }
  }, [selectedRecordType]);

  // Handle table row selection - this will NOT center the map to avoid recentering issues
  const handleTableRowSelection = (postalCode: any) => {
    console.log("🔥 Table row selected:", postalCode);
    setSelectedPostalCode(postalCode);
  };

  // Handle map pin click - this will NOT center the map
  const handleMapPinClick = (data: any) => {
    console.log("🔥 Map pin clicked:", data);
    const clickedPostalCode = postalCodes.find(pc => pc.postalCode === data.name);
    if (clickedPostalCode) {
      setSelectedPostalCode(clickedPostalCode);
    }
  };

  const getTotalNewCountForType = (postalCodes: any, type: string) => {
    let res = postalCodes?.reduce((acc: number, curr: any) => acc + (curr?.recentlyCreatedChanges?.filter((change: any) => change.recordType === type).length || 0), 0);

    if (res > 0) {
      return <div className="text-xs text-green-500">
        +{res}
      </div>;
    } else {
      return "";
    }
  }

  const getTotalPriceChangedCountForType = (postalCodes: any, type: string) => {
    let res = postalCodes?.reduce((acc: number, curr: any) => acc + (curr?.recentlyPriceChanged?.filter((change: any) => change.recordType === type).length || 0), 0);

    if (res > 0) {
      return <div className="text-xs text-blue-500">
        +{res}
      </div>;
    } else {
      return "";
    }
  }
  return (
    <div className="h-full">
      <div className="px-2 py-2 grid grid-cols-1 lg:grid-cols-2 gap-4 w-full mt-12 h-[calc(100vh-120px)]">
        <div className="flex flex-col gap-2 overflow-hidden">
          <Tabs value={selectedRecordType} onValueChange={(value) => {
            setSelectedRecordType(value);
          }}>
            <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 gap-1 h-12">
              <TabsTrigger value="BUILDING" className="flex flex-row gap-1 items-center justify-center text-xs">
                <span className="hidden sm:inline">一棟収益物件</span>
                <span className="sm:hidden">一棟</span>
                <div className="flex flex-row gap-1">
                  {getTotalNewCountForType(postalCodes, "BUILDING")}
                  {getTotalPriceChangedCountForType(postalCodes, "BUILDING")}
                </div>
              </TabsTrigger>
              <TabsTrigger value="HOUSE" className="flex flex-row gap-1 items-center justify-center text-xs">
                <span className="hidden sm:inline">一戸建て</span>
                <span className="sm:hidden">戸建</span>
                <span className="flex flex-row gap-1">
                  {getTotalNewCountForType(postalCodes, "HOUSE")}
                  {getTotalPriceChangedCountForType(postalCodes, "HOUSE")}
                </span>
              </TabsTrigger>
              <TabsTrigger value="LAND" className="flex flex-row gap-1 items-center justify-center text-xs p-2">
                <span>土地</span>
                <span className="flex flex-row gap-1">
                  {getTotalNewCountForType(postalCodes, "LAND")}
                  {getTotalPriceChangedCountForType(postalCodes, "LAND")}
                </span>
              </TabsTrigger>
              <TabsTrigger value="MANSION" className="flex flex-row gap-1 items-center justify-center text-xs p-2">
                <span className="hidden sm:inline">区分マンション</span>
                <span className="sm:hidden">区分</span>
                <span className="flex flex-row gap-1">
                  {getTotalNewCountForType(postalCodes, "MANSION")}
                  {getTotalPriceChangedCountForType(postalCodes, "MANSION")}
                </span>
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="flex-1 overflow-hidden">
            <TableData
              postalCodes={postalCodes}
              isLoading={isLoading}
              selectedRecordType={selectedRecordType}
              tableColumn={tableColumn}
              selectedPostalCode={selectedPostalCode}
              onPostalCodeSelect={handleTableRowSelection}
            />
          </div>
        </div>

        <div className="flex-1 border border-gray-200 rounded-md overflow-hidden">
          <LeafletMap
            height={"100%"}
            center={[35.681236, 139.767125]}
            zoom={16}
            minZoom={14}
            useCustomIcon={true}
            isMapLocked={isLoading}
            data={postalCodes.map((postalCode) => ({
              name: postalCode.postalCode,
              id: postalCode.postalCode,
              coordinate: [postalCode.latitude, postalCode.longitude],
              textToShow: {
                title: `${postalCode.cityName ? `${postalCode.cityName}` : ""} ${postalCode.areaName ? `${postalCode.areaName}` : ""}${postalCode.choumeName ? `${postalCode.choumeName}` : ""}`,
                description: ReactDOMServer.renderToString(<div className="flex flex-row gap-1 items-center justify-center font-bold">
                  <div className="font-bold">
                    {selectedRecordType === "BUILDING" ? postalCode.buildingRecordRoi ? (postalCode.buildingRecordRoi * 100).toFixed(2) + "%" : "-" : ""}
                    {selectedRecordType === "HOUSE" ? postalCode.houseAveragePrice ? postalCode.houseAveragePrice + "万" : "-" : ""}
                    {selectedRecordType === "LAND" ? postalCode.landAveragePrice ? postalCode.landAveragePrice + "万" : "-" : ""}
                    {selectedRecordType === "MANSION" ? postalCode.mansionAveragePrice ? postalCode.mansionAveragePrice + "万" : "-" : ""}
                  </div>
                  {postalCode.recentlyCreatedChanges.filter((change: any) => change.recordType === selectedRecordType).length > 0 && <div className="text-xs text-green-500">
                    +{postalCode.recentlyCreatedChanges.filter((change: any) => change.recordType === selectedRecordType).length}
                  </div>}
                  {postalCode.recentlyPriceChanged.filter((change: any) => change.recordType === selectedRecordType).length > 0 && <div className="text-xs text-blue-500">
                    +{postalCode.recentlyPriceChanged.filter((change: any) => change.recordType === selectedRecordType).length}
                  </div>}
                </div>),
              },
              popUpRenderFunction: () => {
                return <div className="min-w-[280px] p-2">
                  <div className="text-sm font-bold text-gray-800 mb-2 border-b border-gray-200 pb-2">
                    {getCompositeAddress(postalCode)}
                  </div>

                  <div className="text-xs text-gray-500 mb-3">
                    〒{postalCode.postalCode}
                  </div>

                  <div className="space-y-3">
                    {/* Current selected type details */}
                    <div className="bg-blue-50 p-2 rounded-md">
                      <div className="font-semibold text-blue-800 mb-1 text-xs">
                        {selectedRecordType === "BUILDING" && "一棟収益物件"}
                        {selectedRecordType === "HOUSE" && "一戸建て"}
                        {selectedRecordType === "LAND" && "土地"}
                        {selectedRecordType === "MANSION" && "区分マンション"}
                      </div>
                      {selectedRecordType === "BUILDING" && postalCode.buildingAveragePrice && (
                        <div className="text-xs space-y-1">
                          <div>💰 平均価格: <span className="font-semibold">{postalCode.buildingAveragePrice}万円</span></div>
                          <div>📈 平均利回り: <span className="font-semibold text-green-600">{(postalCode.buildingAverageRoi).toFixed(2)}%</span></div>
                          <div>🏢 物件数: <span className="font-semibold">{postalCode.buildingCount}件</span></div>
                          {postalCode.buildingAverageArea && <div>📐 平均面積: <span className="font-semibold">{postalCode.buildingAverageArea}㎡</span></div>}
                        </div>
                      )}
                      {selectedRecordType === "HOUSE" && postalCode.houseAveragePrice && (
                        <div className="text-xs space-y-1">
                          <div>💰 平均価格: <span className="font-semibold">{postalCode.houseAveragePrice}万円</span></div>
                          <div>🏠 物件数: <span className="font-semibold">{postalCode.houseCount}件</span></div>
                          {postalCode.houseAverageArea && <div>📐 平均面積: <span className="font-semibold">{postalCode.houseAverageArea}㎡</span></div>}
                        </div>
                      )}
                      {selectedRecordType === "LAND" && postalCode.landAveragePrice && (
                        <div className="text-xs space-y-1">
                          <div>💰 平均価格: <span className="font-semibold">{postalCode.landAveragePrice}万円</span></div>
                          <div>🏞️ 物件数: <span className="font-semibold">{postalCode.landCount}件</span></div>
                          {postalCode.landAverageArea && <div>📐 平均面積: <span className="font-semibold">{postalCode.landAverageArea}㎡</span></div>}
                        </div>
                      )}
                      {selectedRecordType === "MANSION" && postalCode.mansionAveragePrice && (
                        <div className="text-xs space-y-1">
                          <div>💰 平均価格: <span className="font-semibold">{postalCode.mansionAveragePrice}万円</span></div>
                          <div>🏢 物件数: <span className="font-semibold">{postalCode.mansionCount}件</span></div>
                          {postalCode.mansionAverageArea && <div>📐 平均面積: <span className="font-semibold">{postalCode.mansionAverageArea}㎡</span></div>}
                        </div>
                      )}
                    </div>

                    {/* Recent changes */}
                    {(postalCode.recentlyCreatedChanges.length > 0 || postalCode.recentlyPriceChanged.length > 0) && (
                      <div className="bg-yellow-50 p-2 rounded-md">
                        <div className="font-semibold text-yellow-800 mb-1 text-xs">📊 最近の変更</div>
                        <div className="flex flex-row gap-2 text-xs">
                          {postalCode.recentlyCreatedChanges.length && getCountByType(postalCode.recentlyCreatedChanges, selectedRecordType) > 0 && (
                            <div className="bg-green-100 text-green-700 px-2 py-1 rounded">
                              ✨ 新規{getCountByType(postalCode.recentlyCreatedChanges, selectedRecordType)}件
                            </div>
                          )}
                          {postalCode.recentlyPriceChanged.length && getCountByType(postalCode.recentlyPriceChanged, selectedRecordType) > 0 && (
                            <div className="bg-blue-100 text-blue-700 px-2 py-1 rounded">
                              💹 価変{getCountByType(postalCode.recentlyPriceChanged, selectedRecordType)}件
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Action buttons */}
                    <div className="flex flex-col gap-2 text-white">
                      <Link
                        href={`/an/area/${postalCode.postalCode}#${selectedRecordType.toLocaleLowerCase()}`}
                        target="_blank"
                        className="flex flex-row gap-2 items-center justify-center border border-blue-600 hover:bg-blue-700 px-3 py-2 rounded-md transition-colors duration-200 text-xs"
                      >
                        <ExternalLink className="w-3 h-3" />
                        詳細分析を見る
                      </Link>

                      <Link
                        href={`/ex/search?type=address&selectRecordType=${selectedRecordType}&searchValue=${getCompositeAddress(postalCode)}&updatedTag=0.1`}
                        target="_blank"
                        className="flex flex-row gap-2 items-center justify-center border border-green-600 hover:bg-green-700 px-3 py-2 rounded-md transition-colors duration-200 text-xs"
                      >
                        <ExternalLink className="w-3 h-3" />
                        物件検索
                      </Link>
                    </div>
                  </div>
                </div>
              }
            }))}
            legend={<div>
              <div className="flex flex-row gap-2 bottom-0 absolute z-999 text-center justify-center bg-neutral-50 p-1">
                <div className="w-4 h-4 bg-blue-500"></div>
                <div>中心地</div>
              </div>
            </div>}
            onBoundsChange={(data) => {
              setBounds(data);
            }}
            onMarkerClick={handleMapPinClick}
            selectedMarkerId={selectedPostalCode?.postalCode}
          />

          {/* <div className="px-4 py-2 flex flex-row gap-4 w-full mt-2">
            <div className="text-xs text-gray-800 flex flex-row gap-2 justify-center items-center w-full text-gray-500">
              <div>Top Left: {bounds?.topLeft?.lat}, {bounds?.topLeft?.lng}</div>
              |
              <div>Bottom Right: {bounds?.bottomRight?.lat}, {bounds?.bottomRight?.lng}</div>
            </div>
          </div> */}
        </div>
      </div>
    </div >
  );
}