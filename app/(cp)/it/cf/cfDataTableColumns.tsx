import { ColumnDef } from "@tanstack/react-table"
import { CashFlowResult } from "@/actions/propertySimulationResult"
import { formatCurrency } from "@/lib/helper/format";

export const cfDataTableColumns: ColumnDef<CashFlowResult>[] = [
  {
    accessorKey: "year",
    header: "年数",
    cell: ({ row }) => {
      return <div className="text-center">{row.original.year}</div>
    },
  },
  {
    accessorKey: "income",
    header: "賃料収入",
    cell: ({ row }) => {
      return <div className="text-right">{formatCurrency(row.original.income)}</div>
    },
  },
  {
    accessorKey: "expense",
    header: "支出",
    cell: ({ row }) => {
      return <div className={`text-right ${row.original.expense > 0 ? 'text-red-600' : ""}`}>{formatCurrency(row.original.expense)}</div>
    },
  },
  {
    accessorKey: "tax",
    header: "税金",
    cell: ({ row }) => {
      return <div className={`text-right ${row.original.tax > 0 ? 'text-red-600' : ""}`}>{formatCurrency(row.original.tax)}</div>
    },
  },
  // {
  //   accessorKey: "amortization",
  //   header: "償却",
  //   cell: ({ row }) => {
  //     return <div className={`text-right ${row.original.amortization > 0 ? 'text-red-600' : ""}`}>{formatCurrency(row.original.amortization)}</div>
  //   },
  // },
  {
    accessorKey: "majorRepair",
    header: "修繕",
    cell: ({ row }) => {
      return <div className={`text-right ${row.original.majorRepair > 0 ? 'text-red-600' : ""}`}>{row.original.majorRepair > 0 ? formatCurrency(row.original.majorRepair) : "-"}</div>
    },
  },
  {
    accessorKey: "loanRepayment",
    header: "ローン返済 / 残高",
    cell: ({ row }) => {
      return <div className="text-right">{formatCurrency(row.original.loanRepayment)} / {formatCurrency(row.original.remainLoan)}</div>
    },
  },
  {
    accessorKey: "netCashFlow",
    header: "年間CF",
    cell: ({ row }) => {
      const value = row.original.netCashFlow
      return (
        <div className={`text-right ${value >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {formatCurrency(value)}
        </div>
      )
    },
  },
  {
    accessorKey: "accumulativeCashFlow",
    header: "累計CF",
    cell: ({ row }) => {
      const value = row.original.accumulativeCashFlow
      return (
        <div className={`text-right ${value >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {formatCurrency(value)}
        </div>
      )
    },
  },
  {
    accessorKey: "sellingPrice",
    header: "売却価格",
    cell: ({ row }) => {
      return <div className="text-right">{formatCurrency(row.original.sellingPrice)}</div>
    },
  },
  {
    accessorKey: "sellingNetProfit",
    header: "売却利益",
    cell: ({ row }) => {
      const value = row.original.sellingNetProfit
      return (
        <div className={`text-right ${value >= 0 ? 'text-green-600' : 'text-red-600'}`}>
          {formatCurrency(value)}
        </div>
      )
    },
  },
  {
    accessorKey: "accumulativeNetProfit",
    header: "売却後累計CF",
    cell: ({ row }) => {
      const value = row.original.accumulativeNetProfit
      return (
        <div className={`text - right ${value >= 0 ? 'text-green-600' : 'text-red-600'} `}>
          {formatCurrency(value)}
        </div>
      )
    },
  },
  {
    accessorKey: "irr",
    header: "IRR(内部収益率)",
    cell: ({ row }) => {
      const cashFlows = row.original.cashFlows;
      const irr = row.original.irr;
      return (
        <div className="">
          {irr.toFixed(2)}%
        </div>
      )
    },
  },
] 