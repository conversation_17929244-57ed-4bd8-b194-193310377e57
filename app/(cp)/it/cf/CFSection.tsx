"use client";

import { InvestmentParams } from "@/actions/propertySimulationResult";
import { CashFlowResult } from "@/actions/propertySimulationResult";
import { useEffect, useRef, useState } from "react";
import { cashFlowSimulation } from "@/actions/propertySimulationResult";
import InvestmentForm from "./InvestmentForm";
import SimulationChart from "./SimulationChart";
import { FileDownIcon, Loader2, Printer } from "lucide-react";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import dayjs from "dayjs";
import { getBuildingMaterial } from "@/lib/helper/sekisan";
import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/ui/data-table";
import { cfDataTableColumns } from "./cfDataTableColumns";
import { Tabs, TabsList } from "@/components/ui/tabs";
import { TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";

export default function CFSection({
  currentUserLambdaRecord,
  showTitle = true,
}: {
  currentUserLambdaRecord?: UserLambdaRecordProps | null | undefined;
  showTitle?: boolean;
}) {
  const sectionRef = useRef<HTMLDivElement>(null);

  let initParams = () => {
    return {
      propertyPrice: currentUserLambdaRecord?.price || 10000,
      structure: getBuildingMaterial(
        currentUserLambdaRecord?.buildingMaterial || "木造",
      ),
      buildingAge: currentUserLambdaRecord?.buildingBuiltYear
        ? dayjs().year() - currentUserLambdaRecord?.buildingBuiltYear
        : 15,
      buildingArea:
        currentUserLambdaRecord?.buildingSize ||
        currentUserLambdaRecord?.recordValues?.unitArea ||
        400,

      capital: currentUserLambdaRecord?.price
        ? parseInt((currentUserLambdaRecord?.price * 0.3).toFixed(0))
        : parseInt((2000).toFixed(0)),
      interestRate: 2.5,
      loanPeriod: 30,

      occupancyRate: 95,
      buildingPrice: 4000,
      isCorporate: false,
      expectedAnnualIncome: currentUserLambdaRecord?.yearlyIncome
        ? currentUserLambdaRecord?.yearlyIncome
        : 500,
      rentGrowthRate: -1,

      propertyTax: currentUserLambdaRecord?.yearlyIncome
        ? parseInt((currentUserLambdaRecord?.yearlyIncome / 12).toFixed(0))
        : parseInt((500 / 12).toFixed(0)),
      managementFeeRate: 5,
      repairFeeRate: 5,
      otherFee: 0,

      taxRate: 30,
      majorRepairYear: 10,
      majorRepairCost: currentUserLambdaRecord?.buildingSize
        ? parseInt((currentUserLambdaRecord?.buildingSize * 1).toFixed(0))
        : parseInt((400).toFixed(0)),
      sellSellingCapRatePerc: 5,
    };
  };

  const [cfData, setCfData] = useState<CashFlowResult[]>([]);
  const [params, setParams] = useState<InvestmentParams>();

  const [loading, setLoading] = useState(false);
  const [loadingPdf, setLoadingPdf] = useState(false);
  const [tab, setTab] = useState<"graph" | "table">("graph");

  const [hasInitialized, setHasInitialized] = useState(false); // ✅ 新增 Flag

  const updateParams = (params: InvestmentParams) => {
    setLoading(true);
    cashFlowSimulation({ params }).then((res) => {
      setCfData(res);
      setLoading(false);
    });
  };

  useEffect(() => {
    const initial = initParams();
    setParams(initial);
    updateParams(initial);
    setHasInitialized(true);
  }, [currentUserLambdaRecord]);

  useEffect(() => {
    if (!params || !hasInitialized) return;
    updateParams(params);
  }, [params]);

  // 事件点（大規模修繕、ローン完済）
  const majorRepairYear = params?.majorRepairYear;
  const loanPeriod = params?.loanPeriod;

  const handleExportPdf = async () => {
    setLoadingPdf(true);

    if (!sectionRef.current) {
      setLoadingPdf(false);
      return;
    }

    const html2pdf = (await import("html2pdf.js")).default;

    // 创建一个容器来存放所有要导出的内容
    const exportContainer = document.createElement("div");
    exportContainer.style.padding = "20px";
    exportContainer.style.backgroundColor = "white";

    // 克隆整个CF部分
    const cfSectionClone = document
      .querySelector(".cf-section")
      ?.cloneNode(true) as HTMLElement;
    if (cfSectionClone) {
      // 移除不需要的元素
      const buttonsToRemove =
        cfSectionClone.querySelectorAll(".export-buttons");
      buttonsToRemove.forEach((button) => button.remove());

      // 确保图表可见
      const chartElement = cfSectionClone.querySelector(".chart-container");
      if (chartElement) {
        (chartElement as HTMLElement).style.display = "block";
        (chartElement as HTMLElement).style.visibility = "visible";
        (chartElement as HTMLElement).style.height = "400px";
      }

      // 隐藏第一个部分的数据表格
      const tableElement = cfSectionClone.querySelector("[data-section-ref]");
      if (tableElement) {
        (tableElement as HTMLElement).style.display = "none";
      }

      exportContainer.appendChild(cfSectionClone);
    }

    // 添加新页面分隔符
    const pageBreak = document.createElement("div");
    pageBreak.style.pageBreakBefore = "always";
    exportContainer.appendChild(pageBreak);

    // 添加标题
    const title = document.createElement("h2");
    title.textContent = "詳細データ";
    title.style.fontSize = "16px";
    title.style.fontWeight = "bold";
    title.style.marginBottom = "10px";
    exportContainer.appendChild(title);

    // 克隆数据表格部分
    const tableClone = sectionRef.current.cloneNode(true) as HTMLElement;
    if (tableClone) {
      tableClone.style.visibility = "visible";
      tableClone.style.position = "relative";
      tableClone.style.height = "auto";
      tableClone.style.overflow = "visible";
      exportContainer.appendChild(tableClone);
    }

    try {
      await html2pdf()
        .from(exportContainer)
        .set({
          margin: 2,
          filename: `${currentUserLambdaRecord?.address}_CFシミュレーション_${dayjs().format("YYYYMMDD")}.pdf`,
          html2canvas: {
            scale: 2,
            useCORS: true,
            logging: true,
          },
          jsPDF: {
            unit: "mm",
            format: "a4",
            orientation: "landscape",
            putOnlyUsedFonts: true,
            compress: true,
          },
          pagebreak: { mode: ["avoid-all", "css", "legacy"] },
        })
        .save();
    } finally {
      setLoadingPdf(false);
    }
  };

  return (
    <div className="flex flex-col gap-2 cf-section">
      {showTitle && (
        <div className="border-b border-neutral-200 p-2 flex flex-row items-center justify-between">
          <h2 className="text-xl font-semibold text-neutral-900">
            CFシミュレーション
          </h2>

          <div className="flex flex-row gap-2 text-xs text-gray-500 export-buttons">
            <Button size="sm" variant="outline" onClick={handleExportPdf}>
              {loadingPdf ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Printer className="w-4 h-4" />
              )}{" "}
              PDF
            </Button>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2">
        {/* 左边表单区域 */}
        <div className="flex flex-col gap-2 p-2">
          {params && <InvestmentForm params={params} setParams={setParams} />}
        </div>

        {/* 右边图表区域 */}
        <div className="flex flex-col m-2 mt-0 p-4 rounded-lg bg-neutral-50">
          <h2 className="font-bold text-lg text-center border-b border-neutral-200 pb-2">
            シミュレーション結果
            <span className="text-xs text-gray-500 ml-2">
              <a href="#calculation-method" className="underline">
                算出方法について
              </a>
            </span>
          </h2>

          <div className="flex flex-row items-baseline justify-between w-full relative">
            <Tabs
              value={tab}
              onValueChange={(value) => setTab(value as "graph" | "table")}
              className="mt-2 mb-2"
            >
              <TabsList>
                <TabsTrigger value="graph">グラフ</TabsTrigger>
                <TabsTrigger value="table">詳細データテーブル</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="flex-1">
            {loading ? (
              <div className="flex justify-center items-center h-full">
                <Loader2 className="animate-spin w-10 h-10" />
              </div>
            ) : (
              <>
                {tab === "graph" && (
                  <div className="chart-container">
                    <SimulationChart
                      cfData={cfData}
                      majorRepairYear={majorRepairYear || 0}
                      loanPeriod={loanPeriod || 0}
                    />
                  </div>
                )}

                <div
                  ref={sectionRef}
                  data-section-ref="true" // 添加标识以便在克隆时找到元素
                  style={{
                    visibility: tab === "table" ? "visible" : "hidden",
                    position: tab === "table" ? "relative" : "absolute",
                    height: tab === "table" ? "auto" : "0",
                    overflow: tab === "table" ? "visible" : "hidden",
                  }}
                >
                  <div className="text-xs text-gray-500 mb-2">単位: 万円</div>
                  <DataTable
                    columns={cfDataTableColumns}
                    data={cfData}
                    defaultPageSize={50}
                    isLoading={loading}
                    highlightedColumnId={["累計CF", "累計利益"]}
                  />
                </div>
              </>
            )}
          </div>

          {/* 算出方法について */}
          <section
            className="mt-10 text-sm text-gray-800 text-left"
            id="calculation-method"
          >
            <div className="font-bold mb-2">
              ※シミュレーションの算出方法について<sup>*1</sup>
            </div>
            <div className="mb-4">
              本シミュレーションでは、以下の計算式に基づき算出を行っています。
            </div>

            <table className="border w-full mb-4 text-left text-xs">
              <tbody>
                <tr>
                  <td className="border px-2 py-1 font-semibold">
                    実質年間収入
                  </td>
                  <td className="border px-2 py-1">
                    想定年間収入 ×（1 + 家賃増減率 × 経過年数）× 入居率
                  </td>
                </tr>
                <tr>
                  <td className="border px-2 py-1 font-semibold">支出</td>
                  <td className="border px-2 py-1">
                    年間経費 + 所得税等（法人税等）<sup>*2</sup> + 大規模修繕費
                    <sup>*3</sup> + ローン返済額
                  </td>
                </tr>
                <tr>
                  <td className="border px-2 py-1 font-semibold">
                    売却時累計CF
                  </td>
                  <td className="border px-2 py-1">
                    累計CF<sup>*4</sup> +（売却金額<sup>*5</sup> － 売却費用
                    <sup>*6</sup> － 所得税等（法人税等）<sup>*7</sup> －
                    残債）－ 自己資金
                  </td>
                </tr>
              </tbody>
            </table>

            <ol className="space-y-4 text-xs list-none">
              <li>
                ※1
                本シミュレーションの結果はあくまでも目安です。特に税金に関しては簡易計算となっており、実際の税額とは異なる場合があります。投資判断の際は、税理士など専門家のアドバイスを必ずご確認ください。
              </li>
              <li>
                ※2
                所得税・住民税の計算は、所得控除等を考慮せず、入力された税率を用いた簡易的な方法で行っています。実際の課税額とは異なる場合がありますので、必ず税理士など専門家の確認をおすすめします。
                <ul className="list-none pl-6 mt-1">
                  <li>
                    ・所得税　
                    <a
                      href="https://www.nta.go.jp/taxes/shiraberu/taxanswer/shotoku/2260.htm"
                      target="_blank"
                      className="text-blue-600 underline"
                    >
                      国税庁公式：所得税率
                    </a>
                  </li>
                  <li>
                    ・住民税　お住まいの自治体のホームページ等をご確認ください
                  </li>
                  <li>
                    ・所得控除　
                    <a
                      href="https://www.nta.go.jp/taxes/shiraberu/taxanswer/code/bunya-kakuteishinkoku.htm"
                      target="_blank"
                      className="text-blue-600 underline"
                    >
                      確定申告ガイド（国税庁）
                    </a>
                  </li>
                </ul>
              </li>
              <li>
                ※3
                大規模修繕費は、該当年度の費用として課税対象となる所得に反映しています。
              </li>
              <li>
                ※4
                累計CFとは、売却年の年末時点までに蓄積されたキャッシュフロー合計です。
              </li>
              <li>
                ※5
                売却金額は、以下いずれか大きい方で仮定しています。実際の取引価格とは異なる場合がありますので、目安としてご利用ください。
                <ul className="list-none pl-6 mt-1">
                  <li>・売却時の実質年間収入 ÷ 購入時の表面利回り</li>
                  <li>・購入時の価格または建物価格</li>
                </ul>
              </li>
              <li>
                ※6
                売却費用は、売却価格の3%で概算しています。実際の費用とは異なる場合があります。
              </li>
              <li>
                ※7
                個人による売却の場合、取得から5年以内に売却した際の税率は約40%、6年目以降の売却時は約20%として試算しています。
              </li>
            </ol>
          </section>
        </div>
      </div>
    </div>
  );
}
