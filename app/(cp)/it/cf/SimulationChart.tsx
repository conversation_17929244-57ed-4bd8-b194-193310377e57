"use client"

import { <PERSON>, <PERSON>, Legend, <PERSON>ltip, YAxis, XAxis, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Composed<PERSON>hart } from "recharts";

// 自定义 Tooltip 组件
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border rounded-lg shadow-lg">
        <p className="font-medium text-sm mb-1">{`${label} 年目`}</p>
        <div className="space-y-1">
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center gap-2">
              {entry.dataKey === "accumulativeCashFlow" || entry.dataKey === "accumulativeNetProfit" ? (
                // Bar chart 用の丸いインジケーター
                <div
                  className="w-3 h-3"
                  style={{ backgroundColor: entry.color }}
                />
              ) : (
                // Line chart 用の線のインジケーター
                <div
                  className="w-3 h-0.5"
                  style={{ backgroundColor: entry.color }}
                />
              )}
              <span className="text-sm">
                {`${entry.name}: ${entry.value.toLocaleString()} 万円`}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

export default function SimulationChart({
  cfData,
  majorRepairYear,
  loanPeriod,
}: {
  cfData: any[];
  majorRepairYear: number;
  loanPeriod: number;
}) {

  function roundToMultiple(value: number, multiple: number): number {
    return Math.ceil(value / multiple) * multiple;
  }

  const leftValues = cfData.flatMap(d => [d.income, d.expense, d.netCashFlow]);
  const rightValues = cfData.flatMap(d => [d.accumulativeCashFlow, d.accumulativeNetProfit]);

  // 取左右最大绝对值
  const maxAbs = Math.max(
    ...leftValues.map(v => Math.abs(v)),
    ...rightValues.map(v => Math.abs(v)),
    1
  );

  // 取更美观的刻度间隔（1000 为主，如小数则退为 100）
  const stepUnit = maxAbs > 5000 ? 1000 : 100;

  // 四舍五入到 stepUnit 的整数倍，扩大 1.2 倍 buffer
  const roundedMax = roundToMultiple(maxAbs * 1.2, stepUnit);

  // 最终统一 Y 轴 domain（对称）
  const unifiedDomain: [number, number] = [-roundedMax, roundedMax];

  return <ResponsiveContainer width="100%" height={420}>
    {cfData && cfData.length > 0 ? (
      <ComposedChart data={cfData} margin={{ top: 30, right: 60, left: 0, bottom: 0 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="year" fontSize={12} tickLine={false} axisLine={false} />

        <YAxis
          yAxisId="left"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          // domain={[0, 'auto']} // ✅ 确保从 0 开始
          domain={unifiedDomain}
          label={{ value: "(万円)", angle: -90, position: "insideLeft", offset: 10 }}
        />

        <YAxis
          yAxisId="right"
          orientation="right"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          domain={unifiedDomain}
          // domain={[0, 'auto']} // ✅ 确保从 0 开始
          label={{ value: "(万円)", angle: 90, position: "insideRight", offset: 10 }}
        />

        <Tooltip content={<CustomTooltip />} />
        <Legend wrapperStyle={{ fontSize: 12 }} />

        <Line yAxisId="left" dataKey="income" name="実質年間収入" dot={false} type="monotone" strokeWidth={2} stroke="#86efac" />
        <Line yAxisId="left" dataKey="netCashFlow" name="CF" dot={false} type="monotone" strokeWidth={2} stroke="#93c5fd" />
        <Line yAxisId="left" dataKey="expense" name="支出" dot={false} type="monotone" strokeWidth={2} stroke="#fca5a5" />

        <Bar yAxisId="right" dataKey="accumulativeCashFlow" name="累計CF" fill="#e879f9" barSize={8} />
        <Bar yAxisId="right" dataKey="accumulativeNetProfit" name="売却時累計利益" fill="#c026d3" barSize={8} />

        {/* 事件点：大規模修繕/ローン完済，x/y都为数字且yAxisId指定时才渲染 */}
        {majorRepairYear && (() => {
          const d = cfData.find(d => d.year === majorRepairYear);
          return (
            typeof majorRepairYear === 'number' &&
            d &&
            typeof d.netCashFlow === 'number' &&
            !isNaN(majorRepairYear) &&
            !isNaN(d.netCashFlow)
          ) ? (
            <ReferenceDot
              x={majorRepairYear}
              y={d.netCashFlow}
              yAxisId="left"
              r={8}
              fill="#f87171"
              stroke="#fff"
              label={{ value: "大規模修繕", position: "top", fontSize: 12 }}
            />
          ) : null;
        })()}
        {loanPeriod && (() => {
          const d = cfData.find(d => d.year === loanPeriod);
          return (
            typeof loanPeriod === 'number' &&
            d &&
            typeof d.netCashFlow === 'number' &&
            !isNaN(loanPeriod) &&
            !isNaN(d.netCashFlow)
          ) ? (
            <ReferenceDot
              x={loanPeriod}
              y={d.netCashFlow}
              yAxisId="left"
              r={8}
              fill="#0ea5e9"
              stroke="#fff"
              label={{ value: "ローン完済", position: "top", fontSize: 12 }}
            />
          ) : null;
        })()}
      </ComposedChart>
    ) : (
      <div className="text-center text-gray-400 py-10">データがありません</div>
    )}
  </ResponsiveContainer>
}