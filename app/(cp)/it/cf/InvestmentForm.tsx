import { useState, useEffect, useRef, ReactNode } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Spinner } from "@/components/ui/spinner";

import debounce from "lodash-es/debounce";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import { Info } from "lucide-react";
import { getBuildingMaterial, getBuildingPrice } from "@/lib/helper/sekisan";
import dayjs from "dayjs";
import { InvestmentParams } from "@/actions/propertySimulationResult";
import { getHouseDepreciationYear } from "@/lib/helper/simulation";


const structureOptions = [
  { value: "鉄筋コンクリート造", label: "RC造", depreciationCode: "CONCRETE" },
  { value: "鉄骨造", label: "鉄骨造", depreciationCode: "STEEL" },
  { value: "軽量鉄骨造", label: "軽量鉄骨造", depreciationCode: "LIGHT_STEEL" },
  { value: "木造", label: "木造", depreciationCode: "WOOD" },
];

export default function InvestmentForm({ params, setParams }: {
  params: InvestmentParams;
  setParams: (params: InvestmentParams) => void;
}) {
  const [structure, setStructure] = useState(params.structure || "鉄筋コンクリート造");
  const [buildingAge, setBuildingAge] = useState(params.buildingAge || 15);
  const [buildingArea, setBuildingArea] = useState(params.buildingArea || 400);

  const yearOptions = Array.from({ length: 100 }, (_, i) => 1 + i);

  // 上部4分割の状態
  const [capital, setCapital] = useState(params.capital || 3000);
  const [interestRate, setInterestRate] = useState(params.interestRate || 2.5);
  const [loanPeriod, setLoanPeriod] = useState(params.loanPeriod || 30);
  const [occupancyRate, setOccupancyRate] = useState(params.occupancyRate || 95);

  // 購入条件の状態
  const [propertyPrice, setPropertyPrice] = useState(params.propertyPrice || 10000);
  const [buildingPrice, setBuildingPrice] = useState(params.buildingPrice || 4000);

  // 収入の状態
  const [annualIncome, setAnnualIncome] = useState(params.expectedAnnualIncome || 500);
  const [rentGrowthRate, setRentGrowthRate] = useState(params.rentGrowthRate.toString() || "-1");

  // 支出の状態
  const [propertyTax, setPropertyTax] = useState(params.propertyTax || 57);
  const [managementFeeRate, setManagementFeeRate] = useState(params.managementFeeRate || 5);
  const [repairFeeRate, setRepairFeeRate] = useState(params.repairFeeRate || 5);
  const [otherFee, setOtherFee] = useState(params.otherFee || 0);

  const [taxRate, setTaxRate] = useState(params.taxRate.toString() || "30");
  const [majorRepairYear, setMajorRepairYear] = useState(params.majorRepairYear.toString() || "10");
  const [majorRepairCost, setMajorRepairCost] = useState(params.majorRepairCost || 400);

  const [sellSellingCapRatePerc, setSellSellingCapRatePerc] = useState(parseFloat((params.expectedAnnualIncome / params.propertyPrice * 100).toFixed(1)) || 5);

  // 防抖 onChange
  const debouncedOnChange = useRef(
    debounce((newParams: InvestmentParams) => {
      setParams({
        ...params,
        ...newParams,
      });
    }, 1000)
  ).current;

  const renderTooltip = (text: ReactNode) => {
    return (
      <TooltipProvider delayDuration={200}>
        <Tooltip>
          <TooltipTrigger>
            <Info className="w-3 h-3" />
          </TooltipTrigger>
          <TooltipContent className="rounded-md bg-black text-white text-xs p-3 max-w-xs"
            side="top"
            sideOffset={5}>
            {text}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  const getRemainYear = (structure: string, buildingAge: number) => {
    return getBuildingPrice({
      recordType: "BUILDING",
      buildingMaterial: structure || "木造",
      buildingBuiltYear: dayjs().year() - (buildingAge || 50),
      buildingSize: params.buildingArea || 0,
      unitArea: params.buildingArea || 0,
    }).remainY;
  }

  useEffect(() => {
    const params = {
      propertyPrice: propertyPrice,
      structure: structure,
      buildingAge: buildingAge,
      buildingArea: buildingArea,

      capital: capital,
      interestRate: interestRate,
      loanPeriod: loanPeriod,
      occupancyRate: occupancyRate,

      buildingPrice: buildingPrice,
      expectedAnnualIncome: annualIncome,
      rentGrowthRate: Number(rentGrowthRate),

      propertyTax: propertyTax,
      managementFeeRate: managementFeeRate,
      repairFeeRate: repairFeeRate,
      otherFee: otherFee,

      taxRate: Number(taxRate),
      majorRepairYear: Number(majorRepairYear),
      majorRepairCost: majorRepairCost,

      sellSellingCapRatePerc: sellSellingCapRatePerc,
    };

    debouncedOnChange(params as InvestmentParams);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [propertyPrice, structure, buildingAge, buildingArea, capital, interestRate, loanPeriod, occupancyRate, buildingPrice, annualIncome, rentGrowthRate, propertyTax, managementFeeRate, repairFeeRate, otherFee, taxRate, majorRepairYear, majorRepairCost, sellSellingCapRatePerc]);

  useEffect(() => {
    setBuildingPrice(getBuildingPrice({
      recordType: "BUILDING",
      buildingMaterial: structure || "木造",
      buildingBuiltYear: dayjs().year() - (buildingAge || 50),
      buildingSize: buildingArea || 0,
      unitArea: buildingArea || 0,
    }).rebildBuildingPrice);
  }, [structure, buildingAge, buildingArea]);

  return (
    <>
      <div className="font-bold text-base flex items-end justify-start gap-2 pt-4">
        <span>✏️ 条件を変更</span>
        <span className="text-xs text-gray-500">変更すると自動でグラフに反映されます。</span>
      </div>


      <form className="bg-white shadow-sm flex flex-col gap-2 w-full mx-auto">
        {/* 条件を変更 */}
        <section className="border-t border-neutral-200 p-4 flex flex-col gap-2">
          <div className="font-semibold mb-2 flex flex-row gap-1 items-center">
            物件条件
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-1">
              <label className="block text-sm mb-1">物件購入価格</label>
              <Input
                value={propertyPrice}
                onChange={(e) => setPropertyPrice(Number(e.target.value))}
              />
              <span className="text-xs text-neutral-500 flex flex-row gap-1 items-center">
                購入時諸費用 {parseFloat((propertyPrice * 0.07).toFixed(0))}万円{
                  renderTooltip("購入時諸費用 = 物件価格 × 7%")
                } | 借入額 {parseFloat((propertyPrice - capital).toFixed(0))}万円
              </span>
            </div>

            <div>
              <label className="block text-sm mb-1">物件構造</label>
              <Select value={structure} onValueChange={setStructure}>
                <SelectTrigger className="w-full bg-white"><SelectValue /></SelectTrigger>
                <SelectContent>
                  {structureOptions.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
            <div>
              <label className="block text-sm mb-1">築年数</label>
              <Select value={String(buildingAge)} onValueChange={v => setBuildingAge(Number(v))}>
                <SelectTrigger className="w-full bg-white"><SelectValue /></SelectTrigger>
                <SelectContent>
                  {yearOptions.map(y => <SelectItem key={y} value={String(y)}>{y} 年</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm mb-1">建物面積</label>
              <Input value={buildingArea} onChange={e => setBuildingArea(Number(e.target.value))} className="w-full bg-white" />
            </div>
            <div className="flex flex-col col-span-2">
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                建物価格
                {renderTooltip(<>
                  物件価格のうち、建物が占める価格を入力してください。
                  初期値には、目安として以下の計算式を用いて簡易的に計算された結果が自動入力されています。
                  <br />
                  <br />
                  ■築年数が法定耐用年数内の物件の場合
                  <br />

                  建物の構造毎の再調達価格 × 建物面積 × ｛建物の構造毎の法定耐用年数 - 築年数 + （築年数 × 20%）｝ ÷ 建物の構造毎の法定耐用年数
                  <br />
                  <br />

                  ■築年数が法定耐用年数を超過した物件の場合
                  <br />
                  建物の構造毎の再調達価格 × 建物面積 × （建物の構造毎の法定耐用年数 × 20% ÷ 建物の構造毎の法定耐用年数）
                  <br />
                  <br />
                  ※建物の構造毎の再調達価格と法定耐用年数
                  <br />
                  <ol className="list-decimal list-inside">
                    <li>軽量鉄骨造 再調達価格：13万円/㎡ 耐用年数：19年</li>
                    <li>木造 再調達価格：13万円/㎡ 耐用年数：22年</li>
                    <li>鉄骨造・重量鉄骨造 再調達価格：18万円/㎡ 耐用年数：34年</li>
                    <li>RC・SRC造 再調達価格：20万円/㎡ 耐用年数：47年</li>
                  </ol>
                </>)}
              </label>
              <Input
                disabled
                value={buildingPrice}
                onChange={(e) => setBuildingPrice(Number(e.target.value))}
              />
              <span className="text-xs text-neutral-500 flex flex-row gap-1 items-center mt-1">
                残存耐用年数 {getRemainYear(params.structure, params.buildingAge)}年 | 償却年数
                : {getHouseDepreciationYear(structureOptions.find(opt => opt.value === structure)?.depreciationCode || "WOOD", dayjs().year() - params.buildingAge)} |
                減価償却費 {parseFloat((buildingPrice / (getHouseDepreciationYear(structureOptions.find(opt => opt.value === structure)?.depreciationCode || "WOOD", dayjs().year() - params.buildingAge) || 1)).toFixed(0))}万円{
                  renderTooltip(<>
                    減価償却費については、下記の簡便法の計算式で算出した中古資産の耐用年数と建物価格を元にして、定額法を用いて簡易計算しています。
                    <br /><br />
                    ■法定耐用年数を超過した中古資産(物件)の耐用年数＊1<br />
                    建物の構造毎の法定耐用年数＊2 × 20%
                    <br /><br />
                    ■法定耐用年数を経過していない中古資産(物件)の耐用年数＊1<br />
                    建物の構造毎の法定耐用年数＊2 - 築年数 ＋ (築年数 × 20%)
                    <br /><br />
                    ＊1 上記の計算により算出した耐用年数に1年未満の端数があるときは、その端数を切り捨て、その年数が2年に満たない場合には2年とします。
                    <br /><br />
                    ＊2 建物の構造毎の法定耐用年数は、下記の年数を使用しています。<br />
                    木造:22年  軽量鉄骨造：19年＊3  重量鉄骨造：34年  RC造・SRC造:47年
                    <br /><br />
                    ＊3 軽量鉄骨造の場合、骨格材の肉厚が3mm以下又は4mm超の場合は法定耐用年数が異なりますので注意してください。
                  </>)
                }
              </span>
            </div>
          </div>

        </section>

        {/* <div className="flex justify-start">
      <Button type="button" className="px-4 h-10" onClick={handleSubmit} disabled={loading}>
        {loading ? <Loader2 className="animate-spin w-5 h-5" /> : "シミュレーションする"}
      </Button>
    </div> */}

        {/* 収入 */}
        <section className="border-t border-neutral-200 p-4">
          <div className="font-semibold mb-2">収入</div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex flex-col gap-1">
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                想定年間収入
              </label>
              <Input
                value={annualIncome}
                onChange={(e) => setAnnualIncome(Number(e.target.value))}
              />
              <span className="text-xs text-neutral-500 flex flex-row gap-1 items-center">
                表面利回り {parseFloat((annualIncome / propertyPrice * 100).toFixed(1))}%
              </span>
            </div>
            <div>
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                家賃増減率
                {renderTooltip("1年あたりどのくらい家賃が増減するかの想定値を入力してください。初期値には、目安として「-1%」 が入力されています。")}
              </label>
              <Select
                value={rentGrowthRate}
                onValueChange={setRentGrowthRate}
              >
                <SelectTrigger>
                  <SelectValue placeholder="選択" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 21 }, (_, i) => (i - 10) * 0.5).map((value) => (
                    <SelectItem key={value} value={value.toString()}>{value}% /年</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </section>

        {/* 上部4分割 */}
        <section className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border-t border-neutral-200">
          <div>
            <label className="block text-sm font-medium mb-1 flex flex-row gap-1 items-center">
              自己資金
              {renderTooltip("物件購入に使う自己資金を入力してください。初期値には、目安として「物件価格の2割」が自動入力されています。")}
            </label>
            <div className="flex items-center gap-1">
              <Input
                value={capital}
                onChange={(e) => setCapital(Number(e.target.value))}
                className="w-20"
              />
              <span className="text-sm">万円</span>
            </div>
            <Slider
              value={[capital]}
              onValueChange={(value) => setCapital(value[0])}
              min={0}
              max={10000}
              step={100}
              className="mt-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1 flex flex-row gap-1 items-center">
              金利
              {renderTooltip("融資を受ける場合の金利を入力してください。初期値には、目安として「2%」が自動入力されています。")}
            </label>
            <div className="flex items-center gap-1">
              <Input
                value={interestRate}
                onChange={(e) => setInterestRate(Number(e.target.value))}
                className="w-14"
              />
              <span className="text-sm">%</span>
            </div>
            <Slider
              value={[interestRate]}
              onValueChange={(value) => setInterestRate(value[0])}
              min={0}
              max={10}
              step={0.1}
              className="mt-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1 flex flex-row gap-1 items-center">
              ローン期間
              {renderTooltip("融資を受ける場合のローン期間を入力してください。初期値には、目安として「10年」が自動入力されています。")}
            </label>
            <div className="flex items-center gap-1">
              <Input
                value={loanPeriod}
                onChange={(e) => setLoanPeriod(Number(e.target.value))}
                className="w-14"
              />
              <span className="text-sm">年</span>
            </div>
            <Slider
              value={[loanPeriod]}
              onValueChange={(value) => setLoanPeriod(value[0])}
              min={1}
              max={35}
              step={1}
              className="mt-2"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1 flex flex-row gap-1 items-center">
              入居率
              {renderTooltip("物件の入居率を入力してください。初期値には、目安として「90%」が自動入力されています。")}
            </label>
            <div className="flex items-center gap-1">
              <Input
                value={occupancyRate}
                onChange={(e) => setOccupancyRate(Number(e.target.value))}
                className="w-14"
              />
              <span className="text-sm">%</span>
            </div>
            <Slider
              value={[occupancyRate]}
              onValueChange={(value) => setOccupancyRate(value[0])}
              min={0}
              max={100}
              step={1}
              className="mt-2"
            />
          </div>
        </section>



        {/* 支出 */}
        <section className="border-t border-neutral-200 p-4">
          <div className="font-semibold mb-2">支出</div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                固定資産税・都市計画税
                {renderTooltip("物件にかかる固定資産税と都市計画税を入力してください。初期値には、目安として「想定年間収入の1カ月分」が入力されています。")}
              </label>
              <Input
                value={propertyTax}
                onChange={(e) => setPropertyTax(Number(e.target.value))}
              />
            </div>
            <div>
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                管理費（実質年間収入の%）
                {renderTooltip("物件にかかる管理費を入力してください。初期値には、目安として「5%」が入力されています。")}
              </label>
              <Input
                value={managementFeeRate}
                onChange={(e) => setManagementFeeRate(Number(e.target.value))}
              />
            </div>
            <div>
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                修繕費（実質年間収入の%）
                {renderTooltip("物件の維持管理にかかる修繕費用を入力してください。初期値には、目安として「実質年間家賃収入の5%」が入力されています。築年数が古い物件の場合は、少し高め（例:10%）に設定するなど、物件に応じて設定を変更しましょう。")}
              </label>
              <Input
                value={repairFeeRate}
                onChange={(e) => setRepairFeeRate(Number(e.target.value))}
              />
            </div>
            <div>
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                その他経費
                {renderTooltip("管理費・修繕費以外で定期的に発生する費用（例:消防点検費用など）を入力してください。")}
              </label>
              <Input
                value={otherFee}
                onChange={(e) => setOtherFee(Number(e.target.value))}
              />
            </div>

          </div>
          <div className="flex flex-row gap-4 mt-4">
            <div>
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                所得・法人税率
                {renderTooltip("物件の所得・法人税率を入力してください。初期値には、目安として「30%」が入力されています。")}
              </label>
              <Select
                value={taxRate}
                onValueChange={setTaxRate}
              >
                <SelectTrigger>
                  <SelectValue placeholder="選択" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">0%</SelectItem>
                  <SelectItem value="10">10%</SelectItem>
                  <SelectItem value="20">20%</SelectItem>
                  <SelectItem value="30">30%</SelectItem>
                  <SelectItem value="40">40%</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                大規模修繕費
                {renderTooltip("大規模修繕（外壁塗装、屋上防水等）の時期と金額の想定値を入力してください。初期値には、大規模修繕の1回目は12年後、目安として「建物面積 × 1万円」が入力されています。")}
              </label>
              <div className="flex items-center gap-2">
                <Select
                  value={majorRepairYear}
                  onValueChange={setMajorRepairYear}
                >
                  <SelectTrigger className="w-24">
                    <SelectValue placeholder="年数" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 30 }, (_, i) => i + 1).map(year => (
                      <SelectItem key={year} value={year.toString()}>{year}年後</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Input
                  value={majorRepairCost}
                  onChange={(e) => setMajorRepairCost(Number(e.target.value))}
                  className="w-20"
                />
                <span className="text-sm">万円</span>
              </div>
            </div>
          </div>
        </section>

        {/* 売却 */}
        <section className="border-t border-neutral-200 p-4">
          <div className="font-semibold mb-2">売却</div>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-1">
              <label className="block text-sm mb-1 flex flex-row gap-1 items-center">
                売却時の利回り
                {renderTooltip("物件を売却した際の利回りを入力してください。初期値には、目安として購入するときに同じ利回りが入力されています。")}
              </label>
              <Input
                type="number"
                min={1}
                max={100}
                step={0.01}
                value={sellSellingCapRatePerc.toString()}
                onChange={(e) => setSellSellingCapRatePerc(Number(e.target.value))}
              />
              <div className="text-xs text-neutral-500 flex flex-row gap-1 items-center">
                vs 取得時の利回り: {parseFloat((sellSellingCapRatePerc - (annualIncome / propertyPrice * 100)).toFixed(1))}% | 売却諸費用: 売却価格*4%
              </div>
            </div>
          </div>
        </section>
      </form>
    </>
  );
} 