"use client";

import "leaflet/dist/leaflet.css";
import dynamic from "next/dynamic";
import { useEffect, useRef, useState } from "react";
import { LatLngExpression } from "leaflet";
import { useMap, useMapEvent } from "react-leaflet";
import { getTorusPinData } from "@/actions/chiban";
import { Separator } from "@radix-ui/react-separator";
import { toast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Marker as LeafletMarker } from "leaflet";
import { useAuthStore } from "@/store/auth";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";

let L: any; // Declare L globally

if (typeof window !== "undefined") {
  L = require("leaflet"); // ✅ Import Leaflet only in the browser
}

// ✅ Leaflet 动态导入（避免 SSR 问题）
const MapContainer = dynamic(() => import("react-leaflet").then((mod) => mod.MapContainer), { ssr: false });
const TileLayer = dynamic(() => import("react-leaflet").then((mod) => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import("react-leaflet").then((mod) => mod.Marker), { ssr: false }) as any;
const Popup = dynamic(() => import("react-leaflet").then((mod) => mod.Popup), { ssr: false });


// ✅ 点击事件处理器组件
function ClickHandler({ onClick }: { onClick: (latlng: [number, number]) => void }) {
  useMapEvent("click", (e: any) => {
    const latlng: [number, number] = [e.latlng.lat, e.latlng.lng];
    onClick(latlng);
  });

  return null;
}

function Recenter({ center }: { center: LatLngExpression }) {
  const map = useMap(); // ✅ 替换原来 dynamic 的方式

  useEffect(() => {
    if (center) {
      map.setView(center, map.getZoom(), {
        animate: true,
      });
    }
  }, [center]);

  return null;
}

export default function Chiban() {
  const [clickedLatLng, setClickedLatLng] = useState<[number, number] | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([35.6895, 139.6917]); // 默认新宿
  const [isLoading, setIsLoading] = useState(false);
  const [parsedResult, setParsedResult] = useState<any>(null);
  const { currentUser } = useAuthStore();

  const handleClick = async (latlng: [number, number]) => {
    console.log("📍 Clicked location:", latlng);
    setClickedLatLng(latlng);
    setMapCenter(latlng);
    setIsLoading(true);
    const res = await getTorusPinData({
      lat: latlng[0],
      lng: latlng[1]
    });

    if (res.success) {
      let original = res.data?.place_address + res.data?.place_branch;
      let chiban = res.data?.place_address + res.data?.my_chiban?.branch;

      sendLark({
        message: `[地番マップ][${currentUser?.name}] from ${original} to ${chiban}`,
        url: LARK_URLS.USER_ACTIVITY_CHANNEL,
      })

      setParsedResult(res.data);
    } else {
      toast({
        title: "エラーが発生しました",
        description: res.message || "地番マップの取得に失敗しました",
      });
    }
    setIsLoading(false);
  };

  return (
    <div className="w-full h-full overflow-y-auto">
      <div className="flex justify-between items-center p-4 border-b border-neutral-200">
        <h1 className="text-2xl font-bold">地番マップ</h1>
      </div>

      <div className="flex flex-row justify-between items-center p-4">
        クリックした場所にピンを表示し、地図を移動して、住居表示・地番を表示してます
      </div>

      <MapContainer
        // @ts-ignore 
        center={mapCenter as LatLngExpression}
        zoom={18}
        minZoom={13}
        style={{ height: "600px", width: "100%", position: "relative", zIndex: 0 }}
      >
        <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />

        <ClickHandler onClick={handleClick} />

        <Recenter center={mapCenter} />

        {clickedLatLng && parsedResult && (
          <Marker
            position={clickedLatLng}
            icon={L.icon({
              iconUrl: "/assets/map/blue-dot.png",
              iconSize: [40, 40],
              iconAnchor: [20, 40],
              popupAnchor: [1, -34],
            })}
            ref={(marker: LeafletMarker) => {
              if (marker) {
                setTimeout(() => marker.openPopup(), 0);
              }
            }}
          >
            <Popup autoPan={true}>
              <div>
                <div>住所: {parsedResult?.place_address} {parsedResult?.place_branch}</div>
                <div>地番: {parsedResult?.place_address} {parsedResult?.my_chiban?.branch}</div>
              </div>
            </Popup>
          </Marker>
        )}
      </MapContainer>

      {currentUser?.accessLevel && currentUser?.accessLevel >= 30 && <div className="p-2">
        {/* <div>
          Position: {clickedLatLng ? `${clickedLatLng[0]}, ${clickedLatLng[1]}` : '未選択'}
        </div>

        <div className="flex flex-col">
          <div>
            住所: {parsedResult?.place_address} {parsedResult?.place_branch}
          </div>
          <div>
            地番: {parsedResult?.place_address} {parsedResult?.my_chiban?.branch}
          </div>

        </div> */}

        <pre className="mt-2 text-xs bg-neutral-100 p-2 rounded-md">{isLoading ? <Loader2 className="animate-spin" /> : JSON.stringify(parsedResult, null, 2)}</pre>
      </div>}
    </div>
  );
}