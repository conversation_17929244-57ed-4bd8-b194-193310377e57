import { MinpakuResult } from "./minpakuCalculations"
import { Separator } from "@/components/ui/separator"

interface MinpakuResultTableProps {
  result: MinpakuResult
}

export default function MinpakuResultTable({ result }: MinpakuResultTableProps) {
  const formatNumber = (value: number) => {
    return value.toLocaleString('ja-JP')
  }

  const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  return (
    <div className="space-y-3">
      <div className="bg-neutral-50 p-3 rounded-lg">
        <h3 className="text-lg font-semibold text-center mb-4">年間収益サマリー</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>宿泊料金:</span>
              <span className="font-semibold text-green-600">
                ¥{formatNumber(result.yearlyTotals.totalGrossRevenue)}
              </span>
            </div>
            <div className="flex justify-between text-red-600">
              <span>Airbnb手数料:</span>
              <span>-¥{formatNumber(result.yearlyTotals.totalAirbnbFee)}</span>
            </div>
            <div className="flex justify-between text-red-600">
              <span>管理会社手数料:</span>
              <span>-¥{formatNumber(result.yearlyTotals.totalManagementFee)}</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-red-600">
              <span>清掃費:</span>
              <span>-¥{formatNumber(result.yearlyTotals.totalCleaningFee)}</span>
            </div>
            <div className="flex justify-between text-red-600">
              <span>光熱費・通信費:</span>
              <span>-¥{formatNumber(result.yearlyTotals.totalUtilityFee)}</span>
            </div>
            <div className="flex justify-between text-red-600">
              <span>消耗品費:</span>
              <span>-¥{formatNumber(result.yearlyTotals.totalSuppliesExpense)}</span>
            </div>
            {result.yearlyTotals.totalInternetFee > 0 && (
              <div className="flex justify-between text-red-600">
                <span>インターネット料金:</span>
                <span>-¥{formatNumber(result.yearlyTotals.totalInternetFee)}</span>
              </div>
            )}
          </div>
        </div>
        <Separator className="my-4" />
        <div className="flex justify-between items-center text-lg font-bold">
          <span>年間純収入:</span>
          <span className={result.yearlyTotals.totalNetIncome >= 0 ? "text-green-600" : "text-red-600"}>
            ¥{formatNumber(result.yearlyTotals.totalNetIncome)}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mt-2">
          <div className="flex justify-between">
            <span>平均稼働率:</span>
            <span>{formatPercent(result.yearlyTotals.averageOccupancyRate)}</span>
          </div>
          <div className="flex justify-between">
            <span>総投資額:</span>
            <span>¥{formatNumber(result.yearlyTotals.totalPropertyCost)}</span>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 text-sm mt-3">
          <div className="flex justify-between items-center">
            <span className="font-bold text-gray-700">ROI (売上利回り):</span>
            <span className="font-bold">{formatPercent(result.yearlyTotals.roi)}</span>
          </div>

          <div className="flex justify-between items-center">
            <span className="font-bold text-gray-700">NOI (純利回り):</span>
            <span className="font-bold">{formatPercent(result.yearlyTotals.noi)}</span>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg">
        <div className="p-3 border-b">
          <h3 className="text-base font-semibold">月別詳細</h3>
        </div>
        <div className="p-3">
          <div className="text-xs text-gray-500 mb-2">
            単位: 円
          </div>
          <div className="overflow-x-auto">
            <table className="w-full text-xs border-collapse">
              <thead>
                <tr className="border-b bg-gray-50">
                  <th className="text-left p-2 font-semibold">月</th>
                  <th className="text-right p-2 font-semibold">稼働率(%)</th>
                  <th className="text-right p-2 font-semibold">単価倍率(%)</th>
                  <th className="text-right p-2 font-semibold">宿泊日数</th>
                  <th className="text-right p-2 font-semibold">調整後単価</th>
                  <th className="text-right p-2 font-semibold">RevPar</th>
                  <th className="text-right p-2 font-semibold">月収入</th>
                  <th className="text-left p-2 font-semibold">コメント</th>
                </tr>
              </thead>
              <tbody>
                {result.monthlyData.map((data, index) => {
                  const daysInMonth = new Date(2024, index + 1, 0).getDate()
                  const revPar = data.totalRevenue / daysInMonth

                  return (
                    <tr key={data.month} className="border-b hover:bg-gray-50">
                      <td className="p-2">{data.month}</td>
                      <td className="text-right p-2">{formatPercent(data.occupancyRate)}</td>
                      <td className="text-right p-2">{formatPercent(data.priceMultiplier * 100)}</td>
                      <td className="text-right p-2">{data.nightsBooked}</td>
                      <td className="text-right p-2">¥{formatNumber(data.adjustedPricePerNight)}</td>
                      <td className="text-right p-2">¥{formatNumber(Math.round(revPar))}</td>
                      <td className="text-right p-2 font-semibold">
                        <span className={data.netIncome >= 0 ? "text-green-600" : "text-red-600"}>
                          ¥{formatNumber(data.netIncome)}
                        </span>
                      </td>
                      <td className="p-2 text-gray-600">{data.comment}</td>
                    </tr>
                  )
                })}
              </tbody>
              <tfoot>
                <tr className="border-t-2 bg-gray-100 font-semibold">
                  <td className="p-2">合計</td>
                  <td className="text-right p-2">{formatPercent(result.yearlyTotals.averageOccupancyRate)}</td>
                  <td className="text-right p-2">-</td>
                  <td className="text-right p-2">
                    {result.monthlyData.reduce((sum, data) => sum + data.nightsBooked, 0)}
                  </td>
                  <td className="text-right p-2">
                    ¥{formatNumber(Math.round(result.monthlyData.reduce((sum, data) => sum + data.adjustedPricePerNight, 0) / 12))}
                  </td>
                  <td className="text-right p-2">
                    ¥{formatNumber(Math.round(result.yearlyTotals.totalRevenue / 365))}
                  </td>
                  <td className="text-right p-2">
                    <span className={result.yearlyTotals.totalNetIncome >= 0 ? "text-green-600" : "text-red-600"}>
                      ¥{formatNumber(result.yearlyTotals.totalNetIncome)}
                    </span>
                  </td>
                  <td className="p-2">-</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>

      <div className="bg-neutral-50 p-3 rounded-lg">
        <div className="text-xs text-gray-600 space-y-2">
          <p className="font-semibold">※ シミュレーションについて</p>
          <ul className="space-y-1 list-disc list-inside">
            <li>稼働率は選択されたエリア区分をベースとした月別調整を行っています</li>
            <li>単価倍率は市場データに基づく月別の価格調整係数です</li>
            <li>調整後単価 = 基本料金 × 単価倍率</li>
            <li>RevPar = 1日あたりの平均収入（売上 ÷ 月の日数）</li>
            <li>実際の収益は立地、物件の質、競合状況等により大きく変動する可能性があります</li>
            <li>エリア別稼働率: 東京都心6区100%、東京23区その他90%、大阪・京都80%、地方・郊外50%</li>
            <li>単価倍率は、ANAおよびJALが定めるシーズナリティ（ハイ／ローシーズン）を参考に設定</li>
            <li>ROI = 年間売上 ÷ 総投資額 × 100</li>
            <li>NOI = 年間純収入 ÷ 総投資額 × 100</li>
          </ul>
        </div>
      </div>
    </div>
  )
}