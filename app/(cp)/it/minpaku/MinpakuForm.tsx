import { useState, useEffect, useCallback, useRef } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MinpakuParams } from "./minpakuCalculations"
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip"
import { Info, ExternalLink } from "lucide-react"
import Link from "next/link"

interface MinpakuFormProps {
  params: MinpakuParams
  setParams: (params: MinpakuParams) => void
}

export default function MinpakuForm({ params, setParams }: MinpakuFormProps) {
  const [localParams, setLocalParams] = useState<MinpakuParams>(params)
  const [isUpdating, setIsUpdating] = useState(false)
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    setLocalParams(params)
  }, [params])

  // Debounced function to update parent params
  const debouncedSetParams = useCallback((newParams: MinpakuParams) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    setIsUpdating(true)

    debounceTimeoutRef.current = setTimeout(() => {
      setParams(newParams)
      setIsUpdating(false)
    }, 500) // 500ms debounce delay
  }, [setParams])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [])

  const updateParam = (key: keyof MinpakuParams, value: any) => {
    const newParams = { ...localParams, [key]: value }
    setLocalParams(newParams)
    debouncedSetParams(newParams)
  }



  return (
    <div className="space-y-3">
        {/* Update Status Indicator */}
        {isUpdating && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 text-sm text-blue-700 flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            計算中...
          </div>
        )}

        {/* Property Related Cost */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">物件関連コスト</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 pt-2">
            <div className="space-y-2">
              <Label htmlFor="propertyCost">物件価格</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="propertyCost"
                  type="number"
                  step="0.1"
                  value={localParams.propertyCost || 10000}
                  onChange={(e) => updateParam('propertyCost', parseFloat(e.target.value) || 0)}
                  className="flex-1"
                />
                <span className="text-sm text-gray-500">万円</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
            <div className="space-y-2">
              <Label htmlFor="acquisitionCostRate">取得費用率</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="acquisitionCostRate"
                  type="number"
                  value={localParams.acquisitionCostRate || 7}
                  onChange={(e) => updateParam('acquisitionCostRate', parseFloat(e.target.value) || 0)}
                  className="flex-1"
                  step="0.1"
                />
                <span className="text-sm text-gray-500">%</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="reformCost">リフォーム費用</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="reformCost"
                  type="number"
                  step="0.1"
                  value={localParams.reformCost || 0}
                  onChange={(e) => updateParam('reformCost', parseFloat(e.target.value) || 0)}
                  className="flex-1"
                />
                <span className="text-sm text-gray-500">万円</span>
              </div>
            </div>
            </div>


          </CardContent>
        </Card>

        {/* 基本設定 */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">宿泊収入</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 pt-2">
            <div className="grid grid-cols-1 gap-3">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="basePricePerNight">1泊あたりの基本料金</Label>
                  <TooltipProvider delayDuration={200}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>平日・休日の平均的な料金設定</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Link
                    href="https://www.airbnb.jp/host/homes?room_type=ENTIRE_HOME"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 underline"
                  >
                    <ExternalLink className="w-3 h-3" />
                    Airbnb参考価格
                  </Link>
                </div>
                <div className="flex items-center gap-2">
                  <Input
                    id="basePricePerNight"
                    type="number"
                    value={localParams.basePricePerNight}
                    onChange={(e) => updateParam('basePricePerNight', parseInt(e.target.value) || 0)}
                    className="flex-1"
                  />
                  <span className="text-sm text-gray-500">円</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="baseOccupancyRate">基本稼働率</Label>
                  <TooltipProvider delayDuration={200}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-4 h-4 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>エリア別の基本稼働率を選択<br/>月別で自動調整されます</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Select
                  value={localParams.baseOccupancyRate?.toString() || "100"}
                  onValueChange={(value) => updateParam('baseOccupancyRate', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="エリアを選択" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="100">東京都心6区・民泊人気エリア (浅草、上野、池袋など) - 100%</SelectItem>
                    <SelectItem value="90">東京23区その他地域 - 90%</SelectItem>
                    <SelectItem value="80">大阪・京都など都市部 - 80%</SelectItem>
                    <SelectItem value="50">地方・郊外 - 50%</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
 
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isSuperhost"
                checked={localParams.isSuperhost}
                onCheckedChange={(checked) => updateParam('isSuperhost', checked)}
              />
              <Label htmlFor="isSuperhost" className="flex items-center gap-2">
                  スーパーホスト
                <TooltipProvider delayDuration={200}>
                  <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-4 h-4 text-gray-400" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>稼働率が15%向上します</p>
                  </TooltipContent>
                </Tooltip>
                </TooltipProvider>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="isNewHost"
                checked={localParams.isNewHost}
                onCheckedChange={(checked) => updateParam('isNewHost', checked)}
              />
              <Label htmlFor="isNewHost" className="flex items-center gap-2">
                新規ホスト
                <TooltipProvider delayDuration={200}>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="w-4 h-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>稼働率が30%低下します</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* 手数料設定 */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">ランニングコスト</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 pt-2">
            <div className="space-y-2">
              <Label htmlFor="airbnbChargeRate">Airbnb手数料</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="airbnbChargeRate"
                  type="number"
                  value={localParams.airbnbChargeRate}
                  onChange={(e) => updateParam('airbnbChargeRate', parseFloat(e.target.value) || 0)}
                  className="flex-1"
                  step="0.1"
                />
                <span className="text-sm text-gray-500">%</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="managementCompanyRate">管理会社手数料</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="managementCompanyRate"
                  type="number"
                  value={localParams.managementCompanyRate}
                  onChange={(e) => updateParam('managementCompanyRate', parseFloat(e.target.value) || 0)}
                  className="flex-1"
                  step="0.1"
                />
                <span className="text-sm text-gray-500">%</span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">

            <div className="space-y-2">
              <Label htmlFor="monthlyCleaningFee">清掃費</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="monthlyCleaningFee"
                  type="number"
                  value={localParams.monthlyCleaningFee}
                  onChange={(e) => updateParam('monthlyCleaningFee', parseInt(e.target.value) || 0)}
                  className="flex-1"
                />
                <span className="text-sm text-gray-500">円/月</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="monthlyUtilityFee">光熱費・通信費</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="monthlyUtilityFee"
                  type="number"
                  value={localParams.monthlyUtilityFee}
                  onChange={(e) => updateParam('monthlyUtilityFee', parseInt(e.target.value) || 0)}
                  className="flex-1"
                />
                <span className="text-sm text-gray-500">円/月</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="monthlySuppliesExpense">消耗品費</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="monthlySuppliesExpense"
                  type="number"
                  value={localParams.monthlySuppliesExpense}
                  onChange={(e) => updateParam('monthlySuppliesExpense', parseInt(e.target.value) || 0)}
                  className="flex-1"
                />
                <span className="text-sm text-gray-500">円/月</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="monthlyInternetFee">インターネット料金</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="monthlyInternetFee"
                  type="number"
                  value={localParams.monthlyInternetFee || 0}
                  onChange={(e) => updateParam('monthlyInternetFee', parseInt(e.target.value) || 0)}
                  className="flex-1"
                />
                <span className="text-sm text-gray-500">円/月</span>
              </div>
            </div>
            </div>
          </CardContent>
        </Card>
      </div>
  )
}
