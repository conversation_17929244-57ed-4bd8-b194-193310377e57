"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>kuParams, MinpakuResult, calculateMinpakuIncome } from "./minpakuCalculations"
import MinpakuForm from "./MinpakuForm"
import MinpakuResultTable from "./MinpakuResultTable"
import { Loader2, Printer } from "lucide-react"
import { Button } from "@/components/ui/button"
import { TooltipProvider } from "@/components/ui/tooltip"

interface MinpakuSectionProps {
  initialParams?: Partial<MinpakuParams>
  showTitle?: boolean
  className?: string
}

export default function MinpakuSection({
  initialParams = {},
  showTitle = true,
  className = ""
}: MinpakuSectionProps) {
  const initParams = (): MinpakuParams => {
    return {
      basePricePerNight: 30000,
      baseOccupancyRate: 100, // 東京都心6区・民泊人気エリア
      isSuperhost: false,
      isNewHost: false,
      airbnbChargeRate: 15,
      managementCompanyRate: 20,
      monthlyCleaningFee: 30000,
      monthlyUtilityFee: 15000,
      monthlySuppliesExpense: 10000,
      ...initialParams // 初期パラメータをマージ
    }
  }

  const [params, setParams] = useState<MinpakuParams>(initParams())
  const [result, setResult] = useState<MinpakuResult | null>(null)
  const [loading, setLoading] = useState(false)

  const updateResult = (newParams: MinpakuParams) => {
    setLoading(true)
    // シミュレート非同期処理
    setTimeout(() => {
      const calculatedResult = calculateMinpakuIncome(newParams)
      setResult(calculatedResult)
      setLoading(false)
    }, 300)
  }

  useEffect(() => {
    updateResult(params)
  }, [params])

  const handleExportPdf = async () => {
    // PDF出力機能（後で実装）
    console.log("🔥 PDF出力機能を実装予定")
  }

  return (
    <div className={`flex flex-col gap-2 ${className}`}>
      {showTitle && (
        <div className="border-b border-neutral-200 p-2 flex flex-row items-center justify-between">
          <h2 className="text-xl font-semibold text-neutral-900">
            民泊収益試算
          </h2>
        
          <div className="flex flex-row gap-2 text-xs text-gray-500">
            <Button size="sm" variant="outline" onClick={handleExportPdf}>
              <Printer className="w-4 h-4" /> PDF
            </Button>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 左側：入力フォーム */}
        <div className="flex flex-col gap-2 p-2">
          <TooltipProvider>
            <MinpakuForm params={params} setParams={setParams} />
          </TooltipProvider>
        </div>

        {/* 右側：結果テーブル */}
        <div className="flex flex-col m-2 mt-0 p-4 rounded-lg bg-neutral-50">
          <h2 className="font-bold text-lg text-center border-b border-neutral-200 pb-2">
            収益シミュレーション結果
          </h2>

          <div className="flex-1 mt-4">
            {loading ? (
              <div className="flex justify-center items-center h-full">
                <Loader2 className="animate-spin w-10 h-10" />
              </div>
            ) : (
              result && <MinpakuResultTable result={result} />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
