import { ColumnDef } from "@tanstack/react-table"
import { MonthlyData } from "./minpakuCalculations"

const formatNumber = (value: number) => {
  return value.toLocaleString('ja-JP')
}

const formatPercent = (value: number) => {
  return `${value.toFixed(1)}%`
}

export const minpakuDataTableColumns: ColumnDef<MonthlyData>[] = [
  {
    accessorKey: "month",
    header: "月",
    cell: ({ row }) => {
      return <div className="text-center font-medium">{row.original.month}</div>
    },
  },
  {
    accessorKey: "occupancyRate",
    header: "稼働率(%)",
    cell: ({ row }) => {
      return <div className="text-right">{formatPercent(row.original.occupancyRate)}</div>
    },
  },
  {
    accessorKey: "priceMultiplier",
    header: "単価倍率(%)",
    cell: ({ row }) => {
      return <div className="text-right">{formatPercent(row.original.priceMultiplier * 100)}</div>
    },
  },
  {
    accessorKey: "nightsBooked",
    header: "宿泊日数",
    cell: ({ row }) => {
      return <div className="text-right">{row.original.nightsBooked}</div>
    },
  },
  {
    accessorKey: "adjustedPricePerNight",
    header: "調整後単価",
    cell: ({ row }) => {
      return <div className="text-right">¥{formatNumber(row.original.adjustedPricePerNight)}</div>
    },
  },
  {
    accessorKey: "revPar",
    header: "RevPar",
    cell: ({ row }) => {
      const daysInMonth = new Date(2024, parseInt(row.original.month.replace('月', '')) - 1 + 1, 0).getDate()
      const revPar = row.original.totalRevenue / daysInMonth
      return <div className="text-right">¥{formatNumber(Math.round(revPar))}</div>
    },
  },
  {
    accessorKey: "netIncome",
    header: "月収入",
    cell: ({ row }) => {
      return (
        <div className={`text-right font-semibold ${row.original.netIncome >= 0 ? "text-green-600" : "text-red-600"}`}>
          ¥{formatNumber(row.original.netIncome)}
        </div>
      )
    },
  },
  {
    accessorKey: "comment",
    header: "コメント",
    cell: ({ row }) => {
      return <div className="text-left text-gray-600">{row.original.comment}</div>
    },
  },
]
