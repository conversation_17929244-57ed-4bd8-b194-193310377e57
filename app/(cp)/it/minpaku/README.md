# 民泊収益シミュレーション機能

## 概要

Airbnb/民泊運営の収益性を詳細にシミュレーションできる機能です。月別の稼働率変動、季節調整、各種手数料を考慮した現実的な収益予測を提供します。

## 機能特徴

### 📊 詳細な収益計算

- 月別稼働率の自動調整（新宿・渋谷ベンチマーク）
- 季節料金調整オプション
- 各種手数料・固定費の詳細計算

### 🏠 ホストステータス対応

- **スーパーホスト**: 稼働率15%向上
- **新規ホスト**: 稼働率30%低下

### 💰 包括的な費用計算

- Airbnb手数料（デフォルト15%）
- 管理会社手数料（デフォルト20%）
- 清掃費・光熱費・消耗品費
- 保険料・メンテナンス費
- チェックイン手数料

## 使用方法

### スタンドアロンページ

```
/it/cf?tab=minpaku
```

**注意**: `/it/minpaku` は `/it/cf?tab=minpaku` にリダイレクトされます。

### 物件詳細ページ内での使用

物件詳細ページ（`/ex/search/[id]`）にも自動的に埋め込まれ、物件タイプに応じた初期料金設定が適用されます。

### プログラム内での使用

```tsx
import MinpakuSimulator from "@/app/(cp)/it/minpaku/MinpakuSimulator";

<MinpakuSimulator
  initialParams={{
    basePricePerNight: 12000,
    isSuperhost: true,
  }}
  showTitle={true}
/>;
```

## 月別稼働率調整

新宿・渋谷エリアをベンチマークとした月別調整係数：

| 月   | 調整係数 | 理由         |
| ---- | -------- | ------------ |
| 1月  | 0.75     | 正月         |
| 2月  | 0.70     | 寒い時期     |
| 3月  | 0.85     | 花見シーズン |
| 4月  | 1.00     | Easter       |
| 5月  | 0.86     | GW           |
| 6月  | 0.80     | 梅雨         |
| 7月  | 0.90     | 夏季         |
| 8月  | 0.95     | 夏季・お盆   |
| 9月  | 0.80     | 台風シーズン |
| 10月 | 0.80     | 秋           |
| 11月 | 0.75     | 紅葉         |
| 12月 | 0.81     | クリスマス   |

## 月別単価倍率調整

市場データに基づく月別の価格調整係数：

| 月   | 単価倍率 | 調整後単価計算  |
| ---- | -------- | --------------- |
| 1月  | 85%      | 基本料金 × 0.85 |
| 2月  | 110%     | 基本料金 × 1.10 |
| 3月  | 130%     | 基本料金 × 1.30 |
| 4月  | 100%     | 基本料金 × 1.00 |
| 5月  | 100%     | 基本料金 × 1.00 |
| 6月  | 100%     | 基本料金 × 1.00 |
| 7月  | 130%     | 基本料金 × 1.30 |
| 8月  | 130%     | 基本料金 × 1.30 |
| 9月  | 110%     | 基本料金 × 1.10 |
| 10月 | 100%     | 基本料金 × 1.00 |
| 11月 | 100%     | 基本料金 × 1.00 |
| 12月 | 90%      | 基本料金 × 0.90 |

**計算式**: 宿泊単価 = 単価倍率(%) × 基本料金

## 技術仕様

### ファイル構成

```
app/(cp)/it/minpaku/
├── page.tsx                    # スタンドアロンページ
├── MinpakuSection.tsx          # メインセクション
├── MinpakuSimulator.tsx        # 再利用可能コンポーネント
├── MinpakuForm.tsx            # 入力フォーム
├── MinpakuResultTable.tsx     # 結果表示テーブル
├── minpakuCalculations.ts     # 計算ロジック
└── README.md                  # このファイル
```

### 主要インターフェース

- `MinpakuParams`: 入力パラメータ
- `MinpakuResult`: 計算結果
- `MonthlyData`: 月別データ
- `YearlyTotals`: 年間合計

## 今後の拡張予定

- PDF出力機能の実装
- より詳細な地域別調整係数
- 競合分析機能
- 動的料金最適化提案
