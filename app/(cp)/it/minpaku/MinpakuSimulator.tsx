"use client"

import { useState, useEffect } from "react"
import MinpakuForm from "./MinpakuForm"
import MinpakuResultTable from "./MinpakuResultTable"
import { MinpakuParams, MinpakuResult, calculateMinpakuIncome } from "./minpakuCalculations"
import { Loader2 } from "lucide-react"
import { TooltipProvider } from "@/components/ui/tooltip"

interface MinpakuSimulatorProps {
  initialParams?: Partial<MinpakuParams>
  showTitle?: boolean
  className?: string
}

export default function MinpakuSimulator({ 
  initialParams = {}, 
  showTitle = true,
  className = ""
}: MinpakuSimulatorProps) {
  const getInitParams = (): MinpakuParams => {
    return {
      basePricePerNight: 30000,
      baseOccupancyRate: 100,
      isSuperhost: false,
      isNewHost: false,
      airbnbChargeRate: 15,
      managementCompanyRate: 20,
      monthlyCleaningFee: 30000,
      monthlyUtilityFee: 15000,
      monthlySuppliesExpense: 10000,
      monthlyInternetFee: 5000,
      propertyCost: 10000, // 10000万円  
      acquisitionCostRate: 7,
      reformCost: 0,
      ...initialParams // 初期パラメータをマージ
    }
  }

  const [params, setParams] = useState<MinpakuParams>(getInitParams())
  const [result, setResult] = useState<MinpakuResult | null>(null)
  const [loading, setLoading] = useState(false)

  const updateResult = (newParams: MinpakuParams) => {
    setLoading(true)
    // シミュレート非同期処理
    setTimeout(() => {
      const calculatedResult = calculateMinpakuIncome(newParams)
      setResult(calculatedResult)
      setLoading(false)
    }, 300)
  }

  useEffect(() => {
    updateResult(params)
  }, [params])

  return (
    <div className={`flex flex-col gap-2 ${className}`}>
      {showTitle && (
        <div className="border-b border-neutral-200 p-2">
          <h2 className="text-xl font-semibold text-neutral-900">民泊収益試算</h2>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* 左側：入力フォーム */}
        <div className="flex flex-col gap-2 p-2">
          <TooltipProvider>
            <MinpakuForm params={params} setParams={setParams} />
          </TooltipProvider>
        </div>

        {/* 右側：結果テーブル */}
        <div className="flex flex-col m-2 mt-0 p-4 rounded-lg bg-neutral-50">
          <h2 className="font-bold text-lg text-center border-b border-neutral-200 pb-2">
            収益試算結果
          </h2>

          <div className="flex-1 mt-4">
            {loading ? (
              <div className="flex justify-center items-center h-full">
                <Loader2 className="animate-spin w-10 h-10" />
              </div>
            ) : (
              result && <MinpakuResultTable result={result} />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
