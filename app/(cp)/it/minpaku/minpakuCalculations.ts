export interface MinpakuParams {
  basePricePerNight: number
  baseOccupancyRate: number // 新宿・渋谷ベンチマーク（100%）
  isSuperhost: boolean
  isNewHost: boolean
  airbnbChargeRate: number // パーセンテージ
  managementCompanyRate: number // パーセンテージ
  monthlyCleaningFee: number
  monthlyUtilityFee: number
  monthlySuppliesExpense: number
  monthlyInternetFee?: number // インターネット料金（旧保険料）
  // Property related costs
  propertyCost?: number // 物件価格（万円）
  acquisitionCostRate?: number // 取得費用率（%）
  reformCost?: number // リフォーム費用（万円）
}

export interface MinpakuResult {
  monthlyData: MonthlyData[]
  yearlyTotals: YearlyTotals
}

export interface MonthlyData {
  month: string
  occupancyRate: number
  priceMultiplier: number
  adjustedPricePerNight: number
  nightsBooked: number
  grossRevenue: number
  checkInRevenue: number
  totalRevenue: number
  airbnbFee: number
  managementFee: number
  cleaningFee: number
  utilityFee: number
  suppliesExpense: number
  internetFee: number
  netIncome: number
  comment: string
}

export interface YearlyTotals {
  totalGrossRevenue: number
  totalCheckInRevenue: number
  totalRevenue: number
  totalAirbnbFee: number
  totalManagementFee: number
  totalCleaningFee: number
  totalUtilityFee: number
  totalSuppliesExpense: number
  totalInternetFee: number
  totalNetIncome: number
  averageOccupancyRate: number
  // Property investment metrics
  totalPropertyCost: number
  roi: number // Return on Investment (%)
  noi: number // Net Operating Income (%)
}

// 月別の稼働率調整（新宿・渋谷ベンチマーク）
const monthlyOccupancyMultipliers = {
  1: 0.75, // 1月: 正月
  2: 0.70, // 2月: 寒い
  3: 0.85, // 3月: 花見
  4: 0.75, // 4月: Easter
  5: 0.85, // 5月: GW
  6: 0.80, // 6月: 梅雨
  7: 0.90, // 7月: 夏季
  8: 0.95, // 8月: 夏季・お盆
  9: 0.80, // 9月: 台風
  10: 0.80, // 10月: 秋
  11: 0.75, // 11月: 紅葉
  12: 0.80, // 12月: クリスマス
}

// 月別の単価倍率（チャートデータに基づく）
const monthlyPriceMultipliers = {
  1: 0.85, // 1月: 85%
  2: 0.85, // 2月: 110%
  3: 1.10, // 3月: 130%
  4: 1.00, // 4月: 100%
  5: 1.00, // 5月: 100%
  6: 1.00, // 6月: 100%
  7: 1.30, // 7月: 130%
  8: 1.30, // 8月: 130%
  9: 1.10, // 9月: 110%
  10: 1.00, // 10月: 100%
  11: 1.00, // 11月: 100%
  12: 0.90, // 12月: 90%
}

const monthNames = [
  "1月", "2月", "3月", "4月", "5月", "6月",
  "7月", "8月", "9月", "10月", "11月", "12月"
]

const monthComments = {
  1: "元旦、正月",
  2: "寒い時期",
  3: "花見",
  4: "Easter",
  5: "GW",
  6: "梅雨",
  7: "夏季、夏祭",
  8: "夏季、お盆",
  9: "台風",
  10: "秋",
  11: "紅葉",
  12: "クリスマス"
}

export function calculateMinpakuIncome(params: MinpakuParams): MinpakuResult {
  const monthlyData: MonthlyData[] = []
  
  for (let month = 1; month <= 12; month++) {
    // 基本稼働率の調整
    let adjustedOccupancyRate = params.baseOccupancyRate * monthlyOccupancyMultipliers[month as keyof typeof monthlyOccupancyMultipliers]
    
    // スーパーホストボーナス
    if (params.isSuperhost) {
      adjustedOccupancyRate *= 1.15 // 15%アップ
    }
    
    // 新規ホストペナルティ
    if (params.isNewHost) {
      adjustedOccupancyRate *= 0.7 // 30%ダウン
    }
    
    // 100%を超えないように制限
    adjustedOccupancyRate = Math.min(adjustedOccupancyRate, 100)
    
    // その月の日数
    const daysInMonth = new Date(2024, month, 0).getDate()
    const nightsBooked = Math.round((adjustedOccupancyRate / 100) * daysInMonth)
    
    // 単価倍率による料金調整
    let adjustedPricePerNight = params.basePricePerNight * monthlyPriceMultipliers[month as keyof typeof monthlyPriceMultipliers]

    // 収入計算
    const grossRevenue = nightsBooked * adjustedPricePerNight
    const checkInRevenue = 0 // チェックイン手数料を削除
    const totalRevenue = grossRevenue + checkInRevenue

    const airbnbFee = totalRevenue * (params.airbnbChargeRate / 100)
    const managementFee = totalRevenue * (params.managementCompanyRate / 100)

    // 固定費
    const cleaningFee = params.monthlyCleaningFee
    const utilityFee = params.monthlyUtilityFee
    const suppliesExpense = params.monthlySuppliesExpense
    const internetFee = params.monthlyInternetFee || 0

    // 純収入
    const netIncome = totalRevenue - airbnbFee - managementFee - cleaningFee - utilityFee - suppliesExpense - internetFee
    
    monthlyData.push({
      month: monthNames[month - 1],
      occupancyRate: Math.round(adjustedOccupancyRate * 10) / 10,
      priceMultiplier: monthlyPriceMultipliers[month as keyof typeof monthlyPriceMultipliers],
      adjustedPricePerNight: Math.round(adjustedPricePerNight),
      nightsBooked,
      grossRevenue,
      checkInRevenue,
      totalRevenue,
      airbnbFee,
      managementFee,
      cleaningFee,
      utilityFee,
      suppliesExpense,
      internetFee,
      netIncome,
      comment: monthComments[month as keyof typeof monthComments]
    })
  }
  
  // 年間合計の計算
  const totalNetIncome = monthlyData.reduce((sum, data) => sum + data.netIncome, 0)
  const totalRevenue = monthlyData.reduce((sum, data) => sum + data.totalRevenue, 0)

  // Property investment calculations
  const propertyCostYen = (params.propertyCost || 10000) * 10000 // Convert 万円 to yen
  const acquisitionCost = propertyCostYen * ((params.acquisitionCostRate || 7) / 100)
  const reformCostYen = (params.reformCost || 0) * 10000 // Convert 万円 to yen
  const totalPropertyCost = propertyCostYen + acquisitionCost + reformCostYen

  // ROI = Total Revenue / Total Cost
  const roi = totalPropertyCost > 0 ? (totalRevenue / totalPropertyCost) * 100 : 0

  // NOI = Net Income / Total Cost
  const noi = totalPropertyCost > 0 ? (totalNetIncome / totalPropertyCost) * 100 : 0

  const yearlyTotals: YearlyTotals = {
    totalGrossRevenue: monthlyData.reduce((sum, data) => sum + data.grossRevenue, 0),
    totalCheckInRevenue: monthlyData.reduce((sum, data) => sum + data.checkInRevenue, 0),
    totalRevenue,
    totalAirbnbFee: monthlyData.reduce((sum, data) => sum + data.airbnbFee, 0),
    totalManagementFee: monthlyData.reduce((sum, data) => sum + data.managementFee, 0),
    totalCleaningFee: monthlyData.reduce((sum, data) => sum + data.cleaningFee, 0),
    totalUtilityFee: monthlyData.reduce((sum, data) => sum + data.utilityFee, 0),
    totalSuppliesExpense: monthlyData.reduce((sum, data) => sum + data.suppliesExpense, 0),
    totalInternetFee: monthlyData.reduce((sum, data) => sum + data.internetFee, 0),
    totalNetIncome,
    averageOccupancyRate: monthlyData.reduce((sum, data) => sum + data.occupancyRate, 0) / 12,
    totalPropertyCost,
    roi,
    noi
  }
  
  return {
    monthlyData,
    yearlyTotals
  }
}
