"use client";

import { useState, useMemo } from "react";
import { searchProRegistrationRecords } from "@/actions/proRegistrationRecord";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import dayjs from "dayjs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const areaCodeMapper = {
  13114: "中野区",
  13104: "新宿区",
  13112: "世田谷区",
  13115: "杉並区",
  13113: "渋谷区",
  13109: "品川区",
  13110: "目黒区",
  13103: "港区",
};

const propertyTypeMapper = {
  MANSION: "区建",
  BUILDING_HOUSE: "建物",
  LAND: "土地",
};

const columns = [
  {
    accessorKey: "recordDate",
    header: "登記日",
    cell: ({ row }: any) => row.original.recordDate ? dayjs(row.original.recordDate).format("YYYY/MM/DD") : "",
  },
  {
    accessorKey: "areaCode",
    header: "エリアコード",
    cell: ({ row }: any) => areaCodeMapper[row.original.areaCode as keyof typeof areaCodeMapper],
  },
  {
    accessorKey: "postalCode",
    header: "郵便番号",
  },
  {
    accessorKey: "propertyIsNew",
    header: "新築",
    cell: ({ row }: any) => row.original.propertyIsNew ? "新築" : "中古",
  },
  {
    accessorKey: "propertyType",
    header: "物件タイプ",
    cell: ({ row }: any) => propertyTypeMapper[row.original.propertyType as keyof typeof propertyTypeMapper],
  },
  {
    accessorKey: "registrationType",
    header: "登記種別",
  },
  {
    accessorKey: "addressChiban",
    header: "地番住所",
  },
];

export default function UketsukePage() {
  const [keyword, setKeyword] = useState("");
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<any[]>([]);
  const [filterRegistrationType, setFilterRegistrationType] = useState("all");
  const [filterAreaCode, setFilterAreaCode] = useState("all");

  const handleSearch = async () => {
    setLoading(true);
    const res = await searchProRegistrationRecords({ keyword });
    setRecords(res);
    setLoading(false);
  };

  // 生成筛选选项
  const registrationTypeOptions = useMemo(() => {
    const set = new Set(records.map(r => r.registrationType).filter(Boolean));
    return Array.from(set);
  }, [records]);

  const areaCodeOptions = useMemo(() => {
    const set = new Set(records.map(r => r.areaCode).filter(Boolean));
    return Array.from(set);
  }, [records]);

  // 前端筛选
  const filteredRecords = useMemo(() => {
    return records.filter(r => {
      if (filterRegistrationType !== "all" && r.registrationType !== filterRegistrationType) return false;
      if (filterAreaCode !== "all" && String(r.areaCode) !== String(filterAreaCode)) return false;
      return true;
    });
  }, [records, filterRegistrationType, filterAreaCode]);

  return (
    <div className="p-4">
      <div className="flex gap-2 mb-4 flex-1 justify-start items-end">
        <Input
          placeholder="地番住所で検索"
          value={keyword}
          onChange={e => setKeyword(e.target.value)}
          onKeyDown={e => { if (e.key === "Enter") handleSearch(); }}
          className="w-64 flex-1"
        />
        <Button onClick={handleSearch} disabled={loading}>
          検索
        </Button>
      </div>

      {/* 筛选器和总数 */}
      <div className="flex flex-col flex-wrap gap-4 items-start mb-2 justify-start">

        <div className="flex flex-row gap-4">
          <div className="flex flex-row gap-2 justify-start items-center">
            <Select
              value={filterRegistrationType}
              onValueChange={setFilterRegistrationType}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="登記種別: すべて" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">登記種別: すべて</SelectItem>
                {registrationTypeOptions.map((type: string) => (
                  <SelectItem key={type} value={type}>{type}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-row gap-2 justify-start items-center">
            <Select
              value={filterAreaCode}
              onValueChange={setFilterAreaCode}
            >
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="エリア: すべて" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">エリア: すべて</SelectItem>
                {areaCodeOptions.map((code: number) => (
                  <SelectItem key={code} value={String(code)}>
                    {areaCodeMapper[code as keyof typeof areaCodeMapper]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div>合計: <span className="font-bold">{filteredRecords.length}</span> 件
        </div>

      </div>

      <DataTable
        columns={columns}
        data={filteredRecords}
        isLoading={loading}
      />
    </div>
  );
}