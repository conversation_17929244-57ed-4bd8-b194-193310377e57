import dayjs from 'dayjs';
import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

interface DataPoint {
  recordDate: string;
  address: string;
  price: number;
  value: number
}

interface StackedBarChartProps {
  data: DataPoint[];
  selectedPeriod: "isoWeek" | "month" | "day";
}

const StackedBarChart: React.FC<StackedBarChartProps> = ({ data, selectedPeriod }) => {
  let uniqKeySet = new Set();

  let processedData = data.map((item) => {
    let uniqueKey = `${item.address}-${item.price}`;
    uniqKeySet.add(uniqueKey);
    return {
      ...item,
      uniqueKey: uniqueKey
    }
  })

  let processed = {} as any;
  processedData.forEach((item) => {
    let recordDate = dayjs(item.recordDate).startOf(selectedPeriod).format("YYYY-MM-DD");

    if (!processed[recordDate]) {
      processed[recordDate] = {
        propertyAddress: item.address,
        recordDate: recordDate,
      };
    }

    let current = processed[recordDate][item.uniqueKey] || 0;
    if (current > 0) {
      processed[recordDate][item.uniqueKey] = current + item.value
      processed[recordDate][item.uniqueKey + "-count"] += 1
    } else {
      processed[recordDate][item.uniqueKey] = item.value
      processed[recordDate][item.uniqueKey + "-count"] = 1
    }
  })

  console.log("processed", processed);

  return (
    <ResponsiveContainer width="100%" height={400}>
      <BarChart data={Object.values(processed)} stackOffset="sign">
        <XAxis
          dataKey="recordDate"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          tickFormatter={(value) => dayjs(value).format("YYYY-MM-DD")}
          fontSize={12} // 设置x轴标签字体大小
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          fontSize={12} // 设置y轴标签字体大小
        />
        <Tooltip
          wrapperStyle={{ fontSize: 12 }}
        />
        <Legend
          wrapperStyle={{ fontSize: 12 }} // 设置图例标签字体大小
        />
        {Array.from(uniqKeySet).map((key, index) => (
          <Bar key={index} dataKey={key as string} stackId="a" fill={`#${Math.floor(Math.random() * 16777215).toString(16)}`} />
        ))}
      </BarChart>
    </ResponsiveContainer>
  );
};

export default StackedBarChart; 