"use client"

import { getReinsMetrics, refillEmptyData } from "@/actions/tllReinsMetrics";
import { DataTable } from "@/components/ui/data-table";
import { Separator } from "@/components/ui/separator";
import { TllReinsMetricsProps } from "@/lib/definitions/tllReinsMetrics";
import { useEffect, useState } from "react";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import minMax from "dayjs/plugin/minMax";
dayjs.extend(isoWeek);
dayjs.extend(minMax);
import StackedBarChart from "./BarChart";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectValue, SelectTrigger, SelectContent, SelectItem } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

const fillMissingDates = (data: any[], selectedPeriod: "isoWeek" | "month" | "day") => {
  if (selectedPeriod !== "day") {
    return data;
  }

  // 获取数据中的日期范围
  const dates = data.map(d => dayjs(d.recordDate));
  const minDate = dayjs.min(dates);
  const maxDate = dayjs.max(dates);

  if (!minDate || !maxDate) {
    return data;
  }

  // 生成日期范围内的所有日期（降序排列）
  const allDates = Array.from(
    { length: maxDate.diff(minDate, 'day') + 1 },
    (_, i) => maxDate.subtract(i, 'day').format('YYYY-MM-DD')
  );

  // 创建日期到数据的映射
  const dateToData = new Map(
    data.map(d => [dayjs(d.recordDate).format('YYYY-MM-DD'), d])
  );

  // 填充缺失的日期
  const filledData = allDates.map(date => {
    if (dateToData.has(date)) {
      return dateToData.get(date);
    }

    // 获取该地址的最后一条记录作为基础
    const lastRecord = data
      .filter(d => d.address === dateToData.values().next().value?.address)
      .sort((a, b) => dayjs(b.recordDate).valueOf() - dayjs(a.recordDate).valueOf())[0];

    if (!lastRecord) {
      return null;
    }

    return {
      ...lastRecord,
      recordDate: date,
      downloadDetailsCount: 0,
      checkDetailsCount: 0,
      value: 0,
    };
  }).filter(Boolean);

  return filledData;
};

export default function SellPage() {
  const [reinsMetrics, setReinsMetrics] = useState<TllReinsMetricsProps[]>([]);
  const [selectedTab, setSelectedTab] = useState<"売" | "賃貸">("売");
  const [selectedPeriod, setSelectedPeriod] = useState<"isoWeek" | "month" | "day">("isoWeek");
  const [uniqPropertyAddresss, setUniqPropertyAddresss] = useState<{ address: string, type: string }[]>([]);
  const [selectedPropertyAddress, setSelectedPropertyAddress] = useState<string>("all");
  const [selectedViewType, setSelectedViewType] = useState<"download" | "check" | "both">("both");
  const [isRefilling, setIsRefilling] = useState<boolean>(false);

  let columns = [
    {
      header: "日付",
      accessorKey: "recordDate",
      cell: ({ row }: { row: { original: { recordDate?: string } } }) => row.original.recordDate ? dayjs(row.original.recordDate).format("YYYY-MM-DD") : "",
    },
    {
      header: "REINS ID",
      accessorKey: "reinsId",
    },
    {
      header: "物件種別",
      accessorKey: "listingType",
    },
    {
      header: "物件サブタイプ",
      accessorKey: "listingSubtype",
    },
    {
      header: "住所",
      accessorKey: "address",
    },
    {
      header: "部屋",
      accessorKey: "roomNumber",
    },
    {
      header: "価格",
      accessorKey: "price",
    },
    {
      header: "図面引合",
      accessorKey: "downloadDetailsCount",
    },
    {
      header: "物件引合",
      accessorKey: "checkDetailsCount",
    },
  ]

  const setUniqPropertyAddresssFunc = (data: TllReinsMetricsProps[], selectedTab: "売" | "賃貸") => {
    let uniqAddressSet = {} as any;
    data.filter((metric) => metric.listingSubtype?.includes(selectedTab)).forEach((metric: TllReinsMetricsProps) => {
      const addressKey = metric.address; // ?.slice(0, 10) + "-" + metric.listingSubtype;
      if (addressKey && !uniqAddressSet[addressKey]) {
        uniqAddressSet[addressKey] = { address: addressKey, type: metric.listingSubtype ?? "" };
      }
    });

    setUniqPropertyAddresss(Object.values(uniqAddressSet));
  }

  const fetchData = async () => {
    const response = await getReinsMetrics();
    if (response.success) {
      setReinsMetrics(response.data);

      console.log(response.data);
      setUniqPropertyAddresssFunc(response.data, selectedTab);
    }
  }

  useEffect(() => {
    fetchData();
  }, []);

  const renderTab = () => (
    <div className="w-full text-right flex flex-row justify-end items-center gap-2">
      <Tabs value={selectedViewType} onValueChange={(value) => {
        setSelectedViewType(value as "download" | "check" | "both");
      }}>
        <TabsList>
          <TabsTrigger value="download">図面引合</TabsTrigger>
          <TabsTrigger value="check">物件引合</TabsTrigger>
          <TabsTrigger value="both">両方</TabsTrigger>
        </TabsList>
      </Tabs>

      <Tabs value={selectedPeriod} onValueChange={(value) => {
        setSelectedPeriod(value as "isoWeek" | "month" | "day");
      }}>
        <TabsList>
          <TabsTrigger value="month">月</TabsTrigger>
          <TabsTrigger value="isoWeek">週</TabsTrigger>
          <TabsTrigger value="day">日</TabsTrigger>
        </TabsList>
      </Tabs>

      <Select value={selectedPropertyAddress} onValueChange={(value) => {
        setSelectedPropertyAddress(value);
      }}>
        <SelectTrigger className="w-48">
          <SelectValue placeholder="物件を選ぶ" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">全て</SelectItem>
          {uniqPropertyAddresss.map((address, index) => (
            <SelectItem key={index} value={address.address}>{address.address} [{address.type}]</SelectItem>
          ))}
        </SelectContent>
      </Select>
      {/* <Tabs value={selectedPeriod} onValueChange={(value) => {
        setSelectedPeriod(value as "week" | "month" | "day");
      }}>
        <TabsList>
          {uniqPropertyAddresss.filter(r => r.includes(selectedTab)).map((address, index) => (
            <TabsTrigger key={index} value={address}>{address.slice(3, 10)}</TabsTrigger>
          ))}
        </TabsList>
      </Tabs> */}
    </div>
  )

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="販売タイムライン">
        販売タイムライン
      </h1>

      <div className="flex flex-row gap-2">
        <Button variant="outline" disabled={!selectedPropertyAddress || isRefilling} onClick={() => {
          setIsRefilling(true);
          refillEmptyData({ address: selectedPropertyAddress }).then(() => {
            setIsRefilling(false);
            fetchData();
          });
        }}>
          データを補完
        </Button>
      </div>
    </div>

    <Separator className="" />

    <div className="px-4 py-2 sticky top-0 bg-white z-10 w-full border-b border-gray-200">
      <Tabs value={selectedTab} onValueChange={(value) => {
        setSelectedTab(value as "売" | "賃貸");
        setUniqPropertyAddresssFunc(reinsMetrics, value as "売" | "賃貸");
      }}>
        <TabsList>
          <TabsTrigger value="売">売買</TabsTrigger>
          <TabsTrigger value="賃貸">賃貸</TabsTrigger>
        </TabsList>
      </Tabs>
    </div>

    <Separator className="" />

    <div className="px-4 py-2 flex flex-col gap-4">
      {selectedTab === "賃貸" && (
        <div className="flex flex-col justify-start items-start gap-4">

          <div className="w-full">
            {renderTab()}

            <StackedBarChart
              selectedPeriod={selectedPeriod}
              data={fillMissingDates(
                reinsMetrics
                  .filter((metric) =>
                    metric.listingSubtype?.includes("賃貸") &&
                    (selectedPropertyAddress === "all" || metric.address === selectedPropertyAddress)
                  )
                  .map((metric) => ({
                    recordDate: metric.recordDate ?? "",
                    address: metric.address ?? "",
                    price: metric.price ?? 0,
                    value: selectedViewType === "both"
                      ? (metric.downloadDetailsCount ?? 0) + (metric.checkDetailsCount ?? 0)
                      : selectedViewType === "download"
                        ? metric.downloadDetailsCount ?? 0
                        : metric.checkDetailsCount ?? 0,
                  })),
                selectedPeriod
              )}
            />
          </div>

          <div className="w-full">
            <DataTable columns={columns} data={reinsMetrics.filter((metric) => metric.listingSubtype?.includes("賃貸") && (selectedPropertyAddress === "all" || metric.address === selectedPropertyAddress))} />
          </div>
        </div>
      )}

      {selectedTab === "売" && (
        <div className="flex flex-col justify-start items-start gap-4">
          <div className="w-full">
            {renderTab()}

            <StackedBarChart
              selectedPeriod={selectedPeriod}
              data={fillMissingDates(
                reinsMetrics
                  .filter((metric) =>
                    !metric.listingSubtype?.includes("賃貸") &&
                    (selectedPropertyAddress === "all" || metric.address === selectedPropertyAddress)
                  )
                  .map((metric) => ({
                    recordDate: metric.recordDate ?? "",
                    address: metric.address ?? "",
                    price: metric.price ?? 0,
                    value: selectedViewType === "both"
                      ? (metric.downloadDetailsCount ?? 0) + (metric.checkDetailsCount ?? 0)
                      : selectedViewType === "download"
                        ? metric.downloadDetailsCount ?? 0
                        : metric.checkDetailsCount ?? 0,
                  })),
                selectedPeriod
              )}
            />
          </div>

          <div className="w-full">
            <DataTable columns={columns} data={reinsMetrics.filter((metric) => !metric.listingSubtype?.includes("賃貸") && (selectedPropertyAddress === "all" || metric.address === selectedPropertyAddress))} />
          </div>
        </div>
      )}
    </div>
  </div>
}