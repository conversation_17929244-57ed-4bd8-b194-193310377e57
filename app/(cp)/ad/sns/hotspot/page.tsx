"use client"

import { allHotspots } from "@/lib/constants/allHotspots";
import { useState, useEffect } from "react";
import Link from "next/link";
import { getNewsFromUrlAction } from "@/actions/aiHotspot";
import { Button } from "@/components/ui/button";

import { Spinner } from "@/components/ui/spinner";
import { DataTable } from "@/components/ui/data-table";
import { getAiNewses, createAiNewses } from "@/actions/aiNews";
import { toast } from "@/hooks/use-toast";
import { ActionResponse, AiNewsProps } from "@/lib/definitions";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import dayjs from "dayjs";
import { Link as LinkIcon, Info } from "lucide-react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

export default function Hotspot() {
  const [news, setNews] = useState<AiNewsProps[]>([]);
  const [loading_RAKUMACHI, setLoading_RAKUMACHI] = useState(false);
  const [loading_KENBIYA, setLoading_KENBIYA] = useState(false);
  const [loading_YAHOO_FUDOUSAN, setLoading_YAHOO_FUDOUSAN] = useState(false);
  const [loading_NIKKEI_FUDOUSAN, setLoading_NIKKEI_FUDOUSAN] = useState(false);
  const [loading_TOYOKEIZAI_FUDOUSAN, setLoading_TOYOKEIZAI_FUDOUSAN] = useState(false);
  const [selectedSources, setSelectedSources] = useState<string[]>(["ALL"]);
  const [selectedSort, setSelectedSort] = useState<string>("CREATE_AT");

  useEffect(() => {
    const fetchNews = async () => {
      const news = await getAiNewses() as ActionResponse<AiNewsProps[]>;
      setNews(news.data);
    };
    fetchNews();
  }, []);

  const setLoading = (source: string, value: boolean) => {
    switch (source) {
      case "RAKUMACHI":
        setLoading_RAKUMACHI(value);
        break;
      case "KENBIYA":
        setLoading_KENBIYA(value);
        break;
      case "YAHOO_FUDOUSAN":
        setLoading_YAHOO_FUDOUSAN(value);
        break;
      case "NIKKEI_FUDOUSAN":
        setLoading_NIKKEI_FUDOUSAN(value);
        break;
      case "TOYOKEIZAI_FUDOUSAN":
        setLoading_TOYOKEIZAI_FUDOUSAN(value);
        break;
    }
  }

  const renderLoading = (site: any) => {
    switch (site.source) {
      case "RAKUMACHI":
        return loading_RAKUMACHI ? <Spinner /> : site.name + "を更新";
      case "KENBIYA":
        return loading_KENBIYA ? <Spinner /> : site.name + "を更新";
      case "YAHOO_FUDOUSAN":
        return loading_YAHOO_FUDOUSAN ? <Spinner /> : site.name + "を更新";
      case "NIKKEI_FUDOUSAN":
        return loading_NIKKEI_FUDOUSAN ? <Spinner /> : site.name + "を更新";
      case "TOYOKEIZAI_FUDOUSAN":
        return loading_TOYOKEIZAI_FUDOUSAN ? <Spinner /> : site.name + "を更新";
    }
  }

  const getNews = (source: string, url: string) => {
    try {
      setLoading(source, true);

      getNewsFromUrlAction({ url }).then((res) => {
        if (res.success) {
          const padded = res.data.map((item: any) => ({ ...item, source, releaseDate: new Date(item.releaseDate) }));
          console.log('🔥padded', padded);

          createAiNewses(padded).then((news) => {
            toast({
              title: "ニュースをSaveしました",
              description: `${news.data.length || 0}/${res.data.length} News added`,
            });

            getAiNewses().then((updatedNews) => {
              setNews(updatedNews.data);
            });
            setLoading(source, false);
          });
        } else {
          toast({
            title: "エラー",
            description: "ニュースをSaveしました",
          });
          setLoading(source, false);
        }
      });
    } catch (error) {
      console.error('🔥error', error);
      toast({
        title: "エラー",
        description: "ニュースをSaveしました",
      });
    }
  }

  const columns = [
    {
      header: "SOURCE",
      accessorKey: "source",
      cell: ({ row }: { row: any }) => (
        <Badge variant="outline" className="text-sm">{row.original.source.slice(0, 4)}</Badge>
      ),
    },
    {
      header: selectedSort === "CREATE_AT" ? "追加日" : "リリース日",
      accessorKey: "releaseDate",
      cell: ({ row }: { row: any }) => selectedSort === "CREATE_AT" ?
        <div>
          <div>{dayjs(row.original.createdAt).format("YYYY-MM-DD")}</div>
          <div className="text-xs text-gray-500">リリース:
            <br />
            {dayjs(row.original.releaseDate).format("YYYY-MM-DD")}</div>
        </div> :
        <div>
          <div>{dayjs(row.original.releaseDate).format("YYYY-MM-DD")}</div>
          {/* <div className="text-xs text-gray-500">追加: {dayjs(row.original.createdAt).format("YYYY-MM-DD")}</div> */}
        </div>
    },
    {
      header: "URL",
      cell: ({ row }: { row: any }) => (
        row.original.url ? <Link href={row.original.url} target="_blank" className="text-sm text-gray-500 underline">
          <LinkIcon className="w-4 h-4" />
        </Link> : null
      ),
    },
    {
      header: <div className="relative group flex flex-row items-center">
        <div className="flex flex-row gap-2">
          推薦
        </div>
        <Info className="w-4 h-4" onClick={() => {
          toast({
            title: "推荐等级",
            description: <>
              **recommendationLevel** 1-5, 5为最高, 评分规则如下：
              <ul>
                <li>5️⃣ = 非常适合中国 SNS，话题热门，易引起讨论，适合改写成爆款文章</li>
                <li>4️⃣ = 适合 SNS，内容有趣，可能有爆款潜力</li>
                <li>3️⃣ = 一般文章，可能适合某些用户，但传播性一般</li>
                <li>2️⃣ = 内容较冷门，传播性差，不适合作为 SNS 爆款</li>
                <li>1️⃣ = 话题枯燥，阅读价值低，不适合 SNS 传播</li>
              </ul>
            </>,
          });
        }} />
      </div>,
      accessorKey: "recommendationLevel",
      cell: ({ row }: { row: any }) => {
        const level = row.original.recommendationLevel;
        const color = level === 5 ? 'red' : 'coal';
        return (
          <div style={{ color }} className={`text-sm ${level >= 5 ? 'font-bold' : 'text-neutral-500'}`}>{level}</div>
        );
      },
    },
    {
      header: "表題",
      accessorKey: "title",
      cell: ({ row }: { row: any }) => (
        <div>
          <div className="text-sm font-bold">{row.original.title}</div>
          <div className="text-xs text-gray-500">
            <b>中文翻译:</b>
            {row.original.titleCh}
          </div>
          {row.original.descriptionCh && <div className="text-xs text-gray-500">
            <b>描述:</b>
            {row.original.descriptionCh}
          </div>}
          <Separator className="text-neutral-100" />
          {row.original.recommendationReason && <div className="text-xs text-gray-500">
            <b>推荐理由:</b>
            {row.original.recommendationReason}
          </div>}
        </div>
      ),
    },

    {
      header: "写真リンク",
      accessorKey: "imageLink",
      cell: ({ row }: { row: any }) => row.original.imageLink ? <img src={row.original.imageLink} alt={`${row.original.title || 'ホットスポット'}の写真`} className="w-10 h-10" /> : null,
    },
    {
      header: "閲覧数",
      accessorKey: "readingCount",
    },
    {
      header: "コメント数",
      accessorKey: "commentCount",
    },

  ]

  const getLastUpdate = (source: string) => {
    if (news.length === 0) {
      return "N/A";
    }

    if (news.filter(item => item.source === source).length === 0) {
      return null;
    }

    const latest = news.filter(item => item.source === source)?.reduce((latest, current) => {


      return dayjs(current.createdAt).isAfter(dayjs(latest.createdAt)) ? current : latest;
    })?.createdAt ? dayjs(news.filter(item => item.source === source)?.reduce((latest, current) => dayjs(current.createdAt).isAfter(dayjs(latest.createdAt)) ? current : latest).createdAt) : null;

    const hourDiff = dayjs().diff(dayjs(latest), 'hours');
    return latest ? <div>
      <div>最終更新: {dayjs(latest).format("YYYY-MM-DD HH:mm")} </div>
      <span className={`text-xs text-gray-500 ${hourDiff > 12 ? 'text-red-500' : ''}`}>
        ({hourDiff} hours ago)
      </span>
    </div> : "N/A";
  }

  const prepNewsForRendering = (news: AiNewsProps[], minDays: number, maxDays: number) => {
    return news.filter(item => {
      const dateForSort = selectedSort === "CREATE_AT" ? dayjs(item.createdAt).startOf('day') : dayjs(item.releaseDate).startOf('day');
      const diffDays = dayjs().diff(dateForSort, 'day');
      return diffDays >= minDays && diffDays <= maxDays && (selectedSources.includes("ALL") || selectedSources.includes(item.source as string));
    });
  }

  return <div className="p-2">
    <div className="flex flex-col sm:flex-row">
      <div className="flex flex-row sm:flex-col sm:w-[200px] p-2">
        {Object.keys(allHotspots).map((option) => (

          <ul key={option} >
            {allHotspots[option as keyof typeof allHotspots].map((site) => (
              <div key={site.name} className="flex flex-row sm:flex-col gap-2 mb-4 overflow-x-auto">
                <li key={site.name} className="flex flex-row gap-2 items-center">
                  <Button variant="outline" size="sm" className="flex-1" onClick={() => {
                    getNews(site.source, site.url);
                  }}>
                    {renderLoading(site)}
                  </Button>
                  <Link href={site.url} target="_blank" className="text-sm text-gray-500 underline">
                    <LinkIcon className="w-4 h-4" />
                  </Link>
                </li>

                <div className="text-xs text-gray-500">
                  {getLastUpdate(site.source)}
                </div>
              </div>
            ))}
          </ul>
        ))}
      </div >

      <div className="w-full sm:w-[calc(100%-200px)] p-2">
        <div className="flex flex-row gap-2 overflow-x-auto justify-center items-center">
          <div className="flex-1 flex flex-row gap-2">
            <ToggleGroup value={selectedSources} type="multiple" onValueChange={(value) => {
              if (value.includes("ALL") && value.length > 1) {
                setSelectedSources(value.filter((item) => item !== "ALL"));
              } else {
                setSelectedSources(value);
              }
            }} className="flex flex-row gap-4">
              <ToggleGroupItem value="ALL">ALL</ToggleGroupItem>
              <ToggleGroupItem value="RAKUMACHI">RAKUMACHI</ToggleGroupItem>
              <ToggleGroupItem value="KENBIYA">KENBIYA</ToggleGroupItem>
              <ToggleGroupItem value="YAHOO_FUDOUSAN">YAHOO</ToggleGroupItem>
              <ToggleGroupItem value="NIKKEI_FUDOUSAN">NIKKEI</ToggleGroupItem>
              <ToggleGroupItem value="TOYOKEIZAI_FUDOUSAN">TOYOKEIZAI</ToggleGroupItem>
            </ToggleGroup>
          </div>
          <div className="">
            <ToggleGroup value={selectedSort} type="single" onValueChange={(value) => {
              setSelectedSort(value);
            }} className="flex flex-row gap-4">
              <ToggleGroupItem value="CREATE_AT">追加日</ToggleGroupItem>
              <ToggleGroupItem value="RELEASE_AT">リリース日</ToggleGroupItem>
            </ToggleGroup> </div>
        </div>

        <Separator className="my-2" />


        <div>
          <div className="text-lg font-bold">Today </div>
          <Separator className="my-2" />
          <DataTable columns={columns as any} data={prepNewsForRendering(news, 0, 0)} defaultPageSize={10} />
        </div>

        <div>
          <div className="text-lg font-bold"> Yesterday</div>
          <Separator className="my-2" />
          <DataTable columns={columns as any} data={prepNewsForRendering(news, 1, 1)} defaultPageSize={10} />
        </div>
        <div>
          <div className="text-lg font-bold">This Week</div>
          <Separator className="my-2" />
          <DataTable columns={columns as any} data={prepNewsForRendering(news, 2, 7)} defaultPageSize={10} />
        </div>

        <div>
          <div className="text-lg font-bold">This Month</div>
          <Separator className="my-2" />
          <DataTable columns={columns as any} data={prepNewsForRendering(news, 8, 30)} defaultPageSize={10} />
        </div>

        <div>
          <div className="text-lg font-bold">Past</div>
          <Separator className="my-2" />
          <DataTable columns={columns as any} data={prepNewsForRendering(news, 31, 365)} defaultPageSize={10} />
        </div>
      </div>
    </div >
  </div>
}