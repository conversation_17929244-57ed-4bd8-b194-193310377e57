"use client";

import { useParams } from "next/navigation";
import { getAiSnsContentById, updateAiSnsContent } from "@/actions/aiSnsContents";
import { useEffect, useRef, useState } from "react";
import { AiSnsContentProps } from "@/lib/definitions";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import ImageSection from "./ImageSection";
import TextSection from "./TextSection";
import { EyeIcon } from "lucide-react";

export default function AiPage() {
  const { id } = useParams();
  const [aiSnsContent, setAiSnsContent] = useState<AiSnsContentProps>({} as AiSnsContentProps);
  const [showFullMeta, setShowFullMeta] = useState(false);


  useEffect(() => {
    const fetchAiSnsContent = async () => {
      const results = await getAiSnsContentById(id as string);
      if (results.success) {
        console.log('🔥aiSnsContent', results.data);
        setAiSnsContent(results.data as AiSnsContentProps);
      }
    };
    fetchAiSnsContent();
  }, [id]);

  return (
    <div>
      <div className="">
        <div className="flex justify-between items-center p-4   -mb-2">
          <h1 className="text-2xl font-bold flex-1" aria-label="AI Tools">詳細</h1>
          <div className="flex gap-2">
            <Link href={`/pub/ai/${id}/preview?source=${aiSnsContent?.contentByDeepSeek ? 'deepseek' : 'openai'}`}>
              <Button variant="outline">
                PREVIEW
              </Button>
            </Link>
            <Link href={`/ad/sns/${id}/edit`}>
              <Button variant="outline">
                EDIT
              </Button>
            </Link>
          </div>
        </div>
        <Separator className="my-2" />
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4">
          <div className="flex flex-col gap-4">
            <div>
              <h1> 基本情報 </h1>
              <Separator className="mb-2" />

              <pre className="flex bg-gray-100 rounded-md text-xs">
                <div className="flex flex-row relative overflow-hidden">
                  <EyeIcon className="w-6 h-6 absolute top-0 right-0 z-10 bg-gray-600 text-white rounded-md p-1" onClick={() => setShowFullMeta(!showFullMeta)} />
                  <div className="whitespace-pre-wrap break-words gap-2 p-4">
                    {["newsLink", "newsTitle", "newsDescription", "newsLinkThumbnailUrl", "newsLinkThumbnailUrlSupabase", "newsLinkThumbnailUrlSupabaseWithTitle", "newsSummary", "creatorUserId", "imagePromptRecommendedByOpenAi", "imageUrlByOpenAiOnSupabase", "imageUrlWithTitleByOpenAiOnSupabase"].map((key) => (
                      <div key={key} className={`${!showFullMeta ? "truncate" : ""}`}>
                        <b>{key}:</b> {JSON.stringify(aiSnsContent?.[key as keyof AiSnsContentProps]) || "-"}
                      </div>
                    ))}
                  </div>
                </div>
              </pre>
            </div>

            <ImageSection aiSnsContent={aiSnsContent} setAiSnsContent={setAiSnsContent} />
          </div>

          <TextSection aiSnsContent={aiSnsContent} setAiSnsContent={setAiSnsContent} />
        </div>
      </div>
    </div >
  );
}
