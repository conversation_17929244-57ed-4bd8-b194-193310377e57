"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Spinner } from "@/components/ui/spinner";
import { Switch } from "@/components/ui/switch";
import { AiSnsContentProps } from "@/lib/definitions";
import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Textarea } from "@/components/ui/textarea";
import html2canvas from "html2canvas";
import { uploadImageToSupabase, uploadImageToSupabaseAiContentFromUrl } from "@/actions/helper/supabase";
import { updateAiSnsContent } from "@/actions/aiSnsContents";
import { toast } from "@/hooks/use-toast";
import { createImageForUrlContent } from "@/actions/aiImage";
import { allInstructions } from "@/lib/constants/aiInstructions";
import TextSectionPromptDrawer from "./TextSectionPromptDrawer";
import { Badge } from "@/components/ui/badge";

export default function ImageSection({ aiSnsContent, setAiSnsContent }: { aiSnsContent: AiSnsContentProps, setAiSnsContent: (aiSnsContent: AiSnsContentProps) => void }) {
  const [loadingImage, setLoadingImage] = useState(false);
  const [switchShow, setSwitchShow] = useState(false);
  const [text, setText] = useState("");

  const sectionRef = useRef<HTMLDivElement>(null);
  const sectionLandscapeRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (aiSnsContent?.imageUrlWithTitleByOpenAiOnSupabase) {
      setSwitchShow(true);
    }
  }, [aiSnsContent]);

  const handleExport = async () => {
    if (!sectionRef.current) return;

    console.log('🔥sectionRef.current', sectionRef.current);
    const canvas = await html2canvas(sectionRef.current);
    const dataUrl = canvas.toDataURL("image/png");

    // Create a download link
    const link = document.createElement("a");
    link.href = dataUrl;
    link.download = "exported-section.png";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const uploadForLandScape = async () => {
    if (aiSnsContent?.newsLinkThumbnailUrl) {
      const uploaded = await uploadImageToSupabaseAiContentFromUrl({
        url: aiSnsContent?.newsLinkThumbnailUrl as string,
      });
      if (uploaded.success) {
        const updated = await updateAiSnsContent(aiSnsContent?.id as string, {
          newsLinkThumbnailUrlSupabase: uploaded.data,
        });

        if (updated.success) {
          toast({
            title: "画像を更新しました。",
            description: "画像を更新しました。"
          });
          setAiSnsContent(updated.data as AiSnsContentProps);
        }
      }
    } else {
      toast({
        title: "画像がありません。",
        description: "画像がありません。"
      });
    }
  }


  const handleExportLandscape = async () => {
    if (!sectionLandscapeRef.current) return;

    const canvas = await html2canvas(sectionLandscapeRef.current);
    const dataUrl = canvas.toDataURL("image/png");

    const link = document.createElement("a");
    link.href = dataUrl;
    link.download = "exported-section-landscape.png";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  // const handleUpdateImage = async () => {
  //   if (!sectionRef.current) return;
  //   if (!sectionLandscapeRef.current) return;

  //   setLoadingImage(true);
  //   const canvas = await html2canvas(sectionRef.current);
  //   const canvasLandscape = await html2canvas(sectionLandscapeRef.current);

  //   canvas.toBlob(async (blob) => {
  //     if (!blob) return;

  //     // Convert Blob to File (Required for Supabase upload)
  //     const file = new File([blob], `exported-${Date.now()}.png`, { type: "image/png" });

  //     // Send image to Server Action for upload
  //     const response = await uploadImageToSupabase(file);

  //     canvasLandscape.toBlob(async (newBlob) => {
  //       if (!newBlob) return;

  //       const fileLandscape = new File([newBlob], `exported-${Date.now()}_landsacpe.png`, { type: "image/png" });
  //       const responseLandscape = await uploadImageToSupabase(fileLandscape);

  //       if (response.success) {
  //         const updatedAiSnsContent = await updateAiSnsContent(aiSnsContent?.id as string, {
  //           imageUrlWithTitleByOpenAiOnSupabase: response.data,
  //           newsLinkThumbnailUrlSupabaseWithTitle: responseLandscape.data,
  //         });

  //         if (updatedAiSnsContent.success) {
  //           toast({
  //             title: "画像を更新しました。",
  //             description: "画像を更新しました。"
  //           });
  //           setText("");
  //           setSwitchShow(true);
  //           setAiSnsContent(updatedAiSnsContent.data as AiSnsContentProps);
  //         }
  //       }
  //     });

  //     setLoadingImage(false);
  //   }, "image/png")
  // }

  const getOpenAiImage = async () => {
    setLoadingImage(true);
    createImageForUrlContent(aiSnsContent).then((res) => {
      if (res.success) {
        updateAiSnsContent(aiSnsContent?.id as string, {
          imageUrlByOpenAiOnSupabase: res.data.supabasePublicUrl,
          imagePromptRevisedByOpenAi: res.data.openAiRevisedPrompt,
        }).then((updatedAiSnsContent) => {
          if (updatedAiSnsContent.success) {
            toast({
              title: "画像を更新しました。",
              description: "画像を更新しました。"
            });
            setAiSnsContent(updatedAiSnsContent.data as AiSnsContentProps);
          }
        });
      }
    }).finally(() => {
      setLoadingImage(false);
    });
  }

  return (
    <div>
      <div className="flex justify-between items-center">
        <h1> 画像 </h1>
        <Button variant="outline" onClick={() => getOpenAiImage()} disabled={!aiSnsContent?.imagePrompt}>
          {loadingImage ? <Spinner className="w-4 h-4" /> : "生成"}
        </Button>
      </div>

      <Separator className="my-2" />
      <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap mb-2 flex flex-row items-center gap-2">
        <TextSectionPromptDrawer type="image" aiSnsContent={aiSnsContent} setAiSnsContent={setAiSnsContent} updateColumnKey="imagePrompt" />

        <Badge variant="outline">
          {aiSnsContent?.imageSourceType}
        </Badge>

        <div className="truncate max-w-full">
          {aiSnsContent?.imagePrompt || "No Prompt"}
        </div>
      </pre>

      <div className="grid grid-cols-2 gap-2">
        <div className="flex flex-col gap-2 justify-between content-center items-center">
          <div className="flex flex-row items-center gap-2 text-sm">
            修正前
            <Switch className="my-2" disabled={aiSnsContent?.imageUrlWithTitleByOpenAiOnSupabase === null || aiSnsContent?.imageUrlWithTitleByOpenAiOnSupabase === undefined} checked={switchShow} onCheckedChange={() => setSwitchShow(!switchShow)} />
            修正後(タイトル付け)
          </div>

          <div>
            {aiSnsContent?.imageUrlByOpenAiOnSupabase ?
              <div className="flex flex-col relative items-center justify-center overflow-hidden" ref={sectionRef} style={{
                width: "300px",
                height: "400px",
              }}>
                {text && <canvas width={"100%"} height={"100%"} className="absolute top-0 left-0 w-full h-full opacity-65 bg-black"></canvas>}
                <span className={`${text.length > 30 ? "text-2xl" : "text-4xl"} font-bold absolute inset-0 flex items-center justify-center text-center text-white`} style={{ maxWidth: '80%', marginLeft: '10%', marginRight: '10%', lineHeight: '1.3', whiteSpace: 'pre-wrap' }}>
                  {text}
                </span>

                <Image src={switchShow ? aiSnsContent?.imageUrlWithTitleByOpenAiOnSupabase as string : aiSnsContent?.imageUrlByOpenAiOnSupabase as string} alt="画像" width={900} height={1200} sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px" priority />
              </div>
              : <div className="flex flex-col items-center justify-center h-[100px] w-[100px] bg-gray-100 ">No Image </div>}

            <div className="flex flex-1 items-center justify-center text-gray-500 text-sm">
              4:3 Portrait
            </div>
          </div>

          <Separator className="my-2" />

          <div>
            <div className="flex flex-col relative items-center justify-center overflow-hidden" ref={sectionLandscapeRef} style={{
              width: "300px",
              height: "130px",
            }}>
              {text && <canvas width={"100%"} height={"100%"} className="absolute top-0 left-0 w-full h-full opacity-65 bg-black"></canvas>}
              <span className={`${text.length > 30 ? "text-1xl" : "text-2xl"} font-bold absolute inset-0 flex items-center justify-center text-center text-white`} style={{ maxWidth: '80%', marginLeft: '10%', marginRight: '10%', lineHeight: '1.3', whiteSpace: 'pre-wrap' }}>
                {text}
              </span>
              {aiSnsContent?.newsLinkThumbnailUrlSupabase ?
                switchShow && aiSnsContent?.newsLinkThumbnailUrlSupabaseWithTitle ?
                  <Image src={aiSnsContent?.newsLinkThumbnailUrlSupabaseWithTitle as string} alt="画像" width={400} height={300} sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px" priority /> :
                  <Image src={aiSnsContent?.newsLinkThumbnailUrlSupabase as string} width={400} height={300} sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px" alt="Landscape" />
                :
                <div className="flex flex-col items-center justify-center h-[100px] w-[100px] bg-gray-100 ">No Image </div>
              }
            </div>

            <div className="flex flex-1 items-center justify-center text-gray-500 text-sm">
              Landscape
            </div>
          </div>
        </div>

        <div>
          <div className="text-gray-500">
            タイトルを修正
            <Separator className="mt-2" />
            <Textarea className="mt-2" placeholder="Enter TEXT" value={text} onChange={(e) => setText(e.target.value)} rows={3} />
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="mt-2" onClick={() => {
              // FIXME: 暂时关闭 to prevent the date.now error
              // handleUpdateImage();
              // setText("");
            }} disabled={loadingImage || text === ""}>
              {loadingImage ? <Spinner className="w-4 h-4" /> : "BOTH画像更新"}
            </Button>

            <Button variant="outline" className="mt-2" onClick={handleExport} disabled={loadingImage}>
              {loadingImage ? <Spinner className="w-4 h-4" /> : "PORTRAIT出力"}
            </Button>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" className="mt-2" onClick={uploadForLandScape} disabled={!aiSnsContent?.newsLinkThumbnailUrl}>
              {loadingImage ? <Spinner className="w-4 h-4" /> : "Landscape画像UP"}
            </Button>
            <Button variant="outline" className="mt-2" onClick={handleExportLandscape} disabled={loadingImage}>
              {loadingImage ? <Spinner className="w-4 h-4" /> : "LANDSCAPE出力"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}