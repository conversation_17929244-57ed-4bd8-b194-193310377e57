"use client"
import { useEffect, useState } from "react";
import { getAiSnsContentById } from "@/actions/aiSnsContents";
import { AiSnsContentProps } from "@/lib/definitions";
import { useParams } from "next/navigation";
import { Separator } from "@/components/ui/separator";
import { EyeIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { set } from "zod";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { updateAiSnsContent } from "@/actions/aiSnsContents";
import { toast } from "@/hooks/use-toast";

export default function EditAiPage() {
  const { id } = useParams();
  const [aiSnsContent, setAiSnsContent] = useState<AiSnsContentProps>({} as AiSnsContentProps);
  const [newsFullTextManual, setNewsFullTextManual] = useState<string>("");

  useEffect(() => {
    const fetchAiSnsContent = async () => {
      const results = await getAiSnsContentById(id as string);
      if (results.success) {
        console.log('🔥aiSnsContent', results.data);
        setAiSnsContent(results.data as AiSnsContentProps);
      }
    };
    fetchAiSnsContent();
  }, [id]);

  const handleUpdateAiSnsContent = async () => {
    const results = await updateAiSnsContent(id as string, { newsFullTextManual });
    if (results.success) {
      console.log('🔥results', results);
      toast({
        title: "更新しました",
        description: "更新しました",
      });
    }
    setAiSnsContent(results.data as AiSnsContentProps);
  }


  return <div className="p-2">
    <div className="flex flex-row gap-4">
      <h1 className="text-2xl font-bold flex-1"> 基本情報 </h1>
    </div>

    <Separator className="my-2" />

    <div className="flex flex-col gap-4">
      <Label htmlFor="newsFullTextManual">ニュースの全文</Label>
      <Textarea
        rows={10}
        value={newsFullTextManual}
        onChange={(e) => setNewsFullTextManual(e.target.value)}
        placeholder="ニュースの全文を入力してください"
      />

      <Button onClick={() => {
        handleUpdateAiSnsContent();
      }}>更新</Button>

      <pre className="flex bg-gray-100 rounded-md text-xs">
        <div className="flex flex-row relative overflow-hidden">
          {/* 
          <div className="whitespace-pre-wrap break-words gap-2 p-4">
            {["newsLink", "newsTitle", "newsDescription", "newsLinkThumbnailUrl", "newsSummary", "creatorUserId", "imageUrlByOpenAiOnSupabase", "imageUrlWithTitleByOpenAiOnSupabase", "newsFullTextManual"].map((key) => (
              <div key={key}>
                <b>{key}:</b> {aiSnsContent?.[key as keyof AiSnsContentProps] || "-"}
              </div>
            ))}
          </div> */}
        </div>
      </pre>
    </div>
  </div >;
}