"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer"
import { Replace } from "lucide-react";
import { allInstructions } from "@/lib/constants/aiInstructions";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { AiSnsContentProps, AiSnsContentsContentSourceType } from "@/lib/definitions";
import { updateAiSnsContent } from "@/actions/aiSnsContents";
import { toast } from "@/hooks/use-toast";

export default function TextSectionPromptDrawer({ type, aiSnsContent, setAiSnsContent, updateColumnKey }: { type: string | "text" | "image", aiSnsContent: AiSnsContentProps, setAiSnsContent: (aiSnsContent: AiSnsContentProps) => void, updateColumnKey: string }) {
  const updateSource = async (source: AiSnsContentsContentSourceType) => {
    const updatedAiSnsContent = await updateAiSnsContent(aiSnsContent?.id as string, {
      [type === "text" ? "contentSourceType" : "imageSourceType"]: source,
    });
    if (updatedAiSnsContent.success) {
      setAiSnsContent(updatedAiSnsContent.data as AiSnsContentProps);
      toast({
        title: "Source updated",
        description: "Source updated",
      });
    }
  }

  const updatePrompt = async (value: string) => {
    const updatedAiSnsContent = await updateAiSnsContent(aiSnsContent?.id as string, {
      [updateColumnKey]: value,
    });
    if (updatedAiSnsContent.success) {
      setAiSnsContent(updatedAiSnsContent.data as AiSnsContentProps);
      toast({
        title: "Prompt updated",
        description: "Prompt updated",
      });
    }
    console.log(value);
  }

  const renderSelectBasis = () => {

    const mapper = [
      {
        label: "Title",
        value: AiSnsContentsContentSourceType.TITLE,
        content: aiSnsContent.newsTitle,
        disabled: aiSnsContent.newsTitle === "" || aiSnsContent.newsTitle === null,
        checked: type === "text" && aiSnsContent.contentSourceType === AiSnsContentsContentSourceType.TITLE || type === "image" && aiSnsContent.imageSourceType === AiSnsContentsContentSourceType.TITLE
      },
      {
        label: "Summary",
        value: AiSnsContentsContentSourceType.SUMMARY,
        content: aiSnsContent.newsSummary,
        disabled: aiSnsContent.newsSummary === "" || aiSnsContent.newsSummary === null,
        checked: type === "text" && aiSnsContent.contentSourceType === AiSnsContentsContentSourceType.SUMMARY || type === "image" && aiSnsContent.imageSourceType === AiSnsContentsContentSourceType.SUMMARY
      },
      {
        label: "Manual",
        value: AiSnsContentsContentSourceType.MANUAL,
        content: aiSnsContent.newsFullTextManual,
        disabled: aiSnsContent.newsFullTextManual === "" || aiSnsContent.newsFullTextManual === null,
        checked: type === "text" && aiSnsContent.contentSourceType === AiSnsContentsContentSourceType.MANUAL || type === "image" && aiSnsContent.imageSourceType === AiSnsContentsContentSourceType.MANUAL
      },
      {
        label: "Full text",
        value: AiSnsContentsContentSourceType.FULL_TEXT,
        content: "N/A",
        disabled: false,
        checked: type === "text" && aiSnsContent.contentSourceType === AiSnsContentsContentSourceType.FULL_TEXT || type === "image" && aiSnsContent.imageSourceType === AiSnsContentsContentSourceType.FULL_TEXT
      }
    ]

    return mapper.map((item) => (
      <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap my-2 flex flex-row items-center gap-2">
        <Checkbox disabled={item.disabled} className="w-4 h-4 mr-2" checked={item.checked} onCheckedChange={() => updateSource(item.value)} />
        <div>
          <div className="flex flex-row items-center gap-2">
            <b className="border-b border-gray-900 mb-2 w-full">{item.label}:</b>
          </div>

          {item.content}
        </div>
      </pre>
    ));
  }

  return (
    <Drawer>
      <DrawerTrigger>
        <Replace className="w-4 h-4" />
      </DrawerTrigger>

      <DrawerContent>
        <DrawerHeader>
          <DrawerTitle>Prepare Data for Generation</DrawerTitle>
        </DrawerHeader>

        <ScrollArea className="h-[calc(100vh-300px)] p-4 select-text">
          {type === "text" && <div className="py-2">
            <h2 className="text-lg font-bold">Select Basis</h2>
            {renderSelectBasis()}
          </div>}

          <div className="py-2">
            <h2 className="text-lg font-bold">Select Prompt</h2>
          </div>
          {type === "image" && aiSnsContent?.imagePromptRecommendedByOpenAi && <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap my-2 flex flex-row items-center gap-2">
            <Checkbox className="w-4 h-4 mr-2" checked={aiSnsContent["imagePrompt"] === aiSnsContent["imagePromptRecommendedByOpenAi"]} onCheckedChange={() => updatePrompt(aiSnsContent["imagePromptRecommendedByOpenAi"] as string)} />
            <div>
              <div className="flex flex-row items-center gap-2">
                <b className="border-b border-gray-900 mb-2 w-full">IMAGE PROMOPTY BY OPENAI</b>
              </div>
              {aiSnsContent.imagePromptRecommendedByOpenAi}
            </div>
          </pre>}

          {Object.keys(allInstructions[type]).map((key: string) => (
            <pre key={key} className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap my-2 flex flex-row items-center gap-2">
              <Checkbox className="w-4 h-4 mr-2" checked={aiSnsContent[updateColumnKey as keyof AiSnsContentProps] === allInstructions[type][key]} onCheckedChange={() => updatePrompt(allInstructions[type][key])} />
              <div>
                <div className="flex flex-row items-center gap-2">
                  <b className="border-b border-gray-900 mb-2 w-full">{key}</b>
                </div>
                {allInstructions[type][key]}
              </div>
            </pre>
          ))}

        </ScrollArea>
      </DrawerContent>

    </Drawer >
  );
}
