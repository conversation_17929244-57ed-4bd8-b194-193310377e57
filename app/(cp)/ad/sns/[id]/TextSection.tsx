"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Toggle } from "@/components/ui/toggle";
import { Copy, Replace } from "lucide-react";
import Markdown from "react-markdown";
import { AiSnsContentProps } from "@/lib/definitions";
import { useState } from "react";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "@/hooks/use-toast";
import { rewriteContentFromOpenAI } from "@/actions/ai";
import { rewriteContentFromDeepSeek } from "@/actions/ai";
import { updateAiSnsContent } from "@/actions/aiSnsContents";
import TextSectionPromptDrawer from "./TextSectionPromptDrawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

export default function TextSection({ aiSnsContent, setAiSnsContent }: { aiSnsContent: AiSnsContentProps, setAiSnsContent: (aiSnsContent: AiSnsContentProps) => void }) {
  const [showRaw, setShowRaw] = useState(false);
  const [loadingGpt, setLoadingGpt] = useState(false);
  const [loadingDeepSeek, setLoadingDeepSeek] = useState(false);

  const beautifyMarkdown = (markdown: string) => {
    return markdown;
  }

  const getOpenAiSummary = async (url: string) => {
    setLoadingGpt(true);
    rewriteContentFromOpenAI(url, aiSnsContent).then((res) => {
      if (res.success) {
        updateAiSnsContent(aiSnsContent?.id as string, {
          contentByOpenAi: res.data,
        }).then((updatedAiSnsContent) => {
          if (updatedAiSnsContent.success) {
            toast({
              title: "OPENAI要約を更新しました。",
              description: "OPENAI要約を更新しました。"
            });
            setAiSnsContent(updatedAiSnsContent.data as AiSnsContentProps);
          }
        });
      } else {
        toast({
          title: "OPENAI要約できませんでした。",
          description: res.message
        });
      }
      setLoadingGpt(false);
    });
  }

  const getDeepSeekSummary = async (url: string) => {
    setLoadingDeepSeek(true);
    rewriteContentFromDeepSeek(url, aiSnsContent).then((res) => {
      if (res.success) {
        updateAiSnsContent(aiSnsContent?.id as string, {
          contentByDeepSeek: res.data,
        }).then((updatedAiSnsContent) => {
          if (updatedAiSnsContent.success) {
            toast({
              title: "DEEPSEEK要約を更新しました。",
              description: "DEEPSEEK要約を更新しました。"
            });
            setAiSnsContent(updatedAiSnsContent.data as AiSnsContentProps);
          }
        });
      } else {
        toast({
          title: "DEEPSEEK要約できませんでした。",
          description: res.message
        });
      }
      setLoadingDeepSeek(false);
    });
  }

  const convertMarkdownToPlainText = (markdown: string) => {
    return markdown
      .replace(/!\[.*?\]\(.*?\)/g, '') // Remove images
      .replace(/\[.*?\]\(.*?\)/g, '') // Remove links
      .replace(/(`{1,3})(.*?)\1/g, '$2') // Remove inline & block code
      .replace(/[*_~`>-]/g, '') // Remove common Markdown characters but keep single #
      .replace(/#{2,}/g, '') // Keep single #
      .replace(/\n{2,}/g, '\n\n') // Replace multiple newlines with 2 newline
      .trim();
  };

  return (
    <div>
      <div className="flex justify-between items-center">
        <h1 className="text-lg font-bold flex-1"> 内容 </h1>
        <Toggle pressed={showRaw} onPressedChange={() => setShowRaw(!showRaw)}> RAW </Toggle>
      </div>
      <Separator className="my-2" />

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="my-2">
          <div className="flex justify-between items-center gap-2">
            <h1 className="flex-1 text-lg font-bold"> OPEN AI </h1>
            <Button variant="outline" onClick={() => getOpenAiSummary(aiSnsContent?.newsLink as string)} disabled={!aiSnsContent?.contentByOpenAiPrompt}>
              {loadingGpt ? <Spinner className="w-4 h-4" /> : "生成"}
            </Button>
          </div>
          <Separator className="my-2" />


          <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap my-2 flex flex-row items-center gap-2">
            <TextSectionPromptDrawer type="text" aiSnsContent={aiSnsContent} setAiSnsContent={setAiSnsContent} updateColumnKey="contentByOpenAiPrompt" />

            <Badge variant="outline">
              {aiSnsContent?.contentSourceType}
            </Badge>

            <div className="truncate max-w-full">
              {aiSnsContent?.contentByOpenAiPrompt ? aiSnsContent.contentByOpenAiPrompt : "No Prompt"}
            </div>
          </pre>

          <ScrollArea className="h-[600px]">
            <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap relative">
              <Copy className="absolute right-0 top-0 w-6 h-6 bg-gray-600 rounded-md p-1 text-white" onClick={() => {
                navigator.clipboard.writeText(convertMarkdownToPlainText(aiSnsContent?.contentByOpenAi || ""));
                toast({
                  title: "コピーしました。",
                  description: "コピーしました。"
                });
              }} />
              {loadingGpt ? <Spinner /> :
                showRaw ? aiSnsContent?.contentByOpenAi :
                  <Markdown>{beautifyMarkdown(aiSnsContent?.contentByOpenAi || "")}</Markdown>}
            </pre>
          </ScrollArea>
        </div>

        <div className="my-2">
          <div className="flex justify-between items-center gap-2">
            <h1 className="flex-1 text-lg font-bold"> DEEPSEEK </h1>
            <Button variant="outline" onClick={() => getDeepSeekSummary(aiSnsContent?.newsLink as string)} disabled={!aiSnsContent?.contentByDeepSeekPrompt}>
              {loadingDeepSeek ? <Spinner className="w-4 h-4" /> : "生成"}
            </Button>
          </div>
          <Separator className="my-2" />

          <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap my-2 flex flex-row items-center gap-2">
            <TextSectionPromptDrawer type="text" aiSnsContent={aiSnsContent} setAiSnsContent={setAiSnsContent} updateColumnKey="contentByDeepSeekPrompt" />
            <Badge variant="outline">
              {aiSnsContent?.contentSourceType}
            </Badge>
            <div className="truncate max-w-full">
              {aiSnsContent?.contentByDeepSeekPrompt || "No Prompt"}
            </div>
          </pre>

          <ScrollArea className="h-[600px]">
            <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap relative">
              <Copy className="absolute right-0 top-0 w-6 h-6 bg-gray-600 rounded-md p-1 text-white" onClick={() => {
                navigator.clipboard.writeText(convertMarkdownToPlainText(aiSnsContent?.contentByDeepSeek || ""));
                toast({
                  title: "コピーしました。",
                  description: "コピーしました。"
                });
              }} />
              {loadingDeepSeek ? <Spinner /> :
                showRaw ? aiSnsContent?.contentByDeepSeek :
                  <Markdown>{beautifyMarkdown(aiSnsContent?.contentByDeepSeek || "")}</Markdown>}
            </pre>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
}