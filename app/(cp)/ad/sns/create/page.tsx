"use client"

import Link from "next/link";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import UrlPreviewer from "@/components/UrlPreviewer";
import { toast } from "@/hooks/use-toast";
import { createAiSnsContent } from "@/actions/aiSnsContents";
import { Spinner } from "@/components/ui/spinner";
import { createSummaryFromOpenAI } from "@/actions/ai";
import { useAuthStore } from "@/store/auth";
import { useRouter } from "next/navigation";

export default function NewAiSnsContent() {
  const [inputUrl, setInputUrl] = useState("");
  const { currentUser } = useAuthStore();
  const [metaData, setMetaData] = useState<any>(null);
  const [summary, setSummary] = useState({
    summary: "",
    imagePrompt: "",
  });
  const router = useRouter();

  const [loadingGpt, setLoadingGpt] = useState(false);

  const handleCreateSummary = async (url: string) => {
    setLoadingGpt(true);
    const summary = await createSummaryFromOpenAI(url);
    if (summary.success) {
      console.log('🔥summary', summary.data);
      setSummary(summary.data);
      toast({
        title: "OPENAI要約できました。",
        description: "OPENAI要約できました。"
      });
    }
  }

  const createAndGoToDP = async () => {
    try {
      if (!currentUser) {
        toast({
          title: "ログインしてください。",
          description: "ログインしてください。"
        });
        return;
      }

      const resCreateAiSnsContent = await createAiSnsContent({
        creatorUserId: currentUser.id as string,
        newsLink: inputUrl,
        newsTitle: metaData?.title,
        newsDescription: metaData?.description,
        newsLinkThumbnailUrl: metaData?.ogImage,
        newsSummary: summary.summary,
        imagePromptRecommendedByOpenAi: summary.imagePrompt || "",
      });
      console.log('🔥resCreateAiSnsContent', resCreateAiSnsContent);

      if (resCreateAiSnsContent.success) {
        toast({
          title: "SNS内容を作成しました。",
          description: "SNS内容を作成しました。",
        });

        router.push(`/ad/sns/${resCreateAiSnsContent.data.id}`);
      }
    } catch (error) {
      toast({
        title: "SNS内容を作成できませんでした。",
        description: "SNS内容を作成できませんでした。"
      });
      console.log('🔥Error in creating AI SNS content:', error);
    } finally {
      setLoadingGpt(false);
    }
  }

  return (
    <div className="grid gap-4 grid-cols-1 p-2 sm:p-4">
      <div>
        <UrlPreviewer inputUrl={inputUrl} setInputUrl={setInputUrl} metaData={metaData} setMetaData={setMetaData} />

        <div className="flex flex-row gap-4 my-6 p-4 justify-center">
          <Button variant="default" className="p-4" size="lg" onClick={() => {
            handleCreateSummary(inputUrl);
          }} disabled={inputUrl.length === 0 || loadingGpt || !metaData}>
            {loadingGpt ? <div className="flex justify-center items-center my-2">
              <Spinner /> Creating record.. please wait...
            </div> : "OPENAIでSUMMARY生成"}
          </Button>
        </div>

        {summary.summary && <>
          <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap text-xs word-wrap">
            Content PROMPT:
            <br />
            {summary?.summary || ""}

            {summary.imagePrompt && <>
              <br />
              <br />
              IMAGE PROMPT:
              <br />
              {summary?.imagePrompt || ""}
            </>}
          </pre>
        </>}


        {summary.summary && <div className="flex justify-center items-center my-2">
          <Button className="my-6" onClick={() => {
            createAndGoToDP();
          }}>View Record</Button>
        </div>}
      </div>
    </div >
  );
};
