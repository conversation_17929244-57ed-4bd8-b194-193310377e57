"use client"

import { TrendingUp } from "lucide-react"
import { Bar, Bar<PERSON>hart, CartesianGrid, XAxis, YAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

import { Tabs, TabsTrigger, TabsContent, TabsList } from "@/components/ui/tabs"
import { useEffect, useState } from "react"
import { getAiSnsMetrics } from "@/actions/aiSNSMetrics"
import { AiSnsMetricsProps } from "@/lib/definitions"
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz"

const chartConfig = {
  xhsFollowers: {
    label: "小红书粉丝数",
    color: "hsl(var(--chart-1))",
  },
  wechatFollowers: {
    label: "微信公众号粉丝数",
    color: "hsl(var(--chart-2))",
  },
  // wechatGroupMembers: {
  //   label: "微信社群人数",
  //   color: "hsl(var(--chart-3))",
  // },
} satisfies ChartConfig

const chartConfigIncremental = {
  xhsFollowersIncremental: {
    label: "小红书增量",
    color: "hsl(var(--chart-1))",
  },
  wechatFollowersIncremental: {
    label: "微信公众号增量",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig

export default function AggregateChart() {
  const [tab, setTab] = useState("cumulative");

  const [snsMetricsData, setSnsMetricsData] = useState<AiSnsMetricsProps[]>([]);
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");

  const startOfWeek = (date: Date) => {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is sunday
    return new Date(date.setDate(diff));
  };

  const getPastIsoWeeksForPastXMonths = (months: number): any => {
    const weeks: { [key: string]: any[] } = {};
    const today = dayjsWithTz();
    const threeMonthsAgo = today.subtract(months, 'month').toDate();
    console.log("threeMonthsAgo", threeMonthsAgo);

    for (let date = threeMonthsAgo; date <= today.toDate(); date.setDate(date.getDate() + 7)) {
      const weekStart = dayjsWithTz(date).startOf('week');
      const weekKey = weekStart.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
      weeks[weekKey] = {} as any;
    }

    setStartDate(today.subtract(months, 'month').format("YYYY/MM/DD"));
    setEndDate(dayjsWithTz().format("YYYY/MM/DD"));
    return weeks;
  };

  const fetchAndPreprocessDataForLineChart = async () => {
    const res = await getAiSnsMetrics();
    if (res.success) {
      const isoWeeksRawData = getPastIsoWeeksForPastXMonths(3);

      res.data.filter((item: AiSnsMetricsProps) => item.recordDate !== null).forEach((item: AiSnsMetricsProps) => {
        isoWeeksRawData[dayjsWithTz(item.recordDate).startOf('week').toISOString().split('T')[0]] = { ...item };
      });

      // 如果没有数据，使用上周的数据，除非上周数据为 null
      let mostRecentData: AiSnsMetricsProps | null = null;
      Object.keys(isoWeeksRawData).forEach((key) => {
        // Key is start of week
        if (Object.keys(isoWeeksRawData[key]).length === 0 && mostRecentData) {
          isoWeeksRawData[key] = {
            ...mostRecentData,
            recordDate: key,
            xhsFollowersIncremental: 0,
            wechatFollowersIncremental: 0,
          };
        }

        if (isoWeeksRawData[key]) {
          isoWeeksRawData[key].xhsFollowersIncremental = isoWeeksRawData[key].xhsFollowers - (mostRecentData?.xhsFollowers || 0);
          isoWeeksRawData[key].wechatFollowersIncremental = isoWeeksRawData[key].wechatFollowers - (mostRecentData?.wechatFollowers || 0);

          mostRecentData = isoWeeksRawData[key];
        }
      });

      console.log("isoWeeksRawData", isoWeeksRawData);
      // remoe frist 2 records due to the data is not complete
      setSnsMetricsData(Object.values(isoWeeksRawData).slice(2) as AiSnsMetricsProps[]);
    }
  }

  useEffect(() => {
    fetchAndPreprocessDataForLineChart();
  }, []);

  let renderChart = (tab: string) => {
    return (
      <ChartContainer config={tab === "cumulative" ? chartConfig : chartConfigIncremental}>
        <BarChart accessibilityLayer data={snsMetricsData.filter((item) => dayjsWithTz(item.recordDate).year() === dayjsWithTz().year()).sort((a, b) => dayjsWithTz(a.recordDate).diff(dayjsWithTz(b.recordDate)))}>
          <CartesianGrid vertical={false} />
          <YAxis
            tickLine={false}
            axisLine={true}
            tickFormatter={(value) => value.toLocaleString()}
            domain={[0, (dataMax: number) => dataMax * 1.1]}
          />
          <XAxis
            dataKey="recordDate"
            tickLine={false}
            tickMargin={10}
            axisLine={true}
            tickFormatter={(value) => value.toLocaleString()}
          />
          <ChartTooltip content={<ChartTooltipContent />} />
          <ChartLegend content={<ChartLegendContent />} />
          {Object.keys(tab === "cumulative" ? chartConfig : chartConfigIncremental).map((key) => (
            <Bar
              key={key}
              dataKey={key}
              // stackId="a"
              fill={tab === "cumulative" ? chartConfig[key as keyof typeof chartConfig].color : chartConfigIncremental[key as keyof typeof chartConfigIncremental].color}
              label={{
                position: "top",
                formatter: (value: number) => value.toLocaleString(),
                color: "#000",
              }}
            />
          ))}
        </BarChart>
      </ChartContainer>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-row justify-between items-center">
          <div className="flex flex-col gap-1 flex-1">
            <CardTitle>周获客数</CardTitle>
            <CardDescription>{startDate} ~ {endDate}</CardDescription>
          </div>

          <Tabs value={tab} onValueChange={setTab} className="">
            <TabsList>
              <TabsTrigger value="cumulative">Cumulative</TabsTrigger>
              <TabsTrigger value="incremental">Incremental</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        {tab === "cumulative" && renderChart("cumulative")}

        {tab === "incremental" && renderChart("incremental")}
      </CardContent>
      <CardFooter className="flex flex-col items-middle gap-2 text-sm text-center justify-center">
        <div className="flex gap-2 font-medium leading-none">
          Trending up by xxx% over the period showing <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Increase by xxx viistors, as compared to xxx
        </div>
      </CardFooter>
    </Card>
  )
}
