import { AiSnsMetricsProps } from "@/lib/definitions";
import dayjs from "dayjs";
import { maxBy } from "lodash-es";

export const snsMetricsMapper = {
  xhsFollowers: {
    label: "小红书粉丝数",
    type: "LOW_QUALITY",
  },
  wechatFollowers: {
    label: "微信公众号粉丝数",
    type: "LOW_QUALITY",
  },
  wechatVideoFollowers: {
    label: "微信视频号粉丝数",
    type: "LOW_QUALITY",
  },
  facebookFollowers: {
    label: "Facebook粉丝数",
    type: "LOW_QUALITY",
  },
  linkedinFollowers: {
    label: "LinkedIn粉丝数",
    type: "LOW_QUALITY",
  },
  wechatGroupMembers: {
    label: "微信社群人数",
    type: "HIGH_QUALITY",
  },
  whatsappGroupMembers: {
    label: "WhatsApp社群人数",
    type: "HIGH_QUALITY",
  },
  wechatEnterpriseLeads: {
    label: "微信企业微信客户数",
    type: "HIGH_QUALITY",
  },
  tiktokFollowers: {
    label: "TikTok粉丝数",
    type: "HIGH_QUALITY",
  },
}

export function getLeadsCount(aiSnsMetricsRecords: AiSnsMetricsProps[], type: "LOW_QUALITY" | "HIGH_QUALITY"): {
  totalLatest2025: number;
  totalLatest2024: number;
  incremental2025: number;
} {
  let res = {
    totalLatest2025: 0,
    totalLatest2024: 0,
    incremental2025: 0,
  }

  Object.keys(snsMetricsMapper).filter((key) => snsMetricsMapper[key as keyof typeof snsMetricsMapper].type === type).forEach((key) => {
    const largestRecord2025 = findLastRecordForFieldInYear(aiSnsMetricsRecords, key, 2025);
    if (largestRecord2025?.[key as keyof AiSnsMetricsProps] !== null) {
      res.totalLatest2025 += largestRecord2025[key as keyof AiSnsMetricsProps] as number || 0;
    }

    const largestRecord2024 = findLastRecordForFieldInYear(aiSnsMetricsRecords, key, 2024);
    if (largestRecord2024?.[key as keyof AiSnsMetricsProps] !== null) {
      res.totalLatest2024 += largestRecord2024[key as keyof AiSnsMetricsProps] as number || 0;
    }

    res.incremental2025 = res.totalLatest2025 - res.totalLatest2024;
  });

  return res;
}


const findLastRecordForFieldInYear = (aiSnsMetricsRecords: AiSnsMetricsProps[], field: string, year: number): any => {
  let records = aiSnsMetricsRecords.filter(record => dayjs(record.recordDate).year() === year);
  return maxBy(records, "recordDate") || { [field]: 0 };
}
