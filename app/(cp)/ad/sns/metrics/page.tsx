"use client";

import { Separator } from "@/components/ui/separator";
import { useState, useEffect, use } from "react";
import { useRouter } from 'next/navigation'; // 使用app router
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import AggregateChart from "../(dashboard)/AggregateChart";
import { Button } from "@/components/ui/button";
import { AiSnsMetricsProps } from "@/lib/definitions";
import { getLeadsCount } from "../(dashboard)/utilities";
import { getAiSnsMetrics } from "@/actions/aiSNSMetrics";
import dayjs from "dayjs";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
export default function AI() {
  const router = useRouter(); // 使用router
  const [selectedTab, setSelectedTab] = useState<"hotspot" | "newAiSnsContent" | "listAiSnsContents">();
  const [aiSnsMetricsRecords, setAiSnsMetricsRecords] = useState<AiSnsMetricsProps[]>([]);
  const [lqLeadsCount, setLqLeadsCount] = useState<{
    totalLatest2025: number;
    totalLatest2024: number;
    incremental2025: number;
  }>({
    totalLatest2025: 0,
    totalLatest2024: 0,
    incremental2025: 0,
  });
  const [hqLeadsCount, setHqLeadsCount] = useState<{
    totalLatest2025: number;
    totalLatest2024: number;
    incremental2025: number;
  }>({
    totalLatest2025: 0,
    totalLatest2024: 0,
    incremental2025: 0,
  });

  useEffect(() => {
    if (aiSnsMetricsRecords.length === 0) {
      getAiSnsMetrics().then((res) => {
        let data = res.data;
        setLqLeadsCount(getLeadsCount(data, "LOW_QUALITY"));
        setHqLeadsCount(getLeadsCount(data, "HIGH_QUALITY"));
      });
    }

    const { hash } = window.location;
    console.log("hash", hash);
    const tab = hash.replace('#', '') as "hotspot" | "newAiSnsContent" | "listAiSnsContents";
    if (tab === "listAiSnsContents" || tab === "newAiSnsContent" || tab === "hotspot") {
      setSelectedTab(tab);
    } else {
      setSelectedTab("hotspot");
    }
  }, [setSelectedTab]);

  // console.log("lqLeadsCount", lqLeadsCount);
  // console.log("hqLeadsCount", hqLeadsCount);

  let allGoasl = {
    goal1: {
      start: 2500,
      end: 7500,
    },
    goal2: {
      start: 0,
      end: 5000,
    },
    goal3: {
      start: 250,
      end: 1250,
    },
    goal4: {
      start: 0,
      end: 1000,
    }
  }

  let getGoalForCurrentMonthEnd = (goal: { start: number, end: number }): number => {
    let currentMonth = dayjs().month() + 1;
    return Math.round((goal.end - goal.start) / 12 * currentMonth + goal.start);
  }

  let dataForCard = [
    {
      title: "低质Leads总数",
      value: lqLeadsCount.totalLatest2025,
      goal: getGoalForCurrentMonthEnd(allGoasl.goal1),
      percentage: lqLeadsCount.totalLatest2025 / getGoalForCurrentMonthEnd(allGoasl.goal1) * 100
    },
    {
      title: "低质Leads增加数",
      value: lqLeadsCount.incremental2025,
      goal: getGoalForCurrentMonthEnd(allGoasl.goal2),
      percentage: lqLeadsCount.incremental2025 / getGoalForCurrentMonthEnd(allGoasl.goal2) * 100
    },
    {
      title: "高质Leads总数",
      value: hqLeadsCount.totalLatest2025,
      goal: getGoalForCurrentMonthEnd(allGoasl.goal3),
      percentage: hqLeadsCount.totalLatest2025 / getGoalForCurrentMonthEnd(allGoasl.goal3) * 100
    },
    {
      title: "高质Leads增加数",
      value: hqLeadsCount.incremental2025,
      goal: getGoalForCurrentMonthEnd(allGoasl.goal4),
      percentage: hqLeadsCount.incremental2025 / getGoalForCurrentMonthEnd(allGoasl.goal4) * 100
    },
  ]

  return (
    <div className="">
      <div className="flex justify-between items-center p-4 -mb-2">
        <h1 className="text-2xl font-bold" aria-label="SNSツール">SNS Dashboard</h1>

        <div className="flex flex-row gap-4">
          <Button onClick={() => router.push('/ad/sns/dataManagement')}>
            データ管理
          </Button>
        </div>
      </div>

      <Separator className="my-2" />

      <div className="p-4 flex flex-col gap-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {dataForCard.map((item) => (
            <Card className="rounded-lg py-2" key={item.title}>
              <CardHeader className="pb-0">
                <CardTitle>{item.title}</CardTitle>
              </CardHeader>
              <CardContent className="mt-2">
                <div className="text-3xl font-bold">
                  {item.value}
                  <span className="text-xs text-gray-400 mt-1 ml-2">
                    / {item.goal}
                    ({dayjs().month() + 1}月末目標)
                  </span>
                </div>

                <div className="flex flex-row gap-2 items-center">
                  <Progress value={item.percentage} className="mt-2" />
                  <span className="text-xs text-gray-400 mt-2">
                    {item.percentage.toFixed(2)}%
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="flex flex-row gap-4">
          <Card className="rounded-lg w-full">
            <AggregateChart />
          </Card>
          {/* <Card className="rounded-lg flex-1">
            <CardHeader>
              <CardTitle>新規顧客リスト</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="list-disc pl-5">
                {dataForCard.map((item) => (
                  <li key={item.title} className="text text-gray-700 border-b border-gray-200 pb-2 mt-2">
                    {item.title}: {item.value} / {item.goal} (達成率: {item.percentage}%)
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card> */}
        </div>
      </div>

    </div>
  );
}
