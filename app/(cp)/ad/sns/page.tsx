"use client"

import { getAiSnsContents } from "@/actions/aiSnsContents";
import { useState, useEffect, Suspense } from "react";
import { AiSnsContentProps } from "@/lib/definitions";
import { DataTable } from "@/components/ui/data-table"; // DataTableコンポーネントをインポート
import dayjs from "dayjs";
import { Separator } from "@/components/ui/separator";

export default function ListAiSnsContent() {
  const [aiSnsContents, setAiSnsContents] = useState<AiSnsContentProps[]>([]);
  const [selectedNewsLink, setSelectedNewsLink] = useState<string>("ALL");

  useEffect(() => {
    const fetchAiSnsContents = async () => {
      const contents = await getAiSnsContents();
      if (contents.success) {
        setAiSnsContents(contents.data);
      }
    };
    fetchAiSnsContents();
  }, []);


  const columns = [
    { header: "ID", cell: ({ row }: { row: { original: { id: string } } }) => <a href={`/ad/sns/${row.original.id}`} className="text-blue-500 underline">{row.original.id.slice(0, 6)}...</a> },
    { header: "PREVIEW", cell: ({ row }: { row: { original: { id: string } } }) => <a target="_blank" href={`/pub/ai/${row.original.id}/preview`} className="text-blue-500 underline">LINK</a> },
    {
      header: "画像URL",
      cell: ({ row }: { row: { original: { imageUrlByOpenAiOnSupabase: string } } }) => {
        const imageUrlByOpenAiOnSupabase = row.original.imageUrlByOpenAiOnSupabase;
        return imageUrlByOpenAiOnSupabase ? <img src={imageUrlByOpenAiOnSupabase} alt="AI生成画像プレビュー" width={100} height={100} /> : "未生成";
      }
    },
    {
      header: "画像修正URL",
      cell: ({ row }: { row: { original: { imageUrlWithTitleByOpenAiOnSupabase: string } } }) => {
        const imageUrlWithTitleByOpenAiOnSupabase = row.original.imageUrlWithTitleByOpenAiOnSupabase;
        return imageUrlWithTitleByOpenAiOnSupabase ? <img src={imageUrlWithTitleByOpenAiOnSupabase} alt="AI生成画像修正版プレビュー" width={100} height={100} /> : "未生成";
      }
    },
    {
      header: "作成者", cell: ({ row }: { row: { original: { creator: { name: string } } } }) => {
        const creator = row.original.creator;
        return creator ? creator.name : "未作成";
      }
    },
    {
      header: "リンク", cell: ({ row }: { row: { original: { newsLink: string } } }) => <a
        href={row.original.newsLink}
        className="text-blue-500 underline"
        target="_blank"
        rel="noopener noreferrer"
      >
        {row.original.newsLink.slice(0, 10)}...
      </a>
    },
    {
      header: "OPENAI",
      cell: ({ row }: { row: { original: { contentByOpenAi: string } } }) => {
        const content = row.original.contentByOpenAi;
        return (
          <span className={`h-4 w-4 ${content && content?.length > 0 ? 'text-red-500' : 'text-neutral-100'}`}>{content && content?.length > 0 ? '✔️' : 'x'}</span>
        );
      }
    },
    {
      header: "DEEPSEEK",
      cell: ({ row }: { row: { original: { contentByDeepSeek: string } } }) => {
        const content = row.original.contentByDeepSeek;
        return (
          <span className={`h-4 w-4 ${content && content?.length > 0 ? 'text-red-500' : 'text-neutral-100'}`}>{content && content?.length > 0 ? '✔️' : 'x'}</span>
        );
      }
    },
    {
      header: "作成日",
      cell: ({ row }: { row: { original: { createdAt: string } } }) => {
        const createdAt = row.original.createdAt;
        return createdAt ? dayjs(createdAt).format("YYYY-MM-DD") : "未作成";
      }
    },
    {
      header: "更新日",
      cell: ({ row }: { row: { original: { updatedAt: string } } }) => {
        const updatedAt = row.original.updatedAt;
        return updatedAt ? dayjs(updatedAt).format("YYYY-MM-DD") : "未更新";
      }
    },
  ];

  const getUniqueLinks = () => {
    return Array.from(new Set(aiSnsContents.map((content) => content.newsLink)));
  }

  const getLastCreatedAt = (link: string) => {
    return dayjs(aiSnsContents.filter((content) => content.newsLink === link).reduce((latest, current) => dayjs(current.createdAt).isAfter(dayjs(latest.createdAt)) ? current : latest).createdAt);
  }

  return (
    <div className="">
      <div className="flex justify-between items-center p-2 sm:p-4">
        <h1 className="text-2xl font-bold" aria-label="SNS内容生成">SNS内容生成</h1>
      </div>

      <Separator className="" />

      <div className="flex flex-col sm:flex-row">
        <div className="flex flex-row sm:flex-col sm:w-[200px]">
          <div className="flex flex-row gap-2 overflow-x-auto">
            <div className={`flex flex-row my-1 text-sm border-b justify-between items-center p-2 w-full ${selectedNewsLink === "ALL" ? "bg-neutral-200" : ""}`} onClick={() => setSelectedNewsLink("ALL")}>
              <a href="#" className="text-neutral-500 flex-1">ALL</a>
              <span className="text-xs text-neutral-500">{aiSnsContents.length}</span>
            </div>
          </div>

          {getUniqueLinks().sort((a, b) => getLastCreatedAt(a as string).isAfter(getLastCreatedAt(b as string)) ? -1 : 1).map((link) => (
            <div key={link} className={`flex flex-col my-1 text-sm border-b justify-between p-2 ${selectedNewsLink === link ? "bg-neutral-200" : ""}`} onClick={() => setSelectedNewsLink(link as string)}>
              <div className="flex flex-row items-center">
                <a href={link} className="text-neutral-800 underline flex-1">{link?.slice(0, 20)}...</a>
                <span className="text-xs text-neutral-500">{aiSnsContents.filter((content) => content.newsLink === link).length}</span>
              </div>
              <div>
                <div className="text-xs text-neutral-800 mt-2">
                  {aiSnsContents.filter((content) => content.newsLink === link).filter((content) => content.newsTitle).map((content) => content.newsTitle).join(", ").slice(0, 20)}...
                </div>
              </div>
              <div className="flex flex-row items-center text-xs text-neutral-500 mt-2">
                最終作成: {getLastCreatedAt(link as string).format("YYYY-MM-DD hhA")}
              </div>
            </div>
          ))}
        </div>

        <div className="w-full sm:w-[calc(100%-200px)] p-4">
          <DataTable columns={columns as any} data={aiSnsContents.filter((content) => selectedNewsLink === "ALL" ? true : content.newsLink === selectedNewsLink) as any} defaultPageSize={20} />
        </div>
      </div>
    </div>
  );
};
