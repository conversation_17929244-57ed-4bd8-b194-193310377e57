"use client";

import { deleteAiSnsMetrics, getAiSnsMetrics } from "@/actions/aiSNSMetrics";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { Separator } from "@/components/ui/separator";
import { AiSnsMetricsProps } from "@/lib/definitions";
import { ColumnDef } from "@tanstack/react-table";
import { Pencil, Plus, Trash } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "@/hooks/use-toast";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { useRouter } from "next/navigation";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";


export default function DataManagement() {
  const [data, setData] = useState<AiSnsMetricsProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const fetchData = async () => {
    setIsLoading(true);
    const response = await getAiSnsMetrics();
    if (response.success) {
      setData(response.data);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const columns: ColumnDef<AiSnsMetricsProps>[] = [
    {
      header: "日期",
      accessorKey: "recordDate",
    },
    {
      header: "小红书粉丝数",
      accessorKey: "xhsFollowers",
    },
    {
      header: "微信公众号粉丝数",
      accessorKey: "wechatFollowers",
    },
    {
      header: "微信视频号粉丝数",
      accessorKey: "wechatVideoFollowers",
    },
    {
      header: "TikTok粉丝数",
      accessorKey: "tiktokFollowers",
    },
    {
      header: "Facebook粉丝数",
      accessorKey: "facebookFollowers",
    },
    {
      header: "LinkedIn粉丝数",
      accessorKey: "linkedinFollowers",
    },
    {
      header: "微信社群人数",
      accessorKey: "wechatGroupMembers",
    },
    {
      header: "WhatsApp社群人数",
      accessorKey: "whatsappGroupMembers",
    },
    {
      header: "微信企业微信客户数",
      accessorKey: "wechatEnterpriseLeads",
    },
    {
      header: "操作",
      cell: ({ row }) => {
        return <div className="flex justify-center gap-2">
          {/* <Button variant="outline">
            <Pencil />
          </Button> */}
          <Button variant="outline" onClick={
            async () => {
              if (row.original.id) {
                const response = await deleteAiSnsMetrics(row.original.id);
                if (response.success) {
                  toast({
                    title: "删除成功",
                  });
                  fetchData();
                }
              }
            }
          }>
            <Trash />
          </Button>
        </div>;
      },
    },
  ];

  return (
    <div className="">
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="SNSデータ管理">SNSデータ管理</h1>

        <Button variant="outline" onClick={
          async () => {
            router.push("/ad/sns/dataManagement/create");
          }
        } size="sm">
          <Plus /> データを追加
        </Button>
      </div>

      <Separator className="mb-2" />

      <div className="p-4">
        <DataTable columns={columns} data={data} isLoading={isLoading} />
      </div>
    </div>
  );
}