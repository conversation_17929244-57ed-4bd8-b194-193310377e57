"use client";

import { FormControl, FormDescription, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { FormLabel } from "@/components/ui/form";
import { Form } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import { CalendarIcon, Loader2 } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

import { cn } from "@/lib/utils";
import { createAiSnsMetrics } from "@/actions/aiSNSMetrics";
import { toast } from "@/hooks/use-toast";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { snsMetricsMapper } from "../../(dashboard)/utilities";
import { useRouter } from "next/navigation";
import dayjs from "@/lib/thirdParty/dayjsWithTz";

export default function CreateAiSnsMetrics() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const form = useForm({
    resolver: zodResolver(z.object({
      recordDate: z.string(),
      xhsFollowers: z.number().min(1),
      wechatFollowers: z.number().min(1),
      wechatVideoFollowers: z.number().min(1),
      facebookFollowers: z.number().min(1),
      linkedinFollowers: z.number().min(1),
      wechatGroupMembers: z.number().min(1),
      whatsappGroupMembers: z.number().min(1),
      wechatEnterpriseLeads: z.number().min(1),
      tiktokFollowers: z.number().min(1),
    })),
    defaultValues: {
      recordDate: dayjsWithTz().format("YYYY-MM-DD"),
      xhsFollowers: null,
      wechatFollowers: null,
      wechatVideoFollowers: null,
      facebookFollowers: null,
      linkedinFollowers: null,
      wechatGroupMembers: null,
      whatsappGroupMembers: null,
      wechatEnterpriseLeads: null,
      tiktokFollowers: null,
    },
  }) as any;

  return (
    <div className="">
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold">新規データ作成</h1>
      </div>

      <Separator className="my-2" />

      <div className="p-4">
        <Form {...form}>
          <form onSubmit={async (e) => {

            e.preventDefault();
            // let formCheck = await form.trigger();
            // console.log("formCheck", formCheck);

            // if (!formCheck) {
            //   return;
            // }

            setIsLoading(true);
            const values = form.getValues();

            let updateData = {
              recordDate: dayjsWithTz(values.recordDate).tz("Asia/Tokyo").format("YYYY-MM-DD")
            } as any;

            ["xhsFollowers", "wechatFollowers", "wechatVideoFollowers", "facebookFollowers", "linkedinFollowers", "wechatGroupMembers", "whatsappGroupMembers", "wechatEnterpriseLeads", "tiktokFollowers"].forEach((field) => {
              if (values[field as keyof typeof values] !== null && values[field as keyof typeof values] > 0) {
                updateData[field as keyof typeof updateData] = parseInt(values[field as keyof typeof values]);
              }
            });

            let res = await createAiSnsMetrics(updateData);
            if (res.success) {
              toast({
                title: "メトリクス作成成功",
                description: "メトリクスが作成されました",
              });
              router.push(`/ad/sns/dataManagement`);
            } else {
              toast({
                title: "メトリクス作成失敗",
                description: res.message,
              });
            }
            setIsLoading(false);
          }} className="flex flex-col gap-4">

            <FormField
              control={form.control}
              name="recordDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>日付</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-[240px] pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            dayjsWithTz(field.value).format("YYYY-MM-DD")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        // onSelect={field.onChange}
                        onSelect={async (date: any) => {
                          let newDate = dayjs(date).format('YYYY-MM-DD')
                          field.onChange(newDate);
                        }}
                        disabled={(date: Date) =>
                          date > new Date() || date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            {["xhsFollowers", "wechatFollowers", "wechatVideoFollowers", "facebookFollowers", "linkedinFollowers", "wechatGroupMembers", "whatsappGroupMembers", "wechatEnterpriseLeads", "tiktokFollowers"].map((fieldName, index) => (
              <FormField
                key={index}
                control={form.control}
                name={fieldName}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {snsMetricsMapper[fieldName as keyof typeof snsMetricsMapper].label}
                    </FormLabel>
                    <FormControl>
                      <Input type="number" {...field} onChange={(e) => field.onChange(Number(e.target.value))} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ))}


            <Button type="submit" disabled={isLoading}>
              {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "作成"}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}