"use client"
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { renderARecordWithChange } from "./utility/renderFollowedItem"
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { findLastestChangeCompanyIsFollowed, findLastestChangeCompany } from "./utility/findLastestChangeCompanyIsFollowed";
import { useAuthStore } from "@/store/auth";
import { TllUserProps } from "@/lib/definitions/tllUser";


export default function FollowedCompanies() {
  const { userLambdaRecords } = useUserLambdaRecordStore();
  const { currentUser } = useAuthStore();

  const filteredRecords = userLambdaRecords.filter((record) => findLastestChangeCompanyIsFollowed(record));

  let companyObjects = {} as { [key: string]: any[] };
  filteredRecords.forEach((record: any) => {
    const company = findLastestChangeCompany(record);
    console.log("company", company);

    if (company) {
      if (companyObjects[company.fullName]) {
        companyObjects[company.fullName].push({
          ...record,
          company: company
        }) as any;
      } else {
        companyObjects[company.fullName] = [{
          ...record,
          company: company
        }] as any;
      }
    }
  });

  return (
    <div className="flex flex-col gap-8">
      {Object.keys(companyObjects).map((companyName) => {
        return (
          <div key={companyName} className="flex flex-col gap-2 bg-gray-50 p-2 rounded-md">
            <div className="flex flex-row items-center gap-2 pb-2 mb-2 border-b border-gray-300 ">
              <h1 className="text-lg font-bold flex-1">{companyName}</h1>
              <span className="text-sm text-gray-500">{companyObjects[companyName][0]?.company?.comments}</span>
            </div>

            {companyObjects[companyName].map((record) => {
              return (
                <div key={record.id} className="flex flex-col gap-2 bg-gray-50  rounded-md">
                  {renderARecordWithChange({ record, currentUser: currentUser as TllUserProps })}
                </div>
              )
            })}
          </div>
        )
      })}
      {/* <div>
        <h1 className="text-xl font-bold">監視会社</h1>
        <Separator className="my-2" />
        {userLambdaRecords.filter((record) => favMapIds.includes(record.id)).map((record) => renderARecordWithChange(record))}
      </div> */}
    </div>
  )
}