"use client"

import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { renderARecordWithChange } from "./utility/renderFollowedItem"
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { findLastestChangeCompanyIsFollowed } from "./utility/findLastestChangeCompanyIsFollowed";
import { Terminal } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { WATCHED_LOCATIONS, findWatchedLocation } from "./utility/findWatchedLocation";
import { useAuthStore } from "@/store/auth";
import { TllUserProps } from "@/lib/definitions/tllUser";

export default function FollowLocation() {
  const { currentUser } = useAuthStore();
  const { userLambdaRecords } = useUserLambdaRecordStore();

  const filteredRecords = userLambdaRecords.filter((record) => findWatchedLocation(record));
  let stationObjects = {} as { [key: string]: UserLambdaRecordProps[] };
  filteredRecords.forEach((record) => {
    if (WATCHED_LOCATIONS.some(location => record.address?.includes(location))) {
      const location = WATCHED_LOCATIONS.find(location => record.address?.includes(location));

      if (location && !stationObjects[location]) {
        stationObjects[location] = [];
      }

      if (location && stationObjects[location]) {
        stationObjects[location].push(record);
      }
    }
  });

  return (
    <div className="flex flex-col gap-8">
      <Alert>
        <Terminal className="h-4 w-4" />
        <AlertTitle> Currently only the following locations are watched:</AlertTitle>

        <AlertDescription>
          <div className="flex flex-wrap gap-2 mt-2">
            {WATCHED_LOCATIONS.map((location) => (
              <Badge key={location}>{location}</Badge>
            ))}
          </div>
        </AlertDescription>
      </Alert>


      {Object.keys(stationObjects).map((stationName) => {
        return (
          <div key={stationName} className="flex flex-col gap-2 bg-gray-50 p-2 rounded-md">
            <h1 className="text-lg font-bold border-b border-gray-300">{stationName}</h1>
            {
              stationObjects[stationName].map((record) => {
                return (
                  <div key={record.id} className="flex flex-col gap-2 bg-gray-50 p-2 rounded-md">
                    {renderARecordWithChange({ record, currentUser: currentUser as TllUserProps })}
                  </div>
                )
              })
            }
          </div>
        )
      })}
    </div>
  )
}