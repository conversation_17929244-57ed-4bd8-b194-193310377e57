"use client"

import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"; // 引入shadcn按钮组件
import { CalendarIcon, FilterIcon, GridIcon, ListIcon } from "lucide-react"; // 引入Grid和List图标
import { Calendar } from "@/components/ui/calendar";
import { useState, useEffect } from "react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import dayjs from "dayjs"; // 引入dayjs
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord"; // 引入用户Lambda记录的sto
import { Badge } from "@/components/ui/badge";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { User<PERSON><PERSON>bdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { userlambdaRecordTableColumnsCommonSearch } from "@/components/userLambdaRecord/userlambdaRecordTableColumnsCommon";
import { DataTable } from "@/components/ui/data-table";
import { checkUserLambdaRecordFavMapsAction } from "@/actions/tllUserLambdaRecordFavMaps";
import FollowedCompanies from "./FollowedCompanies";
import { findLastestChangeCompanyIsFollowed } from "./utility/findLastestChangeCompanyIsFollowed";
import { useAuthStore } from "@/store/auth";
import FollowLocation from "./FollowLocation";
import { findWatchedLocation } from "./utility/findWatchedLocation";
import { Toggle } from "@/components/ui/toggle";
import { getLatestChangeValue, getPriceChangeHistory } from "@/components/userLambdaRecord/priceChangeUtilities";
import { getBuildingPrice } from "@/lib/helper/sekisan";
import { getSelectedDataForReinsSold } from "@/app/api/cron/reinsSold/getSelectedDataForReinsSold";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { getUserLambdaRecordForAdminDashboard } from "@/actions/tllUserLambdaRecordForDashboard";
import { toast } from "@/hooks/use-toast";
import { getStatus } from "@/lib/userLambdaRecord/getStatus"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { filter } from "lodash-es";

const MenuItem = ({ filter, selectedFilter, onSelect }: { filter: number, selectedFilter: number, onSelect: (filter: number) => void }) => {
  const isSelected = selectedFilter === filter;

  return (
    <DropdownMenuItem
      onClick={() => onSelect(filter)}
      className={`block px-4 py-2 text-left w-full hover:bg-gray-100 ${isSelected ? "bg-neutral-100" : ""}`}
    >
      {renderOptionForMenuItem(filter)}
    </DropdownMenuItem>
  );
};

const renderOptionForMenuItem = (filter: number) => {
  return <>
    {filter >= 24 ? dayjs().subtract(filter / 24, 'day').format('YYYY-MM-DD') : `Past ${filter} Hours`}
    <span className="ml-2">
      ({dayjs().subtract(filter / 24, 'day').format("ddd")})
    </span>
  </>
}

export default function OverviewPage() {
  const [selectedDate, setSelectedDate] = useState(dayjs().format('YYYY-MM-DD'));
  const [recordTypeFilter, setRecordTypeFilter] = useState("BUILDING");
  const [statusFilter, setStatusFilter] = useState("公開中");
  const [isGridView, setIsGridView] = useState(false); // 新增状态管理视图类型
  const [sourceFilter, setSourceFilter] = useState("ALL");
  const [filterChangeType, setFilterChangeType] = useState("ALL");
  const [filterSellerProperty, setFilterSellerProperty] = useState(false);
  const [filterCanAdvertise, setFilterCanAdvertise] = useState(false);
  const [filterCanHotel, setFilterCanHotel] = useState(false);
  const { setAllUserLambdaRecords, userLambdaRecords } = useUserLambdaRecordStore(); // 获取store中的方法
  const [isLoading, setIsLoading] = useState(false); // 新增加载状态
  const [favPropertiesChangedIds, setFavPropertiesChangedIds] = useState<string[]>([]);
  const { currentUser } = useAuthStore();

  const fetchUserLambdaRecords = async (selectedDate: string) => {
    try {
      setIsLoading(true); // 数据加载开始时设置加载状态为true
      const response = await getUserLambdaRecordForAdminDashboard({
        dateOfRecord: selectedDate,
      });

      if (response.success) {
        setAllUserLambdaRecords(response.data);

        const favMapResponse = await checkUserLambdaRecordFavMapsAction(response.data.map((record: UserLambdaRecordProps) => record.id));
        if (favMapResponse.success) {
          setFavPropertiesChangedIds(favMapResponse.data);
        }
      } else {
        console.error('Failed to fetch user lambda records:', response.message);
      }
    } catch (error) {
      console.error('Failed to fetch user lambda records:', error);
      toast({
        title: 'Failed to fetch user lambda records:',
        description: error instanceof Error ? error.message : 'Unknown error',
      });
    } finally {
      setIsLoading(false); // 数据加载完成后设置加载状态为false
    }
  };

  useEffect(() => {
    if (userLambdaRecords.length === 0) {
      fetchUserLambdaRecords(selectedDate);
    }
  }, []);

  const checkRecordIsNewCompanyEngaged = (record: UserLambdaRecordProps) => {
    // Logic, the recent brokerType contains "一般", however there are other brokerType like "専任" in the previous 3 months

    let priceChangeRecords = record.priceChanges;
    if (!priceChangeRecords || priceChangeRecords.length <= 1) {
      return false;
    }

    // There are > 2 companies and the recent record company is new 
    let allCompanyIds = priceChangeRecords?.map((record) => record.companyId);
    let latestCompanyId = priceChangeRecords?.sort((a, b) => dayjs(b.recordDate).diff(dayjs(a.recordDate)))[0].companyId;

    // New company just showed up 
    if (allCompanyIds && allCompanyIds.length > 2 && latestCompanyId && allCompanyIds.filter(id => id === latestCompanyId).length === 1) {
      return true
    }

    return false;
  }

  const checkRecordIsSenninToIppan = (record: UserLambdaRecordProps) => {
    // Logic, the recent brokerType contains "一般", however there are other brokerType like "専任" in the previous 3 months

    let priceChangeRecords = record.priceChanges;

    if (!priceChangeRecords || priceChangeRecords.length <= 1) {
      return false;
    }

    let latestBrokerType = getLatestChangeValue(record, "brokerType");

    let getBrokerTypes = (priceChangeRecords: UserLambdaRecordPriceChangeProps[]): string[] =>
      priceChangeRecords.filter(r => dayjs(r.recordDate).isAfter(dayjs().subtract(3, 'month')))
        .map((record) => record.brokerType)
        .filter((type): type is string => type !== undefined && type !== null);

    // Current it is 一般、 there is 専任 or 専属 in past 3 months
    if (latestBrokerType && latestBrokerType.indexOf("一般") > -1) {
      const brokerTypes = getBrokerTypes(priceChangeRecords);
      if (brokerTypes && brokerTypes.some(type => type && (type.includes("専任") || type.includes("専属")))) {
        return true;
      }
    }

    return false;
  }

  const checkRecordIsSellerProperty = (record: UserLambdaRecordProps) => {
    if (record.priceChanges && record.priceChanges.length > 0) {
      let latestPriceChange = record.priceChanges.sort((a, b) => dayjs(b.recordDate).diff(dayjs(a.recordDate)))[0];
      if (latestPriceChange.brokerType && latestPriceChange.brokerType.indexOf("売主") > -1) {
        return true;
      }
    }
    return false;
  }


  const checkRecordCanAdvertise = (record: UserLambdaRecordProps) => {
    if (record.priceChanges && record.priceChanges.length > 0) {
      let latestPriceChange = record.priceChanges.sort((a, b) => dayjs(b.recordDate).diff(dayjs(a.recordDate)))[0];
      if (latestPriceChange.canAdvertise && latestPriceChange.canAdvertise.indexOf("広告可") > -1) {
        return true;
      }
    }
    return false;
  }

  const filterData = (userLambdaRecords: UserLambdaRecordProps[]) => {
    return userLambdaRecords.filter(r => {
      if (recordTypeFilter !== "ALL" && r.recordType !== recordTypeFilter) {
        return false;
      }
      if (statusFilter !== "ALL" && getStatus(r) !== statusFilter) {
        return false;
      }

      if (sourceFilter !== "ALL") {
        if (sourceFilter === "住友競売" && r.sourceData !== '住友競売') {
          return false;
        }
        if (sourceFilter === "一般競売" && r.sourceData !== '一般競売') {
          return false;
        }
        if (sourceFilter === "REINS" && r.sourceData !== 'REINS') {
          return false;
        }
        if (sourceFilter === "大手サイト" && ["NOMU", "MITSUI", "MITSUBISHI", "SUMITOMO", "LIVABLE"].indexOf(r.sourceData as string) < 0) {
          return false;
        }
      }

      if (filterChangeType === "senninToIppan" && !checkRecordIsSenninToIppan(r)) {
        return false;
      }

      if (filterChangeType === "sellerProperty" && !checkRecordIsSellerProperty(r)) {
        return false;
      }

      if (filterChangeType === "newCompanyEngaged" && !checkRecordIsNewCompanyEngaged(r)) {
        return false;
      }

      if (filterCanAdvertise && !checkRecordCanAdvertise(r)) {
        return false;
      }



      // if (filterTaiyouGreater5) {
      //   if (r.buildingMaterial === undefined || r.buildingMaterial === null || r.buildingBuiltYear === undefined || r.buildingBuiltYear === null || getBuildingPrice({
      //     recordType: r.recordType,
      //     buildingMaterial: r.buildingMaterial,
      //     buildingBuiltYear: r.buildingBuiltYear,
      //     buildingSize: r.buildingSize || 0,
      //     unitArea: r.recordValues?.unitArea || 0,
      //   }).remainY < 5) {
      //     return false;
      //   }
      // }

      if (filterCanHotel && (!r.landType || r.landType.length === 0 || ["一低", "一中", "二低", "二中"].includes(r.landType))) {
        return false;
      }

      const checkIfNewRecordOrPriceChangeLessThan10 = (record: any) => {
        let changeHistory = getPriceChangeHistory(record.price, record.priceChanges)?.rawData;

        return record.priceChanges && (record.priceChanges.length === 1 || (changeHistory?.priceChangePerc && changeHistory?.priceChangePerc > -0.1) || new Set(record.priceChanges.map((p: any) => p.price)).size === 1); // If price increased ignore too or one record only, or no change
      }

      const checkBidLessThan80Percent = (record: any) => {
        return record.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice && record.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice / record.price < 0.8;
      }

      // if (filterHideNormalProperty) {
      //   if (r.price < 2000) return false;
      //   if (r.nearestStationWalkMinute && r.nearestStationWalkMinute >= 16) return false;
      //   let priceChangeRecent = getPriceChangeHistory(r.price, r.priceChanges as UserLambdaRecordPriceChangeProps[]);
      //   if (priceChangeRecent && priceChangeRecent.rawData.priceChangeRecent && priceChangeRecent.rawData.priceChangeRecent > 0) {
      //     return false;
      //   }

      //   if (r.recordType === "BUILDING" && r.price < 4000) return false;
      //   if (r.recordType !== "MANSION" && r.landSize && r.landSize < 40) return false;
      //   if (r.recordType === "LAND" && (!r.landType || r.landType.length === 0 || ["一低", "一中", "二低", "二中"].includes(r.landType))) return false;
      //   if (r.recordType === "HOUSE" && r.buildingSize && r.buildingSize < 80) return false;
      //   if (r.recordType === "BUILDING" && r.buildingSize && r.buildingSize < 80) return false;

      //   let perc = r?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice && r.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice / r.price;

      //   if (r.recordType === "BUILDING") {
      //     if (r.yearlyIncome && r.yearlyIncome > 0 && r.propertyAnalysisResult?.overallStarLevel === 0 && checkIfNewRecordOrPriceChangeLessThan10(r) && checkBidLessThan80Percent(r)) {
      //       return false;
      //     }
      //   } else {
      //     if (r.propertyAnalysisResult?.overallStarLevel === 0 && checkIfNewRecordOrPriceChangeLessThan10(r) && checkBidLessThan80Percent(r)) {
      //       return false;
      //     }
      //   }

      //   if (r.recordType === "BUILDING") {
      //     if (perc && perc < 0.6 && perc > 0.1) {
      //       return false;
      //     }
      //   }
      //   if (r.recordType === "LAND") {
      //     if (perc && perc < 0.8 && perc > 0.1 && r.landType && r.landType.length > 0) {
      //       return false;
      //     }
      //   }
      //   if (r.recordType === "HOUSE") {
      //     if (perc && perc < 0.8 && perc > 0.1 && (!r.landType || r.landType.length === 0 || ["一低", "一中", "二低", "二中"].includes(r.landType))) {
      //       return false;
      //     }
      //   }
      // }

      if (filterChangeType === "loanBreak") {
        // has > 2 records, recent most is 成約
        if (r.priceChanges && r.priceChanges.length > 2) {
          let sortedPriceChanges = r.priceChanges.sort((a, b) => dayjs(b.recordDate).diff(dayjs(a.recordDate)));
          console.log("sortedPriceChanges", sortedPriceChanges);

          let getTimeDiffByDate = (date1: Date, date2: Date) => {
            let diff = dayjs(date2).diff(dayjs(date1), 'day');
            console.log("diff", diff);
            return diff;
          }


          // Logic is that there is seiyaku and less than 30 days and current not seiyaku
          let findSeiyaku = sortedPriceChanges.find((record) => record.status === "成約");

          if (findSeiyaku && getTimeDiffByDate(findSeiyaku.recordDate, sortedPriceChanges[0].recordDate) <= 90 && (sortedPriceChanges[0].status !== "成約" || getStatus(r) !== "成約")) {
            return true;
          }
        }
        return false;
      }

      return true;
    })
  }

  const filter80Percent = (record: UserLambdaRecordProps) => {
    let bidPrice = record.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice;
    let price = record.price;
    return bidPrice && price && bidPrice / price >= 0.8;
  }

  const filter10PercentDown = (record: UserLambdaRecordProps) => {
    let res1 = getPriceChangeHistory(record.price, record.priceChanges)?.rawData.threeMonthPerc;
    return res1 && res1 <= -0.1;
  }

  const filter10CenterWard = (record: UserLambdaRecordProps) => {
    return ["東京都千代田区", "東京都港区", "東京都新宿区", "東京都渋谷区", "東京都中央区", "東京都目黒区", "東京都文京区", "東京都杉並区", "東京都世田谷区", "東京都大田区"].some((v) => record.address && record.address.includes(v));
  }

  const filterRanked = (record: UserLambdaRecordProps) => {
    return record.propertyAnalysisResult?.overallStarLevel && record.propertyAnalysisResult?.overallStarLevel > 0;
  }

  const sections = [
    {
      title: "TLL目線が80%以上物件",
      dataFilter: (record: UserLambdaRecordProps) => filter80Percent(record)
    },
    {
      title: "３ヶ月内10％価格ダウン",
      dataFilter: (record: UserLambdaRecordProps) => !filter80Percent(record) && filter10PercentDown(record)
    },
    {
      title: "都心10区物件",
      dataFilter: (record: UserLambdaRecordProps) => !filter80Percent(record) && !filter10PercentDown(record) && filter10CenterWard(record)
    },
    {
      title: "ランク付け物件",
      dataFilter: (record: UserLambdaRecordProps) => !filter80Percent(record) && !filter10PercentDown(record) && !filter10CenterWard(record) && filterRanked(record)
    },
    {
      title: "残りの地味物件",
      dataFilter: (record: UserLambdaRecordProps) => !filter80Percent(record) && !filter10PercentDown(record) && !filter10CenterWard(record) && !filterRanked(record)
    },
  ]

  return (
    <div>
      <div className="flex justify-between items-center p-4 -mb-2">
        <h1 className="text-2xl font-bold">ダッシュボード</h1>

        <div className="flex items-center">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-[140px] pl-3 text-left font-normal",
                  !selectedDate && "text-muted-foreground"
                )}
              >
                {selectedDate ? (
                  dayjs(selectedDate).format("YYYY-MM-DD")
                ) : (
                  <span>日付を選択</span>
                )}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dayjs(selectedDate).toDate()}
                onSelect={(date: any) => {
                  let newDate = dayjs(date).format('YYYY-MM-DD')
                  setSelectedDate(newDate);
                  fetchUserLambdaRecords(newDate);
                }}
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>

          {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="p-2">
                <FilterIcon className="w-4 h-4" />
                <p className="hidden sm:block">{renderOptionForMenuItem(selectedFilter)}</p>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="absolute right-0 mt-2 w-48 bg-white border rounded shadow-lg">
              <MenuItem filter={6} selectedFilter={selectedFilter} onSelect={handleFilterSelect} />
              <MenuItem filter={12} selectedFilter={selectedFilter} onSelect={handleFilterSelect} />
              <MenuItem filter={24} selectedFilter={selectedFilter} onSelect={handleFilterSelect} />
              <MenuItem filter={48} selectedFilter={selectedFilter} onSelect={handleFilterSelect} />
              <MenuItem filter={72} selectedFilter={selectedFilter} onSelect={handleFilterSelect} />
              <MenuItem filter={96} selectedFilter={selectedFilter} onSelect={handleFilterSelect} />
              <MenuItem filter={120} selectedFilter={selectedFilter} onSelect={handleFilterSelect} />
            </DropdownMenuContent>
          </DropdownMenu> */}

          {/* <Tabs defaultValue={isGridView ? "grid" : "list"} className="ml-2">
            <TabsList>
              <TabsTrigger value="list" onClick={() => setIsGridView(false)}>
                <ListIcon className="w-4 h-4" />
              </TabsTrigger>
              <TabsTrigger value="grid" onClick={() => setIsGridView(true)}>
                <GridIcon className="w-4 h-4" />
              </TabsTrigger>
            </TabsList>
          </Tabs> */}
        </div>
      </div>

      <Tabs defaultValue="newProperties" className="">
        <div className="w-full overflow-x-auto scrollbar-hide px-2 border-t border-b border-gray-200 py-2">
          <TabsList className="flex space-x-4 py-2 min-w-max justify-start bg-white">
            <TabsTrigger value="newProperties">新規物件
              <Badge variant="outline">
                {userLambdaRecords.length}
              </Badge>
            </TabsTrigger>

            {
              currentUser?.accessLevel && currentUser?.accessLevel >= 99 && (
                <>
                  <TabsTrigger value="followedCompanies">
                    フォロー会社
                    <Badge variant="outline">
                      {userLambdaRecords.filter((record) => findLastestChangeCompanyIsFollowed(record)).length}
                    </Badge>
                  </TabsTrigger>

                  <TabsTrigger value="followedLocations">
                    フォロー場所
                    <Badge variant="outline">
                      {userLambdaRecords.filter((record) => findWatchedLocation(record)).length}
                    </Badge>
                  </TabsTrigger>
                </>
              )
            }
          </TabsList>
        </div>

        <TabsContent value="newProperties" className="px-4 py-2">
          <div className="flex flex-row justify-start items-center text-sm mb-2 gap-2 overflow-x-auto scrollbar-hide">
            <ToggleGroup type="single" value={recordTypeFilter} className="border border-gray-300 rounded-md text-xs" onValueChange={(value) => setRecordTypeFilter(value)}>
              {/* <ToggleGroupItem value="ALL" className="border-r border-gray-300 rounded-none">
                ALL
              </ToggleGroupItem> */}
              {
                ["BUILDING", "HOUSE", "LAND"].map((recordType) => (
                  <ToggleGroupItem value={recordType} key={recordType} className="whitespace-nowrap rounded-none">
                    {recordType}
                    <Badge variant="outline">{userLambdaRecords.filter((record) => record.recordType === recordType).length}</Badge>
                  </ToggleGroupItem>
                ))
              }
            </ToggleGroup>

            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value)}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="ステータスを選択" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">ステータス: ALL</SelectItem>

                {["公開中", "成約"].map((status) => (
                  <SelectItem key={status} value={status} className="flex flex-row items-center">
                    <span className="text-left">{status}</span>
                    <Badge variant="outline" className="ml-auto">{userLambdaRecords.filter((record) => {
                      if (recordTypeFilter !== "ALL") {
                        return record.recordType === recordTypeFilter && getStatus(record) === status;
                      }
                      return getStatus(record) === status;
                    }).length}</Badge>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sourceFilter} onValueChange={(value) => setSourceFilter(value)}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="ソースを選択" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">ソース: ALL</SelectItem>
                {["REINS", "大手サイト", "住友競売"].map((source) => (
                  <SelectItem key={source} value={source}>
                    {source}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* <ToggleGroup type="single" value={recordTypeFilter} className="border border-gray-300 rounded-md p-1 text-xs" onValueChange={(value) => setRecordTypeFilter(value)}>
              <ToggleGroupItem value="ALL" className="border-r border-gray-300 rounded-none">
                ALL
              </ToggleGroupItem>
              {
                ["価格変更"].map((recordType) => (
                  <ToggleGroupItem value={recordType} key={recordType}>
                    {recordType}
                    <Badge variant="outline">{userLambdaRecords.filter((record) => record.recordType === recordType).length}</Badge>
                  </ToggleGroupItem>
                ))
              }
            </ToggleGroup> */}


            <Select value={filterChangeType} onValueChange={(value) => {
              setFilterChangeType(value);
            }}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="フィルターを選択" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">変更要件: ALL</SelectItem>
                <SelectItem value="senninToIppan" className="flex flex-row items-center">
                  <span className="text-left">専任から一般</span>
                  <Badge variant="outline" className="ml-auto">{userLambdaRecords.filter((record) => checkRecordIsSenninToIppan(record)).length}</Badge>
                </SelectItem>
                <SelectItem value="newCompanyEngaged" className="flex flex-row items-center">
                  <span className="text-left">新仲介会社を頼む</span>
                  <Badge variant="outline" className="ml-auto">{userLambdaRecords.filter((record) => checkRecordIsNewCompanyEngaged(record)).length}</Badge>
                </SelectItem>
                <SelectItem value="loanBreak">ローンブレーキ</SelectItem>
              </SelectContent>
            </Select>

            <Toggle className="shrink-0" pressed={filterSellerProperty} onPressedChange={() => setFilterSellerProperty(!filterSellerProperty)}>売主
            </Toggle>

            <Toggle className="shrink-0" pressed={filterCanAdvertise} onPressedChange={() => setFilterCanAdvertise(!filterCanAdvertise)}>広告可
            </Toggle>

            <Toggle className="shrink-0" pressed={filterCanHotel} onPressedChange={() => setFilterCanHotel(!filterCanHotel)}>旅館可
            </Toggle>

          </div>

          <Separator className="my-2" />
          {sections.map((section, index) => (
            <div className="mb-2 w-full border border-gray-200 bg-white rounded-md flex flex-col " key={index}>
              <div className="flex flex-row gap-2 p-2 bg-gray-100 text-black font-bold text-lg w-full">
                #{index + 1} {section.title}
              </div>

              <div className="flex flex-row gap-2 p-2 w-full overflow-x-auto scrollbar-hide">
                <DataTable
                  columns={userlambdaRecordTableColumnsCommonSearch.filter((column) => !["簡易変更履歴"].includes(column.header as string))}
                  data={filterData(userLambdaRecords).filter(section.dataFilter).sort((a: any, b: any) => (b.propertyAnalysisResult?.overallStarLevel || 0) - (a.propertyAnalysisResult?.overallStarLevel || 0))}
                  meta={{ favMapIds: favPropertiesChangedIds }}
                  defaultPageSize={200}
                  isLoading={isLoading} />
              </div>
            </div>
          ))}
        </TabsContent>

        <TabsContent value="followedCompanies" className="w-full p-2 sm:p-4">
          <FollowedCompanies />
        </TabsContent>
        <TabsContent value="followedLocations" className="w-full p-2 sm:p-4">
          <FollowLocation />
        </TabsContent>
      </Tabs>
    </div >
  );
};


