import dayjs from "dayjs";
import { ProCompanyProps } from "@/lib/definitions/proCompany";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";


export const findLastestChangeCompany = (record: UserLambdaRecordProps): ProCompanyProps | undefined => {
  const mostRecentPriceChange = record.priceChanges?.reduce((latest, current) => {
    return dayjs(current.recordDate).isAfter(dayjs(latest.recordDate)) ? current : latest;
  });
  return mostRecentPriceChange?.company;
}


export const findLastestChangeCompanyIsFollowed = (record: UserLambdaRecordProps) => {
  if (!record.priceChanges?.length) {
    return false;
  }

  const mostRecentPriceChange = record.priceChanges?.reduce((latest, current) => {
    return dayjs(current.recordDate).isAfter(dayjs(latest.recordDate)) ? current : latest;
  });
  return mostRecentPriceChange?.company?.isFav === 1;
}
