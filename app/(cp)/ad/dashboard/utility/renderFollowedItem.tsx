"use client"

import { DataTable } from "@/components/ui/data-table"
import { userlambdaRecordTableColumnsCommonSearch } from "@/components/userLambdaRecord/userlambdaRecordTableColumnsCommon"
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord"
import { TllUserProps } from "@/lib/definitions/tllUser"
import { getPriceChangeColumnsBasedOnAccessLevel, priceChangeColumns } from "../../../ex/search/[id]/(details)/(priceChange)/priceChangeColumns"
import Link from "next/link"
import dayjs from "dayjs"
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz"
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns"
import { deletePriceChange } from "@/actions/tllUserLambdaRecordPriceChange"
import { Button } from "@/components/ui/button"
import { Trash2Icon } from "lucide-react"
import { useAuthStore } from "@/store/auth"


export const renderARecordWithChange = ({ record, currentUser }: { record: UserLambdaRecordProps, currentUser: TllUserProps }) => {
  const priceChangeColumns = [
    ...getPriceChangeColumnsBasedOnAccessLevel(currentUser as TllUserProps),
    ...currentUser && currentUser?.accessLevel >= 90 ? [{
      header: '操作',
      cell: ({ row }: { row: any }) => {
        return <Button variant="destructive" size="icon" className="flex flex-row gap-2" onClick={async () => {
          let res = window.confirm("本当に削除しますか？");
          if (res) {
            await deletePriceChange(row.original.id);
            window.location.reload();
          }
        }}>
          <Trash2Icon className="w-4 h-4" />
        </Button>
      }
    }] : []
  ]

  const recordColumns = getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps });

  return (
    <div className="flex flex-col gap-2" key={record.id}>
      <DataTable columns={recordColumns} data={[record]} showFooter={false} />

      {(!currentUser || currentUser?.accessLevel && currentUser?.accessLevel >= 90) ?
        <DataTable columns={priceChangeColumns} data={record.priceChanges || []} showFooter={false} />
        : <div>
          {dayjs().format("YYYY/MM/DD")}に、
          {record.priceChanges?.length}件の変更履歴がありました。
          <Link href={`/ex/search/${record.id}`} className="underline">詳細を見る</Link>
        </div>}
    </div>
  )
}