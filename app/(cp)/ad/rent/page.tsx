"use client";

import { useState } from "react";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useEffect } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { getProBuildingHouseRentForDay } from "@/actions/proBuildingHouseRent";
import { DataTable } from "@/components/ui/data-table";
import { userlambdaRecordTableColumnsCommonSearch } from "@/components/userLambdaRecord/userlambdaRecordTableColumnsCommon";
import { buildingRentColumns, houseRentColumns, mansionRentColumns } from "../../an/mansion/[id]/mansionRentColumns";
import dayjs from "dayjs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { getProMansionRentForDay } from "@/actions/proMansionRent";
import { Select, SelectItem, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Toggle } from "@/components/ui/toggle";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
export default function RentPage() {
  const router = useRouter();

  const searchParams = useSearchParams();
  const [selectedTab, setSelectedTab] = useState<"house" | "buldingWhole" | "mansion" | "buildingPart">(searchParams.get("tab") as "house" | "buldingWhole" | "mansion" | "buildingPart" || "house");
  const [todayRentHouse, setTodayRentHouse] = useState<any[]>([]);
  const [todayRentBuilding, setTodayRentBuilding] = useState<any[]>([]);
  const [todayRentMansion, setTodayRentMansion] = useState<any[]>([]);
  const [todayRentBuildingPart, setTodayRentBuildingPart] = useState<any[]>([]);

  const [filterSelectedArea, setFilterSelectedArea] = useState<string>("60");
  const [filterMaxPrice, setFilterMaxPrice] = useState<string>("50");
  const [filterSelectedBuiltYear, setFilterSelectedBuiltYear] = useState<string>("all");
  const [filterSelectedStationWalkMinute, setFilterSelectedStationWalkMinute] = useState<string>("15");
  const [areaType, setAreaType] = useState<"toshi6" | "toshi10" | "tokyo23">("toshi6");

  const [selectedDate, setSelectedDate] = useState<string>(dayjs().format("YYYY-MM-DD"));
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const fetchData = async (selectedDate: string) => {
    setIsLoading(true);

    let response = await getProBuildingHouseRentForDay({ date: selectedDate });
    if (response.success) {
      setTodayRentHouse(response.data.filter((item: any) => item.recordType === "HOUSE"));
      setTodayRentBuilding(response.data.filter((item: any) => item.recordType === "BUILDING"));
    }

    let response2 = await getProMansionRentForDay({ date: selectedDate });
    if (response2.success) {
      setTodayRentMansion(response2.data.filter((item: any) => item.recordType === "MANSION"));
      setTodayRentBuildingPart(response2.data.filter((item: any) => item.recordType === "BUILDING_PART"));
    }

    setIsLoading(false);
  };

  useEffect(() => {
    fetchData(selectedDate);
  }, [selectedDate]);

  function extractWalkingMinutes(text: string) {
    const match = text.match(/徒歩\s*(\d+)\s*分/);
    if (match && match[1]) {
      return parseInt(match[1], 10);
    }

    return 0; // or return 0 if preferred
  }


  const filterRecord = (record: any) => {
    if (filterSelectedArea !== "all" && (record.unitSize || record.buildingSize) < parseInt(filterSelectedArea)) {
      return false;
    }

    if (filterMaxPrice !== "all" && record.feeRent > parseInt(filterMaxPrice)) {
      return false;
    }

    if (filterSelectedBuiltYear !== "all" && record.buildingBuiltYear && record.buildingBuiltYear < parseInt(filterSelectedBuiltYear)) {
      return false;
    }


    if (filterSelectedStationWalkMinute !== "all" && record.nearestStationWalkMinute && extractWalkingMinutes(record.nearestStationWalkMinute) && extractWalkingMinutes(record.nearestStationWalkMinute) > parseInt(filterSelectedStationWalkMinute)) {
      return false;
    }

    if (areaType === "toshi6" && !["東京都千代田区", "東京都港区", "東京都新宿区", "東京都渋谷区", "東京都中央区", "東京都目黒区"].some((v) => (record.address || record.buildingAddress).includes(v))) {
      return false;
    }

    if (areaType === "toshi10" && !["東京都千代田区", "東京都港区", "東京都新宿区", "東京都渋谷区", "東京都中央区", "東京都目黒区", "東京都文京区", "東京都杉並区", "東京都世田谷区", "東京都大田区"].some((v) => (record.address || record.buildingAddress).includes(v))) {
      return false;
    }

    if (areaType === "tokyo23" && (!((record.address || record.buildingAddress).includes("東京都") && (record.address || record.buildingAddress).includes("区")))) {
      return false;
    }

    return true;
  }

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="賃貸物件">
        賃貸物件
      </h1>

      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={"outline"}
            className={cn(
              "w-[140px] pl-3 text-left font-normal",
              !selectedDate && "text-muted-foreground"
            )}
          >
            {selectedDate ? (
              dayjs(selectedDate).format("YYYY-MM-DD")
            ) : (
              <span>日付を選択</span>
            )}
            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={dayjs(selectedDate).toDate()}
            onSelect={async (date: any) => {
              let newDate = dayjs(date).format('YYYY-MM-DD')
              setSelectedDate(newDate);
            }}
            disabled={(date) =>
              date > new Date() || date < new Date("1900-01-01")
            }
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>

    <Separator className="" />

    <div className="px-4 py-2 sticky top-0 bg-white z-10 w-full border-b border-gray-200">
      <Tabs value={selectedTab} onValueChange={(value) => {
        setSelectedTab(value as "house" | "buldingWhole" | "mansion" | "buildingPart");

        router.push(`/ad/rent?tab=${value}`, { scroll: false });
        // setUniqPropertyAddresssFunc(reinsMetrics, value as "売" | "賃貸");
      }}>
        <TabsList>
          <TabsTrigger value="house">
            戸建
            <Badge
              variant="outline"
              className="ml-2"
            >
              {todayRentHouse.length}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="buldingWhole">
            一棟(全部)
            <Badge
              variant="outline"
              className="ml-2"
            >
              {todayRentBuilding.length}
            </Badge>
          </TabsTrigger>

          <TabsTrigger value="mansion">
            マンション
            <Badge
              variant="outline"
              className="ml-2"
            >
              {todayRentMansion.length}
            </Badge>
          </TabsTrigger>

          <TabsTrigger value="buildingPart">
            一棟(一部)
            <Badge
              variant="outline"
              className="ml-2"
            >
              {todayRentBuildingPart.length}
            </Badge>
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>

    <div className="px-4 py-2 flex flex-col gap-2">
      <div className="flex flex-row justify-start items-center gap-4">
        <Select value={filterMaxPrice} onValueChange={(value) => {
          setFilterMaxPrice(value);
        }}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="賃料を選択" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">賃料: 全て</SelectItem>
            <SelectItem value="30">30万以下</SelectItem>
            <SelectItem value="50">50万以下</SelectItem>
            <SelectItem value="60">60万以下</SelectItem>
            <SelectItem value="70">70万以下</SelectItem>
            <SelectItem value="80">80万以下</SelectItem>
            <SelectItem value="90">90万以下</SelectItem>
            <SelectItem value="100">100万以下</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterSelectedArea} onValueChange={(value) => {
          setFilterSelectedArea(value);
        }}>
          <SelectTrigger className="w-[100px]">
            <SelectValue placeholder="賃貸面積を選択" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">賃貸面積: 全て</SelectItem>
            <SelectItem value="50">50平米+</SelectItem>
            <SelectItem value="60">60平米+</SelectItem>
            <SelectItem value="70">70平米+</SelectItem>
            <SelectItem value="80">80平米+</SelectItem>
            <SelectItem value="90">90平米+</SelectItem>
            <SelectItem value="100">100平米+</SelectItem>
            <SelectItem value="120">120平米+</SelectItem>
            <SelectItem value="150">150平米+</SelectItem>
            <SelectItem value="200">200平米+</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterSelectedBuiltYear} onValueChange={(value) => {
          setFilterSelectedBuiltYear(value);
        }}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="建築年数を選択" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">建築年数: 全て</SelectItem>
            <SelectItem value="1981">1981年以降</SelectItem>
            <SelectItem value="1990">1990年以降</SelectItem>
            <SelectItem value="2000">2000年以降</SelectItem>
            <SelectItem value="2010">2010年以降</SelectItem>
            <SelectItem value="2020">2020年以降</SelectItem>
            <SelectItem value="2025">2025年以降</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterSelectedStationWalkMinute} onValueChange={(value) => {
          setFilterSelectedStationWalkMinute(value);
        }}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="建築年数を選択" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">駅から: 全て</SelectItem>
            <SelectItem value="5">5分以内</SelectItem>
            <SelectItem value="7">7分以内</SelectItem>
            <SelectItem value="10">10分以内</SelectItem>
            <SelectItem value="12">12分以内</SelectItem>
            <SelectItem value="15">15分以内</SelectItem>
            <SelectItem value="20">20分以内</SelectItem>
          </SelectContent>
        </Select>

        <ToggleGroup
          type="single"
          value={areaType}
          onValueChange={(value: any) => {
            setAreaType(value);
          }}
        >
          <ToggleGroupItem value="toshi6">都心6区</ToggleGroupItem>
          <ToggleGroupItem value="toshi10">都心10区</ToggleGroupItem>
          <ToggleGroupItem value="tokyo23">東京23区</ToggleGroupItem>
        </ToggleGroup>

        <Button variant="outline" className="shrink-0" onClick={() => {
          setFilterSelectedStationWalkMinute("10");
          setFilterSelectedBuiltYear("all");
          setFilterMaxPrice("50");
          setFilterSelectedArea("80");
          setAreaType("tokyo23");
        }}>
          民泊条件を適用
        </Button>
      </div>

      <Separator />

      <div className="flex justify-between items-center">
        合計: {selectedTab === "house" && todayRentHouse.filter(filterRecord).length}

        {selectedTab === "buldingWhole" && todayRentBuilding.filter(filterRecord).length}

        {selectedTab === "mansion" && todayRentMansion.filter(filterRecord).length}

        {selectedTab === "buildingPart" && todayRentBuildingPart.filter(filterRecord).length}
        件
      </div>

      <DataTable columns={[
        ...(selectedTab === "house" ? houseRentColumns : selectedTab === "buldingWhole" ? buildingRentColumns : mansionRentColumns),
        {
          header: "土地情報",
          cell: ({ row }) => {
            return <div className="flex flex-col text-xs">
              <div>
                {row.original.landType} | {row.original.landSize}
              </div>
              <div>
                {row.original.roadConnection} | {row.original.roadConnectionFirstFacing}
              </div>
            </div>;
          }
        },
        {
          header: "仲介",
          cell: ({ row }) => {
            return <div className="flex flex-col text-xs">
              <div>
                <span className={`text-xs text-gray-700 ${row.original.brokerType === "貸主" ? "text-green-500 text-bold" : ""}`}>
                  {row.original.brokerType}
                </span>

                <span className="text-xs text-gray-500"> | {row.original.brokerReinsNumber}</span>

                <span className="text-xs text-gray-500 " style={{ color: row.original.brokerStatus === "成約" ? "red" : "" }}> | {row.original.brokerStatus}</span>
              </div>
              <div className="text-xs text-gray-500"> {row.original.brokerListingCompany?.slice(0, 10)} | {row.original.brokerListingCompanyNumber}</div>
            </div>;
          }
        },
        {
          header: "その他",
          cell: ({ row }) => {
            return <div className="flex flex-col text-xs whitespace-nowrap text-neutral-500">
              <div>
                レコードID: {row.original.id}
              </div>
              <div>
                建物ID:
                <Link href={`/an/mansion/${row.original.buildingId}`} className="underline" target="_blank">
                  {row.original.buildingId}
                </Link>
              </div>
            </div>;
          }
        },
        {
          header: "操作",
          cell: ({ row }) => {
            let type = row.original.recordType === "HOUSE" ? "house" : row.original.recordType === "BUILDING" ? "building" : "mansion";

            return <div className="flex flex-col text-xs whitespace-nowrap text-neutral-500">
              <Button variant="outline" size="sm" onClick={() => {
                window.open(`/ad/rent/${type}/${row.original.id}`, "_blank");
              }}>
                GO DETAIL
              </Button>
            </div>;
          }
        }
      ]}
        data={(selectedTab === "house" ? todayRentHouse : selectedTab === "buldingWhole" ? todayRentBuilding : selectedTab === "mansion" ? todayRentMansion : todayRentBuildingPart).filter(filterRecord).sort((a: any, b: any) => (a.feeRent / (a.unitSize || a.buildingSize) || 0) - (b.feeRent / (b.unitSize || b.buildingSize) || 0) || 0)}
        defaultPageSize={500}
        isLoading={isLoading} />
    </div>
  </div>;
} 