"use client";

import { useState } from "react";

import { getProBuildingHouseRentDetail } from "@/actions/proBuildingHouseRent";
import { getProMansionRentDetail } from "@/actions/proMansionRent";
import { useEffect } from "react";
import { useParams } from "next/navigation";
import { Separator } from "@/components/ui/separator";

export default function RentDetailPage() {
  const { type, id } = useParams();

  const [selectedRecord, setSelectedRecord] = useState<any>(null);


  const fetchRecord = async () => {
    if (type === "house") {
      const record = await getProBuildingHouseRentDetail({ id: id as string });
      setSelectedRecord(record.data as any);
    } else if (type === "building") {
      const record = await getProMansionRentDetail({ id: id as string });
      setSelectedRecord(record.data as any);
    }
  };

  useEffect(() => {
    fetchRecord();
  }, [id]);


  return <div className="flex flex-col gap-2">
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold">詳細ページ</h1>
    </div>

    <Separator className="" />

    <div className="p-4 flex flex-col gap-4">
      <pre className="text-xs bg-gray-100 rounded-md p-4">
        {JSON.stringify(selectedRecord, null, 2)}
      </pre>
    </div>
  </div>;
}   