"use client";

import { getSumitomoAuctionByIdAction, saveRecordDirectly, updateSumitomoAuctionAction } from "@/actions/sumitomoAuction";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { SumitomoAuctionProps, SumitomoAuctionSchema } from "@/lib/definitions";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { useParams } from "next/navigation";
import { Checkbox } from "@/components/ui/checkbox"; // 导入Checkbox组件
import { Textarea } from "@/components/ui/textarea"; // 导入Textarea组件
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"; // 导入表单组件
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import PropertyInputFormCommon from "@/app/(cp)/ex/valuation/propertyInputFormCommon";
import { logger } from "@/lib/logger";
import Loading from "@/app/loading";
import { createUserLambdaRecordFromSumitomoAuctionAction } from "@/actions/tllUserLambdaRecordFromSumifuAction";
import { Loader2, Share } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { SumifuColumns } from "../sumitomoKeibaiColumns";
import { TooltipContent, Tooltip } from "@/components/ui/tooltip";
import { TooltipTrigger } from "@/components/ui/tooltip";
import { Tabs, TabsTrigger, TabsList, TabsContent } from "@/components/ui/tabs";

export default function KeibaiSumifuIdPage() {
  const { id } = useParams();
  const router = useRouter();
  const [sumitomoAuction, setSumitomoAuction] = useState<SumitomoAuctionProps | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingDP, setIsCreatingDP] = useState(false);
  const [matchRecordId, setMatchRecordId] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState<string>("auto");

  const form = useForm<z.infer<typeof SumitomoAuctionSchema>>({
    resolver: zodResolver(SumitomoAuctionSchema),
    defaultValues: {
      name: "",
      address: "",
      recordType: UserLambdaRecordType.BUILDING,

      price: 0,
      yearlyIncome: 0,
      nearestStation: "",
      nearestStationWalkMinute: 0,

      landSize: 0,
      landRight: "所有権",
      buildingSize: 0,
      buildingMaterial: "",
      buildingBuiltYear: 1900,

      isFav: 0,
      comments: "",
    },
  });

  const fetchSumitomoAuction = async () => {
    setIsLoading(true);
    const response = await getSumitomoAuctionByIdAction(id as string);
    if (!response.success) {
      console.error("获取需求失败");
      return;
    }
    setSumitomoAuction(response.data);

    form.reset({
      name: response.data.name || "",
      address: response.data.address || "",
      recordType: response.data.recordType || UserLambdaRecordType.BUILDING,
      price: response.data.price || 0,
      yearlyIncome: response.data.yearlyIncome || 0,
      nearestStation: response.data.nearestStation || "",
      nearestStationWalkMinute: response.data.nearestStationWalkMinute || 0,
      landSize: response.data.landSize || 0,
      landRight: response.data.landRight || "所有権",
      buildingSize: response.data.buildingSize || 0,
      buildingMaterial: response.data.buildingMaterial || "",
      buildingBuiltYear: response.data.buildingBuiltYear || 0,
      isFav: response.data.isFav || 0,
      comments: response.data.comments || "",
    });
    setIsLoading(false);
  }

  useEffect(() => {
    fetchSumitomoAuction();
  }, []);

  const handleSubmit = async ({ data }: { data: any }) => {
    try {
      // let formCheck = await form.trigger();
      // if (!formCheck) {
      //   return;
      // }
      setIsSaving(true);

      const whiteListFields = ["name", "address", "recordType", "price", "yearlyIncome", "nearestStationWalkMinute", "landSize", "landRight", "buildingSize", "buildingMaterial", "buildingBuiltYear", "isFav", "comments"];

      let filteredData = Object.fromEntries(Object.entries(data).filter(([key]) => whiteListFields.includes(key) && data[key as keyof typeof data] !== undefined && data[key as keyof typeof data] !== null && data[key as keyof typeof data] !== ""));


      let dataForSaving = {
        ...filteredData,
        // nearestStationGroupId: data.nearestStation?.value, // fixme: this s not saved
        nearestStation: data.nearestStation?.label,
        landSize: parseFloat(filteredData.landSize as string),
        buildingSize: parseFloat(filteredData.buildingSize as string),
        buildingBuiltYear: parseInt(filteredData.buildingBuiltYear as string),
        price: parseFloat(filteredData.price as string),
        yearlyIncome: parseFloat(filteredData.yearlyIncome as string),
        nearestStationWalkMinute: parseInt(filteredData.nearestStationWalkMinute as string),
      } as SumitomoAuctionProps;

      console.log("🔥 dataForSaving", dataForSaving);
      const response = await updateSumitomoAuctionAction({ id: id as string, data: dataForSaving });

      if (response.success) {
        toast({
          title: "更新成功",
          description: "Sumitomo Auction物件を更新しました",
        });

        await new Promise((resolve) => setTimeout(resolve, 1000));
        window.location.reload();
      }
      // router.refresh();
    } catch (error) {
      logger.error("🔥 error", error);
      toast({
        title: "更新失败",
        description: "更新失败" + error,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const createDp = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    setIsCreatingDP(true);

    // console.log("🔥 sumitomoAuction", sumitomoAuction);
    try {
      const response = await createUserLambdaRecordFromSumitomoAuctionAction(sumitomoAuction as SumitomoAuctionProps);

      if (response.success) {
        toast({
          title: "DPを作成しました",
          description: "DPを作成しました",
        });
        router.push("/ex/search/" + response.data.id);
      } else {
        toast({
          title: "DPを作成できませんでした",
          description: response.message,
        });
      }
    }
    catch (error) {
      logger.error("🔥 error", error);
      toast({
        title: "DPを作成できませんでした",
        description: "DPを作成できませんでした" + error,
      });
    }
    setIsCreatingDP(false);
    router.refresh();
  }

  return (
    <div className="">
      <div className="flex justify-between items-center p-4 flex-row">
        <h1 className="text-2xl font-bold" aria-label="競売物件詳細: {sumitomoAuction?.id}">競売物件詳細: {sumitomoAuction?.id}</h1>
        <Tooltip delayDuration={200}>
          <TooltipTrigger className="flex flex-row gap-2 items-center" asChild>
            <Button variant="outline" onClick={() => {
              navigator.clipboard.writeText("https://urbalytics.jp/ad/keibaiSumifu/" + sumitomoAuction?.id);
              toast({
                title: "競売物件詳細URLをクリップボードにコピーしました",
              });
            }}>
              <Share className="w-4 h-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            競売物件詳細URLをクリップボードにコピーしました
          </TooltipContent>
        </Tooltip>
      </div>



      <div className="px-4 py-2 flex flex-col gap-4 bg-neutral-100 border-t border-b border-neutral-200">
        {sumitomoAuction && <DataTable data={[sumitomoAuction as any]} columns={SumifuColumns as any} showFooter={false} />}
      </div>

      <Tabs value={selectedTab} className="px-4 pb-4 mt-4 border-b border-neutral-200" onValueChange={(value) => {
        setSelectedTab(value);
      }}>
        <TabsList>
          <TabsTrigger value="auto" onClick={() => {
            setSelectedTab("auto");
          }}>自動</TabsTrigger>
          <TabsTrigger value="manual" onClick={() => {
            setSelectedTab("manual");
          }}>手動</TabsTrigger>
        </TabsList>
      </Tabs>

      {selectedTab === "manual" && <div className="w-full bg-red-50 p-4 flex flex-row gap-2 mt-4 items-center">
        <Input type="text" value={matchRecordId || ""} onChange={(e) => {
          setMatchRecordId(e.target.value);
        }} />

        <Button onClick={async () => {
          setIsSaving(true);
          let response = await saveRecordDirectly(sumitomoAuction as SumitomoAuctionProps, matchRecordId as string);

          if (response?.success) {
            toast({
              title: "保存しました",
              description: "保存しました",
            });
            window.location.reload();
          } else {
            toast({
              title: "保存できませんでした",
              description: response.data,
            });
          }
          setIsSaving(false);
        }}>
          {isSaving ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
          保存
        </Button>
      </div>}

      {selectedTab === "auto" && <div className="px-4 py-2 flex flex-col gap-4">
        {isLoading ? <Loading /> :
          <Form {...form}>
            <form onSubmit={async (e) => {
              e.preventDefault();
              await handleSubmit({ data: form.getValues() });
            }} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>名前</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...field}
                        className="border p-2 w-full"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <PropertyInputFormCommon form={form} />

              <FormField
                control={form.control}
                name="isFav"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>お気に入り</FormLabel>
                    <FormControl>
                      <Checkbox checked={field.value === 1} onCheckedChange={(checked) => {
                        field.onChange(checked ? 1 : 0);
                      }} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="comments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>コメント</FormLabel>
                    <FormControl>
                      <Textarea {...field} rows={10} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-2">
                <Button type="submit">
                  {isSaving ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
                  保存
                </Button>

                {sumitomoAuction?.lambdaRecordId === null || sumitomoAuction?.lambdaRecordId === undefined ?
                  <Button variant="default" onClick={createDp}>
                    {isCreatingDP ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
                    DPを作成
                  </Button>
                  : <Button variant="outline"
                    onClick={(e) => {
                      e.preventDefault();
                      router.push(`/ex/search/${sumitomoAuction?.lambdaRecordId}`);
                    }}>DPを見る</Button>
                }
              </div>
            </form>
          </Form>
        }
      </div>}
    </div>
  );
}
