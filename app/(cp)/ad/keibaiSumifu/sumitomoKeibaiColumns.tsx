import dayjs from "dayjs";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { Heart, RefreshCcw } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { updateNewlyAddedDataAction, updateSumitomoAuctionAction } from "@/actions/sumitomoAuction";
import { toast } from "@/hooks/use-toast";
import { useSumitomoAuctionStore } from "@/store/sumitomoAuction";
import { getAllTimeAverageRentPrice, getAllTimeAverageSellingPrice } from "@/app/(cp)/an/mansion/utiliy/getAllTimeData";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";
import { debounce } from "lodash-es";

const getColor = (record: { recordType: string; lambdaRecord?: { propertyAnalysisResult?: { overallStarLevel?: number } } }, starLevel: number) => {
  let color = 'white';

  if (starLevel === undefined || starLevel === 0) {
    return color;
  }

  if (record.recordType === 'BUILDING') {
    if (starLevel >= 4) color = 'red';
    else if (starLevel >= 3) color = 'orange';
    else if (starLevel >= 1) color = 'green';
  } else {
    if (starLevel >= 3) color = 'red';
    else if (starLevel >= 2) color = 'orange';
    else if (starLevel >= 1) color = 'green';
  }

  return color;
};

const displayBiddingPrice = (record: { pastMatches?: { price: number }[]; price: number }) => {
  let highest = 0;
  if (record.pastMatches && record.pastMatches.length) {
    highest = getHighestPrice(record.pastMatches);
  }

  return highest === 0 ? (
    ''
  ) : (
    <>
      {highest > 0 && highest !== record.price && (
        <span
          style={{
            color: 'red',
          }}
        >
          過去 {highest}万
          <br />
          {highest > record.price ? '-' : ''}
          <b>{Math.round(((highest - record.price) / highest) * 100)}%, </b>
          {Math.round(highest - record.price)}万
        </span>
      )}
    </>
  );
};

const getHighestPrice = (priceChanges: { price: number }[]) => {
  let highest = 0;
  priceChanges.forEach((p) => {
    if (p.price > highest) {
      highest = p.price;
    }
  });

  return highest;
};

const displayPrice = (record: { lambdaRecord?: { priceChanges?: { price: number }[] }; price: number }) => {
  let highest = 0;
  if (record.lambdaRecord?.priceChanges) {
    highest = getHighestPrice(record.lambdaRecord.priceChanges);
  }

  return highest > 0 && highest !== record.price ? (
    <>
      <span
        style={{
          color: 'red',
        }}
      >
        vs {highest}
        <br />
        {highest > record.price ? '-' : '+'}
        <b>{Math.round(((highest - record.price) / highest) * 100)}%, </b>
        {Math.round(highest - record.price)}万
      </span>
    </>
  ) : (
    ""
  );
};

// 🔥 Optimized and memoized price change history calculation
const getPriceChangeHistory = (currentPrice: number, priceChanges: { price: number; recordDate: string }[]) => {
  if (!priceChanges || priceChanges.length === 0) {
    return "";
  }

  // 🔥 Limit to last 3 changes for performance
  const recentChanges = priceChanges.slice(0, 3);
  let changeString = '';

  recentChanges.forEach((d) => {
    changeString += `${d.price}(${dayjs(d.recordDate).format("MM-DD")}),`;
  });

  return changeString ? changeString.slice(0, -1) : ""; // Remove last comma
};

const refreshRecordMatchingDebounced = debounce(async (id: string) => {
  console.log('🔥 开始刷新记录匹配...');
  const res = await updateNewlyAddedDataAction(id);

  if (res.success) {
    toast({
      title: '更新しました',
      description: res.data.recordsMatched + '件のレコードが更新されました',
    });
    if (res.data.recordsMatched > 0) {
      window.location.reload();
    }
  } else {
    toast({
      title: '更新に失敗しました',
      description: res.message,
    });
  }
}, 1000); // 1秒的防抖时间

export const SumifuColumns = [
  {
    header: "ID",
    accessorKey: "id",
    cell: ({ row }: { row: any }) => {
      return (
        <>
          {row.index + 1}
        </>
      );
    },
  },
  {
    header: "FAV",
    accessorKey: "isFav",
    cell: ({ row }: { row: { original: { isFav: number; id: string } } }) => {
      const isFav = row.original.isFav;
      return (
        <Heart onClick={async () => {
          const { updateSumitomoAuction } = useSumitomoAuctionStore.getState();

          let res = await updateSumitomoAuctionAction({
            id: row.original.id,
            data: {
              isFav: isFav === 1 ? 0 : 1,
            }
          })

          if (res.success) {
            await updateSumitomoAuction({
              ...row.original,
              isFav: isFav === 1 ? 0 : 1,
            });
            toast({
              title: '更新しました',
            });
          }
        }} color={isFav ? 'red' : 'gray'} className="w-4 h-4" />
      );
    },
  },
  {
    header: "レコード",
    cell: ({ row }: { row: any }) => {
      return row.original.lambdaRecordId ?
        <>
          <div className="flex flex-col items-center justify-center text-xs">
            {row.original.lambdaRecord?.propertyAnalysisResult?.overallStarLevel ? <Badge
              className="mb-1"
              style={{
                backgroundColor: getColor(row.original, row.original.lambdaRecord?.propertyAnalysisResult?.overallStarLevel),
              }}
            >
              {row.original.lambdaRecord?.propertyAnalysisResult?.overallStarLevel ?? 'N/A'}
            </Badge> : ""
            }
          </div>

          <div className="flex flex-col gap-1 items-center justify-center text-xs">
            {row.original.lambdaRecordId && <Link
              href={`/ex/search/${row.original.lambdaRecordId}`}
              className="text-blue-500 hover:text-blue-700 underline"
              target="_blank"
            >
              {row.original.lambdaRecordId.slice(0, 6)}
            </Link>
            }

            <Badge variant={getStatus(row.original.lambdaRecord) === '成約' ? 'default' : 'secondary'}>
              {getStatus(row.original.lambdaRecord)}
            </Badge>

            {row.original.lambdaRecord?.bids?.length && row.original.lambdaRecord?.bids?.length > 0 ? <span>
              Bid: {row.original.lambdaRecord?.bids?.length || 0}
            </span> : ""}
          </div>
        </>
        : <RefreshCcw className="w-4 h-4 text-gray-500 w-full" onClick={() => refreshRecordMatchingDebounced(row.original.id)} />
    },
  },
  {
    header: "TLL目線",
    cell: ({ row }: { row: { original: any } }) => {
      let bidPrice = row.original.lambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice;
      // show empty for now
      return <div className="flex flex-col items-center">
        <span className="">
          {bidPrice?.toFixed(0) || '-'}
        </span>
        <span className={`text-xs ${bidPrice ? (bidPrice / row.original.lambdaRecord?.price * 100) > 120 ? 'text-green-700 font-bold' : bidPrice / row.original.lambdaRecord?.price * 100 > 80 ? 'text-green-400' : 'text-neutral-200' : 'inherit'}`}>
          {bidPrice ? (bidPrice / row.original.lambdaRecord?.price * 100).toFixed(1) + "%" : '-'}
        </span>
      </div>
    },
  },
  {
    header: "タイプ",
    cell: ({ row }: { row: any }) =>
      <div className="flex flex-col items-center justify-center">
        {row.original.type?.slice(0, 3)}
        <span className="text-gray-500 text-xs">
          {row.original.propertyStatus}
        </span>
      </div>
  },
  {
    header: "価格",
    cell: ({ row }: { row: { original: { price: number, yearlyIncome: number } } }) => (
      <div className="flex flex-col items-center justify-center">
        <span className="text-sm font-bold">
          {row.original.price}
        </span>
        <div className="flex flex-col items-center justify-center">
          {row.original.yearlyIncome && row.original.yearlyIncome > 0 ? (row.original.yearlyIncome / row.original.price * 100).toFixed(2) + '%' : ''}
        </div>
        <span className="text-gray-500 text-xs">
          {displayBiddingPrice(row.original)}
        </span>
        <span className="text-gray-500 text-xs">
          {displayPrice(row.original)}
        </span>
      </div>
    ),
  },
  {
    header: "物件名 | 住所",
    accessorKey: "name",
    cell: ({ row }: { row: { original: { name: string; address: string, nearestStation: string, building?: { id: string, nameJa: string } } } }) => {
      const maxWidth = 10;
      const name = row.original.name.length > maxWidth ? row.original.name.substring(0, maxWidth) + '...' : row.original.name;
      const address = row.original.address.length > maxWidth ? row.original.address.substring(0, maxWidth) + '...' : row.original.address;

      return (
        <div className="flex flex-col items-center justify-center">
          {row.original.building?.id ? <Link href={`/an/mansion/${row.original.building.id}`} className="underline">
            {row.original.building?.nameJa?.slice(0, maxWidth)}
          </Link> : <span>
            {name}
          </span>}

          <span className="text-gray-500 text-xs">
            {address}
          </span>
          <span className="text-gray-500 text-xs">
            最寄駅: {row.original.nearestStation || "-"}
          </span>
        </div>
      );
    },
  },
  {
    header: "マンション情報",
    cell: ({ row }: { row: any }) => {
      // 🔥 Simplified calculation with early returns for performance
      const building = row.original.building;
      if (!building) return <div className="text-xs text-gray-400">-</div>;

      const saleRecords = building.tllUserLambdaRecords?.length || 0;
      const rentRecords = building.mansionRents?.length || 0;

      return <div className="flex flex-col gap-1 items-center justify-center text-xs">
        {saleRecords > 0 && (
          <Badge variant="outline">売買 {saleRecords}件</Badge>
        )}
        {rentRecords > 0 && (
          <Badge variant="outline">賃貸 {rentRecords}件</Badge>
        )}
        {saleRecords === 0 && rentRecords === 0 && (
          <span className="text-gray-400">データなし</span>
        )}
      </div>
    },
  },
  {
    header: "土地建物(㎡)",
    cell: ({ row }: { row: any }) => {
      return <div className="flex flex-col items-center justify-center">
        <span className="">
          {row.original.landSize}
          {row.original.landRight && row.original.landRight?.length > 0 && <span className="text-gray-500">
            | {row.original.landRight || "-"}
          </span>}
        </span>
        {row.original.buildingSize ? <span className="text-gray-500 text-xs">
          {row.original.buildingSize || "-"} | {row.original.buildingMaterial || "-"} | {row.original.buildingBuiltYear || "-"}
        </span> : ""}
      </div>
    },
  },
  {
    header: "HP",
    accessorKey: "hp",
    width: 60,
    cell: ({ row }: { row: { original: { auctionUrl: string; hpUrl: string } } }) => {
      const auctionUrl = row.original.auctionUrl;
      const hpUrl = row.original.hpUrl;
      return (
        <div className="flex flex-col items-center justify-center text-xs">
          {hpUrl && hpUrl.indexOf('http') > -1 ? (
            <a href={hpUrl} target="_blank" className="text-blue-500 hover:text-blue-700">
              HP
            </a>
          ) : (
            '未掲載'
          )}
          {auctionUrl && (
            <a
              href={auctionUrl}
              target="_blank"
              className="text-blue-500 hover:text-blue-700"
            >
              競売
            </a>
          )}
        </div>
      );
    },
  },
  {
    header: "リンク",
    accessorKey: "materialLinks",
    cell: ({ row }: { row: { original: { materialLinks: Record<string, string> } } }) => (
      <>
        {Object.keys(row.original.materialLinks).map((k, index) => (
          <div key={index} className="flex flex-col items-center justify-center text-xs">
            <a
              href={row.original.materialLinks[k]}
              target="_blank"
              className="text-blue-500 hover:text-blue-700"
              style={{
                marginRight: 5,
              }}
            >
              {k.substring(0, 12)}
            </a>
          </div>
        ))}
      </>
    ),
  },
  {
    header: "タグ",
    accessorKey: "repeat",
    width: 50,
    cell: ({ row }: { row: { original: { comments: string; name: string } } }) => (
      <div className="flex flex-wrap gap-2">
        {['回目', '返済', '相続', '離婚', '施設'].map((keyword) => {
          if (row.original.comments.indexOf(keyword) > -1 || row.original.name.indexOf(keyword) > -1) {
            return (
              <Badge color="red" key={keyword}>
                {row.original.comments.indexOf('回目') > -1 && keyword === '回目'
                  ? row.original.comments.match(/(\d+)回目/)?.[1]
                  : row.original.name.match(/(\d+)回目/)?.[1]}
                {keyword}
              </Badge>
            );
          }
          return null;
        })}
      </div>
    ),
  },
  {
    header: "作成日 | 入札締切日",
    cell: ({ row }: { row: { original: { bidEndDate: string; createdAt: string; pocName: string } } }) => {
      const bidEndDate = dayjs(row.original.bidEndDate).format('YYYY-MM-DD');
      const daysLeft = dayjs(bidEndDate).diff(dayjs(dayjs().format('YYYY-MM-DD')), 'day');
      return (
        <>
          <div className="border-b border-gray-200 pb-1 mb-1">
            {dayjsWithTz(row.original.createdAt).tz('Asia/Tokyo').format('YYYY-MM-DD HH:mm')}
            {dayjsWithTz(row.original.createdAt).tz('Asia/Tokyo').format('YYYY-MM-DD') === dayjsWithTz().tz('Asia/Tokyo').format('YYYY-MM-DD') && (
              <span style={{ color: 'red', marginLeft: 5 }}>新規</span>
            )}
            <br />
            <span className="text-gray-500 text-xs">
              POC: {row.original.pocName}
            </span>
          </div >

          <span
            style={{ color: daysLeft <= 2 ? 'red' : 'inherit' }}
            className={`text-xs ${daysLeft < 1 ? 'font-bold' : ''}`}
          >
            {`${bidEndDate}`}
            {`(${daysLeft}日残り)`}
          </span>
        </>
      );
    },
  },
  {
    header: "コメント",
    accessorKey: "comments",
    cell: ({ row }: { row: { original: { comments: string, price: number, lambdaRecord?: { priceChanges?: { price: number; recordDate: string }[] } } } }) => {
      // 🔥 Simplified comment processing for performance
      const comments = row.original.comments;
      const truncatedComments = comments.length > 200 ? comments.substring(0, 200) + '...' : comments;
      const priceHistory = getPriceChangeHistory(row.original.price, row.original.lambdaRecord?.priceChanges || []);

      return (
        <div className="text-xs max-w-xs">
          {priceHistory && (
            <div className="text-gray-500 mb-1">{priceHistory}</div>
          )}
          <div className="overflow-hidden text-ellipsis" style={{ WebkitLineClamp: 3, display: '-webkit-box', WebkitBoxOrient: 'vertical' }}>
            {truncatedComments}
          </div>
        </div>
      );
    },
  },
  {
    header: "操作",
    cell: ({ row }: { row: { original: { id: string } } }) => {
      return (
        <div className="flex gap-2 justify-center">
          <Link href={`/ad/keibaiSumifu/${row.original.id}`}>
            <Button variant="outline" className="">修正</Button>
          </Link>
        </div>
      );
    },
  }
];