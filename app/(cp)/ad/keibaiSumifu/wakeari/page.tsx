"use client";

import { useEffect, useState } from "react";
import { getSumitomoAuctionAction, getSumitomoAuctionWithKeywords } from "@/actions/sumitomoAuction";
import { SumitomoAuctionProps } from "@/lib/definitions";
import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/ui/data-table";
import { SumifuColumns } from "../sumitomoKeibaiColumns";
import { Toggle } from "@/components/ui/toggle";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

export default function Wakeari() {
  const [sumitomoAuctions, setSumitomoAuctions] = useState<SumitomoAuctionProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filterHideClosed, setFilterHideClosed] = useState(true);
  const [filterHideLowPrice, setFilterHideLowPrice] = useState(true);
  const [filterHideRecent, setFilterHideRecent] = useState(true);
  const [filterShowFav, setFilterShowFav] = useState(false);

  const fetchSumitomoAuctions = async () => {
    setIsLoading(true);
    const response = await getSumitomoAuctionWithKeywords();
    if (response.success) {
      setSumitomoAuctions(response.data);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (sumitomoAuctions.length === 0) {
      fetchSumitomoAuctions();
    }
  }, []);

  const filteredSumitomoAuctions = sumitomoAuctions.filter((auction) => {
    console.log(auction.price);

    if (filterHideClosed && getStatus(auction.lambdaRecord as UserLambdaRecordProps) === '成約') {
      return false;
    }

    if (filterHideLowPrice && auction.price && auction.price < 3000) {
      return false;
    }

    if (filterHideRecent && auction.bidEndDate && dayjsWithTz(auction.bidEndDate).isAfter(dayjsWithTz().subtract(3, 'month'))) {
      return false;
    }

    if (filterShowFav && auction.isFav !== 1) {
      return false;
    }

    return true;
  });


  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="お気に入り">競売: 住友不動産　訳あり物件</h1>
    </div>

    <Separator className="" />
    <div className="flex justify-start items-center px-4 py-2 gap-2">
      <Toggle pressed={filterHideClosed} onPressedChange={() => setFilterHideClosed(!filterHideClosed)}>
        成約隠す
      </Toggle>

      <Toggle pressed={filterHideLowPrice} onPressedChange={() => setFilterHideLowPrice(!filterHideLowPrice)}>
        価格3000万円以下隠す
      </Toggle>

      <Toggle pressed={filterHideRecent} onPressedChange={() => setFilterHideRecent(!filterHideRecent)}>
        直近三ヶ月内競売を隠す
      </Toggle>

      <Toggle pressed={filterShowFav} onPressedChange={() => setFilterShowFav(!filterShowFav)}>
        気に入り物件を表示
      </Toggle>
    </div>

    <Separator className="" />

    <div className="px-4 py-2 flex flex-col gap-4">
      <div className="text-sm text-gray-500">競売後12ヶ月以内の物件  {filteredSumitomoAuctions.length}件</div>
      <DataTable columns={SumifuColumns as any} data={filteredSumitomoAuctions} isLoading={isLoading} defaultPageSize={200} />
    </div>
  </div>;
}
