"use client"

import { Separator } from "@/components/ui/separator"
import { getSumitomoAuctionAction } from "@/actions/sumitomoAuction"
import React, { useEffect, useState, useMemo } from "react";
import { DataTable } from "@/components/ui/data-table"
import { SumifuColumns } from "./sumitomoKeibaiColumns";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import dayjs from "dayjs"; // 导入dayjs库
import { useSumitomoAuctionStore } from "@/store/sumitomoAuction";
import { toast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { updateNewlyAddedDataAction } from "@/actions/sumitomoAuction";
import { Loader2, MapPin, RefreshCcw } from "lucide-react";
import LeafletMap from "@/components/LeafletMap";
import BidMapPage from "../bid/BidMap";
import { Drawer, Drawer<PERSON>ontent, DrawerHeader, <PERSON>er<PERSON>itle, DrawerTrigger } from "@/components/ui/drawer";
import { Toggle } from "@/components/ui/toggle";
import { SumitomoAuctionProps } from "@/lib/definitions";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

export default function Sumifu() {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUpdating, setIsLoadingUpdating] = useState(false);
  const [selectedTab, setSelectedTab] = useState("all");
  const { sumitomoAuctions, setSumitomoAuctions } = useSumitomoAuctionStore();
  const [hideOutsideTokyo, setHideOutsideTokyo] = useState(true);
  const [selectedRecirdType, setSelectedRecirdType] = useState("all");

  const fetchSumitomoAuctions = async () => {
    setIsLoading(true); // ✅ Force state update
    const response = await getSumitomoAuctionAction();
    if (response.success) {
      setSumitomoAuctions(response.data);
    } else {
      toast({
        title: "获取sumitomoAuctions失败",
      });
    }
    setIsLoading(false);
  }

  // 🔥 Memoized filter function for better performance
  const filteredSumitomoAuctions = useMemo(() => {
    return (record: SumitomoAuctionProps) => {
      if (hideOutsideTokyo && record.recordType === "MANSION") {
        if (["神奈川", "埼玉", "千葉"].some(p => record.address?.includes(p))) {
          return false;
        }
      }

      if (selectedRecirdType !== "all") {
        return record.recordType === selectedRecirdType;
      }

      // 🔥 For "すべて" tab, only show records created in recent 2 days
      return dayjs(record.createdAt).isAfter(dayjs().subtract(2, 'day'));
    };
  }, [hideOutsideTokyo, selectedRecirdType]);

  // 🔥 Memoized filtered data
  const allFilteredData = useMemo(() => {
    return sumitomoAuctions.filter(filteredSumitomoAuctions);
  }, [sumitomoAuctions, filteredSumitomoAuctions]);

  const favFilteredData = useMemo(() => {
    return sumitomoAuctions
      .filter(record => record.isFav === 1 && dayjs(record.bidEndDate).isAfter(dayjs().add(-1, 'day')))
      .sort((a, b) => dayjs(a.bidEndDate).diff(dayjs(b.bidEndDate)));
  }, [sumitomoAuctions]);

  useEffect(() => {
    if (sumitomoAuctions.length === 0) {
      fetchSumitomoAuctions();
    }
  }, [sumitomoAuctions]);

  const handleTabChange = (tab: string) => {
    setSelectedTab(tab);
  };

  // 🔥 Show loading wheel when initially loading data
  if (isLoading && sumitomoAuctions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <Loader2 className="w-12 h-12 animate-spin text-blue-500 mb-4" />
        <h2 className="text-xl font-semibold mb-2">データを読み込み中...</h2>
        <p className="text-gray-500 text-center">
          住友不動産の競売データを取得しています。<br />
          しばらくお待ちください。
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold">競売: 住友不動産</h1>

        <div className="flex flex-row gap-2">
          <Button variant="outline" onClick={async () => {
            setIsLoadingUpdating(true);
            let res = await updateNewlyAddedDataAction();
            if (res.success) {
              toast({
                title: "更新成功",
              });
            } else {
              toast({
                title: "更新失败",
              });
            }
            setIsLoadingUpdating(false);
          }}>
            <RefreshCcw className={`w-4 h-4 ${isLoadingUpdating ? "animate-spin" : ""}`} />
          </Button>

          <Drawer>
            <DrawerTrigger asChild>
              <Button variant={"outline"}>
                <MapPin className="w-4 h-4" />
              </Button>
            </DrawerTrigger>
            <DrawerContent className="h-[90vh]">
              <DrawerHeader>
                <DrawerTitle>マップ表示</DrawerTitle>
              </DrawerHeader>
              <LeafletMap height={"80vh"} zoom={11} data={sumitomoAuctions.filter(a => a.isFav && a.longitude && a.latitude).map((record: any) => {
                return {
                  name: record.name || "",
                  colorDot: "green",
                  coordinate: [record.latitude, record.longitude],
                  link: "/ad/keibaiSumifu/" + record.id
                }
              })} legend={
                <div className="flex flex-row gap-2 bottom-0 absolute z-999 text-center justify-center bg-neutral-50 p-1">

                  <div className="w-4 h-4 bg-blue-500"></div>
                  <div>中心点</div>

                  <div className="w-4 h-4 bg-green-500"></div>
                  <div>検討中物件({sumitomoAuctions.filter(a => a.isFav && a.longitude && a.latitude).length}件)</div>
                </div>
              } />
            </DrawerContent>
          </Drawer>
        </div>
      </div>

      <Separator className="" />

      <div className="bg-white z-10 mt-2"> {/* 添加sticky效果 */}
        <Tabs value={selectedTab} className="mb-2"> {/* 使用选中的标签 */}
          <div className="w-full overflow-x-auto scrollbar-hide px-2">
            <TabsList className="flex space-x-4 p-4 min-w-max justify-start" onChange={(value) => (value)}>
              <TabsTrigger value="all" onClick={() => handleTabChange("all")}>
                すべて <Badge variant="outline" className="ml-1"> {sumitomoAuctions.length} </Badge>
              </TabsTrigger>
              <TabsTrigger value="fav" onClick={() => handleTabChange("fav")}>
                気に入り
                <Badge variant="outline" className="ml-1"> {sumitomoAuctions.filter(record => record.isFav === 1 && dayjs(record.bidEndDate).isAfter(dayjs().add(-1, 'day'))).length} </Badge>
              </TabsTrigger>
            </TabsList>
          </div>

          <Separator className="mt-2" />

          {selectedTab === "all" && <div className="p-2 pb-0 flex flex-row gap-2 flex-wrap overflow-x-auto scrollbar-hide">
            <Toggle className="mb-4" pressed={hideOutsideTokyo} onPressedChange={() => setHideOutsideTokyo(!hideOutsideTokyo)}>
              東京都以外のマンションを隠す
            </Toggle>

            <ToggleGroup
              type="single"
              value={selectedRecirdType}
              onValueChange={setSelectedRecirdType}
              className="mb-4 border-l pl-2"
            >
              <ToggleGroupItem value="all" className="whitespace-nowrap">全て</ToggleGroupItem>
              {Object.entries(UserLambdaRecordType).map(([key, value]) => (
                <ToggleGroupItem key={key} value={key} className="whitespace-nowrap">
                  {value}
                </ToggleGroupItem>
              ))
              }
            </ToggleGroup>
          </div>}

          <TabsContent value="all">
            <div className="sm:p-2 pt-0">
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="w-8 h-8 animate-spin mr-2" />
                  <span>データを読み込み中...</span>
                </div>
              ) : (
                <DataTable
                  columns={SumifuColumns as any}
                  data={allFilteredData}
                  defaultPageSize={50} // 🔥 Reduced from 500 to 50
                  isLoading={isLoading}
                />
              )}
            </div>
          </TabsContent>

          <TabsContent value="fav">
            <div className="p-2 sm:p-4">
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="w-8 h-8 animate-spin mr-2" />
                  <span>データを読み込み中...</span>
                </div>
              ) : (
                <DataTable
                  columns={SumifuColumns as any}
                  data={favFilteredData}
                  defaultPageSize={50} // 🔥 Reduced from 200 to 50
                  isLoading={isLoading}
                />
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
