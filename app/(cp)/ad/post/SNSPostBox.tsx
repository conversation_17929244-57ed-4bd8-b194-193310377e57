import { DataTable } from "@/components/ui/data-table";
import dayjs from "dayjs";
import { getPriceChangeHistory } from "@/components/userLambdaRecord/priceChangeUtilities";
import "./page.css";
import "dayjs/locale/ja";

export default function SNSPostBox({
  dataDate,
  dataPrefecture,
  showPriceType,
  dataRecordType,
  columns,
  tableData,
  loadingReins,
  lan,
  hideUrba,
  sortBy,
  filterDataCount,
  showFooter,
  filterRoi,
  includeNew = false,
}: {
  dataDate: string;
  dataPrefecture: { label: string, value: number }[];
  showPriceType: string;
  dataRecordType: string;
  columns: any[];
  tableData: any[];
  loadingReins: boolean;
  lan: string;
  hideUrba: boolean;
  sortBy: string;
  filterDataCount: string;
  showFooter: boolean;
  filterRoi: number;
  includeNew?: boolean;
}) {

  const prepareData = (data: any) => {
    if (!data || data.length === 0) {
      return [];
    }

    let parsed = data
      .filter((d: any) => {
        // Do not show if no multiple changes
        // This is to make the graph look nicer
        if (!includeNew && d.priceChanges?.filter((r: any) => r.price !== d.price).length <= 1) {
          return false;
        }

        if (dataRecordType == 'BUILDING') {
          if (d.yearlyIncome > 0) {
          } else {
            return false;
          }

          if (filterRoi > 0 && (d?.yearlyIncome / (d?.price || 0)) * 100 < filterRoi) {
            return false;
          }
        }

        let priceChangeHistory = getPriceChangeHistory(d.price, d.priceChanges)?.rawData;
        const priceChangeRecent = priceChangeHistory?.priceChangeRecent;
        const priceChangePerc = priceChangeHistory?.priceChangePerc && priceChangeHistory?.priceChangePerc * 100;
        const isPriceIncrease = priceChangeRecent && priceChangeRecent > 0;

        if (showPriceType == '価格減少のみ' && isPriceIncrease) {
          return false;
        } else if (showPriceType == '価格上昇のみ' && !isPriceIncrease) {
          return false;
        }

        return true;
      })
      .map((d: any) => {
        const priceChangeHistory = getPriceChangeHistory(d.price, d.priceChanges)?.rawData;
        return {
          ...d,
          priceChangePerc: priceChangeHistory?.priceChangePerc || 0,
          priceChange: priceChangeHistory?.priceChangeRecent || 0,
        };
      });

    console.log("parsed", parsed);

    if (sortBy === 'priceChangePerc') {
      parsed?.sort((a: any, b: any) => a.priceChangePerc - b.priceChangePerc);
    } else if (sortBy === 'priceChange') {
      parsed?.sort((a: any, b: any) => a.priceChange - b.priceChange);
    }

    // console.log("parsed", parsed);
    // console.log("parsed", filterDataCount);

    return parsed.slice(0, filterDataCount);
  };

  return (
    <div className="flex flex-col relative overflow-hidden">
      <div className="relative w-full py-5 text-white font-semibold text-2xl text-center bg-black -mb-px">
        <div className="text-xl">
          {dayjs().locale('ja').format('YYYY年MM月DD日（ddd）')}
        </div>
        <div>
          {/* {dataDate === 'today' ? '本日' : '昨日'} */}
          {dataPrefecture
            .map((prefecture: any) => {
              switch (prefecture.value) {
                case 11:
                  return '埼玉県';
                case 12:
                  return '千葉県';
                case 13:
                  return '東京都';
                case 14:
                  return '神奈川県';
                default:
                  return '';
              }
            })
            .join('、')}
          の
          {(() => {
            switch (dataRecordType) {
              case 'BUILDING':
                return '一棟収益';
              case 'HOUSE':
                return '戸建';
              case 'LAND':
                return '土地';
              default:
                return '';
            }
          })()}
          物件の価格改正情報
          {/* {showPriceType === '価格上昇のみ'
            ? '値上げ'
            : showPriceType === '価格減少のみ'
              ? '値下げ'
              : '価格改正情報'} */}
        </div>
      </div>

      <div className="snsTable">
        <DataTable
          columns={columns}
          data={prepareData(tableData)}
          showFooter={false}
          isLoading={loadingReins}
        />
      </div>

      <div
        className="flex flex-col text-sm mt-2 relative"
      >
        <span>
          <span className="mr-1">*</span>
          <b>[再販]</b>
          {lan === 'zh'
            ? '物件是指在价格修正历史中，价格下降后价格急剧上升的物件。'
            : '物件とは、価格変更履歴の中で、価格が減少した後に価格が急激に上昇した物件を指します。'}
        </span>
        <span>
          {hideUrba ? (
            ''
          ) : (
            <>
              <span className="mr-1">*</span>
              {lan === 'zh'
                ? '数据是从我们的评估系统Urbalytics中收集和处理的。'
                : 'データは弊社査定システムUrbalyticsより収集・加工したものです。'}
            </>
          )}
        </span>
        <span>
          <span className="mr-1">*</span>
          {lan === 'zh'
            ? '在获取的物件信息中，如果某些详细数据缺失，则项目中没有输入。'
            : '取得した物件情報の中に一部詳細データがない場合は、項目に入力がありません。'}
        </span>

        <span>
          <span className="mr-1">*</span>
          {sortBy === 'priceChangePerc'
            ? lan === 'zh'
              ? '按价格变动比例从大到小排序'
              : '価格変動割合が大きい順に上位'
            : sortBy === 'priceChange'
              ? lan === 'zh'
                ? '按价格变动从大到小排序'
                : '価格変動が大きい順に上位'
              : ''}
          {filterDataCount !== "999" &&
            (lan === 'zh'
              ? `最多显示${filterDataCount}件。`
              : `最大${filterDataCount}件を表示しています。`)}
        </span>

        <span>
          <span className="mr-1">*</span>
          {lan === 'zh'
            ? '请咨询物件详细资料和正在考虑的物件的评估。'
            : '物件詳細資料・検討中物件の査定はお問い合わせください。'}
        </span>

        {!hideUrba && (
          <img src="/assets/urba-full.png" alt="logo" width={200} height={40} className="mt-2 absolute bottom-0 right-0" />
        )}
      </div>

      {showFooter && (
        <>
          <div className="relative flex items-center w-full mt-2">
            <div className="grow border-t border-gray-300 opacity-50"></div>
            <span className="mx-4 text-gray-500 text-sm">発表カレンダー</span>
            <div className="grow border-t border-gray-300 opacity-50"></div>
          </div>

          <div className="grid grid-cols-7 gap-2 mt-2">
            {[
              { day: 'MON', color: 'bg-color-building', location: '東京都', description: '一棟収益' },
              { day: 'TUE', color: 'bg-color-house', location: '東京都', description: '戸建' },
              { day: 'WED', color: 'bg-color-special', location: '', description: '特集' },
              { day: 'THUR', color: 'bg-color-building', location: '三県', description: '一棟収益' },
              { day: 'FRI', color: 'bg-color-building', location: '東京都', description: '一棟収益' },
              { day: 'SAT', color: 'bg-color-building', location: '一都三県', description: '高利回一棟' },
              { day: 'SUN', color: 'bg-color-special', location: '', description: '特集' },
            ].map(({ day, color, location, description }) => (
              <div className={`text-center p-2 ${color}`} style={{ flex: 1 }} key={day}>
                <div className="border-b border-neutral-500 pb-1 text-sm">{day}</div>
                <div className="flex items-center justify-center h-10 mt-1 leading-5 text-center text-sm">{location && <>{location}<br /></>}{description}</div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}