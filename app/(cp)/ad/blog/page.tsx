"use client"

import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useEffect, useState } from "react";
import { getBlogs, translateBlog } from "@/actions/blog";
import { BlogProps } from "@/lib/definitions";
import { DataTable } from "@/components/ui/data-table";
import { useRouter } from "next/navigation";
import { blogWriter } from "@/lib/constants/blogWriter";
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import dayjs from "dayjs";
import { toast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

export default function BlogPage() {
  const [blogsDb, setBlogsDb] = useState<BlogProps[]>([]);
  const [blogsSanity, setBlogsSanity] = useState<any[]>([]);
  const router = useRouter();
  const [isTranslating, setIsTranslating] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  async function fetchPost(locale: string, slug: string) {
    const decodedSlug = decodeURIComponent(slug);

    return await sanityClient.fetch(
      `*[_type == "post"]{
        title_zh,
        title_en,
        title_ja,
        body_zh,
        body_en,
        body_ja,
        image,
        _id,
        "slug_zh": slug_zh.current,
        "slug_en": slug_en.current,
        "slug_ja": slug_ja.current,
        publishedAt,
        "author": coalesce(author->name, "Urbalytics Team")
      }`
    );
  }

  useEffect(() => {
    setIsLoading(true);
    getBlogs({}).then((res) => {
      setBlogsDb(res.data);
      setIsLoading(false);
    });

    fetchPost("zh", "").then((res) => {
      setBlogsSanity(res);
    });
  }, []);

  const findBlogSanity = (id: string) => {
    return blogsSanity.find((blog) => blog._id === id);
  }

  console.log("blogs", blogsDb);

  let columns = [
    // {
    //   header: "ブログID",
    //   accessorKey: "id",
    //   cell: ({ row }: { row: any }) => row.original.id
    // },
    {
      header: "発表日",
      accessorKey: "publishedAt",
      cell: ({ row }: { row: any }) => dayjs(findBlogSanity(row.original.id)?.publishedAt).format("YYYY-MM-DD") || ""
    },
    {
      header: "タイトル_ZH",
      cell: ({ row }: { row: any }) =>
        <div className="text-sm text-ellipsis overflow-hidden line-clamp-2 underline cursor-pointer" onClick={() => {
          window.open(`/blog/zh/${findBlogSanity(row.original.id)["slug_zh"]}`, "_blank");
        }}>
          {findBlogSanity(row.original.id)?.title_zh?.slice(0, 30) || ""}
        </div>
    },
    {
      header: "詳細データ",
      cell: ({ row }: { row: any }) =>
        <div className="flex flex-col text-xs">
          <div className="text-ellipsis overflow-hidden line-clamp-2">
            TITLE: {["zh", "en", "ja"].map((locale) =>
              findBlogSanity(row.original.id)?.[`title_${locale}`]?.length > 0 ? locale : "-"
            ).join(" / ")}
          </div>
          <div className="text-ellipsis overflow-hidden line-clamp-2">
            SLUG: {["zh", "en", "ja"].map((locale) =>
              findBlogSanity(row.original.id)?.[`slug_${locale}`]?.length > 0 ? locale : "-"
            ).join(" / ")}
          </div>
          <div className="text-ellipsis overflow-hidden line-clamp-2">
            BODY: {["zh", "en", "ja"].map((locale) =>
              findBlogSanity(row.original.id)?.[`body_${locale}`]?.length > 0 ? locale : "-"
            ).join(" / ")}
          </div>
        </div>
    },
    {
      header: "閲覧数(ZH/EN/JP)",
      cell: ({ row }: { row: any }) => row.original.viewZh + " / " + row.original.viewEn + " / " + row.original.viewJa,
    },
    {
      header: "関連データ",
      cell: ({ row }: { row: any }) =>
        <div className="flex flex-col text-xs">
          {row.original.buildingId && <div className="text-ellipsis overflow-hidden line-clamp-2">
            BUILDING_ID: {row.original.building?.nameJa || ""}
          </div>}
          {row.original.postalCode && <div className="text-ellipsis overflow-hidden line-clamp-2">
            POSTAL_CODE: {row.original.postalCode?.prefectureName + row.original.postalCode?.areaName + row.original.postalCode?.cityName + row.original.postalCode?.choumeName || ""}
          </div>}
          {row.original.stationGroupId && <div className="text-ellipsis overflow-hidden line-clamp-2">
            STATION_ID: {row.original.stationGroup?.name || ""}
          </div>}
          {row.original.areaCode && <div className="text-ellipsis overflow-hidden line-clamp-2">
            AREA_ID: {row.original.area?.nameJa || ""}
          </div>}
        </div>
    },
    {
      header: "担当者",
      cell: ({ row }: { row: any }) => row.original.creatorUser?.name || ""
    },
    {
      header: "操作",
      cell: ({ row }: { row: any }) => {
        const currentLocale = "zh";

        return <div className="flex gap-2 justify-center">
          <Button variant="outline" size="sm" onClick={() => {
            window.open(`/ad/blog/${row.original.id}`, "_blank");
          }}>
            編集
          </Button>

          <Button variant="outline" size="sm" disabled={isTranslating || (findBlogSanity(row.original.id)?.title_ja?.length > 0 && findBlogSanity(row.original.id)?.body_ja?.length > 0)} onClick={async () => {
            setIsTranslating(true);
            let res = await translateBlog({ blogId: findBlogSanity(row.original.id)?._id, targetLang: "ja" });
            if (res.success) {
              toast({
                title: "翻译成功",
                description: "翻译成功",
                duration: 10000,
              });

              window.location.reload();
            } else {
              toast({
                title: "翻译失败",
                description: res.message,
                duration: 10000,
              });
            }
            setIsTranslating(false);
          }}>
            {isTranslating ? "翻译中..." : "中 => 日"}
          </Button>

          <Button variant="outline" size="sm" disabled={isTranslating || (findBlogSanity(row.original.id)?.title_en?.length > 0 && findBlogSanity(row.original.id)?.body_en?.length > 0)} onClick={async () => {
            setIsTranslating(true);
            let res = await translateBlog({ blogId: findBlogSanity(row.original.id)?._id, targetLang: "en" });
            if (res.success) {
              toast({
                title: "翻译成功",
                description: "翻译成功",
              });
            } else {
              toast({
                title: "翻译失败",
                description: res.message,
              });
            }
            setIsTranslating(false);
          }}>
            {isTranslating ? <Loader2 className="w-4 h-4 animate-spin" /> : "中 => 英"}
          </Button>
        </div>
      }
    }
  ];

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="ブログ管理">ブログ管理</h1>
    </div>

    <Separator className="" />

    <div className="px-4 py-2">
      <DataTable
        columns={columns}
        data={blogsDb?.sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())}
        isLoading={isLoading}
      />
    </div>

    <Separator className="my-4" />

    <div className="px-4 py-2">
      <h2 className="text-lg font-bold" aria-label="担当者">Blog Writers</h2>
      <div className="flex flex-wrap gap-2">
        {blogWriter.map((writer) => (
          <div key={writer.userId} className="flex flex-col gap-2">
            {JSON.stringify(writer)}
          </div>
        ))}
      </div>
    </div>
  </div>;
}