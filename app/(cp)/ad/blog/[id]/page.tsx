"use client"

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { BlogProps } from "@/lib/definitions";
import { getBlog } from "@/actions/blog";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { getListOfTllAgentUserAction } from "@/actions/tllUserLambdaRecordFromSumifuAction";
import { updateBlog } from "@/actions/blog";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import SearchBarPostalCodeUI from "@/components/ui/SearchBarPostalCodeUI";
import SearchBarStationUI from "@/components/ui/SearchBarStationUI";
import SearchBarMansionBuildingUI from "@/components/ui/SearchBarMansionBuildingUI";
import SearchBarAreaUI from "@/components/ui/SearchBarAreaUI";

export default function BlogPage() {
  const params = useParams();
  const [blog, setBlog] = useState<BlogProps | null>(null);
  const [creatorUserId, setCreatorUserId] = useState<string | null>(null);
  const [agentUsers, setAgentUsers] = useState<any[]>([]);
  const [selectedPostalCode, setSelectedPostalCode] = useState<{ label: string; value: number, postalCode: number, latitude?: number, longitude?: number }>({ label: "", value: 0, postalCode: 0 });
  const [selectedStation, setSelectedStation] = useState<any>(null);
  const [selectedArea, setSelectedArea] = useState<any>(null);
  const [selectedBuilding, setSelectedBuilding] = useState<any>(null);

  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    getBlog(params.id as string).then((res) => {
      setBlog(res.data);
      setCreatorUserId(res.data.creatorUserId);
    });

    getListOfTllAgentUserAction().then((res) => {
      if (res.success && res.data) {
        setAgentUsers(res.data);
      }
    });
  }, [params.id]);

  const handleReset = async () => {
    if (!creatorUserId) {
      return;
    }

    setIsLoading(true);

    let res = await updateBlog({
      blogId: params.id as string,
      creatorUserId: creatorUserId,
      postalCodeId: null,
      stationGroupId: null,
      areaCode: null,
      buildingId: null,
    });


    if (res.success) {
      setBlog(res.data);
      toast({
        title: "担当者を更新しました",
        description: "担当者を更新しました",
      })

      router.push("/ad/blog");
    } else {
      toast({
        title: "担当者を更新できませんでした",
        description: res.message,
      })
    }

    setIsLoading(false);
  }

  const handleSave = async () => {
    if (!creatorUserId) {
      return;
    }

    setIsLoading(true);

    let res = await updateBlog({
      blogId: params.id as string,
      creatorUserId: creatorUserId,
      ...(selectedPostalCode.value ? { postalCodeId: selectedPostalCode.value.toString() } : {}),
      ...(selectedStation ? { stationGroupId: selectedStation.value } : {}),
      ...(selectedArea ? { areaCode: (selectedArea.areaCode || 0) } : {}),
      ...(selectedBuilding ? { buildingId: selectedBuilding.value } : {}),
    });


    if (res.success) {
      setBlog(res.data);
      toast({
        title: "担当者を更新しました",
        description: "担当者を更新しました",
      })

      router.push("/ad/blog");
    } else {
      toast({
        title: "担当者を更新できませんでした",
        description: res.message,
      })
    }

    setIsLoading(false);
  }

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="ブログ">ブログ {params.id}</h1>
    </div>

    <Separator className="" />

    <div className="px-4 py-2 flex flex-col gap-4">
      <div className="flex flex-col gap-2">
        <label htmlFor="salesUserId" className="flex flex-row gap-1 items-end justify-start">
          <div>関連建物(BUILDING_ID)</div>

          <div className="text-xs text-gray-500 mt-1 flex flex-row gap-1 items-center">
            <div>現在の値: {blog?.buildingId || ""}</div>
            <div>| {blog?.building?.nameJa || ""}</div>
          </div>
        </label>
        <SearchBarMansionBuildingUI selectedBuilding={selectedBuilding} setSelectedBuilding={setSelectedBuilding} />
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="salesUserId" className="flex flex-row gap-1 items-end justify-start">
          <div>関連郵便番号(POSTAL_CODE_ID)</div>

          <div className="text-xs text-gray-500 mt-1 flex flex-row gap-1 items-center">
            <div>現在の値: {blog?.postalCodeId || ""}</div>
            <div>| {blog?.postalCode?.prefectureName ? blog?.postalCode?.prefectureName + " " : ""}{blog?.postalCode?.areaName ? blog?.postalCode?.areaName + " " : ""}{blog?.postalCode?.cityName ? blog?.postalCode?.cityName + " " : ""}{blog?.postalCode?.choumeName ? blog?.postalCode?.choumeName + " " : ""}</div>
          </div>
        </label>
        <SearchBarPostalCodeUI selectedPostalCode={selectedPostalCode} setSelectedPostalCode={setSelectedPostalCode} />
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="salesUserId" className="flex flex-row gap-1 items-end justify-start">
          <div>関連駅(STATION_ID)</div>

          <div className="text-xs text-gray-500 mt-1 flex flex-row gap-1 items-center">
            <div>現在の値: {blog?.stationGroupId || ""}</div>
            <div>| {blog?.stationGroup?.name || ""}</div>
          </div>
        </label>

        <SearchBarStationUI selectedStation={selectedStation} setSelectedStation={setSelectedStation} />
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="salesUserId" className="flex flex-row gap-1 items-end justify-start">
          <div>関連エリア(AREA_ID)</div>

          <div className="text-xs text-gray-500 mt-1 flex flex-row gap-1 items-center">
            <div>現在の値: {blog?.areaCode || ""}</div>
            <div>| {blog?.area?.nameJa || ""}</div>
          </div>
        </label>
        <SearchBarAreaUI selectedArea={selectedArea} setSelectedArea={setSelectedArea} />
      </div>



      <div className="flex flex-col gap-2">
        <label htmlFor="salesUserId">担当者</label>
        <Select value={creatorUserId || ""} onValueChange={(value) => setCreatorUserId(value)} disabled={agentUsers.length === 0}>
          <SelectTrigger>
            <SelectValue placeholder="担当者を選択してください" />
          </SelectTrigger>
          <SelectContent>
            {agentUsers.map((user) => (
              <SelectItem key={user.id} value={user.id || ""}>[{user.id}] {user.name} | {user.email}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex flex-row gap-2">
        <Button onClick={handleSave} disabled={isLoading || !creatorUserId}>
          {isLoading ?
            <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : ""}
          保存
        </Button>

        <Button onClick={handleReset} disabled={isLoading || !creatorUserId} variant="destructive">
          {isLoading ?
            <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : ""}
          RESET ALL
        </Button>
      </div>
    </div>
  </div>;
}     