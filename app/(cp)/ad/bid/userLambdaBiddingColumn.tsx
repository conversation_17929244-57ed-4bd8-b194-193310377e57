import {
  UserLambdaRecordBidProps,
  TllUserLambdaRecordBidStatusEnum,
} from "@/lib/definitions/userLambdaRecordBid";
import { Badge } from "@/components/ui/badge";
import { getColor } from "@/lib/color";
import dayjs from "dayjs";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Check, CheckCheck } from "lucide-react";
import { getLatestChangeValue } from "@/components/userLambdaRecord/priceChangeUtilities";
import {
  getPropertyStatusColor,
  getStatusColor,
  renderStatusCount,
} from "./checklist/constants";
import Image from "next/image";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";

export const userLambdaBiddingColumns = [
  {
    header: "",
    accessorKey: "index",
    cell: ({ row }: { row: any }) => {
      return (
        <div className="flex flex-col justify-center items-center gap-1">
          {(row.index as number) + 1}
        </div>
      ); // 使用shadui的Link组件
    },
  },
  {
    header: "ステータス",
    accessorKey: "status",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
      if (row.original.status) {
        const status =
          TllUserLambdaRecordBidStatusEnum[
            row.original
              .status as unknown as keyof typeof TllUserLambdaRecordBidStatusEnum
          ];
        return status !== undefined ? (
          <Badge variant="outline" className={getStatusColor(status)}>
            {status}
          </Badge>
        ) : (
          "未設定"
        );
      }
      return "未設定";
    },
  },
  {
    header: "物件ID",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => (
      <div className="flex flex-col justify-center items-center gap-1">
        <Link
          href={`/ex/search/${row.original.recordId}`}
          className="underline"
        >
          {row.original.recordId.slice(0, 6)}
        </Link>
        {row.original.tllUserLambdaRecord?.propertyAnalysisResult
          ?.overallStarLevel > 0 && (
          <Badge
            variant="outline"
            style={{
              backgroundColor: getColor(
                row.original.tllUserLambdaRecord?.propertyAnalysisResult
                  ?.overallStarLevel || 0,
              ),
              color: "white",
              borderColor: "transparent",
            }}
          >
            {
              row.original.tllUserLambdaRecord?.propertyAnalysisResult
                ?.overallStarLevel
            }
          </Badge>
        )}
      </div>
    ),
  },
  {
    header: "状態 | 最後更新",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => (
      <div className="flex flex-col gap-1 justify-center items-center">
        {row.original.tllUserLambdaRecord && (
          <Badge
            variant="outline"
            className={getPropertyStatusColor(
              getStatus(row.original.tllUserLambdaRecord),
            )}
          >
            {getStatus(row.original.tllUserLambdaRecord)}
          </Badge>
        )}

        <span className="text-xs text-gray-500">
          {getLatestChangeValue(row.original.tllUserLambdaRecord, "recordDate")}
          <span
            style={{
              color:
                dayjs().diff(
                  dayjs(
                    getLatestChangeValue(
                      row.original.tllUserLambdaRecord,
                      "recordDate",
                    ),
                  ),
                  "days",
                ) <= 7
                  ? "red"
                  : "gray",
            }}
          >
            (
            {dayjs().diff(
              dayjs(
                getLatestChangeValue(
                  row.original.tllUserLambdaRecord,
                  "recordDate",
                ),
              ),
              "days",
            )}
            日前)
          </span>
        </span>
      </div>
    ),
  },
  {
    header: "物件価格",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => (
      <div>{row.original.recordPrice}万円</div>
    ),
  },
  {
    header: "TLL目線",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
      let bidPrice =
        row.original.tllUserLambdaRecord.analysisSimulationResults
          ?.optimalBiddingPriceCalulation?.bidPrice;
      // show empty for now
      return (
        <div className="flex flex-col items-center">
          <span className="">{bidPrice?.toFixed(0) || "-"}</span>

          <span
            className={`text-xs ${bidPrice ? ((bidPrice / row.original.tllUserLambdaRecord.price) * 100 > 120 ? "text-green-700 font-bold" : (bidPrice / row.original.tllUserLambdaRecord.price) * 100 > 85 ? "text-green-400" : "text-neutral-200") : "inherit"}`}
          >
            {bidPrice
              ? (
                  (bidPrice / row.original.tllUserLambdaRecord.price) *
                  100
                ).toFixed(1) + "%"
              : "-"}
          </span>
        </div>
      );
    },
  },
  {
    header: "入札価格",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => (
      <div className="flex flex-col">
        {row.original.biddingPrice && (
          <>
            <span>{row.original.biddingPrice}万円</span>
            <span className="text-xs text-gray-500">
              LTV:{" "}
              {(
                (row.original.biddingPrice / row.original.recordPrice) *
                100
              ).toFixed(0)}
              %
            </span>
          </>
        )}
      </div>
    ),
  },
  {
    header: "入札結果",
    accessorKey: "biddingPrice",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => (
      <div className="flex flex-col">
        {row.original.biddingResult && (
          <span className="">{row.original.biddingResult}</span>
        )}
      </div>
    ),
  },
  {
    header: "タイプ",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => (
      <div className="flex flex-col gap-0.5">
        {row.original.tllUserLambdaRecord?.recordType}
        <span className="text-xs text-gray-500">
          {row.original.tllUserLambdaRecord?.recordSubType}
        </span>
      </div>
    ),
  },
  {
    header: "物件詳細",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => (
      <div className="flex flex-col gap-0.5">
        <span className="flex gap-1 justify-center items-center">
          {row.original.isSumitomoKeibai ? (
            <Badge variant="secondary">競売 </Badge>
          ) : (
            ""
          )}
          {row.original.tllUserLambdaRecord?.address}
        </span>

        <span className="text-xs text-gray-500">
          {row.original.tllUserLambdaRecord?.landSize}m² |{" "}
          {row.original.tllUserLambdaRecord?.buildingSize}m² |{" "}
          {row.original.tllUserLambdaRecord?.buildingMaterial || "-"} |{" "}
          {row.original.tllUserLambdaRecord?.buildingBuiltYear}年
        </span>
      </div>
    ),
  },
  {
    header: "李　しょう",
    accessorKey: "salesUserId",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
      const salesUser = row.original.salesUser;
      return (
        <div className="flex flex-row gap-1 items-center justify-center">
          {salesUser?.imageUrl &&
            salesUser?.imageUrl !== "" &&
            salesUser?.imageUrl.includes("https") && (
              <Image
                src={salesUser.imageUrl}
                alt={salesUser.name || ""}
                width={20}
                height={20}
                className="rounded-full"
              />
            )}
          {salesUser ? salesUser.name?.slice(0, 8) : "未設定"}
          {/* <span className="text-xs text-gray-500">
          {salesUser ? salesUser.email : ""}
        </span> */}
        </div>
      );
    },
  },
  {
    header: "コメント",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
      return (
        <div className="overflow-hidden text-ellipsis whitespace-pre-wrap text-xs text-center">
          {row.original.comments ? row.original.comments.slice(0, 40) : "-"}
        </div>
      );
    },
  },
  // {
  //   header: "データ",
  //   cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
  //     return (
  //       <div>
  //         {row.original.dataLink ? <Link href={row.original.dataLink} className="underline">リンク</Link> : "-"}
  //       </div>
  //     )
  //   }
  // },
  {
    header: "資料数",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
      return (
        <div className="text-center">
          {row.original.tllUserLambdaRecord?.materialMappings?.length || "-"}
        </div>
      );
    },
  },
  {
    header: "CL要約",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
      return <div>{renderStatusCount(row.original)}</div>;
    },
  },
  {
    header: "操作",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
      return (
        <div className="flex gap-2 justify-center">
          <Button variant="outline" size="sm">
            <Link href={`/ad/bid/${row.original.id}/edit`} className="">
              {" "}
              修正{" "}
            </Link>
          </Button>
        </div>
      );
    },
  },
  {
    header: "作成日時",
    accessorKey: "createdAt",
    cell: ({ row }: { row: { original: UserLambdaRecordBidProps } }) => {
      return (
        <div className="text-center text-sm text-gray-500">
          {row.original.createdAt
            ? dayjs(row.original.createdAt).format("YYYY/MM/DD")
            : "未設定"}
        </div>
      );
    },
  },
];
