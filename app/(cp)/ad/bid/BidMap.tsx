import LeafletMap from "@/components/LeafletMap"
import { SumitomoAuctionProps } from "@/lib/definitions"
import { UserLambdaRecordBidProps } from "@/lib/definitions/userLambdaRecordBid"
import dayjs from "dayjs"
import { LatLngExpression } from "leaflet"
export default function BidMapPage({ bids, sumitomoAuctions }: { bids: UserLambdaRecordBidProps[], sumitomoAuctions: SumitomoAuctionProps[] }) {

  return <div className="">
    <LeafletMap height={"80vh"} zoom={11} data={[
      ...(bids?.map(bid => {
        return {
          name: bid.tllUserLambdaRecord?.recordType + " " + bid.tllUserLambdaRecord?.compositeTitle,
          colorDot: bid.status === "FOUR_CONTRACT" || bid.status === "FIVE_HANDOVER" ? "red" : "green",
          link: "/ex/search/" + bid.tllUserLambdaRecord?.id,
          coordinate: [bid.tllUserLambdaRecord?.latitude, bid.tllUserLambdaRecord?.longitude] as LatLngExpression
        }
      }) || []),
      ...(sumitomoAuctions?.filter(auction => auction.isFav && auction.latitude && auction.longitude).map(auction => {
        return {
          name: auction.name + " " + dayjs(auction.bidEndDate).format("YYYY/MM/DD"),
          colorDot: "yellow",
          link: auction?.lambdaRecord?.id ? "/ex/search/" + auction?.lambdaRecord?.id : auction.auctionUrl,
          coordinate: [auction.latitude!, auction.longitude!] as [number, number]
        }
      }) || []),
    ]} legend={<div>
      <div className="flex flex-row gap-2 bottom-0 absolute z-999 text-center justify-center bg-neutral-50 p-1">
        <div className="w-4 h-4 bg-blue-500"></div>
        <div>中心点</div>

        <div className="w-4 h-4 bg-red-500"></div>
        <div>自社物件({bids?.filter(bid => bid.status === "FOUR_CONTRACT" || bid.status === "FIVE_HANDOVER").length}件)</div>

        <div className="w-4 h-4 bg-green-500"></div>
        <div>検討中物件({bids?.filter(bid => bid.status === "ONE_REQUEST_DATA" || bid.status === "TWO_NEGOTIATION" || bid.status === "THREE_DOCUMENT_PROPERTY_CHECK").length}件)</div>

        <div className="w-4 h-4 bg-yellow-500"></div>
        <div>検討中SUMIFU物件({sumitomoAuctions?.filter(auction => auction.isFav && auction.latitude && auction.longitude).length}件)</div>
      </div>
    </div>} />
  </div>
}