"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { UserLambdaRecordBidProps } from "@/lib/definitions/userLambdaRecordBid";
import { deleteUserLambdaRecordBidAction, updateUserLambdaRecordBidAction } from "@/actions/tllUserLambdaRecordBid";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { useCallback, useEffect, useState } from "react";
import { toast } from "@/hooks/use-toast";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { CHECKLIST_ITEMS } from "../../checklist/constants";
import { useParams } from "next/navigation";
import { Loader2, RefreshCcw } from "lucide-react";


export default function CheckListSheet({ currentBid }: { currentBid: UserLambdaRecordBidProps }) {
  const [fullChecklist, setFullChecklist] = useState<{ [key: string]: string }>({}); // e.g. upside-0: ""  
  const params = useParams();
  const id = params.id as string;
  let [isSaving, setIsSaving] = useState(false);

  const refreshPage = async () => {
    if (!currentBid || !currentBid.id) return;
    setFullChecklist(currentBid.checklist as any);
  }

  useEffect(() => {
    // When opening in new page
    refreshPage();
  }, []);


  const handleValueChange = useCallback((fullKey: string, key: keyof typeof fullChecklist, value: string) => {
    setFullChecklist((prev) => ({
      ...prev,
      [fullKey]: {
        ...prev[fullKey] as any,
        [key]: value,
      }
    }));
  }, []);

  const saveCheckListItems = async () => {
    if (!currentBid || !currentBid.id) return;

    setIsSaving(true);
    const res = await updateUserLambdaRecordBidAction(currentBid.id, {
      checklist: fullChecklist,
    } as any);

    setFullChecklist(res.data.checklist as any);

    if (res.success) {
      toast({
        title: "チェックリストが正常に保存されました",
      });
    } else {
      toast({
        title: "チェックリストの保存に失敗しました",
      });
    }
    setIsSaving(false);
  }

  return <div>
    <div className="flex flex-col gap-2 h-[calc(100vh-10rem)] overflow-y-auto pb-20 p-2 sm:p-4">
      {Array.from(new Set(Object.values(currentBid?.checklist || {}).map((item: any) => item.key))).sort((a: any, b: any) => Object.keys(CHECKLIST_ITEMS).indexOf(a) - Object.keys(CHECKLIST_ITEMS).indexOf(b)).map(
        (categoryKey: any) => {
          let items = Object.values(currentBid?.checklist || {}).filter((item: any) => item.key === categoryKey);

          return <div key={categoryKey} className="flex flex-col gap-2 my-2">
            <div className="text-sm font-bold border-b border-gray-200 pb-2 bg-gray-50 pt-2 flex flex-row items-center justify-between">
              <span className="flex-1">{categoryKey}</span>

              <div className="flex flex-row gap-2">
                <Badge variant="outline">{items.filter((item: any) => item.answerType === "").length}未入力</Badge>
                {items.filter((item: any) => item.answer === "ERROR").length > 0 && <Badge variant="destructive">{items.filter((item: any) => item.answer === "ERROR").length}ERROR</Badge>}
              </div>
            </div>

            {items.map((item: any) => {
              return <div className="flex flex-col sm:flex-row gap-2 sm:items-center sm:justify-start text-left" key={item.question}>
                <div className="text-sm basis-full sm:basis-1/3 text-left">{item.question}</div>
                <div className="text-sm basis-full sm:basis-2/3 flex flex-row gap-2">
                  <div className="w-[120px]">
                    <Select value={
                      (fullChecklist[item.fullKey as keyof typeof fullChecklist] as any)?.answerType as string || ""
                    } onValueChange={(value) => handleValueChange(item.fullKey, "answerType", value)}>
                      <SelectTrigger className={`${(fullChecklist[item.fullKey as keyof typeof fullChecklist] as any)?.answerType === "OK" ? "bg-green-200" : (fullChecklist[item.fullKey as keyof typeof fullChecklist] as any)?.answerType === "WARN" ? "bg-yellow-200" : (fullChecklist[item.fullKey as keyof typeof fullChecklist] as any)?.answerType === "ERROR" ? "bg-red-200" : ""}`}>
                        <SelectValue placeholder="Select..." className="w-[24px]" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="OK">OK</SelectItem>
                        <SelectItem value="WARN">WARN</SelectItem>
                        <SelectItem value="ERROR">ERROR</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Input value={
                    (fullChecklist[item.fullKey as keyof typeof fullChecklist] as any)?.answer as string || ""
                  } onChange={(e) => handleValueChange(item.fullKey, "answer", e.target.value)} />
                </div>
              </div>
            })}
          </div>
        })}
    </div>

    {currentBid.checklist && <div className="border-t border-gray-500 bg-neutral-50 absolute bottom-0 left-0 w-full p-4 bg-white z-10 overflow-hidden">
      <Button variant="default" className="w-full" onClick={() => saveCheckListItems()} disabled={isSaving}>
        {isSaving ? <Loader2 className="w-4 h-4 animate-spin" /> : "CLを保存"}
      </Button>
    </div>
    }
  </div>;
}
