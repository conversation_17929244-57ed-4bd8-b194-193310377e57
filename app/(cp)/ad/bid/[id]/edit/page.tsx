"use client";

import { useParams } from "next/navigation";
import { useUserLambdaRecordBidStore } from "@/store/userLambdaRecordBid";

import { useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { deleteUserLambdaRecordBidAction, getUserLambdaRecordBidAction, updateUserLambdaRecordBidAction } from "@/actions/tllUserLambdaRecordBid";
import { UserLambdaRecordBidProps, TllUserLambdaRecordBidStatusEnum } from "@/lib/definitions/userLambdaRecordBid";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import Link from "next/link";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { DataTable } from "@/components/ui/data-table";
import { userlambdaRecordTableColumnsCommonSearch } from "@/components/userLambdaRecord/userlambdaRecordTableColumnsCommon";
import { AlertDialog, AlertDialogTitle, AlertDialogDescription, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { getListOfTllAgentUserAction } from "@/actions/tllUserLambdaRecordFromSumifuAction";
import Checklist from "./Checklist";
import { CHECKLIST_ITEMS } from "../../checklist/constants";
import { Switch } from "@/components/ui/switch";

export default function BidHistoryPage() {
  const params = useParams();
  const id = params.id as string;

  const [isLoading, setIsLoading] = useState(false);
  const [bid, setBid] = useState<UserLambdaRecordBidProps | null>(null);
  const [biddingPrice, setBiddingPrice] = useState<number | null>(null);
  const [biddingResult, setBiddingResult] = useState<number | null>(null);
  const [salesUserId, setSalesUserId] = useState<string | null>(null);
  const [comments, setComments] = useState<string | null>("");
  const [status, setStatus] = useState<TllUserLambdaRecordBidStatusEnum | null>(null);
  const [agentUsers, setAgentUsers] = useState<TllUserProps[]>([]);
  const [isSumitomoKeibai, setIsSumitomoKeibai] = useState<boolean>(false);
  const router = useRouter();

  const getData = async () => {
    const bid = await getUserLambdaRecordBidAction(id);
    if (bid.success) {
      let res = TllUserLambdaRecordBidStatusEnum[bid.data.status as unknown as keyof typeof TllUserLambdaRecordBidStatusEnum]

      setBid(bid.data);
      setBiddingPrice(bid.data.biddingPrice);
      setBiddingResult(bid.data.biddingResult);
      setComments(bid.data.comments);
      setStatus(bid.data.status);
      setSalesUserId(bid.data.salesUserId);
      setIsSumitomoKeibai(bid.data.isSumitomoKeibai);
    }
  }

  useEffect(() => {
    getData();

    getListOfTllAgentUserAction().then((res) => {
      if (res.success && res.data) {
        setAgentUsers(res.data);
      }
    });
  }, [id]);

  const handleUpdate = async () => {
    setIsLoading(true);

    console.log("biddingPrice", biddingPrice);
    let data: UserLambdaRecordBidProps = {
      id,
      ...(biddingPrice !== null && { biddingPrice }), // 仅在有值时添加 biddingPrice
      ...(comments !== null && { comments }), // 仅在有值时添加 comments
      ...(status !== null && { status }), // 仅在有值时添加 status
      ...(biddingResult !== null && { biddingResult }), // 仅在有值时添加 biddingResult
      ...(salesUserId !== null && { salesUserId }), // 仅在有值时添加 salesUserId
      ...(isSumitomoKeibai !== null && { isSumitomoKeibai }), // 仅在有值时添加 isSumitomoKeibai
    } as any as UserLambdaRecordBidProps;

    const res = await updateUserLambdaRecordBidAction(id, data);
    if (res.success) {
      toast({
        title: "更新しました..",
      });
      useUserLambdaRecordBidStore.getState().updateBid(res.data as UserLambdaRecordBidProps);

      router.push(`/ex/search/${bid?.tllUserLambdaRecord?.id}`);
    } else {
      toast({
        title: "更新に失敗しました",
      });
    }

    setIsLoading(false);
  }

  const applyTemplate = async () => {
    if (!bid || !bid.id) return;

    let getTemplateQuestions = {} as any;
    setIsLoading(true);

    Object.keys(CHECKLIST_ITEMS).forEach((key) => {
      CHECKLIST_ITEMS[key].forEach((question, index) => {
        getTemplateQuestions[`${key}-${index}`] = {
          fullKey: `${key}-${index}`,
          key: key,
          index: index,
          question: question,
          answer: "",
          answerType: "",
        }
      })
    })

    const res = await updateUserLambdaRecordBidAction(bid?.id as string, {
      checklist: getTemplateQuestions,
    } as any);

    if (res.success) {
      toast({
        title: "Checklist applied successfully",
      });

      // Fixme: should not reload next
      // Use store instead // 
      // useUserLambdaRecordBidStore.getState().updateBid(res.data as UserLambdaRecordBidProps);
      window.location.reload();
    } else {
      toast({
        title: "Failed to apply checklist",
      });
    }

    setIsLoading(false);
  }


  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-xl sm:text-2xl font-bold" aria-label="チェックリスト">  詳細修正: {bid?.tllUserLambdaRecord?.address} | {bid?.tllUserLambdaRecord?.price} | {bid?.tllUserLambdaRecord?.recordType}</h1>

      <div className="flex flex-row gap-2">
        {bid?.checklist ? <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="default" size="sm" disabled={!bid?.id}>
              CLリセット
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>本当にリセットしますか？</AlertDialogTitle>
              <AlertDialogDescription>
                この操作は元に戻すことはできません。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>キャンセル</AlertDialogCancel>
              <AlertDialogAction onClick={() => {
                applyTemplate()
              }}>APPLY</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog> : <Button variant="default" size="sm" onClick={() => {
          applyTemplate()
        }}>
          {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "雛形からCL作成"}
        </Button>}

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" size="sm" disabled={!bid?.id}>検討履歴削除</Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>本当に削除しますか？</AlertDialogTitle>
              <AlertDialogDescription>
                この操作は元に戻すことはできません。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>キャンセル</AlertDialogCancel>
              <AlertDialogAction onClick={() => {
                if (bid?.id) {
                  deleteUserLambdaRecordBidAction(bid.id);
                  toast({
                    title: "削除しました",
                  });
                  window.location.href = "https://www.urbalytics.jp/ad/bid"; // 刷新到指定的URL
                }
              }}>削除</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

      </div>
    </div>

    <div className="border-b border-t border-gray-200 bg-neutral-50 sticky top-0 z-10">
      <div className="p-4">
        {bid?.tllUserLambdaRecord && <DataTable columns={userlambdaRecordTableColumnsCommonSearch} data={[bid?.tllUserLambdaRecord]} showFooter={false} />}
      </div>
    </div>

    <div className="p-4 flex flex-col gap-4">

      <div className="flex flex-col gap-2">
        <label htmlFor="salesUserId">担当者</label>
        <Select value={salesUserId || ""} onValueChange={(value) => setSalesUserId(value)}>
          <SelectTrigger>
            <SelectValue placeholder="担当者を選択してください" />
          </SelectTrigger>
          <SelectContent>
            {agentUsers.map((user) => (
              <SelectItem key={user.id} value={user.id || ""}>{user.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-5 gap-2">
        <div className="flex flex-col gap-2 col-span-2">
          <label htmlFor="status">入札価格</label>
          <Input type="number" id="biddingPrice" value={biddingPrice || ""} onChange={(e) => setBiddingPrice(Number(e.target.value))} />
        </div>

        <div className="flex flex-col gap-2 col-span-2">
          <label htmlFor="biddingResult">入札結果</label>
          <Input type="number" id="biddingResult" value={biddingResult || ""} onChange={(e) => setBiddingResult(Number(e.target.value))} />
        </div>

        <div className="flex flex-col gap-2 col-span-1">
          <label htmlFor="isSumitomoKeibai">住友競売物件</label>
          <Switch id="isSumitomoKeibai" checked={isSumitomoKeibai} onCheckedChange={setIsSumitomoKeibai} />
        </div>
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="comments">コメント</label>
        <Textarea id="comments" value={comments || ""} onChange={(e) => setComments(e.target.value)} rows={4} />
      </div>

      <div className="flex flex-col gap-2">
        <label htmlFor="status">ステータス</label>
        <Select value={status || ""} onValueChange={(value) => setStatus(value as TllUserLambdaRecordBidStatusEnum)}>
          <SelectTrigger>
            {/* {TllUserLambdaRecordBidStatusEnum[bid?.status as unknown as keyof typeof TllUserLambdaRecordBidStatusEnum]} */}
            {TllUserLambdaRecordBidStatusEnum[status as unknown as keyof typeof TllUserLambdaRecordBidStatusEnum]}
          </SelectTrigger>
          <SelectContent>
            {Object.entries(TllUserLambdaRecordBidStatusEnum).map(([key, value]) => (
              <SelectItem key={key} value={key}>{value}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Button onClick={handleUpdate} disabled={isLoading || !bid?.id}>
          {isLoading ?
            <Loader2 className="w-4 h-4 animate-spin" /> :
            "詳細を更新"}
        </Button>
      </div>
    </div>

    <Separator />

    {bid && <Checklist currentBid={bid} />}
  </div>
}