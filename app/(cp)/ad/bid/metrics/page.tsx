"use client"

import { getBidForDashboard, getUserLambdaRecordBidsAction } from "@/actions/tllUserLambdaRecordBid";
import { <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useUserLambdaRecordBidStore } from "@/store/userLambdaRecordBid";
import { useEffect, useState } from "react";
import dayjs from "dayjs";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsTrigger } from "@/components/ui/tabs";
import { TabsList } from "@/components/ui/tabs";
import BidBar from "./BidBar";
import BidAnalysisBar from "./BidAnalysisBar";
import { UserLambdaRecordBidProps } from "@/lib/definitions/userLambdaRecordBid";

export default function SuDashboardPage() {
  const [bids, setBids] = useState<UserLambdaRecordBidProps[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [tab, setTab] = useState("bidding");
  const [unit, setUnit] = useState("weekly");

  const getData = async () => {
    setIsLoading(true);
    const res = await getBidForDashboard();

    if (res.success) {
      setBids(res.data as UserLambdaRecordBidProps[]);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    getData();
  }, []);

  let allGoals = {
    KTNTOU_GOAL: 360,
    BIDDING_GOAL: 120,
    BIDDING_ONE_GOAL: 36,
    KAITORI_PRICE: 100000,
  }

  let getGoalForCurrentMonthEnd = (goal: number): number => {
    let currentMonth = dayjs().month() + 1;
    return Math.round(goal / 12 * currentMonth);
  }

  let dataForCard = [
    // {
    //   title: "物件検討数",
    //   value: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025).length,
    //   goal: getGoalForCurrentMonthEnd(allGoals.KTNTOU_GOAL),
    //   percentage: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025).length / getGoalForCurrentMonthEnd(allGoals.KTNTOU_GOAL) * 100,
    // },
    {
      title: "入札(買付)数",
      value: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice).length,
      goal: getGoalForCurrentMonthEnd(allGoals.BIDDING_GOAL),
      percentage: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice).length / getGoalForCurrentMonthEnd(allGoals.BIDDING_GOAL) * 100,
    },
    {
      title: "入札一位数",
      value: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice && bid.biddingResult === 1).length,
      goal: getGoalForCurrentMonthEnd(allGoals.BIDDING_ONE_GOAL),
      percentage: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice && bid.biddingResult === 1).length / getGoalForCurrentMonthEnd(allGoals.BIDDING_ONE_GOAL) * 100,
    },
    {
      title: "契約物件総額",
      value: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice && (bid.status === "FOUR_CONTRACT" || bid.status === "FIVE_HANDOVER")).reduce((acc, bid) => acc + (bid.biddingPrice || 0), 0),
      goal: getGoalForCurrentMonthEnd(allGoals.KAITORI_PRICE),
      percentage: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice && (bid.status === "FOUR_CONTRACT" || bid.status === "FIVE_HANDOVER")).reduce((acc, bid) => acc + (bid.biddingPrice || 0), 0) / getGoalForCurrentMonthEnd(allGoals.KAITORI_PRICE) * 100,
    },
    {
      title: "決済済み総額",
      value: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice && (bid.status === "FIVE_HANDOVER")).reduce((acc, bid) => acc + (bid.biddingPrice || 0), 0),
      goal: getGoalForCurrentMonthEnd(allGoals.KAITORI_PRICE),
      percentage: bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice && (bid.status === "FIVE_HANDOVER")).reduce((acc, bid) => acc + (bid.biddingPrice || 0), 0) / getGoalForCurrentMonthEnd(allGoals.KAITORI_PRICE) * 100,
    },
  ]

  console.log("bids", bids.filter(bid => bid.createdAt && dayjs(bid.createdAt).year() === 2025 && bid.biddingPrice && (bid.status === "FOUR_CONTRACT" || bid.status === "FIVE_HANDOVER")));

  return (
    <div>
      <div className="flex flex-col gap-2 p-4">
        <h1 className="text-2xl font-semibold">入札ダッシュボード</h1>
      </div>

      <Separator className="mb-2" />

      <div className="p-4 flex flex-col gap-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {dataForCard.map((item) => (
            <Card className="rounded-lg py-2" key={item.title}>
              <CardHeader className="pb-0">
                <CardTitle>{item.title}</CardTitle>
              </CardHeader>
              <CardContent className="mt-2">
                <div className="text-3xl font-bold">
                  {item.value}
                  <span className="text-xs text-gray-400 mt-1 ml-2">
                    / {item.goal}
                    ({dayjs().month() + 1}月末目標)
                  </span>
                </div>

                <div className="flex flex-row gap-2 items-center">
                  <Progress value={item.percentage} className={`mt-2 ${item.percentage >= 100 ? "bg-green-500" : "bg-neutral-200"}`} />
                  <span className="text-xs text-gray-400 mt-2">
                    {item.percentage.toFixed(0)}%
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="flex flex-col sm:flex-row gap-4 w-full">
          <Card className="basis-2/3 relative">
            <div className="p-2">
              <div className="text-lg font-bold">入札分析</div>

              <div className="absolute top-2 right-2 flex flex-row gap-2">
                <Tabs value={tab} onValueChange={setTab} className="">
                  <TabsList>
                    <TabsTrigger value="bidding">入札数</TabsTrigger>
                    <TabsTrigger value="consideration">検討数</TabsTrigger>
                  </TabsList>
                </Tabs>

                <Tabs value={unit} onValueChange={setUnit} className="">
                  <TabsList>
                    <TabsTrigger value="weekly">週次</TabsTrigger>
                    <TabsTrigger value="monthly">月次</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </div>
            <div className="p-2">
              <BidBar bids={bids} type={tab} unit={unit} />
            </div>
          </Card>

          <div className="basis-1/3 flex flex-col gap-4" >
            <Card className="basis-2/3">
              <div className="p-2">
                <CardTitle className="text-lg">入札 - LTV分析</CardTitle>
              </div>
              <BidAnalysisBar bids={bids} orderByKey="ltv" />
            </Card>

            <Card >
              <div className="p-2">
                <CardTitle className="text-lg">入札 - タイプ</CardTitle>
              </div>
              <BidAnalysisBar bids={bids} orderByKey="recordType" />
            </Card>

            <Card >
              <div className="p-2">
                <CardTitle className="text-lg">入札 - 価格台(億単位)</CardTitle>
              </div>
              <BidAnalysisBar bids={bids} orderByKey="priceLevel" />
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}