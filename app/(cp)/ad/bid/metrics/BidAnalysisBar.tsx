import { UserLambdaRecordBidProps } from "@/lib/definitions/userLambdaRecordBid";
import dayjs from "dayjs";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts"
import isoWeek from "dayjs/plugin/isoWeek";
import { ChartConfig, ChartContainer, ChartLegendContent, ChartLegend, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { table } from "console";
import { decodeFormState } from "next/dist/server/app-render/entry-base";
dayjs.extend(isoWeek);

export default function BidAnalysisBar({ bids, orderByKey }: { bids: UserLambdaRecordBidProps[], orderByKey: "recordType" | "ltv" | "priceLevel" }) {
  let prepData = () => {
    let bidHasResutls = bids.filter((bid) => bid.createdAt && bid.biddingPrice && bid.biddingResult).map((bid) => ({
      ...bid,
      recordType: bid.tllUserLambdaRecord?.recordType,
      ltv: bid.biddingPrice ? Math.floor(bid.biddingPrice / bid.recordPrice * 100 / 10) * 10 : 0,
      priceLevel: bid.recordPrice ? Math.floor(bid.recordPrice / 10000) : 0
    })).sort((a, b) => a[orderByKey] - b[orderByKey]);

    let uniqueByKey = [...new Set(bidHasResutls.map((bid) => bid[orderByKey]))]

    let parseData = [] as any;

    uniqueByKey.forEach((key) => {
      let bids = bidHasResutls.filter((bid) => bid[orderByKey] === key);
      parseData.push({
        key,
        bid1: bids.filter((bid) => bid.biddingResult === 1).length,
        bid2: bids.filter((bid) => bid.biddingResult === 2).length,
        bid3: bids.filter((bid) => bid.biddingResult === 3).length,
        bidOthers: bids.filter((bid) => bid.biddingResult !== 1 && bid.biddingResult !== 2 && bid.biddingResult !== 3).length,
      })
    })

    return parseData;
  }

  const chartConfig = {
    bid1: {
      label: "bid1",
      color: "hsl(var(--chart-1))",
    },
    bid2: {
      label: "bid2",
      color: "hsl(var(--chart-2))",
    },
    bid3: {
      label: "bid3",
      color: "hsl(var(--chart-3))",
    },
    bidOthers: {
      label: "bidOthers",
      color: "hsl(var(--chart-4))",
    },
  }

  return Object.values(chartConfig).length ? <ChartContainer config={chartConfig}>
    <BarChart width={1200} height={240} data={prepData()}>
      <XAxis dataKey={"key"} tickLine={true} axisLine={true} tickFormatter={(value) => value.toString()} />
      <YAxis domain={[0, (dataMax: number) => dataMax + 5]} tickLine={true} axisLine={true} />
      <CartesianGrid stroke="#ccc" />
      <ChartTooltip content={<ChartTooltipContent />} />
      <ChartLegend content={<ChartLegendContent />} />
      {Object.keys(chartConfig).map((key) => (
        <Bar key={key} dataKey={key} stackId="a" fill={chartConfig[key as keyof typeof chartConfig].color} label={{ position: "top", formatter: (value: number) => value.toLocaleString() }} />
      ))}
    </BarChart>
  </ChartContainer> : "-"
}