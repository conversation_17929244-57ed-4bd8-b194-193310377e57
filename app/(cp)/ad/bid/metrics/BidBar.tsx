import { UserLambdaRecordBidProps } from "@/lib/definitions/userLambdaRecordBid";
import dayjs from "dayjs";
import { Bar, BarChart, CartesianGrid, XAxis, YAxis } from "recharts"
import isoWeek from "dayjs/plugin/isoWeek";
import { ChartConfig, ChartContainer, ChartLegendContent, ChartLegend, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { table } from "console";
dayjs.extend(isoWeek);

export default function BidBar({ bids, type, unit }: { bids: UserLambdaRecordBidProps[], type: string, unit: string }) {
  const chartConfig = {
    bidCount: {
      label: "入札数",
      color: "hsl(var(--chart-2))",
    },
    // wechatFollowers: {
    //   label: "微信公众号粉丝数",
    //   color: "hsl(var(--chart-2))",
    // },
    // wechatGroupMembers: {
    //   label: "微信社群人数",
    //   color: "hsl(var(--chart-3))",
    // },
  } satisfies ChartConfig


  let prepData = () => {
    let filtered2025 = bids.filter((bid) => bid.createdAt && (bid.biddingPrice || type === "consideration"));

    let padWeek = filtered2025.map((bid) => {
      let date = dayjs(bid.createdAt).startOf(unit === "weekly" ? 'isoWeek' : 'month').format('YYYY-MM-DD');
      return {
        date,
        ...bid
      }
    })

    let past12Periods = Array.from({ length: 12 }, (_, i) => dayjs().subtract(i, unit === "weekly" ? 'week' : 'month').startOf(unit === "weekly" ? 'isoWeek' : 'month').format('YYYY-MM-DD'));

    let aggregateData = past12Periods.map((period) => {
      let bids = padWeek.filter((bid) => bid.date === period);
      return {
        date: period,
        bidCount: bids.length
      }
    })

    return Object.values(aggregateData).sort((a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  return (
    <ChartContainer config={chartConfig}>
      <BarChart width={1000} height={1000} data={prepData()}>
        <XAxis dataKey="date" />
        <YAxis domain={[0, (dataMax: number) => dataMax + 5]} />
        <CartesianGrid stroke="#ccc" />
        <ChartTooltip content={<ChartTooltipContent />} />
        <ChartLegend content={<ChartLegendContent />} />
        {Object.keys(chartConfig).map((key) => (
          <Bar key={key} dataKey={key} fill={chartConfig[key as keyof typeof chartConfig].color} label={{ position: "top", formatter: (value: number) => value.toLocaleString() }} />
        ))}
      </BarChart>
    </ChartContainer>
  );
}