"use client";

import { DataTable } from "@/components/ui/data-table";
import { Separator } from "@/components/ui/separator";
import { getUserLambdaRecordBidsAction } from "@/actions/tllUserLambdaRecordBid";
import { useState } from "react";
import { useEffect } from "react";
import { SumitomoAuctionProps } from "@/lib/definitions";
import { TllUserLambdaRecordBidStatusEnum, UserLambdaRecordBidProps } from "@/lib/definitions/userLambdaRecordBid";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { userLambdaBiddingColumns } from "./userLambdaBiddingColumn";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Badge } from "@/components/ui/badge";
import { useUserLambdaRecordBidStore } from "@/store/userLambdaRecordBid";
import { Button } from "@/components/ui/button";
import { MapPin } from "lucide-react";
import { getSumitomoAuctionAction } from "@/actions/sumitomoAuction";
import BidMapPage from "./BidMap";
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer";
import { useAuthStore } from "@/store/auth";

export default function BidPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [agents, setAgents] = useState<TllUserProps[]>([]);
  const [sumitomoAuctions, setSumitomoAuctions] = useState<SumitomoAuctionProps[]>([]);
  const [selectedAgent, setSelectedAgent] = useState<string>("all");
  const { currentUser } = useAuthStore();
  const { bids, setBids } = useUserLambdaRecordBidStore()

  const getData = async () => {
    setIsLoading(true);
    const res = await getUserLambdaRecordBidsAction();
    if (res.success) {
      let d: any = {};
      res.data.filter((bid: UserLambdaRecordBidProps) => bid.salesUserId).forEach((bid: UserLambdaRecordBidProps) => {
        if (bid.salesUserId) {
          d[bid.salesUserId] = bid.salesUser;
        }
      });

      if (currentUser?.id && d[currentUser.id]) {
        setSelectedAgent(currentUser.id);
      }

      setAgents(Object.values(d));
      setBids(res.data);
    }
    setIsLoading(false);
  }

  const getSumitomoAuctions = async () => {
    const res = await getSumitomoAuctionAction();
    if (res.success) {
      setSumitomoAuctions(res.data);
    }
  }

  useEffect(() => {
    if (currentUser?.id && agents.length > 0) {
      if (agents.find(agent => agent.id === currentUser.id)) {
        setSelectedAgent(currentUser.id);
      }
    }
  }, [currentUser, agents]);

  useEffect(() => {
    // Just refetch data because you might have updated the data
    // FIXME: 
    if (bids.length === 0) {
      getData();
    }
    getSumitomoAuctions();
  }, []);

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold flex-1" aria-label="買取管理">
          買取管理
        </h1>

        <div className="flex flex-row gap-2">
          <Button variant="outline">
            Load
          </Button>

          <Drawer>
            <DrawerTrigger asChild>
              <Button variant={"outline"} >
                <MapPin className="w-4 h-4" />
              </Button>
            </DrawerTrigger>
            <DrawerContent className="h-[90vh]">
              <DrawerHeader>
                <DrawerTitle>マップ表示</DrawerTitle>
              </DrawerHeader>
              <BidMapPage bids={bids} sumitomoAuctions={sumitomoAuctions} />
            </DrawerContent>
          </Drawer>
        </div>
      </div>

      <Separator className="mb-2" />

      <div className="flex flex-row text-sm gap-2 overflow-x-auto scrollbar-hide w-full whitespace-nowrap px-4">
        <ToggleGroup
          type="single"
          value={selectedStatus}
          onValueChange={setSelectedStatus}
          className="mb-4"
        >
          <ToggleGroupItem value="all" className="whitespace-nowrap">全て</ToggleGroupItem>
          {Object.entries(TllUserLambdaRecordBidStatusEnum).map(([key, value]) => (
            <ToggleGroupItem key={key} value={key} className="whitespace-nowrap">
              {value}
              <Badge variant="outline">{bids.filter(bid => bid.status === key).length}</Badge>
            </ToggleGroupItem>
          ))
          }
        </ToggleGroup>

        <ToggleGroup
          type="single"
          value={selectedAgent}
          onValueChange={setSelectedAgent}
          className="mb-4 border-l pl-4"
        >
          <ToggleGroupItem value="all" className="whitespace-nowrap">全て</ToggleGroupItem>
          {agents.filter(agent => agent.id).map((agent, index) => (
            <ToggleGroupItem key={index} value={agent.id as string} className="whitespace-nowrap">
              {agent.name}
              <Badge variant="outline">{bids.filter(bid => bid.salesUserId === agent.id).length}</Badge>
            </ToggleGroupItem>
          ))}
        </ToggleGroup>
      </div>

      <Separator className="mt-[-6]" />

      <div className="p-4 flex flex-col gap-4">
        {Array.from(new Set(bids.map(bid => bid.status))).sort((a, b) => {
          const order = [
            "ONE_REQUEST_DATA",
            "TWO_NEGOTIATION",
            "THREE_DOCUMENT_PROPERTY_CHECK",
            "FOUR_CONTRACT",
            "FIVE_HANDOVER",
            "NINE_CANCEL",
          ];
          return order.indexOf(a as string) - order.indexOf(b as string);
        }).filter(status => selectedStatus === "all" || status === selectedStatus).map(status => (
          <div key={status} className="mb-2 bg-gray-50 rounded-md">
            <h2 className="text-xl font-semibold mb-2 p-2 border-b border-gray-200">{TllUserLambdaRecordBidStatusEnum[status as unknown as keyof typeof TllUserLambdaRecordBidStatusEnum]}
            </h2>

            <div className="p-2">
              <DataTable
                data={bids.filter(bid => bid.status === status && (selectedAgent === "all" || bid.salesUserId === selectedAgent))}
                columns={userLambdaBiddingColumns}
                isLoading={isLoading}
                showFooter={false}
              />
            </div>
          </div>
        ))}
      </div>


    </div>
  )
}