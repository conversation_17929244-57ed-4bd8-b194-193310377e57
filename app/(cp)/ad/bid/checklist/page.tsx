"use client";

import { Separator } from "@/components/ui/separator";
import { CHECKLIST_ITEMS } from "./constants";

export default function ChecklistPage() {
  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="入札チェックリスト">入札チェックリスト</h1>
    </div>

    <Separator className="mb-2" />

    <div className="flex flex-col gap-2 p-4 text-sm">
      {Object.keys(CHECKLIST_ITEMS).map((item) => (
        <div key={item} className="flex flex-col gap-2 bg-gray-100 p-2 rounded-md">
          <h2 className="text-lg font-bold border-b border-gray-200 pb-2">{item}</h2>
          {CHECKLIST_ITEMS[item].map((question) => (
            <p key={question}>{question}</p>
          ))}
        </div>
      ))}
    </div>
  </div>;

}