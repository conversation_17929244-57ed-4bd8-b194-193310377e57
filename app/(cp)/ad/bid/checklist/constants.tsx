import { UserLambdaRecordBidProps } from "@/lib/definitions/userLambdaRecordBid";

export const CHECKLIST_ANSWER_ENUM = [
  "OK",
  "WARN",
  "ERROR",
] as const;

export const getStatusColor = (status: string) => {
  if (!status) {
    return "bg-gray-100 border-none";
  }

  if (status.indexOf("キャンセル") !== -1) {
    return "bg-red-100 border-none";
  }

  return "bg-gray-100 border-none";
}

export const getPropertyStatusColor = (status: string | undefined) => {
  if (!status) {
    return "bg-gray-100 border-none";
  }

  if (status?.indexOf("成約") !== -1 || status?.indexOf("申込") !== -1) {
    return "bg-red-100 border-none";
  }

  return "bg-gray-100 border-none";
}

export const getStatusCount = (bid: UserLambdaRecordBidProps, status: string) => {
  return Object.values(bid.checklist || {}).filter((item: any) => item.answerType === status).length || 0;
}

export const renderStatusCount = (bid: UserLambdaRecordBidProps) => {
  return bid.checklist ? (
    <div className="flex flex-row gap-2 items-center justify-center">
      <span className="text-xs text-gray-500">
        {getStatusCount(bid, "")}
      </span>
      {getStatusCount(bid, "WARN") > 0 && <span className="text-xs text-yellow-500">
        {getStatusCount(bid, "WARN")}
      </span>}
      {getStatusCount(bid, "ERROR") > 0 && <span className="text-xs text-red-500">
        {getStatusCount(bid, "ERROR")}
      </span>}
    </div>
  ) : ""
}

export const CHECKLIST_ITEMS = {
  "初期チェック": [
    "[Urba数字確認] 年収入(チラシと確認)/相続路線価/建蔽率/容積率(実質)/部屋数/建物階数を再確認して",
    "[CF計算式確認A] 80%Percentileはどのぐらい? 成約と履歴ありの物件を絞って計算してください、癖ありの物件の場合は要調整(e.g. 再建築不可70%, 借地70%, 底地30%)",
    "[CF計算式確認B] リフォームなど費用?(原状回復1万/m2, リフォーム10万/m2, 大規模修繕m2*1.5万)を確認して、TLL目線確認を再確認 c) 賃料アップサイド",
    "[CF計算式確認C] 賃料アップサイドありますか? 現賃料は稼働率に応じて調整、アップサイド後賃料は(基本的に相場との差異の1/3ぐらいの実現を想定可能).",
    "[CF計算式確認D] ホテル・民泊・シェアハウス運営の物件の場合、賃料を普通賃借として計算",
    "[TLL目線確認] ABCDを考慮し、TLL目線は? 基本的に80%以下は入札なし",

    "アップサイド - 駅： 他の利用駅がありますか? 駅周辺大開発プラン?",
    "アップサイド - 出口: 区域区分は正しい? 区域区分民泊・旅館可能？住宅ローン可能?シェアハウス・寮ニーズ?",
    "アップサイド - 賃料: 部屋数? 平均賃料適切ですか？(高い場合)理由ありますか？持続できますか？UPいくらなりますか?実現可能でしょうか？",
    "アップサイド - 賃料: 現行アップサイドますか？駐車場・LUUP・自販機の導入は可能？",
    "アップサイド - 建築プラン: 有無? 土地面積の使用割合？利用してない容積率"
  ],
  "契約前 - 書類チェック": [
    "相続路線価: T3Y(直近3年の)上昇率は?",
    "固定資産税: (セットバックあれば)削除再計算はいくら？",
    "固定資産税: Urbaの数字は適切ですか?((固定資産税*1.13)",
    "売却経緯: Urbalytics上以前成約履歴? REINS上以前成約履歴を確認?",
    "売却経緯: 売主個人法人？住所・URLは？経緯は？ローンどのぐらい残り？価格交渉最低ラインは？",
    "駅: 駅利用者> 30000 - ここ数年のトレンドは? 最寄駅と距離は正しい？",
    "用途地域:  用途地域は？敷地面積の最低制限あり？法定容積率・建蔽は正しい？",
    "稼働: 稼働率どのぐらい？稼働率が高い・低い理由は？(店舗)昔テナントは？賃料推移ありますか",
    "稼働: 今募集中がありますか？RRと相違がありますか？",
    "管理: ランニングコストの詳細, 目立ったコストはないのでしょうか? インタネット・共有水道・電気はいくら？",
    "管理: 修繕履歴の記録はあるか? 修繕入りかますか？費用は？",
    "管理: 管理会社: どこの会社？募集はどうやって？専任ですか？解約: 解約条件は？売買により解約は違約金が発生する？PM費用は?",
    "管理入居: 長い賃借人がいますか？出たら賃料改正リスクはありますか？専有面積と契約面積は一緒ですか？",
    "管理入居: 各部屋の専有面積 ? SUM(専有面積) = 謄本上記録の面積ですか ?敷金ある？合計は合ってるのか? 敷金等の一時金の有無、金額、返還の要否",
    "管理入居: 生活保護: 何人？どうやって納付？滞納があるか？(70歳+)高齢者: 保証人 OR 保証会社？見守りサービス？",
    "管理入居: 普通賃貸借か定期賃貸借か、契約期間、自動更新の有無, フリーレント有無？賃貸借契約とRRに合ってるかどうか",
    "管理入居: すべでの入居者について：駆け付け会社が入ってるのか？火災保険？保証会社？敷金？入居時の賃料推移？",
    "管理入居: 一時金の額: 賃料、共益費、敷金・礼金等の一時金の額、償却の有無等？原状回復に係る契約内容修繕に係る費用負担区分(所有者負担かテナント負担の別)",
    "ライフライン:(上水)給水分岐管理図 / 水道管路情報 - ありますか？私営ですか？公営ですか？整備/埋設状況/引き込める状況、口径どのぐらい、変更工事入りますか? 13cmの場合、2階に行かない(水圧不足), 20cmに変更OR加圧タンクを設置 (both need 10w+)",
    "ライフライン:(下水)水道配置図 / 公共下水道台帳管理図 - 净化槽の配管経路、口径、埋設状況、メンテンス方法、費用など? 汚水、雑排水、雨水的排水系统 ? 埋設管越境(ekkyou)有無",
    "ライフライン: どちらの道路で埋設してますか？道路の通行・掘削証明がありますか？",
  ],
  "契約前 - 注意点": [
    "遵法性: 建蔽率・容積率オーバー？解消できる？",
    "遵法性: 藤本面積あってますか？いつの謄本？売主何人？",
    "遵法性: 未登記増築？",
    "遵法性: 再建築不可？",
    "遵法性: 謄本と台帳の一致性？",
    "遵法性: 東京都前面空地条例(建筑物在道路一侧必须留出一定的空地, 住宅：前面空地 至少 1.5m~2.0m)",
    "遵法性: 前面道路の掘削通行は？前面道路は公道ではない場合、隣地に通行しますか？(府中のようにリンチと揉めてる可能性ありますか)",
    "遵法性: 建築確認？検査済み有無？築年？(ASUKAの場合、平成15/2以降は基本NG　？台帳記載証明書あるかどうか?申請書面積と登記面積あってますか?",
    "遵法性: (2025/01~)四号建筑物建造木造两层建筑/200㎡+ですか? (结构审查可能性あり)",
    "遵法性: 2024以降一軒家はZEN?住宅ローン適用?",
    "接道: 接道義務を満たしますか？(接道) 确认 a) 幅 b) 間口 側溝ある？4mに含める? (市町村に確認) ref 道路台帳 | 私道の利用合意書(通行掘削合意書)",
    "接道: (建築上)道路種別: 42条1项4号 / 42条1项5号などの確認",
    "接道: 42条1项5号/位置指定道路(申請図 / 指定道路調書 - 指定番号? 現状幅員？認定幅員? 延長距離? 隅切りは2M以上か？",
    "接道: 42条2项道路/みなし道路 (申請図 / 指定道路調書 - 指定道路調書がありますか？ 中心線はどこ？一方後退になりますか？ 基道はどこからどこまで？セットバック入ります？後退どのぐらい？セットバックしたら遵法性？",
    "接道: 私道ありますか？利用するため合意書ありますか？",
    "接道: 隣地道路利用する？(e.g. 府中) 通行権がなければ必ず取得? 黙認はNG",
    "接道: 土地の形と接道を確認、売買対象を確認、全部隣地番号ありますか？国有地ありますか？",
    "癖あり: https://www.oshimaland.co.jp/ | 告知事項ありますか?",
    "癖あり: 旧耐震・借地権？",
    "癖あり: 文化財",
    "癖あり: 再建築不可？解消可能？",
    "点検: エレベーター状況、点検レポート？",
    "点検: 貯水槽状況、点検レポート？",
    "点検: 擁壁、点検レポート",
    "業者: Check https://www.houjin-bangou.nta.go.jp/",
  ],
  "契約前 - 現地確認": [
    "駅: 駅までの道路はどうですか、駅周辺再開発プランありますか？",
    "境界: 測量vs現状の差、面積あってますか？(地積測量図を参照) 境界確認できますか？境界の界標が埋設 / 破損の状況 / 境界杭の有無 / 距離の正確(境界プレート / 杭KUI / コンクリート杭 / 金属鋲BYOU)",
    "境界: 近隣物件との隙間 + 越境物 if any? 地上げ対象は?",
    "嫌恶设施: 近隣との距離チェック、越境物，高空电压线などチェク?",
    "嫌恶设施: フェンスのこと?",
    "遵法性: 増築ある？未登記の部分がある？倉庫 or 物置？設計通り施工したか ? 車庫転とかはないか？",
    "環境: 日あたりどうですか? 臭气ありますか？倾斜有、高低差ありますか? 擁壁がある場合その高さと劣化のゆうむ",
    "外装: クラック・ひびチェック? 塗装ありますか? 外壁の劣化はどうですか?",
    "屋上: 屋上からのパイプは大丈夫？壁から離れたことある？壁に泥が付着していませんか？(雨が降ってきたため）？雨どい、クーラー室外機越境？屋上チェック、防水は大丈夫?"
  ],
  "決済前 - 管理関連": [
    "名義変更: ライフライン(電気・水道)との切り替えに関して連絡",
    "名義変更: JCOM名義変更",
    "名義変更: ランニングコスト(町会費、掃除費用)確認・名義変更、給湯器はリース(名義変更可能?)",
    "名義変更: ランニング収入(自販機、ソフトバンク基地、電柱など)の変更",
    "残置物撤去",
    "(空室)内見予約 & 実行",
    "決済前の設備修理",
  ],
  "決済前 - 賃貸関連": [
    "追い出すテナントの予定がある場合、進捗状況",
    "不足の更新書類を確認・結び直す",
    "不足の契約書類を確認・結び直す",
    "更新・契約書類精査とLARKで記録",
    "保証会社確認 & 変更連絡",
    "契約 - 決済の間の空室がある場合の原状回復",
    "リースバック - あれば契約対応",
    "貸主変更のお知らせ"
  ],
  "決済前 - 権利関係": [
    "司法書士(所有権移転)選定",
    "司法書士(抵当権)選定",
    "確定測量完了状況",
    "越境是正状況",
    "再建築不可解消 & 考慮",
  ],
  "決済前 - 総合": [
    "売主様の口座情報確認",
    "精算書作成、確認",
    "資金調達(銀行)を決める"
  ]
} as {
  [key: string]: string[];
};