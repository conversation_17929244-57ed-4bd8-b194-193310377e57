"use client"

import { houseRentColumns, buildingRentColumns, mansionRentColumns } from "@/app/(cp)/an/mansion/[id]/mansionRentColumns"
import NoPermissionBox from "@/app/(cp)/my/usage/NoPermissionBox"

import { DataTable } from "@/components/ui/data-table"
import { TllUserProps } from "@/lib/definitions/tllUser"
import { useEffect, useMemo, useState } from "react"
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord"
import { ColumnDef } from "@tanstack/react-table"
import RentInsights from "./RentInsights"
import { Select, SelectItem, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { GeoRailwayStationRentsRoomLayoutType } from "@prisma/client"
import { ExportCSVButton } from "@/components/ExportCsvButton"
import BlogRelated from "../BlogRelated"

export default function RentPage({ rentData, currentUser, selectRecordType, selectedOption }: { rentData: any, currentUser: TllUserProps, selectRecordType: UserLambdaRecordType, selectedOption: { label: string, value: number, type: string } }) {
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const [selectedPrice, setSelectedPrice] = useState<string>("ALL");
  const [selectedFloor, setSelectedFloor] = useState<string>("ALL");
  const [selectedRoomLayout, setSelectedRoomLayout] = useState<string>("ALL");

  useEffect(() => {
    setColumns(selectRecordType === UserLambdaRecordType.BUILDING ? buildingRentColumns : selectRecordType === UserLambdaRecordType.HOUSE ? houseRentColumns : mansionRentColumns);
  }, [selectRecordType]);

  let filterRecords = (rent: any) => {
    if (selectedPrice !== "ALL" && rent.unitSize < parseInt(selectedPrice)) {
      return false;
    }

    if (selectedFloor !== "ALL" && rent.unitLevel < parseInt(selectedFloor)) {
      return false;
    }

    if (selectedRoomLayout !== "ALL") {
      // 使用正则表达式提取开头的数字
      const match = rent.unitLayout.match(/^\d+/);
      const roomCount = match ? parseInt(match[0], 10) : null;

      if (roomCount !== null && roomCount < parseInt(selectedRoomLayout)) {
        return false;
      }
    }

    return true;
  };

  const renderRentInsights = useMemo(() => {
    return (
      <RentInsights
        rentData={rentData && rentData.filter(filterRecords)}
        selectedOption={selectedOption || { label: "", value: 0, type: "" }}
        selectRecordType={selectRecordType}
      />
    );
  }, [rentData, selectedOption, selectRecordType, selectedFloor, selectedPrice]);

  return currentUser?.accessLevel && currentUser?.accessLevel < 10 ? <NoPermissionBox pageType="freeRentSearch" /> :
    <div className="flex flex-col w-full p-2">
      <div className="flex flex-col md:flex-row gap-2 p-2 w-full justify-start items-center flex-nowrap overflow-x-auto">
        <div className="flex flex-row gap-2 flex-nowrap w-full md:w-auto">
          <div className="w-[120px] flex flex-col gap-1 items-start">
            <Label className="text-xs text-gray-800">専有面積 </Label>
            <Select value={selectedPrice} onValueChange={(value) => {
              setSelectedPrice(value as string);
            }}>
              <SelectTrigger className="">
                <SelectValue placeholder="専有面積" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">全て</SelectItem>
                <SelectItem value="30">30㎡以上</SelectItem>
                <SelectItem value="50">50㎡以上</SelectItem>
                <SelectItem value="100">100㎡以上</SelectItem>
                <SelectItem value="200">200㎡以上</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="w-[120px] flex flex-col gap-1 items-start">
            <Label className="text-xs text-gray-800">階数</Label>
            <Select value={selectedFloor} onValueChange={(value) => {
              setSelectedFloor(value as string);
            }}>
              <SelectTrigger className="">
                <SelectValue placeholder="専有面積" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">全て</SelectItem>
                <SelectItem value="5">5階以上</SelectItem>
                <SelectItem value="10">10階以上</SelectItem>
                <SelectItem value="20">20階以上</SelectItem>
                <SelectItem value="30">30階以上</SelectItem>
                <SelectItem value="40">40階以上</SelectItem>
                <SelectItem value="50">50階以上</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="w-[120px] flex flex-col gap-1 items-start">
            <Label className="text-xs text-gray-800">間取り</Label>
            <Select value={selectedRoomLayout} onValueChange={(value) => {
              setSelectedRoomLayout(value as string);
            }}>
              <SelectTrigger className="">
                <SelectValue placeholder="専有面積" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">全て</SelectItem>
                <SelectItem value="1">1R/1K/1DK/1LDK以上</SelectItem>
                <SelectItem value="2">2K/2DK/2LDK以上</SelectItem>
                <SelectItem value="3">3K/3DK/3LDK以上</SelectItem>
                <SelectItem value="4">4K/4DK/4LDK以上</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Separator className="" />

      <div className="grid grid-cols-1 md:grid-cols-4 gap-2 pt-2 w-full">
        <div className={`flex flex-col gap-2 w-full ${rentData.length > 0 ? "md:col-span-3" : "md:col-span-4"}`}>
          <div className="flex">
            <div className="flex-1 flex flex-row gap-1 justify-start items-end">
              <div className="text-2xl">
                {rentData && rentData.filter(filterRecords).length.toLocaleString()}
              </div>

              <div className="text-sm text-gray-600">
                件該当賃貸物件
                {currentUser?.accessLevel && currentUser?.accessLevel < 10 && <span className="text-xs text-gray-500 ml-1">
                  (無料ユーザーは一部のみ表示)
                </span>}
              </div>
            </div>

            <div className="flex flex-row gap-2">
              <ExportCSVButton
                data={rentData && rentData.filter(filterRecords)}
                type="rentRecord"
              />
            </div>
          </div>

          <DataTable columns={columns} data={rentData && rentData.filter(filterRecords).sort((a: any, b: any) => new Date(a.updatedAt).getTime() > new Date(b.updatedAt).getTime() ? -1 : 1)} defaultPageSize={20} />
        </div>

        <div className="sm:col-span-1 flex flex-col gap-2">
          {rentData && rentData.filter(filterRecords).length > 0 && renderRentInsights}
        </div>
      </div>
    </div>
}