"use client"

import { Badge } from "@/components/ui/badge";
import { UserLambdaRecordProps, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { ExternalLink, Loader2 } from "lucide-react";
import SaleChart from "@/app/(cp)/an/mansion/[id]/SaleChart";
import dayjs from "dayjs";
import { getAllTimeAverageBuildingHouseRentPrice, getAllTimeAverageRentPrice, getAllTimeAverageSellingPrice } from "@/app/(cp)/an/mansion/utiliy/getAllTimeData";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";
import { getAverageRoiForType } from "@/lib/userLambdaRecord/summaryForType";
import { useState, useEffect } from "react";
import RentChart from "@/app/(cp)/an/(common)/RentChart";
import RentBuildingHouseChart from "@/app/(cp)/an/(common)/RentBuildingHouseChart";

export default function RentInsights({ selectRecordType, rentData, selectedOption }: { selectRecordType: string, rentData: any, selectedOption: { label: string, value: number, type: string } }) {
  const { currentUser } = useAuthStore();
  const [analysisResult, setAnalysisResult] = useState<any>(null);

  useEffect(() => {
    if (rentData && rentData.length > 0) {
      if (selectRecordType === UserLambdaRecordType.MANSION) {
        const analysisResult = getAllTimeAverageRentPrice(rentData);
        setAnalysisResult(analysisResult);
      } else if (selectRecordType === UserLambdaRecordType.BUILDING || selectRecordType === UserLambdaRecordType.HOUSE) {
        const analysisResult = getAllTimeAverageBuildingHouseRentPrice(rentData);
        setAnalysisResult(analysisResult);
      }
    }
  }, [rentData]);

  const mapperForSelectedOption = {
    "area": {
      name: "地域",
      linkPrefix: ""
    },
    "building": {
      name: "建物",
      linkPrefix: "/an/mansion",
    },
    "station": {
      name: "駅",
      linkPrefix: "/an/station",
    },
    "postalCode": {
      name: "地域",
      linkPrefix: "/an/area",
    }
  }

  const goDp = () => {
    createNewSystemUserActivityAction({
      data: {
        eventType: "BUTTON_CLICK",
        route: `/an/${mapperForSelectedOption[selectedOption?.type as keyof typeof mapperForSelectedOption]?.linkPrefix}/${selectedOption?.value}#${selectRecordType.toLowerCase()}`,
      },
    });

    sendLark({
      message: `[👀][${mapperForSelectedOption[selectedOption?.type as keyof typeof mapperForSelectedOption]?.name}詳細分析][${currentUser?.name}]${selectedOption?.label}:${selectedOption?.value}| [物件タイプ]${selectRecordType.toLowerCase()} from search results`,
      url: LARK_URLS.USER_ACTIVITY_CHANNEL,
    })
  }

  return rentData.length > 0 ? <div className="flex flex-col gap-2 bg-gray-50 border border-gray-200 rounded-lg">
    <div className="flex flex-row gap-2 items-center justify-between border-b border-gray-200 p-2 bg-gray-100">
      <div className="flex flex-col gap-1 items-start">
        <h1 className="text-lg font-bold">
          {selectedOption?.label}
        </h1>
        <div className="text-sm text-gray-500 flex flex-row gap-2">
          <Badge variant="outline">
            {mapperForSelectedOption[selectedOption?.type as keyof typeof mapperForSelectedOption]?.name}
          </Badge>
        </div>
      </div>

      {selectedOption.type !== "area" && <Link href={`${mapperForSelectedOption[selectedOption?.type as keyof typeof mapperForSelectedOption]?.linkPrefix}/${selectedOption?.value}#${selectRecordType.toLowerCase()}`} target="_blank" onClick={() => {
        goDp()
      }}>
        <Button variant="outline">
          <ExternalLink className="w-4 h-4" />
          {mapperForSelectedOption[selectedOption?.type as keyof typeof mapperForSelectedOption]?.name}詳細分析
        </Button>
      </Link>}
    </div>

    <div className="p-2">
      {/* <div className="text-base font-bold border-b border-gray-200 pb-2 mb-2">
        {selectedOption.type !== "building" ? "近隣" : ""}
        {mapper[selectRecordType as keyof typeof mapper].name}賃貸履歴
      </div> */}
      <div className="flex flex-row gap-2 p-2 border-gray-200 bg-white border rounded-lg">
        {rentData && rentData.length > 0 ? <div className="flex flex-col gap-2">
          <div className="flex flex-row items-center">
            <b className="mr-1">平均単価:</b>
            {analysisResult ? analysisResult.averageUnitPrice : 0}万円/坪

            <div className="text-sm text-gray-500 ml-2">
              {analysisResult ? (analysisResult.averageUnitPrice / 3.305785).toFixed(2) : 0}万円/平米
            </div>
          </div>
          <div>
            <b className="mr-1">平均面積:</b>
            {analysisResult ? analysisResult.averageUnitSize : 0}平米
          </div>
          <div>
            <b className="mr-1">平均賃料:</b>
            {analysisResult ? analysisResult.averagePrice : 0}万円
          </div>
        </div> : <div className="text-center text-gray-400 h-full flex items-center justify-center bg-neutral-100">
          データなし
        </div>}
      </div>

      <div className="flex flex-col gap-2 mt-2 p-2 border-gray-200 bg-white border rounded-lg">
        <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
          <h1 className="text-base">月別賃料推移</h1>
        </div>

        {selectRecordType === UserLambdaRecordType.BUILDING || selectRecordType === UserLambdaRecordType.HOUSE ? <RentBuildingHouseChart records={rentData?.sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt))) as any[]} unit="month" /> : <RentChart records={rentData?.sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt))) as any[]} unit="month" />}
      </div>
    </div>

  </div> : <div className="text-center text-gray-400 h-full flex items-center justify-center bg-neutral-100 border border-gray-200 rounded-lg flex flex-col gap-2">
    <div className="text-2xl">
      インサイト
    </div>

    <div className="text-sm text-gray-500">
      検索を開始すると表示されます
    </div>
  </div>
}