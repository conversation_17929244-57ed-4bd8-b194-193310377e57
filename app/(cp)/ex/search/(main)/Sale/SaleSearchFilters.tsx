"use client";

import { Select, SelectContent, SelectValue, SelectTrigger, SelectItem } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Info } from "lucide-react";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import dayjs from "dayjs";
import { getStatus } from "@/lib/userLambdaRecord/getStatus"

export default function SaleSearchFilters({ selectRecordType, userLambdaRecords, setFilteredUserLambdaRecords }: { selectRecordType: string, userLambdaRecords: UserLambdaRecordProps[], setFilteredUserLambdaRecords: (records: UserLambdaRecordProps[]) => void }) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [selectUpdatedTag, setSelectUpdatedTag] = useState(searchParams.get("updatedTag") || "12");
  const [selectBuildingSize, setSelectBuildingSize] = useState(searchParams.get("buildingSize") || "ALL");
  const [selectBuildingLevel, setSelectBuildingLevel] = useState(searchParams.get("buildingLevel") || "ALL");
  const [selectStationDistance, setSelectStationDistance] = useState(searchParams.get("stationDistance") || "ALL");
  const [selectLandRight, setSelectLandRight] = useState(searchParams.get("selectLandRight") || "ALL");
  const [selectLandSize, setSelectLandSize] = useState(searchParams.get("landSize") || "ALL");
  const [selectedPrice, setSelectedPrice] = useState(searchParams.get("selectedPrice") || "ALL");
  const [selectedYield, setSelectedYield] = useState(searchParams.get("selectedYield") || "ALL");
  const [selectBuildingYear, setSelectBuildingYear] = useState(searchParams.get("buildingYear") || "ALL");
  const [selectStatus, setSelectStatus] = useState(searchParams.get("selectStatus") || "ALL");

  const [filterPriceDown, setFilterPriceDown] = useState(false);
  const [filterNew, setFilterNew] = useState(false);

  const filterForNew = (record: UserLambdaRecordProps) => {
    if (dayjs().diff(dayjs(record.createdAt), "day") > 14) return false;

    return true;
  }

  const filterRecords = (record: UserLambdaRecordProps) => {
    if (filterPriceDown) {
      if (!filterForPriceDown(record)) return false;
    }

    if (filterNew) {
      if (!filterForNew(record)) return false;
    }

    if (selectStatus !== "ALL") {
      if (getStatus(record as UserLambdaRecordProps) !== selectStatus) return false;
    }

    if (selectedPrice !== "ALL") {
      if (record.price > parseFloat(selectedPrice)) return false;
    }

    if (selectLandRight !== "ALL") {
      if (record.landRight === "所有権" && (selectLandRight && selectLandRight !== "所有権")) return false;
      if (record.landRight === "借地権" && selectLandRight !== "借地権") return false;
    }

    if (selectLandSize !== "ALL") {
      if (record.landSize && record.landSize < parseFloat(selectLandSize)) return false;
    }

    if (selectBuildingSize !== "ALL" && selectRecordType !== "LAND") {
      if (record.buildingSize && record.buildingSize < parseFloat(selectBuildingSize)) return false;
    }

    if (selectBuildingLevel !== "ALL" && selectRecordType === "MANSION") {
      let level = record.buildingLevel?.replace("階", "") || record.recordValues?.unitLevel?.replace("階", "") || null;
      if (!level || parseInt(level) < parseInt(selectBuildingLevel)) return false;
    }

    if (selectBuildingYear !== "ALL" && selectRecordType !== "LAND") {
      if (!record.buildingBuiltYear || record.buildingBuiltYear < parseFloat(selectBuildingYear)) return false;
    }

    if (selectUpdatedTag !== "ALL") {
      const updatedAt = dayjs(record.updatedAt);
      const diff = dayjs().diff(updatedAt, "day");
      if (diff > parseFloat(selectUpdatedTag) * 30) return false;
    }

    if (selectedYield !== "ALL" && selectRecordType !== "LAND") {
      if (!record.yearlyIncome || record.yearlyIncome / record.price * 100 < parseFloat(selectedYield)) return false;
    }

    if (selectStationDistance !== "ALL") {
      if (!record.nearestStationWalkMinute || record.nearestStationWalkMinute > parseFloat(selectStationDistance)) return false;
    }

    return true;
  }

  useEffect(() => {
    if (userLambdaRecords.length > 0) {
      const filtered = userLambdaRecords.filter(filterRecords);
      setFilteredUserLambdaRecords(filtered);
    }
  }, [userLambdaRecords, filterPriceDown, filterNew, selectStatus, selectedPrice, selectLandRight, selectBuildingSize, selectBuildingYear, selectStationDistance, selectUpdatedTag, selectedYield, selectLandSize, selectBuildingLevel]);

  const filterForPriceDown = (record: UserLambdaRecordProps) => {
    if (!record.priceChanges || record.priceChanges.length < 2) return false;

    const priceChangeSorted = record.priceChanges.sort((a, b) => a.recordDate.getTime() - b.recordDate.getTime());

    if (priceChangeSorted[priceChangeSorted.length - 1].price === priceChangeSorted[priceChangeSorted.length - 2].price) return false;

    if (dayjs().diff(dayjs(priceChangeSorted[priceChangeSorted.length - 1].recordDate), "day") > 14) return false;

    return true;
  }

  const getCountForPriceDown = () => {
    return userLambdaRecords.filter(filterForPriceDown).length;
  }

  const getCountForNew = () => {
    return userLambdaRecords.filter(filterForNew).length;
  }

  return (
    <div className="flex flex-col md:flex-row gap-2 py-2 w-full justify-start items-center flex-nowrap overflow-x-auto">
      <div className="flex flex-row gap-2 flex-nowrap w-full md:w-auto">
        <div className="w-[120px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800">価格</Label>
          <Select value={selectedPrice} onValueChange={(value) => {
            setSelectedPrice(value as string);
            const newParams = new URLSearchParams(searchParams.toString())
            newParams.set("selectedPrice", value as string)
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false })
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="価格" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="5000">5000万以下</SelectItem>
              <SelectItem value="10000">1億以下</SelectItem>
              <SelectItem value="20000">2億以下</SelectItem>
              <SelectItem value="50000">5億以下</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {selectRecordType !== "LAND" && <div className="w-[100px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800">利回り</Label>
          <Select value={selectedYield} onValueChange={(value) => {
            setSelectedYield(value as string);
            const newParams = new URLSearchParams(searchParams.toString())
            newParams.set("selectedYield", value as string)
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false })
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="利回り" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="4">4%以上</SelectItem>
              <SelectItem value="5">5%以上</SelectItem>
              <SelectItem value="6">6%以上</SelectItem>
              <SelectItem value="7">7%以上</SelectItem>
              <SelectItem value="8">8%以上</SelectItem>
              <SelectItem value="9">9%以上</SelectItem>
              <SelectItem value="10">10%以上</SelectItem>
            </SelectContent>
          </Select>
        </div>}

        <div className="w-[140px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800 line-clamp-1">販売状況</Label>
          <Select value={selectStatus} onValueChange={(value) => {
            setSelectStatus(value as string);
            const newParams = new URLSearchParams(searchParams.toString())
            newParams.set("selectStatus", value as string)
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false })
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="ステータス" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="公開中">公開中のみ</SelectItem>
              <SelectItem value="成約">成約のみ</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {selectRecordType !== "MANSION" && <div className="w-[100px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800">土地権利</Label>
          <Select value={selectLandRight} onValueChange={(value) => {
            setSelectLandRight(value as string);

            const newParams = new URLSearchParams(searchParams.toString())
            newParams.set("selectLandRight", value as string)
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false })
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="土地権利" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="所有権">所有権</SelectItem>
              <SelectItem value="借地権">借地権</SelectItem>
            </SelectContent>
          </Select>
        </div>}

        {selectRecordType !== "MANSION" && <div className="w-[100px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800">土地面積</Label>
          <Select value={selectLandSize} onValueChange={(value) => {
            setSelectLandSize(value as string);

            const newParams = new URLSearchParams(searchParams.toString())
            newParams.set("selectLandSize", value as string)
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false })
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="土地面積" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="30">30平米以上</SelectItem>
              <SelectItem value="40">40平米以上</SelectItem>
              <SelectItem value="50">50平米以上</SelectItem>
              <SelectItem value="60">60平米以上</SelectItem>
              <SelectItem value="70">70平米以上</SelectItem>
              <SelectItem value="80">80平米以上</SelectItem>
              <SelectItem value="90">90平米以上</SelectItem>
              <SelectItem value="100">100平米以上</SelectItem>
              <SelectItem value="120">120平米以上</SelectItem>
              <SelectItem value="150">150平米以上</SelectItem>
              <SelectItem value="200">200平米以上</SelectItem>
            </SelectContent>
          </Select>
        </div>}

        {selectRecordType !== "LAND" && <div className="w-[120px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800">建築面積</Label>
          <Select value={selectBuildingSize} onValueChange={(value) => {
            setSelectBuildingSize(value as string);
            const newParams = new URLSearchParams(searchParams.toString());
            newParams.set("buildingSize", value as string);
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false });
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="建築面積" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="30">30平米以上</SelectItem>
              <SelectItem value="40">40平米以上</SelectItem>
              <SelectItem value="50">50平米以上</SelectItem>
              <SelectItem value="60">60平米以上</SelectItem>
              <SelectItem value="70">70平米以上</SelectItem>
              <SelectItem value="80">80平米以上</SelectItem>
              <SelectItem value="90">90平米以上</SelectItem>
              <SelectItem value="100">100平米以上</SelectItem>
              <SelectItem value="120">120平米以上</SelectItem>
              <SelectItem value="150">150平米以上</SelectItem>
              <SelectItem value="200">200平米以上</SelectItem>
            </SelectContent>
          </Select>
        </div>}

        {
          selectRecordType === "MANSION" && <div className="w-[120px] flex flex-col gap-1 items-start">
            <Label className="text-xs text-gray-800">建築階数</Label>
            <Select value={selectBuildingLevel} onValueChange={(value) => {
              setSelectBuildingLevel(value as string);
            }}>
              <SelectTrigger className="">
                <SelectValue placeholder="建築階数" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">全て</SelectItem>
                <SelectItem value="10">10階以上</SelectItem>
                <SelectItem value="20">20階以上</SelectItem>
                <SelectItem value="30">30階以上</SelectItem>
              </SelectContent>
            </Select>
          </div>
        }

        {selectRecordType !== "LAND" && <div className="w-[120px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800">建築年数</Label>
          <Select value={selectBuildingYear} onValueChange={(value) => {
            setSelectBuildingYear(value as string);
            const newParams = new URLSearchParams(searchParams.toString());
            newParams.set("buildingYear", value as string);
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false });
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="建築年数" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="1980">1980年以降</SelectItem>
              <SelectItem value="1981">1981年以降(新耐震)</SelectItem>
              <SelectItem value="1990">1990年以降</SelectItem>
              <SelectItem value="2000">2000年以降</SelectItem>
              <SelectItem value="2010">2010年以降</SelectItem>
              <SelectItem value="2020">2020年以降</SelectItem>
            </SelectContent>
          </Select>
        </div>}

        <div className="w-[120px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800">駅徒歩分数</Label>
          <Select value={selectStationDistance} onValueChange={(value) => {
            setSelectStationDistance(value as string);
            const newParams = new URLSearchParams(searchParams.toString());
            newParams.set("stationDistance", value as string);
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false });
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="駅徒歩" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="5">5分以内</SelectItem>
              <SelectItem value="10">10分以内</SelectItem>
              <SelectItem value="15">15分以内</SelectItem>
              <SelectItem value="20">20分以内</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="w-[120px] flex flex-col gap-1 items-start">
          <Label className="text-xs text-gray-800">更新日</Label>
          <Select value={selectUpdatedTag} onValueChange={(value) => {
            setSelectUpdatedTag(value as string);
            const newParams = new URLSearchParams(searchParams.toString())
            newParams.set("updatedTag", value as string)
            router.push(`/ex/search?${newParams.toString()}`, { scroll: false })
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="更新日" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">全て</SelectItem>
              <SelectItem value="0.1">3日内</SelectItem>
              <SelectItem value="0.25">1週間内</SelectItem>
              <SelectItem value="1">1ヶ月内</SelectItem>
              <SelectItem value="3">3ヶ月内</SelectItem>
              <SelectItem value="6">6ヶ月内</SelectItem>
              <SelectItem value="12">12ヶ月内</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex flex-row gap-2 flex-nowrap justify-start md:justify-end items-end w-full">
        <Badge variant={filterPriceDown ? "default" : "outline"} className="p-1.5 px-4 text-sm font-normal flex flex-row justify-center items-center gap-1" onClick={() => {
          setFilterPriceDown(!filterPriceDown);
        }}>
          <div className="flex flex-row gap-2 items-center">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="w-4 h-4" />
                </TooltipTrigger>
                <TooltipContent>
                  2週間以内に物件価格が値下がりした物件に表示しています。
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          値下げ

          <div className={getCountForPriceDown() > 0 ? "font-bold" : "text-gray-500"}>
            ({getCountForPriceDown()}
            件)
          </div>
        </Badge>

        <Badge variant={filterNew ? "default" : "outline"} className="p-1.5 px-4 text-sm font-normal flex flex-row justify-center items-center gap-1" onClick={() => {
          setFilterNew(!filterNew);
        }}>
          <div className="flex flex-row gap-2 items-center">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="w-4 h-4" />
                </TooltipTrigger>
                <TooltipContent>
                  2週間以内に新規物件に表示しています。
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          新規

          <div className={getCountForNew() > 0 ? "font-bold" : "text-gray-500"}>
            ({getCountForNew()}
            件)
          </div>
        </Badge>
      </div>
    </div>
  );
}