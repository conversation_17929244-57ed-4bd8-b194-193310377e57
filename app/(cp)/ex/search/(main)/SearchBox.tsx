"use client"

import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useAuthStore } from "@/store/auth";
import { useUserUsageStore } from "@/store/userUsage";
import { getUserLambdaRecords } from "@/actions/tllUserLambdaRecords";
import { sendLark } from "@/lib/thirdParty/lark";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import { useSearchHistoryStore } from "@/store/userSearchHistory";
import dynamic from "next/dynamic";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { getProMansionRent } from "@/actions/proMansionRent";
import { getProBuildingHouseRent } from "@/actions/proBuildingHouseRent";
import dayjs from "dayjs";
import { getSumitomoAuctionForSearchAction } from "@/actions/sumitomoAuction";

// 这里关闭 SearchBarCombinedUI 的 SSR
const SearchBarCombinedUI = dynamic(() => import("@/components/ui/SearchBarCombinedUI"), { ssr: false });

export default function SearchBox({ setIsLoadingData, setUserLambdaRecords, setSumitomoAuctions, selectedOption, setSelectedOption, selectRecordType, setSelectRecordType, setRentData }: { setIsLoadingData: (isLoadingData: boolean) => void, setUserLambdaRecords: (userLambdaRecords: any) => void, setSumitomoAuctions: (sumitomoAuctions: any) => void, selectedOption: any, setSelectedOption: (selectedOption: any) => void, selectRecordType: string, setSelectRecordType: (selectRecordType: string) => void, setRentData: (rentData: any) => void }) {
  const currentUser = useAuthStore((state) => state.currentUser);
  const searchParams = useSearchParams();
  const router = useRouter();
  type RecordType = "BUILDING" | "HOUSE" | "LAND" | "MANSION"; // 根据你的业务扩展

  const { addHistory } = useSearchHistoryStore();

  const searchForUserLambdaRecord = async () => {
    setIsLoadingData(true); // 设置正在加载状态

    getUserLambdaRecords({
      ...(selectedOption?.type === "area" && { areaCode: selectedOption?.value ? selectedOption?.value : undefined }),
      ...(selectedOption?.type === "station" && { nearestStationGroupId: selectedOption?.value ? selectedOption?.value : undefined }),
      ...(selectedOption?.type === "building" && { buildingId: selectedOption?.value ? selectedOption?.value : undefined }),
      ...(selectedOption?.type === "postalCode" && { postalCode: selectedOption?.value ? selectedOption?.value : undefined }),
      recordType: selectRecordType
    }).then((res) => {

      setIsLoadingData(false); // 设置正在加载状态

      if (res.success) {
        setUserLambdaRecords(res.data);

        const queryObj: { [key: string]: string } = {};
        searchParams.forEach((value, key) => {
          queryObj[key] = value;
        });

        addHistory({
          queryParams: queryObj,
          timestamp: Date.now(),
        });
      } else {
        console.error('Failed to fetch user lambda record:', res.message);
      }
    });

    // const sumitomoAuctionPromise = Promise.resolve({
    //   success: true,
    //   data: []
    // } as any);

    getSumitomoAuctionForSearchAction({
      ...(selectedOption?.type === "station" && { nearestStation: selectedOption?.value ? selectedOption?.value : undefined }),
      ...(selectedOption?.type === "postalCode" && { postalCode: selectedOption?.value ? selectedOption?.value : undefined }),
      ...(selectedOption?.type === "building" && { buildingId: selectedOption?.value ? selectedOption?.value : undefined }),
      recordType: selectRecordType,
    }).then((res) => {
      if (res.success) {
        setSumitomoAuctions(res.data);
      } else {
        console.error('Failed to fetch sumitomo auction:', res.message);
      }
    });
  }

  const searchForRentRecords = async () => {
    if (selectRecordType === UserLambdaRecordType.LAND) {
      return;
    }

    if (!selectedOption?.value) {
      return;
    }

    if (selectedOption?.type === "postalCode") {
      if (selectRecordType === UserLambdaRecordType.MANSION) {
        let res = await getProMansionRent({
          postalCode: selectedOption?.value ? parseInt(selectedOption?.value.toString()) : undefined,
          recordType: selectRecordType
        });
        setRentData(res.data);
      } else {
        let res = await getProBuildingHouseRent({
          postalCode: selectedOption?.value ? parseInt(selectedOption?.value.toString()) : undefined,
          recordType: selectRecordType
        });
        setRentData(res.data);
      }
    }

    if (selectedOption?.type === "building") {
      let res = await getProMansionRent({
        buildingId: selectedOption?.value ? selectedOption?.value : undefined,
        recordType: selectRecordType
      });
      setRentData(res.data);
    }

    if (selectedOption?.type === "station") {
      if (selectRecordType === UserLambdaRecordType.MANSION) {
        const res = await getProMansionRent({
          nearestStationGroupId: selectedOption?.value,
          recordType: selectRecordType
        });

        setRentData(res.data);
      } else {
        const res = await getProBuildingHouseRent({
          nearestStationGroupId: selectedOption?.value,
          recordType: selectRecordType
        });

        setRentData(res.data);
      }
    }
  }

  useEffect(() => {
    if (selectedOption?.value) {
      searchForUserLambdaRecord();
      searchForRentRecords();

      createNewSystemUserActivityAction({
        data: {
          eventType: "BUTTON_CLICK",
          route: "/ex/search",
          eventMetadata: {
            buttonName: "searchButton",
            searchType: selectedOption?.type,
            selectedOption: selectedOption,
            selectRecordType: selectRecordType,
          },
        },
      });

      sendLark({
        message: `[🔍][物件検索][${selectedOption?.type}][by ${currentUser?.email}][${currentUser?.accessLevel}] label: ${selectedOption?.label} | value: ${selectedOption?.value}, RecordType: ${selectRecordType}`,
        url: LARK_URLS.USER_ACTIVITY_CHANNEL,
      });
    }
  }, [selectedOption, selectRecordType]);

  return (
    <div className="bg-neutral-100 rounded-lg p-1 py-2 m-2">
      <div className="p-1 flex flex-row justify-start items-center">
        <Tabs value={selectRecordType} onValueChange={(value) => {
          const newParams = new URLSearchParams(searchParams.toString())
          newParams.set("selectRecordType", value)
          router.push(`/ex/search?${newParams.toString()}`, { scroll: false })

          setSelectRecordType(value as RecordType);
          setUserLambdaRecords([]);
          setSumitomoAuctions([]);
          setRentData([]);
        }}>
          <TabsList>
            {["MANSION", "BUILDING", "HOUSE", "LAND"].map((recordType) => <TabsTrigger key={recordType} value={recordType}>
              <div className="flex flex-row gap-2 items-center">
                {mapper[recordType as keyof typeof mapper].iconSmall}
                {mapper[recordType as keyof typeof mapper].name}
              </div>
            </TabsTrigger>)}
          </TabsList>
        </Tabs>
      </div>

      <div className="flex flex-col px-2 py-1 w-full">
        <SearchBarCombinedUI selectedOption={selectedOption} setSelectedOption={setSelectedOption} recordType={selectRecordType} />
      </div>
    </div>
  );
}