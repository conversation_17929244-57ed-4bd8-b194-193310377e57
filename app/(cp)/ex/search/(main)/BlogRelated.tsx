"use client"

import { getBlogs } from "@/actions/blog";
import { BlogProps } from "@/lib/definitions";
import { useEffect, useState } from "react";
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import BlogCard from "@/app/blog/BlogCard";
import { useLocale } from "next-intl";

export default function BlogRelated({ selectedOption }: { selectedOption: { label: string, value: number, type: string, valueId?: string } }) {
  const [blogs, setBlogs] = useState<BlogProps[]>([]);
  const [blogsSanity, setBlogsSanity] = useState<any[]>([]);
  const currentLocale = useLocale();

  const fetchBlogs = async () => {
    let filter = {
      ...(selectedOption.type === "building" ? { buildingId: selectedOption.value.toString() } : {}),
      ...(selectedOption.type === "postalCode" ? { postalCodeId: selectedOption.valueId } : {}),
      ...(selectedOption.type === "station" ? { stationGroupId: selectedOption.value.toString() } : {}),
      ...(selectedOption.type === "area" ? { areaCode: parseInt(selectedOption.value.toString()) } : {}),
    }

    console.log("🔥 filter", filter);

    if (Object.keys(filter).length > 0) {
      const blogs = await getBlogs(filter);
      setBlogs(blogs.data || []);

      const blogIds = blogs.data.map((blog: BlogProps) => blog.id);
      console.log("🔥 blogIds", blogIds);

      const posts = await sanityClient.fetch(
        `*[_type == "post" && _id in $ids] {
          _id, title_${currentLocale}, slug_${currentLocale}, publishedAt, body_${currentLocale}, image,
          category->{_id, name_${currentLocale}},
          "author": coalesce(author->name, "Urbalytics Team")
        }`,
        { ids: blogIds }
      );

      setBlogsSanity(posts || []);
    }
  }

  useEffect(() => {
    if (selectedOption.type && selectedOption.value) {
      fetchBlogs();
    }
  }, [selectedOption]);

  // postalcode shold be fixed

  return <div className="flex flex-col gap-2 p-4 w-full">
    <div className="flex flex-col gap-2">
      <div className="">
        <div className="text-lg font-bold">関連記事</div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
        {blogsSanity.map((blog: any) => (
          <BlogCard post={blog} postLocalDb={blogs.find((blogLocalDb: BlogProps) => blogLocalDb.id === blog._id)} currentLocale={currentLocale} />
        ))}
      </div>
    </div>
  </div>;
}