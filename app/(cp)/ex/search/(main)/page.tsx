"use client";

import { useState, useEffect, startTransition, useMemo } from "react"; // 导入useState和useEffect
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { UserLambdaRecordProps, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useAuthStore } from "@/store/auth";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowDown, ArrowDownUp, Info, LockIcon, Loader2 } from "lucide-react";
import Link from "next/link";
import dayjs from "dayjs";
import { SumifuColumns } from "@/app/(cp)/ad/keibaiSumifu/sumitomoKeibaiColumns";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabsRadix";
import { useSearchParams } from "next/navigation";
import { useSearchHistoryStore } from "@/store/userSearchHistory";
import { getDiff, getDiffAmount } from "@/lib/userLambdaRecord/valueRanking";
import { useRouter } from "next/navigation";
import SaleInsights from "./Sale/SaleInsights";
import PerRecomSearchHistory from "@/components/ui/recomShovelers/PerRecomSearchHistory";
import HighReturnRecom from "@/components/ui/recomShovelers/HighReturnRecom";
import PriceDownRecom from "@/components/ui/recomShovelers/PriceDownRecom";
import OtherTypesRecom from "@/components/ui/recomShovelers/OtherTypesRecom";
import SearchBox from "./SearchBox";
import SaleSearchFilters from "./Sale/SaleSearchFilters";
import { ExportCSVButton } from "@/components/ExportCsvButton";
import { orderBy } from "lodash-es";
import { Skeleton } from "@/components/ui/skeleton";
import LeafletMapSimple from "@/components/LeafletMapSimple";
import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import RentPage from "./Rent/RentPage";
import { ToastAction } from "@/components/ui/toast";
import { toast } from "@/hooks/use-toast";
import { createCustomerNeedForSelfFromSearchAction } from "@/actions/customerNeeds";
import { TllCustomerNeedCriteriaType } from "@/lib/definitions";
import BlogRelated from "./BlogRelated";

export default function Search() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { history } = useSearchHistoryStore();
  type RecordType = "BUILDING" | "HOUSE" | "LAND" | "MANSION"; // 根据你的业务扩展
  const rawParamRecordType = searchParams.get("selectRecordType");
  const rawSearchValue = searchParams.get("searchValue");
  const isValidRecordType = (val: string | null): val is RecordType =>
    val === "BUILDING" || val === "HOUSE" || val === "LAND" || val === "MANSION";
  const [selectRecordType, setSelectRecordType] = useState<RecordType>(
    isValidRecordType(rawParamRecordType) ? rawParamRecordType : "MANSION"
  ); // 新增状态来存储第二个选择框的值

  const [userLambdaRecords, setUserLambdaRecords] = useState([] as any[]);
  const [filteredUserLambdaRecords, setFilteredUserLambdaRecords] = useState([] as any[]);
  const [sumitomoAuctions, setSumitomoAuctions] = useState([] as any[]);
  const [isLoadingData, setIsLoadingData] = useState(rawSearchValue ? true : false); // 新增状态
  const [selectedResultTab, setSelectedResultTab] = useState(searchParams.get("selectedResultTab") || "sale");
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const [sortBy, setSortBy] = useState(searchParams.get("sortBy") || "decreasingCreatedAt");
  const [rentData, setRentData] = useState([] as any[]);

  const { currentUser } = useAuthStore();
  const [selectedOption, setSelectedOption] = useState<{
    label: string;
    value: number;
    type: string;
  } | null>(null); // 新增状态来存储选中的车站  

  useEffect(() => {
    if (currentUser) {
      setColumns(getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps, recordType: selectRecordType }));
    }
  }, [currentUser, selectRecordType]);

  useEffect(() => {
    if (filteredUserLambdaRecords.length > 0) {
      document.title = `Urbalytics | ${filteredUserLambdaRecords.length}件該当物件`;
    }
  }, [filteredUserLambdaRecords]);

  useEffect(() => {
    if (userLambdaRecords.length > 0) {
      setTimeout(() => {
        toast({
          title: `本検索条件(${selectedOption?.label}(${mapper[selectRecordType as keyof typeof mapper].nameFull})をお気に入りに追加しますか？`,
          description: "物件の更新情報をプッシュ通知で受け取ることができます",
          duration: 1000 * 60,
          action: <ToastAction altText="On" className="" onClick={async () => {
            const response = await createCustomerNeedForSelfFromSearchAction({
              title: `${selectedOption?.label} - ${mapper[selectRecordType as keyof typeof mapper].nameFull}`,
              recordType: selectRecordType as UserLambdaRecordType,
              criteriaType: selectedOption?.type === "station" ? TllCustomerNeedCriteriaType.STATION_GROUP_ID : selectedOption?.type === "postalCode" ? TllCustomerNeedCriteriaType.POSTAL_CODE : selectedOption?.type === "area" ? TllCustomerNeedCriteriaType.AREA_CODE : TllCustomerNeedCriteriaType.BUILDING_ID,
              value: selectedOption?.value.toString() as string,
            });

            if (response.success) {
              sendLark({
                message: `[🔍][物件検索からのお気に入り][by ${currentUser?.email}]add to favorite: ${selectedOption?.label} - ${mapper[selectRecordType as keyof typeof mapper].nameFull}`,
                url: LARK_URLS.USER_ACTIVITY_CHANNEL,
              });

              toast({
                title: "物件をお気に入りに追加しました",
              });
            }
          }}>追加する</ToastAction>
        })
      }, 1000 * 10) // 30秒后触发
    }
  }, [userLambdaRecords]);

  function getSortedLambdaRecords(
    records: UserLambdaRecordProps[],
    sortBy: string
  ): UserLambdaRecordProps[] {
    switch (sortBy) {
      case "increasingPrice":
        return orderBy(records, ["price"], ["desc"]);

      case "decreasingPrice":
        return orderBy(records, ["price"], ["asc"]);

      case "decreasingCreatedAt":
        return orderBy(records, [(r) => dayjs(r.updatedAt).toDate()], ["desc"]);

      case "decreasingStarRating":
        return orderBy(
          records,
          [(r) => (getDiff(r, r.recordType) === 0 ? 999999 : getDiff(r, r.recordType))],
          ["asc"]
        );

      case "decreasingStarRatingAmount":
        return orderBy(
          records,
          [(r) => (getDiffAmount(r, r.recordType) === 0 ? 999999 : getDiffAmount(r, r.recordType))],
          ["asc"]
        );

      default:
        return records;
    }
  }

  const sortedRecords = useMemo(() => {
    return getSortedLambdaRecords(filteredUserLambdaRecords, sortBy);
  }, [filteredUserLambdaRecords, sortBy]);

  const sortedSumitomoAuctions = useMemo(() => {
    return getSortedLambdaRecords(sumitomoAuctions, sortBy);
  }, [sumitomoAuctions, sortBy]);

  const renderInsights = useMemo(() => {
    return (
      <SaleInsights
        userLambdaRecords={filteredUserLambdaRecords}
        selectedOption={selectedOption || { label: "", value: 0, type: "" }}
        selectRecordType={selectRecordType}
        isLoadingData={isLoadingData}
      />
    );
  }, [filteredUserLambdaRecords, selectedOption, selectRecordType, isLoadingData]);

  const findCenter = useMemo(() => {
    return filteredUserLambdaRecords.find((record) => record.longitude && record.latitude)
  }, [filteredUserLambdaRecords]);

  const isValidCoordinate = (val: any): val is number =>
    typeof val === "number" && !isNaN(val);
  const isValidCenter = findCenter && isValidCoordinate(findCenter.latitude) && isValidCoordinate(findCenter.longitude);

  return <div className="flex flex-col w-full">
    <SearchBox setIsLoadingData={setIsLoadingData} setUserLambdaRecords={setUserLambdaRecords} setSumitomoAuctions={setSumitomoAuctions} selectedOption={selectedOption} setSelectedOption={setSelectedOption} selectRecordType={selectRecordType} setSelectRecordType={(value) => setSelectRecordType(value as RecordType)} setRentData={setRentData} />

    <Tabs defaultValue={selectedResultTab} className="w-full" onValueChange={(value) => {
      setSelectedResultTab(value)

      const newParams = new URLSearchParams(searchParams.toString())
      newParams.set("selectedResultTab", value as string)
      router.push(`/ex/search?${newParams.toString()}`, { scroll: false })

      if (value === "rent") {
        // createNewSystemUserActivityAction({
        //   data: {
        //     eventType: "BUTTON_CLICK",
        //     route: "/ex/rentSearch",
        //     eventMetadata: {
        //       buttonName: "searchButton",
        //       searchType: "buildingName",
        //       selectedBuilding: selectedBuilding,
        //       selectRecordType: selectRecordType,
        //     },
        //   },
        // });

        sendLark({
          message: `[🔍][物件検索-賃料][by ${currentUser?.email}]search type is rent (clicking on rent tab), RecordType: ${selectRecordType}`,
          url: LARK_URLS.USER_ACTIVITY_CHANNEL,
        });
      }

    }}>
      <TabsList>
        <TabsTrigger value="sale">
          売買物件
          {isLoadingData ? <Loader2 className="w-10 h-4 animate-spin" /> :
            userLambdaRecords.length > 0 && <span className="text-xs text-gray-500 ml-1">
              {userLambdaRecords.length.toLocaleString()}件
            </span>}
        </TabsTrigger>
        <TabsTrigger value="rent">
          賃貸物件

          {isLoadingData ? <Loader2 className="w-10 h-4 animate-spin" /> : rentData.length > 0 && <span className="text-xs text-gray-500 ml-1 flex flex-row gap-1">
            <div>
              {rentData.length.toLocaleString()}件
            </div>

            {currentUser?.accessLevel && currentUser?.accessLevel < 10 ? <LockIcon className="w-4 h-4" /> : ""
            }
          </span>}</TabsTrigger>
      </TabsList>
    </Tabs>

    {selectedResultTab === "rent" && <RentPage rentData={rentData} currentUser={currentUser as TllUserProps} selectRecordType={selectRecordType as UserLambdaRecordType} selectedOption={selectedOption || { label: "", value: 0, type: "" }} />}

    {selectedResultTab === "sale" && <div className="p-2">
      <SaleSearchFilters selectRecordType={selectRecordType} userLambdaRecords={userLambdaRecords} setFilteredUserLambdaRecords={setFilteredUserLambdaRecords} />

      <Separator className="" />

      <div className="grid grid-cols-1 md:grid-cols-4 gap-2 pt-2 w-full">
        <div className={`flex flex-col gap-2 w-full ${userLambdaRecords.length > 0 ? "md:col-span-3" : "md:col-span-4"}`}>
          <div className="flex flex-col sm:flex-row gap-2 text-sm">
            <div className="flex-1 flex flex-row gap-2 justify-start items-end">
              <div className="flex flex-row gap-1 justify-start items-end">
                <div className="text-2xl">
                  {filteredUserLambdaRecords.length.toLocaleString()}
                </div>

                <div className="text-sm text-gray-600">
                  件該当売買物件
                  {/* {currentUser?.accessLevel && currentUser?.accessLevel < 10 && <span className="text-xs text-gray-500 ml-1">
                    (無料ユーザーは一部のみ表示)
                  </span>} */}
                </div>
              </div>
            </div>

            <div className="flex flex-row gap-2">
              <div className="flex flex-row gap-2 items-center">
                <ArrowDownUp className="w-4 h-4" />

                <Select value={sortBy} onValueChange={(value) => {
                  setSortBy(value as string);

                  const newParams = new URLSearchParams(searchParams.toString())
                  newParams.set("sortBy", value as string)
                  router.push(`/ex/search?${newParams.toString()}`, { scroll: false })
                }}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="並び替え" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="increasingPrice">価格が高い順</SelectItem>
                    <SelectItem value="decreasingPrice">価格が低い順</SelectItem>
                    <SelectItem value="decreasingCreatedAt">更新日が新しい順</SelectItem>
                    <SelectItem value="decreasingStarRating">割安度が高い順</SelectItem>
                    <SelectItem value="decreasingStarRatingAmount">割安額が高い順</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <ExportCSVButton
                data={filteredUserLambdaRecords}
                type="userLambdaRecord"
              />
            </div>
          </div>

          {isLoadingData ? (
            <div className="space-y-2">
              {[...Array(10)].map((_, idx) => (
                <div key={idx} className="flex gap-4">
                  <Skeleton className="h-20 w-[100px]" />
                  <Skeleton className="h-4 w-[80px]" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ))}
            </div>
          ) : (
            <DataTable columns={columns} data={sortedRecords} isLoading={false} defaultPageSize={50} />
          )}

          <Separator className="" />

          {filteredUserLambdaRecords.length > 0 && <div className="text-sm text-gray-500 text-center p-2 pb-4">
            マーチングされる物件が見つからない場合、
            <Link href="/ex/valuation" className="text-blue-500 underline">
              物件査定
            </Link>
            をご利用ください
          </div>}
        </div>

        <div className="sm:col-span-1 flex flex-col gap-2">
          {/* {isValidCenter && (
            <div className="hidden sm:block flex flex-col gap-2 border border-gray-200 rounded-md">
              <LeafletMapSimple
                height="200px"
                zoom={14}
                data={filteredUserLambdaRecords.map((record) => ({
                  name: record.compositeName,
                  longitude: record.longitude as number,
                  latitude: record.latitude as number,
                  coordinate: {
                    lat: record.latitude as number,
                    lng: record.longitude as number,
                  },
                  popUpRenderFunction: () => {
                    return <Link href={`/ex/search/${record.id}`} target="_blank" className="text-sm text-gray-500 flex flex-row justify-start items-center gap-1">
                      <div className="text-gray-500">
                        {mapper[record.recordType as keyof typeof mapper].iconBig}
                      </div>
                      <div className="flex flex-col gap-1">
                        <div className="text-base text-gray-800">
                          {record.address}
                        </div>
                        <div className="text-xs text-gray-500 flex flex-row gap-1">
                          <strong>{record.price}万円</strong>
                          |
                          土地 {record.landSize}m2
                          |
                          建物 {record.buildingSize}m2  </div>
                      </div>
                    </Link>
                  }
                }))}
              />
            </div>
          )} */}

          {userLambdaRecords.length > 0 && renderInsights}
        </div>
      </div>


      {
        currentUser?.accessLevel && currentUser?.accessLevel >= 90 && (
          <div className="flex flex-col gap-2 w-full">
            <div className="text-sm flex flex-row gap-1 items-end justify-start">
              <div className="text-2xl">
                {sumitomoAuctions.length}
              </div>
              <div className="text-sm text-gray-600">
                件該当売買物件(住友競売)
              </div>
            </div>
            <DataTable columns={SumifuColumns as ColumnDef<any>[]} data={sortedSumitomoAuctions} isLoading={isLoadingData} defaultPageSize={50} />
          </div>
        )
      }
    </div>}


    {selectedOption && <>
      <Separator className="mt-8" />
      <BlogRelated selectedOption={selectedOption || { label: "", value: 0, type: "" }} />
    </>}

    {userLambdaRecords.length > 0 && (
      <>
        <Separator className="mt-8" />
        <div className="flex flex-col gap-8 w-full p-4">
          {selectRecordType && selectedOption && <OtherTypesRecom recordType={selectRecordType as UserLambdaRecordType} option={selectedOption} currentUserLambdaRecord={userLambdaRecords[0]} />}
          <PerRecomSearchHistory />
          <HighReturnRecom />
          <PriceDownRecom />
        </div>
      </>
    )}
  </div >
}
