import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { Info } from "lucide-react";
import { renderStarFromDiff } from "@/lib/userLambdaRecord/valueRanking";

export function renderPropertyInfoStatsBox(record: UserLambdaRecordProps, type: any) {
  if (!record) return null;

  const showPriceDiff = (mapper: any) => {
    let diff = (mapper.comparator - mapper.value) / mapper.value * 100;

    return <span className={`ml-1 font-bold ${mapper.unit === "%"
      && diff < 0 && 'text-green-400'} ${mapper.unit === "%" && diff > 0 && 'text-red-400'}
      ${mapper.unit !== "%" && diff > 0 && 'text-green-400'} ${mapper.unit !== "%" && diff < 0 && 'text-red-400'}`}>
      {diff < 0 ? diff.toFixed(1) : `+${diff.toFixed(1)}`}%
    </span>
  };

  const mapper = {
    isshuUnitAverage: {
      title: "一種坪単価",
      value: (record.price / (record.landSize || 1) * 3.3).toFixed(0),
      comparator: (record.propertyAnalysisResult?.nearbyAvgIssuePrice || 0) * 3.3,
      unit: "万円/坪",
      average: ((record.propertyAnalysisResult?.nearbyAvgIssuePrice || 0) * 3.3).toFixed(0),
      extra: <>
        {((record.propertyAnalysisResult?.nearbyMinIssuePrice || 0) * 3.3).toFixed(0)}~{((record.propertyAnalysisResult?.nearbyMaxIssuePrice || 0) * 3.3).toFixed(0)}
        | 80P: {((record.propertyAnalysisResult?.nearby80PIssuePrice || 0) * 3.3).toFixed(0)}
      </>,
    },
    roiAverage: {
      title: "収益性",
      value: record?.yearlyIncome ? ((record?.yearlyIncome || 0) / (record?.price || 1) * 100).toFixed(2) : null,
      unit: "%",
      comparator: record.propertyAnalysisResult?.nearbyAvgCap,
      average: (record.propertyAnalysisResult?.nearbyAvgCap || 0).toFixed(2),
      extra: <>
        {record.propertyAnalysisResult?.nearbyMinCap}%~{record.propertyAnalysisResult?.nearbyMaxCap}% | 80P: {record.propertyAnalysisResult?.nearby80PCap}%
      </>,
    },
    // rowAverageLR: {
    //   title: "ROI平均評価(LR)",
    //   value: (record?.propertyAnalysisResult?.roiValuation?.lrPrice)?.toFixed(0) || null,
    //   unit: "万円",
    //   comparator: record.propertyAnalysisResult?.nearbyAvgCap,
    //   extra: <>
    //     ROI:{' '}
    //     {record.propertyAnalysisResult
    //       ? parseFloat(
    //         (record.yearlyIncome ?? 0 / (record?.propertyAnalysisResult?.roiValuation?.lrPrice ?? 0) * 100).toFixed(2)
    //       ) + '%'
    //       : '-'}
    //   </>,
    // },
    "gfaAverage": {
      title: "GFA坪単価",
      unit: "万円/坪",
      unitMultipler: 3.3,
      value: (record?.price / (record.buildingSize || record.recordValues.unitArea || 1) * 3.3).toFixed(0) || null,
      comparator: (record.propertyAnalysisResult?.nearbyAvgGfa || 0) * 3.3,
      average: ((record.propertyAnalysisResult?.nearbyAvgGfa || 0) * 3.3).toFixed(0),
      extra: <div>
        {((record.propertyAnalysisResult?.nearbyMinGfa || 0) * 3.3).toFixed(0)}~{((record.propertyAnalysisResult?.nearbyMaxGfa || 0) * 3.3).toFixed(0)} | 80P: {((record.propertyAnalysisResult?.nearby80PGfa || 0) * 3.3).toFixed(0)}
      </div>,
    },
    upside: {
      title: "賃料アップサイド",
      unit: "円/坪",
      value: !record?.yearlyIncome ? null : ((record?.yearlyIncome * 3.3 || 0) / 12 / (record.buildingSize || record.recordValues.unitArea) * 10000)?.toFixed(0) || null,
      comparator: (record.propertyAnalysisResult?.nearbyAvgRent || 0) * 3.3,
      average: ((record.propertyAnalysisResult?.nearbyAvgRent || 0) * 3.3).toFixed(0),
      extra: <div>
        {((record.propertyAnalysisResult?.nearbyMinRent || 0) * 3.3).toFixed(0)} ~ {((record.propertyAnalysisResult?.nearbyMaxRent || 0) * 3.3).toFixed(0)} | 80P: {((record.propertyAnalysisResult?.nearby80PRent || 0) * 3.3).toFixed(0)}
      </div>,
    },
    sekisanAverage: {
      title: "積算価格割合",
      unit: "%",
      comparator: record.propertyAnalysisResult?.nearbyAvgSekisanPercentage,
      average: (record.propertyAnalysisResult?.nearbyAvgSekisanPercentage || 0).toFixed(0),
      tooltip: "借地権を加味",
      value: record.propertyAnalysisResult?.sekisanValuation?.sekisanTotalPercentage?.toFixed(0) || null,
      extra: <>
        {record.propertyAnalysisResult?.nearbyMinSekisanPercentage?.toFixed(0)}~{record.propertyAnalysisResult?.nearbyMaxSekisanPercentage?.toFixed(0)}% | 80P: {record.propertyAnalysisResult?.nearby80PSekisanPercentage?.toFixed(0)}
        | 土地:{record.propertyAnalysisResult?.sekisanValuation?.sekisanLandPercentage}%
      </>,
    },
    // "gfaAverageSameBuilding": {
    //   title: "GFA平均価格(同棟)",
    //   value: record?.propertyAnalysisResult?.gfaValuation?.avgGfaPriceSameBuilding,
    //   extra: <div>
    //     {(
    //       record.price /
    //       (record.buildingSize !== undefined
    //         ? record.buildingSize
    //         : record.recordValues.unitArea)
    //     ).toFixed(0)}
    //     vs 平均
    //     {(
    //       record?.propertyAnalysisResult?.gfaValuation?.avgGfaPriceSameBuilding /
    //       (record.buildingSize !== undefined
    //         ? record.buildingSize
    //         : record.recordValues.unitArea)
    //     ).toFixed(0)}
    //     万円/m2
    //   </div>,
    // },
  } as Record<string, { title: any; value: any; extra: any; tooltip?: string; unit?: string; comparator?: number, average?: string }>;

  return mapper[type as keyof typeof mapper] ? (
    <div className="bg-white p-4 rounded shadow-sm" >
      <div className="text-sm text-neutral-900 flex items-center">
        <div>
          {mapper[type as keyof typeof mapper]?.title}
          {renderStarFromDiff(mapper[type as keyof typeof mapper])}
        </div>

        {record.landRight !== "所有権" && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="w-4 h-4 mx-2 opacity-50 cursor-default" />
            </TooltipTrigger>
            <TooltipContent>所有権の場合、本物件は{record.landRight}のため、権利の影響を加味した価格です。</TooltipContent>
          </Tooltip>
        )}
      </div>

      <div className="w-full flex items-center flex-row justify-start">
        <span className="text-lg font-bold">
          {mapper[type as keyof typeof mapper]?.value || "-"}
          {mapper[type as keyof typeof mapper]?.unit && <span>{mapper[type as keyof typeof mapper]?.unit || ""}</span>}
        </span>
      </div>

      <div className="text-xs text-gray-500 flex flex-col justify-start items-start">
        <div className="flex flex-row justify-start items-center">
          {mapper[type as keyof typeof mapper]?.average && <span className="text-xs text-gray-500">平均: {mapper[type as keyof typeof mapper]?.average}{mapper[type as keyof typeof mapper]?.unit || ""}</span>}
          {mapper[type as keyof typeof mapper]?.comparator && showPriceDiff(mapper[type as keyof typeof mapper])}
        </div>

        {mapper[type as keyof typeof mapper]?.extra}
      </div>
    </div >
  ) : null;
}