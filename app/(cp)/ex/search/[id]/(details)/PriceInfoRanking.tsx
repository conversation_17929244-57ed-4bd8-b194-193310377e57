import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { renderPropertyInfoStatsBox } from "./utility/renderPropertyInfoStatsBox";
import { Star, StarIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { calculateAvgPrice, renderStarRating, calculateMinPrice, calculateMaxPrice, calculateP80Price, getDiff, renderStarRatingClass } from "@/lib/userLambdaRecord/valueRanking";
import { raw } from "@prisma/client/runtime/library";
import { PriceInfoRankingSimpleProgressBar } from "./PriceInfoRankingSimpleProgressBar";
import { PropertyAnalysisResultsProps } from "@/lib/definitions/propertyAnalysisResults";
import { coef } from "@/app/api/cron/constants";
import { Separator } from "@/components/ui/separator";

export const renderDiffText = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  const comparator = calculateAvgPrice(currentUserLambdaRecord, type);
  const diff = (currentUserLambdaRecord?.price - comparator) / comparator * 100;
  const perc = parseFloat((comparator / currentUserLambdaRecord?.price).toFixed(2));

  if (!comparator) {
    return <div className="">

    </div>
  }

  return <div className="flex flex-row items-center justify-center w-full">
    {/* {currentUserLambdaRecord?.landRight !== "所有権" && "(土地権利を加味後)"}査定価格の
    <strong>{comparator.toFixed(0)}万円</strong>
    は、約物件価格の
    <strong className={perc < 1 ? "text-red-300" : "text-green-300"}>{perc.toFixed(2)}倍
    </strong>
    です。 */}
    物件の割安度は
    {renderStarRating(currentUserLambdaRecord, type)}
    です、割安額は
    <strong className={renderStarRatingClass(diff)}>{Math.abs(currentUserLambdaRecord?.price - comparator).toFixed(0)}万円
      {diff > 0 ? "(割高)" : ""}
    </strong>
    です。
  </div>
}

const boxForType = {
  BUILDING: {
    name: "一棟収益物件",
    nameMultiplier: 1,
    unit: "表面利回り(ROI)",
    items: [
      "roiAverage",
      "upside",
      "gfaAverage",
      "isshuUnitAverage",
      // "sekisanAverage",
    ],
    unitSymbol: "%",
    comparator: "high",
    maxFieldName: "nearbyMaxCap",
    minFieldName: "nearbyMinCap",
    avgFieldName: "nearbyAvgCap",
    p80FieldName: "nearby80PCap",
  },
  MANSION: {
    name: "区分マンション",
    nameMultiplier: 3.3,
    unit: "延床面積坪単価",
    items: [
      // "gfaAverageSameBuilding",
      // "gfaLowSameBuilding",
      "gfaAverage",
    ],
    unitSymbol: "万円",
    comparator: "high",
    maxFieldName: "nearbyMaxGfa",
    minFieldName: "nearbyMinGfa",
    avgFieldName: "nearbyAvgGfa",
    p80FieldName: "nearby80PGfa",
  },
  HOUSE: {
    name: "一戸建て",
    nameMultiplier: 3.3,
    unit: "延床面積坪単価",
    items: [
      "gfaAverage",
      "isshuUnitAverage",
      "sekisanAverage",
    ],
    unitSymbol: "万円",
    comparator: "low",
    maxFieldName: "nearbyMaxGfa",
    minFieldName: "nearbyMinGfa",
    avgFieldName: "nearbyAvgGfa",
    p80FieldName: "nearby80PGfa",
  },
  LAND: {
    name: "土地",
    nameMultiplier: 3.3,
    unit: "一種坪単価",
    items: [
      "isshuUnitAverage",
      "sekisanAverage",
    ],
    unitSymbol: "万円",
    comparator: "low",
    maxFieldName: "nearbyMaxIssuePrice",
    minFieldName: "nearbyMinIssuePrice",
    avgFieldName: "nearbyAvgIssuePrice",
    p80FieldName: "nearby80PIssuePrice",
  },
} as Record<string, { name: string, unit: string, items: string[], unitSymbol: string, comparator: string, maxFieldName: string, minFieldName: string, avgFieldName: string, p80FieldName: string, nameMultiplier: number }>;

export const renderPriceInfo = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  if (!currentUserLambdaRecord || !type) {
    return <div>
      -
    </div>
  }

  let selectedRawInfo = boxForType[type as keyof typeof boxForType];
  const comparator = selectedRawInfo?.comparator === "high" ? "以下" : "以上";

  if (!selectedRawInfo) {
    return <div>
      -
    </div>
  }

  return (
    <div className="flex flex-col gap-1">
      <PriceInfoRankingSimpleProgressBar
        currentPrice={currentUserLambdaRecord?.price || 0}
        minPrice={calculateMinPrice(currentUserLambdaRecord, type)}
        maxPrice={calculateMaxPrice(currentUserLambdaRecord, type)}
        avgPrice={calculateAvgPrice(currentUserLambdaRecord, type)}
        p80Price={calculateP80Price(currentUserLambdaRecord, type)}
      />

      <div className="text-sm text-gray-500 flex flex-row gap-2 text-center justify-center">
        <div>
          近隣物件の{selectedRawInfo?.unit}の正常範囲は
          <strong>{(currentUserLambdaRecord.propertyAnalysisResult?.[selectedRawInfo?.minFieldName as keyof PropertyAnalysisResultsProps] * selectedRawInfo.nameMultiplier || 0).toFixed(1)}~{(currentUserLambdaRecord.propertyAnalysisResult?.[selectedRawInfo?.maxFieldName as keyof PropertyAnalysisResultsProps] * selectedRawInfo.nameMultiplier || 0).toFixed(1)}{selectedRawInfo.unitSymbol}</strong>
        </div>
      </div>

      <div className="text-sm text-gray-500 flex flex-row gap-2 text-center justify-center">
        <div>
          平均は<strong>{(currentUserLambdaRecord.propertyAnalysisResult?.[selectedRawInfo.avgFieldName as keyof PropertyAnalysisResultsProps] * selectedRawInfo.nameMultiplier || 0).toFixed(1)}{selectedRawInfo.unitSymbol}</strong>
        </div>

        <div>|</div>

        <div>
          8割の物件は<strong>{(currentUserLambdaRecord.propertyAnalysisResult?.[selectedRawInfo.p80FieldName as keyof PropertyAnalysisResultsProps] * selectedRawInfo.nameMultiplier || 0).toFixed(1)}{selectedRawInfo.unitSymbol}</strong>{comparator}
        </div>
      </div>
    </div>
  );
};

export default function PriceInfoRanking({ currentUserLambdaRecord, currentUser }: { currentUserLambdaRecord: UserLambdaRecordProps, currentUser: TllUserProps }) {

  const [selectedRankingMethod, setSelectedRankingMethod] = useState("all");

  useEffect(() => {
    setSelectedRankingMethod(currentUserLambdaRecord?.recordType || "BUILDING");
  }, [currentUserLambdaRecord]);

  return (
    <>
      <div className="flex flex-col gap-2 bg-neutral-50 border border-neutral-200 rounded-md m-2">
        <div className="font-bold text-neutral-500 border-b border-neutral-200 p-2">
          割安度評価
        </div>

        {currentUserLambdaRecord?.recordType !== "MANSION" && currentUserLambdaRecord?.recordType !== "LAND" &&
          <Tabs value={selectedRankingMethod} onValueChange={setSelectedRankingMethod} className="px-2 w-full justify-start flex flex-nowrap overflow-x-auto">
            <TabsList>
              {["BUILDING"].includes(currentUserLambdaRecord?.recordType) ? <TabsTrigger value="BUILDING" disabled={!currentUserLambdaRecord?.yearlyIncome} className={currentUserLambdaRecord?.recordType === "BUILDING" ? "font-bold" : ""}>
                収益物件として
                <Badge variant="outline" className="ml-2">
                  {renderStarRating(currentUserLambdaRecord, "BUILDING")}
                </Badge>
              </TabsTrigger> : ""}
              {["HOUSE", "BUILDING"].includes(currentUserLambdaRecord?.recordType || "") && <TabsTrigger value="HOUSE" className={currentUserLambdaRecord?.recordType === "HOUSE" ? "font-bold" : ""}>
                一戸建てとして
                <Badge variant="outline" className="ml-2">
                  {renderStarRating(currentUserLambdaRecord, "HOUSE")}
                </Badge>
              </TabsTrigger>}
              {["HOUSE", "BUILDING"].includes(currentUserLambdaRecord?.recordType || "") && <TabsTrigger value="LAND">
                土地として
                <Badge variant="outline" className="ml-2">
                  {renderStarRating(currentUserLambdaRecord, "LAND")}
                </Badge>
              </TabsTrigger>}
            </TabsList>
          </Tabs>}

        <div className={`text-lg font-bold  text-center py-2 w-full flex justify-center bg-neutral-100 ${getDiff(currentUserLambdaRecord, selectedRankingMethod) >= 15 && "text-red-600 bg-red-200"} ${getDiff(currentUserLambdaRecord, selectedRankingMethod) <= -15 && "text-green-600 bg-green-200"}`}>
          <div className={`flex items-center`}>
            <strong>割安度:</strong>
            <div className="ml-2">
              {renderStarRating(currentUserLambdaRecord, selectedRankingMethod)}
            </div>
          </div>
        </div>

        <div className="w-full text-center">
          <div className="text-sm mb-2">
            本物件({boxForType[selectedRankingMethod as keyof typeof boxForType]?.name})の場合、Urbalytics上の評価参考単位は
            <strong>{boxForType[selectedRankingMethod as keyof typeof boxForType]?.unit}</strong>
          </div>

          <div className="w-full text-center flex flex-col gap-1 text-sm text-gray-500 pb-2">
            {renderPriceInfo(currentUserLambdaRecord, selectedRankingMethod)}

            {renderDiffText(currentUserLambdaRecord, selectedRankingMethod)}
          </div>

          <div className="text-xs text-gray-500 mt-2 flex flex-col gap-1 border-t border-neutral-200 pt-4 text-left p-2">
            <div>
              * 割安度は、対象物件の価格が周辺の平均価格からどの程度乖離しているかを基準に算出しています。
            </div>
          </div>
        </div>
      </div>


      {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && <div className="flex flex-col gap-2 bg-neutral-100 border border-neutral-600 rounded-md m-2">
        <div className="font-bold text-neutral-500 border-b mb-2 p-2">[TLL管理者用] 査定情報</div>

        <div className=" flex flex-row justify-center mb-2">
          <div className="flex flex-row gap-2">
            ラッキング: {Array.from({ length: currentUserLambdaRecord?.propertyAnalysisResult?.overallStarLevel || 0 }).map((_, index) => (
              <StarIcon key={index} className="fill-yellow-500 text-yellow-500" />
            ))}
          </div>
        </div>

        <Separator />

        <div className="border-neutral-200">
          <div className={`p-2 grid grid-cols-${boxForType[selectedRankingMethod as keyof typeof boxForType]?.items?.length == 3 ? 3 : 2} 2xl:grid-cols-${boxForType[selectedRankingMethod as keyof typeof boxForType]?.items?.length || 4} gap-2`}>
            {selectedRankingMethod && boxForType[selectedRankingMethod as keyof typeof boxForType] && boxForType[selectedRankingMethod as keyof typeof boxForType].items.map((t: any) => (
              <div key={t}>
                {renderPropertyInfoStatsBox(currentUserLambdaRecord as UserLambdaRecordProps, t)}
              </div>
            ))}
          </div>
        </div>
      </div>}
    </>
  );
}