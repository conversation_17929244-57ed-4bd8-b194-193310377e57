"use client"

import * as React from "react"
import { TrendingUp } from "lucide-react"
import { Label, <PERSON>, <PERSON>C<PERSON>, Cell, Legend } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord"
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { coef } from "@/app/api/cron/constants";


const chartConfig = {
  totalIncomeGainNetCostAndInterest: {
    label: "収入純利益",
    color: "hsl(var(--chart-1))",
  },
  totalCapitalGain: {
    label: "資本純利益",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig

function prepChartData(currentUserLambdaRecord: UserLambdaRecordProps | null) {
  const results = currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation || {};
  const chartData = Object.entries(results).filter(([key, value]) => key === 'totalCapitalGain' || key === 'totalIncomeGainNetCostAndInterest').map(([key, value]) => ({
    item: key,
    value: parseInt(value as string), // 随机化访客数
    fill: `var(--color-${key})`, // 假设有对应的颜色变量
  }));
  return chartData;
}


export default function PriceCfCalculationFormDonutChart() {
  const { currentUserLambdaRecord } = useUserLambdaRecordStore();

  const chartData = prepChartData(currentUserLambdaRecord);

  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>物件価格: {currentUserLambdaRecord?.price} 万円 vs 想定入札: {(parseFloat(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice || "0") * coef(currentUserLambdaRecord as UserLambdaRecordProps)).toFixed(0)} 万円
          {coef(currentUserLambdaRecord) !== 1 && `(coef: ${coef(currentUserLambdaRecord)})`}
        </CardTitle>
        <CardDescription>
          ABOUT <span className={`text-sm font-bold my-2 ${currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice * 100 / (currentUserLambdaRecord?.price || 1) >= 90 ? 'text-green-500' : 'text-red-500'}`}>
            {(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice * 100 / (currentUserLambdaRecord?.price || 1))?.toFixed(2)}%
          </span> OF PRICE
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={chartData}
              dataKey="value"
              nameKey="item"
              innerRadius={60}
              strokeWidth={5}
              // cx="50%"
              // cy="50%"
              fill="#8884d8"
              label
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-3xl font-bold"
                        >
                          {parseInt(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.targetNetProfit || "0")}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-muted-foreground"
                        >
                          目標純利益(万円)
                        </tspan>
                      </text>
                    )
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>

      <CardFooter className="flex-col gap-2 text-sm text-left flex-wrap justify-start items-start max-w-[80%] mx-auto">
        <div className="leading-none">
          <b>販売想定利益</b>: {parseInt(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.totalCapitalGain || "0")}  = 売却ネット価格 {parseInt(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.targetSellingPriceNetCost || "0")} - 初期総費用 {parseInt(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.initialCost)} 万円
        </div>
        <div className="leading-none">
          <b>賃料想定利益</b>: {parseInt(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.totalIncomeGainNetCostAndInterest || "0")} = 収入純利益 {parseInt(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.totalIncomeGain || "0")} - 利息 - 費用
        </div>
        <div className="font-bold">
          <b>ROC</b>: {(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.roc)?.toFixed(2)}%
        </div>
      </CardFooter>
    </Card>
  )
}
