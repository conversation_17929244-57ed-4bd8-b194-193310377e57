"use client";

import { Separator } from "@/components/ui/separator";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { ProProject } from "@/lib/definitions/proProject";
import { getProjectsByPostalCodeAction } from "@/actions/proProject";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { DataTable } from "@/components/ui/data-table";
import { columns } from "@/app/(cp)/it/insight/devplan/ProjectTableColumns";
import LeafletMapSimple from "@/components/LeafletMapSimple";
import dayjs from "dayjs";
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import MapFilter from "@/app/(cp)/it/insight/devplan/MapFilter";

export default function NearbyProjects({ currentUserLambdaRecord }: { currentUserLambdaRecord?: UserLambdaRecordProps | null }) {
  const { nearbyProjects, setNearbyProjects } = useUserLambdaRecordStore();
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [areaFilter, setAreaFilter] = useState<string>("all");
  const [usageFilter, setUsageFilter] = useState<string>("all");
  const [yearFilter, setYearFilter] = useState<string>("all");


  const filterProjects = (project: ProProject) => {
    if (yearFilter !== "all" && project.projectEndDateYear < Number(yearFilter)) {
      return false
    }

    if (usageFilter !== "all" && project.projectUsage && !project.projectUsage.includes(usageFilter.toLowerCase())) {
      return false
    }

    if (areaFilter !== "all" && project.constructionArea && project.constructionArea < Number(areaFilter)) {
      return false
    }

    if (typeFilter !== "all" && project.type !== typeFilter) {
      return false
    }

    return true
  }

  useEffect(() => {
    if (currentUserLambdaRecord && currentUserLambdaRecord?.postalCode) {
      getProjectsByPostalCodeAction({ postalCode: currentUserLambdaRecord?.postalCode as number }).then((res) => {
        if (res.success) {
          setNearbyProjects(res.data);
        }
      });
    }
  }, [currentUserLambdaRecord]);

  return <section id="nearbyProjects" className="bg-white">
    <div className="flex flex-col items-start sm:items-start justify-start sm:justify-between">
      <div className="text-lg font-bold text-neutral-900 flex-1 p-2">
        近隣開発計画
      </div>

      <Separator className="" />

      <div className="p-2 grid grid-cols-1 sm:grid-cols-2 gap-2 w-full">
        <div className="p-2 col-span-1 w-full">
          <div className="flex-1 flex flex-row gap-2 justify-start items-end mb-2">
            <div className="flex flex-row gap-1 justify-start items-end">
              <div className="text-2xl">
                {nearbyProjects.filter(filterProjects).length.toLocaleString()}
              </div>

              <div className="text-sm text-gray-600">
                件該当プロジェクト
              </div>
            </div>
          </div>

          <DataTable columns={columns} data={nearbyProjects.filter(filterProjects)} defaultPageSize={10} />
        </div>

        <div className="bg-neutral-100 p-2 col-span-1 relative min-h-[600px]">
          <div className="absolute top-0 right-0 flex flex-row gap-2 pt-2 z-10 justify-end items-center flex-wrap">
            <MapFilter
              typeFilter={typeFilter}
              setTypeFilter={setTypeFilter}
              areaFilter={areaFilter}
              setAreaFilter={setAreaFilter}
              usageFilter={usageFilter}
              setUsageFilter={setUsageFilter}
              yearFilter={yearFilter}
              setYearFilter={setYearFilter}
            />
          </div>

          <LeafletMapSimple
            selectedPropertyCoordinates={currentUserLambdaRecord && currentUserLambdaRecord?.longitude && currentUserLambdaRecord?.latitude ? {
              lat: currentUserLambdaRecord?.latitude as number,
              lng: currentUserLambdaRecord?.longitude as number,
            } : undefined}
            height="100%"
            zoom={14}
            data={nearbyProjects
              .filter((record: any) => record.longitude > 0 && record.latitude > 0)
              .filter(filterProjects)
              .map((record: any) => ({
                name: record.compositeTitle,
                latitude: record.latitude as number,
                longitude: record.longitude as number,
                coordinate: {
                  lat: record.latitude as number,
                  lng: record.longitude as number,
                },
                popUpRenderFunction: () => {
                  return <div className="flex flex-col gap-1">
                    <div className="text-base text-gray-800">
                      {record.name}
                    </div>
                    <div className="text-xs text-gray-500 flex flex-col gap-1">
                      <div>
                        施工面積: {record.constructionArea}m2
                      </div>
                      <div>
                        用途: {record.projectUsage}
                      </div>
                      <div>
                        竣工予定日: {dayjs(record.projectEndDate).format("YYYY/MM/DD")}
                      </div>
                      {/* <strong>{record.price}万円</strong>
                          |
                          土地 {record.landSize}m2
                          |
                          建物 {record.buildingSize}m2  </div> */}
                    </div>
                  </div>
                }
              }))}
          />
        </div>
      </div>


      {/* <span className="sm:float-right text-sm text-neutral-500">{nearbyMaxMin80Percentile?.comparatorType}:{nearbyMaxMin80Percentile?.min}~{nearbyMaxMin80Percentile?.max}(平均:{nearbyMaxMin80Percentile?.avg} vs 80%:{boldText(nearbyMaxMin80Percentile?.eightyPercentile)})
          {currentUserLambdaRecord?.recordType === 'BUILDING' && <>
            | 郵便{currentUserLambdaRecord?.postalCode}: 平均 {boldText(((postalCodeData?.buildingRecordRoi || 0) * 100).toFixed(2) + "%") || "-"}</>}
        </span>

        <ExportCSVButton
          data={nearbyRecords}
          type="userLambdaRecord"
        /> */}
    </div>


  </section>
}