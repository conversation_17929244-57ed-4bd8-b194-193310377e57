
"use client";

import dayjs from "dayjs";
import { DataTable } from "@/components/ui/data-table";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { rentUpsideColumns } from "./RentUpsideColumns";
import { getProBuildingHouseRent } from "@/actions/proBuildingHouseRent";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { buildingRentColumns, houseRentColumns, mansionRentColumns } from "@/app/(cp)/an/mansion/[id]/mansionRentColumns";
import { getMansionHouseAverageRent } from "@/lib/userLambdaRecord/recordAnalysisHelpers/getMansionHouseAverageRent";
import { getProMansionRent } from "@/actions/proMansionRent";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { useUserLambdaRecordRentStore } from "@/store/userLambdaRecordRent";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import NoPermissionBox from "@/app/(cp)/my/usage/NoPermissionBox";
import { useAuthStore } from "@/store/auth";
import { Separator } from "@radix-ui/react-separator";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function RentUpside({ currentUserLambdaRecord, nearbyRecords }: { currentUserLambdaRecord: UserLambdaRecordProps, nearbyRecords?: UserLambdaRecordProps[] }) {
  let nearbyRecordsForThisRecord = nearbyRecords || useUserLambdaRecordStore.getState().nearbyRecords;

  const { nearbySold, setNearbySold, nearbyRent, setNearbyRent, nearbyRentPart, setNearbyRentPart } = useUserLambdaRecordRentStore();
  const [nearbyRentRecords, setNearbyRentRecords] = useState<ProBuildingHouseRentProps[]>([]);
  const [nearbyRentPartRecords, setNearbyRentPartRecords] = useState<ProMansionRentProps[]>([]);
  const [selectedBuiltYearTag, setSelectedBuiltYearTag] = useState<string>("all");

  const [tab, setTab] = useState("nearbySold");
  const [buildingHouseAverageRent, setBuildingHouseAverageRent] = useState<number>(0);
  const [builingPartMansionAverageRent, setBuilingPartMansionAverageRent] = useState<number>(0);
  const [selectedBuiltYearFromTag, setSelectedBuiltYearFromTag] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(false);
  const { currentUser } = useAuthStore();

  const getDiff = (value: number, comparator: number) => {
    if (comparator > 0) {
      let diff = (value - comparator) / comparator * 100;
      return diff;
    }

    return 0;
  }

  const renderDiff = ({ type, avg, buildingSize }: { type: "nearbySold" | "nearbyRent" | "nearbyRentPart", avg: number, buildingSize: number }) => {
    let v = type === "nearbySold" ? nearbySold : type === "nearbyRent" ? nearbyRent : nearbyRentPart;

    if (v != 0) {
      return <div className={v > 0 ? "text-green-500" : "text-red-500"}>
        {v > 0 ? "+" : "-"}{Math.abs(v).toFixed(1)}%
      </div>
    }

    let estimated = avg * buildingSize / 3.3 / 10000;

    if (estimated > 0) {
      return <div className={`text-neutral-600 flex flex-row gap-2 justify-center items-center`}>
        <Badge variant="outline">想定</Badge>
        {estimated.toFixed(0)}万円/月
      </div>
    }

    return <div className={`text-neutral-400`}>
      データ取得中
    </div>
  }

  const fetchRentWholeRecords = async () => {
    setIsLoading(true);
    const nearbyRecords = await getProBuildingHouseRent({
      postalCode: currentUserLambdaRecord?.postalCode,
      nearestStationGroupId: currentUserLambdaRecord?.nearestStationGroupId,
      recordTypes: ["BUILDING", "HOUSE"],
      recordType: currentUserLambdaRecord?.recordType as string, // note this wont be used as i will get both types
    });
    setNearbyRentRecords(nearbyRecords.data);
    if (nearbyRecords.data.length > 0) {
      const buildingHouseAverageRent = getMansionHouseAverageRent({
        records: nearbyRecords.data,
      });
      setNearbyRent(getDiff(buildingHouseAverageRent, (currentUserLambdaRecord?.yearlyIncome || 0) * 10000 / 12 / (currentUserLambdaRecord?.buildingSize || 1) * 3.3));
      setBuildingHouseAverageRent(buildingHouseAverageRent);
    }
    setIsLoading(false);
  }

  const fetchBuilingPartMansionAverageRent = async () => {
    setIsLoading(true);
    let nearbyRecords = {} as any;

    if (currentUserLambdaRecord?.recordType === "MANSION" && currentUserLambdaRecord?.buildingId) {
      nearbyRecords = await getProMansionRent({
        buildingId: currentUserLambdaRecord?.buildingId,
        recordType: "MANSION" as string,
      });
    } else {
      nearbyRecords = await getProMansionRent({
        postalCode: currentUserLambdaRecord?.postalCode,
        nearestStationGroupId: currentUserLambdaRecord?.nearestStationGroupId,
        recordType: "BUILDING_PART" as string,
      });
    }

    setNearbyRentPartRecords(nearbyRecords.data);
    if (nearbyRecords.data.length > 0) {
      const builingPartMansionAverageRent = getMansionHouseAverageRent({
        records: nearbyRecords.data,
      });

      setNearbyRentPart(getDiff(builingPartMansionAverageRent, (currentUserLambdaRecord?.yearlyIncome || 0) * 10000 / 12 / (currentUserLambdaRecord?.buildingSize || 1) * 3.3));
      setBuilingPartMansionAverageRent(builingPartMansionAverageRent);
    }

    setIsLoading(false);
  }

  useEffect(() => {
    if (currentUserLambdaRecord && currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgRent) {
      setNearbySold(getDiff((currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgRent || 0) * 3.3, (currentUserLambdaRecord?.yearlyIncome || 0) * 10000 / 12 / (currentUserLambdaRecord?.buildingSize || 1) * 3.3));
    }

    if (currentUserLambdaRecord?.recordType === "BUILDING" || currentUserLambdaRecord?.recordType === "HOUSE") {
      fetchRentWholeRecords();
    }

    if (currentUserLambdaRecord?.recordType === "HOUSE") {
      setTab("nearbyRent");
    }

    if (currentUserLambdaRecord?.recordType === "MANSION") {
      setTab("nearbyRentPart");
    }

    fetchBuilingPartMansionAverageRent();
  }, [currentUserLambdaRecord]);

  const filterRecords = (record: any) => {
    if (!record.yearlyIncome || !record.buildingSize || record.buildingSize <= 1) {
      return false;
    }

    if (selectedBuiltYearTag !== "all") {
      if (record.buildingBuiltYear && dayjs().year() - record.buildingBuiltYear >= parseInt(selectedBuiltYearTag)) {
        return false;
      }
    }

    if (selectedBuiltYearFromTag !== "all") {
      if (record.buildingBuiltYear && dayjs().year() - record.buildingBuiltYear < parseInt(selectedBuiltYearFromTag)) {
        return false;
      }
    }

    return true;
  }

  const filterForPart = (record: any) => {
    if (selectedBuiltYearTag !== "all") {
      if (record.buildingBuiltYear && dayjs().year() - record.buildingBuiltYear >= parseInt(selectedBuiltYearTag)) {
        return false;
      }
    }

    if (selectedBuiltYearFromTag !== "all") {
      if (record.buildingBuiltYear && dayjs().year() - record.buildingBuiltYear < parseInt(selectedBuiltYearFromTag)) {
        return false;
      }
    }

    return true;
  }

  const getAverageRent = (records: any[]) => {
    return records.reduce((acc, record: any) => acc + ((Number(record.feeRent) || 0) + (Number(record.feeManagement) / 10000 || 0) + (Number(record.feeUtility) / 10000 || 0)), 0) / records.length;
  }

  const getAverageSize = (records: any[]) => {
    return records.reduce((acc, record: any) => acc + (record.buildingSize || record.unitSize), 0) / records.length;
  }

  return <section id="upsideAnalysis" className="bg-white">
    {currentUser?.accessLevel && currentUser?.accessLevel >= 10 ? <>
      <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2 flex flex-row items-center justify-between">
        <div className="flex flex-row items-center gap-2">
          賃料アップサイド
        </div>

        <Link href={`/ex/rent`} target="_blank">
          <Button variant="outline" size="sm" className="text-sm">
            より詳しい賃料査定へ
          </Button>
        </Link>
      </div>

      <div className={`p-2 grid grid-cols-${currentUserLambdaRecord?.recordType === "BUILDING" || currentUserLambdaRecord?.recordType === "HOUSE" ? 3 : 2} gap-2`} id="nearbySold">
        <div className={`border border-neutral-200 rounded-md p-2 text-center ${tab === "nearbySold" ? "bg-neutral-100 border-neutral-300" : ""}`} onClick={() => setTab("nearbySold")}>
          <div className="text-sm text-neutral-600">
            近隣売買物件に参考
          </div>

          <div className="text-2xl sm:text-4xl font-bold my-2 text-green-500">
            {renderDiff({ type: "nearbySold", avg: (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgRent || 0) * 3.3, buildingSize: currentUserLambdaRecord?.buildingSize || 0 })}
          </div>
          <div className="text-sm text-neutral-900">
            平均坪賃料{((currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgRent || 0) * 3.3 / 10000).toFixed(2)}万円 vs 本物件{((currentUserLambdaRecord?.yearlyIncome || 0) / 12 / (currentUserLambdaRecord?.buildingSize || 1) * 3.3).toFixed(2)}万円
          </div>
        </div>

        {(currentUserLambdaRecord?.recordType === "BUILDING" || currentUserLambdaRecord?.recordType === "HOUSE") && <div className={`border border-neutral-200 rounded-md p-2 text-center ${tab === "nearbyRent" ? "bg-neutral-100 border-neutral-300" : ""}`} onClick={() => setTab("nearbyRent")}>
          <div className="text-sm text-neutral-600">
            近隣賃貸成約事例(一棟)に参考
          </div>
          <div className="text-2xl sm:text-4xl font-bold my-2 text-red-500">
            {renderDiff({ type: "nearbyRent", avg: buildingHouseAverageRent, buildingSize: currentUserLambdaRecord?.buildingSize || 0 })}
          </div>
          <div className="text-sm text-neutral-900">
            平均坪賃料{(buildingHouseAverageRent / 10000).toFixed(2)}万円 vs 本物件{((currentUserLambdaRecord?.yearlyIncome || 0) / 12 / (currentUserLambdaRecord?.buildingSize || 1) * 3.3).toFixed(2)}万円
          </div>
        </div>}

        <div className={`border border-neutral-200 rounded-md p-2 text-center ${tab === "nearbyRentPart" ? "bg-neutral-100 border-neutral-300" : ""}`} onClick={() => setTab("nearbyRentPart")}>
          <div className="text-sm text-neutral-600">
            近隣賃貸成約事例(区分)に参考
          </div>
          <div className="text-2xl sm:text-4xl font-bold my-2 text-green-500">
            {renderDiff({ type: "nearbyRentPart", avg: builingPartMansionAverageRent, buildingSize: currentUserLambdaRecord?.buildingSize || 0 })}
          </div>
          <div className="text-sm text-neutral-900">
            平均坪賃料{(builingPartMansionAverageRent / 10000).toFixed(2)}万円 vs 本物件{((currentUserLambdaRecord?.yearlyIncome || 0) / 12 / (currentUserLambdaRecord?.buildingSize || 1) * 3.3).toFixed(2)}万円
          </div>
        </div>
      </div>

      <Separator className="my-2" />

      <div className="p-2 flex flex-col gap-2" id="nearbyRent">
        <div className="text-sm flex flex-row items-center gap-2">
          <Select value={selectedBuiltYearFromTag} onValueChange={(value) => {
            setSelectedBuiltYearFromTag(value);
          }}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="築年数" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">築年数: ALL</SelectItem>
              <SelectItem value="10">10年以上</SelectItem>
              <SelectItem value="20">20年以上</SelectItem>
              <SelectItem value="30">30年以上</SelectItem>
              <SelectItem value="40">40年以上</SelectItem>
              <SelectItem value="50">50年以上</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedBuiltYearTag} onValueChange={(value) => {
            setSelectedBuiltYearTag(value);
          }}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="築年数" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">築年数: ALL</SelectItem>
              <SelectItem value="10">10年以内</SelectItem>
              <SelectItem value="20">20年以内</SelectItem>
              <SelectItem value="30">30年以内</SelectItem>
              <SelectItem value="40">40年以内</SelectItem>
              <SelectItem value="50">50年以内</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {tab === "nearbySold" &&
          <div className="flex flex-col gap-2">
            <span className="text-sm text-neutral-500">
              合計: {nearbyRecordsForThisRecord.filter((record) => record.yearlyIncome && record.yearlyIncome > 0 && record.buildingSize && record.buildingSize > 1).length}件
            </span>

            <DataTable columns={rentUpsideColumns} data={nearbyRecordsForThisRecord.filter((r: any) => filterRecords(r)).sort((a, b) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} defaultPageSize={10} isLoading={isLoading} />
          </div>
        }

        {tab === "nearbyRent" &&
          <>
            <span className="text-sm text-neutral-500">
              合計: {nearbyRentRecords.filter((record) => record.buildingSize && record.buildingSize > 1).length}件
            </span>

            {nearbyRentRecords.filter((record) => record.buildingSize === currentUserLambdaRecord?.buildingSize).length > 0 && <div className="bg-neutral-200 p-2" >
              <span className="text-sm text-neutral-500">本物件かしれない</span>
              <DataTable columns={
                currentUserLambdaRecord?.recordType === UserLambdaRecordType.BUILDING ? buildingRentColumns : currentUserLambdaRecord?.recordType === UserLambdaRecordType.HOUSE ? houseRentColumns : mansionRentColumns
              } data={nearbyRentRecords.filter((record) => record.buildingSize === currentUserLambdaRecord?.buildingSize)} defaultPageSize={10} showFooter={false} isLoading={isLoading} />
            </div>}

            <DataTable columns={
              currentUserLambdaRecord?.recordType === UserLambdaRecordType.BUILDING ? buildingRentColumns : currentUserLambdaRecord?.recordType === UserLambdaRecordType.HOUSE ? houseRentColumns : mansionRentColumns
            } data={nearbyRentRecords.filter((record) => record.buildingSize && record.buildingSize > 1).filter((record) => filterForPart(record)).sort((a, b) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} defaultPageSize={10} isLoading={isLoading} />
          </>}

        {tab === "nearbyRentPart" &&
          <>
            <span className="text-sm text-neutral-500">
              合計: {nearbyRentPartRecords.filter((record: any) => (record.buildingSize || record.unitSize) && (record.buildingSize || record.unitSize) > 1).filter((record: any) => filterForPart(record)).length}件 |  平均価格: {getAverageRent(nearbyRentPartRecords.filter((record: any) => (record.buildingSize || record.unitSize) && (record.buildingSize || record.unitSize) > 1).filter((record: any) => filterForPart(record))).toFixed(0)}万円 | 平均賃貸面積: {getAverageSize(nearbyRentPartRecords.filter((record: any) => (record.buildingSize || record.unitSize) && (record.buildingSize || record.unitSize) > 1).filter((record: any) => filterForPart(record))).toFixed(0)}平米 |  平均賃料: {(getAverageRent(nearbyRentPartRecords.filter((record: any) => (record.buildingSize || record.unitSize) && (record.buildingSize || record.unitSize) > 1).filter((record: any) => filterForPart(record))) / getAverageSize(nearbyRentPartRecords.filter((record: any) => (record.buildingSize || record.unitSize) && (record.buildingSize || record.unitSize) > 1).filter((record: any) => filterForPart(record)))).toFixed(2)}万円/平米
            </span>

            <DataTable columns={mansionRentColumns} data={nearbyRentPartRecords.filter((record: any) => (record.buildingSize || record.unitSize) && (record.buildingSize || record.unitSize) > 1).filter((record: any) => filterForPart(record)).sort((a: any, b: any) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} defaultPageSize={10} isLoading={isLoading} />
          </>}
      </div>
    </> : <NoPermissionBox pageType="freeSearchUpside" />
    }
  </section >
}