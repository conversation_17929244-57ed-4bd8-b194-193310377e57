import { deleteUser<PERSON><PERSON>bdaRecordBidAction } from "@/actions/tllUserLambdaRecordBid";
import { renderStatusCount } from "@/app/(cp)/ad/bid/checklist/constants";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { TllUserLambdaRecordBidStatusEnum } from "@/lib/definitions/userLambdaRecordBid";
import dayjs from "dayjs";
import Link from "next/link";

export const bidHistoryColumns = [
  {
    header: "No",
    accessorKey: "no",
    cell: ({ row }: { row: any }) => {
      return <div>{row.index + 1}</div>;
    },
  },
  {
    header: "状態",
    accessorKey: "status",
    cell: ({ row }: { row: any }) => {
      return <div>{TllUserLambdaRecordBidStatusEnum[row.original.status as unknown as keyof typeof TllUserLambdaRecordBidStatusEnum]}</div>;
    },
  },
  {
    header: "価格",
    accessorKey: "biddingPrice",
  },
  {
    header: "結果",
    accessorKey: "biddingResult",
  },
  {
    header: "担当",
    cell: ({ row }: { row: any }) => {
      return <div>{row.original.salesUser?.name}</div>;
    },
  },
  {
    header: "CL作成日",
    cell: ({ row }: { row: any }) => {
      return <div>{dayjs(row.original.createdAt).format("YYYY/MM/DD")}</div>;
    },
  },
  {
    header: "コメント",
    cell: ({ row }: { row: any }) => {
      return <div>{row.original.comments ? row.original.comments.slice(0, 40) : "-"}</div>;
    },
  },
  {
    header: "CL要約",
    cell: ({ row }: { row: any }) => {
      return <div>
        {renderStatusCount(row.original)}
      </div>;
    },
  },
  {
    header: "操作",
    cell: ({ row }: { row: any }) => {
      return <div className="flex flex-row gap-2 justify-center">
        <Link href={`/ad/bid/${row.original.id}/edit`} target="_blank">
          <Button variant="outline">詳細修正</Button>
        </Link>

        <Button variant="destructive" size="sm" onClick={() => {
          if (row.original.id) {
            if (window.confirm("本当に削除しますか？")) {
              deleteUserLambdaRecordBidAction(row.original.id);
              toast({
                title: "削除しました",
              });
              window.location.reload();
            }
          }
        }}>
          削除
        </Button>
      </div>;
    },
  },
];
