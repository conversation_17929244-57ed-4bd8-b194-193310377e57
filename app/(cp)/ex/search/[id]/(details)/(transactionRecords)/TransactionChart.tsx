"use client"

import { TrendingUp } from "lucide-react"
import { Bar, CartesianGrid, ComposedChart, Line, LineChart, Tooltip, XAxis, YAxis } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { TransactionRecordProps } from "@/lib/definitions/transactionRecords"
import { mean } from "lodash-es"
import dayjs from "dayjs"
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord"

const chartConfig = {
  land: {
    label: "Land",
    color: "hsl(var(--chart-1))",
  },
  house: {
    label: "House",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig

export function TransactionChart({ transactionRecords, recordType }: { transactionRecords: TransactionRecordProps[], recordType: string }) {
  let rawData = {} as any;
  let dataChartData = [] as any[];

  transactionRecords.forEach((record: TransactionRecordProps) => {
    let dateString = dayjs(record.transactionQuartileStartDate).format("YYYY-MM-DD");
    if (rawData[dateString] === undefined) {
      rawData[dateString] = [] as any[];
    }

    rawData[dateString].push({
      ...record,
      unitLandPrice: record.landSize ? (record.transactionPrice || 0) / record.landSize / 10000 * 3.3 : 0,
      unitHousePrice: record.houseSize ? (record.transactionPrice || 0) / record.houseSize / 10000 * 3.3 : 0,
    });
  });

  Object.keys(rawData).sort((a, b) => dayjs(a).diff(dayjs(b))).forEach((key: string) => {
    dataChartData.push({
      date: key,
      land: mean(rawData[key].map((item: any) => item.unitLandPrice)),
      house: mean(rawData[key].map((item: any) => item.unitHousePrice)),
      count: rawData[key].length,
    });
  });

  return (
    <div className="flex flex-col gap-4">
      {recordType !== UserLambdaRecordType.MANSION && <div className="bg-neutral-50">
        <div className="text-neutral-900 p-2 mb-2">
          土地単価推移
        </div>

        <ChartContainer config={chartConfig}>
          <ComposedChart
            accessibilityLayer
            data={dataChartData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => dayjs(value).format("YYYY-MM-DD")}
            />

            <YAxis
              yAxisId="left"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.toFixed(0) + "万円"}
              domain={[
                (dataMin: number) => dataMin * 0.8,
                (dataMax: number) => dataMax * 1.2,
              ]}
            />

            <YAxis
              yAxisId="right"
              orientation="right"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.toFixed(0) + "件"}
              domain={[
                (dataMin: number) => dataMin * 0.8,
                (dataMax: number) => dataMax * 1.2,
              ]}
            />

            <Tooltip />

            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />

            <Line
              yAxisId="left"
              dataKey="land"
              type="natural"
              stroke="hsl(var(--chart-3))"
              strokeWidth={2}
              dot={false}
            />

            <Bar
              yAxisId="right"
              dataKey="count"
              fill="hsl(var(--chart-1))"
              barSize={30}
              opacity={0.8}
            />
          </ComposedChart>
        </ChartContainer>
      </div>}

      {recordType !== UserLambdaRecordType.LAND && <div className="bg-neutral-50">
        <div className="text-neutral-900 p-2 mb-2">
          延床単価推移
        </div>

        <ChartContainer config={chartConfig}>
          <ComposedChart
            accessibilityLayer
            data={dataChartData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => dayjs(value).format("YYYY-MM-DD")}
            />

            <YAxis
              yAxisId="left"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.toFixed(0) + "万円"}
              domain={[
                (dataMin: number) => dataMin * 0.8,
                (dataMax: number) => dataMax * 1.2,
              ]}
            />

            <YAxis
              yAxisId="right"
              orientation="right"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.toFixed(0) + "件"}
              domain={[
                (dataMin: number) => dataMin * 0.8,
                (dataMax: number) => dataMax * 1.2,
              ]}
            />

            <Tooltip />

            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />

            <Line
              yAxisId="left"
              dataKey="house"
              type="natural"
              stroke="hsl(var(--chart-2))"
              strokeWidth={2}
              dot={false}
            />

            <Bar
              yAxisId="right"
              dataKey="count"
              fill="hsl(var(--chart-1))"
              barSize={30}
              opacity={0.8}
            />
          </ComposedChart>
        </ChartContainer>
      </div>}
    </div>
  )
}
