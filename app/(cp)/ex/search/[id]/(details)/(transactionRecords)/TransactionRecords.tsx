
"use client"

import { TransactionRecordProps, TrTransactionRecordType } from "@/lib/definitions/transactionRecords";
import { getTransactionRecordsAction } from "@/actions/transactionRecords";
import { useState, useEffect } from "react";
import { DataTable } from "@/components/ui/data-table";
import { columnsBuildingHouse, columnsMansion, columnsLand } from "./transactionTableColumn";
import { TransactionChart } from "./TransactionChart";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";

export default function TransactionRecords({ currentUserLambdaRecord }: { currentUserLambdaRecord: any }) {
  const [transactionRecords, setTransactionRecords] = useState<TransactionRecordProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [columns, setColumns] = useState<any[]>([]);

  const fetchTransactionRecords = async () => {
    setIsLoading(true);

    let recordType = TrTransactionRecordType.HOUSE;

    if (currentUserLambdaRecord?.recordType === UserLambdaRecordType.MANSION) {
      recordType = TrTransactionRecordType.MANSION;
      setColumns(columnsMansion);
    } else if (currentUserLambdaRecord?.recordType === UserLambdaRecordType.HOUSE) {
      recordType = TrTransactionRecordType.HOUSE;
      setColumns(columnsBuildingHouse);
    } else if (currentUserLambdaRecord?.recordType === UserLambdaRecordType.LAND) {
      recordType = TrTransactionRecordType.LAND;
      setColumns(columnsLand);
    } else if (currentUserLambdaRecord?.recordType === UserLambdaRecordType.BUILDING) {
      recordType = TrTransactionRecordType.HOUSE;
      setColumns(columnsBuildingHouse);
    }

    const res = await getTransactionRecordsAction({ nearestStationGroupId: Number(currentUserLambdaRecord?.nearestStationGroupId), recordType: recordType as TrTransactionRecordType });
    if (res.success) {
      setTransactionRecords(res.data);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchTransactionRecords();
  }, [currentUserLambdaRecord]);

  return <section id="transactionRecords" className="bg-white">
    <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
      近隣公示成約事例
    </div>

    <div className="p-2">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
        <div className="col-span-1">
          <TransactionChart transactionRecords={transactionRecords} recordType={currentUserLambdaRecord?.recordType} />
        </div>

        <div className="col-span-2 flex flex-col gap-2" >
          <div className="text-sm text-neutral-500">
            合計: {transactionRecords.length}件
          </div>
          <DataTable columns={columns} data={transactionRecords} isLoading={isLoading} defaultPageSize={20} />
        </div>

      </div>
    </div>
  </section>;
}     