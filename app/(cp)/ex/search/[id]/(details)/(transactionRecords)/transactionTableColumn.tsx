import dayjs from "dayjs";

const mapper = {
  "HOUSE": "一戸建て",
  "MANSION": "マンション",
  "LAND": "土地",
  "FOREST": "山林",
  "FARM": "農地",
}

const transactionTableColumn = [
  // {
  //   header: "ID",
  //   accessorKey: "id",
  // },
  {
    header: "成約日",
    cell: ({ row }: { row: any }) => {
      return <div>{dayjs(row.original.transactionQuartileStartDate).format("YYYY/MM/DD")}</div>;
    },
  },
  {
    header: "タイプ",
    cell: ({ row }: { row: any }) => {
      return <div>{mapper[row.original.propertyType as keyof typeof mapper]}</div>;
    },
  },
  {
    header: "価格",
    cell: ({ row }: { row: any }) => {
      return <div>{row.original.transactionPrice ? (row.original.transactionPrice / 10000).toLocaleString() + "万円" : "-"}</div>;
    },
  },
  {
    header: "土地",
    cell: ({ row }: { row: any }) => {
      return <div className="flex flex-col items-center ">
        <div>{row.original.landSize ? row.original.landSize.toLocaleString() + "㎡" : "-"}</div>
        <div className="text-xs text-neutral-500">{row.original.landShape ? row.original.landShape : "-"} | {row.original.areaCityPlanningType ? row.original.areaCityPlanningType?.replace("地域", "") : "-"}</div>
        <div className="text-xs text-neutral-500">
          {row.original.landBuildingLandRatio ? row.original.landBuildingLandRatio + "%" : "-"} | {row.original.landFloorAreaRatio ? row.original.landFloorAreaRatio + "%" : "-"}
        </div>
      </div>;
    },
  },
  {
    header: "土地単価",
    cell: ({ row }: { row: any }) => {
      return <div className="flex flex-col items-center">
        <div>{row.original.landSize > 0 ? (row.original.transactionPrice / 10000 / row.original.landSize * 3.305785).toFixed(0) + "万円/坪" : "-"}</div>
        <div className="text-xs text-neutral-500">
          一種単価: {row.original.landSize > 0 ? (row.original.transactionPrice / 10000 / row.original.landSize * 3.305785 / row.original.landFloorAreaRatio * 100 / row.original.landBuildingLandRatio * 100).toFixed(0) + "万円/坪" : "-"}</div>
      </div>;
    },
  },
  {
    header: "道路",
    cell: ({ row }: { row: any }) => {
      return <div className="flex flex-col items-center ">
        <div>{row.original.frontRoadDirection ? row.original.frontRoadDirection : "-"}</div>
        <div className="text-xs text-neutral-500">{row.original.frontRoadClassification ? row.original.frontRoadClassification : "-"}</div>
        <div className="text-xs text-neutral-500">{row.original.frontRoadBreadth ? row.original.frontRoadBreadth + "m" : "-"}</div>
      </div>;
    },
  },
  {
    header: "建築",
    cell: ({ row }: { row: any }) => {
      return <div className="flex flex-col items-center ">
        <div>{row.original.houseSize ? row.original.houseSize.toLocaleString() + "㎡" : "-"}</div>
        <div className="text-xs text-neutral-500">{row.original.buildingUse ? row.original.buildingUse : "-"} | {row.original.material ? row.original.material : "-"}</div>
        <div className="text-xs text-neutral-500">{row.original.builtYear ? row.original.builtYear : "-"}年築</div>
      </div>;
    },
  },
  {
    header: "延床単価",
    cell: ({ row }: { row: any }) => {
      return <div className="flex flex-col items-center">
        <div>{row.original.houseSize > 0 ? (row.original.transactionPrice / 10000 / row.original.houseSize * 3.305785).toFixed(0) + "万円/坪" : "-"}</div>
      </div>;
    },
  },
];


export const columnsBuildingHouse = transactionTableColumn.filter((column) => ["成約日", "タイプ", "価格", "土地単価", "土地", "道路", "建築", "延床単価"].includes(column.header ?? ""));

export const columnsMansion = transactionTableColumn.filter((column) => ["成約日", "タイプ", "価格", "建築", "延床単価",].includes(column.header ?? ""));

export const columnsLand = transactionTableColumn.filter((column) => ["成約日", "タイプ", "価格", "土地単価", "土地", "道路"].includes(column.header ?? ""));
