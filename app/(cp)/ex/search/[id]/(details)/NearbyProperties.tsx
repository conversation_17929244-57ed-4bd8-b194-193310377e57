"use client";

import { getTopResultsFromIds } from "@/actions/tllUserLambdaRecordNearbyRecords";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel, paddColumnWithDistance } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { DataTable } from "@/components/ui/data-table";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { Suspense, useEffect, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import dayjs from "dayjs";
import { getPostalCodeAction } from "@/actions/geoPostalCodes";
import { GeoPostalCodeProps } from "@/lib/definitions";
import { getNearbyMaxMin80Percentile } from "@/lib/userLambdaRecord/getNearbyMaxMin80Percentile";
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { ColumnDef } from "@tanstack/react-table";
import { useAuthStore } from "@/store/auth";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";
import { ExportCSVButton } from "@/components/ExportCsvButton";
import { Button } from "@/components/ui/button";
import { FileDown } from "lucide-react";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Drawer, DrawerHeader, DrawerFooter, DrawerDescription, DrawerTitle, DrawerContent, DrawerClose, DrawerTrigger } from "@/components/ui/drawer";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import NearbyPropertiesExport from "./NearbyPropertiesExport";


export default function NearbyProperties({ currentUserLambdaRecord }: { currentUserLambdaRecord?: UserLambdaRecordProps | null }) {
  const { nearbyRecords, setNearbyRecords } = useUserLambdaRecordStore();
  const [selectedTypeTags, setSelectedTypeTags] = useState<string>("all");
  const [selectedUpdatedTag, setSelectedUpdatedTag] = useState<string>("12");
  const [postalCodeData, setPostalCodeData] = useState<GeoPostalCodeProps | null>(null);
  const [selectedBuiltYearToTag, setSelectedBuiltYearToTag] = useState<string>("all");
  const [selectedBuiltYearFromTag, setSelectedBuiltYearFromTag] = useState<string>("all");
  const [selectedStatusTag, setSelectedStatusTag] = useState<string>("all");
  const { setNearbyMaxMin80Percentile, nearbyMaxMin80Percentile } = useUserLambdaRecordStore();
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const { currentUser } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedLandSize, setSelectedLandSize] = useState<string>("all");
  const [selectedBuildingSize, setSelectedBuildingSize] = useState<string>("all");
  const [selectedPriceMax, setSelectedPriceMax] = useState<string>("all");
  const [exportingMode, setExportingMode] = useState<boolean>(false);

  const filterRecords = (record: any) => {
    if (selectedStatusTag !== "all" && getStatus(record) !== selectedStatusTag) return false;

    if (selectedTypeTags !== "all" && record.recordSubType !== selectedTypeTags) return false;

    if (selectedUpdatedTag !== 'ALL' && record?.priceChanges?.length && record.priceChanges.length > 0 && dayjs(record.priceChanges[record.priceChanges.length - 1].recordDate).isBefore(dayjs().subtract(parseInt(selectedUpdatedTag), 'month'))) {
      return false;
    }

    if (selectedPriceMax !== "all" && record.price && record.price > parseInt(selectedPriceMax)) {
      return false;
    }

    if (selectedLandSize !== "all" && record.landSize && record.landSize > parseInt(selectedLandSize)) {
      return false;
    }

    if (selectedBuildingSize !== "all" && record.buildingSize && record.buildingSize > parseInt(selectedBuildingSize)) {
      return false;
    }

    if (selectedBuiltYearToTag !== "all") {
      if (record.buildingBuiltYear && dayjs().year() - record.buildingBuiltYear > parseInt(selectedBuiltYearToTag)) {
        return false;
      }
    }

    if (selectedBuiltYearFromTag !== "all") {
      if (record.buildingBuiltYear && dayjs().year() - record.buildingBuiltYear < parseInt(selectedBuiltYearFromTag)) {
        return false;
      }
    }

    return true;
  }

  const fetchNearbyRecords = async () => {
    let distanceXIds = currentUserLambdaRecord?.propertyAnalysisResult?.nearbyRecordIdAndDistance || {};
    setIsLoading(true);

    const res = await getTopResultsFromIds({ distanceXId: distanceXIds });
    if (res.success) {
      let nearbyRecords = res.data;
      setNearbyRecords(nearbyRecords);

      await updatePercentile(nearbyRecords);

      if (currentUserLambdaRecord?.postalCode) {
        const postalCode = await getPostalCodeAction(currentUserLambdaRecord?.postalCode);
        if (postalCode?.success) {
          setPostalCodeData(postalCode?.data[0]);
        }
      }
    }

    setIsLoading(false);
  }

  const updatePercentile = async (nearbyRecords: UserLambdaRecordProps[]) => {
    const nearbyMaxMin80Percentile = await getNearbyMaxMin80Percentile(nearbyRecords.filter(filterRecords), currentUserLambdaRecord?.recordType as string);

    setNearbyMaxMin80Percentile(nearbyMaxMin80Percentile);
  }

  useEffect(() => {
    if (currentUserLambdaRecord?.id && nearbyRecords.length === 0) {
      fetchNearbyRecords();
    } else {
      updatePercentile(nearbyRecords);
    }

    if (currentUser && currentUser?.accessLevel) {
      setColumns(paddColumnWithDistance(getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps })));
    }
  }, [currentUserLambdaRecord, selectedStatusTag, selectedTypeTags, selectedUpdatedTag, currentUser, selectedBuiltYearToTag, selectedBuiltYearFromTag, selectedLandSize, selectedBuildingSize, selectedPriceMax]);

  const boldText = (text: string) => {
    return <span className="font-bold">{text}</span>
  }

  return (
    <section id="nearbyProperties" className="bg-white">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-start sm:justify-between p-2 gap-2">
        <div className="text-lg font-bold text-neutral-900 flex-1">
          近隣売買履歴
        </div>

        <span className="sm:float-right text-sm text-neutral-500">{nearbyMaxMin80Percentile?.comparatorType}:{nearbyMaxMin80Percentile?.min}~{nearbyMaxMin80Percentile?.max}(平均:{nearbyMaxMin80Percentile?.avg} vs 80%:{boldText(nearbyMaxMin80Percentile?.eightyPercentile)})
          {currentUserLambdaRecord?.recordType === 'BUILDING' && <>
            | 郵便{currentUserLambdaRecord?.postalCode}: 平均 {boldText(((postalCodeData?.buildingRecordRoi || 0) * 100).toFixed(2) + "%") || "-"}</>}
        </span>

        <ExportCSVButton
          data={nearbyRecords}
          type="userLambdaRecord"
        />

        {currentUser?.accessLevel && currentUser?.accessLevel >= 30 &&
          <Drawer>
            <DrawerTrigger asChild>
              <Button variant="outline" size="sm" className="shrink-0" onClick={() => {
                setExportingMode(!exportingMode);
              }}>
                <FileDown
                  className="w-4 h-4"
                />
              </Button>
            </DrawerTrigger>
            <DrawerContent className="h-[90vh] overflow-y-auto pointer-events-auto">
              <VisuallyHidden>
                <DrawerHeader>
                  <DrawerTitle>Are you absolutely sure?</DrawerTitle>
                  <DrawerDescription>This action cannot be undone.</DrawerDescription>
                </DrawerHeader>
              </VisuallyHidden>

              <NearbyPropertiesExport nearbyRecords={nearbyRecords} filterRecords={filterRecords} currentUserLambdaRecord={currentUserLambdaRecord} nearbyMaxMin80Percentile={nearbyMaxMin80Percentile} />
            </DrawerContent>
          </Drawer>
        }
      </div>

      <div className="bg-neutral-200 p-2">
        {currentUserLambdaRecord && <DataTable columns={columns} data={[currentUserLambdaRecord]} defaultPageSize={10} showFooter={false} showTableHeader={false} />}
      </div>

      <div className="flex flex-wrap gap-2 items-center p-2 my-2">
        <div className="flex flex-wrap gap-2">
          <Select
            disabled={!currentUser || currentUser?.accessLevel < 10}
            value={selectedStatusTag} onValueChange={(value) => {
              setSelectedStatusTag(value);
            }}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="販売状況" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">販売状況:全て</SelectItem>
              <SelectItem value="公開中">公開中のみ</SelectItem>
              <SelectItem value="成約">成約のみ</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex flex-wrap gap-2">
          <Select value={selectedPriceMax} onValueChange={(value) => {
            setSelectedPriceMax(value as string);
          }}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="価格" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">価格:全て</SelectItem>
              <SelectItem value="3000">3000万円以下</SelectItem>
              <SelectItem value="5000">5000万円以下</SelectItem>
              <SelectItem value="10000">1億円以下</SelectItem>
              <SelectItem value="20000">2億円以下</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* <div className="flex flex-wrap gap-2 items-center">
          <div className="text-sm flex items-center">状況：</div>
          <Badge
            variant={!selectedTags.length ? "default" : "outline"}
            onClick={() => {
              setSelectedTags([]);
            }}>
            ALL
          </Badge>
          {['公開中', '成約']
            .filter((t) => t !== undefined)
            .map((status, index) => (
              <Badge
                key={index}
                variant={selectedTags.indexOf(status) > -1 ? "default" : "outline"}
                onClick={() => {
                  let newSelectedTags = [];
                  if (selectedTags.indexOf(status) === -1) {
                    newSelectedTags = [...selectedTags, status];
                  } else {
                    const i = selectedTags.indexOf(status);
                    newSelectedTags = selectedTags.splice(i, 1);
                  }
                  setSelectedTags(newSelectedTags);
                }}
              >
                {status}
              </Badge>
            ))}
        </div> */}

        {currentUserLambdaRecord?.recordType !== 'LAND' && <div className="flex flex-row gap-2">
          <Select value={selectedBuiltYearFromTag} onValueChange={(value) => {
            setSelectedBuiltYearFromTag(value);
          }}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="築年数" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">築年数以上:全て</SelectItem>
              <SelectItem value="5">5年以上</SelectItem>
              <SelectItem value="10">10年以上</SelectItem>
              <SelectItem value="15">15年以上</SelectItem>
              <SelectItem value="20">20年以上</SelectItem>
              <SelectItem value="25">25年以上</SelectItem>
              <SelectItem value="30">30年以上</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedBuiltYearToTag} onValueChange={(value) => {
            setSelectedBuiltYearToTag(value);
          }}>
            <SelectTrigger className="w-[130px]">
              <SelectValue placeholder="築年数" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">築年数以内:全て</SelectItem>
              <SelectItem value="1">1年以内</SelectItem>
              <SelectItem value="3">3年以内</SelectItem>
              <SelectItem value="5">5年以内</SelectItem>
              <SelectItem value="10">10年以内</SelectItem>
              <SelectItem value="15">15年以内</SelectItem>
              <SelectItem value="20">20年以内</SelectItem>
              <SelectItem value="25">25年以内</SelectItem>
              <SelectItem value="30">30年以内</SelectItem>
            </SelectContent>
          </Select>
        </div>}

        <div className="flex flex-wrap gap-2">
          <Select value={selectedUpdatedTag} onValueChange={(value) => {
            setSelectedUpdatedTag(value as string);
          }}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="更新日" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">更新日:全て</SelectItem>
              <SelectItem value="1">1ヶ月内</SelectItem>
              <SelectItem value="3">3ヶ月内</SelectItem>
              <SelectItem value="6">6ヶ月内</SelectItem>
              <SelectItem value="12">1年以内</SelectItem>
              <SelectItem value="24">2年以内</SelectItem>
              <SelectItem value="60">5年以内</SelectItem>
              <SelectItem value="120">10年以内</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {currentUserLambdaRecord?.recordType === 'BUILDING' && <div className="flex flex-wrap gap-2 justify-center items-center">
          <Select value={selectedTypeTags} onValueChange={(value) => {
            // setSelectedUpdatedTag(value as string);
            setSelectedTypeTags(value);
          }}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="タイプ：" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">タイプ: 全て</SelectItem>
              <SelectItem value="アパート">アパート</SelectItem>
              <SelectItem value="マンション">マンション</SelectItem>
              <SelectItem value="ビル">ビル</SelectItem>
              <SelectItem value="店舗">店舗</SelectItem>
            </SelectContent>
          </Select>
        </div>}

        {currentUserLambdaRecord?.recordType !== 'MANSION' && <div className="flex flex-wrap gap-2">
          <Select value={selectedLandSize} onValueChange={(value) => {
            setSelectedLandSize(value as string);
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="土地面積" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全て: 土地面積</SelectItem>
              <SelectItem value="30">30平米以下</SelectItem>
              <SelectItem value="50">50平米以下</SelectItem>
              <SelectItem value="70">70平米以下</SelectItem>
              <SelectItem value="100">100平米以下</SelectItem>
              <SelectItem value="120">120平米以下</SelectItem>
              <SelectItem value="150">150平米以下</SelectItem>
              <SelectItem value="200">200平米以下</SelectItem>
              <SelectItem value="300">300平米以下</SelectItem>
              <SelectItem value="500">500平米以下</SelectItem>
            </SelectContent>
          </Select>
        </div>}

        {currentUserLambdaRecord?.recordType !== 'LAND' && <div className="flex flex-wrap gap-2">
          <Select value={selectedBuildingSize} onValueChange={(value) => {
            setSelectedBuildingSize(value as string);
          }}>
            <SelectTrigger className="">
              <SelectValue placeholder="建築面積" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全て: 建築面積</SelectItem>
              <SelectItem value="30">30平米以下</SelectItem>
              <SelectItem value="50">50平米以下</SelectItem>
              <SelectItem value="70">70平米以下</SelectItem>
              <SelectItem value="100">100平米以下</SelectItem>
              <SelectItem value="120">120平米以下</SelectItem>
              <SelectItem value="150">150平米以下</SelectItem>
              <SelectItem value="200">200平米以下</SelectItem>
              <SelectItem value="300">300平米以下</SelectItem>
              <SelectItem value="500">500平米以下</SelectItem>
            </SelectContent>
          </Select>
        </div>}
      </div>

      <Separator className="" />

      <div className="p-2">
        <div className="text-sm flex items-center text-neutral-500 mb-1">
          合計: {nearbyRecords.filter(filterRecords).length}件
        </div>

        {currentUserLambdaRecord && <DataTable columns={columns} data={nearbyRecords.filter(filterRecords)} defaultPageSize={10} showFooter={true} isLoading={isLoading} />}

        <div className="flex flex-col border-t border-neutral-200 pt-2 gap-2">
          <div className="text-sm text-neutral-500">
            * 距離: 本物件との距離を表示します。概算のため、実際の距離とは異なる場合があります。
          </div>
        </div>
      </div>
    </section >)

}