import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form"; // 导入表单组件
import { Input } from "@/components/ui/input"; // 导入输入组件
import { Button } from "@/components/ui/button"; // 导入按钮组件

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { record, z } from "zod";
import { tllUserLambdaRecordCfSilmulation } from "@/actions/tllUserLambdaRecordCfSilmulation";
import { getUserLambdaRecordAction } from "@/actions/tllUserLambdaRecords";
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { toast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { Loader2 } from "lucide-react";
import { getValuationRecordAction } from "@/actions/valuationRecord";

export default function PriceCfCalculationForm({ type }: { type: "valuation" | "search" }) {
  const { setCurrentUserLambdaRecord, currentUserLambdaRecord, nearbyMaxMin80Percentile } = useUserLambdaRecordStore();
  const [isLoading, setIsLoading] = useState(false);

  const getDefaultValues = (currentUserLambdaRecord: UserLambdaRecordProps) => {
    return {
      "investmentSellingEOYYear": currentUserLambdaRecord?.analysisSimulationConfig?.investmentSellingEOYYear || 1,
      "investmentMinimalROCPerc": currentUserLambdaRecord?.analysisSimulationConfig?.investmentMinimalROCPerc || 30,
      "investmentMinimalNetProfitPerc": currentUserLambdaRecord?.analysisSimulationConfig?.investmentMinimalNetProfitPerc || 20,
      "purchasePurchaseCostPerc": currentUserLambdaRecord?.analysisSimulationConfig?.purchasePurchaseCostPerc || 7,
      "purchaseReformCost": currentUserLambdaRecord?.analysisSimulationConfig?.purchaseReformCost || 0,
      "loanAmount": currentUserLambdaRecord?.analysisSimulationConfig?.loanAmount || 0,
      "loanInterestPerc": currentUserLambdaRecord?.analysisSimulationConfig?.loanInterestPerc || 2.8,
      "loanYears": currentUserLambdaRecord?.analysisSimulationConfig?.loanYears || 1,
      "holdingInitialRentIncome": currentUserLambdaRecord?.analysisSimulationConfig?.holdingInitialRentIncome || 0,
      "holdingEndRentIncome": currentUserLambdaRecord?.analysisSimulationConfig?.holdingEndRentIncome || 0,
      "holdingRunningCostPerc": currentUserLambdaRecord?.analysisSimulationConfig?.holdingRunningCostPerc || 20,
      "sellSellingFeePerc": currentUserLambdaRecord?.analysisSimulationConfig?.sellSellingFeePerc || 3,
      "sellSellingCapRatePerc": currentUserLambdaRecord?.analysisSimulationConfig?.sellSellingCapRatePerc || 8,
      "sellSellingGfaUnitPrice": currentUserLambdaRecord?.analysisSimulationConfig?.sellSellingGfaUnitPrice || 30,
      "sellSellingLandUnitPrice": currentUserLambdaRecord?.analysisSimulationConfig?.sellSellingLandUnitPrice || 30,
    }
  }
  const form = useForm({
    resolver: zodResolver(z.object({
      "investmentSellingEOYYear": z.number().min(1),
      "investmentMinimalROCPerc": z.number().min(1),
      "investmentMinimalNetProfitPerc": z.number().min(1),
      "purchasePurchaseCostPerc": z.number().min(1),
      "purchaseReformCost": z.number().min(0).default(0), // 设置默认值为0
      "loanAmount": z.number().min(1),
      "loanInterestPerc": z.number().min(1),
      "loanYears": z.number().min(1),
      "holdingInitialRentIncome": z.number().min(0),
      "holdingEndRentIncome": z.number().min(0),
      "holdingRunningCostPerc": z.number().min(1),
      "sellSellingFeePerc": z.number().min(1),
      "sellSellingCapRatePerc": z.number().min(1),
      "sellSellingGfaUnitPrice": z.number().min(1),
      "sellSellingLandUnitPrice": z.number().min(1),
    })),
    defaultValues: getDefaultValues(currentUserLambdaRecord || {} as UserLambdaRecordProps),
  });

  const getRentUpside = (currentUserLambdaRecord: UserLambdaRecordProps) => {
    if (!currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgRent) {
      return null;
    }

    if (!currentUserLambdaRecord?.yearlyIncome) {
      return null;
    }

    let current = ((currentUserLambdaRecord?.yearlyIncome * 3.3 || 0) / 12 / (currentUserLambdaRecord?.buildingSize || currentUserLambdaRecord?.recordValues.unitArea) * 10000) || null;

    let avg = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgRent || 0) * 3.3;
    let diff = current && current > 0 ? ((avg - current) / current * 100).toFixed(1) : null;

    return {
      current,
      diff: diff ? parseFloat(diff) : null,
      avg
    }
  }

  useEffect(() => {
    form.reset(getDefaultValues(currentUserLambdaRecord || {} as UserLambdaRecordProps));
  }, [currentUserLambdaRecord]);

  return <Form {...form}>
    <form onSubmit={async (e) => {
      setIsLoading(true);
      e.preventDefault();

      await tllUserLambdaRecordCfSilmulation({
        currentUserLambdaRecord: currentUserLambdaRecord as UserLambdaRecordProps,
        data: form.getValues(),
        type: type
      });

      const response = type === "valuation" ? await getValuationRecordAction(currentUserLambdaRecord?.id || "") : await getUserLambdaRecordAction(currentUserLambdaRecord?.id || "");

      if (response.success) {
        toast({
          title: "成功",
          description: "CF計算結果を保存しました",
        });
        setCurrentUserLambdaRecord(response.data);
      } else {
        toast({
          title: "エラー",
          description: "ユーザーラムダレコードの取得に失敗しました",
        });
      }
      setIsLoading(false);
    }} className="space-y-4">
      <div className="grid grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="investmentSellingEOYYear"
          render={({ field }) => (
            <FormItem>
              <FormLabel>投資年数:</FormLabel>
              <FormControl>
                <Input type="number" value={field.value} min={1} onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormDescription>予想売却年数</FormDescription>
              <FormMessage />
            </FormItem>
          )} />

        <FormField
          control={form.control}
          name="investmentMinimalROCPerc"
          render={({ field }) => (
            <FormItem>
              <FormLabel>最低ROC(%):</FormLabel>
              <FormControl>
                <Input type="number" value={field.value} step="0.1" onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormDescription>ROC=年収入/投資額</FormDescription>
              <FormMessage />
            </FormItem>
          )} />

        <FormField
          control={form.control}
          name="investmentMinimalNetProfitPerc"
          render={({ field }) => (
            <FormItem>
              <FormLabel>最低純利益率(%):</FormLabel>
              <FormControl>
                <Input type="number" value={field.value} step="0.1" onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormDescription>純利益率=純純利益/取得費用</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="purchasePurchaseCostPerc"
          render={({ field }) => (
            <FormItem>
              <FormLabel>購入費用(%):</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} min={1} onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormDescription>取得税+銀行融資手数料+保険+仲介手数料=7%想定
                <br />
                (売主の場合は4%)</FormDescription>
              <FormMessage />
            </FormItem>
          )} />

        <FormField
          control={form.control}
          name="purchaseReformCost"
          render={({ field }) => (
            <FormItem>
              <FormLabel>リフォームコスト(万円):</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormDescription>購入時想定してるレノべ・リフォームコスト</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="loanAmount"
          render={({ field }) => {
            const price = currentUserLambdaRecord?.price || 1;
            const ltv = (field.value / price) * 100; // 计算LTV百分比
            return (
              <FormItem className="">
                <FormLabel>ローン金額(万円):</FormLabel>
                <FormControl className="flex-1">
                  <Input className="w-full" type="number" value={field.value} onChange={(e) => field.onChange(Number(e.target.value))} />
                </FormControl>
                <FormDescription> LTV=融資額/物件価格=
                  <span className="font-bold">{ltv.toFixed(2)}%</span>
                </FormDescription>
                <FormMessage />
              </FormItem>
            );
          }} />

        <FormField
          control={form.control}
          name="loanInterestPerc"
          render={({ field }) => (
            <FormItem>
              <FormLabel>ローン金利(%):</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} min={1} step="0.1" onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />

        <FormField
          control={form.control}
          name="loanYears"
          render={({ field }) => (
            <FormItem>
              <FormLabel>ローン年数:</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} min={1} onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="holdingInitialRentIncome"
          render={({ field }) => (
            <FormItem>
              <FormLabel>初期年収入(万円):</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} onChange={(e) => field.onChange(Number(e.target.value))} step="0.1" />
              </FormControl>
              <FormDescription>ROI=
                <span className="font-bold">{(field.value / (currentUserLambdaRecord?.price || 1) * 100).toFixed(2)}%</span>
                (現状ベース) | 月{(field.value / 12).toFixed(0)}万円</FormDescription>
              <FormMessage />
            </FormItem>
          )} />

        <FormField
          control={form.control}
          name="holdingEndRentIncome"
          render={({ field }) => (
            <FormItem>
              <FormLabel>終期年収入(万円):</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} onChange={(e) => field.onChange(Number(e.target.value))} step="0.1" />
              </FormControl>
              <FormDescription>ROI=
                <span className="font-bold">

                  {(field.value / (currentUserLambdaRecord?.price || 1) * 100).toFixed(2)}%
                </span>
                (想定
                {field.value > (currentUserLambdaRecord?.yearlyIncome || 0) && "+"}
                {((100 * (field.value - (currentUserLambdaRecord?.yearlyIncome || 0)) / (currentUserLambdaRecord?.yearlyIncome || 1))).toFixed(1)}%) | 月{(field.value / 12).toFixed(0)}万円 | 平均:
                <span className={(() => {
                  const rentUpside = getRentUpside(currentUserLambdaRecord as UserLambdaRecordProps);
                  return rentUpside?.diff !== null && rentUpside?.diff !== undefined && rentUpside.diff > 0 ? "text-green-500" : "text-red-500";
                })()}>
                  {getRentUpside(currentUserLambdaRecord as UserLambdaRecordProps)?.diff ?? "-"}%
                </span>
              </FormDescription>
              <FormMessage />
            </FormItem>
          )} />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <FormField
          control={form.control}
          name="holdingRunningCostPerc"
          render={({ field }) => (
            <FormItem>
              <FormLabel>運用コスト(%):</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} min={1} onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormDescription>不明の場合20%想定</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
        <FormField
          control={form.control}
          name="sellSellingFeePerc"
          render={({ field }) => (
            <FormItem>
              <FormLabel>売却手数料(%):</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} min={1} onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormMessage />
              <FormDescription>売却価格の3%を想定</FormDescription>
            </FormItem>
          )} />

        {currentUserLambdaRecord?.recordType === 'BUILDING' && <FormField
          control={form.control}
          name="sellSellingCapRatePerc"
          render={({ field }) => (
            <FormItem>
              <FormLabel>売却利回り(%):</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} min={1} step="0.01" onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormMessage />
              <FormDescription>
                AVG: {nearbyMaxMin80Percentile?.avg}% | 80P: {nearbyMaxMin80Percentile?.eightyPercentile}% | 平均: {((nearbyMaxMin80Percentile?.avg + nearbyMaxMin80Percentile?.eightyPercentile) / 2).toFixed(2)}%
                <br />
                土地: {currentUserLambdaRecord?.landSize}m2 | 建物: {currentUserLambdaRecord?.buildingSize}m2
              </FormDescription>
            </FormItem>
          )} />}

        {(currentUserLambdaRecord?.recordType === 'MANSION' || currentUserLambdaRecord?.recordType === 'HOUSE') && <FormField
          control={form.control}
          name="sellSellingGfaUnitPrice"
          render={({ field }) => (
            <FormItem>
              <FormLabel>売却延床単価:</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} min={1} step="0.1" onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormMessage />
              <FormDescription>
                平均: {nearbyMaxMin80Percentile?.avg} | 80P: {nearbyMaxMin80Percentile?.eightyPercentile}
                <br />
                土地: {currentUserLambdaRecord?.landSize}m2 | 建物: {currentUserLambdaRecord?.buildingSize}m2
              </FormDescription>
            </FormItem>
          )} />}

        {currentUserLambdaRecord?.recordType === 'LAND' && <FormField
          control={form.control}
          name="sellSellingLandUnitPrice"
          render={({ field }) => (
            <FormItem>
              <FormLabel>売却土地一種単価:</FormLabel>
              <FormControl>
                <Input className="w-full" type="number" value={field.value} min={1} step="0.01" onChange={(e) => field.onChange(Number(e.target.value))} />
              </FormControl>
              <FormMessage />
              <FormDescription>
                平均 {nearbyMaxMin80Percentile?.avg} | 80P: {nearbyMaxMin80Percentile?.eightyPercentile}
                <br />
                土地: {currentUserLambdaRecord?.landSize}m2
              </FormDescription>
            </FormItem>
          )} />}
      </div>

      <Button type="submit" className="mt-4" disabled={isLoading}>
        {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "保存"}
      </Button>
    </form>
  </Form>
}