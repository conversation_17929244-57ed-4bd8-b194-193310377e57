import { But<PERSON> } from "@/components/ui/button";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Tooltip, TooltipTrigger } from "@/components/ui/tooltip";
import { Progress } from "@/components/ui/progress";
import { TooltipContent } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { ChevronDownIcon, ChevronRight, ChevronUpIcon, Info } from "lucide-react";

export function PriceInfoRankingSimpleProgressBar({ currentPrice, minPrice, maxPrice, avgPrice, p80Price }: {
  currentPrice: number;
  minPrice: number;
  maxPrice: number;
  avgPrice: number;
  p80Price: number;
}) {
  // Do some magic to keep the bar
  if (currentPrice < minPrice) minPrice = currentPrice * 0.8;
  if (currentPrice > maxPrice) maxPrice = currentPrice * 1.2;
  // Ensure valid values
  if (minPrice >= maxPrice) return null;

  // Calculate percentiles
  const percentile = ((currentPrice - minPrice) / (maxPrice - minPrice)) * 100;
  const avgPercentile = ((avgPrice - minPrice) / (maxPrice - minPrice)) * 100;
  const p80Percentile = ((p80Price - minPrice) / (maxPrice - minPrice)) * 100; // 计算 p80Percentile

  return (
    <div className="w-full p-4 pb-16">
      <div className="flex justify-between text-xs font-bold text-gray-700 mb-1 w-full z-10">
        <span>¥{(parseInt(minPrice.toString()))}万円</span>
        <span>¥{(parseInt(maxPrice.toString()))}万円</span>
      </div>

      <div className="relative w-full">
        {/* Main Progress Bar */}
        <Progress value={percentile} className="h-4 bg-gray-200" />

        {/* Markers */}
        <div className="relative w-full -mt-5">
          {/* Current Price Marker */}
          <div
            className={cn(
              "absolute top-0 transform -translate-x-1/2 text-xs font-bold text-white px-2 py-1 rounded-lg bg-black text-white",
              "transition-all duration-200"
            )}
            style={{ left: `${percentile}%` }}
          >
            本件: {currentPrice}
          </div>

          {/* Average Price Marker */}
          <div className="transform -translate-x-1/2 absolute top-[24px] flex flex-col items-center gap-1 " style={{ left: `${avgPercentile}%` }}>
            <div
              className=" text-xs font-bold text-black bg-yellow-300 rounded-lg px-2 py-1"
            >
              査定価格: {Number(avgPrice.toFixed(0))}
            </div>
            <ChevronUpIcon className="w-4 h-4 absolute top-[-10px] text-yellow-300 fill-current" />
          </div>

          {/* P80 Price Marker */}
          <div className="transform -translate-x-1/2  absolute top-[-24px] flex flex-col items-center gap-1 " style={{ left: `${p80Percentile}%` }}>
            <div
              className="text-xs font-bold text-black px-2 py-1 bg-red-300 rounded-lg"
            >
              <span className="flex items-center gap-1">
                保守査定価格: {Number(p80Price.toFixed(0))}
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="w-4 h-4 text-gray-500" />
                    </TooltipTrigger>
                    <TooltipContent>
                      この査定価格は、周辺物件の80パーセンタイルの単価に基づいています。
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </span>
            </div>
            <ChevronDownIcon className="w-4 h-4 absolute bottom-[-10px] text-red-300 fill-current" />
          </div>
        </div>
      </div>
    </div>
  );
}