"use client";

import { useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/ui/data-table";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";

export default function NearbyPropertiesExport({ nearbyRecords, filterRecords, currentUserLambdaRecord, nearbyMaxMin80Percentile }: { nearbyRecords: any[], filterRecords: any, currentUserLambdaRecord: any, nearbyMaxMin80Percentile: any }) {
  const [columns, setColumns] = useState<any[]>([]);

  useEffect(() => {
    setColumns(getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUserLambdaRecord.currentUser, recordType: currentUserLambdaRecord.recordType, isForReport: true }));
  }, [currentUserLambdaRecord]);

  const getDiff = () => {
    let current = currentUserLambdaRecord.yearlyIncome / currentUserLambdaRecord.price * 100;
    let nearby = nearbyMaxMin80Percentile.avg;

    let diff = parseFloat(((current - nearby) / nearby * 100).toFixed(2));
    let amount = parseFloat((diff / 100 * currentUserLambdaRecord.price).toFixed(0));

    console.log(current, nearby, diff, amount);
    return {
      diff,
      amount
    }
  }

  return (
    <div className="h-full w-full"  >
      <div className="flex flex-col gap-2 p-4">
        近隣売買履歴データ
      </div>

      <Separator />

      <div className="flex flex-row gap-2 p-4 bg-neutral-100 border-b border-neutral-200 justify-center items-center">
        <div className="  text-sm flex flex-col text-right text-lg">
          {/* <span className="">
          近隣物件の利回りは：
          <b>
            {nearbyMaxMin80Percentile?.min}~{nearbyMaxMin80Percentile?.max}%
          </b>
        </span> */}
          <span className="">
            本物件の利回:
            <b className="">
              {parseFloat((currentUserLambdaRecord?.yearlyIncome / currentUserLambdaRecord?.price * 100).toFixed(2))}%
            </b>
          </span>

          <span className="">
            近隣物件の平均利回:
            <b>
              {nearbyMaxMin80Percentile?.avg}%
            </b>
          </span>
        </div>

        <div className="flex flex-col gap-1">
          <span className="text-4xl ml-2 text-green-500">
            割安度: {getDiff().diff}%
          </span>

          <span className="text-xl ml-2 text-green-800">
            割安額: {getDiff().amount}万円
          </span>
        </div>

      </div>


      <div className="p-4">
        <div className="text-sm flex items-center text-neutral-500 mb-1">
          合計: {nearbyRecords.filter(filterRecords).length}件
        </div>

        {nearbyRecords && nearbyRecords.length > 0 && <DataTable columns={columns} data={nearbyRecords.filter(filterRecords)} defaultPageSize={10} showFooter={true} />}

        <div className="flex flex-col border-t border-neutral-200 pt-2 gap-2">
          <div className="text-sm text-neutral-500">
            * 距離: 本物件との距離を表示します。概算のため、実際の距離とは異なる場合があります。
          </div>
        </div>
      </div>
    </div>
  );
} 