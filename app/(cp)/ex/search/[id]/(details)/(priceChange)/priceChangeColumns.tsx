import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import Link from "next/link"; // 引入 Link 组件
import dayjs from "dayjs";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { ColumnDef } from "@tanstack/react-table";
import { LockIcon } from "lucide-react";
import { renderLock } from "@/components/userLambdaRecord/userlambdaRecordTableColumnsCommon";

export const maskStatus = ({ columns, currentUser }: { columns: ColumnDef<any>[], currentUser: TllUserProps }) => {
  return columns.map((column) => {
    if (column.header === "販売状況") {
      // if (!currentUser || currentUser.accessLevel < 10) {
      //   return {
      //     header: '販売状況',
      //     cell: ({ row }: { row: any }) => {
      //       return renderLock()
      //     }
      //   };
      // }

      return {
        ...column,
        cell: ({ row }: { row: any }) => {
          let status = row.original.status;
          if (row.original.status !== '成約' && row.original.status !== '公開中') {
            status = '公開中'
          }

          return <Badge variant="outline" className={status === '成約' ? 'bg-red-100 border-none' : ''}>{status}</Badge>
        }
      }
    }


    if (column.header === "取引態様") {
      if (!currentUser || currentUser.accessLevel < 30) {
        return {
          header: '取引態様',
          cell: ({ row }: { row: any }) => {
            if (row.original.brokerType === null || row.original.brokerType === '' || row.original.brokerType === '-') {
              return <Badge variant="outline">
                仲介
              </Badge>
            }

            if (row.original.brokerType?.indexOf('売主') > -1) {
              return <Badge variant="outline">
                {row.original.brokerType?.replace('売主', '')}
              </Badge>
            }


            if (row.original.brokerType?.indexOf('代理') > -1) {
              return <Badge variant="outline">
                代理
              </Badge>
            }

            return <Badge variant="outline">
              仲介
            </Badge>
          }
        };
      }

      return {
        ...column,
      }
    }

    // if (column.header === "価格") {
    //   if (!currentUser || currentUser.accessLevel < 10) {
    //     return {
    //       header: '価格',
    //       cell: ({ row }: { row: any }) => {
    //         return renderLock()
    //       }
    //     };
    //   }
    //   return column;
    // }


    return column;
  })
}

export const getPriceChangeColumnsBasedOnAccessLevel = (currentUser: TllUserProps) => {
  if (currentUser && currentUser.accessLevel >= 90) {
    return priceChangeColumns;
  }

  if (currentUser && currentUser.accessLevel >= 30) {
    return priceChangeColumns.filter(column => !['コメント'].includes(column.header))
  }

  return maskStatus({ columns: priceChangeColumns.filter(column => !["会社", 'REINS', "広告", 'チラシ', 'コメント', "ソース"].includes(column.header)), currentUser })
}

export const priceChangeColumnsForSNS = [
  {
    header: '日付',
    cell: ({ row }: { row: any }) => dayjs(row.original.recordDate).format("YYYY-MM-DD"),
  },
  {
    header: '販売状況',
    accessorKey: 'status',
  },
  {
    header: '価格',
    accessorKey: 'price',
  },
]

export const priceChangeColumns = [
  {
    header: '日付',
    cell: ({ row }: { row: any }) => dayjs(row.original.recordDate).format("YYYY-MM-DD"),
  },
  {
    header: '販売状況',
    cell: ({ row }: { row: any }) => (
      <Badge variant="outline" className={row.original.status === '成約' ? 'bg-red-100 border-none' : ''}>{row.original.status}</Badge>
    ),
  },
  {
    header: 'ソース',
    cell: ({ row }: { row: any }) => row.original.source,
  },
  {
    header: '価格',
    accessorKey: 'price',
  },
  {
    header: '年収入',
    accessorKey: 'yearlyIncome',
    cell: ({ row }: { row: any }) => <div className="flex flex-col items-center justify-center">
      {row.original.yearlyIncome !== undefined && row.original.yearlyIncome > 0 ? `${row.original.yearlyIncome}万円` : '-'}

      <div className="text-xs text-neutral-500">
        {row.original.yearlyIncome !== undefined && row.original.yearlyIncome > 0 ? `${((row.original.yearlyIncome * 100) / row.original.price).toFixed(2)}%` : ''}
      </div>
    </div>
  },
  {
    header: '会社',
    cell: ({ row }: { row: any }) => {
      return row.original.company?.fullName ?
        <Link href={`/an/company/${row.original.companyId}`} passHref className="underline text-xs">
          {row.original.company?.fullName.slice(0, 10)} {/* 添加默认值 */}
        </Link>
        : '-'
    }
  },
  {
    header: '取引態様',
    cell: ({ row }: { row: any }) => {
      return row.original.brokerType !== null && row.original.brokerType !== '' ? (
        <Badge variant="outline" className={row.original.brokerType?.indexOf('売主') > -1 ? 'bg-green-500' : ''}>
          {row.original.brokerType.slice(0, 2)}
        </Badge>
      ) : (
        '-'
      );
    },
  },
  {
    header: 'チラシ',
    cell: ({ row }: { row: any }) => {
      const chirashiLink = row.original.chirashiLink;

      if (!chirashiLink || chirashiLink === '' || chirashiLink === '図面') {
        return '-';
      }

      const isPdf = chirashiLink.indexOf("pdf") > -1;
      const isImage = chirashiLink.indexOf("png") > -1 || chirashiLink.indexOf("jpg") > -1;

      return (
        <a
          href={chirashiLink}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center gap-1 text-gray-600 hover:text-gray-800 hover:underline"
          onClick={(e) => {
            if (isPdf) {
              e.preventDefault();
              // 在新窗口打开PDF，避免自动下载
              window.open(chirashiLink, '_blank', 'noopener,noreferrer');
            }
          }}
        >
          {isPdf && (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-file-text">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
                <line x1="16" x2="8" y1="13" y2="13" />
                <line x1="16" x2="8" y1="17" y2="17" />
                <line x1="10" x2="8" y1="9" y2="9" />
              </svg>
              PDF
            </>
          )}
          {isImage && (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-image">
                <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                <circle cx="9" cy="9" r="2" />
                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" />
              </svg>
              画像
            </>
          )}
          {!isPdf && !isImage && (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-link">
                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
              </svg>
              LINK
            </>
          )}
        </a>
      );
    },
  },
  {
    header: 'コメント',
    accessorKey: 'comments',
    cell: ({ row }: { row: any }) => {

      return <span
        className="overflow-hidden text-ellipsis whitespace-pre-wrap text-xs min-w-[240px]"
        style={{ display: '-webkit-box', WebkitBoxOrient: 'vertical', overflow: 'hidden', WebkitLineClamp: 5 }} // 限制显示行数为5行
      >
        {row.original.comments !== null && row.original.comments !== '' ? row.original.comments : '-'}
      </span>
    }
  },
  {
    header: 'REINS',
    // accessorKey: 'reinsNumber',
    cell: ({ row }: { row: any }) => {
      return <div className="flex flex-col items-center justify-center">
        {row.original.reinsNumber ?
          row.original.reinsNumber
          : '-'}

        <div className="text-xs text-neutral-500">
          {row.original.canAdvertise}
        </div>

      </div>
    }
  },
  // {
  //   header: '作成日',
  //   accessorKey: 'createdAt',
  //   cell: ({ row }: { row: any }) => {
  //     return row.original.createdAt ? dayjs(row.original.createdAt).format('YYYY/MM/DD HH:mm') : '-';
  //   },
  // }
];