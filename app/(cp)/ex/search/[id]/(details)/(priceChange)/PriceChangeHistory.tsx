"use client";

import { useState, useEffect } from "react";

import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { Badge } from "@/components/ui/badge";
import { Eye, Trash2Icon } from "lucide-react";
import { DataTable } from "@/components/ui/data-table";
import { getPriceChangeColumnsBasedOnAccessLevel } from "./priceChangeColumns";
import PriceChangeHistoryChart from "./PriceChangeHistoryChart";
import PriceChangeChirashi from "./PriceChangeChirashi";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { deletePriceChange } from "@/actions/tllUserLambdaRecordPriceChange";
import DailyLimitExhaustedBox from "@/app/(cp)/my/usage/DailyLimitExhaustedBox";
import { getPricingTierForUser } from "@/lib/constants/pricingTier";
import {
  getTotalDailyUsageCount,
  markDailyUsage,
  isTotalDailyUsageLimitExceeded,
  DAILY_USAGE_FEATURES,
} from "@/lib/utils/dailyUsageTracker";

export default function PriceChangeHistory({
  currentUserLambdaRecord,
  currentUser,
  recordId,
}: {
  currentUserLambdaRecord: UserLambdaRecordProps | null;
  currentUser: TllUserProps;
  recordId: string;
}) {
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const [selectedCompany, setSelectedCompany] = useState<
    string | null | undefined
  >(null); // 新增状态管理选中的公司
  const [hideForSns, setHideForSns] = useState(false);

  // Check and handle daily usage for free users
  useEffect(() => {
    if (currentUser && recordId) {
      // For free users (accessLevel 1), check daily usage limit
      if (currentUser.accessLevel === 1) {
        const pricingTier = getPricingTierForUser(currentUser);
        const dailyLimit = pricingTier?.dailyPriceChangeHistoryCount || 3;

        const totalUsage = getTotalDailyUsageCount(
          DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY,
        );

        if (totalUsage < dailyLimit) {
          // Mark usage for this record (only counts once per day per record)
          const wasMarked = markDailyUsage(
            DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY,
            recordId,
          );

          if (wasMarked) {
            console.log(
              `🔥 PriceChangeHistory viewed for first time today: ${recordId}`,
            );
          }
        }
      }
    }
  }, [currentUser, recordId]);

  useEffect(() => {
    if (currentUser) {
      setColumns([
        ...getPriceChangeColumnsBasedOnAccessLevel(currentUser),
        ...(currentUser?.accessLevel >= 90
          ? [
              {
                header: "操作",
                cell: ({ row }: { row: any }) => {
                  return (
                    <Button
                      variant="destructive"
                      size="icon"
                      className="flex flex-row gap-2"
                      onClick={async () => {
                        let res = window.confirm("本当に削除しますか？");
                        console.log(res);
                        if (res) {
                          await deletePriceChange(row.original.id);
                          window.location.reload();
                        }
                      }}
                    >
                      <Trash2Icon className="w-4 h-4" />
                    </Button>
                  );
                },
              },
            ]
          : []),
      ]);
    }
  }, [currentUser]);

  const pricingTier = getPricingTierForUser(currentUser);
  const dailyLimit = pricingTier?.dailyPriceChangeHistoryCount || 3;

  const totalUsage = getTotalDailyUsageCount(
    DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY,
  );

  return currentUser?.accessLevel === 1 &&
    isTotalDailyUsageLimitExceeded(
      DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY,
      dailyLimit,
    ) ? (
    <DailyLimitExhaustedBox
      pageType="priceChangeHistory"
      currentCount={totalUsage}
      maxCount={dailyLimit}
    />
  ) : (
    <section id="priceChangeHistory" className="bg-white">
      <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2 flex items-center justify-between">
        価格変更履歴
        {currentUser?.accessLevel && currentUser?.accessLevel >= 30 && (
          <div className="text-sm text-neutral-500 flex items-center gap-2">
            <Button
              variant={hideForSns ? "default" : "outline"}
              size="icon"
              onClick={() => {
                setHideForSns(!hideForSns);
              }}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="grid sm:grid-cols-1 lg:grid-cols-3">
        <div className="overflow-hidden lg:col-span-1 border-t border-neutral-200 sm:border-t-0">
          <div className="overflow-hidden lg:col-span-2 border-t border-neutral-200 sm:border-t-0">
            <PriceChangeHistoryChart
              currentUserLambdaRecord={
                currentUserLambdaRecord as UserLambdaRecordProps
              }
            />
          </div>

          {currentUser?.accessLevel && currentUser?.accessLevel >= 30 && (
            <PriceChangeChirashi
              selectedProperty={
                currentUserLambdaRecord as UserLambdaRecordProps
              }
              currentUser={currentUser as TllUserProps}
            />
          )}
        </div>

        <div className="overflow-hidden p-2 lg:col-span-2 bg-neutral-50 rounded-md m-2 border border-neutral-200">
          {(() => {
            // For free users (accessLevel 1), check daily usage limit
            if (currentUser?.accessLevel === 1) {
              const pricingTier = getPricingTierForUser(currentUser);
              const dailyLimit = pricingTier?.dailyPriceChangeHistoryCount || 3;
              const totalUsage = getTotalDailyUsageCount(
                DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY,
              );

              if (
                isTotalDailyUsageLimitExceeded(
                  DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY,
                  dailyLimit,
                )
              ) {
                return (
                  <DailyLimitExhaustedBox
                    pageType="priceChangeHistory"
                    currentCount={totalUsage}
                    maxCount={dailyLimit}
                  />
                );
              }

              // Free user within daily limit - show limited content
              return (
                <>
                  <div className="text-sm text-neutral-900 mt-2 mb-2">
                    合計: {currentUserLambdaRecord?.priceChanges?.length}件履歴
                  </div>
                  <DataTable
                    columns={columns}
                    data={
                      currentUserLambdaRecord?.priceChanges?.sort((a, b) =>
                        new Date(a.recordDate || "").getTime() <
                        new Date(b.recordDate || "").getTime()
                          ? 1
                          : -1,
                      ) || []
                    }
                    defaultPageSize={10}
                    showFooter={false}
                  />
                </>
              );
            }

            // For paid users (accessLevel >= 10) - show full content
            return (
              <>
                {currentUser && currentUser.accessLevel >= 30 && (
                  <div className="flex flex-wrap gap-2 mb-2">
                    <Badge
                      variant={selectedCompany === null ? "default" : "outline"}
                      onClick={() => setSelectedCompany(null)}
                    >
                      すべて
                    </Badge>

                    {Array.from(
                      new Set(
                        currentUserLambdaRecord?.priceChanges?.map(
                          (change) => change.company?.fullName,
                        ),
                      ),
                    )
                      .filter(
                        (companyName) =>
                          companyName !== null &&
                          companyName !== "" &&
                          companyName != undefined,
                      )
                      .map((companyName, index) => {
                        const displayedName = companyName?.slice(0, 10);
                        return (
                          <Badge
                            key={index}
                            variant={
                              selectedCompany === companyName
                                ? "default"
                                : "outline"
                            }
                            onClick={() => setSelectedCompany(companyName)}
                            title={companyName}
                          >
                            {displayedName}
                          </Badge>
                        );
                      })}
                  </div>
                )}

                <div className="text-sm text-neutral-900 mt-2 mb-2">
                  合計: {currentUserLambdaRecord?.priceChanges?.length}件履歴
                </div>

                <DataTable
                  columns={columns.filter(
                    (column) =>
                      !hideForSns ||
                      (column.header !== "ソース" &&
                        column.header !== "取引態様" &&
                        column.header !== "操作" &&
                        column.header !== "REINS" &&
                        column.header !== "コメント"),
                  )}
                  data={
                    currentUserLambdaRecord?.priceChanges
                      ?.filter((change) =>
                        selectedCompany
                          ? change.company?.fullName === selectedCompany
                          : true,
                      )
                      .sort((a, b) =>
                        new Date(a.recordDate || "").getTime() <
                        new Date(b.recordDate || "").getTime()
                          ? 1
                          : -1,
                      ) || []
                  }
                  defaultPageSize={10}
                  showFooter={
                    currentUser && currentUser?.accessLevel >= 30 ? true : false
                  }
                />
              </>
            );
          })()}

          <div className="text-xs text-gray-500 mt-2 flex flex-col gap-2 border-t border-neutral-200 pt-4">
            <div>* 販売状況は,「公開中」または「成約」となります。</div>
            <div>
              *
              「公開中」：Urbalyticsが最後にデータを取得した時点において販売中であったことを示すものであり、現在も販売中であることを保証するものではありません。
            </div>
            <div>
              *
              「成約」：最新のリスティングデータと法務局の登録記録をクロス参照した結果に基づいております。なお、実際の成約価格や条件は変更されている可能性がありますので、あらかじめご了承ください。
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
