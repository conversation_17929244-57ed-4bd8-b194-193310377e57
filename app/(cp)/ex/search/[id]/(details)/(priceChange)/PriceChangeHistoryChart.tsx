import { type ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { YAxis, XAxis, LineChart, Line, CartesianGrid } from "recharts";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import dayjs from "dayjs";
export default function PriceChangeHistoryChart({ currentUserLambdaRecord }: { currentUserLambdaRecord: UserLambdaRecordProps }) {
  const uniqueCompanies = Array.from(new Set(currentUserLambdaRecord?.priceChanges?.map(change => change?.company?.fullName))) || [];

  const chartData = currentUserLambdaRecord?.priceChanges?.map(change => ({
    recordDate: change.recordDate,
    [change?.company?.fullName || '']: change.price,
    price: change.price
  })).sort((a, b) => new Date(a.recordDate || "").getTime() < new Date(b.recordDate || "").getTime() ? -1 : 1);

  const chartConfig = {
    a: {
      label: "Desktop",
      color: "hsl(var(--chart-1))",
    },
    b: {
      label: "Mobile",
      color: "hsl(var(--chart-2))",
    },
  } satisfies ChartConfig


  return (
    <div className="overflow-hidden p-2 lg:col-span-1 flex flex-col gap-2 rounded-md bg-neutral-50 m-2 border border-neutral-200">
      <div className="flex flex-col gap-2">
        <div className="text-base font-bold text-neutral-700">
          価格推移表
        </div>
      </div>

      <div className="p-2">
        <ChartContainer config={chartConfig}>
          <LineChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 12,
              right: 24,
            }}
          >
            <CartesianGrid vertical={false} />
            <YAxis
              domain={[(dataMin: number) => dataMin * 0.9, (dataMax: number) => dataMax * 1.1]}
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => {
                return value.toLocaleString();
              }}
            />
            <XAxis
              dataKey="recordDate"
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => {
                return dayjs(value).format("YYYY-MM-DD");
              }}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />

            {/* {uniqueCompanies.map((company, index) => (
            <Line key={index} dataKey={company} type="monotone" stroke={index % 2 === 0 ? 'hsl(var(--chart-1))' : 'hsl(var(--chart-2))'} strokeWidth={2} dot={true} />
          ))} */}
            <Line dataKey="price" type="monotone" stroke="hsl(var(--chart-1))" strokeWidth={2} dot={true} label={{
              position: "top",
              formatter: (value: number) => value.toLocaleString(),
            }} />
          </LineChart>
        </ChartContainer>
      </div>

      {/* <div className="flex flex-col items-middle gap-2 text-sm text-center justify-center">
      <div className="flex flex-row justify-center gap-2 font-medium leading-none">
        (Mock)今月は-%の上昇 <TrendingUp className="h-4 w-4" />
      </div>
      <div className="leading-none text-muted-foreground">(Mock)過去6ヶ月のデータを表示</div>
    </div> */}
    </div>
  );
}