import <PERSON><PERSON><PERSON><PERSON>or<PERSON><PERSON><PERSON> from "@/components/IframeForChirashi";
import { Separator } from "@/components/ui/separator";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { UserLambdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { getLatestPriceChange } from "@/lib/userLambdaRecord/getLatestPriceChange";
import { Aperture, } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { TllUserProps } from "@/lib/definitions/tllUser";
import dayjs from "dayjs";

export default function PriceChangeChirashi({ selectedProperty, currentUser }: {
  selectedProperty: UserLambdaRecordProps,
  currentUser: TllUserProps
}) {
  const latestPriceChange = getLatestPriceChange(selectedProperty);
  const [selectedTab, setSelectedTab] = useState<string>();
  const [selectedKeibaiLink, setSelectedKeibaiLink] = useState<string>();
  // let items = selectedProperty?.priceChanges?.find((change: UserLambdaRecordPriceChangeProps) => change.chirashiLink && /^https?:\/\//.test(change.chirashiLink));

  const filterPriceChange = (change: UserLambdaRecordPriceChangeProps) => {
    if (currentUser && currentUser.accessLevel >= 90) {
      return change.chirashiLink && (/^https?:\/\//.test(change.chirashiLink)) || change.source === "KEIBAI_SUMITOMO";
    }

    return change.chirashiLink && (/^https?:\/\//.test(change.chirashiLink)) && change.source !== "KEIBAI_SUMITOMO";
  }


  useEffect(() => {
    const item = selectedProperty?.priceChanges?.filter((change: UserLambdaRecordPriceChangeProps) => filterPriceChange(change))

    if (item && item.length > 0) {
      let selectedTab = item[item.length - 1];
      setSelectedTab(selectedTab.id as string);

      if (selectedTab.source === "KEIBAI_SUMITOMO") {
        let link = Object.values((JSON.parse(selectedTab.chirashiLink || "{}")))[0] as string;
        setSelectedKeibaiLink(link);
      }
    }
  }, [selectedProperty])

  const currentPriceChange = selectedProperty?.priceChanges?.find((change: UserLambdaRecordPriceChangeProps) => change.id === selectedTab) as UserLambdaRecordPriceChangeProps;

  const currentChirashiLink = currentPriceChange?.chirashiLink || latestPriceChange?.chirashiLink;

  const renderChirashiItem = (change: UserLambdaRecordPriceChangeProps) => {
    return (
      <ToggleGroupItem key={change.id} value={change.id} className="h-[60px] mx-0.5">
        <div className="flex flex-col">
          <p className="text-xs text-neutral-600">{dayjs(change.recordDate).format("YYYY-MM-DD")}</p>
          <p className="text-sm text-neutral-900 font-bold">{change.price}万円</p>
          {currentUser && currentUser.accessLevel >= 90 && <p className="text-xs text-neutral-500">{change.company?.companyName ? change.company?.companyName?.slice(0, 6) : change.status}</p>}
        </div>
      </ToggleGroupItem>
    )
  }

  const renderChirashiContent = (change: UserLambdaRecordPriceChangeProps) => {
    if (change?.chirashiLink !== undefined && /^https?:\/\//.test(change.chirashiLink)) {
      return <div className="text-center">
        <div className="flex justify-between items-center w-full mb-2">
          <div className="flex flex-1">
            <p className="text-xs text-neutral-900">{currentPriceChange?.company?.companyName || "未設定"}
              | {currentPriceChange?.brokerType || "未設定"} | {currentPriceChange?.canAdvertise || "未設定"}
            </p>
          </div>
          <Link href={currentChirashiLink || ""} target="_blank" rel="noopener noreferrer" className="text-xs text-neutral-900 underline">
            LINK
          </Link>
        </div>
        <IframeForChirashi url={currentChirashiLink || ""} currentUser={currentUser} />
      </div>;
    }

    if (change?.source === "KEIBAI_SUMITOMO") {
      return <div className="">
        {currentUser && currentUser.accessLevel >= 90 && <div className="flex flex-row justify-between items-center w-full mb-2">
          <div className="flex flex-col w-full">
            {Object.keys(JSON.parse(change.chirashiLink || "{}")).map((key) => <div key={key} className="flex w-full flex-row justify-start items-center">
              <span className="text-sm text-neutral-900 flex-1 underline" onClick={() => setSelectedKeibaiLink(JSON.parse(change.chirashiLink || "{}")[key])}>{key}</span>
              <span className="text-xs text-neutral-900">
                <Link href={JSON.parse(change.chirashiLink || "{}")[key]} target="_blank" className="text-xs text-neutral-900 underline">
                  LINK
                </Link>
              </span>
            </div>)}
          </div>
        </div>}
        <IframeForChirashi url={selectedKeibaiLink || ""} currentUser={currentUser} />
      </div>
    }

    return <div className="w-full h-[400px] flex flex-col items-center justify-center bg-gray-50 gap-4">
      <p className="text-4xl text-neutral-300">画像準備中</p>
      <Aperture className="w-12 h-12 text-neutral-300" />
    </div>
  }

  return (
    <div className="min-h-[200px] border border-neutral-200 rounded-md bg-neutral-50 m-2">
      <div className="flex flex-row overflow-x-auto scrollbar-hide justify-start pb-0 px-4 whitespace-nowrap ">
        <ToggleGroup type="single" value={selectedTab} onValueChange={(value) => {
          let selectedPriceChange = selectedProperty?.priceChanges?.find((change: UserLambdaRecordPriceChangeProps) => change.id === value);

          if (selectedPriceChange?.source === "KEIBAI_SUMITOMO") {
            let link = Object.values((JSON.parse(selectedPriceChange?.chirashiLink || "{}")))[0] as string;
            setSelectedKeibaiLink(link);
          }
          setSelectedTab(value);
        }}>

          {selectedProperty?.priceChanges?.filter((change: UserLambdaRecordPriceChangeProps) => filterPriceChange(change)).map((change: UserLambdaRecordPriceChangeProps) => renderChirashiItem(change))}
        </ToggleGroup>
      </div>

      <Separator className="my-0" />

      <div className="w-full p-2 sm:p-4 ">
        {renderChirashiContent(currentPriceChange)}
      </div>
    </div>
  );
}
