"use client";

import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { Card, CardContent } from "@/components/ui/card";
import dayjs from "dayjs";
import "dayjs/locale/ja";
import { Separator } from "@/components/ui/separator";
import Stamp from "@/components/assets/stamp.png"; // Adjust path based on file location
import Image from "next/image";
import html2canvas from "html2canvas";
import { Button } from "@/components/ui/button";
import { Printer } from "lucide-react";
import { useRef } from "react";

export default function BidKaitsuke({ currentUserLambdaRecord, bidTerms, biddingPrice, initialCost, extraStatement }: { currentUserLambdaRecord: UserLambdaRecordProps, bidTerms: { itemName: string, selected: boolean }[], biddingPrice: number, initialCost: number, extraStatement: string }) {
  const sectionRef = useRef<HTMLDivElement>(null);

  const handleExport = async () => {
    if (!sectionRef.current) return;
    console.log('🔥sectionRef.current', sectionRef.current);
    const canvas = await html2canvas(sectionRef.current, {
      backgroundColor: "#ffffff", // 强制白色背景
      useCORS: true, // 如果图片有跨域问题
    });
    const dataUrl = canvas.toDataURL("image/png");

    // Create a download link
    const link = document.createElement("a");
    link.href = dataUrl;
    link.download = `${currentUserLambdaRecord?.address}_買付証明書_${dayjs().locale('ja').format('YYYYMMDD')}_${biddingPrice}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleExportPdf = async () => {
    if (!sectionRef.current) return;

    const html2pdf = (await import("html2pdf.js")).default;

    html2pdf()
      .from(sectionRef.current)
      .set({
        margin: 0,
        filename: `${currentUserLambdaRecord?.address}_買付証明書_${dayjs().format('YYYYMMDD')}_${biddingPrice}.pdf`,
        html2canvas: { scale: 2, useCORS: true },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' },
      })
      .save();
  };

  return <div>
    <div className="border-b bg-neutral-100 flex flex-row items-center justify-between p-4">
      <div className="flex-1">
        買付証明書 Preview
      </div>
      <div className="flex flex-row gap-2 items-center justify-left">
        <Button size="sm" variant="outline" onClick={() => {
          handleExport();
        }}>
          <Printer className="w-4 h-4" /> PNG
        </Button>

        <Button size="sm" variant="outline" onClick={() => {
          handleExportPdf();
        }}>
          <Printer className="w-4 h-4" /> PDF
        </Button>
      </div>
    </div>

    {/* // 297 will overflow for pdf so make it smaller */}
    <div className="w-[210mm] h-[290mm] bg-white p-2 overflow-hidden box-border" ref={sectionRef}>
      <div className="mx-auto h-full p-6 bg-white shadow-md border border-gray-300 rounded-lg">
        <div className="flex flex-col gap-4">
          <div>
            <div className="text-left text-base font-bold text-gray-700">売主様</div>
            <h1 className="text-2xl font-bold text-center mt-2">買付証明書</h1>
            <p className="text-center text-base text-gray-600">Letter of Intent</p>
          </div>

          {currentUserLambdaRecord?.recordType !== "MANSION" && <div>
            <h2 className="font-semibold">土地表記</h2>
            <div className="list-decimal list-inside ml-4">
              <div>1. 物件所在地: {currentUserLambdaRecord?.address}</div>
              <div>2. 土地面積: {currentUserLambdaRecord?.landSize} 平米</div>
            </div>
          </div>}

          {currentUserLambdaRecord?.recordType !== "LAND" && <div>
            <h2 className="font-semibold mb-[-2px]">建物表記</h2>
            <div className="list-decimal list-inside ml-4">
              <div>1. 物件所在地: {currentUserLambdaRecord?.address}</div>
              <div>2. 建築面積: {currentUserLambdaRecord?.buildingSize} 平米</div>
            </div>
          </div>}

          <p>
            表記物件を下記条件により買い付けることを証明いたします。
            <br />(有効期限: {dayjs().locale('ja').add(2, 'week').format('YYYY年MM月DD日（ddd）')})
          </p>
        </div>

        <div className="border-t border-b my-8 py-4 border-gray-300">
          <div className="flex flex-col gap-2 mt-2 text-lg text-center justify-center items-center">
            <div className="flex flex-row gap-4 justify-center items-center">
              <p>購入希望価格:</p>
              <p className="text-right text-2xl font-bold">{biddingPrice?.toLocaleString()}</p>
              <p>万円</p>
            </div>

            <div className="flex flex-row gap-4 justify-center items-center">
              <p>手付金:</p>
              <p className="text-right text-2xl font-bold">{initialCost?.toLocaleString()}</p>
              <p>万円</p>
            </div>
          </div>
        </div>

        <div className="text-base">
          <h2 className="font-semibold">特記事項</h2>
          <div className="list-decimal list-inside">
            {bidTerms.filter((term) => term.selected).map((term, index) => {
              return <div className="ml-4" key={index}>{index + 1}. {term.itemName.indexOf("-") !== -1 ? term.itemName.split("-")[1] : term.itemName}</div>
            })}

            {extraStatement && <div className="ml-4">
              {bidTerms.filter((term) => term.selected).length + 1}.

              <span>
                {extraStatement}
              </span>
            </div>}
          </div>
        </div>

        {/* <Separator className="my-8" /> */}

        <div className="border-gray-300 text-base text-left mt-10 flex flex-col justify-between gap-2 relative">
          <div className="text-right">
            {dayjs().locale('ja').format('YYYY年MM月DD日（ddd）')}
          </div>

          <h2 className="font-semibold">買付人</h2>

          <div className="flex flex-col gap-2 w-full">
            <p className="w-full border-b border-gray-500 pb-2">住所:
              <span className="ml-2">
                東京都渋谷区神泉町 11-11
              </span>
            </p>
            <p className="w-full border-b border-gray-500 pb-2">氏名:
              <span className="ml-2">
                TLL合同会社　代表社員 　タン チェン
              </span>
              <Image src={Stamp} alt="logo" width={60} height={60} className="absolute left-[320px] top-[90px] " sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 800px" priority />
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
}