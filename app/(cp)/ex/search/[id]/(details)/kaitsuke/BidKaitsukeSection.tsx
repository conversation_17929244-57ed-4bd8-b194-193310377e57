"use client";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON>er, <PERSON>er<PERSON>ontent, Drawer<PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Eye, Printer } from "lucide-react";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import BidKaitsuke from "./BidKaitsuke";
import { useEffect, useRef, useState } from "react";
import { bidTermsConstants } from "../bidTermsConstants";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import html2canvas from "html2canvas";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

export default function BidKaitsukeSection({ currentUserLambdaRecord }: { currentUserLambdaRecord: UserLambdaRecordProps }) {
  const [bidTerms, setBidTerms] = useState<{
    itemName: string;
    selected: boolean;
  }[]>([]);
  const [biddingPrice, setBiddingPrice] = useState<number>(0);
  const [initialCost, setInitialCost] = useState<number>(0);
  const [extraStatement, setExtraStatement] = useState<string>("");
  const sectionRef = useRef<HTMLDivElement>(null);

  // const handleExport = async () => {
  //   if (!sectionRef.current) return;

  //   console.log('🔥sectionRef.current', sectionRef.current);
  //   const canvas = await html2canvas(sectionRef.current);
  //   const dataUrl = canvas.toDataURL("image/png");

  //   // Create a download link
  //   const link = document.createElement("a");
  //   link.href = dataUrl;
  //   link.download = "exported-section.png";
  //   document.body.appendChild(link);
  //   link.click();
  //   document.body.removeChild(link);
  // };


  useEffect(() => {
    if (currentUserLambdaRecord?.id) {
      let itemTerms = [];
      for (const [category, items] of Object.entries(bidTermsConstants)) {
        for (const [item, propertyTypes] of Object.entries(items)) {
          itemTerms.push({
            itemName: `${category} - ${item}`,
            selected: propertyTypes.includes(currentUserLambdaRecord?.recordType as string),
          })
        }
      }
      setBidTerms(itemTerms);
      let bidPrice = currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice;

      if (bidPrice) {
        setBiddingPrice(Math.floor(bidPrice / 10) * 10);
        setInitialCost(Math.floor(bidPrice * 0.05 / 50) * 50);
      }
    }
  }, [currentUserLambdaRecord?.id]);

  return <div className="bg-neutral-50 p-2 border border-neutral-300 gap-2 flex flex-col">
    <div className="flex flex-row gap-2 border-b border-neutral-200 justify-between items-center">
      <div className="font-bold text-neutral-900 p-2 flex-1">買付作成</div>

      <Drawer>
        <DrawerTrigger asChild>
          <Button size="sm" variant="outline" onClick={() => {
          }}>
            <Eye className="w-4 h-4" />
          </Button>
        </DrawerTrigger>
        <DrawerContent className="h-[calc(100vh-100px)] w-full">
          <VisuallyHidden>
            <DrawerHeader className="border-b bg-neutral-100 flex flex-row items-center justify-between">
              <DrawerTitle className="flex-1">買い付け Preview</DrawerTitle>
            </DrawerHeader>
          </VisuallyHidden>
          <div className="w-full overflow-y-auto pt-[480px] bg-neutral-500 flex items-center justify-center">
            {currentUserLambdaRecord && <BidKaitsuke currentUserLambdaRecord={currentUserLambdaRecord} bidTerms={bidTerms} biddingPrice={biddingPrice} initialCost={initialCost} extraStatement={extraStatement} />}
          </div>
        </DrawerContent>
      </Drawer>
    </div>


    <div className="grid grid-cols-2 gap-2 items-center justify-left">
      <div className="flex flex-col gap-2 items-left justify-left">
        <Label>買付価格</Label>
        <Input type="number" placeholder="買付証明書のタイトル" value={biddingPrice} onChange={(e) => setBiddingPrice(Number(e.target.value))} />
        <Label className="text-sm text-neutral-500">vs 物件価格: {currentUserLambdaRecord?.price} ({(biddingPrice / currentUserLambdaRecord?.price * 100).toFixed(0)}% )</Label>
      </div>

      <div className="flex flex-col gap-2 items-left justify-left">
        <Label>手付金</Label>
        <Input type="number" placeholder="買付証明書のタイトル" value={initialCost} onChange={(e) => setInitialCost(Number(e.target.value))} />
        <Label className="text-sm text-neutral-500">vs 買付価格: {biddingPrice} ({(initialCost / biddingPrice * 100).toFixed(2)}% )</Label>
      </div>
    </div>

    <div className="flex flex-col p-2">
      {bidTerms.map((term, index) => {
        return <div key={index} className="flex flex-row gap-2 items-center justify-left">
          <Checkbox id={`terms-${index}`} checked={term.selected} onCheckedChange={(checked: boolean) => {
            setBidTerms(bidTerms.map((t, i) => {
              if (i === index) {
                return { ...t, selected: checked };
              }
              return t;
            }));
          }} />
          <div>{term.itemName}</div>
        </div>
      })}

      <div className="flex flex-row gap-2 items-center justify-left mt-2">
        <Input type="text" placeholder="追加説明" value={extraStatement} onChange={(e) => setExtraStatement(e.target.value)} />
      </div>
    </div>
  </div>

}