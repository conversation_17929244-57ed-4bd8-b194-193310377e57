import { coef } from "@/app/api/cron/constants";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import PropertyInfoAdminMaterials from "./PropertyInfoAdminMaterials";

export default function PropertyInfoAdmin({ selectedProperty }: { selectedProperty: UserLambdaRecordProps }) {
  return (
    <div className="flex flex-col border border-neutral-500 m-2 rounded-md bg-neutral-100">
      <div className="font-bold text-neutral-500 border-b mb-2 p-2">[TLL管理者用] 物件情報</div>

      <div className="grid grid-cols-1 p-2 gap-2">
        <div className="flex flex-col gap-2">
          <div>
            <strong>COEF:</strong>
            {coef(selectedProperty)}
          </div>
          <div className="text-xs flex flex-row gap-2 text-neutral-500">
            所有権: 1 | 借地権: 0.7 (商業地の場合0.8) | 底地権: 0.3 (商業地の場合0.2)
          </div>
        </div>

        <div className="flex flex-row gap-2">
          <strong>COMPOSITE TITLE:</strong>
          {selectedProperty?.compositeTitle}
        </div>

        <div className="flex flex-col gap-2">
          <strong>詳細:</strong>
          <div className="text-xs flex-wrap flex overflow-x-auto scrollbar-hide">
            {JSON.stringify(selectedProperty?.recordValues)}
          </div>
        </div>

        <PropertyInfoAdminMaterials currentUserLambdaRecord={selectedProperty as UserLambdaRecordProps} />
      </div>
    </div>
  );
}