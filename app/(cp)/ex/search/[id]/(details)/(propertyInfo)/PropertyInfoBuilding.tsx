import { Badge } from "@/components/ui/badge";
import { Table } from "@/components/ui/table";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { getM2GFA, getM2Rent } from "@/lib/userLambdaRecord/m2Metrics";

export default function PropertyInfoBuilding({ selectedProperty }: { selectedProperty: UserLambdaRecordProps }) {

  const renderRoomSize = () => {
    let roomCount = selectedProperty?.buildingRoomCount;

    if (roomCount === undefined || roomCount === null || roomCount === 0) return <strong>部屋数:</strong>;

    // let roomSize = (selectedProperty.buildingSize !== undefined
    //   ? selectedProperty.buildingSize
    //   : selectedProperty.recordValues.unitArea) /
    //   (selectedProperty.buildingRoomCount || 1);


    let roomSize = selectedProperty?.buildingSize !== undefined && selectedProperty?.buildingSize !== null && selectedProperty?.buildingSize > 0 && selectedProperty?.buildingRoomCount !== undefined && selectedProperty?.buildingRoomCount !== null && selectedProperty?.buildingRoomCount > 0 ? (selectedProperty?.buildingSize / (selectedProperty?.buildingRoomCount || 1)) : 0;

    return <>
      <strong>部屋平均面積:</strong>
      {roomCount !== undefined &&
        `${roomSize?.toFixed(1)}m2`}

      {roomSize < 15 ? (
        <Badge
          variant={'destructive'}
          style={{
            marginLeft: 5,
          }}
        >
          狭小住宅
        </Badge>
      ) : (
        ''
      )}
    </>
  }

  return (
    <div className="flex flex-col border border-neutral-200 m-2 rounded-md bg-neutral-50">
      <div className="font-bold text-neutral-500 border-b border-neutral-200 mb-2 p-2 border-t border-neutral-200 sm:border-t-0">建物情報</div>

      <div className="grid grid-cols-1 p-2 gap-1">
        <p>
          <strong>延床面積:</strong> {selectedProperty?.buildingSize || "-"}㎡
          <span className="text-sm text-neutral-500 ml-2">
            (約{selectedProperty?.buildingSize !== undefined && selectedProperty?.buildingSize !== null && selectedProperty?.buildingSize > 0 ? (selectedProperty?.buildingSize / 3.3).toFixed(1) : "-"}坪)
          </span>
        </p>
        <span className="text-sm text-neutral-500">
          GFA単価: {getM2GFA(selectedProperty).toFixed(0)}万円/㎡
        </span>

        <p><strong>建築材料:</strong> {selectedProperty?.buildingMaterial}</p>
        <div className="flex flex-row gap-1">
          <strong>築年数:</strong>

          <span>
            {selectedProperty?.buildingBuiltYear}
            {selectedProperty?.buildingBuiltYear !== undefined && selectedProperty?.buildingBuiltYear > 0 &&
              `(築${dayjsWithTz().year() - selectedProperty?.buildingBuiltYear}年)`}
          </span>
          {selectedProperty?.buildingBuiltYear === dayjsWithTz().year() ? (
            <Badge
              variant={'secondary'}
              style={{
                fontWeight: 'bold',
                backgroundColor: '#30ff68',
              }}
            >
              新築
            </Badge>
          ) : null}


          {selectedProperty?.buildingBuiltYear !== undefined && selectedProperty?.buildingBuiltYear > 0 &&
            selectedProperty?.buildingBuiltYear <= 1981 ? (
            <Badge
              variant={'destructive'}
              style={{
                fontWeight: 'bold',
              }}
            >
              旧耐震
            </Badge>
          ) : null}
        </div>

        <div className="grid grid-cols-2 gap-1">
          <p><strong>建物階数:</strong> {selectedProperty?.buildingLevel && parseInt(selectedProperty?.buildingLevel) > 0 ? `${selectedProperty?.buildingLevel}` : '-'}</p>
        </div>

        <p>
          {renderRoomSize() || '-'}
        </p>

        <p>
          <strong>部屋平均賃料: </strong>
          {selectedProperty?.yearlyIncome !== undefined && selectedProperty?.yearlyIncome !== null && selectedProperty?.yearlyIncome > 0 && selectedProperty?.buildingRoomCount !== undefined && selectedProperty?.buildingRoomCount !== null && selectedProperty?.buildingRoomCount > 0 ? (selectedProperty?.yearlyIncome * 10000 / 12 / (selectedProperty?.buildingRoomCount || 1)).toFixed(0).toLocaleString() : '-'}円
        </p>
        <span className="text-sm text-neutral-500">
          賃料(m2単価): {getM2Rent(selectedProperty) ? getM2Rent(selectedProperty).toFixed(0).toLocaleString() : '-'}円/㎡
        </span>


        <p>
          <strong>間取り:</strong> {selectedProperty?.recordValues?.unitLayout}
        </p>

        <p>
          <strong>物件階数:</strong> {selectedProperty?.recordValues?.unitLevel || "-"}
        </p>
      </div>
    </div>
  );
}