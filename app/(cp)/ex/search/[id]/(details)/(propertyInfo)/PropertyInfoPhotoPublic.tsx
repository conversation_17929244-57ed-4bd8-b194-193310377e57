"use client";

import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi } from "@/components/ui/carousel";
import { Aperture, X } from "lucide-react";
import { getMaterialsFolderContentsInFolder } from "@/actions/helper/supabaseNode";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { TllUserProps } from "@/lib/definitions/tllUser";
import useEmblaCarousel from "embla-carousel-react";
import { Button } from "react-day-picker";
import { DialogContent, Dialog, DialogClose, DialogTitle, DialogHeader } from "@/components/ui/dialog";
import { DialogTrigger } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

export default function PropertyInfoChirashi({ selectedProperty, currentUser }: {
  selectedProperty: UserLambdaRecordProps,
  currentUser: TllUserProps
}) {
  const [fileLinks, setFileLinks] = useState<any[]>([]);
  const [api, setApi] = useState<CarouselApi>();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const getFileLinks = async () => {
    const res = await getMaterialsFolderContentsInFolder({
      folderName: selectedProperty?.id as string,
      bucketKey: "propertyMaterialsPublic",
    });

    if (res.success) {
      setFileLinks(res.data);
    }
  };

  useEffect(() => {
    if (selectedProperty?.id) {
      getFileLinks();
    }
  }, [selectedProperty?.id]);

  useEffect(() => {
    if (!api) {
      return
    }
    const updateSelected = () => {
      const index = api.selectedScrollSnap();
      setSelectedIndex(index);
    };

    api.on("select", updateSelected);
    updateSelected(); // 初始选中一次
  }, [api]);


  const handleThumbnailClick = (index: number) => {
    if (api) {
      console.log("🔍 scrolling to", index);
      api.scrollTo(index);
      setSelectedIndex(index);
    }
  };

  return <div className="min-h-[100px] flex flex-col gap-4 bg-neutral-50 rounded-md border border-neutral-200 m-2">
    <div className="font-bold text-neutral-500 border-b border-neutral-200 mb-2 p-2">
      物件写真
    </div>

    {fileLinks.length > 0 ? (
      <>
        <div className="flex flex-col gap-2 items-center justify-center">
          <div className="w-[calc(100%-100px)] h-[360px] flex flex-col">
            <Carousel setApi={setApi}>
              <CarouselContent>
                {fileLinks.map((record: any, idx: number) => (
                  <CarouselItem key={idx} className="w-full justify-center items-center h-[360px] flex flex-col">
                    {/* <Image
                  src={record.link}
                  alt={`Property Image ${idx + 1}`}
                  width={900}
                  height={600}
                  className="rounded-lg object-contain cursor-zoom-in"
                  onClick={() => window.open(record.link, "_blank")}
                />
                 */}

                    <Dialog>
                      <DialogTrigger asChild>
                        <Image
                          src={record.link}
                          alt={`Property Image ${idx + 1}`}
                          width={900}
                          height={600}
                          className="rounded-lg object-contain overflow-auto cursor-zoom-in"
                          onClick={() => setPreviewImage(record.link)}
                        />
                      </DialogTrigger>

                      <DialogContent className="max-w-5xl w-full p-0 bg-neutral-900 border-none shadow-none flex justify-center items-center">
                        <VisuallyHidden>
                          <DialogHeader className="border-b bg-neutral-100 flex flex-row items-center justify-between">
                            <DialogTitle className="flex-1">写真アップロード</DialogTitle>
                          </DialogHeader>
                        </VisuallyHidden>

                        <div className="relative w-full h-[90vh] flex justify-center items-center bg-black">
                          <Image
                            src={record.link}
                            alt="Full preview"
                            fill
                            className="object-contain rounded-md"
                          />
                          {/* 🔥 自定义关闭按钮 */}
                          <DialogClose className="absolute top-4 right-4 p-2 bg-white/80 hover:bg-white text-black rounded-full shadow-md transition z-20">
                            <X className="h-6 w-6" />
                          </DialogClose>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious />
              <CarouselNext />
            </Carousel>
          </div>
        </div>


        {fileLinks.length > 0 && <div className="flex flex-nowrap overflow-x-auto p-2 gap-2 justify-start border-t border-neutral-300 pt-2 w-full">
          {fileLinks.sort((a, b) => a.updatedAt.localeCompare(b.updatedAt)).map((record: any, idx: number) => (
            <button
              key={`thumb-${idx}`}
              onClick={() => handleThumbnailClick(idx)}
              className={`shrink-0 border border-gray-300 rounded hover:border-gray-500 transition ${selectedIndex === idx ? "border-gray-800 border-2" : "border-transparent border-2"}`}
            >
              <Image
                src={record.link}
                alt={`Thumbnail ${idx + 1}`}
                width={100}
                height={66}
                className="rounded object-contain w-[80px] h-[60px] bg-white"
              />
            </button>
          ))}



        </div>
        }
      </>
    ) : (
      <div className="h-[360px] flex flex-col items-center justify-center bg-gray-50 gap-4 border-neutral-200 m-2">
        <p className="text-4xl text-neutral-300">画像準備中</p>
        <Aperture className="w-12 h-12 text-neutral-300" />
      </div>
    )}
  </div>
}