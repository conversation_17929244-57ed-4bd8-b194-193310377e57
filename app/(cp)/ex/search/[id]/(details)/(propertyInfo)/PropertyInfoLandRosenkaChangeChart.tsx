"use client";

import {
  BarChart,
  Bar,
  Cell,
  Line,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  LabelList,
  ResponsiveContainer,
} from "recharts";
import React, { useMemo } from "react";

// ---------- 1. 抽取标题 ----------

// // ---------- 2. 整理年度数据 ----------
// const years = Array.from({ length: 11 }, (_, i) => 2014 + i);
// const data = years
//   .map((y) => {
//     const v = raw[`kijun${y}` as keyof typeof raw] ?? raw[`kouji${y}` as keyof typeof raw];
//     return v ? { year: String(y), price: v / 10000 } : null; // 万円/㎡
//   })
//   .filter((d) => d !== null) as { year: string; price: number }[];

// // ---------- 3. 动态计算 Y 轴 ----------
// const prices = data.map((d) => d.price);
// const minP = Math.min(...prices);
// const maxP = Math.max(...prices);
// const minY = Math.floor(minP * 0.99);
// const roughStep = (maxP - minY) / 6;
// const step = roughStep >= 1 ? 1 : 0.5;
// const ticks: number[] = [];
// for (let v = minY; v <= maxP + step; v += step) ticks.push(Number(v.toFixed(1)));

// // ---------- 4. 首尾两点及涨幅 ----------
// const firstYear = data[0].year;
// const lastYear = data[data.length - 1].year;
// const firstPrice = data[0].price;
// const lastPrice = data[data.length - 1].price;
// const deltaPct = ((lastPrice - firstPrice) / firstPrice) * 100;

// // 仅首尾两点，用于绘制"首尾趋势线"
// const trendData = [
//   { year: firstYear, price: firstPrice },
//   { year: lastYear, price: lastPrice },
// ];



// ---------- 3. 主组件 ----------
export default function BenchmarkChart({ nearestRecord }: { nearestRecord: any }) {
  // ---------- 1. 数据处理工具 ----------
  function extractYearlyData(raw: any) {
    // 优先用 kijun，没有就用 kouji

    if (!raw) return { data: [], type: "" };
    const years = Array.from({ length: 13 }, (_, i) => 2012 + i);
    let type = "";
    let foundType = false;

    let data = years
      .map((y) => {
        let v = raw[`kijun${y}`];
        if (v != null) {
          if (!foundType) { type = "基準地価"; foundType = true; }
          return { year: String(y), price: v / 10000 };
        }
        v = raw[`kouji${y}`];
        if (v != null) {
          if (!foundType) { type = "公示地価"; foundType = true; }
          return { year: String(y), price: v / 10000 };
        }
        return null;
      })
      .filter((d) => d !== null) as { year: string; price: number }[];
    return { data, type };
  }

  const { data, type } = useMemo(() => extractYearlyData(nearestRecord), [nearestRecord]);
  // 限制最多3个点，优先取最新的3个
  const limitedData = data.slice(-3);
  if (!limitedData.length) return <div>データなし</div>;

  // Y 轴
  const prices = limitedData.map((d) => d.price);
  const minP = Math.min(...prices);
  const maxP = Math.max(...prices);
  const minY = Math.floor(minP * 0.95);
  const roughStep = (maxP - minY) / 6;
  const step = roughStep >= 1 ? 1 : 0.5;
  const ticks: number[] = [];
  for (let v = minY; v <= maxP + step; v += step) ticks.push(Number(v.toFixed(1)));

  // 首尾
  const firstYear = limitedData[0]?.year || 2012;
  const lastYear = limitedData[limitedData.length - 1]?.year || 2024;
  const firstPrice = limitedData[0]?.price || 0;
  const lastPrice = limitedData[limitedData.length - 1]?.price || 0;
  const deltaPct = ((lastPrice - firstPrice) / firstPrice) * 100;
  const deltaAbs = lastPrice - firstPrice;
  const label = `+${deltaAbs.toFixed(1)}万 (+${deltaPct.toFixed(1)}%)`;

  // 标题
  const title = (() => {
    const m = nearestRecord?.addressRegistered?.match(/([^\s]*[^丁目]+丁目)/);
    return m ? m[1] : "対象地";
  })();

  return (
    <div className="w-full mt-2 border border-neutral-200 rounded-md p-2" style={{ position: "relative" }}>
      {/* 标题与副标题 */}
      <div className="flex flex-col items-center justify-center">
        <h2 className="text-xl font-bold text-center">{title}</h2>
        <div className="text-base text-neutral-500">近隣{type}推移（万円/㎡）</div>
      </div>

      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={limitedData} margin={{ top: 30, right: 30, left: 10, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="year" />
          <YAxis
            domain={[minY, maxP]}
            ticks={ticks}
            tickFormatter={(v) => `${v}万円`}
            width={80}
          />
          <Tooltip
            formatter={(v: number) => `${v.toFixed(1)} 万円/㎡`}
            labelFormatter={(l) => `${l} 年`}
          />
          <Bar dataKey="price" barSize={32}>
            {limitedData.map((d) => (
              <Cell
                key={d.year}
                fill={d.year === firstYear || d.year === lastYear ? "#4A6CF7" : "#C0C0C0"}
              />
            ))}
            <LabelList
              dataKey="price"
              position="top"
              formatter={(v: number) => `${v.toFixed(1)}万`}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>

      <div className="text-sm text-center">
        {firstYear} → {lastYear} ({(lastYear as number) - (firstYear as number)}年間) 累计
        <br />
        <span className={deltaPct > 0 ? "text-2xl font-bold text-green-500" : "text-lg font-bold text-red-500"}>{deltaPct.toFixed(1)}%</span> {deltaPct > 0 ? "上昇" : "下落"}
      </div>
    </div>
  );
}