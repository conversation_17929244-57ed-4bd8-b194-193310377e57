"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { Aperture, X } from "lucide-react";
import { getMaterialsFolderContentsInFolder } from "@/actions/helper/supabaseNode";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { DialogContent, Dialog, DialogClose, DialogTitle, DialogHeader } from "@/components/ui/dialog";
import { DialogTrigger } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

export default function PropertyInfoPhotoPublicCollage({ selectedProperty, currentUser }: {
  selectedProperty: UserLambdaRecordProps,
  currentUser: TllUserProps
}) {
  const [fileLinks, setFileLinks] = useState<any[]>([]);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const getFileLinks = async () => {
    const res = await getMaterialsFolderContentsInFolder({
      folderName: selectedProperty?.id as string,
      bucketKey: "propertyMaterialsPublic",
    });

    if (res.success) {
      setFileLinks(res.data);
    }
  };

  useEffect(() => {
    if (selectedProperty?.id) {
      getFileLinks();
    }
  }, [selectedProperty?.id]);

  // 根据图片数量计算网格布局
  const getGridClass = (count: number) => {
    switch (count) {
      case 1:
        return "grid-cols-1";
      case 2:
        return "grid-cols-2";
      case 3:
        return "grid-cols-3";
      case 4:
        return "grid-cols-2 grid-rows-2";
      default:
        return "grid-cols-3 grid-rows-3";
    }
  };

  // 根据图片数量计算图片容器高度
  const getImageContainerHeight = (count: number) => {
    switch (count) {
      case 1:
        return "h-[400px]";
      case 2:
        return "h-[300px]";
      case 3:
      case 4:
        return "h-[250px]";
      default:
        return "h-[200px]";
    }
  };

  return <div className="min-h-[100px] flex flex-col">
    {fileLinks.length > 0 ? (
      <div className={`grid ${getGridClass(fileLinks.length)} gap-4 p-4`}>
        {fileLinks.slice(0, 9).map((record: any, idx: number) => (
          <div key={idx} className={`relative ${getImageContainerHeight(fileLinks.length)}`}>
            <Dialog>
              <DialogTrigger asChild>
                <div className="w-full h-full relative cursor-zoom-in hover:opacity-90 transition-opacity">
                  <Image
                    src={record.link}
                    alt={`Property Image ${idx + 1}`}
                    fill
                    className="object-cover rounded-lg"
                    onClick={() => setPreviewImage(record.link)}
                  />
                </div>
              </DialogTrigger>

              <DialogContent className="max-w-5xl w-full p-0 bg-neutral-900 border-none shadow-none flex justify-center items-center">
                <VisuallyHidden>
                  <DialogHeader className="border-b bg-neutral-100 flex flex-row items-center justify-between">
                    <DialogTitle className="flex-1">写真プレビュー</DialogTitle>
                  </DialogHeader>
                </VisuallyHidden>

                <div className="relative w-full h-[90vh] flex justify-center items-center bg-black">
                  <Image
                    src={record.link}
                    alt="Full preview"
                    fill
                    className="object-contain rounded-md"
                  />
                  <DialogClose className="absolute top-4 right-4 p-2 bg-white/80 hover:bg-white text-black rounded-full shadow-md transition z-20">
                    <X className="h-6 w-6" />
                  </DialogClose>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        ))}
      </div>
    ) : (
      <div className="h-[360px] flex flex-col items-center justify-center bg-gray-50 gap-4 border-neutral-200 m-2">
        <p className="text-4xl text-neutral-300">画像準備中</p>
        <Aperture className="w-12 h-12 text-neutral-300" />
      </div>
    )}
  </div>
}