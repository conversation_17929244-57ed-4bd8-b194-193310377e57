"use client";

import { Badge } from "@/components/ui/badge";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

let rawData = [
  {
    "区": "千代田区",
    "常駐義務": "あり",
    "フロント設置義務": "あり",
    "駆けつけ要件": "常駐",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "家主滞在型or常駐で文教地区等・学校等周辺以外の地域は制限なし\nその他地域は金土のみor営業不可"
  },
  {
    "区": "中央区",
    "常駐義務": "あり",
    "フロント設置義務": "あり",
    "駆けつけ要件": "常駐",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "土日のみ営業可"
  },
  {
    "区": "港区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "キーボックス不可",
    "民泊新法 - 上乗せ条例の概要": "家主居住型は制限なし\n住居専用地域・文教地区の家主不在型は営業期間に制限あり"
  },
  {
    "区": "新宿区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "キーボックス不可",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域は金土日のみ営業可\n共同住宅は住民と施設の動線を分ける必要あり"
  },
  {
    "区": "文京区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "徒歩10分以内",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域・文教地区は金土日のみ営業可\n届出の15日前までに近隣住民に通知が必要"
  },
  {
    "区": "台東区",
    "常駐義務": "あり",
    "フロント設置義務": "なし",
    "駆けつけ要件": "常駐",
    "鍵渡し": "キーボックス不可",
    "民泊新法 - 上乗せ条例の概要": "家主滞在型or常駐は制限なし\nその他は土日祝・年末年始のみ営業可"
  },
  {
    "区": "墨田区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "徒歩10分以内",
    "鍵渡し": "キーボックス不可",
    "民泊新法 - 上乗せ条例の概要": "なし"
  },
  {
    "区": "江東区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "徒歩10分以内",
    "鍵渡し": "キーボックス不可",
    "民泊新法 - 上乗せ条例の概要": "土日のみ営業可"
  },
  {
    "区": "品川区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "なし",
    "民泊新法 - 上乗せ条例の概要": "商業地域・近隣商業地域（文教地区を除く）は制限なし\nその他は土日のみ営業可"
  },
  {
    "区": "目黒区",
    "常駐義務": "なし",
    "フロント設置義務": "あり（施設外可）",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "金土のみ営業可\n届出の15日前までに近隣住民に通知が必要"
  },
  {
    "区": "大田区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "なし",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域、工業地域は不可"
  },
  {
    "区": "世田谷区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "キーボックス不可",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域は土日祝のみ営業可"
  },
  {
    "区": "渋谷区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "キーボックス不可",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域・文教地区は営業期間に制限あり"
  },
  {
    "区": "中野区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "キーボックス不可",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域は金土日祝のみ営業可"
  },
  {
    "区": "杉並区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "徒歩10分以内",
    "鍵渡し": "なし",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域での家主不在型は金土日祝、祝前日のみ営業可"
  },
  {
    "区": "豊島区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "10分以内",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "なし"
  },
  {
    "区": "北区",
    "常駐義務": "あり",
    "フロント設置義務": "あり",
    "駆けつけ要件": "常駐",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "なし"
  },
  {
    "区": "荒川区",
    "常駐義務": "あり",
    "フロント設置義務": "なし",
    "駆けつけ要件": "常駐",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "土日のみ営業可"
  },
  {
    "区": "板橋区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "徒歩10分以内",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域は金土日祝、祝前日のみ営業可（家主居住型は規制対象外の場合あり）"
  },
  {
    "区": "練馬区",
    "常駐義務": "あり",
    "フロント設置義務": "なし",
    "駆けつけ要件": "常駐",
    "鍵渡し": "直接対面",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域は金土日祝、祝前日のみ営業可\n届出の15日前までに近隣住民に通知が必要"
  },
  {
    "区": "足立区",
    "常駐義務": "あり",
    "フロント設置義務": "なし",
    "駆けつけ要件": "徒歩10分以内",
    "鍵渡し": "なし",
    "民泊新法 - 上乗せ条例の概要": "住居専用地域は金土日祝のみ営業可（年末年始を除く）"
  },
  {
    "区": "葛飾区",
    "常駐義務": "なし",
    "フロント設置義務": "なし",
    "駆けつけ要件": "徒歩10分以内",
    "鍵渡し": "なし",
    "民泊新法 - 上乗せ条例の概要": "なし"
  },
  {
    "区": "江戸川区",
    "常駐義務": "あり",
    "フロント設置義務": "なし",
    "駆けつけ要件": "常駐",
    "鍵渡し": "なし",
    "民泊新法 - 上乗せ条例の概要": "なし"
  }
];


const getSummaryForHotel = (item: any) => {
  return item.常駐義務 === "なし" ? "OK" : "NG";
}

export default function PropertyInfoLandHotelCheck({ selectedProperty }: { selectedProperty: UserLambdaRecordProps }) {
  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row items-center justify-between p-2 border-b border-neutral-200">
        <div className="text-2xl font-bold"> 23区の民泊・ホテル制限 </div>
      </div>

      <table className="min-w-full border-collapse border border-neutral-200">
        <thead>
          <tr>
            <th className="border border-neutral-200 p-2"></th>
            <th className="border border-neutral-200 p-2 bg-neutral-100" colSpan={4}>ホテル</th>
            <th className="border border-neutral-200 p-2 bg-neutral-100" colSpan={2}>民泊</th>
          </tr>
          <tr>
            <th className="border border-neutral-200 p-2">区</th>
            <th className="border border-neutral-200 p-2">常駐義務</th>
            <th className="border border-neutral-200 p-2">フロント設置義務</th>
            <th className="border border-neutral-200 p-2">駆けつけ要件</th>
            <th className="border border-neutral-200 p-2">鍵渡し</th>
            <th className="border border-neutral-200 p-2">上乗せ条例の概要</th>
          </tr>
        </thead>
        <tbody>
          {rawData.map((item, index) => (
            <tr key={index}>
              <td className={`border border-neutral-200 p-2`}>
                {selectedProperty?.address?.includes(item.区) ? <div className="text-red-500 flex flex-col items-center justify-start font-bold">
                  <Badge variant="outline" className="ml-2">  本物件</Badge>
                  {item.区}
                </div> : item.区}
              </td>
              <td className={`border border-neutral-200 p-2 ${item.常駐義務 === "なし" ? "bg-green-100" : ""}`}>{item.常駐義務}</td>
              <td className={`border border-neutral-200 p-2 ${item.フロント設置義務 === "なし" ? "bg-green-100" : ""}`}>{item.フロント設置義務}</td>
              <td className={`border border-neutral-200 p-2 ${item.駆けつけ要件 === "なし" ? "bg-green-100" : ""}`}>{item.駆けつけ要件}</td>
              <td className={`border border-neutral-200 p-2 ${item.鍵渡し === "なし" ? "bg-green-100" : ""}`}>{item.鍵渡し}</td>
              <td className={`border border-neutral-200 p-2 ${item["民泊新法 - 上乗せ条例の概要"] === "なし" ? "bg-green-100" : ""}`}>{item["民泊新法 - 上乗せ条例の概要"]}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
