import { Badge } from "@/components/ui/badge";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { useAuthStore } from "@/store/auth";
import Link from "next/link";
import { useState } from "react";
import LeafletMap from "@/components/LeafletMap";
import { ExternalLink, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";

export default function PropertyInfoCommon({ selectedProperty }: { selectedProperty: UserLambdaRecordProps }) {
  const { currentUser } = useAuthStore();
  const [stationUserTrend, setStationUserTrend] = useState<string>('-');

  const getStationUserTrend = () => {
    let tillYear = null;

    if (selectedProperty?.nearestStationGroup == null) return '-';

    let data = selectedProperty?.nearestStationGroup;

    let recentData;
    if (data['stationUserCombined2020'] > 0) {
      tillYear = 2020;
      recentData = data['stationUserCombined2020'];
    } else if (data['stationUserCombined2018'] > 0) {
      tillYear = 2018;
      recentData = data['stationUserCombined2018'];
    } else {
      return '-';
    }

    const start = data['stationUserCombined2011'];
    const end = data['stationUserCombined2018'];

    if (start == 0 || end == 0) {
      return '-';
    }

    let stationUserChange = ((Math.pow(end / start, 1 / (2018 - 2011)) - 1) * 100);

    return (
      <div className="flex flex-col gap-2 items-start justify-start">
        <div className="flex flex-row gap-2 items-center justify-start">
          <strong>駅利用者:</strong>
          {recentData}({tillYear}年)
        </div>

        <span className="text-sm text-neutral-500">
          駅利用者推移:
          <span
            style={{
              color: stationUserChange > 0 ? 'green' : 'red',
              marginLeft: 5,
            }}
          >
            {stationUserChange > 0 ? '▲' : '▼'}
            {stationUserChange.toFixed(2)}%
          </span>
          (5年平均)
        </span>
      </div>
    );
  };

  const getTransportation = () => {
    let res = `${selectedProperty?.nearestStation}`;
    let res1 = `徒歩${selectedProperty?.nearestStationWalkMinute || '-'}分`;

    let renderTag = false;

    if ((Number.isInteger(selectedProperty?.nearestStationWalkMinute) &&
      selectedProperty?.nearestStationWalkMinute !== undefined &&
      selectedProperty?.nearestStationWalkMinute !== null &&
      selectedProperty?.nearestStationWalkMinute > 15) ||
      selectedProperty?.nearestStationWalkMinute === 999) {
      renderTag = true;
    }

    if (!Number.isInteger(selectedProperty?.nearestStationWalkMinute) &&
      selectedProperty?.nearestStationWalkMinute !== null &&
      selectedProperty?.nearestStationWalkMinute !== undefined &&
      selectedProperty?.nearestStationWalkMinute > 15) {
      renderTag = true;
    }

    return <div className="flex flex-row gap-2 items-center justify-start">
      {selectedProperty?.nearestStationGroupId ?
        <Link href={`/an/station/${selectedProperty?.nearestStationGroupId}`} className="underline flex flex-row gap-2 items-center justify-start" target="_blank" onClick={() => {
          sendLark({
            message: `[👀][駅詳細分析][${currentUser?.name}]${selectedProperty?.nearestStation} from property detailed page`,
            url: LARK_URLS.USER_ACTIVITY_CHANNEL,
          })
        }}>
          {res}{res1}
          <ExternalLink className="h-4 w-4 text-neutral-500" />
        </Link> :
        selectedProperty?.transport}
      {renderTag && <Badge
        variant={'destructive'}
        style={{
          fontWeight: 'bold',
          marginLeft: 10,
        }}
      >
        駅遠
      </Badge>}
    </div>;
  };


  return (
    <div className="flex flex-col bg-neutral-50 border border-neutral-200 rounded-md m-2">
      <div className="font-bold text-neutral-500 border-b mb-2 p-2 border-neutral-200 sm:border-t-0">
        基本情報
      </div>

      <div className={`grid grid-cols-1 sm:grid-cols-2 gap-2`}>
        <div className="grid grid-cols-1 p-2 gap-1">
          <div className="flex flex-row items-center justify-start">
            <strong>種類:</strong>
            <div className="ml-2">
              {selectedProperty?.recordType === "MANSION" && "区分マンション"}
              {selectedProperty?.recordType === "BUILDING" && "一棟収益物件"}
              {selectedProperty?.recordType === "HOUSE" && "一戸建て"}
              {selectedProperty?.recordType === "LAND" && "土地"}
            </div>
            <div className="ml-2 text-sm text-neutral-500">
              {selectedProperty?.recordSubType && ` ${selectedProperty?.recordSubType}`}
            </div>
          </div>
          <div className="flex flex-row gap-2 items-center justify-start">
            <strong>所在地:</strong>
            {selectedProperty?.address}
          </div>
          <div className="flex flex-row gap-2 items-center justify-start">
            <strong>郵便番号:</strong>
            <Link href={`/an/area/${selectedProperty?.postalCode}`} className="underline flex flex-row gap-2 items-center justify-start" target="_blank" onClick={() => {
              sendLark({
                message: `[👀][地域詳細分析][${currentUser?.name}]${selectedProperty?.postalCode} from property detailed page`,
                url: LARK_URLS.USER_ACTIVITY_CHANNEL,
              })
            }}> {selectedProperty?.postalCode}
              <ExternalLink className="h-4 w-4 text-neutral-500" />
            </Link>
          </div>
          <div>
            <strong>建物名:</strong>
            {selectedProperty?.buildingId ?
              <Link href={`/an/mansion/${selectedProperty?.buildingId}`} className="underline" target="_blank" onClick={() => {
                sendLark({
                  message: `[👀][建築物詳細分析][${currentUser?.name}]${selectedProperty?.buildingName} from property detailed page`,
                  url: LARK_URLS.USER_ACTIVITY_CHANNEL,
                })
              }}>
                {selectedProperty?.buildingName}
              </Link>
              :
              <span className="ml-2">
                {selectedProperty?.buildingName}
              </span>
            }
          </div>
          <div className="flex flex-row gap-2 items-center justify-start">
            <strong>アクセス:</strong> {getTransportation()}
          </div>
          <div className="flex flex-row gap-2 items-center justify-start">
            {getStationUserTrend()}
          </div>
          <div className="flex flex-row gap-2 items-center justify-start">
            <p className="font-bold">想定積算:
            </p>
            {selectedProperty?.propertyAnalysisResult?.sekisanValuation?.sekisanPrice && selectedProperty?.propertyAnalysisResult?.sekisanValuation?.sekisanPrice > 0 && selectedProperty?.propertyAnalysisResult?.sekisanValuation?.sekisanPrice.toFixed(0)}万円
            <p className="text-sm text-neutral-500">割合: {selectedProperty?.propertyAnalysisResult?.sekisanValuation?.sekisanTotalPercentage?.toFixed(0)}%</p>
          </div>

          {currentUser && currentUser.accessLevel >= 90 && <div className="">
            <strong>コメント:</strong>
            <pre className="text-xs bg-gray-100 rounded-md whitespace-pre-wrap break-all overflow-hidden min-h-[100px] p-2">
              {selectedProperty?.salesComments}
            </pre>
          </div>}
        </div>

        <div className="border border-transparent rounded-md m-2">
          {selectedProperty?.latitude && selectedProperty?.longitude && <LeafletMap center={[selectedProperty?.latitude, selectedProperty?.longitude]} zoom={14} />}
        </div>
      </div>
    </div >
  );
}
