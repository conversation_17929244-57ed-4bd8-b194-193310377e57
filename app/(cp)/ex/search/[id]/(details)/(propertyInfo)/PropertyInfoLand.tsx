"use client";


import { trEvaluationKijunChangesInP5YAction } from "@/actions/trEvaluationCombinedRecords";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Sheet, SheetDescription, SheetTitle, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Crown, ExternalLink, EyeIcon, Link2Icon } from "lucide-react";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { useEffect, useState } from "react";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import PropertyInfoLandHotelCheck from "./PropertyInfoLandHotelCheck";
import PropertyInfoLandRosenkaChangeChart from "./PropertyInfoLandRosenkaChangeChart";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  <PERSON>er<PERSON>it<PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer"
import { type } from "os";

export default function PropertyInfoLand({ selectedProperty }: { selectedProperty: UserLambdaRecordProps }) {
  const [kijunChangesInP5Y, setKijunChangesInP5Y] = useState<any>(null);
  const [hotelCheckOpen, setHotelCheckOpen] = useState<boolean>(false);
  const [nearestRecord, setNearestRecord] = useState<any>(null);

  useEffect(() => {
    if (selectedProperty?.nearestStation && selectedProperty?.longitude && selectedProperty?.latitude) {
      trEvaluationKijunChangesInP5YAction({
        stationName: selectedProperty?.nearestStation,
        longitude: selectedProperty?.longitude,
        latitude: selectedProperty?.latitude,
      }).then((res) => {
        if (res.success) {
          setKijunChangesInP5Y(res.data);

          if (res.data.records) {
            setNearestRecord(res.data.records.sort((a: any, b: any) => a.distance - b.distance)[0]);
          }
        }
      });
    }
  }, [selectedProperty]);


  const getGuessedActualCoverageRatio = () => {
    if (selectedProperty?.buildingSize && selectedProperty?.buildingLevel && parseInt(selectedProperty?.buildingLevel) > 0 && selectedProperty?.landSize) {
      return (selectedProperty?.buildingSize / parseInt(selectedProperty?.buildingLevel) / selectedProperty?.landSize) * 100 || 0
    }
    return 0
  }

  const getGuessedActualVolumeRatio = () => {
    if (selectedProperty?.buildingSize && selectedProperty?.landSize) {
      return (selectedProperty?.buildingSize / selectedProperty?.landSize) * 100
    }

    return 0
  }

  return (
    <div className="flex flex-col border border-neutral-200 m-2 rounded-md bg-neutral-50">
      <div className="font-bold text-neutral-500 border-b border-neutral-200 mb-2 p-2">土地情報</div>

      <div className="grid grid-cols-1 p-2 gap-1">
        <p><strong>土地面積:</strong>
          {selectedProperty?.landSize}㎡
          <span className="text-sm text-neutral-500 ml-2">
            (約{selectedProperty?.landSize !== undefined && selectedProperty?.landSize !== null && selectedProperty?.landSize > 0 ? (selectedProperty?.landSize / 3.3).toFixed(1) : "-"}坪)
          </span>
        </p>
        <span className="text-sm text-neutral-500">土地不算入面積: - </span>
        <p><strong>土地権利:</strong>
          {selectedProperty?.landRight !== "所有権" ? <span className="text-red-500">{selectedProperty?.landRight}</span> : selectedProperty?.landRight || "-"}
        </p>
        <div className="flex flex-col items-start justify-start">
          <div className="flex flex-row items-center justify-start">
            <strong>用途地域:</strong>
            {selectedProperty?.landType}
          </div>
          <div className="text-sm flex flex-row items-center justify-start text-neutral-500">
            旅館用地:
            <span className="text-sm text-neutral-500 ml-2">
              {selectedProperty?.landType ? selectedProperty?.landType.length > 0 && ["一低", "一中", "二低", "二中"].includes(selectedProperty?.landType) ?
                <span className="text-red-500">NG</span> : <span className="text-green-500">OK</span>
                : "-"
              }
            </span>

            <Sheet open={hotelCheckOpen} onOpenChange={setHotelCheckOpen}>
              <SheetTrigger asChild>
                <div className="text-sm text-neutral-500 ml-2 underline cursor-pointer">
                  民泊・旅館制限について
                </div>
              </SheetTrigger>
              <SheetContent side="bottom" className="h-[90vh] overflow-y-auto pointer-events-auto">
                <VisuallyHidden>
                  <SheetTitle>検討履歴</SheetTitle>
                  <SheetDescription>
                    検討履歴を表示します。
                  </SheetDescription>
                </VisuallyHidden>
                {/* <SheetHeader> */}
                {/* <SheetTitle>Are you absolutely sure?</SheetTitle> */}
                {/* <SheetDescription>
                        This action cannot be undone. This will permanently delete your account
                        and remove your data from our servers.
                      </SheetDescription> */}
                {/* </SheetHeader> */}
                <PropertyInfoLandHotelCheck selectedProperty={selectedProperty} />
              </SheetContent>
            </Sheet>
          </div>
        </div>

        <div className="flex flex-row items-center justify-start">
          <strong>建蔽率:</strong>
          {selectedProperty?.landBuildingCoverageRatio || "-"}%
          <span className="text-sm text-neutral-500 ml-2">
            <strong>実質:</strong>
            {getGuessedActualCoverageRatio() > 0 ? getGuessedActualCoverageRatio()?.toFixed(2) : "-"}%
          </span>

          {selectedProperty?.landBuildingCoverageRatio && getGuessedActualCoverageRatio() !== null && getGuessedActualCoverageRatio() && selectedProperty?.landBuildingCoverageRatio !== null && selectedProperty?.landBuildingCoverageRatio !== undefined && getGuessedActualCoverageRatio() && getGuessedActualCoverageRatio() > selectedProperty?.landBuildingCoverageRatio ? <span className="text-sm text-neutral-500 ml-2">
            <Badge variant={'destructive'}>建蔽OVER</Badge>
          </span> : ""}

          {selectedProperty?.landBuildingCoverageRatio && getGuessedActualCoverageRatio() !== null && getGuessedActualCoverageRatio() && selectedProperty?.landBuildingCoverageRatio !== null && selectedProperty?.landBuildingCoverageRatio !== undefined && getGuessedActualCoverageRatio() && getGuessedActualCoverageRatio() < selectedProperty?.landBuildingCoverageRatio * 0.75 ? <span className="text-sm text-neutral-500 ml-2">
            <Badge variant="outline" className="bg-green-500 text-white">建蔽未消化</Badge>
          </span> : ""}
        </div>

        <div className="flex flex-row items-center justify-start">
          <strong>容積率:</strong> {selectedProperty?.landFloorAreaRatio || "-"}%
          <span className="text-sm text-neutral-500 ml-2"><strong>実質:</strong>
            {getGuessedActualVolumeRatio() > 0 ? getGuessedActualVolumeRatio()?.toFixed(2) : "-"}%
          </span>

          {selectedProperty?.landFloorAreaRatio && getGuessedActualVolumeRatio() !== null && getGuessedActualVolumeRatio() && selectedProperty?.landFloorAreaRatio !== null && selectedProperty?.landFloorAreaRatio !== undefined && getGuessedActualVolumeRatio() && getGuessedActualVolumeRatio() > selectedProperty?.landFloorAreaRatio ? <span className="text-sm text-neutral-500 ml-2">
            <Badge variant={'destructive'}>容積OVER</Badge>
          </span> : ""}

          {selectedProperty?.landFloorAreaRatio && getGuessedActualVolumeRatio() !== null && getGuessedActualVolumeRatio() && selectedProperty?.landFloorAreaRatio !== null && selectedProperty?.landFloorAreaRatio !== undefined && getGuessedActualVolumeRatio() && getGuessedActualVolumeRatio() < selectedProperty?.landFloorAreaRatio * 0.75 ? <span className="text-sm text-neutral-500 ml-2">
            <Badge variant={'outline'} className="bg-green-500 text-white">容積未消化</Badge>
          </span> : ""}
        </div>

        <p><strong>接道:</strong>
          {selectedProperty?.roadConnection}
          <span className="text-sm text-neutral-500 ml-2">
            {selectedProperty?.roadConnectionFirstType} {selectedProperty?.roadConnectionFirstFacing} {selectedProperty?.roadConnectionFirstWidth} {selectedProperty?.roadConnectionSecondType} {selectedProperty?.roadConnectionSecondFacing} {selectedProperty?.roadConnectionSecondWidth}
          </span>
        </p>
        <div className="flex flex-col items-start justify-start">
          <div className="flex flex-row items-center justify-start gap-2">
            <strong>相続路線価(推測):</strong>

            <span className="underline cursor-pointer flex flex-row items-center justify-start gap-1">
              {selectedProperty?.valueRosenka}万円/平米

              <ExternalLink onClick={() => {
                window.open(
                  `https://www.chikamap.jp/chikamap/Map?mid=321&mpx=${selectedProperty?.longitude}&mpy=${selectedProperty?.latitude}&bsw=2151&bsh=1223`,
                  '_blank',
                  'width=2000,height=1000',
                );
              }} className="w-4 h-4 text-gray-500" />
            </span>
          </div>

          <div className="flex flex-row items-center justify-start text-sm text-neutral-500 gap-2">
            <span className="">
              5Y路線価変更:
              {kijunChangesInP5Y && kijunChangesInP5Y?.mean ? <span className={kijunChangesInP5Y?.mean > 0 ? "text-green-500" : "text-red-500"}>{kijunChangesInP5Y?.mean + "%"}</span> : "-"}
            </span>

            <Separator orientation="vertical" className="h-4" />

            <strong>平均:</strong>
            {kijunChangesInP5Y && kijunChangesInP5Y?.yearly ? <span className={kijunChangesInP5Y?.yearly > 0 ? "text-green-500" : "text-red-500"}>{kijunChangesInP5Y?.yearly + "%"}</span> : "-"}

            <Drawer>
              <DrawerTrigger>
                <EyeIcon className="w-4 h-4 text-gray-500" />
              </DrawerTrigger>
              <DrawerContent >
                <DrawerHeader>
                  <DrawerTitle>近隣地価推移</DrawerTitle>
                </DrawerHeader>
                <div className="mb-8">
                  <PropertyInfoLandRosenkaChangeChart nearestRecord={nearestRecord} />
                </div>
              </DrawerContent>
            </Drawer>
          </div>
        </div>
      </div>
    </div >
  );
}