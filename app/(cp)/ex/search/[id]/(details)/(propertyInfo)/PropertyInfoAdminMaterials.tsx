"use client";

import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { getMaterialsFolderContentsInFolder } from "@/actions/helper/supabaseNode";
import { useEffect } from "react";
import Link from "next/link";
import { useState } from "react";
import dayjs from "dayjs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Link2 } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function PropertyInfoAdminMaterials({ currentUserLambdaRecord }: { currentUserLambdaRecord: UserLambdaRecordProps }) {
  const [fileLinks, setFileLinks] = useState<any[]>([]);

  const getFileLinks = async () => {
    const res = await getMaterialsFolderContentsInFolder({ folderName: currentUserLambdaRecord?.id as string, bucketKey: "propertyMaterials" });
    if (res.success) {
      setFileLinks(res.data);
    }
  }

  useEffect(() => {
    if (currentUserLambdaRecord?.id) {
      getFileLinks();
    }
  }, [currentUserLambdaRecord?.id]);

  return (
    <div className="bg-neutral-100 border-t border-neutral-200 mt-2">
      <div className="flex items-center justify-center gap-2 p-2">
        <span className="text-xs text-neutral-500">[TLL管理者用, 物件資料]</span>
      </div>

      <div className="p-2 flex flex-col gap-2">
        <div className="text-sm text-neutral-900">
          {fileLinks.length}件の書類
          <div className="max-h-[240px] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>No</TableHead>
                  <TableHead>名前</TableHead>
                  <TableHead>更新日時</TableHead>
                  <TableHead>サイズ</TableHead>
                  <TableHead>種類</TableHead>
                  <TableHead>変更人</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {fileLinks.map((file, index) => (
                  <TableRow key={file.link}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{file.originalName ? file.originalName.slice(0, 20) : file.name.slice(0, 20)}</TableCell>
                    <TableCell>{dayjs(file.updatedAt).format("YYYY/MM/DD HH:mm")}</TableCell>
                    <TableCell>{((file.size || 0) / 1024 / 1024).toFixed(2)} MB</TableCell>
                    <TableCell>{file.type.indexOf("/") > -1 ? file.type.split("/")[1] : file.type}</TableCell>

                    <TableCell>{file.tllUser?.name || "-"}</TableCell>

                    <TableCell className="flex flex-row gap-2 justify-center">
                      {/* <Drawer>
                            <DrawerTrigger asChild>
                              <Button size="sm" variant="outline" >
                                <Eye className="w-4 h-4" />
                              </Button>
                            </DrawerTrigger>
                            <DrawerContent className="h-[calc(70vh-100px)] mb-10 w-full">
                              <DrawerHeader>
                                <DrawerTitle>{file.originalName}</DrawerTitle>
                                <DrawerDescription>{dayjs(file.updatedAt).format("YYYY/MM/DD HH:mm")}</DrawerDescription>
                              </DrawerHeader>

                              <div className="w-full h-full">
                                <IframeImage url={file.link as string} showToolbar={true} />
                              </div>
                            </DrawerContent>
                          </Drawer> */}

                      <Link href={file.link} target="_blank">
                        <Button size="sm" variant="outline">
                          <Link2 className="w-4 h-4" />
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
}