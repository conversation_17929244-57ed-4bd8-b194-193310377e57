import dayjs from "dayjs";

export const rentUpsideColumns = [
  {
    header: '更新日',
    accessorKey: 'updatedAt',
    cell: ({ row }: { row: any }) => {
      return dayjs(row.original.updatedAt).format('YYYY/MM/DD');
    },
  },
  {
    header: '物件タイプ',
    accessorKey: 'recordSubType',
  },
  {
    header: '物件情報',
    accessorKey: 'address',
  },
  {
    header: '築年数',
    accessorKey: 'buildingBuiltYear',
  },
  // {
  //   header: '仲介情報',
  //   accessorKey: 'brokerType',
  // },
  {
    header: '月賃料',
    accessorKey: 'yearlyIncome',
    cell: ({ row }: { row: any }) => {
      return row.original.yearlyIncome ? parseFloat((row.original.yearlyIncome / 12).toFixed(2)).toLocaleString() + "万円" : "-";
    },
  },
  // {
  //   header: 'ROI',
  //   cell: ({ row }: { row: any }) => {
  //     if (row.original.yearlyIncome > 0) {
  //       const income = row.original.yearlyIncome; // 确保类型为number
  //       const price = row.original.price; // 确保类型为number
  //       return income > 0 ? (income / price * 100).toFixed(2) : ''; // 使用shadui的Badge组件
  //     } else {
  //       return "-";
  //     }
  //   },
  // },
  {
    header: '賃貸面積',
    accessorKey: 'buildingSize',
    cell: ({ row }: { row: any }) => {
      return row.original.buildingSize ? row.original.buildingSize.toLocaleString() + "㎡" : "-";
    },
  },
  {
    header: '坪単価',
    cell: ({ row }: { row: any }) => {
      if (row.original.yearlyIncome > 0) {
        return (row.original.yearlyIncome / 12 / row.original.buildingSize * 3.3).toFixed(2) + "万円";
      } else {
        return "-";
      }
    },
  }
]