"use client";

import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";

import { Loader2 } from "lucide-react";
import { uploadFileToPropertyMaterialsBucket } from "@/actions/helper/supabase";
import { sendLark } from "@/lib/thirdParty/lark";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { TllUserProps } from "@/lib/definitions/tllUser";

export default function FileDropzone({ currentUser, recordId, onUploaded }: { currentUser: TllUserProps, recordId: string, onUploaded: () => void }) {
  const [uploading, setUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Handle drop event
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      setUploading(true);
      setError(null); // Reset error state
      for (const file of acceptedFiles) {
        const isValidFile = file.type === 'application/pdf' || file.type.startsWith('image/');
        if (!isValidFile) {
          setError("🔥 サポートされていないファイル形式です。PDFまたは画像ファイルをアップロードしてください。");
          setUploading(false);
          return; // 不正なファイル时，停止上传
        }

        if (!currentUser?.id) {
          setError("🔥 ユーザーが見つかりません。");
          setUploading(false);
          return;
        }

        await uploadFileToPropertyMaterialsBucket(file, recordId, currentUser?.id);
        setUploadedFiles(prev => [...prev, file.name]);
        onUploaded();
      }
      sendLark({
        message: `[⚙️][物件データアップロード]${currentUser?.name}さんが${acceptedFiles.length}件の物件データをアップロードしました。物件ID is ${recordId} `,
        url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
      });
      setUploading(false);
    },
    [recordId]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop });

  return (
    <div
      {...getRootProps()}
      className="border-2 border-dashed p-6 text-center cursor-pointer rounded-lg bg-gray-100 hover:bg-gray-200 h-[200px] flex flex-col items-center justify-center w-full"
    >
      <input {...getInputProps()} />

      {isDragActive ? <div className="text-sm">📂 ファイルをここにドロップしてください...</div> : <div className="text-sm">ファイルをドラッグ＆ドロップするか、クリックしてファイルを選択してください</div>}

      <div className="flex flex-col items-center justify-center text-sm">
        {uploading && <div className="text-blue-600 mt-2 flex flex-row items-center gap-2">アップロード中...
          <Loader2 className="w-4 h-4 animate-spin" />
        </div>}
        {error && <div className="text-red-600 mt-2">{error}</div>}

        {uploadedFiles.length > 0 && (
          <div className="mt-4">
            <h3 className="font-semibold">アップロードされたファイル:</h3>
            <ul className="list-disc pl-5">
              {uploadedFiles.map((file, index) => (
                <li key={index}>{file}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}