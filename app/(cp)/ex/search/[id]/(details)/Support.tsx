"use client"

import { DivideCircle, Mail } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Phone, MessageCircle } from "lucide-react";
import Image from "next/image";
import SupportPopUp from "@/components/SupportPopUp";

export default function Support() {
  return (
    <section id="support" className="bg-white">
      <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
        購入・売却のご相談
      </div>

      <div className="p-4 flex flex-col gap-4">
        <div className="flex flex-col gap-4 bg-gray-100 p-4 rounded-md">
          <div className="text-lg font-bold">この物件についてもっと知りたいですか？</div>
          <Separator className="" />
          <div className="text-sm text-gray-500">
            ご希望の条件に合うかどうか、専門スタッフが個別にご相談を承ります。
          </div>

          <div className="flex flex-row gap-4 flex-nowrap overflow-x-auto">
            <Button variant="outline" className="w-full" onClick={() => {
              window.open("mailto:<EMAIL>", "_blank");
            }}>
              <Mail className="w-4 h-4" />
              メールで相談</Button>
            <Button variant="outline" className="w-full" onClick={() => {
              window.open("tel:+81345663208");
            }}>
              <Phone className="w-4 h-4" />
              {/* 03-4566-3208 */}
              電話する
            </Button>

            <SupportPopUp />
          </div>

          <Separator className="my-2" />
          <div className="text-sm text-gray-500">
            受付時間：平日10:00〜18:00（最短当日対応）
            <br />
            通常1営業日以内にご返信いたします。
          </div>
        </div>

        <div className="flex flex-col bg-gray-100 rounded-md">
          <div className="text-lg font-bold p-4">オンラインで無料個別相談</div>
          <Separator className="" />
          {/* <div className="text-sm text-gray-500 text-center">
            ✅ 初めて不動産購入を検討されている方
            ✅ 海外からの投資をお考えの方
            ✅ 売却をお急ぎの方
            ✅ 利回り・相場について相談したい方
          </div> */}
          <iframe
            src="https://cal.com/譚琛-tvz53r/30min?locale=ja"
            width="100%"
            height="650"
            style={{ border: '0' }}
            allowFullScreen
          ></iframe>

        </div>
      </div>
    </section >
  )
}
