import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { UserLambdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import Link from "next/link";
import { useState } from "react";
import IframeImage from "@/components/IframeImage";
import { Info, Pencil, Star, StarIcon } from "lucide-react";
import PropertyInfoCommon from "./(propertyInfo)/PropertyInfoCommon";
import PropertyInfoBuilding from "./(propertyInfo)/PropertyInfoBuilding";
import PropertyInfoLand from "./(propertyInfo)/PropertyInfoLand";
import PropertyInfo<PERSON>hirashi from "./(priceChange)/PriceChangeChirashi";
import PropertyInfoAdmin from "./(propertyInfo)/PropertyInfoAdmin";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import LeafletMap from "@/components/LeafletMap";
import PriceInfoRanking from "./PriceInfoRanking";
import { Badge } from "@/components/ui/badge";
import { getAllTimeAverageRentPrice } from "@/app/(cp)/an/mansion/utiliy/getAllTimeData";
import PropertyInfoPhotoPublic from "./(propertyInfo)/PropertyInfoPhotoPublic";
import { useUserLambdaRecordRentStore } from "@/store/userLambdaRecordRent";

export default function PropertyInfo({ currentUserLambdaRecord, currentUser }: { currentUserLambdaRecord: UserLambdaRecordProps | null, currentUser: TllUserProps | null }) {
  const router = useRouter();
  const { nearbyRent } = useUserLambdaRecordRentStore();

  const getYearlyIncome = (): {
    result: number,
    isEstimated: boolean
  } => {
    if (currentUserLambdaRecord?.yearlyIncome) {
      return {
        result: parseFloat(currentUserLambdaRecord?.yearlyIncome.toFixed(2)),
        isEstimated: false
      };
    }

    if (currentUserLambdaRecord?.recordType === "MANSION" && currentUserLambdaRecord?.building?.mansionRents?.length && currentUserLambdaRecord?.buildingSize) {
      const averageRentPrice = getAllTimeAverageRentPrice(currentUserLambdaRecord?.building?.mansionRents);
      return {
        result: parseInt((averageRentPrice.averageUnitPrice * 12 * currentUserLambdaRecord?.buildingSize / 3.3).toFixed(0)),
        isEstimated: true
      };
    }

    if (currentUserLambdaRecord?.recordType === "BUILDING" && currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgRent && currentUserLambdaRecord?.buildingSize) {
      return {
        result: parseInt((currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgRent * 12 * currentUserLambdaRecord?.buildingSize / 10000).toFixed(0)),
        isEstimated: true
      };
    }

    return {
      result: 0,
      isEstimated: false
    };
  }

  return (
    <section id="propertyInfo" className="bg-white">
      <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2 flex justify-between">
        <div className="flex-1">
          物件情報
        </div>

        {currentUser && currentUser.accessLevel >= 30 && <Button variant="outline" size="sm" onClick={() => router.push(`/ex/search/${currentUserLambdaRecord?.id}/edit`)}>
          <Pencil />
        </Button>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2">
        <div className="flex-1">
          <div className="grid grid-cols-3 border border-neutral-200 rounded-md bg-neutral-50 m-2"> {/* 添加 gap-4 以防止边框重叠 */}
            <div className="p-4 border-r border-neutral-200"> {/* 添加 rounded-md 以改善视觉效果 */}
              <div className="text-sm text-neutral-500">物件価格</div>
              <div className="text-2xl font-bold text-neutral-900">{currentUserLambdaRecord?.price}
                <span className="text-xs text-neutral-500 ml-1">万円</span>
              </div>
            </div>

            {currentUserLambdaRecord?.recordType !== "LAND" && <div className="border-r border-neutral-200 p-4"> {/* 添加 rounded-md 以改善视觉效果 */}
              <div className="text-sm text-neutral-500">年収入</div>

              <div className={`text-2xl font-bold text-neutral-900 ${getYearlyIncome().isEstimated ? "text-neutral-400" : ""}`}>
                {getYearlyIncome().result}
                <span className="text-xs text-neutral-500 ml-1">万円</span>
                {getYearlyIncome().isEstimated && <Badge variant="outline" className="ml-1">想定</Badge>}
              </div>

              <div className="text-xs text-neutral-500">月: {getYearlyIncome().result ? (getYearlyIncome().result / 12).toFixed(2) : "-"}万円
              </div>
            </div>}

            {currentUserLambdaRecord?.recordType !== "LAND" && <div className="p-4"> {/* 添加 rounded-md 以改善视觉效果 */}
              <div className="text-sm text-neutral-500">表面利回り</div>
              <div className={`text-2xl font-bold text-neutral-900 ${getYearlyIncome().isEstimated ? "text-neutral-400" : ""}`}>{getYearlyIncome().result ? (getYearlyIncome().result / (currentUserLambdaRecord?.price || 1) * 100).toFixed(2) : "-"} %
                {getYearlyIncome().isEstimated && <Badge variant="outline" className="ml-1">推定</Badge>}
              </div>
            </div>}
          </div>

          <div className="flex flex-col">
            <PropertyInfoCommon selectedProperty={currentUserLambdaRecord as UserLambdaRecordProps} />

            <div className={`grid grid-cols-1 sm:grid-cols-${currentUserLambdaRecord?.recordType !== "LAND" ? 2 : 1}`}>
              <PropertyInfoLand selectedProperty={currentUserLambdaRecord as UserLambdaRecordProps} />
              {currentUserLambdaRecord?.recordType !== "LAND" && <PropertyInfoBuilding selectedProperty={currentUserLambdaRecord as UserLambdaRecordProps} />}
            </div>



            {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && <PropertyInfoAdmin selectedProperty={currentUserLambdaRecord as UserLambdaRecordProps} />}
          </div>
        </div>

        <div className="">
          <PriceInfoRanking currentUserLambdaRecord={currentUserLambdaRecord as UserLambdaRecordProps} currentUser={currentUser as TllUserProps} />

          <PropertyInfoPhotoPublic selectedProperty={currentUserLambdaRecord as UserLambdaRecordProps} currentUser={currentUser as TllUserProps} />
        </div>
      </div>
    </section >
  );
}