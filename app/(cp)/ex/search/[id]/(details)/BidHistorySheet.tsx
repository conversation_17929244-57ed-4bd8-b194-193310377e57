"use client";

import { Badge } from "@/components/ui/badge";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { TllUserProps } from "@/lib/definitions/tllUser";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, <PERSON>, Speaker, Link2, BellRing, FileWarning } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { createUserLambdaRecordBidAction } from "@/actions/tllUserLambdaRecordBid";
import { useAuthStore } from "@/store/auth";
import { toast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import dayjs from "dayjs";
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { Table, TableHeader, TableRow, TableHead, TableCell, TableBody } from "@/components/ui/table";
import FileDropzone from "./BidHistorySheetFileDropZone";
import { deleteFileFromPropertyMaterialsBucket, getMaterialsFolderContentsInFolder } from "@/actions/helper/supabaseNode";
import UrlPreviewer from "@/components/UrlPreviewer";
import IframeImage from "@/components/IframeImage";
import { bidHistoryColumns } from "./bidHistoryColumns";
import { DataTable } from "@/components/ui/data-table";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer";
import { bidTermsConstants } from "./bidTermsConstants";
import { Checkbox } from "@/components/ui/checkbox";
import BidKaitsukeSection from "./kaitsuke/BidKaitsukeSection";
import { setUserFavIfNotSetAction } from "@/actions/tllUserLambdaRecordFavMaps";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { AlertTitle } from "@/components/ui/alert";
import { AlertDialog } from "@/components/ui/alert-dialog";

export default function BidHistorySheet({ currentUserLambdaRecord, currentUser }: { currentUserLambdaRecord?: UserLambdaRecordProps | null, currentUser: TllUserProps | null }) {
  const [isCreatingBid, setIsCreatingBid] = useState(false);
  const { setCurrentUserLambdaRecord } = useUserLambdaRecordStore();

  const [fileLinks, setFileLinks] = useState<{
    name: string;
    originalName: string;
    updatedAt: string;
    size: number;
    type: string;
    fileSize: number;
    link: string;
    uploadUserId: string;
    tllUser: TllUserProps | null;
  }[]>([]);

  const getFileLinks = async () => {
    const res = await getMaterialsFolderContentsInFolder({ folderName: currentUserLambdaRecord?.id as string, bucketKey: "propertyMaterials" });
    if (res.success) {
      setFileLinks(res.data);
    }
  }

  useEffect(() => {
    if (currentUserLambdaRecord?.id) {
      getFileLinks();
    }
  }, [currentUserLambdaRecord?.id]);

  const deleteFile = async (fileLink: string) => {
    const res = await deleteFileFromPropertyMaterialsBucket(fileLink);
    if (res.success) {
      toast({
        title: "書類を削除しました",
      });
      getFileLinks();
    } else {
      toast({
        title: "書類を削除できませんでした",
        description: res.message,
      });
    }
  }

  const createUserLambdaRecordBid = async () => {
    setIsCreatingBid(true);
    const bid = await createUserLambdaRecordBidAction({
      recordId: currentUserLambdaRecord?.id as string,
      recordPrice: currentUserLambdaRecord?.price as number,
      salesUserId: currentUser?.id as string,
      isSumitomoKeibai: currentUserLambdaRecord?.priceChanges?.some((priceChange) => priceChange.source === "KEIBAI_SUMITOMO") as boolean,
    });

    console.log("🔥 bid", bid);

    if (bid.success) {
      toast({
        title: "物件を入札する",
      });
      sendLark({
        message: `[⚙️][物件検討]${currentUser?.name}さんが物件検討レコードを作成しました。物件ID is ${currentUserLambdaRecord?.id}`,
        url: LARK_URLS.USER_ACTIVITY_CHANNEL,
      });

      // Update the store with the new bid data instead of reloading the page
      if (currentUserLambdaRecord) {
        const updatedRecord = {
          ...currentUserLambdaRecord,
          bids: [...(currentUserLambdaRecord.bids || []), bid.data]
        };
        setCurrentUserLambdaRecord(updatedRecord);
      }
    } else {
      toast({
        title: "物件を入札できませんでした",
        description: bid.message,
      });
    }
    setIsCreatingBid(false);
  }

  return <section id="bidHistory" className="bg-white">
    <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">仕入管理</div>

    <div className="flex flex-col gap-2 mt-4">
      {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && <div className="flex flex-col bg-neutral-50 border border-neutral-300 p-2 gap-2">
        <div className="flex flex-row justify-between border-b border-neutral-200 items-center">
          <div className="font-bold text-neutral-900  p-2 flex-1">検討履歴</div>
          <Button disabled={isCreatingBid} size="sm" variant="outline" onClick={async (e) => {
            // Somethow this is preventing new itms from being shown
            e.preventDefault();
            e.stopPropagation();
            await createUserLambdaRecordBid();
            await setUserFavIfNotSetAction(currentUserLambdaRecord?.id as string);
          }}>
            {isCreatingBid ? <Loader2 className="w-4 h-4 animate-spin" /> : "検討レコード作成"}
          </Button>
        </div>

        <DataTable columns={bidHistoryColumns} data={currentUserLambdaRecord?.bids || []} showFooter={false} />
      </div>}

      <div className="gap-2 grid grid-cols-1 sm:grid-cols-2">

        <BidKaitsukeSection currentUserLambdaRecord={currentUserLambdaRecord as UserLambdaRecordProps} />

        <div className="bg-neutral-50 p-2 border border-neutral-300 ">
          <div className="flex flex-row gap-2 border-b border-neutral-200 justify-between items-center">
            <div className="font-bold text-neutral-900 p-2 flex-1">関連書類</div>
          </div>

          <Alert variant="destructive" className="mt-2 bg-gray-100 p-2 gap-2 items-center justify-between">
            <AlertDescription className="flex flex-row gap-2 items-center">
              <FileWarning className="h-4 w-4" />  4.5MB以上のファイルはアップロードできません
            </AlertDescription>
          </Alert>

          <div className="flex flex-col gap-2 overflow-y-auto pt-2 ">
            <FileDropzone currentUser={currentUser as TllUserProps} recordId={currentUserLambdaRecord?.id as string} onUploaded={() => {
              getMaterialsFolderContentsInFolder({ folderName: currentUserLambdaRecord?.id as string, bucketKey: "propertyMaterials" }).then(res => {
                if (res.success) {
                  setFileLinks(res.data);
                }
              });
            }} />

            <div className="flex flex-col gap-2">
              <div className="text-sm text-neutral-900">
                {fileLinks.length}件の書類

                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>No</TableHead>
                      <TableHead>名前</TableHead>
                      <TableHead>更新日時</TableHead>
                      <TableHead>サイズ</TableHead>
                      <TableHead>種類</TableHead>
                      <TableHead>変更人</TableHead>
                      <TableHead>操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fileLinks.map((file, index) => (
                      <TableRow key={file.link}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>{file.originalName ? file.originalName.slice(0, 20) : file.name.slice(0, 20)}</TableCell>
                        <TableCell>{dayjs(file.updatedAt).format("YYYY/MM/DD HH:mm")}</TableCell>
                        <TableCell>{((file.size || 0) / 1024 / 1024).toFixed(2)} MB</TableCell>
                        <TableCell>{file.type.indexOf("/") > -1 ? file.type.split("/")[1] : file.type}</TableCell>

                        <TableCell>{file.tllUser?.name || "-"}</TableCell>

                        <TableCell className="flex flex-row gap-2 justify-center">
                          {/* <Drawer>
                            <DrawerTrigger asChild>
                              <Button size="sm" variant="outline" >
                                <Eye className="w-4 h-4" />
                              </Button>
                            </DrawerTrigger>
                            <DrawerContent className="h-[calc(70vh-100px)] mb-10 w-full">
                              <DrawerHeader>
                                <DrawerTitle>{file.originalName}</DrawerTitle>
                                <DrawerDescription>{dayjs(file.updatedAt).format("YYYY/MM/DD HH:mm")}</DrawerDescription>
                              </DrawerHeader>

                              <div className="w-full h-full">
                                <IframeImage url={file.link as string} showToolbar={true} />
                              </div>
                            </DrawerContent>
                          </Drawer> */}

                          <Link href={file.link} target="_blank">
                            <Button size="sm" variant="outline">
                              <Link2 className="w-4 h-4" />
                            </Button>
                          </Link>

                          <Button size="sm" variant="destructive" onClick={() => {
                            deleteFile(file.link);
                          }}>
                            削除
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
}