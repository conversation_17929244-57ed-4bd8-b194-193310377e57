import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import PriceCfCalculationForm from "./PriceCfCalculationForm";
import { Separator } from "@/components/ui/separator";
import PriceCfCalculationFormDonutChart from "./PriceCfCalculationFormDonutChart";

export default function PriceCfCalculation({ currentUserLambdaRecord, type }: { currentUserLambdaRecord: UserLambdaRecordProps | null, type: "valuation" | "search" }) {

  return (
    <section id="priceCfCalculation" className="bg-white">
      <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">CF計算</div>

      <div className="flex flex-col sm:flex-row">
        <div className="sm:w-1/2  p-2">
          <PriceCfCalculationForm type={type} />
        </div>

        <div className="sm:w-1/2 grid grid-cols-1 gap-4">
          {currentUserLambdaRecord !== null &&
            typeof currentUserLambdaRecord?.analysisSimulationResults ===
            'object' && (
              <div className="rounded-md bg-neutral-50 m-2">
                <div className="font-bold text-neutral-500 border-b border-neutral-200 p-2">
                  CF計算結果
                </div>

                <div className="p-2">
                  <PriceCfCalculationFormDonutChart />
                </div>

                <Separator />

                <div className="p-2">
                  <ul className="list-disc pl-5">
                    {currentUserLambdaRecord.analysisSimulationResults?.optimalBiddingPriceCalulation
                      ? Object.entries(
                        currentUserLambdaRecord.analysisSimulationResults?.optimalBiddingPriceCalulation,
                      ).filter(([key, value1]) => key !== 'donutChartData' && key !== 'bidPrice')
                        .map(([key, value2]) => (
                          <li key={key} className="mb-2">
                            <span className="font-semibold">{key}:</span>
                            <span>
                              {typeof value2 === 'number' ? `${value2.toFixed(2)}万円` : '0万円'}
                            </span>
                          </li>
                        ))
                      : "..."}
                  </ul>
                </div>
              </div>
            )}
        </div>
      </div>
    </section>
  );
}