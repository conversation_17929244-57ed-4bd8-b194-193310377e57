import type { Metadata } from "next";
import { generateSEOMetadata } from "@/lib/seo/utils";

export async function generateMetadata({ params }: { 
  params: Promise<{ id: string }> 
}): Promise<Metadata> {
  const { id } = await params;
  
  return generateSEOMetadata({
    title: `物件詳細 - ${id} | Urbalytics`,
    description: '詳細な物件情報、価格履歴、周辺相場、収益性分析を確認できます。AI駆動の投資判断サポートで最適な不動産投資を実現。',
    keywords: '物件詳細, 不動産情報, 価格履歴, 収益性分析, 投資判断, 周辺相場',
    url: `https://www.urbalytics.jp/ex/search/${id}`,
    type: 'website',
  });
}

export default function PropertyDetailLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
