"use client"

import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { getUserLambdaRecordAction, updateUserLambdaRecord } from "@/actions/tllUserLambdaRecords";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Link2, Link2Icon, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function EditPage() {
  const { id } = useParams();
  const { currentUserLambdaRecord, setCurrentUserLambdaRecord } = useUserLambdaRecordStore();
  const [salesComments, setSalesComments] = useState<string>("");
  const [buildingRoomCount, setBuildingRoomCount] = useState<number>(0);
  const [yearlyIncome, setYearlyIncome] = useState<number>(0);
  const [rosenka, setRosenka] = useState<number>(0);
  const [landBuildingCoverageRatio, setLandBuildingCoverageRatio] = useState<number>(0);
  const [landFloorAreaRatio, setLandFloorAreaRatio] = useState<number>(0);
  const [buildingMaterial, setBuildingMaterial] = useState<string>("");
  const [buildingLevel, setBuildingLevel] = useState<number>(0);
  const [buildingBuiltYear, setBuildingBuiltYear] = useState<number>(0);
  const [nearestStationWalkMinute, setNearestStationWalkMinute] = useState<number>(0);
  const [landRight, setLandRight] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [landType, setLandType] = useState<string>("");
  const router = useRouter();

  const fetchCurrentUserLambdaRecord = async () => {
    setIsLoading(prev => !prev);
    const results = await getUserLambdaRecordAction(id as string);

    if (results.success) {
      setCurrentUserLambdaRecord(results.data as UserLambdaRecordProps);
      setSalesComments(results.data?.salesComments || "");
      setBuildingRoomCount(results.data?.buildingRoomCount || 0);
      setYearlyIncome(results.data?.yearlyIncome || 0);
      setRosenka(results.data?.valueRosenka || 0);
      setLandBuildingCoverageRatio(results.data?.landBuildingCoverageRatio || 0);
      setBuildingLevel(results.data?.buildingLevel || 0);
      setLandFloorAreaRatio(results.data?.landFloorAreaRatio || 0);
      setLandRight(results.data?.landRight || "");
      setNearestStationWalkMinute(results.data?.nearestStationWalkMinute || 0);
      setBuildingMaterial(results.data?.buildingMaterial || "");
      setBuildingBuiltYear(results.data?.buildingBuiltYear || 0);
      setLandType(results.data?.landType || "");
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchCurrentUserLambdaRecord();
  }, [id]);

  const handleUpdateUserLambdaRecord = async () => {
    setIsLoading(prev => !prev);
    const results = await updateUserLambdaRecord(id as string, { salesComments, buildingRoomCount, yearlyIncome, valueRosenka: rosenka, landFloorAreaRatio, landBuildingCoverageRatio, landRight, nearestStationWalkMinute, buildingLevel: buildingLevel.toString(), buildingMaterial, buildingBuiltYear, landType });
    if (results.success) {
      toast({
        title: "更新しました",
        description: "更新しました",
      });
      setCurrentUserLambdaRecord(results.data as UserLambdaRecordProps);
      router.push(`/ex/search/${id}`);
    }
    setIsLoading(false);
  };

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="物件詳細を修正">詳細修正: {currentUserLambdaRecord?.address}</h1>
    </div>

    <Separator className="mb-2" />


    <div className="flex flex-col gap-4 p-2">
      <div className="flex flex-col gap-2 bg-gray-50 p-2 rounded-md">
        <div className="text-lg font-bold">物件について</div>

        <div className="grid grid-cols-3 gap-2">
          <div className="flex flex-col gap-2">
            <Label>年収入</Label>
            <Input type="number" placeholder="年収入を修正" step="0.1" value={yearlyIncome} onChange={(e) => setYearlyIncome(parseFloat(e.target.value) || 0)} />
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex flex-row gap-2 items-center">
              <Label>路線価</Label>
              <Link2Icon onClick={() => {
                window.open(
                  `https://www.chikamap.jp/chikamap/Map?mid=321&mpx=${currentUserLambdaRecord?.longitude}&mpy=${currentUserLambdaRecord?.latitude}&bsw=2151&bsh=1223`,
                  '_blank',
                  'width=2000,height=1000',
                );
              }} className="w-4 h-4 text-gray-500" />
            </div>
            <Input type="number" placeholder="ローゼンカを修正" value={rosenka} onChange={(e) => setRosenka(parseInt(e.target.value))} />
          </div>

          <div className="flex flex-col gap-2">
            <Label>最寄り駅までの徒歩分</Label>
            <Input type="number" placeholder="最寄り駅までの徒歩分を修正" value={nearestStationWalkMinute} onChange={(e) => setNearestStationWalkMinute(parseInt(e.target.value))} min={0} />
          </div>
        </div>


        <Label>コメント</Label>
        <Textarea placeholder="Commentsを修正" value={salesComments} onChange={(e) => setSalesComments(e.target.value)} rows={5} />
      </div>

      <div className="flex flex-col gap-2 bg-gray-50 p-2 rounded-md">
        <div className="text-lg font-bold">建物について</div>
        <div className="grid grid-cols-3 gap-2">
          <div className="flex flex-col gap-2">
            <Label>部屋数</Label>
            <Input type="number" placeholder="部屋数を修正" value={buildingRoomCount} onChange={(e) => setBuildingRoomCount(parseInt(e.target.value))} />
          </div>

          <div className="flex flex-col gap-2">
            <Label>建物階数</Label>
            <Input type="number" placeholder="建物階数を修正" value={buildingLevel} onChange={(e) => setBuildingLevel(parseInt(e.target.value))} />
          </div>

          <div className="flex flex-col gap-2">
            <Label>建物材料</Label>
            <Select onValueChange={(value) => setBuildingMaterial(value)} value={buildingMaterial}>
              <SelectTrigger>
                <SelectValue placeholder="建物材料を選択" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="木造">木造</SelectItem>
                <SelectItem value="軽量鉄骨造">軽量鉄骨造</SelectItem>
                <SelectItem value="鉄骨造">鉄骨造</SelectItem>
                <SelectItem value="鉄筋コンクリート造">鉄筋コンクリート造</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>


        <div className="grid grid-cols-3 gap-2">
          <div className="flex flex-col gap-2">
            <Label>築年数</Label>
            <Input type="number" placeholder="築年数を修正" value={buildingBuiltYear} onChange={(e) => setBuildingBuiltYear(parseInt(e.target.value))} />
          </div>

        </div>
      </div>


      <div className="flex flex-col gap-2 bg-gray-50 p-2 rounded-md">
        <div className="text-lg font-bold">土地について</div>
        <div className="grid grid-cols-3 gap-2">
          <div className="flex flex-col gap-2">
            <Label>土地権利</Label>
            <Select onValueChange={(value) => setLandRight(value)} value={landRight}>
              <SelectTrigger>
                <SelectValue placeholder="土地権利を選択" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="所有権">所有権</SelectItem>
                <SelectItem value="所有権(再建築不可)">所有権(再建築不可)</SelectItem>
                <SelectItem value="借地権">借地権</SelectItem>
                <SelectItem value="底地権">底地権</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-2">
            <Label>建蔽率</Label>
            <Input type="number" placeholder="建蔽率を修正" value={landBuildingCoverageRatio} onChange={(e) => setLandBuildingCoverageRatio(parseInt(e.target.value))} />
          </div>

          <div className="flex flex-col gap-2">
            <Label>容積率</Label>
            <Input type="number" placeholder="容積率を修正" value={landFloorAreaRatio} onChange={(e) => setLandFloorAreaRatio(parseInt(e.target.value))} />
          </div>

          <div className="flex flex-col gap-2">
            <Label>用途地域</Label>
            <Select onValueChange={(value) => setLandType(value)} value={landType}>
              <SelectTrigger>
                <SelectValue placeholder="用途地域を選択" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="一低">第一種低層住居専用地域</SelectItem>
                <SelectItem value="二低">第二種低層住居専用地域</SelectItem>
                <SelectItem value="一中">第一種中高層住居専用地域</SelectItem>
                <SelectItem value="二中">第二種中高層住居専用地域</SelectItem>
                <SelectItem value="一住">第一種住居地域</SelectItem>
                <SelectItem value="二住">第二種住居地域</SelectItem>
                <SelectItem value="準住">準住居地域</SelectItem>
                <SelectItem value="近商">近隣商業地域</SelectItem>
                <SelectItem value="商業">商業地域</SelectItem>
                <SelectItem value="準工">準工業地域</SelectItem>
                <SelectItem value="⼯業">工業地域</SelectItem>
                <SelectItem value="⼯専">工業専用地域</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Button onClick={handleUpdateUserLambdaRecord} disabled={isLoading}>
        {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : "更新"}
      </Button>
    </div>
  </div>;
}