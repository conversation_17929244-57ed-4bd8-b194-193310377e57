"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react"; // 添加 useState
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { getUserLambdaRecordAction } from "@/actions/tllUserLambdaRecords";
import PropertyInfo from "./(details)/PropertyInfo";
import PriceChangeHistory from "./(details)/(priceChange)/PriceChangeHistory";
import NearbyProperties from "./(details)/NearbyProperties";
import PriceCfCalculation from "./(details)/PriceCfCalculation";
import { CopyCheckIcon, Crown, Loader2, LockIcon, Share } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import FavIcon from "./FavIcon";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import dayjs from "dayjs";
import { tllUserLambdaRecordReevaluateAction } from "@/actions/tllUserLambdaRecordReevaluate";
import { useAuthStore } from "@/store/auth";
import BidHistorySheet from "./(details)/BidHistorySheet";
import { getLatestChangeValue } from "@/components/userLambdaRecord/priceChangeUtilities";
import {
  SheetDescription,
  SheetTrigger,
  SheetTitle,
  SheetContent,
  Sheet,
} from "@/components/ui/sheet";
import { useUIStore } from "@/store/ui";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { RentSection } from "@/app/(cp)/an/mansion/[id]/RentSection";
import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";
import { renderStatusCount } from "@/app/(cp)/ad/bid/checklist/constants";
import { fillPrefectureAreaPostalCode } from "@/actions/geoPrefecture";
import { TllUserProps } from "@/lib/definitions/tllUser";
import RentUpside from "./(details)/RentUpside";
import { useUserLambdaRecordRentStore } from "@/store/userLambdaRecordRent";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";
import TransactionRecords from "./(details)/(transactionRecords)/TransactionRecords";
import { useUserUsageStore } from "@/store/userUsage";
import UsageOverBox from "@/app/(cp)/my/usage/UsageOverBox";
import { getPricingTierForUser } from "@/lib/constants/pricingTier";
import Support from "./(details)/Support";
import HighReturnRecom from "@/components/ui/recomShovelers/HighReturnRecom";
import PriceDownRecom from "@/components/ui/recomShovelers/PriceDownRecom";
import NearbySimilarRecom from "@/components/ui/recomShovelers/NearbySimilarRecom";
import NearbyProjects from "./(details)/NearbyProjects";
import { useInView } from "react-intersection-observer";
import ProfitabilityAnalysis from "./(details)/ProfitabilityAnalysis";
import { coef } from "@/app/api/cron/constants";

export default function PropertyDetailPage({}) {
  const { id } = useParams();
  const router = useRouter();
  const { currentUser } = useAuthStore();

  const {
    setCurrentUserLambdaRecord,
    currentUserLambdaRecord,
    nearbyProjects,
  } = useUserLambdaRecordStore();
  const [isReevaluating, setIsReevaluating] = useState(false);
  const [selectedTab, setSelectedTab] = useState("propertyInfo"); // 新增状态管理选中的标签
  const [isLoading, setIsLoading] = useState(false);
  const { bidSheetOpen, setBidSheetOpen } = useUIStore();
  const { nearbySold, nearbyRent, nearbyRentPart } =
    useUserLambdaRecordRentStore();

  const { todaySearchUsageData } = useUserUsageStore();
  const [isViewed, setIsViewed] = useState(false);

  const fetchUserLambdaRecord = async () => {
    setIsLoading(true);
    const response = await getUserLambdaRecordAction(id as string);

    if (response.success) {
      setCurrentUserLambdaRecord(response.data);
    } else {
      console.error("Failed to fetch user lambda record:", response.message);
    }
    setIsLoading(false);

    // cannnot put it in the useEffect because it will block server actions
    const hash = window.location.hash; // 获取当前URL的hash
    if (hash) {
      const tab = hash.replace("#", ""); // 去掉#符号
      if (
        [
          "propertyInfo",
          "priceChangeHistory",
          "nearbyProperties",
          "priceCfCalculation",
          "upsideAnalysis",
          "profitabilityAnalysis",
          "buildingRentRecords",
          "nearbyProjects",
          "transactionRecords",
          "support",
        ].includes(tab)
      ) {
        setSelectedTab(tab);
        // Smooth scroll to section with top margin offset for initial load
        setTimeout(() => {
          const element = document.getElementById(tab);
          if (element) {
            const headerOffset = 120; // Same offset as handleTabChange
            const elementPosition = element.getBoundingClientRect().top;
            const offsetPosition =
              elementPosition + window.pageYOffset - headerOffset;

            // Check if scrolling to this position would show too much footer
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            const maxScrollTop = documentHeight - windowHeight;

            // If the calculated position would scroll past the optimal point,
            // adjust to show the element better positioned
            const finalScrollTop = Math.min(offsetPosition, maxScrollTop - 200); // 200px buffer from bottom

            window.scrollTo({
              top: Math.max(0, finalScrollTop), // Ensure we don't scroll to negative position
              behavior: "smooth",
            });
          }
        }, 500); // Longer delay for initial load to ensure all content is rendered
      }
    }
  };

  const { ref: refRecommendation, inView: inViewRecommendation } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    if (currentUserLambdaRecord) {
      document.title = `${currentUserLambdaRecord?.address} ${mapper[currentUserLambdaRecord?.recordType as keyof typeof mapper]?.name} `;
    }
  }, [currentUserLambdaRecord]);

  useEffect(() => {
    fetchUserLambdaRecord();
  }, []);

  useEffect(() => {
    const diffDays = dayjs().diff(
      dayjs(currentUserLambdaRecord?.propertyAnalysisResult?.analysisDate),
      "days",
    );
    if (diffDays > 30) {
      revalue();
    }
  }, [currentUserLambdaRecord]);

  useEffect(() => {
    let max = getPricingTierForUser(
      currentUser as TllUserProps,
    )?.dailySearchCount;

    if (!max) {
      return;
    }

    let diff = max - todaySearchUsageData;
    if (diff <= 10 && diff > 0) {
      toast({
        title: `今月の残りの検索回数:${diff}回`,
        description:
          "有料プランにアップグレードすると、より多くの検索が可能になります",
        duration: 10000,
        action: (
          <Button
            variant="outline"
            onClick={() => {
              router.push("/my/billing");
            }}
          >
            アップグレード
          </Button>
        ),
      });
    }

    // 06/10: This will create bug because every view will create a record after a while, thus blockign it for now
    // if (todaySearchUsageData > (getPricingTierForUser(currentUser as TllUserProps)?.dailySearchCount || 0)) {
    //   getIfRecordIsViewedBefore({ recordId: currentUserLambdaRecord?.id as string }).then((res) => {
    //     if (res.success) {
    //       setIsViewed(res.data);
    //     }
    //   });
    // }
  }, [todaySearchUsageData]);

  const handleTabChange = (section: string) => {
    setSelectedTab(section);
    router.push(`#${section}`, {
      scroll: false, // disable default scroll
    });

    // Smooth scroll to section with top margin offset
    setTimeout(() => {
      const element = document.getElementById(section);
      if (element) {
        const headerOffset = 120; // Adjust this value for desired top margin
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition =
          elementPosition + window.pageYOffset - headerOffset;

        window.scrollTo({
          top: offsetPosition,
          behavior: "smooth",
        });
      }
    }, 100); // Small delay to ensure DOM is updated
  };

  const getBidPricePercentage = (record: UserLambdaRecordProps | null): any => {
    const bidPrice =
      record?.analysisSimulationResults?.optimalBiddingPriceCalulation
        ?.bidPrice || 0;
    const price = record?.price || 1;
    return ((bidPrice * 100) / price).toFixed(0);
  };

  const revalue = async () => {
    setIsReevaluating(true);

    if (
      currentUserLambdaRecord?.prefectureCode === null ||
      !currentUserLambdaRecord?.valueRosenka
    ) {
      await fillPrefectureAreaPostalCode(currentUserLambdaRecord?.id as string);
    }

    // if (currentUserLambdaRecord?.nearestStationGroupId === null) {
    // }
    let res = await tllUserLambdaRecordReevaluateAction(
      currentUserLambdaRecord?.id as string,
    );
    if (res.success) {
      toast({
        title: "再査定しました",
      });
      fetchUserLambdaRecord();
    }
    setIsReevaluating(false);
  };

  let nameMatch = {
    BUILDING: "一棟収益物件",
    MANSION: "区分マンション",
    LAND: "土地",
    HOUSE: "一戸建て",
  };

  const prepText = (data: UserLambdaRecordProps) => {
    let starRatingIcon =
      data.propertyAnalysisResult &&
      data.propertyAnalysisResult?.overallStarLevel &&
      data.propertyAnalysisResult?.overallStarLevel < 1
        ? "⭐"
        : data.propertyAnalysisResult?.overallStarLevel &&
            data.propertyAnalysisResult?.overallStarLevel <= 3
          ? "🌟"
          : "✨";

    let title = `${starRatingIcon} [${dayjs().format("MM-DD HH:mm")}][${getStatus(currentUserLambdaRecord as UserLambdaRecordProps)}] ${nameMatch[data.recordType]}${data.recordSubType ? `${data.recordSubType}` : ""} ${starRatingIcon}`;
    let descriptionPrice = `💰価格: ${data.price}万円 ${data.yearlyIncome ? `利回り${((data.yearlyIncome / data.price) * 100).toFixed(1)}%` : ""}`;
    let descriptionTransport = `📍住所: ${data.address} | 交通: ${data.transport}徒歩${data.nearestStationWalkMinute}分`;
    let descriptionLand = `🏞️土地: ${data.landSize}㎡ | ${data.landRight} | ${data.landType}`;
    let descriptionBuilding = `🏠建物: ${data.buildingSize}㎡ | 築${data.buildingBuiltYear}年 | ${data.buildingMaterial || "-"}`;

    return `${title} \n${descriptionPrice} \n${descriptionTransport} \n${descriptionLand} \n${descriptionBuilding}`;
  };

  async function handleNativeShare() {
    if (navigator.share) {
      try {
        navigator.share({
          title: document.title,
          text: prepText(currentUserLambdaRecord as UserLambdaRecordProps),
          url: window.location.href,
        });
        console.log("分享成功");
      } catch (err) {
        console.error("分享失败", err);
      }
    } else {
      alert("当前浏览器不支持原生分享功能");
    }
  }

  return todaySearchUsageData &&
    todaySearchUsageData >
      (getPricingTierForUser(currentUser as TllUserProps)?.dailySearchCount ||
        0) &&
    !isViewed ? (
    <UsageOverBox pageType="searchDp" />
  ) : (
    <div className="min-h-screen flex flex-col overflow-x-hidden scroll-container">
      {/* Sticky header section containing title and tabs */}
      <div className="sticky top-0 bg-white z-50 border-b border-gray-200 w-full shadow-xs">
        <div className="flex justify-between items-left sm:items-center w-full flex-col sm:flex-row gap-2 sm:gap-0 pt-4 px-2 pb-2 sm:px-4">
          <div className="flex flex-col">
            <h1 className="text-2xl font-bold">
              {currentUserLambdaRecord?.recordType === "MANSION" &&
              currentUserLambdaRecord?.building?.nameJa
                ? currentUserLambdaRecord?.building?.nameJa
                : currentUserLambdaRecord?.address}
            </h1>

            <div className="flex space-x-2 mt-1">
              {
                mapper[
                  currentUserLambdaRecord?.recordType as keyof typeof mapper
                ]?.icon
              }

              <Badge variant="outline">
                {
                  nameMatch[
                    currentUserLambdaRecord?.recordType as keyof typeof nameMatch
                  ]
                }
              </Badge>

              <Badge
                className={
                  getStatus(
                    currentUserLambdaRecord as UserLambdaRecordProps,
                  ) === "公開中"
                    ? "bg-green-500"
                    : "bg-red-500"
                }
              >
                {getStatus(currentUserLambdaRecord as UserLambdaRecordProps)}
              </Badge>

              {currentUser?.accessLevel &&
                currentUser?.accessLevel >= 30 &&
                currentUserLambdaRecord?.priceChanges &&
                currentUserLambdaRecord?.priceChanges.length > 0 &&
                getLatestChangeValue(currentUserLambdaRecord, "brokerType") && (
                  <Badge variant="outline">
                    {getLatestChangeValue(
                      currentUserLambdaRecord,
                      "brokerType",
                    )}
                  </Badge>
                )}
            </div>
          </div>

          <div className="flex flex-row gap-2 items-center">
            <Button
              variant="outline"
              disabled={
                !currentUser?.accessLevel || currentUser?.accessLevel < 90
              }
              onClick={async () => {
                revalue();
              }}
              className="rounded-full hover:bg-neutral-200"
            >
              {isReevaluating && <Loader2 className="w-4 h-4 animate-spin" />}

              {currentUser?.accessLevel && currentUser?.accessLevel >= 90
                ? "再査定"
                : "更新日"}

              <span className="ml-[-4px] text-xs text-neutral-500">
                {dayjs().diff(
                  dayjs(
                    currentUserLambdaRecord?.propertyAnalysisResult
                      ?.analysisDate,
                  ),
                  "days",
                )}
                日前
              </span>
            </Button>

            <Button
              variant="outline"
              size="icon"
              onClick={async () => {
                await handleNativeShare();
              }}
              className="rounded-full hover:bg-neutral-200"
            >
              <Share className="w-8 h-8" />
            </Button>

            {currentUser?.accessLevel && currentUser?.accessLevel >= 30 && (
              <Tooltip delayDuration={200}>
                <TooltipTrigger
                  className="flex flex-row gap-2 items-center"
                  asChild
                >
                  <Button
                    variant="outline"
                    size="icon"
                    className="rounded-full hover:bg-neutral-200"
                    onClick={() => {
                      navigator.clipboard.writeText(
                        prepText(
                          currentUserLambdaRecord as UserLambdaRecordProps,
                        ) +
                          "\n" +
                          "🔗 https://urbalytics.jp/pub/rec/" +
                          currentUserLambdaRecord?.id +
                          "/preview",
                      );
                      toast({
                        title: "PreviewURLをクリップボードにコピーしました",
                      });
                    }}
                  >
                    <CopyCheckIcon className="w-4 h-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  プレビューページのリンクを作成中...
                </TooltipContent>
              </Tooltip>
            )}

            <FavIcon
              currentUserLambdaRecord={
                currentUserLambdaRecord as UserLambdaRecordProps
              }
            />
          </div>
        </div>

        {/* Tabs section inside the sticky header */}
        <Tabs value={selectedTab} className="border-t border-gray-200 pt-2">
          {/* 使用选中的标签 */}
          <div className="w-full overflow-x-auto scrollbar-hide px-2">
            <TabsList
              className="bg-white flex space-x-4 p-4 min-w-max justify-start"
              onChange={(value) => value}
            >
              <TabsTrigger
                value="propertyInfo"
                onClick={() => handleTabChange("propertyInfo")}
              >
                物件情報
              </TabsTrigger>

              <div className="flex items-center">
                <TabsTrigger
                  value="priceChangeHistory"
                  onClick={() => handleTabChange("priceChangeHistory")}
                >
                  価格変更履歴
                  {currentUserLambdaRecord?.priceChanges &&
                    currentUserLambdaRecord.priceChanges.length > 0 && (
                      <>
                        :
                        <span
                          className={`font-bold ${currentUserLambdaRecord.priceChanges.length > 1 ? "text-red-500" : ""}`}
                        >
                          {currentUserLambdaRecord.priceChanges.length}件
                        </span>
                      </>
                    )}
                </TabsTrigger>
              </div>

              <TabsTrigger
                value="nearbyProperties"
                onClick={() => handleTabChange("nearbyProperties")}
              >
                近隣売買履歴
                {currentUserLambdaRecord?.propertyAnalysisResult
                  ?.nearbyRecordIdAndDistance &&
                  Object.keys(
                    currentUserLambdaRecord?.propertyAnalysisResult
                      ?.nearbyRecordIdAndDistance,
                  ).length > 0 && (
                    <span className="">
                      :{" "}
                      {
                        Object.keys(
                          currentUserLambdaRecord?.propertyAnalysisResult
                            ?.nearbyRecordIdAndDistance,
                        ).length
                      }
                      件
                    </span>
                  )}
              </TabsTrigger>

              {currentUserLambdaRecord?.recordType === "MANSION" && (
                <TabsTrigger
                  value="buildingRentRecords"
                  onClick={() => handleTabChange("buildingRentRecords")}
                  className="flex flex-row gap-0 items-center"
                >
                  賃貸掲載履歴
                  {currentUserLambdaRecord?.building?.mansionRents?.length >
                    0 && (
                    <span className="">
                      :{" "}
                      {currentUserLambdaRecord?.building?.mansionRents?.length}
                      件
                    </span>
                  )}
                  {currentUser?.accessLevel &&
                    currentUser?.accessLevel < 10 && (
                      <LockIcon className="ml-2 w-4 h-4 text-neutral-500" />
                    )}
                </TabsTrigger>
              )}

              {currentUserLambdaRecord?.recordType !== "LAND" && (
                <TabsTrigger
                  value="upsideAnalysis"
                  onClick={() => handleTabChange("upsideAnalysis")}
                  className="flex flex-row gap-0 items-center"
                >
                  賃料アップサイド
                  {[nearbySold, nearbyRent, nearbyRentPart].filter((v) => v > 0)
                    .length > 0 && (
                    <>
                      :
                      <span className="font-bold text-green-500">
                        {
                          [nearbySold, nearbyRent, nearbyRentPart].filter(
                            (v) => v > 0,
                          ).length
                        }
                        件
                      </span>
                    </>
                  )}
                  {currentUser?.accessLevel &&
                    currentUser?.accessLevel < 10 && (
                      <LockIcon className="ml-2 w-4 h-4 text-neutral-500" />
                    )}
                </TabsTrigger>
              )}

              <TabsTrigger
                value="profitabilityAnalysis"
                onClick={() => handleTabChange("profitabilityAnalysis")}
              >
                収益性試算
                {currentUser?.accessLevel &&
                  currentUser?.accessLevel >= 90 &&
                  currentUserLambdaRecord?.recordType !== "MANSION" && (
                    <>
                      :
                      {(
                        parseFloat(
                          currentUserLambdaRecord?.analysisSimulationResults
                            ?.optimalBiddingPriceCalulation?.bidPrice || "0",
                        ) *
                        coef(currentUserLambdaRecord as UserLambdaRecordProps)
                      ).toFixed(0)}
                      万円
                      <span
                        style={{
                          color:
                            getBidPricePercentage(currentUserLambdaRecord) >= 80
                              ? "green"
                              : "gray",
                        }}
                      >
                        ({getBidPricePercentage(currentUserLambdaRecord)}%)
                      </span>
                    </>
                  )}
              </TabsTrigger>

              <TabsTrigger
                value="nearbyProjects"
                onClick={() => handleTabChange("nearbyProjects")}
              >
                近隣開発計画
                {nearbyProjects?.length > 0 && (
                  <>
                    :
                    <span className="text-neutral-500">
                      {" "}
                      {nearbyProjects?.length}件
                    </span>
                  </>
                )}
              </TabsTrigger>

              <TabsTrigger
                value="transactionRecords"
                onClick={() => handleTabChange("transactionRecords")}
              >
                近隣公示成約事例
              </TabsTrigger>

              {currentUser?.accessLevel && currentUser?.accessLevel >= 90 ? (
                <TabsTrigger
                  value="bidHistory"
                  onClick={() => handleTabChange("bidHistory")}
                >
                  <Sheet open={bidSheetOpen} onOpenChange={setBidSheetOpen}>
                    <SheetTrigger asChild>
                      <div className="flex items-center gap-1">
                        <Crown className="w-4 h-4" />
                        入札: {currentUserLambdaRecord?.bids?.length || 0}件
                        {currentUserLambdaRecord?.bids?.length &&
                          currentUserLambdaRecord?.bids?.length > 0 &&
                          currentUserLambdaRecord?.bids?.sort((a, b) =>
                            dayjs(b.createdAt).diff(dayjs(a.createdAt)),
                          )[0]?.checklist && (
                            <Badge variant="outline" className="ml-1">
                              {renderStatusCount(
                                currentUserLambdaRecord?.bids?.sort((a, b) =>
                                  dayjs(b.createdAt).diff(dayjs(a.createdAt)),
                                )[0],
                              )}
                            </Badge>
                          )}
                        <span className="text-xs text-neutral-500">|</span>
                        資料:{" "}
                        {currentUserLambdaRecord?.materialMappings?.length || 0}
                        件
                      </div>
                    </SheetTrigger>
                    <SheetContent
                      side="bottom"
                      className="h-[90vh] overflow-y-auto pointer-events-auto"
                    >
                      <VisuallyHidden>
                        <SheetTitle>検討履歴</SheetTitle>
                        <SheetDescription>
                          検討履歴を表示します。
                        </SheetDescription>
                      </VisuallyHidden>
                      {/* <SheetHeader> */}
                      {/* <SheetTitle>Are you absolutely sure?</SheetTitle> */}
                      {/* <SheetDescription>
                        This action cannot be undone. This will permanently delete your account
                        and remove your data from our servers.
                      </SheetDescription> */}
                      {/* </SheetHeader> */}
                      <BidHistorySheet
                        currentUserLambdaRecord={currentUserLambdaRecord}
                        currentUser={currentUser as TllUserProps}
                      />
                    </SheetContent>
                  </Sheet>
                </TabsTrigger>
              ) : (
                ""
              )}

              <TabsTrigger
                value="support"
                onClick={() => handleTabChange("support")}
              >
                購入・売却のご相談
              </TabsTrigger>
            </TabsList>
          </div>
          <Separator className="mt-2" />
        </Tabs>
      </div>{" "}
      {/* Close sticky header div */}
      {/* Main content area that grows to fill available space */}
      <div className="flex-1 p-2 sm:p-4 gap-4 bg-neutral-100 flex flex-col gap-4">
        <PropertyInfo
          currentUserLambdaRecord={currentUserLambdaRecord}
          currentUser={currentUser}
        />

        <PriceChangeHistory
          currentUserLambdaRecord={currentUserLambdaRecord}
          currentUser={currentUser as TllUserProps}
          recordId={id as string}
        />

        <NearbyProperties currentUserLambdaRecord={currentUserLambdaRecord} />

        {currentUserLambdaRecord?.recordType !== "LAND" ? (
          <RentUpside
            currentUserLambdaRecord={
              currentUserLambdaRecord as UserLambdaRecordProps
            }
          />
        ) : (
          ""
        )}

        {currentUserLambdaRecord?.recordType === "MANSION" ? (
          isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <div
              className="bg-white flex flex-col gap-2 border border-gray-200"
              id="buildingRentRecords"
            >
              <RentSection
                mansionRents={currentUserLambdaRecord?.building?.mansionRents}
              />
            </div>
          )
        ) : (
          ""
        )}

        <ProfitabilityAnalysis
          currentUserLambdaRecord={currentUserLambdaRecord}
        />

        <NearbyProjects currentUserLambdaRecord={currentUserLambdaRecord} />

        <TransactionRecords currentUserLambdaRecord={currentUserLambdaRecord} />

        <Support />
      </div>
      <Separator className="bg-neutral-200" />
      <div
        ref={refRecommendation}
        id="recommendation"
        className="p-2 sm:p-4 gap-4 bg-neutral-100 flex flex-col gap-4"
      >
        {inViewRecommendation ? (
          <>
            <NearbySimilarRecom
              currentUserLambdaRecord={
                currentUserLambdaRecord as UserLambdaRecordProps
              }
            />
            <HighReturnRecom />
            <PriceDownRecom />
          </>
        ) : (
          <div className="h-20">Loading...</div>
        )}
      </div>{" "}
      {/* Close main content div */}
    </div>
  ); // Close ternary operator
}
