"use client";

import { useEffect, useState } from "react";
import { checkUserLambdaRecordFavMapsAction, updateUserLambdaRecordFavMapsAction } from "@/actions/tllUserLambdaRecordFavMaps";
import { toast } from "@/hooks/use-toast";
import { Heart } from "lucide-react";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ToastAction } from "@/components/ui/toast";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

const mapper = {
  "HOUSE": "一戸建て",
  "BUILDING": "一棟収益物件",
  "LAND": "土地",
  "MANSION": "区分マンション",
}

export default function FavIcon({ currentUserLambdaRecord }: { currentUserLambdaRecord: UserLambdaRecordProps }) {
  const [isFavMap, setIsFavMap] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleFavMapClick = async () => {
    setIsLoading(true);
    const response = await updateUserLambdaRecordFavMapsAction(currentUserLambdaRecord.id as string);
    if (response.success) {
      setIsFavMap(response.data);

      toast({
        title: `物件を${isFavMap ? 'お気に入りから削除しました' : 'お気に入りに追加しました'}`,
      });
    }
    setIsLoading(false);
  }

  const checkFavMap = async () => {
    setIsLoading(true);
    if (!currentUserLambdaRecord.id) {
      setIsLoading(false);
      return;
    }

    const response = await checkUserLambdaRecordFavMapsAction([currentUserLambdaRecord.id] as string[]);
    if (response.success) {
      let isFav = response.data.includes(currentUserLambdaRecord.id)
      setIsFavMap(isFav);

      if (!isFav) {
        setTimeout(() => {
          toast({
            title: `本物件(${currentUserLambdaRecord.address}${mapper[currentUserLambdaRecord.recordType as keyof typeof mapper]})をお気に入りに追加しますか？`,
            description: "物件の更新情報をプッシュ通知で受け取ることができます",
            duration: 1000 * 60,
            action: <ToastAction altText="On" className="" onClick={() => {
              handleFavMapClick();
            }}>追加する</ToastAction>
          })
        }, 1000 * 30) // 30秒后触发
      }
    }
    setIsLoading(false);
  }

  useEffect(() => {
    if (currentUserLambdaRecord) {
      checkFavMap();
    }
  }, [currentUserLambdaRecord]);

  return (
    <Button variant="outline" size="icon" className="rounded-full hover:bg-neutral-200" onClick={handleFavMapClick}>
      {isLoading ? (
        <Loader2 className="w-6 h-6 animate-spin" />
      ) : (
        <Heart className={isFavMap ? 'text-red-500 fill-current' : ''} />
      )}
    </Button>
  )
}
