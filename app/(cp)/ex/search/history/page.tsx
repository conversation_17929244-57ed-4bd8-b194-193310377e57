"use client";

import { Separator } from "@/components/ui/separator";
import { getUserLambdaRecordSearchHistory } from "@/actions/tllUserLambdaRecordSearchHistory";
import { DataTable } from "@/components/ui/data-table";
import { useEffect, useState } from "react";
import { TllUserLambdaRecordSearchHistoryProps } from "@/lib/definitions";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { useAuthStore } from "@/store/auth";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { Info, Terminal } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";

export default function SearchHistory() {
  const [searchHistory, setSearchHistory] = useState<TllUserLambdaRecordSearchHistoryProps[]>([]);
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { currentUser } = useAuthStore();

  const fetchSearchHistory = async () => {
    setIsLoading(true);
    const response = await getUserLambdaRecordSearchHistory({});
    if (response.success) {
      setSearchHistory(response.data.map((item: any) => ({
        ...item.tllUserLambdaRecord,
        searchDate: dayjs(item.createdAt).format("YYYY-MM-DD"),
        isValid: item.isValid,
      })));
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchSearchHistory();

    if (currentUser && currentUser?.accessLevel) {
      setColumns([
        {
          header: "検索日時",
          cell: ({ row }) => {
            return <div className="flex flex-col">
              <div>
                {dayjs(row.original.searchDate).format("YYYY-MM-DD")}
              </div>
              {!row.original.isValid ? <div className="text-xs text-bold text-red-500">無効</div> :
                <div className="text-xs text-bold text-neutral-500">失効日: {dayjs(row.original.searchDate).add(7, "day").format("YYYY-MM-DD")}</div>}
            </div>
          },
        },
        ...getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps }),
      ]);
    }
  }, [currentUser]);

  return <div className="">
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="物件閲覧履歴">物件閲覧履歴</h1>
    </div>

    <Separator className="" />
    <div className="px-4 py-2 flex flex-col gap-2">
      <Alert variant="default">
        <Info className="h-4 w-4" />
        <AlertTitle>失効日について</AlertTitle>
        <AlertDescription>
          物件の閲覧記録は閲覧から <strong>7</strong>日後に無効となります。期限切れ後に同じ物件を再度開く場合は、新たに使用量が消費されます。
        </AlertDescription>
      </Alert>

      <div className="text-sm text-gray-500 mt-2">
        合計 {searchHistory.length} 件 |  今月{searchHistory.filter((item: any) => dayjs(item.searchDate).month() === dayjs().month()).length}件 | 失効 {searchHistory.filter((item: any) => !item.isValid).length} 件
      </div>

      <DataTable columns={columns} data={searchHistory.filter((item: any) => item.isValid)} isLoading={isLoading} />
    </div>
  </div>
}