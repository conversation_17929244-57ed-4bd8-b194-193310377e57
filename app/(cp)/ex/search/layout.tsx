import type { Metadata } from "next";
import { generateSEOMetadata, generateSoftwareApplicationSchema } from "@/lib/seo/utils";

export const metadata: Metadata = generateSEOMetadata({
  title: '不動産検索 - AI駆動の投資物件検索プラットフォーム | Urbalytics',
  description: '日本全国の投資用不動産を効率的に検索。AI分析による収益性評価、詳細な市場データ、投資シミュレーション機能で最適な物件を見つけましょう。リアルタイム価格情報と高精度な投資指標を提供。',
  keywords: '不動産検索, 投資物件, AI分析, 収益性評価, 市場データ, 投資シミュレーション, 不動産投資, 物件検索, プロパティサーチ',
  url: 'https://www.urbalytics.jp/ex/search',
  type: 'website',
});

export default function SearchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const softwareSchema = generateSoftwareApplicationSchema({
    name: "Urbalytics 不動産検索",
    description: "AI駆動の投資物件検索プラットフォーム。日本全国の不動産を効率的に検索し、収益性を分析できます。",
    url: "https://www.urbalytics.jp/ex/search",
    applicationCategory: "BusinessApplication",
    operatingSystem: "Web Browser"
  });

  return (
    <>
      {/* ソフトウェアアプリケーション構造化データ */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareSchema)
        }}
      />

      {children}
    </>
  );
}
