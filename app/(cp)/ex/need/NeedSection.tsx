
"use client";

import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { DataTable } from "@/components/ui/data-table";
import { Badge } from "@/components/ui/badge";

import { mapper } from "../../an/(common)/recordTypeMapper";
import { useEffect, useState, useCallback } from "react";
import { getRecordsMatchForNeedAction } from "@/actions/customerNeeds";
import { Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { TllCustomerNeedCriteriaType } from "@/lib/definitions";
import dayjs from "dayjs";

export default function NeedSection({ item, currentUser, sinceDate, index }: { item: any, currentUser: TllUserProps, sinceDate?: string, index: number }) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const [matchedData, setMatchedData] = useState([]);

  const fetchMatchedData = useCallback(async () => {
    console.log("🔥 [NeedSection] Fetching matched data for", item.title, "sinceDate:", sinceDate);
    setIsLoading(true);
    const response = await getRecordsMatchForNeedAction({ needId: item.id, sinceDate: sinceDate ?? dayjs().add(-1, 'day').toISOString() });
    if (response.success) {
      setMatchedData(response.data);
    }
    setIsLoading(false);
  }, [item.id, item.title, sinceDate]);

  useEffect(() => {
    if (item.id && sinceDate) {
      // Add a small delay to prevent rapid-fire API calls
      const timer = setTimeout(() => {
        fetchMatchedData();
      }, index * 100); // Stagger the calls by 100ms per component

      return () => clearTimeout(timer);
    }
  }, [item.id, sinceDate, fetchMatchedData, index]);

  const mapperForType = {
    [TllCustomerNeedCriteriaType.POSTAL_CODE]: "郵便番号",
    [TllCustomerNeedCriteriaType.STATION_GROUP_ID]: "駅",
    [TllCustomerNeedCriteriaType.AREA_CODE]: "エリア",
  }

  return <div>
    <div key={item.id} className="flex flex-col border rounded-md">
      <div className="text-base flex flex-row justify-between items-center mb-2 bg-gray-100 p-2 rounded-t-md">
        <div className="text-sm flex flex-row gap-2 justify-between items-end">
          <div className="text-lg font-bold">
            #{index + 1} {item.title}
          </div>
          <Badge variant="outline" className="">
            {mapper[item.recordType as keyof typeof mapper]?.name}
          </Badge>
          <Badge variant="outline" className="">
            {mapperForType[item.criteriaType as keyof typeof mapperForType]}
          </Badge>

          {item.paddedWatchedValues?.map((v: any, i: number) => (
            <span key={i} className="text-xs text-gray-500 overflow-hidden text-ellipsis">
              {v.label}
            </span>
          ))}
        </div>

        <Button variant="outline" onClick={() => {
          router.push(`/ex/need/${item.id}`);
        }}>
          <Eye className="h-4 w-4" />
        </Button>

      </div>

      <div className="flex flex-col w-full p-2">
        <div className="text-xs text-gray-500 mb-2">合計: {matchedData?.length || 0}件</div>

        {matchedData && <DataTable columns={getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps })} data={matchedData} isLoading={isLoading} showFooter={true} defaultPageSize={10} />}
      </div>
    </div>
  </div>
}