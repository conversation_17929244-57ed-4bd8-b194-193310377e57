"use client";

import { useState } from "react";
import { CustomerNeedProps, TllCustomerNeedCriteriaType } from "@/lib/definitions";

import { toast } from "@/hooks/use-toast";
import { createCustomerNeedAction } from "@/actions/customerNeeds";
import { useSearchParams, useRouter } from "next/navigation";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";
import NeedForm from "../../need/[needId]/NeedForm";

import { useCustomerNeedStore } from "@/store/customerNeed";
import systemContraint from "@/lib/constants/systemContraint";

export default function NeedOwnNew() {
  const { currentUser } = useAuthStore();
  const [need, setNeed] = useState<any>({
    criteriaType: "AREA_CODE",
  });

  const router = useRouter();
  const customerId = useSearchParams().get("customerId");
  const { addNeed, needs } = useCustomerNeedStore((state) => state);

  const createNeed = async () => {
    try {
      let dataForSaving = {
        criteriaType: need.criteriaType,
        recordType: need.recordType,

        ...(need.criteriaType === TllCustomerNeedCriteriaType.STATION_GROUP_ID && { nearestStationGroupIds: need.paddedWatchedValues?.map((station: any) => station.value).join(",") }),
        ...(need.criteriaType === TllCustomerNeedCriteriaType.AREA_CODE && { areaCodes: need.paddedWatchedValues?.map((area: any) => area.value).join(",") }),
        ...(need.criteriaType === TllCustomerNeedCriteriaType.POSTAL_CODE && { postalCodes: need.paddedWatchedValues?.map((postalCode: any) => postalCode.value).join(",") }),
        ...(need.criteriaType === TllCustomerNeedCriteriaType.BUILDING_ID && { buildingIds: need.paddedWatchedValues?.map((building: any) => building.value).join(",") }),

        title: need.title,

        type: need.type,
        priority: need.priority,
        priceFrom: need.priceFrom,
        priceTo: need.priceTo,
        roiFrom: need.roiFrom,
        landAreaFrom: need.landAreaFrom,
        landAreaTo: need.landAreaTo,
        buildingAreaFrom: need.buildingAreaFrom,
        buildingAreaTo: need.buildingAreaTo,
        yearFrom: need.yearFrom,
        nearestStationWalkMinuteTo: need.nearestStationWalkMinuteTo,
        landCanHotel: need.landCanHotel,
        comment: need.comment,
      } as CustomerNeedProps;

      const response = await createCustomerNeedAction({
        ...(customerId && { customerId: customerId as string }),
        customerNeedData: dataForSaving as CustomerNeedProps
      });

      if (response.success) {
        setNeed(response.data);
        toast({
          title: "更新成功",
          description: "ニーズが更新されました",
        });
        await sendLark({
          message: `[⚙️][ニーズ作成][${currentUser?.name}は、 ${customerId ? "顧客" + customerId + " さん" : "自分"} のニーズを作成しました]`,
          url: LARK_URLS.USER_ACTIVITY_CHANNEL,
        });
        router.push(`/ex/need/${response.data.id}`);

        addNeed(response.data);
      } else {
        console.error("🚨 更新需求时出错:", response.message);
      }
    } catch (error) {
      console.error("🚨 更新需求时出错:", error);
      toast({
        title: "更新失败",
        description: "ニーズが更新されませんでした",
      });
    }
  }

  return <div className="w-full h-full overflow-y-auto">
    <div className="flex justify-between items-center p-4 border-b border-neutral-200">
      <h1 className="text-2xl font-bold" aria-label="ニーズ追加">ニーズ追加</h1>
    </div>
    {needs.length >= systemContraint.customerNeedMaxCount && <div className="text-center text-red-500 h-full flex items-center justify-center">
      ニーズの上限 ({systemContraint.customerNeedMaxCount})に達しました。既存のニーズを削除してください。
    </div>}

    {needs.length < systemContraint.customerNeedMaxCount && <NeedForm need={need} setNeed={setNeed} submit={createNeed} />}
  </div>;
}
