"use client"

import ReactSelect from "react-select";
import { useDebouncedCallback } from "use-debounce";
import { useState } from "react";
import { ActionResponse, GeoPostalCodeProps } from "@/lib/definitions";
import { getPostalCodeByAddressAction } from "@/actions/geoPostalCodes";
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";

const CustomOption = (props: any) => {
  const { data, innerRef, innerProps } = props;
  return (
    <div
      ref={innerRef}
      {...innerProps}
      className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
    >
      <div className="flex flex-row gap-2 w-full justify-between items-center">
        <span className="font-semibold text-black flex-1 text-left">{data.label}</span>
        <span className="text-sm text-gray-600 flex flex-row gap-2">
          〒{data.postalCode}
          <Separator orientation="vertical" className="h-4" />
          {data.address}
        </span>
      </div>
    </div>
  );
};

// Use any because it can be either a single station or an array of stations
export default function NeedFormPostalCodeSearch({ selectedAreas, setSelectedAreas }: { selectedAreas: { label: string; value: string }[], setSelectedAreas: (areas: { label: string; value: string }[]) => void }) {
  const [nearestAreaOptions, setNearestAreaOptions] = useState<{ label: string; value: string }[]>([]);
  const [isLoadingArea, setIsLoadingArea] = useState(false);

  const handleSearch = useDebouncedCallback(async (term: string) => {
    if (!term) {
      return;
    }

    try {
      setIsLoadingArea(true); // 设置正在加载状态
      const response = await getPostalCodeByAddressAction(term.trim()) as ActionResponse<GeoPostalCodeProps[]>;

      if (response.success) {
        setNearestAreaOptions(response.data.map((postalCodeRecord: GeoPostalCodeProps) => ({
          label: `${postalCodeRecord.prefectureName}${postalCodeRecord.cityName}${postalCodeRecord.areaName}${postalCodeRecord.choumeName}`,
          value: postalCodeRecord.postalCode,
          address: `${postalCodeRecord.prefectureName}${postalCodeRecord.cityName}${postalCodeRecord.areaName}${postalCodeRecord.choumeName}`,
          postalCode: postalCodeRecord.postalCode
        }))); // 更新最近车站
      }
    } catch (error) {
      console.error('Failed to fetch nearest stations:', error);
    } finally {
      setIsLoadingArea(false); // 设置正在加载状态
    }
  }, 500);

  return (
    <ReactSelect
      loadingMessage={() =>
        <div className="flex flex-row gap-2 items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>読み込み中</span>
        </div>
      } // ✅ 修改为日语
      instanceId="need-form-area-search"
      aria-activedescendant="need-form-area-search"
      placeholder="エリアを検索...."
      options={nearestAreaOptions || []}
      onInputChange={(term) => {
        handleSearch(term);
      }} // Calls API as user types
      isMulti={true}
      isLoading={isLoadingArea}
      isSearchable
      value={selectedAreas}
      isClearable={true}
      noOptionsMessage={() => "データなし"}
      onChange={(selected) => {
        setSelectedAreas(selected as { label: string; value: string }[]);
      }}
      components={{
        Option: CustomOption,
      }}
    />
  )
}