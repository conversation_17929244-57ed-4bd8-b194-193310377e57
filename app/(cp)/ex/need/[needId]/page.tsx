"use client";

import { getNeedAction, getRecordsMatchForNeedAction } from "@/actions/customerNeeds";
import { DataTable } from "@/components/ui/data-table";
import { Separator } from "@/components/ui/separator";
import { CalendarIcon, Columns, Loader2 } from "lucide-react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { useEffect } from "react";
import { needColumns } from "../../../pa/customer/needColumns";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useAuthStore } from "@/store/auth";
import dayjs from "dayjs";
import { Select, SelectValue, SelectTrigger, SelectItem, SelectContent } from "@/components/ui/select";

export default function CustomerNeedPage() {
  const { needId } = useParams();
  const [need, setNeed] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [records, setRecords] = useState<any[]>([]);
  const [isLoadingRecords, setIsLoadingRecords] = useState(true);
  const [columns, setColumns] = useState<any[]>([]);
  const [selectedSinceDate, setSelectedSinceDate] = useState<string>("1");

  const { currentUser } = useAuthStore();

  const fetchNeed = async () => {
    setIsLoading(true);
    const need = await getNeedAction(needId as string);
    setNeed(need.data);
    setIsLoading(false);
  };

  const fetchNeedRecords = async () => {
    setIsLoadingRecords(true);

    const records = await getRecordsMatchForNeedAction({ needId: needId as string, sinceDate: dayjs().subtract(Number(selectedSinceDate), 'day').toISOString() });

    console.log("records", records);
    if (records.success && records.data.length > 0) {
      setRecords(records.data);
    } else {
      setRecords([]);
    }
    setIsLoadingRecords(false);
  };

  useEffect(() => {
    fetchNeed();
    fetchNeedRecords();
    if (currentUser) {
      setColumns(getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps }));
    }

  }, [needId, currentUser]);

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="ニーズ詳細">
        検査条件詳細 {need?.customer?.name ? `[顧客名: ${need?.customer?.name}]` : ""}
      </h1>
    </div>

    <div className="px-4 py-2 flex flex-col gap-4 bg-neutral-50 border-t border-b border-neutral-200">
      {isLoading ? <div>
        <Loader2 className="animate-spin" />
      </div> : <div>
        <DataTable data={[need]} columns={needColumns} showFooter={false} />
      </div>}
    </div>


    <div className="px-4 mt-4 py-2 flex flex-col gap-4">
      <div className="flex flex-row gap-2 items-center justify-between">
        <div className="text-sm text-neutral-500 flex-1">
          合計{records?.length}件
        </div>

        <div className="flex flex-row gap-2 items-center justify-end">
          <Select onValueChange={(value) => {
            setSelectedSinceDate(value);
            fetchNeedRecords();
          }} value={selectedSinceDate}>
            <SelectTrigger>
              <SelectValue placeholder="日付を選択" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1日前</SelectItem>
              <SelectItem value="2">2日前</SelectItem>
              <SelectItem value="3">3日前</SelectItem>
              <SelectItem value="7">7日前</SelectItem>
              <SelectItem value="14">14日前</SelectItem>
              <SelectItem value="30">30日前</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <DataTable data={records} columns={columns} showFooter={false} isLoading={isLoadingRecords} />
    </div>
  </div>;
}