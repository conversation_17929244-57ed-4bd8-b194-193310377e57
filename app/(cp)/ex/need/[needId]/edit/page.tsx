"use client";

import { Separator } from "@/components/ui/separator";
import { DataTable } from "@/components/ui/data-table";
import { useParams, useRouter } from "next/navigation";
import { fetchCustomersAction } from "@/actions/customers";
import { useEffect, useState } from "react";
import { needColumns } from "../../../../pa/customer/needColumns";
import { getNeedAction, updateCustomerNeedAction } from "@/actions/customerNeeds";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { CustomerNeedProps, TllCustomerNeedCriteriaType } from "@/lib/definitions";
import { toast } from "@/hooks/use-toast";
import { getPrefecturesAction } from "@/actions/geoPrefecture";
import NeedForm from "../NeedForm";
import { sendLark, LARK_URLS } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";
import { useCustomerNeedStore } from "@/store/customerNeed";
import { promise } from "zod";

export default function EditNeedPage() {
  const { needId } = useParams();
  const [need, setNeed] = useState<any>({});
  const { currentUser } = useAuthStore();
  const { updateNeed: updateNeedInStore } = useCustomerNeedStore();
  const router = useRouter();

  useEffect(() => {
    fetchNeed();
  }, []);

  const fetchNeed = async () => {
    const response = await getNeedAction(needId as string);

    if (response.success) {
      setNeed(response.data);
    }
  }

  const updateNeed = async () => {
    try {
      let dataForSaving = {
        criteriaType: need.criteriaType,
        recordType: need.recordType,

        ...(need.criteriaType === TllCustomerNeedCriteriaType.STATION_GROUP_ID && { nearestStationGroupIds: need.paddedWatchedValues?.map((station: any) => station.value).join(",") }),
        ...(need.criteriaType === TllCustomerNeedCriteriaType.AREA_CODE && { areaCodes: need.paddedWatchedValues?.map((area: any) => area.value).join(",") }),
        ...(need.criteriaType === TllCustomerNeedCriteriaType.POSTAL_CODE && { postalCodes: need.paddedWatchedValues?.map((postalCode: any) => postalCode.value).join(",") }),
        ...(need.criteriaType === TllCustomerNeedCriteriaType.BUILDING_ID && { buildingIds: need.paddedWatchedValues?.map((building: any) => building.value).join(",") }),

        title: need.title,
        type: need.type,
        priority: need.priority,
        priceFrom: need.priceFrom,
        priceTo: need.priceTo,
        roiFrom: need.roiFrom,
        landAreaFrom: need.landAreaFrom,
        landAreaTo: need.landAreaTo,
        buildingAreaFrom: need.buildingAreaFrom,
        buildingAreaTo: need.buildingAreaTo,
        yearFrom: need.yearFrom,
        nearestStationWalkMinuteTo: need.nearestStationWalkMinuteTo,
        landCanHotel: need.landCanHotel,
        comment: need.comment,
      } as CustomerNeedProps;

      console.log("dataForSaving", dataForSaving);

      const response = await updateCustomerNeedAction(need.id, dataForSaving as CustomerNeedProps);

      if (response.success) {
        setNeed(response.data);

        toast({
          title: "更新成功",
          description: "ニーズが更新されました...",
        });

        await new Promise((resolve) => setTimeout(resolve, 1000));

        await sendLark({
          message: `[⚙️][ニーズ更新][${currentUser?.name}は、${need.customer?.name ? need.customer?.name + " さん" : "自分"} のニーズを更新しました]`,
          url: LARK_URLS.USER_ACTIVITY_CHANNEL,
        });

        updateNeedInStore(response.data);
        router.push(`/ex/need`);
      } else {
        console.error("🚨 更新需求时出错:", response.message);
      }
    } catch (error) {
      console.error("🚨 更新需求时出错:", error);
      toast({
        title: "更新失败",
        description: "ニーズが更新されませんでした",
      });
    }
  }

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="ニーズ編集">ニーズ編集 {need.customer && `[顧客: ${need.customer?.name}]`}</h1>
    </div>

    <div className="p-4 flex flex-col gap-4 border-t border-b border-neutral-200 sticky top-0 bg-neutral-50">
      <DataTable data={[need]} columns={needColumns} showFooter={false} />
    </div>

    <NeedForm need={need} setNeed={setNeed} submit={updateNeed} />
  </div>;
}
