"use client";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CustomerNeedProps, GeoPrefectureProps, CustomerProps } from "@/lib/definitions";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Loader } from "lucide-react";
import { useState, useEffect } from "react";
import NeedFormAreaSearch from "./NeedFormAreaSearch";
import NeedFormPostalCodeSearch from "./NeedFormPostalCodeSearch";
import { getPrefecturesAction } from "@/actions/geoPrefecture";
import NeedFormStationSearch from "./NeedFormStationSearch";
import { TllCustomerNeedCriteriaType } from "@/lib/definitions";
import { toast } from "@/hooks/use-toast";
import { auth } from "@/lib/auth";
import { useAuthStore } from "@/store/auth";
import { fetchCustomersAction } from "@/actions/customers";
import { usePathname, useSearchParams } from "next/navigation";
import SearchBarMansionBuildingUI from "@/components/ui/SearchBarMansionBuildingUI";

export default function NeedForm({ need, setNeed, submit }: { need: any, setNeed: (need: CustomerNeedProps) => void, submit: () => void }) {

  const [isUpdating, setIsUpdating] = useState(false);
  const { currentUser } = useAuthStore();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [customers, setCustomers] = useState<CustomerProps[]>([]);

  const fetchCustomers = async () => {
    const response = await fetchCustomersAction();
    if (response.success) {
      setCustomers(response.data);
    }
  }

  useEffect(() => {
    if (currentUser && currentUser.accessLevel >= 30) {
      fetchCustomers();
    }
  }, [currentUser]);

  useEffect(() => {
    if (pathname.includes("new")) {
      const customerId = searchParams.get("customerId");
      if (customerId) {
        setNeed({ ...need, customerId: customerId as string });
      }
    }
  }, [pathname]);

  let formCheck = async () => {
    if (!need.paddedWatchedValues || need.paddedWatchedValues?.length === 0) {
      toast({
        variant: "destructive",
        title: "条件を選択してください",
        description: "エリア、駅、郵便番号を選択してください",
        duration: 3000,
      });
      return false;
    }

    if (!need.recordType) {
      toast({
        variant: "destructive",
        title: "物件タイプを選択してください",
        description: "物件タイプを選択してください",
        duration: 3000,
      });
      return false;
    }

    if (!need.title) {
      toast({
        variant: "destructive",
        title: "タイトルを入力してください",
        description: "タイトルを入力してください",
        duration: 3000,
      });
      return false;
    }

    if (!need.priceTo) {
      toast({
        variant: "destructive",
        title: "価格(まで)を入力してください",
        description: "価格(まで)を入力してください",
        duration: 3000,
      });
      return false;
    }

    return true;
  }

  return <div className="p-4 flex flex-col gap-4">
    {currentUser && currentUser.accessLevel >= 30 && <div className="flex flex-col gap-2 col-span-1">
      <div className="text-sm text-neutral-500">顧客名</div>
      <Select onValueChange={(value) => {
        setNeed({ ...need, customerId: value as string });
      }} value={need.customerId}>
        <SelectTrigger className="bg-white">
          <SelectValue placeholder="顧客名を選択" />
        </SelectTrigger>
        <SelectContent>
          {customers.map((customer: CustomerProps) => (
            <SelectItem key={customer.id} value={customer.id as string}>{customer.name}</SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>}


    <div className="grid grid-cols-3 gap-4 bg-neutral-100 p-2 rounded-md">
      <div className="flex flex-col gap-2 col-span-1">
        <div className="text-sm text-neutral-500">*物件タイプ</div>
        <Select onValueChange={(value) => {
          setNeed({ ...need, recordType: value as UserLambdaRecordType });

          // FIXME: should make sure that mansion is not selected when criteriaType is not mansion
        }} value={need.recordType}>
          <SelectTrigger className="bg-white">
            <SelectValue placeholder="物件種類を選択" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={UserLambdaRecordType.BUILDING}>一棟収益物件</SelectItem>
            <SelectItem value={UserLambdaRecordType.LAND}>土地</SelectItem>
            <SelectItem value={UserLambdaRecordType.HOUSE}>一戸建て</SelectItem>
            <SelectItem value={UserLambdaRecordType.MANSION}>マンション</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* <div className="flex flex-col gap-2">
          <div className="text-sm text-neutral-500">優先度</div>
          <Input type="number" min={0} max={3} onChange={(e) => setNeed({ ...need, priority: parseInt(e.target.value) })} value={need.priority || ""} />
        </div> */}

      <div className="flex flex-col gap-2 col-span-2">
        <div className="text-sm text-neutral-500">*タイトル</div>
        <Input type="text" className="bg-white" onChange={(e) => setNeed({ ...need, title: e.target.value })} value={need.title || ""} />
      </div>
    </div>


    <div className="grid grid-cols-3 gap-4 bg-neutral-100 p-2 rounded-md">
      <div className="flex flex-col gap-2 col-span-1">
        <div className="text-sm text-neutral-500">*検索タイプ</div>
        <Select onValueChange={(value) => {
          setNeed({ ...need, criteriaType: value as TllCustomerNeedCriteriaType })
        }} value={need.criteriaType}>
          <SelectTrigger className="bg-white">
            <SelectValue placeholder="検索タイプを選択" />
          </SelectTrigger>
          <SelectContent className="bg-white" >
            <SelectItem value="AREA_CODE">エリア</SelectItem>
            <SelectItem value="STATION_GROUP_ID">駅</SelectItem>
            <SelectItem value="POSTAL_CODE">郵便番号</SelectItem>
            <SelectItem value="BUILDING_ID">マンション名</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {need.criteriaType === TllCustomerNeedCriteriaType.AREA_CODE && <div className="flex flex-col w-full gap-2 col-span-2">
        <div className="text-sm text-neutral-500">エリア名</div>
        {/* <div className="text-sm text-neutral-500">{need.areas?.map((area: any) => area.nameJa).join(", ")}</div> */}
        <NeedFormAreaSearch selectedAreas={need.paddedWatchedValues || []} setSelectedAreas={(areas: any) => setNeed({ ...need, paddedWatchedValues: areas })} />
      </div>}

      {need.criteriaType === TllCustomerNeedCriteriaType.POSTAL_CODE && <div className="flex flex-col w-full gap-2 col-span-2">
        <div className="text-sm text-neutral-500">郵便番号</div>
        <NeedFormPostalCodeSearch selectedAreas={need.paddedWatchedValues || []} setSelectedAreas={(postalCodes: any) => setNeed({ ...need, paddedWatchedValues: postalCodes })} />
      </div>}

      {need.criteriaType === TllCustomerNeedCriteriaType.STATION_GROUP_ID && <div className="flex flex-col w-full gap-2 col-span-2">
        <div className="flex flex-col gap-2">
          <div className="text-sm text-neutral-500">最寄駅</div>
          <NeedFormStationSearch selectedStations={need.paddedWatchedValues || []} setSelectedStations={(stations: any) => setNeed({ ...need, paddedWatchedValues: stations })} />
        </div>
      </div>}

      {need.criteriaType === TllCustomerNeedCriteriaType.BUILDING_ID && <div className="flex flex-col w-full gap-2 col-span-2">
        <div className="text-sm text-neutral-500">マンション名</div>
        <SearchBarMansionBuildingUI selectedBuilding={need.paddedWatchedValues?.[0] as any} setSelectedBuilding={(building: any) => setNeed({ ...need, paddedWatchedValues: [building] })} />
      </div>}
    </div>

    <div className="grid grid-cols-3 gap-4">
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <div className="text-sm text-neutral-500">価格から(万円)</div>
          <Input type="number" onChange={(e) => setNeed({ ...need, priceFrom: parseInt(e.target.value) })} value={need.priceFrom || ""} />
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <div className="text-sm text-neutral-500">*価格まで(万円)</div>
          <Input type="number" onChange={(e) => setNeed({ ...need, priceTo: parseInt(e.target.value) })} value={need.priceTo || ""} />
        </div>
      </div>

      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-2">
          <div className="text-sm text-neutral-500">ROIから (%)</div>
          <Input type="number" onChange={(e) => setNeed({ ...need, roiFrom: parseFloat(e.target.value) })} value={need.roiFrom || ""} />
        </div>
      </div>
    </div>

    <div className="grid grid-cols-2 gap-4">
      <div className="flex flex-col gap-2">
        <div className="text-sm text-neutral-500">土地面積から(㎡)</div>
        <Input type="number" onChange={(e) => setNeed({ ...need, landAreaFrom: parseInt(e.target.value) })} value={need.landAreaFrom || ""} />
      </div>

      <div className="flex flex-col gap-2">
        <div className="text-sm text-neutral-500">土地面積まで(㎡)</div>
        <Input type="number" onChange={(e) => setNeed({ ...need, landAreaTo: parseInt(e.target.value) })} value={need.landAreaTo || ""} />
      </div>
    </div>

    <div className="grid grid-cols-3 gap-4">
      <div className="flex flex-col gap-2">
        <div className="text-sm text-neutral-500">建物面積から(㎡)</div>
        <Input type="number" onChange={(e) => setNeed({ ...need, buildingAreaFrom: parseInt(e.target.value) })} value={need.buildingAreaFrom || ""} />
      </div>

      <div className="flex flex-col gap-2">
        <div className="text-sm text-neutral-500">建物面積まで(㎡)</div>
        <Input type="number" onChange={(e) => setNeed({ ...need, buildingAreaTo: parseInt(e.target.value) })} value={need.buildingAreaTo || ""} />
      </div>

      <div className="flex flex-col gap-2">
        <div className="text-sm text-neutral-500">建物年数から(e.g. 1981新耐震...)</div>
        <Input type="number" onChange={(e) => setNeed({ ...need, yearFrom: parseInt(e.target.value) })} value={need.yearFrom || ""} />
      </div>
    </div>

    <div className="grid grid-cols-3 gap-4">
      <div className="flex flex-col gap-2">
        <div className="text-sm text-neutral-500">最寄り駅徒歩(分)</div>
        <Input type="number" onChange={(e) => setNeed({ ...need, nearestStationWalkMinuteTo: parseInt(e.target.value) })} value={need.nearestStationWalkMinuteTo || ""} />
      </div>
    </div>

    <div>
      <div className="text-sm text-neutral-500">ホテル可</div>
      <Switch checked={need.landCanHotel === 1} onCheckedChange={(checked) => setNeed({ ...need, landCanHotel: checked ? 1 : 0 })} />
    </div>


    <div>
      <div className="text-sm text-neutral-500">コメント</div>
      <Textarea value={need.comment || ""} onChange={(e) => setNeed({ ...need, comment: e.target.value })} />
    </div>

    <div>
      <Button variant="default" onClick={async () => {
        setIsUpdating(true);
        let res = await formCheck();
        if (res) {
          await submit();
        }
        setIsUpdating(false);
      }} disabled={isUpdating}> {isUpdating ?
        <div className="flex flex-row gap-2 items-center">
          <Loader className="w-4 h-4 animate-spin" />
          <span>更新中...</span>
        </div>
        : "更新"} </Button>
    </div>
  </div>
}