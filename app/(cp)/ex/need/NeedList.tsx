"use client";

import { CustomerNeedProps, CustomerNeedSchema } from "@/lib/definitions";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { fetchMyNeedsAction, getRecordMatchSimpleForNeedIdsAction, getRecordsMatchForNeedAction } from "@/actions/customerNeeds";
import { useState } from "react";
import { useEffect } from "react";
import { needColumns } from "../../pa/customer/needColumns";
import { DataTable } from "@/components/ui/data-table";
import { useCustomerNeedStore } from "@/store/customerNeed";
import { mapper } from "../../an/(common)/recordTypeMapper";
import { Badge } from "@/components/ui/badge";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { useAuthStore } from "@/store/auth";
import { TllUserProps } from "@/lib/definitions/tllUser";
import NeedSection from "./NeedSection";
import Link from "next/link";
import dayjs from "dayjs";
import { Skeleton } from "@/components/ui/skeleton";
import { Loader2 } from "lucide-react";

export default function NeedList() {
  const [isLoading, setIsLoading] = useState(false);
  const [isMatchLoading, setIsMatchLoading] = useState(false);
  const { needs, setNeeds } = useCustomerNeedStore();
  const { currentUser } = useAuthStore();

  const padNeedColumns = (needColumns: any[]) => {
    return [
      {
        header: "マーチ",
        cell: ({ row }: { row: any }) => {
          return <div className="flex flex-row gap-2 items-center justify-center">
            {isMatchLoading ? <Loader2 className="w-4 h-4 animate-spin" /> :
              <Link href={`/ex/need/${row.original.id}`} className={`${row.original.matchCount > 0 ? "text-green-500 underline hover:text-green-600" : "text-neutral-500"} text-sm`}  >
                {row.original.matchCount}
              </Link>}
          </div>;
        },
      },
      ...needColumns,
    ];
  };

  const fetchCustomerNeeds = async () => {
    setIsLoading(true);

    const response = await fetchMyNeedsAction();
    if (response.success) {
      setNeeds(response.data);
      setIsLoading(false);
      setIsMatchLoading(true);

      const res = await getRecordMatchSimpleForNeedIdsAction({ needIds: response.data.map((need: CustomerNeedProps) => need.id) as string[] }) as any;
      setNeeds(response.data.map((need: CustomerNeedProps) => ({
        ...need,
        matchCount: res.data.find((item: any) => item.id === need.id)?.matchCount || 0,
      })));
      setIsMatchLoading(false);
    } else {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    if (needs.length === 0) {
      fetchCustomerNeeds();
    }
  }, []);

  return <div>
    <div className="flex justify-between items-center text-sm text-neutral-500 gap-2 flex-col md:flex-row mb-2">
      <div>
        合計: {needs.length}件ニーズ
      </div>
    </div>

    <DataTable data={needs} columns={padNeedColumns(needColumns)} isLoading={isLoading} />
  </div>;
}