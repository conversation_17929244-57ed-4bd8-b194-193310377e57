"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { CalendarIcon, Plus } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { useEffect, useState, useMemo } from "react";
import dayjs from "dayjs";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRouter, useSearchParams } from "next/navigation";
import NeedList from "./NeedList";
import MatchedList from "./MatchedList";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function WatchPage() {
  const searchParams = useSearchParams();
  const tab = searchParams.get("tab");

  const [selectedTab, setSelectedTab] = useState<string>("need");
  const [selectedRecentDays, setSelectedRecentDays] = useState("1");
  const router = useRouter();

  // Memoize sinceDate to prevent multiple calculations
  const sinceDate = useMemo(() => {
    return dayjs().subtract(Number(selectedRecentDays), 'day').toISOString();
  }, [selectedRecentDays]);

  // const fetchMatchedData = async (date: string) => {
  //   setIsLoading(true);
  //   let data = await getMihariMatched({ date: date });

  //   if (data.success) {
  //     setMihariData(data.data);
  //   }
  //   setIsLoading(false);
  // };

  // useEffect(() => {
  //   const unsub = useDataStore.persist.onFinishHydration(() => {
  //     setHydrated(true);
  //   });
  //   setHydrated(useDataStore.persist.hasHydrated()); // 如果已经 ready

  //   return () => {
  //     unsub(); // 清理监听器
  //   };
  // }, []);

  // useEffect(() => {
  //   if (hydrated && mihariData === null) {
  //     fetchMatchedData(selectedDate);
  //   }
  // }, [hydrated]);

  useEffect(() => {
    if (tab) {
      setSelectedTab(tab);
    }
  }, [tab]);

  return (
    <div className="">
      <div className="flex flex-row justify-between items-center p-4 h-[60px]">
        <h1 className="text-2xl font-bold flex-1" aria-label="気に入り検索条件">
          気に入り検索条件
        </h1>

        <div className="flex items-center flex-row gap-2">
          {selectedTab === "need" && <Button variant="outline" onClick={() => {
            router.push("/ex/need/new");
          }}>
            <Plus className="h-4 w-4" />追加
          </Button>}

          {selectedTab === "matched" && <Select onValueChange={async (value) => {
            setSelectedRecentDays(value);
          }} value={selectedRecentDays}>
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="日付を選択" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">最近1日間</SelectItem>
              <SelectItem value="2">最近2日間</SelectItem>
              <SelectItem value="3">最近3日間</SelectItem>
              <SelectItem value="5">最近5日間</SelectItem>
              <SelectItem value="7">最近7日間</SelectItem>
              <SelectItem value="14">最近14日間</SelectItem>
            </SelectContent>
          </Select>
          }

          {/* {selectedTab === "matched" && <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-[140px] pl-3 text-left font-normal",
                  !selectedDate && "text-muted-foreground"
                )}
              >
                {selectedDate ? (
                  dayjs(selectedDate).format("YYYY-MM-DD")
                ) : (
                  <span>日付を選択</span>
                )}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dayjs(selectedDate).toDate()}
                onSelect={async (date: any) => {
                  let newDate = dayjs(date).format('YYYY-MM-DD')
                  setSelectedDate(newDate);
                  // await fetchMatchedData(newDate);
                }}
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
          } */}
        </div>
      </div>

      <Separator className="" />

      <div className="flex flex-row gap-2 items-center justify-center p-2">
        <Tabs value={selectedTab} onValueChange={(value) => {
          setSelectedTab(value);
          router.push(`/ex/need?tab=${value}`);
        }} className="w-full">
          <TabsList>
            <TabsTrigger value="need">気に入り条件一覧</TabsTrigger>
            <TabsTrigger value="matched">マーチング物件</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Empty State Card */}

      {selectedTab === "need" && <div className="p-2 w-full">
        <NeedList />
      </div>}

      {selectedTab === "matched" && <MatchedList sinceDate={sinceDate} />}

      <div className="flex flex-col gap-2 w-full p-2">
        {/* <Separator className="mt-8 " /> */}
        {/* <PerRecomMIhari /> */}
      </div>
    </div>
  );
}
