"use client";

import { CustomerNeedProps, CustomerNeedSchema } from "@/lib/definitions";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect, useCallback } from "react";
import { needColumns } from "../../pa/customer/needColumns";
import { useCustomerNeedStore } from "@/store/customerNeed";
import { useAuthStore } from "@/store/auth";
import { TllUserProps } from "@/lib/definitions/tllUser";
import NeedSection from "./NeedSection";
import { fetchMyNeedsAction } from "@/actions/customerNeeds";
import { useUIStore } from "@/store/ui";

export default function MatchedList({
  sinceDate,
}: {
  sinceDate?: string,
}) {
  const [isLoading, setIsLoading] = useState(false);
  const { needs, setNeeds } = useCustomerNeedStore();
  const { currentUser } = useAuthStore();

  const fetchCustomerNeeds = useCallback(async () => {
    setIsLoading(true);
    const response = await fetchMyNeedsAction();
    if (response.success) {
      setNeeds(response.data);
    }
    setIsLoading(false);
  }, [setNeeds]);

  useEffect(() => {
    console.log("🔥 [MatchedList] needs", needs);
    if (needs.length === 0) {
      fetchCustomerNeeds();
    }
  }, [needs.length, fetchCustomerNeeds]);

  return <div className="flex flex-col gap-2 p-2">
    {needs && needs.map((item: any, index: number) => {
      return <NeedSection item={item} key={item.id} currentUser={currentUser as TllUserProps} sinceDate={sinceDate} index={index} />
    })}
  </div>
}