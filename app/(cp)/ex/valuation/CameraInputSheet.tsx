import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON><PERSON>onte<PERSON>, SheetDescription, She<PERSON>H<PERSON>er, SheetTitle } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { CameraIcon, FileIcon, Loader } from "lucide-react";
import { useState } from "react";
import Image from "next/image";
import { toast } from "@/hooks/use-toast";
import { aiAnalyzeImage } from "@/actions/aiAnalyzeImage";

export function CameraInputSheet({ setInitialData }: { setInitialData: (data: any) => void }) {
  const [photo, setPhoto] = useState<string | null>(null);
  const [photoName, setPhotoName] = useState<string | null>(null);
  const [photoType, setPhotoType] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  return (
    <div className="bg-neutral-50 p-2">
      <div className="text-center">
        <h1 className="text-xl font-bold" aria-label="写真を撮る">写真を撮る</h1>
        <Separator className="mb-2" />
        <p className="text-gray-500 text-xs">
          カメラを呼び出します。写真はアカウントに保存されます。
        </p>
      </div>

      <div className="flex flex-col gap-2 w-full h-2/3 overflow-y-auto border border-gray-500 bg-gray-100 mt-2">
        {photo ? (
          <Image src={photo} alt="拍摄的照片" className="w-full h-[300px] sm:h-[500px]" layout="responsive" width={500} height={300} />
        ) : (
          <div className="w-full h-[300px] sm:h-[500px] flex items-center justify-center flex-col gap-2" onClick={() => {
            // 重新拍摄照片或从相册选择的逻辑
            const isMobile = /Mobi|Android/i.test(navigator.userAgent);
            setPhoto(null); // 清除当前照片
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';

            if (isMobile) {
              input.capture = 'camera'; // 强制调用相机
            }

            input.onchange = (event) => {
              const file = (event.target as HTMLInputElement).files?.[0];
              if (file) {
                const reader = new FileReader();
                reader.onloadend = () => {
                  setPhoto(reader.result as string); // 设置新照片状态
                  setPhotoName(file.name); // 设置新照片名
                  setPhotoType(file.type); // 设置新照片类型
                };
                reader.readAsDataURL(file); // 读取文件为数据URL
              }
            };
            input.click();
          }}>
            <FileIcon className="w-8 h-8 mx-2 text-gray-300" />
            <p className="text-gray-500">写真を撮ってください</p>
          </div>
        )}
      </div>

      <div className="flex flex-row gap-2 justify-between mt-4 my-2">
        <Button
          size="lg"
          disabled={!photo || isLoading} // 加入加载状态的控制
          variant="default"
          className="w-full"
          onClick={async () => {
            console.log('photo', photo);
            console.log('photo type', typeof photo);
            if (!photo) {
              console.error('没有照片');
              return;
            }

            const fileData = photo; // 照片数据
            const fileName = photoName; // 使用原始文件名
            const fileMime = photoType; // 使用原始文件类型

            setIsLoading(true); // 结束加载

            try {
              const result = await aiAnalyzeImage({
                base64Data: fileData,
                filename: fileName || "",
                mime: fileMime || "",
              });
              console.log('result', result);

              if (result.success) {
                console.log('result.data', result.data);
                toast({
                  title: "分析成功",
                  description: JSON.stringify(result.data),
                  duration: 3000,
                });
                setInitialData(result.data);
              } else {
                // 处理错误
                console.error('分析失败:', result.message);
                toast({
                  title: "分析失败",
                  description: `${result.message}`,
                });
              }
            } catch (error) {
              console.error('分析失败:', error);
              toast({
                title: "分析失败",
                description: `${error}`,
              });
            } finally {
              setIsLoading(false); // 结束加载
            }
          }}
        >
          {isLoading ? (
            <span className="flex items-center">
              <Loader className="animate-spin h-5 w-5 mr-2" />
              アップロード中...
            </span>
          ) : (
            "アップロード"
          )}
        </Button>
      </div>
    </div>
  );
}