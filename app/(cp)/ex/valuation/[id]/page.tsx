"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react"; // 添加 useState
import { useUserLambdaRecordStore } from "@/store/userLambdaRecord";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { Crown, Link, Loader2, LockIcon, Pencil, TicketCheck } from "lucide-react";

import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import dayjs from "dayjs";
import { useAuthStore } from "@/store/auth"
import PropertyInfo from "@/app/(cp)/ex/search/[id]/(details)/PropertyInfo";
import NearbyProperties from "@/app/(cp)/ex/search/[id]/(details)/NearbyProperties";
import PriceCfCalculation from "@/app/(cp)/ex/search/[id]/(details)/PriceCfCalculation";
import { getValuationRecordAction } from "@/actions/valuationRecord";
import PriceInfoRanking from "@/app/(cp)/ex/search/[id]/(details)/PriceInfoRanking";
import PropertyInfoCommon from "@/app/(cp)/ex/search/[id]/(details)/(propertyInfo)/PropertyInfoCommon";
import PropertyInfoLand from "@/app/(cp)/ex/search/[id]/(details)/(propertyInfo)/PropertyInfoLand";
import PropertyInfoBuilding from "@/app/(cp)/ex/search/[id]/(details)/(propertyInfo)/PropertyInfoBuilding";
import Map from "@/components/LeafletMap";
import { fillRecordAnalysis } from "@/app/api/cron/reinsFill/recordAnalysis/fillRecordAnalysis";
import { fillNearestStationGroupIdWalkMinute } from "@/actions/geoRailwayStationGroups";
import RentUpside from "@/app/(cp)/ex/search/[id]/(details)/RentUpside";

import Support from "@/app/(cp)/ex/search/[id]/(details)/Support";
import { fillPrefectureAreaPostalCode } from "@/actions/geoPrefecture";
export default function ValuationDetailPage({ }) {
  const { id } = useParams();
  const router = useRouter();
  const { currentUser } = useAuthStore();

  const { setCurrentUserLambdaRecord, currentUserLambdaRecord } = useUserLambdaRecordStore();
  const [isReevaluating, setIsReevaluating] = useState(false);
  const [selectedTab, setSelectedTab] = useState("propertyInfo"); // 新增状态管理选中的标签

  const fetchValuationRecord = async () => {
    const response = await getValuationRecordAction(id as string);

    if (response.success) {
      setCurrentUserLambdaRecord(response.data);
    } else {
      console.error('Failed to fetch user lambda record:', response.message);
    }

    // cannnot put it in the useEffect because it will block server actions
    const hash = window.location.hash; // 获取当前URL的hash
    if (hash) {
      const tab = hash.replace('#', ''); // 去掉#符号
      if (["propertyInfo", "priceChangeHistory", "nearbyProperties", "priceCfCalculation", "upsideAnalysis"].includes(tab)) {
        handleTabChange(tab);
      }
    }
  };

  useEffect(() => {
    // always fetch because the id might be changed
    fetchValuationRecord();
  }, []);

  const handleTabChange = (section: string) => {
    setSelectedTab(section);
    router.push(`#${section}`, {
      scroll: true, // no default scroll
    });
  };

  const getBidPricePercentage = (record: UserLambdaRecordProps | null): any => {
    const bidPrice = record?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice || 0;
    const price = record?.price || 1;
    return ((bidPrice * 100 / price)).toFixed(0);
  }

  return (
    <div className="h-screen overflow-y-auto scroll-container">
      <div className="flex flex-col justify-start items-start p-2 sm:p-4">
        <div className="flex justify-between items-center w-full">
          <div className="">
            <h1 className="text-2xl font-bold">{currentUserLambdaRecord?.address}</h1>

            <div className="flex space-x-2 mt-1">
              <Badge variant="default">{currentUserLambdaRecord?.recordType}</Badge>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <div className="flex flex-row gap-2 items-center">
              <Button variant="outline" onClick={async () => {
                setIsReevaluating(true);
                try {
                  await fillRecordAnalysis({
                    recordId: currentUserLambdaRecord?.id as string,
                    model: "valuationRecord",
                  });

                  toast({
                    title: "再査定しました",
                  });
                  fetchValuationRecord();
                } catch (error) {
                  toast({
                    title: "再査定に失敗しました",
                    description: error instanceof Error ? error.message : "不明なエラー",
                  });
                }
                setIsReevaluating(false);
              }}>
                {isReevaluating ? <Loader2 className="w-4 h-4 animate-spin" /> : "再査定"}
                <span className="ml-[-4px] text-xs text-neutral-500">
                  {dayjs().diff(dayjs(currentUserLambdaRecord?.propertyAnalysisResult?.analysisDate), 'days')}日前
                </span>
              </Button>

              {/* <Tooltip delayDuration={200}>
                <TooltipTrigger asChild>
                  <Button variant="outline" onClick={() => {
                    navigator.clipboard.writeText("https://urbalytics.jp/pub/rec/" + currentUserLambdaRecord?.id + "/preview");
                    toast({
                      title: "PreviewURLをクリップボードにコピーしました",
                    });
                  }}>
                    <Link />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  プレビューページのリンクを作成中...
                </TooltipContent>
              </Tooltip> */}
            </div>

            {/* <div className="flex flex-row gap-2 justify-end items-start float-right">
              <Button size={"sm"} variant="ghost" onClick={() => {
                router.push(`/pub/rec/${currentUserLambdaRecord?.id}/preview`);
              }}>
                SNS LINK
              </Button>
            </div> */}
          </div>

        </div>
      </div>

      <div className="sticky top-0 bg-white z-10"> {/* 添加sticky效果 */}
        <Tabs value={selectedTab} className="my-2"> {/* 使用选中的标签 */}
          <Separator className="my-2" />
          <div className="w-full overflow-x-auto scrollbar-hide px-2 bg-red">
            <TabsList className="sticky top-100 bg-white z-10 flex space-x-4 p-4 min-w-max justify-start" onChange={(value) => (value)}>
              <TabsTrigger value="propertyInfo" onClick={() => handleTabChange("propertyInfo")}>
                物件情報
              </TabsTrigger>

              <TabsTrigger value="nearbyProperties" onClick={() => handleTabChange("nearbyProperties")}>
                近隣掲載物件
              </TabsTrigger>

              {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && <>
                <TabsTrigger value="priceCfCalculation" onClick={() => handleTabChange("priceCfCalculation")}>
                  CF計算-
                  {parseInt(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice)}万円
                  <span style={{
                    color: getBidPricePercentage(currentUserLambdaRecord) >= 80 ? 'green' : 'gray',
                  }}>
                    ({getBidPricePercentage(currentUserLambdaRecord)}%)
                  </span>
                </TabsTrigger>
              </>}

              {currentUserLambdaRecord?.recordType !== "LAND" && <TabsTrigger value="upsideAnalysis" onClick={() => handleTabChange("upsideAnalysis")} className="flex flex-row items-center gap-2">
                賃料アップサイド
                {currentUser?.accessLevel && currentUser?.accessLevel < 10 && <LockIcon className="w-4 h-4" />}
              </TabsTrigger>}
              {/* 
              <TabsTrigger value="priceCfCalculation" onClick={() => handleTabChange("priceCfCalculation")}>
                長期CF試算
                CF計算-
                {parseInt(currentUserLambdaRecord?.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice)}万円
                <span style={{
                  color: getBidPricePercentage(currentUserLambdaRecord) >= 80 ? 'green' : 'gray',
                }}>
                  ({getBidPricePercentage(currentUserLambdaRecord)}%)
                </span>
              </TabsTrigger> */}

              <TabsTrigger value="support" onClick={() => handleTabChange("support")}> 購入・売却のご相談 </TabsTrigger>
            </TabsList>
          </div>
          <Separator className="my-2" />
        </Tabs>
      </div>

      <div className="p-2 sm:p-4 gap-4 bg-neutral-100 flex flex-col gap-4">
        <section id="propertyInfo" className="bg-white">
          <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2 flex justify-between">
            <div className="flex-1">
              物件情報
            </div>

            {/* <Button variant="outline" size="sm" onClick={() => router.push(`/ex/search/${currentUserLambdaRecord?.id}/edit`)}>
              <Pencil />
            </Button> */}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2">
            <div className="flex-1">
              <div className="grid grid-cols-3 border border-neutral-200 rounded-md bg-neutral-50 m-2"> {/* 添加 gap-4 以防止边框重叠 */}
                <div className="border-r border-neutral-200 p-4"> {/* 添加 rounded-md 以改善视觉效果 */}
                  <div className="text-sm text-neutral-500">物件価格</div>
                  <div className="text-2xl font-bold text-neutral-900">{currentUserLambdaRecord?.price}
                    <span className="text-xs text-neutral-500 ml-1">万円</span>
                  </div>
                </div>
                <div className="border-r border-neutral-200 p-4"> {/* 添加 rounded-md 以改善视觉效果 */}
                  <div className="text-sm text-neutral-500">年収入</div>
                  <div className="text-2xl font-bold text-neutral-900">{currentUserLambdaRecord?.yearlyIncome ? currentUserLambdaRecord?.yearlyIncome.toFixed(2) : "-"}
                    <span className="text-xs text-neutral-500 ml-1">万円</span>
                  </div>
                  <div className="text-xs text-neutral-500">月: {currentUserLambdaRecord?.yearlyIncome ? (currentUserLambdaRecord?.yearlyIncome / 12).toFixed(2) : "-"}万円</div>
                </div>
                <div className="border-neutral-200 p-4"> {/* 添加 rounded-md 以改善视觉效果 */}
                  <div className="text-sm text-neutral-500">表面利回り</div>
                  <div className="text-2xl font-bold text-neutral-900">{currentUserLambdaRecord?.yearlyIncome ? (currentUserLambdaRecord?.yearlyIncome / currentUserLambdaRecord?.price * 100).toFixed(2) : "-"} %</div>
                </div>
              </div>

              <div className="flex flex-col">
                <PropertyInfoCommon selectedProperty={currentUserLambdaRecord as UserLambdaRecordProps} />

                <div className={`grid grid-cols-1 sm:grid-cols-${currentUserLambdaRecord?.recordType !== "LAND" ? 2 : 1}`}>
                  <PropertyInfoLand selectedProperty={currentUserLambdaRecord as UserLambdaRecordProps} />
                  {currentUserLambdaRecord?.recordType !== "LAND" && <PropertyInfoBuilding selectedProperty={currentUserLambdaRecord as UserLambdaRecordProps} />}
                </div>
              </div>
            </div>

            <div className="">
              <PriceInfoRanking currentUserLambdaRecord={currentUserLambdaRecord as UserLambdaRecordProps} currentUser={currentUser as TllUserProps} />
            </div>
          </div>
        </section >

        <NearbyProperties currentUserLambdaRecord={currentUserLambdaRecord} />

        {currentUserLambdaRecord?.recordType !== "LAND" && <RentUpside currentUserLambdaRecord={currentUserLambdaRecord as UserLambdaRecordProps} />}

        {currentUser?.accessLevel && currentUser?.accessLevel >= 90 && <PriceCfCalculation currentUserLambdaRecord={currentUserLambdaRecord} type="valuation" />}

        {/* <section id="priceCfCalculation" className="bg-white">
          <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">長期CF試算</div>
          <div className="p-2">
            <p>長期CF試算内容展示</p>
          </div>
        </section> */}

        <Support />
      </div>
    </div>
  );
}
