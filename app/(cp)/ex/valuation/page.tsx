"use client";

import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import ValuationForm from "./valuationForm";
import { CameraIcon, HandIcon, LinkIcon, Loader, Terminal, FileIcon } from "lucide-react";
import { CameraInputSheet } from "./CameraInputSheet";
import { usePathname, useSearchParams } from "next/navigation";
import UrlInputSheet from "./UrlInputSheet";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { findPossibleMatchAction } from "@/actions/tllUserLambdaRecordGuess";
import { toast } from "@/hooks/use-toast";
import Link from "next/link";
import dayjs from "dayjs";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { logger } from "@/lib/logger";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import PdfInputSheet from "./PdfInputSheet";
import { useRouter } from "next/navigation";

import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";
import MansionValuation from "./MansionValuation";

export default function Valuation() {
  const [inputType, setInputType] = useState<"form" | "photo" | "url" | "pdf" | "">("form");
  const searchParams = useSearchParams();
  const [initialData, setInitialData] = useState<any>(null);
  const [resultCandidate, setResultCandidate] = useState<any>([]);
  const [possibleMatch, setPossibleMatch] = useState<any[]>([]);
  const [valuationType, setValuationType] = useState<"mansion" | "building">(searchParams.get("valuationType") as "mansion" | "building" || "building");

  const router = useRouter();

  const findSimilarAutomatically = async (data: any) => {
    const { price, address, buildingSize, landSize, nearestStation } = data;

    if (price && address && buildingSize && landSize && nearestStation) {
      logger.info('automatically find similar');
      const res = await findPossibleMatchAction(data);
      logger.info('res', res);
      if (res.success) {
        setPossibleMatch(res.data || []);
        toast({
          title: "類似物件を見つけました",
          description: res.data?.length + "件の類似物件を見つけました",
        });
      } else {
        //
      }
    }
  }

  return (
    <div>
      <div className="p-2 flex flex-row items-center gap-2">
        <Tabs id="valuationTypeTab" value={valuationType} onValueChange={(value: any) => {
          setValuationType(value);
          router.push(`/ex/valuation?valuationType=${value}`, { scroll: false });
        }}>
          <TabsList>
            <TabsTrigger value="building" className="flex flex-row items-center gap-2">
              {mapper["BUILDING"].iconSmall}
              一棟・戸建・土地
            </TabsTrigger>
            <TabsTrigger value="mansion" className="flex flex-row items-center gap-2">
              {mapper["MANSION"].iconSmall}
              区分マンション(簡易査定)
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Separator className="" />

      {valuationType === "building" &&
        <>
          <div className="p-2 flex flex-row items-center gap-2">
            <Tabs id="valuationInputTypeTab" value={inputType} onValueChange={(value: any) => {
              setInputType(value);
              router.push(`/ex/valuation?inputType=${value}`, { scroll: false });

              createNewSystemUserActivityAction({
                data: {
                  eventType: "BUTTON_CLICK",
                  route: "/ex/valuation",
                  eventMetadata: {
                    buttonName: "valuationInputTypeTabSwitcher",
                    inputType: value,
                  },
                },
              });
            }} className="flex flex-row items-center gap-2 flex-wrap overflow-x-auto">
              <TabsList>
                <TabsTrigger value="form">
                  <HandIcon className="w-4 h-4 mx-2" />手入力で査定
                </TabsTrigger>

                <TabsTrigger value="photo">
                  <CameraIcon className="w-4 h-4 mx-2" />写真から査定
                </TabsTrigger>

                <TabsTrigger value="pdf">
                  <FileIcon className="w-4 h-4 mx-2" /> PDFから査定
                </TabsTrigger>

                <TabsTrigger value="url">
                  <LinkIcon className="w-4 h-4 mx-2" /> URLから査定
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>


          <div className={`grid grid-cols-1 sm:grid-cols-${inputType === "photo" || inputType === "url" || inputType === "pdf" ? 2 : 1}`}>
            <div className="px-2">
              {inputType === "photo" && <CameraInputSheet setInitialData={setInitialData} />}

              {inputType === "url" && <UrlInputSheet setResultCandidate={setResultCandidate} setInitialData={setInitialData} findSimilarAutomatically={findSimilarAutomatically} />}

              {inputType === "pdf" && <PdfInputSheet setInitialData={setInitialData} />}

              {inputType === "url" && resultCandidate.length > 0 && <pre className="p-4 border bg-neutral-100 text-xs">
                <div className="text-sm font-bold mb-2 border-b pb-2">
                  {resultCandidate.length}件のデータを取得しました:
                </div>

                {resultCandidate.map((item: any, index: number) => (
                  <div key={index}>
                    {Object.entries(item).map(([key, value]) => (
                      <div key={key} className="flex flex-row gap-2">
                        <span className="font-semibold">{key}:</span>
                        <span>
                          {typeof value === 'string' ? value : JSON.stringify(value)} {/* 确保值为字符串 */}
                        </span>
                      </div>
                    ))}
                  </div>
                ))}

                <div className="mt-4 border-t pt-2 text-break word-break-all whitespace-pre-wrap">
                  {
                    possibleMatch && possibleMatch.length > 0 &&
                    <div className="flex flex-col gap-2">
                      {possibleMatch.length > 0 ? possibleMatch.map((item: any) => (
                        <div key={item.id} className="flex flex-row gap-2">
                          <Link href={`/ex/valuation/${item.id}`} className="font-semibold text-blue-500 underline">{item.id}:</Link>
                          <span>
                            {item.price} - {item.address} - {item.buildingSize}㎡ - {item.landSize}㎡ - {item.buildingBuiltYear}年建築 - {item.nearestStation} - {item.transport} {dayjs(item.createdAt).format('YYYY-MM-DD')}
                          </span>
                        </div>
                      )) : (
                        <p>類似物件が見つかりません</p>
                      )}
                    </div>
                  }
                </div>
              </pre>}
            </div >

            <div>
              {inputType === "url" && resultCandidate.length > 0 &&
                <div className="p-2 border bg-neutral-100">
                  <Select onValueChange={(value: any) => {
                    let d = resultCandidate[parseInt(value)]
                    console.log('d', d);
                    setInitialData(d);
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select property" />
                    </SelectTrigger>
                    <SelectContent>
                      {resultCandidate.map((item: any, index: number) => (
                        <SelectItem key={index} value={index.toString()}>
                          ({index + 1})[{item.price}万] {item.address} | {item.buildingSize}㎡ | {item.landSize}㎡ | {item.buildingBuiltYear}年建築
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>}

              <ValuationForm initialData={initialData} />
            </div>
          </div>
        </>}

      {valuationType === "mansion" && <MansionValuation />}
    </div>
  );
}
