import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FileIcon, Loader } from "lucide-react";
import { toast } from "@/hooks/use-toast"; // 导入toast用于提示
import { aiAnalyzeImage } from "@/actions/aiAnalyzeImage";
import { aiAnalyzePdf } from "@/actions/aiAnalyzePdf";

export default function PdfInputSheet({ setInitialData }: { setInitialData: (data: any) => void }) {
  const [isLoading, setIsLoading] = useState(false);
  const [pdf, setPdf] = useState<string | null>(null);

  const handlePdfUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPdf(reader.result as string); // 设置PDF状态
        toast({
          title: "アップロード成功",
          description: "PDFが正常にアップロードされました",
        });
      };
      reader.readAsDataURL(file); // 读取文件为数据URL
    } else {
      toast({
        title: "アップロード失敗",
        description: "PDFのアップロードに失敗しました",
      });
    }
  };

  return (
    <div className="bg-neutral-50 p-2">
      <div className="text-center">
        <h1 className="text-xl font-bold" aria-label="PDFをアップロード">PDFをアップロード</h1>
      </div>
      <div className="flex flex-col gap-2 w-full h-2/3 overflow-y-auto border border-gray-500 bg-gray-100 mt-2">
        {pdf ? (
          <iframe src={pdf} className="w-full h-[300px] sm:h-[500px]" title="Uploaded PDF" />
        ) : (
          <div className="w-full h-[300px] sm:h-[500px] flex items-center justify-center flex-col gap-2">
            <input
              type="file"
              accept="application/pdf"
              onChange={handlePdfUpload}
              className="hidden" // 隐藏文件输入
              id="pdf-upload"
            />
            <label htmlFor="pdf-upload" className="w-full h-full flex items-center justify-center flex-col gap-2 cursor-pointer">
              <FileIcon className="w-8 h-8 mx-2 text-gray-300" />
              <p className="text-gray-500">PDFをアップロードしてください</p>
            </label>
          </div>
        )}
      </div>

      <div className="flex flex-row gap-2 justify-between mt-4 my-2">
        <Button
          size="lg"
          disabled={!pdf || isLoading} // 加入加载状态的控制
          variant="default"
          className="w-full"
          onClick={async () => {
            if (!pdf) {
              console.error('没有照片');
              return;
            }

            setIsLoading(true); // 结束加载

            try {
              const result = await aiAnalyzePdf({
                base64: pdf,
              });
              console.log('result', result);

              if (result.success) {
                console.log('result.data', result.data);
                toast({
                  title: "分析成功",
                  description: JSON.stringify(result.data),
                  duration: 3000,
                });
                setInitialData(result.data);
              } else {
                // 处理错误
                console.error('分析失败:', result.message);
                toast({
                  title: "分析失败",
                  description: `${result.message}`,
                });
              }
            } catch (error) {
              console.error('分析失败:', error);
              toast({
                title: "分析失败",
                description: `${error}`,
              });
            } finally {
              setIsLoading(false); // 结束加载
            }
          }}
        >
          {isLoading ? (
            <span className="flex items-center">
              <Loader className="animate-spin h-5 w-5 mr-2" />
              分析中...
            </span>
          ) : (
            "分析"
          )}
        </Button>
      </div>
    </div>
  );
}
