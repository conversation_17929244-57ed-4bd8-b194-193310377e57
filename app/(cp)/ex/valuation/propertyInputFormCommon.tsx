'use client';

import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form"; // 导入表单组件
import { Input } from '@/components/ui/input';
import StationSearchBarUI from "@/components/ui/SearchBarStationUI";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select'; // 假设有Select组件
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";

export default function PropertyInputFormCommon({ form }: { form: any }) {
  return (
    <>
      <FormField
        control={form.control}
        name="recordType"
        render={({ field }) => (
          <FormItem>
            <FormLabel htmlFor="recordType">* 物件タイプ</FormLabel>
            <FormControl>
              <Select onValueChange={(value) => {
                field.onChange(value as UserLambdaRecordType);
                form.trigger();
              }} value={field.value}>
                <SelectTrigger>
                  <SelectValue placeholder="物件種類を選択" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={UserLambdaRecordType.BUILDING}>一棟収益物件</SelectItem>
                  {/* <SelectItem value={UserLambdaRecordType.MANSION}>マンション</SelectItem> */}
                  <SelectItem value={UserLambdaRecordType.LAND}>土地</SelectItem>
                  <SelectItem value={UserLambdaRecordType.HOUSE}>一戸建て</SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage>{form.formState.errors.recordType?.message as string}</FormMessage>
          </FormItem>
        )}
      />

      <div className="grid grid-cols-2 gap-2">
        <FormField
          control={form.control}
          name="price"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="price">* 物件価格 (万円)</FormLabel>
              <FormControl>
                <Input id="price" type="number" {...field}
                  onChange={(e) => {
                    field.onChange(e.target.valueAsNumber || "");
                  }}
                />
              </FormControl>
              <FormMessage>{form.formState.errors.price?.message as string}</FormMessage>
            </FormItem>
          )}
        />

        {form.getValues('recordType') !== UserLambdaRecordType.LAND && <FormField
          control={form.control}
          name="yearlyIncome"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="yearlyIncome">満室年間収入 (万円)</FormLabel>
              <FormControl>
                <Input id="yearlyIncome" type="number" {...field}
                  onChange={(e) => {
                    field.onChange(e.target.valueAsNumber || "");
                  }}
                  defaultValue={0}
                  min={0}
                />
              </FormControl>
              <FormDescription>
                不詳の場合は0のままにしてください | ROI: {form.getValues('yearlyIncome') ? (form.getValues('yearlyIncome') / form.getValues('price') * 100).toFixed(2) + "%" : "-"}
              </FormDescription>
              <FormMessage>{form.formState.errors.yearlyIncome?.message as string}</FormMessage>
            </FormItem>
          )}
        />}
      </div>

      <FormField
        control={form.control}
        name="address"
        render={({ field }) => (
          <FormItem>
            <FormLabel htmlFor="address">* アドレス</FormLabel>
            <FormControl>
              <Input id="address" {...field} />
            </FormControl>
            <FormMessage>{form.formState.errors.address?.message as string}</FormMessage>
          </FormItem>
        )}
      />

      <div className="grid grid-cols-2 gap-2">
        <FormField
          control={form.control}
          name="nearestStation"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="nearestStation">* 最寄駅</FormLabel>
              <FormControl>
                <StationSearchBarUI selectedStation={field.value} setSelectedStation={field.onChange} />
              </FormControl>
              <FormMessage>{form.formState.errors.nearestStation?.message as string}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="nearestStationWalkMinute"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="nearestStationWalkMinute">徒歩距離(分数)</FormLabel>
              <FormControl>
                <Input id="nearestStationWalkMinute" type="number" {...field}
                  onChange={(e) => {
                    field.onChange(e.target.valueAsNumber || "");
                  }}
                />
              </FormControl>
              <FormDescription>徒歩の場合は分数で入力してください。例：10分30秒 -&gt;10.5。バス便の場合は0を空欄にしてください。</FormDescription>
              <FormMessage>{form.formState.errors.nearestStationWalkMinute?.message as string}</FormMessage>
            </FormItem>
          )}
        />
      </div>

      {form.getValues('recordType') !== UserLambdaRecordType.MANSION && <div className="grid grid-cols-2 gap-2">
        <FormField
          control={form.control}
          name="landRight"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="landRight">* 土地権利</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="権利を選択" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="所有権">所有権</SelectItem>
                    <SelectItem value="借地権">借地権</SelectItem>
                    <SelectItem value="底地権">底地権</SelectItem>
                    {/* 他の選択肢を追加 */}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage>{form.formState.errors.landRight?.message as string}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="landSize"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="landSize">* 土地面積(㎡)</FormLabel>
              <FormControl>
                <Input id="landSize" type="number" {...field}
                  onChange={(e) => {
                    field.onChange(e.target.valueAsNumber || "");
                  }}
                />
              </FormControl>
              <FormMessage>{form.formState.errors.landSize?.message as string}</FormMessage>
            </FormItem>
          )}
        />
      </div>}

      {form.getValues('recordType') !== UserLambdaRecordType.LAND && <div className="grid grid-cols-3 gap-2">
        <FormField
          control={form.control}
          name="buildingSize"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="buildingSize">* 建物延べ面積(㎡)</FormLabel>
              <FormControl>
                <Input id="buildingSize" type="number" {...field}
                  onChange={(e) => {
                    field.onChange(e.target.valueAsNumber || "");
                  }}
                />
              </FormControl>
              <FormMessage>{form.formState.errors.buildingSize?.message as string}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="buildingMaterial"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="buildingMaterial">* 建物材料</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="材料を選択" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="木造">木造</SelectItem>
                    <SelectItem value="軽量鉄骨造">軽量鉄骨造</SelectItem>
                    <SelectItem value="鉄骨造">鉄骨造</SelectItem>
                    <SelectItem value="鉄筋コンクリート造">鉄筋コンクリート造</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage>{form.formState.errors.buildingMaterial?.message as string}</FormMessage>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="buildingBuiltYear"
          render={({ field }) => (
            <FormItem>
              <FormLabel htmlFor="buildingBuiltYear">建築年</FormLabel>
              <FormControl>
                <Input id="buildingBuiltYear" type="number" {...field}
                  max={new Date().getFullYear() + 10}
                  min={1900}
                  defaultValue={1900}
                  onChange={(e) => {
                    field.onChange(e.target.valueAsNumber || "");
                  }}
                />
              </FormControl>
              <FormDescription>
                e.g. 2024, 不祥の場合は1900のままにしてください
              </FormDescription>
              <FormMessage>{form.formState.errors.buildingBuiltYear?.message as string}</FormMessage>
            </FormItem>
          )}
        />
      </div>}
    </>
  );
}