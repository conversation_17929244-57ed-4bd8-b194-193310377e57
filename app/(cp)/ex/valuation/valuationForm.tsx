'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form } from "@/components/ui/form";
import PropertyInputForm<PERSON>ommon from './propertyInputFormCommon';
import { useEffect, useState } from 'react';
import { UserLambdaRecordCreateProps, UserLambdaRecordCreateSchema } from '@/lib/definitions';
import { UserLambdaRecordProps } from '@/lib/definitions/userLambdaRecord';
import { createValuationRecordAction } from '@/actions/valuationRecord';
import { toast } from '@/hooks/use-toast';
import { Loader, Terminal } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { sendLark } from '@/lib/thirdParty/lark';
import { logger } from '@/lib/logger';
import { auth } from '@/lib/auth';
import { useAuthStore } from '@/store/auth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { LARK_URLS } from '@/lib/thirdParty/lark';
import { createNewSystemUserActivityAction } from '@/actions/systemUserActivity';
import { getMatchedPropertiesBeforeValuation } from '@/actions/tllUserLambdaRecords';
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer';
import { DataTable } from '@/components/ui/data-table';
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from '@/components/userLambdaRecord/userlambdaRecordTableColumns';

export default function ValuationForm({ initialData }: { initialData: UserLambdaRecordCreateProps }) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { currentUser } = useAuthStore();
  const [isMatchedPropertiesDrawerOpen, setIsMatchedPropertiesDrawerOpen] = useState(false);
  const [matchedProperties, setMatchedProperties] = useState<UserLambdaRecordProps[]>([]);
  const [matchColumns, setMatchColumns] = useState<any>([]);

  const form = useForm({
    resolver: zodResolver(UserLambdaRecordCreateSchema),
    defaultValues: initialData || {
      // recordType: UserLambdaRecordType.BUILDING,
      // price: 0,
      // address: "目黒区目黒本町1-1-1",
      // landSize: 100,
      // landRight: "所有権",
      // nearestStation: "目黒駅",
      // nearestStationWalkMinute: 10,
      // yearlyIncome: 0,
    }
  });

  useEffect(() => {
    if (currentUser) {
      setMatchColumns(getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser }));
    }
  }, [currentUser]);

  const onSubmit = async (data: UserLambdaRecordCreateProps) => {
    console.log("🔥 data", data);
    if (!data.nearestStation?.value) {
      toast({
        title: 'エラーが発生しました',
        description: '最寄駅を選択してください',
        variant: "destructive",
        duration: 10000,
      });
      return;
    }

    let res = await form.trigger();
    if (!res) {
      toast({
        title: 'エラーが発生しました',
        description: 'エラーが発生しました',
        variant: "destructive",
        duration: 10000,
      });
      return;
    }

    // First check if any existing properties
    let matchedProperties = await getMatchedPropertiesBeforeValuation({
      recordType: data.recordType,
      nearestStationGroupId: data.nearestStation?.value,
      landSize: data.landSize,
      buildingSize: data.buildingSize,
    });

    if (matchedProperties.data.length > 0) {
      setMatchedProperties(matchedProperties.data);
      setIsMatchedPropertiesDrawerOpen(true);

      await sendLark({
        message: `[🔍][物件査定][Matched][by ${currentUser?.email}]Address: ${data.address}, RecordType: ${data.recordType}, BuildingMaterial: ${data.buildingMaterial}, BuildingBuiltYear: ${data.buildingBuiltYear}, NearestStation: ${data.nearestStation}, NearestStationWalkMinute: ${data.nearestStationWalkMinute}, Price: ${data.price}, YearlyIncome: ${data.yearlyIncome}, LandRight: ${data.landRight}, LandSize: ${data.landSize}, BuildingSize: ${data.buildingSize} ||| price ${data.price} || matchedProperties ${matchedProperties.data[0].id} and price ${matchedProperties.data[0].price}`,
        url: LARK_URLS.USER_ACTIVITY_CHANNEL,
      });

      return;
    }

    await valuateProperty();
  };

  const valuateProperty = async () => {
    const data = form.getValues();

    setIsLoading(true);
    try {
      let res = await createValuationRecordAction({
        ...data,
        nearestStationGroupId: data.nearestStation?.value,
        nearestStation: data.nearestStation?.label,
      });

      if (res.success) {
        createNewSystemUserActivityAction({
          data: {
            eventType: "FORM_SUBMIT",
            route: "/ex/valuation",
            eventMetadata: {
              formValues: data,
            },
          },
        });

        let analysisData = res.data as UserLambdaRecordProps;
        logger.debug("analysisData", analysisData);

        await sendLark({
          message: `[🔍][物件査定][by ${currentUser?.email}]Address: ${data.address}, RecordType: ${data.recordType}, BuildingMaterial: ${data.buildingMaterial}, BuildingBuiltYear: ${data.buildingBuiltYear}, NearestStation: ${data.nearestStation}, NearestStationWalkMinute: ${data.nearestStationWalkMinute}, Price: ${data.price}, YearlyIncome: ${data.yearlyIncome}, LandRight: ${data.landRight}, LandSize: ${data.landSize}, BuildingSize: ${data.buildingSize} ||| Score is ${analysisData.propertyAnalysisResult?.overallStarLevel}, evaluation (ROI) ${analysisData?.propertyAnalysisResult?.roiValuation?.p80Price?.toFixed(0)} | rent upside ${analysisData?.propertyAnalysisResult?.rentValuation?.priceAfterUpside?.toFixed(0)} | gfa valuation ${analysisData?.propertyAnalysisResult?.gfaValuation?.avgPrice?.toFixed(0)} | unit land avg ${analysisData?.propertyAnalysisResult?.unitLandPriceValuation?.avgPrice?.toFixed(0)}  | sekisan avg ${analysisData?.propertyAnalysisResult?.sekisanValuation?.avgPrice?.toFixed(0)} | vs price ${data.price}`,
          url: LARK_URLS.USER_ACTIVITY_CHANNEL,
        });

        toast({
          title: '査定を作成しました',
          description: '査定を作成しました',
        });

        // FIXME: revert this soon // 
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2秒待機
        router.push(`/ex/valuation/${res.data.id}`);
      } else {
        console.log(res.message);
        toast({
          title: '査定を作成できませんでした',
          description: '査定を作成できませんでした',
        });
      }
    } catch (error) {
      toast({
        title: 'エラーが発生しました',
        description: 'エラーが発生しました' + error,
      });
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    form.reset(initialData);
  }, [initialData]);

  return (
    <div className="p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <PropertyInputFormCommon form={form} />

          <Alert variant="default" className="bg-gray-100">
            <Terminal className="h-4 w-4" />
            <AlertTitle>注意</AlertTitle>
            <AlertDescription>
              現時点では、査定対象は一都三県の物件となります。
            </AlertDescription>
          </Alert>

          <Button size="lg" type="submit" className="mt-4 w-full" disabled={isLoading}>
            {isLoading ? <Loader className="mr-2 h-4 w-4 animate-spin" /> : '確定'}
          </Button>
        </form>
      </Form>

      <Drawer open={isMatchedPropertiesDrawerOpen} onOpenChange={setIsMatchedPropertiesDrawerOpen} >
        <DrawerContent className="h-2/3 w-full">
          <DrawerHeader>
            <DrawerTitle>
              {isLoading ? '物件を確認中...' : 'すでに物件が存在します、物件詳細を確認しますか'}
            </DrawerTitle>
          </DrawerHeader>

          <div className="h-full w-full p-4">
            <DataTable columns={matchColumns} data={matchedProperties} />

            <Button size="lg" type="submit" className="mt-4 w-full" onClick={() => {
              valuateProperty();
            }}>
              既存物件を確認せずに査定を続行する
            </Button>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
}