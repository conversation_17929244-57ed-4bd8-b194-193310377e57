import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { SheetContent, SheetDescription, SheetHeader, SheetTitle } from "@/components/ui/sheet"
import { useState } from "react";
import UrlPreviewer from "@/components/UrlPreviewer";
import IframeImage from "@/components/IframeImage";
import { aiAnalyzeUrl } from "@/actions/aiAnalyzeImage";
import { Link, Loader, SearchIcon } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { findPossibleMatchAction } from "@/actions/tllUserLambdaRecordGuess";
import { logger } from "@/lib/logger";

export default function UrlInputSheet({ setResultCandidate, setInitialData, findSimilarAutomatically }: { setResultCandidate: (data: any) => void, setInitialData: (data: any) => void, findSimilarAutomatically: (data: any) => void }) {
  const [url, setUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleAnalyze = async () => {
    try {
      setIsLoading(true);
      const res = await aiAnalyzeUrl({ url });

      toast({
        title: "分析成功",
        description: `${res.data.length || 1}件のデータを取得しました`,
      });

      if (res.data.length >= 1) {
        setResultCandidate(res.data);
      } else {
        setResultCandidate([res.data]);
        setInitialData(res.data);
        findSimilarAutomatically(res.data);
      }
    } catch (err: any) {
      console.error(err);
      toast({
        title: "分析失败",
        description: err.message,
        variant: "destructive",
        duration: 10000,
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="bg-neutral-50 p-2">
      <div className="flex flex-row gap-2 my-2">
        <Input type="text" placeholder="URLを入力" value={url} onChange={(e) => setUrl(e.target.value)} /> {/* 绑定输入值 */}
        <Button variant="outline" onClick={() => handleAnalyze()} disabled={!url || isLoading}>
          {isLoading ? <Loader className="w-4 h-4 mx-2 animate-spin" /> : <SearchIcon className="w-4 h-4 mx-2" />}
          検索
        </Button>
      </div>

      <div className="mt-4 h-[300px] sm:h-[500px] overflow-y-auto border border-gray-500">
        <IframeImage url={url} />
      </div>
    </div>
  );
}