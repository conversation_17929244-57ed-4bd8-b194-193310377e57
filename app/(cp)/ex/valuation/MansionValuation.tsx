"use client"

import { Input } from "@/components/ui/input";
import SearchBarMansionBuildingUI from "@/components/ui/SearchBarMansionBuildingUI";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { getAllTimeAverageSellingPrice } from "../../an/mansion/utiliy/getAllTimeData";
import { getNearbyBuildingSummary } from "@/actions/proBuilding";
import { getProBuildingById } from "@/actions/proBuilding";
import { useUserUsageStore } from "@/store/userUsage";

export default function MansionValuation() {
  const [selectedBuilding, setSelectedBuilding] = useState<ProBuildingProps | null>(null);
  const [proBuilding, setProBuilding] = useState<ProBuildingProps | null>(null);
  const [buildingSize, setBuildingSize] = useState<number>(0);
  console.log("selectedBuilding", selectedBuilding);

  const fetchProBuilding = async () => {
    const response = await getProBuildingById({ id: selectedBuilding?.id as string, isSystem: false });

    if (response && response.success && response.data) {
      setProBuilding(response.data as ProBuildingProps);
    }
  };

  useEffect(() => {
    if (selectedBuilding) {
      fetchProBuilding();
    }
  }, [selectedBuilding]);

  return <div className="p-2 flex flex-col gap-4 mt-4">
    <div className="flex-1 w-full flex flex-col gap-2">
      建物名を選択ください
      <SearchBarMansionBuildingUI selectedBuilding={selectedBuilding} setSelectedBuilding={setSelectedBuilding} />
    </div>

    {proBuilding && proBuilding.tllUserLambdaRecords.length > 0 ? <div className="flex flex-col gap-2 bg-neutral-100 p-2 rounded-md border border-neutral-200">
      <div>
        <b className="mr-1">平均単価:</b>
        {proBuilding.tllUserLambdaRecords ? getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averageUnitPrice : 0}万円/坪
      </div>
      <div>
        <b className="mr-1">平均面積:</b>
        {proBuilding.tllUserLambdaRecords ? getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averageUnitSize : 0}平米
      </div>
      <div>
        <b className="mr-1">平均価格:</b>
        {proBuilding.tllUserLambdaRecords ? getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averagePrice : 0}万円
      </div>
    </div> : <div className="text-center text-gray-400 h-full flex items-center justify-center bg-neutral-100 rounded-md border border-neutral-200 min-h-[200px]">
      売買データなし
    </div>}

    {selectedBuilding && <div className="bg-neutral-100 p-2 rounded-md flex flex-col gap-4">
      <div className="grid grid-cols-2 gap-2">
        <div className="flex flex-col gap-2">
          延床面積(㎡)
          <Input type="number" placeholder="延床面積" onChange={(e: any) => {
            setBuildingSize(e.target.value);
          }} />
        </div>

        <div className="flex flex-col gap-2">
          査定価格
          <div className="text-2xl font-bold flex flex-row items-end gap-1">
            {proBuilding && proBuilding.tllUserLambdaRecords ? (getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averageUnitPrice * buildingSize).toLocaleString() : 0}
            <span className="text-sm">
              万円
            </span>
          </div>
        </div>
      </div>
    </div>}
  </div>;
}
