"use client";

import { useEffect, useState } from "react";
import { DataTable } from "@/components/ui/data-table";
import { Separator } from "@/components/ui/separator";
import { getUserlambdaRecordValuationTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { getValuationRecordsAction } from "@/actions/valuationRecord";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { useAuthStore } from "@/store/auth";

export default function ValuationHistory() {
  const { currentUser } = useAuthStore();
  const [valuationHistory, setValuationHistory] = useState<UserLambdaRecordProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);

  const getValuationHistory = async () => {
    setIsLoading(true);
    const valuationHistoryResponse = await getValuationRecordsAction({});

    if (valuationHistoryResponse.success && valuationHistoryResponse.data) {
      setValuationHistory(valuationHistoryResponse.data as UserLambdaRecordProps[]);
    }

    setIsLoading(false);
  };

  useEffect(() => {
    if (valuationHistory.length === 0) {
      getValuationHistory();
    }
  }, []); // 只在组件挂载时调用一次

  useEffect(() => {
    if (currentUser) {
      setColumns(getUserlambdaRecordValuationTableColumnsBasedOnAccessLevel(currentUser));
    }
  }, [currentUser]);

  return <div>

    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="査定履歴">査定履歴</h1>
    </div>

    <Separator className="mb-2" />
    {/* <div className="p-4 flex flex-col gap-4">
        <Tabs value={selectedTab} onValueChange={(value) => {
          setSelectedTab(value);
          getValuationHistory();
        }}>
          <TabsList>
            <TabsTrigger value="30">過去30日</TabsTrigger>
            <TabsTrigger value="90">過去90日</TabsTrigger>
            <TabsTrigger value="180">過去180日</TabsTrigger>
            <TabsTrigger value="365">過去一年</TabsTrigger>
          </TabsList>
        </Tabs>


      </div> */}
    <div className="p-4">
      <div className="text-sm text-gray-500 mb-2">
        合計 {valuationHistory.length} 件, 今月 {valuationHistory.filter((item: any) => dayjs(item.createdAt).month() === dayjs().month()).length} 件
      </div>
      <DataTable columns={columns} data={valuationHistory} isLoading={isLoading} defaultPageSize={100} />
    </div>
  </div>;
}