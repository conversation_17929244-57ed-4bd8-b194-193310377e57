"use client";

import { getUserLambdaRecordFavMapsAction } from "@/actions/tllUserLambdaRecordFavMaps";
import { DataTable } from "@/components/ui/data-table"; // 假设你有一个数据表组件
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { Suspense, useEffect, useState } from "react";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsTrigger, TabsList, TabsContent } from "@/components/ui/tabs";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { useAuthStore } from "@/store/auth";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import FavChanges from "./FavChanges";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CalendarIcon, Trash2Icon } from "lucide-react";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { deletePriceChange } from "@/actions/tllUserLambdaRecordPriceChange";
import PerRecomFav from "@/components/ui/recomShovelers/PerRecomFav";

export default function FavPages() {
  const [favMaps, setFavMaps] = useState<UserLambdaRecordProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedTab, setSelectedTab] = useState("30");
  const [selectedRecordType, setSelectedRecordType] = useState("all");
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const { currentUser } = useAuthStore();
  const [mainTabType, setMainTabType] = useState("hasChange");
  const [selectedDate, setSelectedDate] = useState(dayjs().format("YYYY-MM-DD"));
  const router = useRouter();
  const searchParams = useSearchParams()
  const tabFromParams = searchParams.get("tab") as string

  const fetchFavMaps = async (period: string, recordType: string) => {
    setIsLoading(true);
    const favMapsResponse = await getUserLambdaRecordFavMapsAction({ period, recordType });
    if (favMapsResponse.success && favMapsResponse.data) {
      setFavMaps(favMapsResponse.data as UserLambdaRecordProps[]);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (favMaps.length === 0) {
      fetchFavMaps(selectedTab, selectedRecordType);
    }

    setColumns([
      {
        header: 'お気に入り日',
        accessorKey: 'favDate',
        cell: ({ row }) => {
          return dayjs(row.original.favDate).format("YYYY/MM/DD");
        },
      },
      ...getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps }),
    ]);
  }, [currentUser]); // 只在组件挂载时调用一次

  useEffect(() => {
    if (tabFromParams) {
      setMainTabType(tabFromParams);
    }
  }, [tabFromParams]);

  return (
    <div className="">
      <div className="flex flex-row justify-between items-center p-4 h-[60px]">
        <h1 className="text-2xl font-bold flex-1" aria-label="気に入り物件">気に入り物件</h1>

        {mainTabType === "hasChange" && <div className="flex items-center">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-[140px] pl-3 text-left font-normal",
                  !selectedDate && "text-muted-foreground"
                )}
              >
                {selectedDate ? (
                  dayjs(selectedDate).format("YYYY-MM-DD")
                ) : (
                  <span>日付を選択</span>
                )}
                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>

            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dayjs(selectedDate).toDate()}
                onSelect={async (date: any) => {
                  let newDate = dayjs(date).format('YYYY-MM-DD')
                  setSelectedDate(newDate);
                  await fetchFavMaps(selectedTab, selectedRecordType);
                }}
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>}
      </div>

      <Separator className="" />

      <div className="p-4 flex flex-row justify-start items-center gap-4">
        <Tabs value={mainTabType} onValueChange={(value) => {
          setMainTabType(value);
          router.push(`/ex/fav?tab=${value}`);
        }}>
          <TabsList>
            <TabsTrigger value="hasChange">変更あり物件</TabsTrigger>
            <TabsTrigger value="all">全て</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="px-4 py-2">
        {mainTabType === "hasChange" && (
          <FavChanges selectedDate={selectedDate} />
        )}

        {mainTabType === "all" && (
          <div>
            <div className="py-1 flex flex-col gap-4">
              <div className="flex flex-row justify-start items-center gap-4">
                <Tabs value={selectedTab} onValueChange={(value) => {
                  setSelectedTab(value);
                  fetchFavMaps(value, selectedRecordType);
                }}>
                  <TabsList>
                    <TabsTrigger value="30">過去30日</TabsTrigger>
                    <TabsTrigger value="90">過去90日</TabsTrigger>
                    <TabsTrigger value="180">過去180日</TabsTrigger>
                    <TabsTrigger value="365">過去一年</TabsTrigger>
                  </TabsList>
                </Tabs>

                <Tabs value={selectedRecordType} onValueChange={(value) => {
                  setSelectedRecordType(value);
                  fetchFavMaps(selectedTab, value);
                }}>
                  <TabsList>
                    <TabsTrigger value="all">全て</TabsTrigger>
                    <TabsTrigger value="BUILDING">建物</TabsTrigger>
                    <TabsTrigger value="HOUSE">マンション</TabsTrigger>
                    <TabsTrigger value="LAND">土地</TabsTrigger>
                    <TabsTrigger value="MANSION">マンション</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
            </div>

            <Separator className="my-2" />

            <div className="text-sm text-gray-500 pb-2">合計: {favMaps.length}件</div>
            <DataTable columns={columns} data={favMaps} isLoading={isLoading} />
          </div>
        )}
      </div>

      <Separator className="mt-8 " />
      <div className="flex flex-col gap-2 w-full p-2">
        <PerRecomFav />
      </div>
    </div>
  );
}
