"use client"

import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";
import { checkUserLambdaRecordFavMapsAction, getNewMatchFavForDate } from "@/actions/tllUserLambdaRecordFavMaps";
import { renderARecordWithChange } from "../../ad/dashboard/utility/renderFollowedItem";
import { useAuthStore } from "@/store/auth";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import dayjs from "@/lib/thirdParty/dayjsWithTz";
import { Loader2 } from "lucide-react";
import { TllUserProps } from "@/lib/definitions/tllUser";

export default function FavChanges({ selectedDate }: { selectedDate: string }) {
  const { currentUser } = useAuthStore();
  const [changedFavRecords, setChangedFavRecords] = useState<UserLambdaRecordProps[]>([]); // 获取store中的方法
  const [isLoading, setIsLoading] = useState(false);

  let fetchUserLambdaRecords = async () => {
    setIsLoading(true);
    const response = await getNewMatchFavForDate({ date: dayjs(selectedDate).format("YYYY-MM-DD") });

    if (response.success) {
      setChangedFavRecords(response.data);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    fetchUserLambdaRecords();
  }, [selectedDate]);

  return (
    <div className="flex flex-col gap-4">
      <div className="text-sm text-gray-500">
        {changedFavRecords.length}件のお気に入り変更物件があります。
      </div>

      {isLoading && <div className="flex justify-center items-center h-screen">
        <Loader2 className="w-10 h-10 animate-spin" />
      </div>}

      {!isLoading && changedFavRecords.map((record) => {
        return (
          <div key={record.id} className="flex flex-col gap-2 bg-gray-50 p-2 rounded-md">
            {renderARecordWithChange({ record, currentUser: currentUser as TllUserProps })}
          </div>
        )
      })}
      {/* <div>
        <h1 className="text-xl font-bold">監視会社</h1>
        <Separator className="my-2" />
        {userLambdaRecords.filter((record) => favMapIds.includes(record.id)).map((record) => renderARecordWithChange(record))}
      </div> */}
    </div>
  );
}