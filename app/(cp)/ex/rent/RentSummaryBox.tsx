"use client"

import { getAverageRent, getAverageRentFromSale } from "./rentUtilities";
import { getAllTimeAverageBuildingHouseRentPrice, getAllTimeAverageRentPrice } from "../../an/mansion/utiliy/getAllTimeData";
import { ProMansionRent } from "@prisma/client";
import { Loader2, ChevronDown, ChevronUp } from "lucide-react";
import dayjs from "dayjs";
import Rent<PERSON>hart from "../../an/(common)/RentChart";
import RentBuildingHouseChart from "../../an/(common)/RentBuildingHouseChart";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";

interface RentSummaryBoxProps {
  data: any[];
  dataType: string;
  isLoading: boolean;
  selected: boolean;
  tabMapper: any;
  t: (key: string) => string;
}

export default function RentSummaryBox({ data, dataType, isLoading, selected, tabMapper, t }: RentSummaryBoxProps) {
  const [isExpanded, setIsExpanded] = useState(selected);

  useEffect(() => {
    setIsExpanded(selected);
  }, [selected]);

  return (
    <Card
      className={`transition-all duration-200 ${selected ? "ring-2 ring-blue-100 bg-blue-50/50" : "hover:bg-gray-50"}`}
      onClick={() => setIsExpanded(!isExpanded)}
    >
      <CardHeader className="p-4 pb-2 cursor-pointer">
        <div className="flex flex-row items-center justify-between w-full">
          <div className="flex flex-col items-center justify-center w-[60px] text-center p-2 rounded-lg">
            <div className="text-neutral-700">
              {tabMapper[dataType as keyof typeof tabMapper]?.iconBig}
            </div>
            <Badge variant="secondary" className="mt-1 text-[10px]">
              {tabMapper[dataType as keyof typeof tabMapper]?.labelShort}
            </Badge>
          </div>

          <div className="flex flex-col flex-1 ml-4">
            {isLoading ? (
              <div className="flex flex-row flex-1 justify-start items-center p-2 bg-gray-50 rounded-lg">
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                <div className="text-sm text-gray-500 ml-2">
                  {t("RentUtilities.messages.dataLoading")}
                </div>
              </div>
            ) : data && data.length > 0 ? (
              <div className="flex flex-row flex-1 justify-between items-center">
                <div className="flex flex-col text-sm flex-1 space-y-1">
                  <div className="text-base font-medium flex items-center gap-2">
                    <span className="text-gray-600">平均賃料:</span>
                    <span className="text-blue-500 font-semibold">
                      {dataType === "saleRecords" ? getAverageRentFromSale(data).averageRent : getAverageRent(data).averageRent}万円
                    </span>
                  </div>

                  <div className="text-xs flex items-center gap-2 text-gray-600">
                    <span>平均賃貸面積:</span>
                    <span className="font-medium">
                      {dataType === "saleRecords" ? getAverageRentFromSale(data).averageUnitSize : getAverageRent(data).averageUnitSize}平米
                    </span>
                  </div>

                  <div className="text-xs flex items-center gap-2 text-gray-600">
                    <span>平均坪賃料:</span>
                    <span className="font-medium">
                      {dataType === "saleRecords" ? getAverageRentFromSale(data).averageUnitRent : getAverageRent(data).averageUnitRent}円/坪
                    </span>
                  </div>
                </div>

                <div className="flex flex-row items-center gap-2">
                  <Badge variant="secondary" className="text-lg px-3 py-1">
                    {data?.length}
                    <span className="text-sm ml-1 font-normal">件</span>
                  </Badge>
                  {isExpanded ? <ChevronUp className="w-4 h-4 text-gray-400" /> : <ChevronDown className="w-4 h-4 text-gray-400" />}
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-400 p-4 rounded-lg">
                {t("RentUtilities.messages.noData")}
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="p-4 pt-2">
          {(dataType === "rentHouse" || dataType === "rentAll") && (
            <div className="mt-2 border-t pt-2">
              <RentBuildingHouseChart
                records={data?.sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) =>
                  dayjs(b.createdAt).diff(dayjs(a.createdAt))) as any[]}
                unit="month"
              />
            </div>
          )}

          {(dataType === "rentIchibu" || dataType === "rentSamePropertyKubun" || dataType === "rentKubun") && (
            <div className="mt-2 border-t pt-2">
              <RentChart
                records={data?.sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) =>
                  dayjs(b.createdAt).diff(dayjs(a.createdAt))) as any[]}
                unit="month"
              />
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}