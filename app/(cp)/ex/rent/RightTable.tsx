"use client"

import { DataTable } from "@/components/ui/data-table"
import { useEffect, useState } from "react"
import { buildingRentColumns, houseRentColumns, mansionRentColumns } from "../../an/mansion/[id]/mansionRentColumns"
import { Badge } from "@/components/ui/badge"
import { Loader2Icon } from "lucide-react"
import { Tabs as TabsRaxis, TabsContent, TabsList as TabsListRaxis, TabsTrigger as TabsTriggerRaxis } from "@/components/ui/tabsRadix";
import { useAuthStore } from "@/store/auth"
import { TllUserProps } from "@/lib/definitions/tllUser"
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns"
import { Select, SelectItem, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select"
import dayjs from "dayjs"

interface RightTableProps {
  setSelectedResultTab: (value: string) => void;
  selectedResultTab: string;
  tabsToShow: string[];
  isLoading: boolean;
  rentEstimate: any;
  tabMapper: any;
}

export default function RightTable({ setSelectedResultTab, selectedResultTab, tabsToShow, isLoading, rentEstimate, tabMapper }: RightTableProps) {
  const [shuuekiColumns, setShuuekiColumns] = useState<any[]>([])
  const [filterCreatedAtMonth, setFilterCreatedAtMonth] = useState<number>(0)

  const { currentUser } = useAuthStore()

  useEffect(() => {
    if (currentUser && currentUser?.accessLevel) {
      setShuuekiColumns((getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps })));
    }
  }, [currentUser])

  const filter = (record: any) => {
    if (Number(filterCreatedAtMonth) > 0 && record.createdAt) {
      if (dayjs().diff(dayjs(record.createdAt), 'month') > Number(filterCreatedAtMonth)) {
        return false;
      }
    }
    return true;
  }


  return <TabsRaxis value={selectedResultTab} className="flex flex-row justify-start items-center flex-wrap overflow-x-auto w-full" onValueChange={(value: string) => {
    setSelectedResultTab(value)
  }}>
    <TabsListRaxis className="flex flex-row justify-start items-center whitespace-nowrap overflow-x-auto w-full scrollbar-hide">
      {tabsToShow.map((tab) => <TabsTriggerRaxis key={tab} value={tab}>
        <div className="flex flex-row gap-2 items-center">
          {tabMapper[tab as keyof typeof tabMapper].icon}
          {tabMapper[tab as keyof typeof tabMapper].label}

          {isLoading ? <Loader2Icon className="w-4 h-4 animate-spin ml-2" /> : rentEstimate[tab as keyof typeof rentEstimate]?.length > 0 ? <Badge variant="outline" className="text-x ml-2">
            {rentEstimate[tab as keyof typeof rentEstimate]?.length}
          </Badge> : ""}
        </div>
      </TabsTriggerRaxis>)}
    </TabsListRaxis>

    <div className="w-full pt-4 flex flex-col gap-2">

      <div className="flex flex-row justify-start items-center gap-2">
        <div className="flex flex-row justify-start items-center gap-2">
          <Select value={filterCreatedAtMonth.toString()} onValueChange={(value: string) => {
            setFilterCreatedAtMonth(Number(value))
          }}>
            <SelectTrigger>
              <SelectValue placeholder="作成日を選ぶ" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">作成日: すべて</SelectItem>
              <SelectItem value="1">1ヶ月内</SelectItem>
              <SelectItem value="3">3ヶ月内</SelectItem>
              <SelectItem value="6">半年内</SelectItem>
              <SelectItem value="12">1年以内</SelectItem>
            </SelectContent>
          </Select>

          {/* <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select a city" />
            </SelectTrigger>
          </Select> */}
        </div>

      </div>

      {selectedResultTab !== "saleRecords" && <DataTable columns={
        selectedResultTab === "rentHouse" ? houseRentColumns : selectedResultTab === "rentBuilding" ? buildingRentColumns : mansionRentColumns
      } data={rentEstimate[selectedResultTab as keyof typeof rentEstimate]?.filter(filter).sort((a: any, b: any) => {
        return b.createdAt.getTime() - a.createdAt.getTime()
      }) || []} defaultPageSize={20} isLoading={isLoading} />}


      {selectedResultTab === "saleRecords" &&
        <DataTable columns={shuuekiColumns} data={rentEstimate[selectedResultTab as keyof typeof rentEstimate]?.filter(filter).sort((a: any, b: any) => {
          return b.createdAt.getTime() - a.createdAt.getTime()
        }) || []} defaultPageSize={20} isLoading={isLoading} />
      }

      {/* <DataTable
        columns={columns}
        data={rentEstimate[selectedResultTab as keyof typeof rentEstimate]}
      /> */}
    </div>
  </TabsRaxis>
}