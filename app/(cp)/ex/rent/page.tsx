"use client"

import SearchBarCombined<PERSON> from "@/components/ui/SearchBarCombinedUI"

import { mapper } from "../../an/(common)/recordTypeMapper"
import { TabsList } from "@/components/ui/tabs"
import { RecordType } from "zod"
import { Tabs } from "@/components/ui/tabs"
import { useRouter } from "next/navigation"
import { useSearchParams } from "next/navigation"
import { TabsTrigger } from "@/components/ui/tabs"
import { useEffect, useState } from "react"
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord"
import dynamic from "next/dynamic"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { getRentEstimate } from "@/actions/rent";
import { BadgeJapaneseYen, Building, Building2Icon, HotelIcon, HouseIcon, Info, InfoIcon, Loader2Icon, MapIcon, MapPinIcon, TrainIcon, Warehouse } from "lucide-react"
import { useAuthStore } from "@/store/auth"
import RentSummaryBox from "./RentSummaryBox"
import { removeOutlier } from "@/lib/helper/stats"
import { coef } from "@/app/api/cron/constants"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import RightTable from "./RightTable"
import { Tooltip } from "@/components/ui/tooltip"
import { TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { useTranslations } from "next-intl"
import NodeCache from "node-cache"
import NoPermissionBox from "../../my/usage/NoPermissionBox"
import DailyLimitExhaustedBox from "../../my/usage/DailyLimitExhaustedBox"
import { getAverageRent, getAverageRentFromSale, createTabMapper } from "./rentUtilities"
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark"
import { getPricingTierForUser } from "@/lib/constants/pricingTier"
import {
  markDailyUsage,
  getTotalDailyUsageCount,
  isTotalDailyUsageLimitExceeded,
  DAILY_USAGE_FEATURES
} from "@/lib/utils/dailyUsageTracker"

export default function RentPage() {
  const searchParams = useSearchParams();
  const t = useTranslations();

  // Create tab mapper with translations
  const tabMapper = createTabMapper(t);

  const [selectedOption, setSelectedOption] = useState<any>(null)
  const [selectedResultTab, setSelectedResultTab] = useState<string>("rentIchibu")
  const { currentUser } = useAuthStore()

  const rawParamRecordType = searchParams.get("selectRecordType");
  const [selectRecordType, setSelectRecordType] = useState<"MANSION" | "BUILDING" | "HOUSE">(rawParamRecordType as "MANSION" | "BUILDING" | "HOUSE" || "MANSION")

  const [isLoading, setIsLoading] = useState(false)
  const [tabsToShow, setTabsToShow] = useState<string[]>(["rentSamePropertyKubun", "rentKubun", "rentHouse", "rentIchibu", "rentAll"])
  const [inputYear, setInputYear] = useState<string>("all")
  const [inputArea, setInputArea] = useState<number>(50)
  const [inputRent, setInputRent] = useState<number>(15)

  const [rentEstimate, setRentEstimate] = useState<any>({
    building: [],
    station: [],
    postalCode: [],
    area: [],
  })
  const [estimateSummary, setEstimateSummary] = useState<any>({
    avgPrice: 0,
    p80Price: 0,
    avgUnit: 0,
    p80Unit: 0,
  })

  const getData = async () => {
    if (!selectedOption || !selectRecordType) return;

    // Check daily usage limit for free users BEFORE making API call
    if (currentUser?.accessLevel === 1) {
      const pricingTier = getPricingTierForUser(currentUser);
      const dailyLimit = pricingTier?.dailyRentSearchCount || 5;

      if (isTotalDailyUsageLimitExceeded(DAILY_USAGE_FEATURES.RENT_SEARCH, dailyLimit)) {
        console.log(`🔥 Daily rent search limit exceeded for user: ${currentUser.email}`);
        return; // Don't proceed with search
      }
    }

    setIsLoading(true)
    const res = await getRentEstimate({ selectedOption, recordType: selectRecordType })

    if (res.success) {
      let data = res.data
      setRentEstimate(data)
      setUnitRentArrayFromData(data)

      // Deafult set to first tab
      const firstTabWithData = Object.keys(tabMapper).find((tab) => data[tab as keyof typeof data].length > 0);
      if (firstTabWithData) {
        setSelectedResultTab(firstTabWithData);
      }

      // Mark usage for rent search ONLY after successful API call
      if (currentUser?.accessLevel === 1) {
        // Create same unique key based on search parameters
        const searchKey = `${selectedOption.value}_${selectedOption.type}_${selectRecordType}`;
        const wasMarked = markDailyUsage(DAILY_USAGE_FEATURES.RENT_SEARCH, searchKey);
        if (wasMarked) {
          console.log(`🔥 Rent search usage marked after successful API call for user: ${currentUser.email}, key: ${searchKey}`);
        }
      }
    } else {
      console.log(`🔥 Rent search API call failed, usage not counted for user: ${currentUser?.email || 'unknown'}`);
    }
    setIsLoading(false)
  }

  const setUnitRentArrayFromData = (data: any) => {
    let tempArray = getAverageRentFromSale(data.saleRecords, inputYear === "all" ? undefined : Number(inputYear)).averageUnitRentArr as any[];

    (["rentSamePropertyKubun", "rentKubun", "rentHouse", "rentIchibu", "rentAll"] as (keyof typeof data)[]).forEach((key: keyof typeof data) => {
      if (data[key]) {
        tempArray = tempArray.concat(getAverageRent(data[key], inputYear === "all" ? undefined : Number(inputYear)).averageUnitRentArr)
      }
    })

    let cleandData = removeOutlier(tempArray)
    getEstimateBasedOnAvg(cleandData)
  }

  useEffect(() => {
    if (selectedOption) {
      getData()

      sendLark({
        message: `[賃料査定][${currentUser?.email}]: ${mapper[selectRecordType as keyof typeof mapper].name} ${selectedOption.type} ${selectedOption.value}`,
        url: LARK_URLS.USER_ACTIVITY_CHANNEL,
      })
    }
  }, [selectedOption, selectRecordType])

  useEffect(() => {
    if (selectedOption) {
      setTabsToShow(Object.keys(tabMapper).filter((tab) => tabMapper[tab as keyof typeof tabMapper].showForType.includes(selectRecordType) && tabMapper[tab as keyof typeof tabMapper].showForOption.includes(selectedOption?.type)))
    }
  }, [selectedOption, selectRecordType])


  useEffect(() => {
    if (inputYear && inputArea && inputRent) {
      setUnitRentArrayFromData(rentEstimate)
    }
  }, [inputYear, inputArea, inputRent])

  const getEstimateBasedOnAvg = (arrayUnitValue: number[]) => {
    let avg = arrayUnitValue.reduce((a, b) => a + b, 0) / arrayUnitValue.length;

    let eightyP = arrayUnitValue[Math.floor(arrayUnitValue.length * 0.8)];

    let res = {
      p80Unit: eightyP ? parseFloat(((eightyP * 3.3 / 10000).toFixed(2))) : 0,
      p80Price: eightyP ? parseFloat((eightyP * inputArea / 10000).toFixed(2)) : 0,
      avgUnit: avg ? parseFloat(((avg * 3.3 / 10000).toFixed(2))) : 0,
      avgPrice: avg ? parseFloat((avg * inputArea / 10000).toFixed(2)) : 0,
    }

    setEstimateSummary(res);
  }

  // Daily limit check for free users
  if (currentUser?.accessLevel === 1) {
    const pricingTier = getPricingTierForUser(currentUser);
    const dailyLimit = pricingTier?.dailyRentSearchCount || 5;
    const currentUsage = getTotalDailyUsageCount(DAILY_USAGE_FEATURES.RENT_SEARCH);

    if (isTotalDailyUsageLimitExceeded(DAILY_USAGE_FEATURES.RENT_SEARCH, dailyLimit)) {
      return <DailyLimitExhaustedBox
        pageType="rent"
        currentCount={currentUsage}
        maxCount={dailyLimit}
      />
    }
  }

  return (
    <div className="flex flex-col w-full">
      <div className="bg-neutral-100 rounded-lg p-1 py-2 m-2">
        <div className="p-1 flex flex-row justify-start items-center">
          <Tabs value={selectRecordType} onValueChange={(value) => {
            setSelectRecordType(value as "MANSION" | "BUILDING" | "HOUSE");
          }}>
            <TabsList>
              {["MANSION", "BUILDING", "HOUSE"].map((recordType) => <TabsTrigger key={recordType} value={recordType}>
                <div className="flex flex-row gap-2 items-center">
                  {mapper[recordType as keyof typeof mapper].iconSmall}
                  {mapper[recordType as keyof typeof mapper].name}
                </div>
              </TabsTrigger>)}
            </TabsList>
          </Tabs>
        </div>

        <div className="flex flex-col px-2 py-1 w-full">
          <SearchBarCombinedUI
            selectedOption={selectedOption}
            setSelectedOption={setSelectedOption}
            recordType={selectRecordType} />
        </div>
      </div>

      <Separator className="" />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 h-screen">
        <div className="col-span-1 flex flex-col border-r border-gray-200 ">

          <div className="flex flex-col  bg-gray-100 m-2 p-2 rounded-md border border-gray-400">
            <div className="flex flex-row gap-2 rounded-md">
              <div className="flex flex-col gap-1">
                <Label className="text-xs text-gray-800">築年数</Label>
                <Select
                  value={inputYear.toString()}
                  onValueChange={(value) => {
                    setInputYear(value)
                  }}
                >
                  <SelectTrigger className="w-[100px] bg-white">
                    <SelectValue placeholder="築年数" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全て</SelectItem>
                    <SelectItem value="10">~10年</SelectItem>
                    <SelectItem value="20">~20年</SelectItem>
                    <SelectItem value="30">~30年</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex flex-col gap-1">
                <Label className="text-xs text-gray-800">賃貸面積 (㎡)</Label>
                <Input
                  value={inputArea}
                  type="number"
                  min={0}
                  placeholder="例: 50"
                  className="w-full bg-white"
                  onChange={(e) => {
                    setInputArea(Number(e.target.value))
                  }}
                />
              </div>

              <div className="flex flex-col gap-1">
                <Label className="text-xs text-gray-800">現在賃料 (万円)</Label>
                <Input
                  value={inputRent}
                  type="number"
                  min={0}
                  placeholder="例: 15"
                  className="w-full bg-white  "
                  onChange={(e) => {
                    setInputRent(Number(e.target.value))
                  }}
                />
              </div>
            </div>
            <div className="text-xs text-gray-500 pt-2">
              {t("RentUtilities.messages.dataAutoReflect")}
            </div>
          </div>

          <div className="flex flex-col">
            <div className="m-2 text-center flex flex-col bg-gray-100 p-2 rounded-md">

              <div className="flex flex-row gap-2 items-center justify-center">
                査定賃料(単位: 万円)

                <Tooltip>
                  <TooltipTrigger asChild>
                    <InfoIcon className="w-4 h-4 text-gray-500" />
                  </TooltipTrigger>
                  <TooltipContent side="right" align="start">
                    {t("RentUtilities.messages.estimateTooltip")}
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="flex flex-row gap-2 text-3xl text-center items-end justify-center mt-2">
                {estimateSummary.avgPrice} ~ {estimateSummary.p80Price}
              </div>
              <div className="text-sm text-gray-500">
                約{estimateSummary.avgUnit} ~ {estimateSummary.p80Unit}万円/坪
              </div>
              {inputRent > 0 && <div className="mt-4 text-sm text-gray-500 flex flex-row gap-2 items-center justify-center">
                現賃料({inputRent}万円)は{estimateSummary.avgPrice > inputRent ? <div className="text-green-500">割安</div> : estimateSummary.p80Price < inputRent ? <div className="text-red-500">割高</div> : <div className="">正常</div>}
              </div>}
            </div>

            <Separator className="" />

            <div className="flex flex-col gap-2 m-2">
              {tabsToShow.map((tab) =>
                <div key={tab} className="cursor-pointer w-full" onClick={() => {
                  setSelectedResultTab(tab)
                }}>
                  <RentSummaryBox data={rentEstimate[tab as keyof typeof rentEstimate]} dataType={tab} isLoading={isLoading} selected={selectedResultTab === tab} tabMapper={tabMapper} t={t} />
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="col-span-2 p-2">
          {selectedOption ? <RightTable setSelectedResultTab={setSelectedResultTab} selectedResultTab={selectedResultTab} tabsToShow={tabsToShow} isLoading={isLoading} rentEstimate={rentEstimate} tabMapper={tabMapper} /> : <div className="flex flex-col gap-2 text-start text-gray-500">
            {t("RentUtilities.messages.searchToShowResults")}
          </div>}
        </div>
      </div>
    </div >
  )
} 