import { Building2Icon, HouseIcon, HotelIcon, Warehouse, BadgeJapaneseYen } from "lucide-react"
import dayjs from "dayjs"

// Function to create tab mapper with translations
export const createTabMapper = (t: (key: string) => string) => ({
  rentSamePropertyKubun: {
    label: t("RentUtilities.tabLabels.rentSamePropertyKubun"),
    labelShort: t("RentUtilities.tabLabels.rentSamePropertyKubunShort"),
    icon: <Building2Icon className="w-4 h-4" />,
    iconBig: <Building2Icon className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["MANSION"],
    showForOption: ["building"],
  },
  rentKubun: {
    label: t("RentUtilities.tabLabels.rentKubun"),
    labelShort: t("RentUtilities.tabLabels.rentKubunShort"),
    icon: <Building2Icon className="w-4 h-4" />,
    iconBig: <Building2Icon className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["MANSION"],
    showForOption: ["building", "station", "postalCode"],
  },
  rentHouse: {
    label: t("RentUtilities.tabLabels.rentHouse"),
    labelShort: t("RentUtilities.tabLabels.rentHouseShort"),
    icon: <HouseIcon className="w-4 h-4" />,
    iconBig: <HouseIcon className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["HOUSE"],
    showForOption: ["station", "postalCode", "area"]
  },
  rentIchibu: {
    label: t("RentUtilities.tabLabels.rentIchibu"),
    labelShort: t("RentUtilities.tabLabels.rentIchibuShort"),
    icon: <Warehouse className="w-4 h-4" />,
    iconBig: <Warehouse className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["MANSION", "BUILDING", "HOUSE"], // BUILDING_PART
    showForOption: ["station", "postalCode"]
  },
  rentAll: {
    label: t("RentUtilities.tabLabels.rentAll"),
    labelShort: t("RentUtilities.tabLabels.rentAllShort"),
    icon: <HotelIcon className="w-4 h-4" />,
    iconBig: <HotelIcon className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["BUILDING"],
    showForOption: ["station", "postalCode", "area"]
  },
  saleRecords: {
    label: t("RentUtilities.tabLabels.saleRecords"),
    labelShort: t("RentUtilities.tabLabels.saleRecordsShort"),
    icon: <BadgeJapaneseYen className="w-4 h-4" />,
    iconBig: <BadgeJapaneseYen className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["MANSION", "BUILDING", "HOUSE"],
    showForOption: ["station", "postalCode", "area"]
  },
})

// Legacy export for backward compatibility (will use Japanese as default)
export const tabMapper = {
  rentSamePropertyKubun: {
    label: "賃貸同物件",
    labelShort: "賃貸同",
    icon: <Building2Icon className="w-4 h-4" />,
    iconBig: <Building2Icon className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["MANSION"],
    showForOption: ["building"],
  },
  rentKubun: {
    label: "賃貸近隣区分",
    labelShort: "賃貸区分",
    icon: <Building2Icon className="w-4 h-4" />,
    iconBig: <Building2Icon className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["MANSION"],
    showForOption: ["building", "station", "postalCode"],
  },
  rentHouse: {
    label: "賃貸近隣戸建",
    labelShort: "賃貸戸建",
    icon: <HouseIcon className="w-4 h-4" />,
    iconBig: <HouseIcon className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["HOUSE"],
    showForOption: ["station", "postalCode", "area"]
  },
  rentIchibu: {
    label: "賃貸一部",
    labelShort: "賃貸一部",
    icon: <Warehouse className="w-4 h-4" />,
    iconBig: <Warehouse className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["MANSION", "BUILDING", "HOUSE"], // BUILDING_PART
    showForOption: ["station", "postalCode"]
  },
  rentAll: {
    label: "賃貸全て",
    labelShort: "賃貸全て",
    icon: <HotelIcon className="w-4 h-4" />,
    iconBig: <HotelIcon className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["BUILDING"],
    showForOption: ["station", "postalCode", "area"]
  },
  saleRecords: {
    label: "売買物件参考事例",
    labelShort: "売買物件",
    icon: <BadgeJapaneseYen className="w-4 h-4" />,
    iconBig: <BadgeJapaneseYen className="w-[24px] h-[24px] stroke-1" />,
    showForType: ["MANSION", "BUILDING", "HOUSE"],
    showForOption: ["station", "postalCode", "area"]
  },
}


export const getAverageRent = (data: any[], yearFilter?: number): {
  averageRent: number,
  averageRentArr: number[],
  averageUnitSize: number,
  averageUnitSizeArr: number[],
  averageUnitRent: number,
  averageUnitRentArr: number[],
} => {
  if (!data || data?.length === 0) return {
    averageRent: 0,
    averageRentArr: [],
    averageUnitSize: 0,
    averageUnitSizeArr: [],
    averageUnitRent: 0,
    averageUnitRentArr: [],
  };

  if (yearFilter) {
    console.log("🔥 yearFilter22", yearFilter)
    console.log("🔥 data", data)
  }

  let averageRentArr = [] as number[]; // in yen
  let averageUnitSizeArr = [] as number[];
  let averageUnitRentArr = [] as number[];
  data.filter((item) => {
    if (!item.feeRent) return false;
    if (!item.unitSize && !item.buildingSize) return false;

    if (yearFilter) {
      if (!item.buildingBuiltYear) return false;

      console.log("🔥 item", item)
      console.log("🔥 dayjs().diff(dayjs(item.buildingBuiltYear), 'year')", dayjs().year() - item.buildingBuiltYear)

      if (dayjs().year() - item.buildingBuiltYear > yearFilter) return false;
    }

    return true;
  }).forEach((item) => {
    let rent = (item.feeRent || 0) * 10000 + (item.feeManagement && item.feeManagement > 0 ? item.feeManagement : 0) + (item.feeUtility && item.feeUtility > 0 ? item.feeUtility : 0) || 0;
    averageRentArr.push(rent);

    let area = item.unitSize || item.buildingSize;
    averageUnitSizeArr.push(area);

    averageUnitRentArr.push(rent / area);
  });

  let averageRent = (averageRentArr.reduce((a, b) => a + b, 0) / data.length) / 10000;
  let averageUnitSize = averageUnitSizeArr.reduce((a, b) => a + b, 0) / data.length;
  let averageUnitRent = averageUnitRentArr.reduce((a, b) => a + b, 0) / data.length;

  return {
    averageRent: parseFloat(averageRent.toFixed(2)),
    averageRentArr: averageRentArr,
    averageUnitSize: parseFloat(averageUnitSize.toFixed(2)),
    averageUnitSizeArr: averageUnitSizeArr,
    averageUnitRent: parseFloat(averageUnitRent.toFixed(2)),
    averageUnitRentArr: averageUnitRentArr,
  };
}

export const getAverageRentFromSale = (data: any[], yearFilter?: number): {
  averageRent: number,
  averageRentArr: number[],
  averageUnitSize: number,
  averageUnitSizeArr: number[],
  averageUnitRent: number,
  averageUnitRentArr: number[],
} => {
  if (!data || data?.length === 0) return {
    averageRent: 0,
    averageRentArr: [],
    averageUnitSize: 0,
    averageUnitSizeArr: [],
    averageUnitRent: 0,
    averageUnitRentArr: [],
  };


  if (yearFilter) {
    console.log("🔥 yearFilter", yearFilter)
  }

  let averageRentArr = [] as number[]; // in yen
  let averageUnitSizeArr = [] as number[];
  let averageUnitRentArr = [] as number[];

  data.filter((item) => {
    if (!item.yearlyIncome) return false;
    if (!item.buildingSize && !item?.recordValues?.unitArea) return false;

    if (yearFilter) {
      if (dayjs().year() - item.buildingBuiltYear > yearFilter) return false;
    }

    return true;
  }).forEach((item) => {
    let area = item.buildingSize || item?.recordValues?.unitArea;

    if (area > 0) {
      averageRentArr.push(item.yearlyIncome * 10000 / 12);
      averageUnitSizeArr.push(area);
      averageUnitRentArr.push(item.yearlyIncome * 10000 / 12 / area);
    }

  });

  let averageRent = (averageRentArr.reduce((a, b) => a + b, 0) / data.length) / 10000;
  let averageUnitSize = averageUnitSizeArr.reduce((a, b) => a + b, 0) / data.length;
  let averageUnitRent = averageRent / averageUnitSize * 3.3;

  return {
    averageRent: parseFloat(averageRent.toFixed(2)),
    averageRentArr: averageRentArr,
    averageUnitSize: parseFloat(averageUnitSize.toFixed(2)),
    averageUnitSizeArr: averageUnitSizeArr,
    averageUnitRent: parseFloat(averageUnitRent.toFixed(2)),
    averageUnitRentArr: averageUnitRentArr,
  };
}