"use client";

import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";
import { getBuildingWithWeridName } from "@/actions/proBuilding";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { DataTable } from "@/components/ui/data-table";
import dayjs from "dayjs";
import { processBuildingName } from "@/lib/userLambdaRecord/building";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import Link from "next/link";

const columns = [
  {
    header: "ID",
    accessorKey: "id",
    cell: ({ row }: { row: any }) => {
      return <div>{row.original.id.toString().slice(0, 5)}</div>;
    },
  },
  {
    header: "名称",
    accessorKey: "name_ja",
  },
  {
    header: "名称（処理後）",
    cell: ({ row }: { row: any }) => {
      return <div>{row.original.name_ja ? processBuildingName(row.original.name_ja) : ""}</div>;
    },
  },
  {
    header: "住所",
    accessorKey: "address",
  },
  {
    header: "区分",
    cell: ({ row }: { row: any }) => {
      return row.original.is_kubun ? "Y" : "";
    },
  },
  {
    header: "作成日",
    accessorKey: "created_at",
    cell: ({ row }: { row: any }) => {
      return <div>{row.original.created_at ? dayjs(row.original.created_at).format("YYYY/MM/DD") : ""}</div>;
    },
  },
  // {
  //   header: "更新日",
  //   accessorKey: "updated_at",
  //   cell: ({ row }: { row: any }) => {
  //     return <div>{row.original.updated_at ? dayjs(row.original.updated_at).format("YYYY/MM/DD") : ""}</div>;
  //   },
  // },
  {
    header: "操作",
    cell: ({ row }: { row: any }) => {
      return <div>
        <Button variant="outline" size="sm" asChild>
          <Link href={`/su/data/building/${row.original.id}`}>詳細ページ</Link>
        </Button>
      </div>;
    },
  },
];

export default function BuildingPage() {
  const [buildings, setBuildings] = useState<ProBuildingProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(true);
    getBuildingWithWeridName().then((res) => {
      setBuildings(res.data);
      setIsLoading(false);
    });
  }, []);

  return <div>
    <div className="flex flex-row justify-between items-center p-4">
      <h1 className="text-2xl font-bold flex-1" aria-label="ビル修正">
        ビル修正
      </h1>
    </div>

    <Separator className="" />

    <div className="p-4 flex flex-col gap-4">
      <div className="text-sm text-gray-500" >合計: {buildings?.length}件</div>

      <DataTable columns={columns} data={buildings} isLoading={isLoading} defaultPageSize={500} />
    </div>
  </div>;
}
