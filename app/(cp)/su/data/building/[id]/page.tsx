"use client";

import { useParams } from "next/navigation";
import { getBuildingWithWeridName, getAllUsageForThisBuilding, getProBuildingById, getProBuildingFuzzyName, updateBuildingWithNewNumber } from "@/actions/proBuilding";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { useState, useEffect } from "react";
import { Separator } from "@/components/ui/separator";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { SystemReportViewHistoryProps } from "@/lib/definitions/system";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { processBuildingName } from "@/lib/userLambdaRecord/building";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { Spinner } from "@/components/ui/spinner";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";


export default function BuildingPage() {
  const { id } = useParams();
  const router = useRouter();
  const [building, setBuilding] = useState<ProBuildingProps | null>(null);
  const [improvedBuildings, setImprovedBuildings] = useState<any[]>([]);
  const [selectedImprovedBuilding, setSelectedImprovedBuilding] = useState<any>(null);
  const [userLambdaRecords, setUserLambdaRecords] = useState<any[]>([]);
  const [proRawSumitomoAuctions, setProRawSumitomoAuctions] = useState<any[]>([]);
  const [proBuildingHouseRents, setProBuildingHouseRents] = useState<any[]>([]);
  const [proMansionRents, setProMansionRents] = useState<any[]>([]);
  const [systemReportViewHistories, setSystemReportViewHistories] = useState<any[]>([]);
  const [newUserLambdaRecords, setNewUserLambdaRecords] = useState<any[]>([]);
  const [newProRawSumitomoAuctions, setNewProRawSumitomoAuctions] = useState<any[]>([]);
  const [newProBuildingHouseRents, setNewProBuildingHouseRents] = useState<any[]>([]);
  const [newProMansionRents, setNewProMansionRents] = useState<any[]>([]);
  const [newSystemReportViewHistories, setNewSystemReportViewHistories] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [newBuildingName, setNewBuildingName] = useState("");
  // useEffect(() => {
  //   getBuildingWithWeridName(id as string).then((res) => {
  //     setBuilding(res.data);
  //   });
  // }, [id]);

  useEffect(() => {
    getProBuildingById({ id: id as string, isSystem: true }).then((res: any) => {
      setBuilding(res.data);
    });

    getAllUsageForThisBuilding(id as string).then((res) => {
      setUserLambdaRecords(res.data.userLambdaRecords);
      setProRawSumitomoAuctions(res.data.proRawSumitomoAuctions);
      setProBuildingHouseRents(res.data.proBuildingHouseRents);
      setProMansionRents(res.data.proMansionRents);
      setSystemReportViewHistories(res.data.systemReportViewHistories);
    });
  }, [id]);

  useEffect(() => {
    if (building && building.nameJa) {
      searchForMatch(processBuildingName(building?.nameJa as string));
    }
  }, [building]);

  const searchForMatch = (name: string) => {
    setIsLoading(true);
    getProBuildingFuzzyName({ name, isFuzzy: false }).then((res) => {
      setImprovedBuildings(res.data);
      setIsLoading(false);
    });
  }

  const getDetailsForNewBuilding = (buildingId: string) => {
    setIsLoading(true);
    getAllUsageForThisBuilding(buildingId as string).then((res) => {
      setNewUserLambdaRecords(res.data.userLambdaRecords);
      setNewProRawSumitomoAuctions(res.data.proRawSumitomoAuctions);
      setNewProBuildingHouseRents(res.data.proBuildingHouseRents);
      setNewProMansionRents(res.data.proMansionRents);
      setNewSystemReportViewHistories(res.data.systemReportViewHistories);
      setIsLoading(false);
    });
  }
  return <div>
    <div className="flex flex-row justify-between items-center p-4">
      <h1 className="text-2xl font-bold flex-1" aria-label="ビル修正">ビル修正: {building?.nameJa}[Id: {building?.id}]</h1>
    </div>

    <Separator className="" />

    <div className="p-2">
      <div className="flex flex-row gap-4 grid grid-cols-2">
        <div className="p-4 flex flex-col gap-2 bg-gray-100 rounded-lg">
          <strong>TllUserLambdaRecord:</strong>
          {userLambdaRecords.map((record) => {
            return <div key={record.id}>{record.id}</div>
          })}

          <Separator className="" />

          <strong>ProRawSumitomoAuction:</strong>
          {proRawSumitomoAuctions.map((record) => {
            return <div key={record.id}>{record.id}</div>
          })}

          <Separator className="" />

          <strong>ProBuildingHouseRent:</strong>
          {proBuildingHouseRents.map((record) => {
            return <div key={record.id}>{record.id}</div>
          })}

          <Separator className="" />

          <strong>ProMansionRent:</strong>
          {proMansionRents.map((record) => {
            return <div key={record.id}>{record.id}</div>
          })}

          <Separator className="" />

          <strong>SystemReportViewHistory:</strong>
          {systemReportViewHistories.map((record) => {
            return <div key={record.id}>{record.id}</div>
          })}
        </div>

        <div className="flex flex-col gap-4">
          <div className="p-4 flex flex-col gap-4 bg-gray-100 rounded-lg">
            <div>
              <strong>処理後:</strong>
              {building?.nameJa && processBuildingName(building?.nameJa as string)}
            </div>

            <div className="flex flex-row gap-2 items-center justify-start">
              <Input type="text" placeholder="新しいビル名" value={newBuildingName} onChange={(e) => setNewBuildingName(e.target.value)} />
              <Button variant="outline" size="sm" onClick={() => {
                searchForMatch(newBuildingName);
              }}>
                検索
              </Button>
            </div>

            <div>
              <strong>詳細:
                {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : ""}
              </strong>
              {improvedBuildings.map((building) => {
                return <div key={building.id} className="flex flex-row gap-2 items-center justify-between">
                  {building.id} :
                  {building.nameJa}
                  <Button variant="outline" size="sm" onClick={() => {
                    getDetailsForNewBuilding(building.id);
                    setSelectedImprovedBuilding(building);
                  }}>
                    {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "詳細を取得"}
                  </Button>
                </div>
              })}
            </div>
          </div>

          <div className="p-4 flex flex-col gap-2 bg-gray-100 rounded-lg">
            <div className="flex flex-row gap-2 items-center justify-start flex-wrap">
              <strong>TllUserLambdaRecord: [{newUserLambdaRecords.length} Records]</strong>
              {newUserLambdaRecords.map((record) => {
                return <div key={record.id}>{record.id}</div>
              })}
            </div>


            <Separator className="" />

            <div className="flex flex-row gap-2 items-center justify-start flex-wrap">
              <strong>ProRawSumitomoAuction: [{newProRawSumitomoAuctions.length} Records]</strong>
              {newProRawSumitomoAuctions.map((record) => {
                return <div key={record.id}>{record.id}</div>
              })}
            </div>

            <Separator className="" />

            <div className="flex flex-row gap-2 items-center justify-start flex-wrap">
              <strong>ProBuildingHouseRent: [{newProBuildingHouseRents.length} Records]</strong>
              {newProBuildingHouseRents.map((record) => {
                return <div key={record.id}>{record.id}</div>
              })}
            </div>
            <Separator className="" />

            <div className="flex flex-row gap-2 items-center justify-start flex-wrap">
              <strong>ProMansionRent: [{newProMansionRents.length} Records]</strong>
              {newProMansionRents.map((record) => {
                return <div key={record.id}>{record.id}</div>
              })}
            </div>
            <Separator className="" />

            <div className="flex flex-row gap-2 items-center justify-start flex-wrap">
              <strong>SystemReportViewHistory: [{newSystemReportViewHistories.length} Records]</strong>
              {newSystemReportViewHistories.map((record) => {
                return <div key={record.id}>{record.id}</div>
              })}
            </div>

            <Separator className="" />

            <Button variant="outline" size="sm" disabled={!selectedImprovedBuilding} onClick={async () => {
              setIsLoading(true);
              const res = await updateBuildingWithNewNumber({ oldId: building?.id as string, newId: selectedImprovedBuilding?.id });
              if (res.success) {
                toast({
                  title: "Building updated",
                  description: "Building updated successfully.. loading back to the page",
                });
                router.push(`/su/data/building`);
              } else {
                toast({
                  title: "Failed to update building",
                  description: "Failed to update building",
                });
              }
              setIsLoading(false);
            }}>
              {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "Update with: " + selectedImprovedBuilding?.nameJa}
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
}

