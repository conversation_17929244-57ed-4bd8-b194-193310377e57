"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import { getAllUsageForThisRecord, deleteAllUsageForThisRecord } from "@/actions/tllUserLambdaRecordMerge";
import { Loader2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";

export default function MergePage() {
  const [search, setSearch] = useState("");
  const [searchResult, setSearchResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = async () => {
    if (!search) return;

    setIsLoading(true);
    const res = await getAllUsageForThisRecord(search);
    if (res.success) {
      setSearchResult(res.data);
    }
    setIsLoading(false);
  }

  const handleDelete = async () => {
    if (!search) return;

    if (!confirm("データを削除しますか？")) return;

    setIsLoading(true);
    const res = await deleteAllUsageForThisRecord(search);
    if (res.success) {
      toast({
        title: "データ削除完了",
        description: "データ削除が完了しました",
      });
      setSearchResult(null);
    }
    setIsLoading(false);
  }

  return <div>
    <div className="flex flex-col gap-2 p-4">
      <h1 className="text-2xl font-bold " aria-label="データ統合">
        データ統合
      </h1>
    </div>

    <Separator />

    <div className="flex flex-row gap-2 p-4">
      <Input placeholder="データ検索" value={search} onChange={(e) => setSearch(e.target.value)} />

      <Button onClick={handleSearch}>
        {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "データ検索"}
      </Button>
    </div>

    <Separator />

    <div className="grid grid-cols-2 gap-2 p-2">
      <div className="flex flex-col gap-2 p-2">
        <h2 className="text-lg font-bold">データ検索結果</h2>

        <Separator />

        {searchResult && (
          <div className="flex flex-col gap-2">
            {Object.keys(searchResult).map((key) => (
              <div key={key}>
                <h3 className="text-md font-bold">{key}</h3>
                {searchResult[key].length > 0 ? (
                  <pre>{JSON.stringify(searchResult[key], null, 2)}</pre>
                ) : (
                  <p className="text-sm text-gray-500">データなし</p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="flex flex-col gap-2 p-2">
        <div className="flex flex-col gap-2 bg-gray-100 p-2 rounded-lg">
          <h2 className="text-lg font-bold">データ削除</h2>
          <Separator />
          <div className="flex flex-row gap-2">
            <Button onClick={handleDelete} disabled={!search || isLoading} variant="destructive" className="w-full">
              {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "データ削除"}
            </Button>
          </div>
        </div>

        <div className="flex flex-col gap-2 bg-gray-100 p-2 rounded-lg">
          <h2 className="text-lg font-bold">データ統合</h2>
          <Separator />
          <div className="flex flex-row gap-2">
            <Input className="w-full" placeholder="New id" />

            <Button>データ統合</Button>
          </div>
        </div>
      </div>
    </div>
  </div>;
}