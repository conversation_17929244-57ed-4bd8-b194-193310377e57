"use client";

import { DataTable } from "@/components/ui/data-table";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { useEffect, useState } from "react";
import { useAuthStore } from "@/store/auth";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { ColumnDef } from "@tanstack/react-table";
import { getNullNearestStationWalkMinuteRecords, getNullStationGroupIdRecords, getRentMansionNullBuldingIdRecords, getRentBuildingNullBuldingIdRecords } from "@/actions/tllUserLambdaRecordAdmin";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { mansionRentForRefillColumns, buildingRentForRefillColumns } from "@/app/(cp)/an/mansion/[id]/mansionRentColumns";

export default function DataPage() {
  const [selectedTab, setSelectedTab] = useState("nullStationGroupId");
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const { currentUser } = useAuthStore();
  const [nullStationGroupIdRecords, setNullStationGroupIdRecords] = useState<UserLambdaRecordProps[]>([]);
  const [nullNearestStationWalkMinuteRecords, setNullNearestStationWalkMinuteRecords] = useState<UserLambdaRecordProps[]>([]);
  const [rentMansionNullBuldingIdRecords, setRentMansionNullBuldingIdRecords] = useState<ProMansionRentProps[]>([]);
  const [rentBuildingNullBuldingIdRecords, setRentBuildingNullBuldingIdRecords] = useState<ProBuildingHouseRentProps[]>([]);

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (currentUser) {
      setColumns(getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps }));
    }
  }, [currentUser]);

  useEffect(() => {
    setIsLoading(true);

    getNullStationGroupIdRecords().then((records) => {
      setNullStationGroupIdRecords(records as UserLambdaRecordProps[]);
      setIsLoading(false);
    });
  }, []);

  return <div>
    <div className="flex flex-row justify-between items-center p-4">
      <h1 className="text-2xl font-bold flex-1" aria-label="データ修正">データ修正</h1>
    </div>

    <Separator className="" />

    <div className="p-4 flex flex-col gap-4">
      <Tabs value={selectedTab} onValueChange={(value) => {
        setSelectedTab(value);

        if (value === "nullNearestStationWalkMinute") {
          setIsLoading(true);
          getNullNearestStationWalkMinuteRecords().then((records) => {
            setNullNearestStationWalkMinuteRecords(records as UserLambdaRecordProps[]);
            setIsLoading(false);
          });
        }

        if (value === "rentMansionNullBuldingId") {
          setIsLoading(true);
          getRentMansionNullBuldingIdRecords().then((records) => {
            setRentMansionNullBuldingIdRecords(records as ProMansionRentProps[]);
            setIsLoading(false);
          });
        }

        if (value === "rentBuildingNullBuldingId") {
          setIsLoading(true);
          getRentBuildingNullBuldingIdRecords().then((records) => {
            setRentBuildingNullBuldingIdRecords(records as ProBuildingHouseRentProps[]);
            setIsLoading(false);
          });
        }

      }}>
        <TabsList>
          <TabsTrigger value="nullStationGroupId">最寄駅グループID=NULL</TabsTrigger>
          <TabsTrigger value="nullNearestStationWalkMinute">徒歩分=NULL</TabsTrigger>
          <TabsTrigger value="rentMansionNullBuldingId">マンション賃貸BUILDINGID=NULL</TabsTrigger>
          <TabsTrigger value="rentBuildingNullBuldingId">一棟賃貸BUILDINGID=NULL</TabsTrigger>
          {/* <TabsTrigger value="Empty Nearest Station Group Id">空の最寄駅グループID</TabsTrigger> */}
        </TabsList>
      </Tabs>

      {selectedTab === "nullStationGroupId" && <div className="flex flex-col gap-2">
        <div className="text-sm text-gray-500" >合計: {nullStationGroupIdRecords.length}件</div>

        <DataTable columns={columns} data={nullStationGroupIdRecords} isLoading={isLoading} />
      </div>}

      {selectedTab === "nullNearestStationWalkMinute" && <div className="flex flex-col gap-2">
        <div className="text-sm text-gray-500" >合計: {nullNearestStationWalkMinuteRecords.length}件</div>

        <DataTable columns={columns} data={nullNearestStationWalkMinuteRecords} isLoading={isLoading} />
      </div>}

      {selectedTab === "rentMansionNullBuldingId" && <div className="flex flex-col gap-2">
        <div className="text-sm text-gray-500" >合計: {rentMansionNullBuldingIdRecords.length}件</div>

        <DataTable columns={mansionRentForRefillColumns} data={rentMansionNullBuldingIdRecords} isLoading={isLoading} />
      </div>}


      {selectedTab === "rentBuildingNullBuldingId" && <div className="flex flex-col gap-2">
        <div className="text-sm text-gray-500" >合計: {rentBuildingNullBuldingIdRecords.length}件</div>

        <DataTable columns={buildingRentForRefillColumns} data={rentBuildingNullBuldingIdRecords} isLoading={isLoading} />
      </div>}

    </div>
  </div>;
}
