"use client";

import { Separator } from "@/components/ui/separator";
import { getTllProperties } from "@/actions/tllProperty";
import { useEffect, useState } from "react";
import { Spinner } from "@/components/ui/spinner";
import { DataTable } from "@/components/ui/data-table";
import { getPriceChangeColumnsBasedOnAccessLevel, priceChangeColumns } from "@/app/(cp)/ex/search/[id]/(details)/(priceChange)/priceChangeColumns";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useAuthStore } from "@/store/auth";
import { ColumnDef } from "@tanstack/react-table";
import { Link, Loader2, TrashIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { deletePriceChange } from "@/actions/tllUserLambdaRecordPriceChange";
import { useRouter } from "next/navigation";
import { toast } from "@/hooks/use-toast";
import { useToast } from "@/hooks/use-toast";

export default function TllBuildingPage() {
  const [tllProperties, setTllProperties] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { currentUser } = useAuthStore();
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const router = useRouter();

  useEffect(() => {
    setIsLoading(true);
    getTllProperties().then((res) => {
      setTllProperties(res);
      setIsLoading(false);
    });
  }, []);

  useEffect(() => {
    if (currentUser) {
      setColumns(getPriceChangeColumnsBasedOnAccessLevel(currentUser as TllUserProps));
    }
  }, [currentUser]);

  return <div>
    <div className="flex flex-row justify-between items-center p-4">
      <h1 className="text-2xl font-bold flex-1" aria-label="TLL物件">TLL物件</h1>
    </div>

    <Separator className="" />

    <div className="p-2">
      {isLoading ? <div className="flex justify-center items-center h-full">
        <Spinner />
      </div> : ""}
      <div className="flex flex-col gap-2">
        {tllProperties.map((property) => {
          return <div key={property.id} className="p-2 bg-gray-100 rounded-lg">
            <div>{property.recordId} - {property.title} - {property.address}</div>
            <div>
              {property.userLambdaRecords?.priceChanges?.length > 0 && <DataTable columns={[
                ...columns,
                {
                  header: '操作',
                  cell: ({ row }: { row: any }) => <div>
                    <Button variant="destructive" size="icon" onClick={() => {
                      setIsLoading(true);
                      deletePriceChange(row.original.id).then((res) => {
                        setIsLoading(false);
                        toast({
                          title: "成功",
                          description: "価格改正を削除しました",
                        });
                        // window.location.reload();
                      });
                    }}>
                      {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : <TrashIcon className="w-4 h-4" />}
                    </Button>
                  </div>,
                },

              ]} data={property.userLambdaRecords?.priceChanges.sort((a: any, b: any) => new Date(a.recordDate).getTime() - new Date(b.recordDate).getTime())} />}
            </div>
          </div>;
        })}
      </div>
    </div>
  </div>;
}