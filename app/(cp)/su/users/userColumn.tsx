import { TllUserProps } from "@/lib/definitions/tllUser";
import dayjs from "dayjs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useState } from "react";

const findLastLoginMethod = (user: TllUserProps) => {
  let loginHistory = user.loginHistory;
  if (loginHistory && loginHistory.length > 0) {
    return <Badge variant="outline" className="text-align-center"> {loginHistory[loginHistory.length - 1].loginMethod?.trim()}</Badge>;
  }
  return '';
}

export const columns = (router: any, setOpen: any, setUserIdToDelete: any) => [
  { header: "#", cell: ({ row }: { row: any }) => row.index + 1 },
  { header: "名前", accessorKey: "name" },
  { header: "メール", accessorKey: "email" },
  { header: "ソース", accessorKey: "source" },
  { header: "言語", accessorKey: "language" },
  { header: "電話", accessorKey: "phone" },
  { header: "アクセスレベル", accessorKey: "accessLevel" },
  {
    header: "最終ログイン日時", accessorKey: "lastLoginAt", cell: ({ row }: { row: { original: { lastLoginAt?: string } } }) => <div className="flex flex-col gap-1 justify-center items-center">
      <div>
        {row.original.lastLoginAt ? dayjs(row.original.lastLoginAt).format('YYYY-MM-DD HH:mm') : ''}
      </div>

      {findLastLoginMethod(row.original as TllUserProps)}
    </div>
  },

  { header: "Sub Plan", accessorKey: "subscriptionPlan", render: ({ row }: { row: { original: { subscriptionPlan?: string } } }) => row.original.subscriptionPlan ? <Badge variant="default">{row.original.subscriptionPlan}</Badge> : '' },

  { header: "Sub Status", accessorKey: "subscriptionStatus", render: ({ row }: { row: { original: { subscriptionStatus?: string } } }) => row.original.subscriptionStatus ? <Badge variant="outline">{row.original.subscriptionStatus}</Badge> : '' },

  { header: "作成日時", accessorKey: "createdAt", cell: ({ row }: { row: { original: { createdAt?: string } } }) => row.original.createdAt ? dayjs(row.original.createdAt).format('YYYY-MM-DD') : '' },

  {
    header: "操作",
    accessorKey: "edit",
    minWidth: 100,
    cell: ({ row }: { row: { original: { id: string } } }) => (
      <div className="flex space-x-2">
        <Button
          onClick={() => router.push(`/su/users/${row.original.id}`)}
          variant="outline"
          size="sm"
        >
          修正
        </Button>
        <Button
          onClick={() => {
            setOpen(true);
            setUserIdToDelete(row.original.id); // 削除するユーザーのIDを設定
          }}
          variant="destructive"
          size="sm"
        >
          削除
        </Button>
      </div>
    ),
  },
];