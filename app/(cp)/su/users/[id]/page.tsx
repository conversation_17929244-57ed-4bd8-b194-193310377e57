"use client";

import { useEffect, useState } from "react";
import { useUserStore } from "@/store/user"; // ユーザーストアをインポート
import { useRouter } from 'next/navigation'; // ルーターをインポート
import { Separator } from "@/components/ui/separator"; // セパレーターをインポート

import { useParams } from 'next/navigation';
import { Spinner } from "@/components/ui/spinner"; // ローディングスピナーをインポート
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useForm } from "react-hook-form";
import UserForm from "@/components/UserForm"; // 导入新的用户表单组件
import { updateUserAction } from "@/actions/users";
import { TllUserFormSchema, TllUserProps } from "@/lib/definitions/tllUser";
import { getUserById } from "@/actions/users";
import { Switch } from "@/components/ui/switch";

const UserDetailPage = () => {
  const router = useRouter();
  const { id } = useParams();
  const { users, updateUser } = useUserStore(); // ユーザーストアを取得
  const [userDetails, setUserDetails] = useState<TllUserProps | null>(null); // ユーザー詳細の状態
  const [isLoading, setIsLoading] = useState(true); // ロード状態
  const [isNewUser, setIsNewUser] = useState(false);
  const [sendEmail, setSendEmail] = useState(false);

  const form = useForm<z.infer<typeof TllUserFormSchema>>({
    resolver: zodResolver(TllUserFormSchema),
    defaultValues: {
      id: "",
      name: "",
      email: "",
      password: "",
      accessLevel: 1,
    },
  })

  useEffect(() => {
    if (id) {
      setIsLoading(true); // データのロードが完了
      getUserById(id as string).then((data) => {
        let user = data.data;
        setUserDetails(user);

        form.reset({
          id: user.id,
          name: user.name || '',
          email: user.email || '',
          accessLevel: user.accessLevel || 1,
        });
      }).finally(() => {
        setIsLoading(false); // データのロードが完了
      });
    }
  }, []);

  const handleSubmit = async (data: z.infer<typeof TllUserFormSchema>) => {
    const response = await updateUserAction({ id: data.id as string, data: data, sendEmail: sendEmail });
    await updateUser(data as TllUserProps);

    if (response) {
      router.push("/su/users"); // 提交后返回用户列表
    } else {
      console.error("ユーザー情報の更新に失敗しました。");
    }
  };

  return (
    <div className="">
      <div className="p-4">
        <h1 className="text-2xl font-bold">ユーザー詳細</h1>
      </div>

      <Separator className="my-2" />

      <div className="p-4">
        メールを送る：<Switch
          checked={sendEmail}
          onCheckedChange={(checked) => {
            setSendEmail(checked);
          }}
        />
      </div>

      <div className="p-4">
        {!isLoading ? <UserForm onSubmit={handleSubmit} defaultValues={userDetails as TllUserProps} isNewUser={false} /> : <Spinner />} {/* 使用新的用户表单组件 */}
      </div>
    </div>
  );
};

export default UserDetailPage;
