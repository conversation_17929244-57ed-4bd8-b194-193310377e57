"use client"

import { useUserStore } from "@/store/user";
import { DataTable } from "@/components/ui/data-table"; // DataTableコンポーネントをインポート
import { Separator } from "@/components/ui/separator";
import { useRouter } from 'next/navigation'; // useRouterをインポート
import dayjs from 'dayjs';
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogAction, AlertDialogCancel } from "@/components/ui/alert-dialog";
import { useState } from "react";
import { Button } from "@/components/ui/button"; // shadcnボタンをインポート
import { useToast } from "@/hooks/use-toast";
import { deleteUserAction } from "@/actions/users";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, PlusIcon } from "lucide-react";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { columns } from "./userColumn";

export default function UserPage() {
  const { users, deleteUser } = useUserStore(); // useUserStoreからdeleteUserメソッドを取得
  const { toast } = useToast();
  const [accessValue, setAccessValue] = useState("all");
  const [subscriptionPlan, setSubscriptionPlan] = useState("all");

  const router = useRouter(); // routerを初期化
  const [open, setOpen] = useState(false); // ダイアログの状態を制御
  const [userIdToDelete, setUserIdToDelete] = useState<string | null>(null); // 削除するユーザーのIDを保存


  const handleDelete = async () => {
    if (userIdToDelete) {
      const response = await deleteUserAction.bind(null, userIdToDelete)();
      if (response.success) {
        toast({
          title: "削除成功",
          description: "ユーザーが正常に削除されました！",
        });
        deleteUser(userIdToDelete);
        setUserIdToDelete(null); // 削除するユーザーのIDをリセット
        setOpen(false);
      }
    }
  };

  const filteredUsers = (user: TllUserProps) => {
    if (accessValue !== "all" && user.accessLevel !== parseInt(accessValue)) return false;

    if (subscriptionPlan !== "all" && user.subscriptionPlan !== subscriptionPlan) return false;

    return true;
  }

  const accessLevelList = {
    "all": "All",
    "1": "Free",
    "10": "Plus",
    "20": "Pro",
    "30": "Partner",
    "90": "Admin",
    "99": "Super Admin"
  }

  const subscriptionPlanList = {
    "all": "All",
    "FREE": "Free",
    "PLUS": "Plus",
    "PRO": "Pro",
  }

  return (
    <div className="">
      <div className="flex flex-row items-center justify-between p-2 sm:p-4">
        <h1 className="text-2xl font-bold">ユーザー一覧</h1>

        <Link href="/su/users/new">
          <Button variant="outline">
            <PlusIcon className="w-4 h-4" />
            新規ユーザー
          </Button>
        </Link>
      </div>
      <Separator className="" />


      <div className="p-2 sm:p-4 flex flex-col gap-2">
        <div className="flex flex-row items-center justify-start gap-2 flex-wrap">
          <div className="text-sm text-neutral-500">アクセス: </div>
          <Tabs value={accessValue} onValueChange={(value) => {
            setAccessValue(value);
          }}>
            <TabsList>
              {Object.keys(accessLevelList).map((key) => (
                <TabsTrigger key={key} value={key}>{accessLevelList[key as keyof typeof accessLevelList]}
                  <Badge variant="outline" className="ml-2">
                    {users?.filter((user) => key === "all" ? true : user.accessLevel === parseInt(key)).length}
                  </Badge>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>

          <div className="text-sm text-neutral-500">プラン: </div>
          <Tabs value={subscriptionPlan} onValueChange={(value) => {
            setSubscriptionPlan(value);
          }}>
            <TabsList>
              {Object.keys(subscriptionPlanList).map((key) => (
                <TabsTrigger key={key} value={key}>{subscriptionPlanList[key as keyof typeof subscriptionPlanList]}
                  <Badge variant="outline" className="ml-2">
                    {users?.filter((user) => key === "all" ? true : user.subscriptionPlan === key).length}
                  </Badge>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>

        <Separator className="" />

        <div className="text-sm text-neutral-500"> Total: {users?.length} Records </div>

        {users ? (
          <DataTable data={users.filter(filteredUsers).sort((a, b) =>
            dayjs(b.lastLoginAt ?? 0).diff(dayjs(a.lastLoginAt ?? 0))
          ) as any} columns={columns(router, setOpen, setUserIdToDelete) as any} defaultPageSize={500} />
        ) : (
          <p>
            <Loader2 className="w-4 h-4 animate-spin" />
            読み込み中...</p>
        )}

        <AlertDialog open={open} onOpenChange={setOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>削除の確認</AlertDialogTitle>
              <AlertDialogDescription>
                このユーザーを削除してもよろしいですか？
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>キャンセル</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete}>削除</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};
