"use client";

import React, { useState } from 'react';
import UserForm from "@/components/UserForm"; // 新しいユーザーフォームコンポーネントをインポート
import { z } from "zod";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { createUserAction } from "@/actions/users";
import { useUserStore } from '@/store/user';
import { TllUserFormSchema, TllUserProps } from "@/lib/definitions/tllUser";
import { Separator } from "@/components/ui/separator";
export default function CreateUserPage() {
  const router = useRouter();
  const { toast } = useToast()
  const { addUser } = useUserStore();

  const handleSubmit = async (data: z.infer<typeof TllUserFormSchema>) => {
    const response = await createUserAction(data as TllUserProps);

    if (response.success) {
      addUser(response.data as TllUserProps);

      toast({
        title: "ユーザー作成成功",
        description: "ユーザーが作成されました。ユーザーリストページにリダイレクトします。",
        duration: 2000,
      });

      router.push("/su/users");
    } else {
      toast({
        title: "ユーザー作成失敗",
        description: response.message,
        duration: 10000,
      });
    }
  };

  return (
    <div className="">
      <div className="p-4">
        <h1 className="text-2xl font-bold">ユーザー作成</h1>
      </div>

      <Separator className="" />

      <div className="p-4">
        <UserForm onSubmit={handleSubmit} isNewUser={true} /> {/* 新しいユーザーフォームコンポーネントを使用 */}
      </div>
    </div>
  );
};
