"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "recharts"

import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { getSystemMetricsChangeRecordsDetailedAction } from "@/actions/systemMetrics"
import { useEffect, useState } from "react"
import { sum } from "lodash-es"

const chartData = [
  { browser: "chrome", visitors: 275, fill: "var(--color-chrome)" },
  { browser: "safari", visitors: 200, fill: "var(--color-safari)" },
  { browser: "firefox", visitors: 187, fill: "var(--color-firefox)" },
  { browser: "edge", visitors: 173, fill: "var(--color-edge)" },
  { browser: "other", visitors: 90, fill: "var(--color-other)" },
]

export function ChartForChangeRecord() {
  const [chartData, setChartData] = useState<any[]>([]);

  useEffect(() => {
    getSystemMetricsChangeRecordsDetailedAction().then((res) => {
      setChartData(res.data as any);
    });
  }, []);

  let chartConfig = {} as any;
  if (chartData.length > 0) {
    chartData.forEach((item: any, index: number) => {
      chartConfig[item.source] = {
        label: item.source,
        color: `hsl(var(--chart-${index + 1}))`,
      }
    })
  }

  return (
    <>
      <div className="text-sm text-neutral-500 mb-4">合計データ数 : {sum(chartData.map((item: any) => item._count))}</div>

      {chartData.length > 0 ? <ChartContainer
        config={chartConfig}
        className="mx-auto aspect-square max-h-[250px]"
      >
        <PieChart>
          <ChartTooltip
            cursor={false}
            content={<ChartTooltipContent hideLabel />}
          />
          <Legend />
          <Pie data={chartData} dataKey="_count" nameKey="source" />
        </PieChart>
      </ChartContainer> : <div className="text-sm text-neutral-500 mb-4">データがありません</div>}

      {JSON.stringify(chartData)}
    </>
  )
}
