"use client"

import { getSystemMetricsAction } from "@/actions/systemMetrics"
import { DataTable } from "@/components/ui/data-table"
import { SystemMetricProps } from "@/lib/definitions/system"
import { SystemMetricKeyEnum } from "@prisma/client"
import dayjs from "dayjs"
import { useEffect, useState } from "react"
import { SystemMetricsChart } from "./SystemMetricsChart"
import { ChartForChangeRecord } from "./ChartForChangeRecord"

export default function System() {
  const [systemData, setSystemData] = useState<SystemMetricProps[]>([]);

  const getSystemData = async () => {
    const res = await getSystemMetricsAction();
    setSystemData(res.data as SystemMetricProps[]);
  }

  useEffect(() => {
    getSystemData();
  }, []);

  let mapper = {
    // TOTAL_PROPERTY_BUILDING_RECORD_COUNT: "総一棟収益物件数",
    // TOTAL_PROPERTY_HOUSE_RECORD_COUNT: "総戸建物件数",
    // TOTAL_PROPERTY_LAND_RECORD_COUNT: "総土地物件数",
    // TOTAL_PROPERTY_MANSION_RECORD_COUNT: "総マンション物件数",
    TOTAL_PROPERTY_CHANGE_RECORD_COUNT: "総変更物件数",
    // TOTAL_PROPERTY_BUILDING_RENT_RECORD_COUNT: "総一棟収益物件賃貸数",
    // TOTAL_PROPERTY_HOUSE_RENT_RECORD_COUNT: "総戸建賃貸数",
    // TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT: "総マンション賃貸数",
    TOTAL_USER_COUNT: "総ユーザー",
    TOTAL_USER_PAID_COUNT: "総有料ユーザー",

    TOTAL_VALUATION_RECORD_COUNT: "総査定数",
    TOTAL_USER_DAU_COUNT: "総DAU",
    TOTAL_USER_WAU_COUNT: "総WAU",
    TOTAL_USER_MAU_COUNT: "総MAU",
    TOTAL_MRR: "総MRR",
  }

  let columns = [
    {
      header: "作成日",
      accessorKey: "recordDate",
      cell: ({ row }: { row: any }) => {
        return <div>{dayjs(row.original.recordDate).format("YYYY-MM-DD")}</div>
      }
    },
    {
      header: "総売買数",
      cell: ({ row }: { row: any }) => {
        return <div className="text-center flex flex-col items-center justify-center" >
          <div className="text-sm">{parseInt(row.original.TOTAL_PROPERTY_BUILDING_RECORD_COUNT) + parseInt(row.original.TOTAL_PROPERTY_HOUSE_RECORD_COUNT) + parseInt(row.original.TOTAL_PROPERTY_LAND_RECORD_COUNT) + parseInt(row.original.TOTAL_PROPERTY_MANSION_RECORD_COUNT)}
          </div>

          <div className="flex flex-row gap-2">
            <div className="text-xs text-gray-500">
              一棟: {parseInt(row.original.TOTAL_PROPERTY_BUILDING_RECORD_COUNT).toLocaleString()}
            </div>
            <div className="text-xs text-gray-500">
              マンション: {parseInt(row.original.TOTAL_PROPERTY_MANSION_RECORD_COUNT).toLocaleString()}
            </div>
          </div>
          <div className="flex flex-row gap-2">
            <div className="text-xs text-gray-500">
              戸建: {parseInt(row.original.TOTAL_PROPERTY_HOUSE_RECORD_COUNT).toLocaleString()}
            </div>
            <div className="text-xs text-gray-500">
              土地: {parseInt(row.original.TOTAL_PROPERTY_LAND_RECORD_COUNT).toLocaleString()}
            </div>
          </div>
        </div>
      }
    },

    {
      header: "総賃貸数",
      cell: ({ row }: { row: any }) => {
        return <div className="text-center flex flex-col items-center justify-center" >
          <div className="text-sm">{parseInt(row.original.TOTAL_PROPERTY_BUILDING_RENT_RECORD_COUNT) + parseInt(row.original.TOTAL_PROPERTY_HOUSE_RENT_RECORD_COUNT) + parseInt(row.original.TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT)}
          </div>

          <div className="flex flex-row gap-2">
            <div className="text-xs text-gray-500">
              一棟: {parseInt(row.original.TOTAL_PROPERTY_BUILDING_RENT_RECORD_COUNT).toLocaleString()}
            </div>
            <div className="text-xs text-gray-500">
              マンション: {parseInt(row.original.TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT).toLocaleString()}
            </div>
          </div>
          <div className="flex flex-row gap-2">
            <div className="text-xs text-gray-500">
              戸建: {parseInt(row.original.TOTAL_PROPERTY_HOUSE_RENT_RECORD_COUNT).toLocaleString()}
            </div>
          </div>
        </div>
      }
    },

    ...Object.values(SystemMetricKeyEnum).filter((key) => mapper[key as keyof typeof mapper]).map((key) => ({
      header: mapper[key as keyof typeof mapper],
      cell: ({ row }: { row: any }) => {
        return <div className="text-center flex flex-col items-center justify-center" >
          <div className="text-sm">{row.original[key]?.toLocaleString()}</div>
        </div>
      }
    })),
  ]

  let parseSystemData = (data: SystemMetricProps[]) => {
    // return data.map((item) => ({
    //   key: item.key,
    //   value: item.value,
    //   recordDate: item.recordDate ,
    // }));

    let parsedData = {} as any;

    let distinctRecords = [...new Set(data.map(item => dayjs(item.recordDate as any).format("YYYY-MM-DD")))];
    for (let record of distinctRecords) {
      parsedData[record] = {
        recordDate: record,
      };
    }

    for (let item of data) {
      if (Object.values(SystemMetricKeyEnum).includes(item.key) && item.recordDate) {
        parsedData[dayjs(item.recordDate as any).format("YYYY-MM-DD")][item.key] = item.value;
      }
    }

    return Object.values(parsedData).sort((a: any, b: any) => dayjs(a.recordDate).isAfter(dayjs(b.recordDate)) ? -1 : 1);
  }

  return <div className="p-4 flex flex-col gap-4">
    {/* <div className="text-sm">
    {JSON.stringify(systemData)}
  </div> */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="col-span-1 md:col-span-1 flex flex-col gap-4">
        <div className="flex flex-col gap-2 border rounded-md bg-gray-100">
          <div className="text-sm bg-white p-2 font-bold">
            総売買数
          </div>
          <div className="p-2">
            <SystemMetricsChart data={parseSystemData(systemData) as any} distinctKey="sell" />
          </div>
        </div>

        <div className="flex flex-col gap-2 border rounded-md bg-gray-100">
          <div className="text-sm bg-white p-2 font-bold">
            総変更数 + 総賃貸数
          </div>

          <div className="p-2">
            <SystemMetricsChart data={parseSystemData(systemData) as any} distinctKey="rent" />
          </div>
        </div>

        <div className="flex flex-col gap-2 border rounded-md bg-gray-100">
          <div className="text-sm bg-white p-2 font-bold">
            総変更数 BY SOURCE
          </div>

          <div className="p-2">
            <ChartForChangeRecord />
          </div>
        </div>
      </div>

      <div className="col-span-1 md:col-span-2">
        <DataTable columns={columns} data={parseSystemData(systemData)} />
      </div>
    </div>
  </div>

}