"use client"

import { TrendingUp } from "lucide-react"
import { Area, AreaChart, CartesianGrid, Legend, XAxis, YAxis } from "recharts"

import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { SystemMetricProps, SystemUserActivityProps } from "@/lib/definitions/system"
import dayjs from "dayjs"


export function SystemMetricsChart({ data, distinctKey }: { data: any, distinctKey: "sell" | "rent" }) {

  // let prepChartData = 
  let preppedData = {} as any;

  // data.forEach((item: any) => {
  //   if (preppedData[item.recordDate] == undefined) {
  //     preppedData[item.recordDate] = {
  //       recordDate: item.recordDate,
  //     };
  //   }

  //   if (preppedData[item.recordDate][item[distinctKey]] == undefined) {
  //     if (!uniqueGraphKeys.includes(item[distinctKey])) {
  //       uniqueGraphKeys.push(item[distinctKey]);
  //     }
  //     preppedData[item.recordDate][item[distinctKey]] = 1;
  //   } else {
  //     preppedData[item.recordDate][item[distinctKey]] += 1;
  //   }
  // });
  let mapper = {
    "BUILDING": "一棟",
    "HOUSE": "戸建",
    "LAND": "土地",
    "MANSION": "マンション",
  }
  let mapperForName = {
    TOTAL_PROPERTY_BUILDING_RECORD_COUNT: "総一棟収益物件数",
    TOTAL_PROPERTY_HOUSE_RECORD_COUNT: "総戸建物件数",
    TOTAL_PROPERTY_LAND_RECORD_COUNT: "総土地物件数",
    TOTAL_PROPERTY_MANSION_RECORD_COUNT: "総マンション物件数",
    TOTAL_PROPERTY_CHANGE_RECORD_COUNT: "総変更物件数",
    TOTAL_PROPERTY_BUILDING_RENT_RECORD_COUNT: "総一棟収益物件賃貸数",
    TOTAL_PROPERTY_HOUSE_RENT_RECORD_COUNT: "総戸建賃貸数",
    TOTAL_PROPERTY_MANSION_RENT_RECORD_COUNT: "総マンション賃貸数",
    TOTAL_USER_COUNT: "総ユーザー",
    TOTAL_VALUATION_RECORD_COUNT: "総査定数",
  }

  let chartConfig = {} as any;
  ["BUILDING", "MANSION", "HOUSE", ...(distinctKey === "sell" ? ["LAND"] : [])].forEach((key: any, index: number) => {
    chartConfig[key] = {
      label: mapper[key as keyof typeof mapper],
      color: `hsl(var(--chart-${index + 1}))`,
    }
  })

  const getColor = (index: number) => {
    const colors = [
      "#A1C4FD", "#B39DDB", "#FFAB91", "#80CBC4", "#F48FB1", "#81D4FA", "#FFD54F", "#FF8A65",
      "#9FA8DA", "#B0BEC5", "#C5E1A5", "#E6EE9C", "#CE93D8", "#EF9A9A", "#90CAF9", "#A5D6A7",
      "#FFCC80", "#B0BEC5", "#F06292", "#9575CD", "#64B5F6", "#4DB6AC", "#BA68C8", "#FF8A65",
      "#AED581", "#DCE775", "#FF7043", "#90A4AE", "#E57373", "#FDD835"
    ];
    return colors[index % colors.length]; // Cycle through colors
  };

  return (
    <div className="w-full h-full">
      <ChartContainer config={chartConfig}>
        <AreaChart
          accessibilityLayer
          data={data.sort((a: any, b: any) => dayjs(a.recordDate).diff(dayjs(b.recordDate)))}
          margin={{
            left: 12,
            right: 12,
          }}
        >
          <CartesianGrid vertical={false} />
          <Legend />
          <XAxis
            dataKey="recordDate"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => dayjs(value).format("YYYY-MM-DD")}
          />
          <YAxis
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            domain={[(dataMin: number) => parseFloat((dataMin * 0.8).toFixed(0)), (dataMax: number) => parseFloat((dataMax * 1.2).toFixed(0))]}
            tickFormatter={(value) => value.toLocaleString()}
          />
          <ChartTooltip
            cursor={false}
            content={<ChartTooltipContent indicator="dot" />}
          />
          {Object.keys(chartConfig).map((key: any, index: number) => (
            <Area
              dataKey={distinctKey === "sell" ? `TOTAL_PROPERTY_${key}_RECORD_COUNT` : `TOTAL_PROPERTY_${key}_RENT_RECORD_COUNT`}
              key={distinctKey === "sell" ? `TOTAL_PROPERTY_${key}_RECORD_COUNT` : `TOTAL_PROPERTY_${key}_RENT_RECORD_COUNT`}
              type="monotone"
              fill={getColor(index)}
              fillOpacity={0.5}
              dot={false}
              stackId="a"
              connectNulls />
          ))}
        </AreaChart>
      </ChartContainer>
    </div>
  )
}
