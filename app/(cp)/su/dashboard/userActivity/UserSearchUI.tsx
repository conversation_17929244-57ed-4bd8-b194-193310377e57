"use client";

import ReactSelect from "react-select";
import { useDebouncedCallback } from "use-debounce";
import { useEffect, useState } from "react";
import { Loader2 } from "lucide-react";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { fuzzySearchUserAction } from "@/actions/users";

// Custom option rendering component
const CustomOption = (props: any) => {
  const { data, innerRef, innerProps } = props;
  return (
    <div
      ref={innerRef}
      {...innerProps}
      className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
    >
      <div className="flex flex-col gap-2 w-full justify-between items-start">
        <span className="font-semibold text-black flex-1 text-left">
          {data.label}
        </span>
        <span className="text-sm text-gray-600">
          メール: {data.email} | アクセスレベル: {data.accessLevel}
        </span>
      </div>
    </div>
  );
};

export default function UserSearchUI({
  selectedUser,
  setSelectedUser,
}: {
  selectedUser: { label: string; value: string | null };
  setSelectedUser: (user: { label: string; value: string | null }) => void;
}) {
  const [userOptions, setUserOptions] = useState<
    { label: string; value: string; email: string; accessLevel: string }[]
  >([]);
  const [isLoadingArea, setIsLoadingArea] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const handleSearch = useDebouncedCallback(async ({ value }: { value: string }) => {
    if (!value) return;

    try {
      setIsLoadingArea(true);
      const response = await fuzzySearchUserAction(value);

      if (response.success) {
        const options = response.data.map((user: TllUserProps) => ({
          label: user.name,
          value: String(user.id),
          accessLevel: user.accessLevel,
          email: user.email,
        }));
        setUserOptions(options);
      }
    } catch (error) {
      console.error("❌ Failed to fetch users:", error);
    } finally {
      setIsLoadingArea(false);
    }
  }, 500);

  const matchedSelected =
    userOptions.find((opt) => opt.value === selectedUser?.value) || null;

  return (
    <ReactSelect
      instanceId="user-search-bar"
      aria-activedescendant="user-search-bar"
      placeholder="ユーザーを検索..."
      options={userOptions}
      value={matchedSelected}
      isSearchable
      isClearable
      isLoading={isLoadingArea}
      noOptionsMessage={() => "データなし"}
      filterOption={() => true}
      loadingMessage={() => (
        <div className="flex flex-row gap-2 items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>読み込み中</span>
        </div>
      )}
      onInputChange={(value, action) => {
        if (action.action === "input-change") {
          handleSearch({ value });
        }
      }}
      onChange={(selected) => {
        setSelectedUser(selected as { label: string; value: string | null });

        const newParams = new URLSearchParams(searchParams.toString());
        newParams.set("value", selected?.label || "");
        router.push(`${pathname}?${newParams.toString()}`, { scroll: false });
      }}
      components={{
        Option: CustomOption,
      }}
    />
  );
}