"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>onsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { useMemo } from "react";

// 颜色定义
const COLORS: { [key: string]: string } = {
  BUTTON_CLICK: "#8884d8",
  FORM_SUBMIT: "#82ca9d",
  PAGE_VIEW: "#ffc658",
};

export default function StackedBarChart({ rawData }: { rawData: { recordDate: string; eventType: string }[] }) {
  const data = useMemo(() => {
    if (!rawData || rawData.length === 0) return [];

    const grouped: { [date: string]: { [type: string]: number } } = {};

    rawData.forEach(({ eventType, recordDate }) => {
      if (!grouped[recordDate]) grouped[recordDate] = {};
      if (!grouped[recordDate][eventType]) grouped[recordDate][eventType] = 0;
      grouped[recordDate][eventType]++;
    });

    return Object.entries(grouped)
      .map(([date, types]) => ({
        date,
        ...types,
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()); // ✅ 按日期升序排列
  }, [rawData]);

  const allTypes = useMemo(() => Array.from(new Set(rawData.map(d => d.eventType))), [rawData]);

  const isLoading = !rawData || rawData.length === 0;

  return (
    <div className="w-full h-[400px] p-4 border border-neutral-200 rounded-md bg-neutral-50 pb-12">
      <div className="flex flex-row gap-2 font-semibold text-neutral-700 mb-4">
        日付別ユーザー活動
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-[300px] text-neutral-400">
          データを読み込み中...
        </div>
      ) : (
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 20, right: 30, left: 0, bottom: 5 }}>
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            {allTypes.map((type) => (
              <Bar
                key={type}
                dataKey={type}
                stackId="stack"
                fill={COLORS[type] || "#8884d8"}
              />
            ))}
          </BarChart>
        </ResponsiveContainer>
      )}
    </div>
  );
}