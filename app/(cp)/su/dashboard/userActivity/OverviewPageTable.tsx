"use client";

import { getDistinctSearchHistoryCountPerUserPerDay } from "@/actions/tllUserLambdaRecordSearchHistory";
import { useState, useEffect, useMemo } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";

export default function OverviewPageTable({ data, title }: { data: any[], title: string }) {

  const [pivotRows, setPivotRows] = useState<any[]>([]);
  const [allDates, setAllDates] = useState<string[]>([]); // ← 日期是列

  useEffect(() => {
    if (data.length > 0) {
      const dates = [...new Set(data.map((d: any) => d.dateOfRecord))].sort().reverse();
      const users = [...new Set(data.map((d: any) => d.userId))].sort();

      setAllDates(dates as string[]);

      const pivot = users.map((userId) => {
        const user = data.find((d: any) => d.userId === userId);
        const label = user
          ? `${user.email?.split("@")[0]}（${user.subscriptionPlan ?? "-"} / ${user.accessLevel ?? "-"}）`
          : userId;

        const row: Record<string, any> = { userLabel: label }; // 👈 替代 userId

        dates.forEach((date) => {
          const match = data.find(
            (d: any) => d.userId === userId && d.dateOfRecord === date
          );
          row[date as string] = match?.count ?? 0;
        });

        return row;
      });

      const filteredPivot = pivot.filter((row) => {
        // 除了 userLabel 以外的列是否都为 0 或 undefined？
        return dates.some((date) => row[date] && row[date] > 0);
      });

      setPivotRows(filteredPivot);
    }
  }, [data]);

  const columns: ColumnDef<any>[] = useMemo(() => {
    return [
      {
        accessorKey: "userLabel", // 👈 替换 userId
        header: "ユーザー（プラン / 権限）",
        cell: ({ row }) => {
          const value = row.original.userLabel;
          return <span className="text-xs">{value ?? "-"}</span>;
        },
      },
      ...allDates.map((date) => ({
        accessorKey: date,
        header: date,
        cell: (info: any) => {
          const value = info.getValue();
          return value > 0 ? value.toString() : <span className="text-neutral-300">-</span>;
        },
      })),
    ];
  }, [allDates]);

  const table = useReactTable({
    data: pivotRows,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (

    <div className="border border-neutral-200 rounded-md p-2 bg-neutral-50 flex flex-col gap-2">
      <div className="text-sm text-neutral-500">
        {title} 合計: {pivotRows.length}件
      </div>

      <Separator className="my-2" />

      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}