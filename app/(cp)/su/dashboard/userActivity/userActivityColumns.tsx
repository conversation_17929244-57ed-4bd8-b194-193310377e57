import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";

export const userActivityColumns = [
  {
    header: "日時",
    accessorKey: "recordDate",
    cell: ({ row }: { row: any }) => {
      return dayjs(row.original?.recordDate).format("YYYY-MM-DD");
    },
  },
  {
    header: "タイプ",
    accessorKey: "eventType",
  },
  {
    header: "ルート(正規化)",
    accessorKey: "routeNormalized",
    cell: ({ row }: { row: any }) => {
      return <div className="text-xs whitespace-pre-wrap max-w-sm overflow-hidden">
        {row.original?.routeNormalized ? row.original?.routeNormalized.slice(0, 50) : ""}
        <div className="text-xs text-gray-500 whitespace-pre-wrap max-w-sm overflow-hidden">Origin: {row.original?.route ? row.original?.route.slice(0, 50) : ""}</div>
        <div className="text-xs text-gray-500 whitespace-pre-wrap max-w-sm overflow-hidden">Referer: {row.original?.referer ? row.original?.referer.slice(0, 50) : ""}</div>
      </div>;
    },
  },

  // {
  //   header: "ユーザーID",
  //   accessorKey: "userId",
  // },
  {
    header: "ユーザーエージェント",
    // accessorKey: "userAgent",
    cell: ({ row }: { row: any }) => {
      return <div className="text-xs text-gray-500 whitespace-pre-wrap max-w-sm overflow-hidden">{row.original.userAgent ? row.original.userAgent.slice(0, 20) : ""}</div>;
    },
  },
  {
    header: "イベントメタデータ",
    accessorKey: "eventMetadata",
    cell: ({ row }: { row: any }) => {
      return <div className="text-xs text-gray-500 whitespace-pre-wrap max-w-md overflow-hidden">{row.original.eventMetadata ? JSON.stringify(row.original.eventMetadata) : ""}</div>;
    },
  },
  {
    header: "作成日時",
    accessorKey: "createdAt",
    cell: ({ row }: { row: any }) => {
      return dayjs(row.original?.createdAt).format("YYYY-MM-DD HH:mm:ss");
    },
  },

] as ColumnDef<any>[];