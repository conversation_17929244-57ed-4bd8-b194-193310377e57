"use client";

import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";
import { getUserActivityAction } from "@/actions/systemUserActivity";
import { SystemUserActivityProps } from "@/lib/definitions/system";
import { DataTable } from "@/components/ui/data-table";
import UserSearchUI from "./UserSearchUI";
import { Badge } from "@/components/ui/badge";
import ActivityBar from "./ActivityBar";
import dayjs from "dayjs";
import { UserActivityChart } from "./UserActivityChart";
import { userActivityColumns } from "./userActivityColumns";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import OverviewPage from "./OverviewPage";

export default function UserActivity() {
  let [activity, setActivity] = useState<SystemUserActivityProps[]>([]);
  let [selectedUser, setSelectedUser] = useState<{ label: string; value: string | null }>({ label: "", value: null });
  let [selectedType, setSelectedType] = useState<string>("ALL");
  let [selectedTab, setSelectedTab] = useState<string>("overview");

  useEffect(() => {
    getUserActivityAction().then((res) => {
      setActivity(res.data);
    });
  }, []);

  const filteredActivity = activity.filter((item) => selectedUser.value ? item.userId === selectedUser.value : true).filter((item) => selectedType === "ALL" ? true : item.eventType === selectedType) as SystemUserActivityProps[];

  let types = ["PAGE_VIEW", "BUTTON_CLICK", "FORM_SUBMIT", "LOGIN", "LOGOUT"];

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-row gap-2 p-2">
        <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value)}  >
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="userActivity">User Activity</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {selectedTab === "overview" && (
        <OverviewPage />
      )}

      {selectedTab === "userActivity" && (
        <>
          <div className="grid grid-cols-2 gap-2 p-2">
            <div className="flex flex-col justify-between items-start border border-neutral-200 rounded-md bg-neutral-50 p-2 ">
              <div>
                日次ユーザー活動
              </div>
              <Separator className="my-2" />
              <UserActivityChart data={activity} distinctKey="userId" />
            </div>
            <div className="flex flex-col justify-between items-start border border-neutral-200 rounded-md bg-neutral-50 p-2">
              <div>
                日次活動チャート
              </div>
              <Separator className="my-2" />
              <UserActivityChart data={activity} distinctKey="eventType" />
            </div>
          </div>

          <Separator className="" />

          <div className="p-2 flex flex-col gap-2">
            <UserSearchUI selectedUser={selectedUser} setSelectedUser={setSelectedUser} />

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
              <ActivityBar rawData={filteredActivity.filter((item) => item.recordDate).map((item) => ({ eventType: item.eventType, recordDate: dayjs(item.recordDate).format("YYYY-MM-DD") }))} />

              <div className="col-span-1 sm:col-span-2 flex flex-col gap-2">
                <div className="flex flex-row gap-2">
                  <Badge variant={selectedType === "ALL" ? "default" : "outline"} onClick={() => setSelectedType("ALL")}>All</Badge>
                  {types.map((type) => (
                    <Badge variant={selectedType === type ? "default" : "outline"} key={type} onClick={() => setSelectedType(type)}>{type}</Badge>
                  ))}
                </div>

                <div className="text-sm text-neutral-500">
                  合計: {filteredActivity.length}件
                </div>

                {selectedUser.value && <DataTable columns={userActivityColumns} data={filteredActivity} defaultPageSize={100} />}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}