"use client"

import { TrendingUp } from "lucide-react"
import { Area, AreaChart, CartesianGrid, Legend, XAxis, YAxis } from "recharts"

import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { SystemUserActivityProps } from "@/lib/definitions/system"
import dayjs from "dayjs"


export function UserActivityChart({ data, distinctKey }: { data: SystemUserActivityProps[], distinctKey: "userId" | "eventType" }) {

  // let prepChartData = 
  let preppedData = {} as any;
  let uniqueGraphKeys = [] as any;

  data.forEach((item: any) => {
    if (preppedData[item.recordDate] == undefined) {
      preppedData[item.recordDate] = {
        recordDate: item.recordDate,
      };
    }

    if (distinctKey === "eventType") {
      preppedData[item.recordDate][item[distinctKey]] = (preppedData[item.recordDate][item[distinctKey]] || 0) + 1;
      if (!uniqueGraphKeys.includes(item[distinctKey])) {
        uniqueGraphKeys.push(item[distinctKey]);
      }
    } else if (distinctKey === "userId") {
      preppedData[item.recordDate][item["user"]?.name] = (preppedData[item.recordDate][item["user"]?.name] || 0) + 1;
      if (!uniqueGraphKeys.includes(item["user"]?.name)) {
        uniqueGraphKeys.push(item["user"]?.name);
      }
    }
  });

  let chartConfig = {} as any;
  if (uniqueGraphKeys.length > 0) {
    uniqueGraphKeys.forEach((key: any, index: number) => {
      chartConfig[key] = {
        label: key,
        color: `hsl(var(--chart-${index + 1}))`,
      }
    })
  }

  const getColor = (index: number) => {
    const colors = [
      "#A1C4FD", "#B39DDB", "#FFAB91", "#80CBC4", "#F48FB1", "#81D4FA", "#FFD54F", "#FF8A65",
      "#9FA8DA", "#B0BEC5", "#C5E1A5", "#E6EE9C", "#CE93D8", "#EF9A9A", "#90CAF9", "#A5D6A7",
      "#FFCC80", "#B0BEC5", "#F06292", "#9575CD", "#64B5F6", "#4DB6AC", "#BA68C8", "#FF8A65",
      "#AED581", "#DCE775", "#FF7043", "#90A4AE", "#E57373", "#FDD835"
    ];
    return colors[index % colors.length]; // Cycle through colors
  };

  // console.log("preppedData", JSON.stringify(preppedData, null, 2));
  // console.log("uniqueGraphKeys", uniqueGraphKeys);

  return (
    <div className="w-full h-full">
      <ChartContainer config={chartConfig}>
        <AreaChart
          accessibilityLayer
          data={Object.values(preppedData).sort((a: any, b: any) => dayjs(a.recordDate).diff(dayjs(b.recordDate)))}
          margin={{
            left: 12,
            right: 12,
          }}
        >
          <CartesianGrid vertical={false} />
          <Legend />
          <XAxis
            dataKey="recordDate"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => dayjs(value).format("YYYY-MM-DD")}
          />
          <YAxis
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            domain={[(dataMin: number) => parseFloat((dataMin * 0.9).toFixed(0)), (dataMax: number) => parseFloat((dataMax * 1.1).toFixed(0))]}
            tickFormatter={(value) => value.toLocaleString()}
          />
          <ChartTooltip
            cursor={false}
            content={<ChartTooltipContent indicator="dot" />}
          />
          {uniqueGraphKeys.map((key: any, index: number) => (
            <Area
              dataKey={key}
              key={key + index}
              type="monotone"
              fill={getColor(index)}
              fillOpacity={0.5}
              dot={false}
              stackId="a"
              connectNulls />
          ))}
        </AreaChart>
      </ChartContainer>
    </div>
  )
}
