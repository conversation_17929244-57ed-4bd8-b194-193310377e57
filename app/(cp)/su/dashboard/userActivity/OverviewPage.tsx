"use client";

import {
  getDistinctSearchHistoryCountPerUserPerDay,
} from "@/actions/tllUserLambdaRecordSearchHistory";
import { getValuationRecordCountPerUserPerDay } from "@/actions/valuationRecord";
import { getReportViewHistoryCountPerUserPerDay } from "@/actions/systemReportViewHistory";
import { useEffect, useMemo, useState } from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";

export default function CombinedOverviewPageTable() {
  const [pivotRows, setPivotRows] = useState<any[]>([]);
  const [allDates, setAllDates] = useState<string[]>([]);

  useEffect(() => {
    Promise.all([
      getDistinctSearchHistoryCountPerUserPerDay(),
      getValuationRecordCountPerUserPerDay(),
      getReportViewHistoryCountPerUserPerDay(),
    ]).then(([searchRes, valuationRes, reportRes]) => {
      const search = searchRes.data ?? [];
      const valuation = valuationRes.data ?? [];
      const report = reportRes.data ?? [];

      const raw = [...search, ...valuation, ...report];
      const dates = [...new Set(raw.map((d: any) => d.dateOfRecord))].sort().reverse();
      const users = [...new Set(raw.map((d: any) => d.userId))].sort();

      setAllDates(dates);

      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();

      const pivot = users.map((userId) => {
        const user = raw.find((d: any) => d.userId === userId);
        const label = user
          ? `${user.email?.split("@")[0]}（${user.subscriptionPlan ?? "-"} / ${user.accessLevel ?? "-"}）`
          : userId;

        const row: Record<string, any> = { userLabel: label, userId };
        let monthlyS = 0, monthlyV = 0, monthlyR = 0;

        dates.forEach((date) => {
          const s = search.find((d: any) => d.userId === userId && d.dateOfRecord === date)?.count ?? 0;
          const v = valuation.find((d: any) => d.userId === userId && d.dateOfRecord === date)?.count ?? 0;
          const r = report.find((d: any) => d.userId === userId && d.dateOfRecord === date)?.count ?? 0;
          row[date] = { s, v, r };

          const parsed = new Date(date);
          if (parsed.getMonth() === currentMonth && parsed.getFullYear() === currentYear) {
            monthlyS += s;
            monthlyV += v;
            monthlyR += r;
          }
        });

        row["monthlyTotal"] = { s: monthlyS, v: monthlyV, r: monthlyR };
        return row;
      });

      const filteredPivot = pivot.filter((row) =>
        dates.some((date) => {
          const val = row[date];
          return val && (val.s > 0 || val.v > 0 || val.r > 0);
        })
      );

      filteredPivot.sort((a, b) => {
        for (const date of dates) {
          const aVal = a[date];
          const bVal = b[date];
          const aSum = (aVal?.s ?? 0) + (aVal?.v ?? 0) + (aVal?.r ?? 0);
          const bSum = (bVal?.s ?? 0) + (bVal?.v ?? 0) + (bVal?.r ?? 0);
          if (aSum !== bSum) return bSum - aSum;
        }
        return 0;
      });

      setPivotRows(filteredPivot);
    });
  }, []);

  const columns: ColumnDef<any>[] = useMemo(() => {
    return [
      {
        accessorKey: "userLabel",
        header: "ユーザー（プラン / 権限）",
        cell: ({ row }) => <span className="text-xs">{row.original.userLabel}</span>,
      },
      {
        accessorKey: "monthlyTotal",
        header: "今月",
        cell: (info: any) => {
          const val = info.getValue();
          if (!val || (val.s === 0 && val.v === 0 && val.r === 0)) {
            return <span className="text-neutral-300">-</span>;
          }
          return (
            <span className="text-xs">
              <span className={val.s === 0 ? "text-neutral-300" : ""}>{val.s}</span>/
              <span className={val.v === 0 ? "text-neutral-300" : ""}>{val.v}</span>/
              <span className={val.r === 0 ? "text-neutral-300" : ""}>{val.r}</span>
            </span>
          );
        },
      },
      ...allDates.map((date) => ({
        accessorKey: date,
        header: date,
        cell: (info: any) => {
          const val = info.getValue();
          if (!val || (val.s === 0 && val.v === 0 && val.r === 0)) {
            return <span className="text-neutral-300">-</span>;
          }
          return (
            <span className="text-xs">
              <span className={val.s === 0 ? "text-neutral-300" : ""}>{val.s}</span>/
              <span className={val.v === 0 ? "text-neutral-300" : ""}>{val.v}</span>/
              <span className={val.r === 0 ? "text-neutral-300" : ""}>{val.r}</span>
            </span>
          );
        },
      })),
    ];
  }, [allDates]);

  const table = useReactTable({
    data: pivotRows,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="border border-neutral-200 rounded-md p-2 bg-neutral-50 flex flex-col gap-2 m-2 p-2">
      <div className="text-sm text-neutral-500">
        総合ユーザーアクティビティ | 合計: {pivotRows.length}件 | Search / Valuation / Report
      </div>
      <Separator className="my-2" />
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <TableRow key={row.id}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}