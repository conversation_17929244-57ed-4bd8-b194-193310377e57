import { getFavoriteMetricsAction } from "@/actions/superAdminDashboard";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import CommonChart from "./CommonChart";
import { useEffect, useState } from "react";
import { getSuperAdminDashboardFavoriteAction } from "@/actions/superAdminDashboard";

export default function FavChart() {
  const [data, setData] = useState([]);
  const [detailedData, setDetailedData] = useState([]);
  const [view, setView] = useState("month");

  useEffect(() => {
    getFavoriteMetricsAction().then((res) => {
      setData(res.data);
    });
  }, []);

  const getDetailedData = async () => {
    const res = await getSuperAdminDashboardFavoriteAction();
    setDetailedData(res.data);
  }

  useEffect(() => {
    getDetailedData();
  }, []);


  console.log("detailedData", detailedData);

  return (
    <>
      <div className="border border-neutral-200 bg-neutral-50 rounded-lg w-full bg-white">
        <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
          <div className="flex-1 font-bold">
            {view === "month" ? "MONTHLY" : view === "week" ? "WEEKLY" : "DAILY"}お気入り数
          </div>

          <div className="flex">
            <ToggleGroup type="single" onValueChange={(value) => setView(value as "day" | "month")} defaultValue={view}>
              <ToggleGroupItem value="day">日</ToggleGroupItem>
              <ToggleGroupItem value="week">週</ToggleGroupItem>
              <ToggleGroupItem value="month">月</ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>

        <div className="w-full p-2">
          {view === "month" && <CommonChart rawData={detailedData} distinctKey={"month"} dateValuekKey={"favDate"} />}
          {view === "week" && <CommonChart rawData={detailedData} distinctKey={"week"} dateValuekKey={"favDate"} />}
          {view === "day" && <CommonChart rawData={detailedData} distinctKey={"day"} dateValuekKey={"favDate"} />}
        </div>
      </div>

      <div className="border-neutral-200 bg-white rounded-lg">
        <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
          <div className="flex-1 font-bold">
            お気入り数
          </div>
        </div>

        <div className="flex flex-col p-2 justify-center items-center border-b border-neutral-200">
          <table className="min-w-full border-collapse border border-gray-200">
            <thead>
              <tr>
                <th className="border border-gray-300 p-2">ユーザー名</th>
                <th className="border border-gray-300 p-2">お気入り数</th>
              </tr>
            </thead>
            <tbody>
              {data.sort((a: any, b: any) => b._count?._all - a._count?._all).map((item: any) => (
                <tr key={item.userId}>
                  <td className="border border-gray-300 p-2">{item.user?.name}</td>
                  <td className="border border-gray-300 p-2">{item._count?._all}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </>
  );
}
