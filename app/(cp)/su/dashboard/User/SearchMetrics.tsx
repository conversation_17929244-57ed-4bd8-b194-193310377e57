"use client"

import dayjs from "dayjs";
import { TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TableCell } from "@/components/ui/table";
import { Table } from "@/components/ui/table";
import { useState, useEffect } from "react";
import { getSuperAdminDashboardSearchAction } from "@/actions/superAdminDashboard";
import CommonChart from "./CommonChart";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Separator } from "@/components/ui/separator";

export default function SearchMetrics() {
  const [searchRecords, setSearchRecords] = useState<any>([]);
  const [view, setView] = useState<"day" | "month" | "week">("day");

  const getData = async () => {
    const res = await getSuperAdminDashboardSearchAction();
    setSearchRecords(res.data);
  }

  useEffect(() => {
    getData();
  }, []);

  const userTotalSearch = searchRecords.reduce((acc: any, item: any) => {
    const userId = item.user?.id;
    if (!acc[userId]) {
      acc[userId] = { name: item.user?.name, total: 0 };
    }
    acc[userId].total += item._count?._all || 0;
    return acc;
  }, {});

  return <>
    <div className="border border-neutral-200 bg-white rounded-lg ">
      <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
        <div className="flex-1 font-bold">
          {view === "day" ? "DAILY" : view === "week" ? "WEEKLY" : "MONTHLY"}閲覧履歴
        </div>

        <div className="flex">
          <ToggleGroup type="single" onValueChange={(value) => setView(value as "day" | "month")} defaultValue={view}>
            <ToggleGroupItem value="day">日</ToggleGroupItem>
            <ToggleGroupItem value="week">週</ToggleGroupItem>
            <ToggleGroupItem value="month">月</ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>

      <div className="w-full p-2">
        {view === "day" && <CommonChart rawData={searchRecords} distinctKey={"day"} dateValuekKey={"searchDate"} />}
        {view === "week" && <CommonChart rawData={searchRecords} distinctKey={"week"} dateValuekKey={"searchDate"} />}
        {view === "month" && <CommonChart rawData={searchRecords} distinctKey={"month"} dateValuekKey={"searchDate"} />}
      </div>

      {view === "day" && <Table className="w-full bg-neutral-50">
        <TableHeader>
          <TableRow>
            <TableHead>ユーザー</TableHead>
            <TableHead>アクセスレベル</TableHead>
            <TableHead>検索日</TableHead>
            <TableHead>検索数</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {searchRecords?.sort((a: any, b: any) => {
            if (dayjs(a.searchDate).isSame(dayjs(b.searchDate), 'day')) {
              return b._count?._all - a._count?._all; // 按照数量排序
            }
            return dayjs(b.searchDate).diff(dayjs(a.searchDate)); // 按照搜索日期排序
          })?.slice(0, 10).map((item: any, index: number) => (
            <TableRow key={index}>
              <TableCell>{item.user?.name}</TableCell>
              <TableCell>{item.user?.accessLevel}</TableCell>
              <TableCell>{dayjs(item.searchDate).format("YYYY-MM-DD")}</TableCell>
              <TableCell>{item._count?._all}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>}
    </div>

    <div className="border border-neutral-200 bg-white rounded-lg">
      <div className="font-bold p-2">TOTAL閲覧
      </div>
      <Separator className="" />
      <div className="p-2">
        {/* <span className="text-sm text-neutral-500">Show first 10 only</span> */}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ユーザー名</TableHead>
              <TableHead>閲覧数</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Object.entries(userTotalSearch).sort((a: any, b: any) => b[1].total - a[1].total).map(([userId, user]: any) => (
              <TableRow key={userId}>
                <TableCell>{user.name || "不明"}</TableCell>
                <TableCell>{user.total}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  </>
}

