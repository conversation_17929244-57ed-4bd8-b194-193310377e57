"use client"

import { useEffect, useState } from "react";
import { getDauMetricsAction } from "@/actions/systemMetrics";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import CommonChart from "./CommonChart";
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts";

export default function DauMetrics() {
  const [data, setData] = useState<any[]>([]);
  const [view, setView] = useState<"day" | "week" | "month">("day");

  useEffect(() => {
    getDauMetricsAction().then((res) => {
      setData(res.data);
    });
  }, []);

  function transformData(data: any[]) {
    const grouped = new Map();

    data.forEach((item) => {
      const date = item.recordDate.toISOString().split("T")[0];
      if (!grouped.has(date)) {
        grouped.set(date, { date });
      }
      grouped.get(date)[item.key] = Number(item.value);
    });

    return Array.from(grouped.values());
  }

  function transformDataForPercentage(data: any[]) {
    const grouped = new Map();

    data.forEach((item) => {
      const date = item.recordDate.toISOString().split("T")[0];
      if (!grouped.has(date)) {
        grouped.set(date, { date });
      }
      grouped.get(date)[item.key] = Number(item.value);
    });

    // 计算 DAU / Total 等比率
    for (const item of grouped.values()) {
      const dau = item.TOTAL_USER_DAU_COUNT ?? 0;
      const wau = item.TOTAL_USER_WAU_COUNT ?? 0;
      const mau = item.TOTAL_USER_MAU_COUNT ?? 0;
      const total = item.TOTAL_USER_COUNT ?? 0;

      const PREVUSER = 100; // to be removed after June

      item.dauRatio = total > 0 ? Number((dau / (total - PREVUSER) * 100).toFixed(0)) : 0;
      item.wauRatio = total > 0 ? Number((wau / (total - PREVUSER) * 100).toFixed(0)) : 0;
      item.mauRatio = total > 0 ? Number((mau / (total - PREVUSER) * 100).toFixed(0)) : 0;
      item.stickiness = total > 0 ? Number((dau / mau * 100).toFixed(0)) : 0;
    }

    return Array.from(grouped.values());
  }

  console.log("data", transformData(data))

  return <>
    <div className="col-span-1 flex flex-col gap-4">
      <div className="flex flex-col gap-4 border border-neutral-200 rounded-lg bg-white">
        <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
          <div className="flex-1 font-bold">
            {/* {view === "month" ? "MONTHLY" : view === "week" ? "WEEKLY" : "DAILY"}お気入り数 */}
            DAU / MAU / WAU 数
          </div>

          {/* <div className="flex">
          <ToggleGroup type="single" onValueChange={(value) => setView(value as "day" | "month")} defaultValue={view}>
            <ToggleGroupItem value="day">日</ToggleGroupItem>
            <ToggleGroupItem value="week">週</ToggleGroupItem>
            <ToggleGroupItem value="month">月</ToggleGroupItem>
          </ToggleGroup>
        </div> */}
        </div>

        <div className="w-full p-2 h-[500px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={transformData(data)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="TOTAL_USER_DAU_COUNT"
                stroke="#8884d8"
                name="DAU"
              />
              <Line
                type="monotone"
                dataKey="TOTAL_USER_WAU_COUNT"
                stroke="#82ca9d"
                name="WAU"
              />
              <Line
                type="monotone"
                dataKey="TOTAL_USER_MAU_COUNT"
                stroke="#ffc658"
                name="MAU"
              />
              <Line
                type="monotone"
                dataKey="TOTAL_USER_COUNT"
                stroke="#000000"
                name="TOTAL_USER_COUNT"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="flex flex-col gap-4 border border-neutral-200 rounded-lg bg-white">
        <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
          <div className="flex-1 font-bold">
            {/* {view === "month" ? "MONTHLY" : view === "week" ? "WEEKLY" : "DAILY"}お気入り数 */}
            DAU / MAU / WAU 数比率
          </div>

          {/* <div className="flex">
          <ToggleGroup type="single" onValueChange={(value) => setView(value as "day" | "month")} defaultValue={view}>
            <ToggleGroupItem value="day">日</ToggleGroupItem>
            <ToggleGroupItem value="week">週</ToggleGroupItem>
            <ToggleGroupItem value="month">月</ToggleGroupItem>
          </ToggleGroup>
        </div> */}
        </div>

        <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
          1. Chart by daily DAU vs RAU vs MAU, then / overally user (10-30, 30-50, 50-80 is good)
          <br />
          2. Stickiness = DAU / MAU (30%)
        </div>

        <div className="w-full p-2 h-[500px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={transformDataForPercentage(data)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="dauRatio"
                stroke="#8884d8"
                name="DAU / Total"
                strokeDasharray="3 3"
              />
              <Line
                type="monotone"
                dataKey="wauRatio"
                stroke="#82ca9d"
                name="WAU / Total"
                strokeDasharray="3 3"
              />
              <Line
                type="monotone"
                dataKey="mauRatio"
                stroke="#ffc658"
                name="MAU / Total"
                strokeDasharray="3 3"
              />
              <Line
                type="monotone"
                dataKey="stickiness"
                stroke="#000000"
                name="Stickiness"
                strokeDasharray="3 3"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>

    <div className="col-span-1 border border-neutral-200 bg-neutral-50 rounded-lg bg-white p-2">
      1. Chart by daily DAU vs RAU vs MAU, then / overally user (10-30, 30-50, 50-80 is good)
      <br />
      2. Stickiness = DAU / MAU (30%)
      <br />
      3. 7 日留存 (15-25% is good) - this should be a rolling window as well //

      1.	第1天注册用户集（Day 0 cohort）
      •	统计某天（如 2025-04-01）注册的所有用户 ID
      2.	第7天活跃用户集
      •	查询 2025-04-08 这天登录/使用的用户 ID
      3.	取交集
      •	查看有多少注册用户在7天后还活跃
      4.	计算留存率

    </div>
  </>
}