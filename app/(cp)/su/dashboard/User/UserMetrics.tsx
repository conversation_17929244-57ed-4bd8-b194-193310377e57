"use client";

import { getUserIncreaseByDayAction } from "@/actions/superAdminDashboard";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useEffect, useState } from "react";
import CommonChart from "./CommonChart";

export default function UserMetrics() {
  const [data, setData] = useState([]);
  const [view, setView] = useState("month");

  useEffect(() => {
    getUserIncreaseByDayAction().then((res) => {
      setData(res.data);
    });
  }, []);

  return <>
    <div className="flex flex-col gap-4 w-full border border-neutral-200 rounded-lg bg-white">
      <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
        <div className="flex-1 font-bold">
          {view === "month" ? "MONTHLY" : view === "week" ? "WEEKLY" : "DAILY"}ユーザー数
        </div>

        <div className="flex">
          <ToggleGroup type="single" onValueChange={(value) => setView(value as "day" | "month")} defaultValue={view}>
            <ToggleGroupItem value="day">日</ToggleGroupItem>
            <ToggleGroupItem value="week">週</ToggleGroupItem>
            <ToggleGroupItem value="month">月</ToggleGroupItem>
            <ToggleGroupItem value="year">年</ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>

      <div className="w-full p-2">
        {view === "month" && <CommonChart rawData={data} distinctKey={"month"} dateValuekKey={"createdDate"} />}
        {view === "week" && <CommonChart rawData={data} distinctKey={"week"} dateValuekKey={"createdDate"} />}
        {view === "day" && <CommonChart rawData={data} distinctKey={"day"} dateValuekKey={"createdDate"} />}
        {view === "year" && <CommonChart rawData={data} distinctKey={"year"} dateValuekKey={"createdDate"} />}
      </div>
    </div>


    <div className="flex flex-col gap-4 w-full border border-neutral-200 rounded-lg bg-white">
      <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
        <div className="flex-1 font-bold">
          {view === "month" ? "MONTHLY" : view === "week" ? "WEEKLY" : "DAILY"}有料ユーザー数
        </div>


        <div className="flex">
          <ToggleGroup type="single" onValueChange={(value) => setView(value as "day" | "month")} defaultValue={view}>
            <ToggleGroupItem value="day">日</ToggleGroupItem>
            <ToggleGroupItem value="week">週</ToggleGroupItem>
            <ToggleGroupItem value="month">月</ToggleGroupItem>
            <ToggleGroupItem value="year">年</ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>


      <div className="flex flex-col gap-2">
        -
      </div>
    </div>
  </>;
}