import { getNeedMetricsAction, getSuperAdminDashboardCustomerNeedAction } from "@/actions/superAdminDashboard";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import CommonChart from "./CommonChart";

export default function NeedMetrics() {
  const [data, setData] = useState([]);
  const [view, setView] = useState("week");
  const [detailedData, setDetailedData] = useState([]);

  useEffect(() => {
    getNeedMetricsAction().then((res) => {
      setData(res.data);
    });
  }, []);

  const getDetailedData = async () => {
    const res = await getSuperAdminDashboardCustomerNeedAction();
    setDetailedData(res.data);
  }

  useEffect(() => {
    getDetailedData();
  }, []);

  return <>
    <div className="border border-neutral-200 bg-neutral-50 rounded-lg w-full bg-white">
      <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
        <div className="flex-1 font-bold">
          {view === "month" ? "MONTHLY" : view === "week" ? "WEEKLY" : "DAILY"}気に入り検索条件
        </div>

        <div className="flex">
          <ToggleGroup type="single" onValueChange={(value) => setView(value as "day" | "month")} defaultValue={view}>
            <ToggleGroupItem value="day">日</ToggleGroupItem>
            <ToggleGroupItem value="week">週</ToggleGroupItem>
            <ToggleGroupItem value="month">月</ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>

      <div className="w-full p-2">
        {view === "month" && <CommonChart rawData={detailedData} distinctKey={"month"} dateValuekKey={"needDate"} />}
        {view === "week" && <CommonChart rawData={detailedData} distinctKey={"week"} dateValuekKey={"needDate"} />}
        {view === "day" && <CommonChart rawData={detailedData} distinctKey={"day"} dateValuekKey={"needDate"} />}
      </div>
    </div>

    <div className=" bg-white rounded-lg w-full">
      <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200 w-full">
        <div className="flex-1 font-bold">
          気に入り検索条件
        </div>
      </div>

      <div className="flex flex-col p-2 justify-center items-center border-b border-neutral-200 w-full">
        <table className="min-w-full border-collapse border-gray-200">
          <thead>
            <tr>
              <th className="border border-gray-300 p-2">ユーザー名</th>
              <th className="border border-gray-300 p-2">お気入り数</th>
            </tr>
          </thead>
          <tbody>
            {data.sort((a: any, b: any) => b._count?._all - a._count?._all).map((item: any, index: number) => (
              <tr key={index}>
                <td className="border border-gray-300 p-2">{item.user?.name}</td>
                <td className="border border-gray-300 p-2">{item._count?._all}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  </>;
}