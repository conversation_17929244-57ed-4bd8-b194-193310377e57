import { ChartContainer } from "@/components/ui/chart"
import { LineChart, Line, BarChart, Bar, CartesianGrid, Legend, XAxis, YAxis, AreaChart, Area } from "recharts";
import dayjs from "dayjs";
import { ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";


const getDataByType = ({ rawData, type, dateValuekKey }: { rawData: any, type: "week" | "month" | "day" | "year", dateValuekKey: string }) => {
  // 计算每月査定数
  if (type === "day") {
    const dailyData = rawData.reduce((acc: any, item: any) => {
      const day = dayjs(item[dateValuekKey]).format("YYYY-MM-DD"); // 获取年月
      if (!acc[day]) {
        acc[day] = {};
      }
      const userId = item.user?.id;
      if (!acc[day][userId]) {
        acc[day][userId] = { name: item.user?.name, count: 0 }; // 修改为对象以存储用户名
      }
      acc[day][userId].count += item._count?._all || item.count || 0; // 更新査定数
      return acc;
    }, {});
    return dailyData;
  }

  // 计算每月査定数
  if (type === "month") {
    const monthlyData = rawData.reduce((acc: any, item: any) => {
      const month = dayjs(item[dateValuekKey]).format("YYYY-MM"); // 获取年月
      if (!acc[month]) {
        acc[month] = {};
      }
      const userId = item.user?.id;
      if (!acc[month][userId]) {
        acc[month][userId] = { name: item.user?.name, count: 0 }; // 修改为对象以存储用户名
      }
      acc[month][userId].count += item._count?._all || item.count || 0; // 更新査定数
      return acc;
    }, {});
    return monthlyData;
  }

  // 计算每周査定数
  if (type === "week") {
    const weeklyData = rawData.reduce((acc: any, item: any) => {
      const week = dayjs(item[dateValuekKey]).startOf("week").format("YYYY-MM-DD"); // 获取年月
      if (!acc[week]) {
        acc[week] = {};
      }
      const userId = item.user?.id;
      if (!acc[week][userId]) {
        acc[week][userId] = { name: item.user?.name, count: 0 }; // 修改为对象以存储用户名
      }
      acc[week][userId].count += item._count?._all || item.count || 0; // 更新査定数
      return acc;
    }, {});
    return weeklyData;
  }

  // 计算每年査定数
  if (type === "year") {
    const yearlyData = rawData.reduce((acc: any, item: any) => {
      const year = dayjs(item[dateValuekKey]).format("YYYY"); // 获取年月
      if (!acc[year]) {
        acc[year] = {};
      }
      const userId = item.user?.id;
      if (!acc[year][userId]) {
        acc[year][userId] = { name: item.user?.name, count: 0 }; // 修改为对象以存储用户名
      }
      acc[year][userId].count += item._count?._all || item.count || 0; // 更新査定数
      return acc;
    }, {});
    return yearlyData;
  }

  return {};
}

export default function CommonChart({ rawData, distinctKey, dateValuekKey }: {
  rawData: any,
  distinctKey: "month" | "day" | "week" | "year",
  dateValuekKey: string
}) {
  let totalCount = 0;
  let parsedDataForType = {} as any;

  // 计算总査定数
  if (rawData) {
    parsedDataForType = getDataByType({ rawData, type: distinctKey, dateValuekKey });

    totalCount = rawData.reduce((acc: number, item: any) => acc + (item._count?._all || item.count || 0), 0);
  }

  // Generate dynamic colors (30+ unique colors)
  const getColor = (index: number) => {
    const colors = [
      "#A1C4FD", "#B39DDB", "#FFAB91", "#80CBC4", "#F48FB1", "#81D4FA", "#FFD54F", "#FF8A65",
      "#9FA8DA", "#B0BEC5", "#C5E1A5", "#E6EE9C", "#CE93D8", "#EF9A9A", "#90CAF9", "#A5D6A7",
      "#FFCC80", "#B0BEC5", "#F06292", "#9575CD", "#64B5F6", "#4DB6AC", "#BA68C8", "#FF8A65",
      "#AED581", "#DCE775", "#FF7043", "#90A4AE", "#E57373", "#FDD835"
    ];
    return colors[index % colors.length]; // Cycle through colors
  };

  const chartData = Object.entries(parsedDataForType)
    .map(([k, values]: any) => ({
      [distinctKey]: k,
      ...Object.entries(values).reduce((acc: any, [userId, { name, count }]: any) => {
        acc[name] = count;
        return acc;
      }, {})
    }))
    .sort((a: any, b: any) => dayjs(a[distinctKey]).diff(dayjs(b[distinctKey])))
    .filter((data: any) => dayjs().subtract(distinctKey === "month" ? 12 : distinctKey === "week" ? 26 : distinctKey === "year" ? 10 : 30, distinctKey).isBefore(dayjs(data[distinctKey]))); // 仅显示最近12个月的数据

  let distinctKeys = [...new Set(chartData && chartData?.flatMap((item: any) => Object.keys(item).filter((key: any) => key !== distinctKey)))];

  let chartConfig = {} as any;
  if (distinctKeys.length > 0) {
    distinctKeys.forEach((key: any, index: number) => {
      chartConfig[key] = {
        label: key,
        color: `hsl(var(--chart-${index + 1}))`,
      }
    })
  }

  const filledData = chartData.map((entry) => {
    return {
      ...entry,
      ...distinctKeys.reduce((acc: any, k) => {
        acc[k] = entry[k] ?? 0; // ✅ Replace missing values with 0
        return acc;
      }, {}),
    };
  });

  return <div className="flex flex-col gap-2">
    <div className="text-sm text-neutral-500 mb-4">合計データ数: {totalCount}</div> {/* 显示总査定数 */}
    <ChartContainer config={chartConfig}>
      <BarChart
        data={filledData}
        margin={{ left: 12, right: 12 }}
      >
        <CartesianGrid vertical={false} />
        <Legend />
        <XAxis
          dataKey={distinctKey}
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          tickFormatter={(value) =>
            distinctKey === "month"
              ? dayjs(value).format("YYYY-MM")
              : distinctKey === "week"
                ? dayjs(value).format("YYYY-MM-DD")
                : distinctKey === "year"
                  ? dayjs(value).format("YYYY")
                  : dayjs(value).format("YYYY-MM-DD")
          }
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          domain={[
            (dataMin: number) => parseFloat((dataMin * 0.9).toFixed(0)),
            (dataMax: number) => parseFloat((dataMax * 1.1).toFixed(0)),
          ]}
        />
        <ChartTooltip cursor={{ fill: "transparent" }} content={<ChartTooltipContent />} />

        {distinctKeys.map((user: any, index: number) => (
          <Bar
            key={user}
            dataKey={user}
            stackId="a"
            fill={getColor(index)}
          // label={{
          //   position: 'top',
          //   fill: '#333',
          //   fontSize: 12,
          // }}
          />
        ))}
      </BarChart>
    </ChartContainer>
  </div>
}