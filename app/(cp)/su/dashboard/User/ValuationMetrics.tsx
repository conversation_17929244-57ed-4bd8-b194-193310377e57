import { useState, useEffect } from "react";
import { getSuperAdminDashboardValuationAction } from "@/actions/superAdminDashboard";
import dayjs from "dayjs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import CommonChart from "./CommonChart";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Separator } from "@/components/ui/separator";

export default function ValuationMetrics() {
  const [valuationRes, setValuationRes] = useState<any>([]);
  const [view, setView] = useState<"day" | "month" | "week">("day");

  const getData = async () => {
    const res = await getSuperAdminDashboardValuationAction();
    setValuationRes(res.data);
  }

  useEffect(() => {
    getData();
  }, []);

  // 计算每个用户的总査定数
  const userTotalValuation = valuationRes.reduce((acc: any, item: any) => {
    const userId = item.user?.id;
    if (!acc[userId]) {
      acc[userId] = { name: item.user?.name, total: 0 };
    }
    acc[userId].total += item._count?._all || 0;
    return acc;
  }, {});


  return <>
    <div className="border border-neutral-200 rounded-lg bg-white">
      <div className="flex flex-row p-2 justify-center items-center border-b border-neutral-200">
        <div className="flex-1 font-bold">
          {view === "month" ? "MONTHLY" : view === "week" ? "WEEKLY" : "DAILY"}査定履歴
        </div>

        <div className="flex">
          <ToggleGroup type="single" onValueChange={(value) => setView(value as "day" | "month")} defaultValue={view}>
            <ToggleGroupItem value="day">日</ToggleGroupItem>
            <ToggleGroupItem value="week">週</ToggleGroupItem>
            <ToggleGroupItem value="month">月</ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>

      <div className="w-full p-2">
        {view === "month" && <CommonChart rawData={valuationRes} distinctKey={"month"} dateValuekKey={"valuationDate"} />}
        {view === "week" && <CommonChart rawData={valuationRes} distinctKey={"week"} dateValuekKey={"valuationDate"} />}
        {view === "day" && <CommonChart rawData={valuationRes} distinctKey={"day"} dateValuekKey={"valuationDate"} />}
      </div>

      {view === "day" && <div className="p-2">
        <Table className="w-full bg-neutral-50 border border-neutral-200">
          <TableHeader>
            <TableRow>
              <TableHead>ユーザー</TableHead>
              <TableHead>アクセスレベル</TableHead>
              <TableHead>検索日</TableHead>
              <TableHead>検索数</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {valuationRes?.sort((a: any, b: any) => {
              if (dayjs(a.valuationDate).isSame(dayjs(b.valuationDate), 'day')) {
                return b._count?._all - a._count?._all; // 按照数量排序
              }
              return dayjs(b.valuationDate).diff(dayjs(a.valuationDate)); // 按照搜索日期排序
            })?.slice(0, 10).map((item: any, index: number) => (
              <TableRow key={index}>
                <TableCell>{item.user?.name}</TableCell>
                <TableCell>{item.user?.accessLevel}</TableCell>
                <TableCell>{dayjs(item.valuationDate).format("YYYY-MM-DD")}</TableCell>
                <TableCell>{item._count?._all}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>}
    </div>

    <div className="border border-neutral-200 rounded-lg bg-white">
      <div className="font-bold p-2">TOTAL査定</div>
      <Separator className="" />
      <div className="p-2">
        {/* <span className="text-sm text-neutral-500">Show first 10 only</span> */}
        <Table className="border-none">
          <TableHeader>
            <TableRow>
              <TableHead>ユーザー名</TableHead>
              <TableHead>査定数</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Object.entries(userTotalValuation).sort((a: any, b: any) => b[1].total - a[1].total).map(([userId, user]: any) => (
              <TableRow key={userId}>
                <TableCell>{user.name || "不明"}</TableCell>
                <TableCell>{user.total}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  </>
}