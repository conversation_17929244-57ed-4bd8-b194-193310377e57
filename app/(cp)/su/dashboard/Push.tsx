"use client"

import { getAllPushSubscriptions, getUserCurrentActivePushSubscriptionsForDevice } from "@/actions/tllUserPushSubscriptions";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useEffect, useState } from "react"
import dayjs from "dayjs";

export default function Push() {
  const [pushSubscriptions, setPushSubscriptions] = useState<any[]>([]);

  useEffect(() => {
    const fetchPushSubscriptions = async () => {
      const res = await getAllPushSubscriptions()
      setPushSubscriptions(res.data)
    }
    fetchPushSubscriptions()
  }, [])

  return <div className="flex flex-col gap-4 p-4">
    <div className="flex flex-col gap-4 bg-neutral-50 border border-neutral-300">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>CreatedAt</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>DeviceId</TableHead>
            <TableHead>Subscription</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {pushSubscriptions.sort((a, b) => dayjs(b.createdAt).diff(dayjs(a.createdAt))).map((subscription) => (
            <TableRow key={subscription.id}>
              <TableCell>
                {dayjs(subscription.createdAt).format("YYYY-MM-DD")}
              </TableCell>
              <TableCell>{subscription.user.name}({subscription.user.id})</TableCell>
              <TableCell>{subscription.deviceId}</TableCell>
              {/* <TableCell>{JSON.stringify(subscription.subscription)}</TableCell> */}
            </TableRow>
          ))}
        </TableBody>



      </Table>
    </div>
  </div>
}