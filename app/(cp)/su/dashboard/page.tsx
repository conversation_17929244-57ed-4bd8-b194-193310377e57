"use client";

import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Suspense, useEffect, useState } from "react";
import dayjs from "dayjs";
import SearchMetrics from "./User/SearchMetrics";
import ValuationMetrics from "./User/ValuationMetrics";
import { Progress } from "@/components/ui/progress";
import { getSuperAdminOveralMetricsData } from "@/actions/superAdminDashboard";
import { Loader2 } from "lucide-react";

import { Tabs, TabsTrigger, TabsList } from "@/components/ui/tabs";
import System from "./System";
import Push from "./Push";
import { useSearchParams } from "next/navigation";
import FavMetrics from "./User/FavMetrics";
import UserMetrics from "./User/UserMetrics";
import DauMetrics from "./User/DauMetrics";
import { useRouter } from "next/navigation";

import NeedMetrics from "./User/NeedMetrics";

export default function DashboardPage() {
  const [tab, setTab] = useState<string>("user");
  const searchParams = useSearchParams()
  const tabFromParams = searchParams.get("tab") as string
  const router = useRouter();

  const [data, setData] = useState<any>({
    totalUsers: 0,
    totalPaidUsers: 0,
    totalValuationCount: 0,
    totalUsersThisWeek: 0,
    totalPaidUsersThisWeek: 0,
    totalValuationCountThisWeek: 0,
    totalUserThisMonth: 0,
    totalPaidUserThisMonth: 0,
    totalValuationCountThisMonth: 0,
    totalSearchCount: 0,
    totalSearchCountThisMonth: 0,
    totalSearchCountThisWeek: 0,
  });
  const [loadingMetrics, setLoadingMetrics] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>("need");

  let allGoals = {
    USER_GOAL: 500,
    PROPERTY_FAVORITE_GOAL: 2000,
    PROPERTY_WATCH_GOAL: 2000,
    VALUATION_GOAL: 5000,
    SEARCH_GOAL: 50000,
    DAU_GOAL: 100,
  }

  let getGoalForCurrentMonthEnd = (goal: number): number => {
    let currentMonth = dayjs().month() + 1;
    return Math.round(goal / 12 * currentMonth);
  }

  let dataForCard = [
    {
      title: "総ユーザー数",
      tabName: "user",
      value: data.totalUsers,
      incrementalValue: data.totalUserThisMonth,
      incrementalValueThisWeek: data.totalUsersThisWeek,
      goal: getGoalForCurrentMonthEnd(allGoals.USER_GOAL),
      percentage: data.totalUsers / getGoalForCurrentMonthEnd(allGoals.USER_GOAL) * 100
    },
    {
      title: "DAU",
      tabName: "dau",
      value: data.totalDauCount,
      incrementalValue: data.totalMauCount,
      incrementalValueThisWeek: data.totalWauCount,
      goal: getGoalForCurrentMonthEnd(allGoals.DAU_GOAL),
      percentage: data.totalDauCount / getGoalForCurrentMonthEnd(allGoals.DAU_GOAL) * 100
    },
    {
      title: "2025総閲覧数",
      tabName: "search",
      value: data.totalSearchCount,
      incrementalValue: data.totalSearchCountThisMonth,
      incrementalValueThisWeek: data.totalSearchCountThisWeek,
      goal: getGoalForCurrentMonthEnd(allGoals.SEARCH_GOAL),
      percentage: data.totalSearchCount / getGoalForCurrentMonthEnd(allGoals.SEARCH_GOAL) * 100
    },
    {
      title: "2025総査定数",
      tabName: "valuation",
      value: data.totalValuationCount,
      incrementalValue: data.totalValuationCountThisMonth,
      incrementalValueThisWeek: data.totalValuationCountThisWeek,
      goal: getGoalForCurrentMonthEnd(allGoals.VALUATION_GOAL),
      percentage: data.totalValuationCount / getGoalForCurrentMonthEnd(allGoals.VALUATION_GOAL) * 100
    },
    {
      title: "総気に入り物件数",
      tabName: "favorite",
      value: data.totalFavoriteThisYear,
      incrementalValue: data.totalFavoriteThisYear,
      incrementalValueThisWeek: data.totalFavoriteThisWeek,
      goal: getGoalForCurrentMonthEnd(allGoals.PROPERTY_FAVORITE_GOAL),
      percentage: data.totalFavoriteThisYear / getGoalForCurrentMonthEnd(allGoals.PROPERTY_FAVORITE_GOAL) * 100
    },
    {
      title: "総気に入り検索条件数",
      tabName: "need",
      value: data.totalNeedThisYear,
      incrementalValue: data.totalNeedThisYear,
      incrementalValueThisWeek: data.totalNeedThisWeek,
      goal: getGoalForCurrentMonthEnd(allGoals.PROPERTY_WATCH_GOAL),
      percentage: data.totalNeedThisYear / getGoalForCurrentMonthEnd(allGoals.PROPERTY_WATCH_GOAL) * 100
    },
  ]

  const getData = async () => {
    setLoadingMetrics(true);
    const res = await getSuperAdminOveralMetricsData();
    setData(res.data);
    setLoadingMetrics(false);
  }

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    if (tabFromParams) {
      setTab(tabFromParams);
    }
  }, [tabFromParams]);

  return (
    <div>
      <div className="flex flex-row gap-2 items-center px-4 pt-4">
        <Tabs value={tab} onValueChange={(value) => {
          setTab(value);
          router.push(`/su/dashboard?tab=${value}`);
        }} className="w-[300px]">
          <TabsList>
            <TabsTrigger value="user">ユーザー</TabsTrigger>
            <TabsTrigger value="system">システム</TabsTrigger>
            <TabsTrigger value="push">プッシュ通知</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {tab === "user" && <>
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 pb-0">
          {dataForCard.map((item, index) => (
            <Card className={`rounded-none py-2 cursor-pointer ${selectedTab === item.tabName ? "bg-blue-50 border-blue-200" : ""}`} key={index} onClick={() => setSelectedTab(item.tabName)}>
              <CardHeader className="p-4  pb-0">
                <CardTitle>{item.title}</CardTitle>
              </CardHeader>
              <CardContent className="p-4  pt-0 mt-2">
                {loadingMetrics ? <Loader2 className="w-4 h-4 animate-spin" /> : (
                  <div>
                    <div className="text-3xl font-bold">
                      {item.value}
                      <span className="text-xs text-gray-400 mt-1 ml-2">
                        / {item.goal}
                        ({dayjs().month() + 1}月末目標)
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      +{item.incrementalValueThisWeek} 今週 | +{item.incrementalValue} 今月
                    </div>

                    <div className="flex flex-row gap-2 items-center">
                      <Progress value={item.percentage} className={`mt-2 ${item.percentage >= 100 ? "bg-green-200" : "bg-neutral-200"}`} />
                      <span className="text-xs text-gray-400 mt-2">
                        {item.percentage.toFixed(2)}%
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        <Separator className="mt-4" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-neutral-50 rounded-lg">
          {selectedTab === "user" && <UserMetrics />}
          {selectedTab === "dau" && <DauMetrics />}
          {selectedTab === "search" && <SearchMetrics />}
          {selectedTab === "valuation" && <ValuationMetrics />}
          {selectedTab === "need" && <NeedMetrics />}
          {selectedTab === "favorite" && <FavMetrics />}
        </div>
      </>}

      {tab === "system" && <System />}

      {tab === "push" && <Push />}
    </div >
  );
}