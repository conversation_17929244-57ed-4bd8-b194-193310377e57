"use client";

import { Separator } from "@/components/ui/separator";
import { useAuthStore } from "@/store/auth";
import { useUIStore } from "@/store/ui";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { getUserLambdaRecordSearchHistory } from "@/actions/tllUserLambdaRecordSearchHistory";
import dayjs from "dayjs";


import { getPricingTierForUser } from "@/lib/constants/pricingTier";
import { TllUserProps } from "@/lib/definitions/tllUser";
import Pricing from "@/app/(homepage)/Pricing";
import {
  getTotalDailyUsageCount,
  getDailyUsageBreakdown,
  DAILY_USAGE_FEATURES
} from "@/lib/utils/dailyUsageTracker";
export default function UsagePage() {
  const { currentUser } = useAuthStore() as { currentUser: TllUserProps };

  // FIXME: this is using the most updated data, not the data from the store
  const [todaySearchCount, setTodaySearchCount] = useState<any>(null);
  const [priceChangeHistoryUsage, setPriceChangeHistoryUsage] = useState<{
    totalCount: number;
    breakdown: Array<{recordId: string, count: number}>;
  }>({ totalCount: 0, breakdown: [] });
  const [rentSearchUsage, setRentSearchUsage] = useState(0);

  useEffect(() => {
    getUserLambdaRecordSearchHistory({
      dateFrom: dayjs().startOf('month').toISOString(),
    }).then((res) => {
      if (res.success) {
        setTodaySearchCount(res.data);
      }
    });

    // Get PriceChangeHistory usage data
    const totalCount = getTotalDailyUsageCount(DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY);
    const breakdown = getDailyUsageBreakdown(DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY);
    setPriceChangeHistoryUsage({ totalCount, breakdown });

    // Get RentSearch usage data
    const rentCount = getTotalDailyUsageCount(DAILY_USAGE_FEATURES.RENT_SEARCH);
    setRentSearchUsage(rentCount);
  }, []);

  const getSearchByDate = () => {
    if (!todaySearchCount) return [];

    const distinctDates = [...new Set(todaySearchCount.map((item: any) => dayjs(item.searchDate).format('YYYY-MM-DD')))];
    const searchCount = distinctDates.map((d: any) => {
      const count = todaySearchCount.filter((item: any) => dayjs(item.searchDate).format('YYYY-MM-DD') === d).length;
      return { date: d, count };
    });
    return searchCount;
  }






  return <div>
    <div className="flex flex-row justify-between items-center p-4">
      <h1 className="text-2xl font-bold flex-1" aria-label="今日利用量">
        今日利用量
      </h1>
    </div>
    <Separator className="" />
    <div className="p-4 flex flex-col gap-4">

      <div className="flex flex-col gap-2 bg-gray-100 p-4 rounded-md">
        <div className="flex flex-row gap-2 items-center">
          <div className="text-lg font-bold flex-1">
            売買履歴閲覧数
          </div>

          <span className={todaySearchCount?.length > (getPricingTierForUser(currentUser as TllUserProps)?.dailySearchCount || 0) ? "text-red-500" : ""}>{todaySearchCount?.length} / {getPricingTierForUser(currentUser as TllUserProps)?.dailySearchCount || "∞"} 件</span>
        </div>

        <Separator className="" />

        <div className="text-sm text-gray-500">
          売買履歴閲覧数は、物件詳細履歴を閲覧した回数です。

          {getSearchByDate().map((item: any) => (
            <div key={item.date}>
              {item.date} : {item.count}
            </div>
          ))}
        </div>
      </div>
      {/* PriceChangeHistory Daily Usage Section - Only show for free users */}
      {currentUser?.accessLevel === 1 && (
        <div className="flex flex-col gap-2 bg-gray-100 p-4 rounded-md">
          <div className="flex flex-row gap-2 items-center">
            <div className="text-lg font-bold flex-1">
              価格変更履歴閲覧数
            </div>

            <span className={priceChangeHistoryUsage.totalCount >= (getPricingTierForUser(currentUser as TllUserProps)?.dailyPriceChangeHistoryCount || 3) ? "text-red-500" : ""}>
              {priceChangeHistoryUsage.totalCount} / {getPricingTierForUser(currentUser as TllUserProps)?.dailyPriceChangeHistoryCount || 3} 回
            </span>
          </div>

          <Separator className="" />

          <div className="text-sm text-gray-500">
            価格変更履歴閲覧数は、物件詳細ページで価格変更履歴を閲覧した回数です（物件ごとに個別カウント）。

            {priceChangeHistoryUsage.breakdown.length > 0 && (
              <div className="mt-2">
                <div className="font-semibold mb-1">今日の閲覧詳細:</div>
                {priceChangeHistoryUsage.breakdown.map((item) => (
                  <div key={item.recordId} className="text-xs">
                    物件ID {item.recordId.slice(-8)}: {item.count}回
                  </div>
                ))}
              </div>
            )}

            {priceChangeHistoryUsage.totalCount === 0 && (
              <div className="text-xs text-gray-400 mt-1">
                今日はまだ価格変更履歴を閲覧していません。
              </div>
            )}
          </div>
        </div>
      )}
      {/* RentSearch Daily Usage Section - Only show for free users */}
      {currentUser?.accessLevel === 1 && (
        <div className="flex flex-col gap-2 bg-gray-100 p-4 rounded-md">
          <div className="flex flex-row gap-2 items-center">
            <div className="text-lg font-bold flex-1">
              賃料査定数
            </div>

            <span className={rentSearchUsage >= (getPricingTierForUser(currentUser as TllUserProps)?.dailyRentSearchCount || 5) ? "text-red-500" : ""}>
              {rentSearchUsage} / {getPricingTierForUser(currentUser as TllUserProps)?.dailyRentSearchCount || 5} 回
            </span>
          </div>

          <Separator className="" />

          <div className="text-sm text-gray-500">
            賃料査定数は、賃料査定ページで異なる条件での査定を実行した回数です（異なる地域・建物種別の組み合わせごとに1回まで）。

            {rentSearchUsage === 0 && (
              <div className="text-xs text-gray-400 mt-1">
                今日はまだ賃料査定を実行していません。
              </div>
            )}

            {rentSearchUsage > 0 && (
              <div className="text-xs text-green-600 mt-1">
                今日は{rentSearchUsage}種類の条件で賃料査定を実行済みです。
              </div>
            )}
          </div>
        </div>
      )}





      <Separator className="" />

      <Pricing />
    </div >
  </div >
}
