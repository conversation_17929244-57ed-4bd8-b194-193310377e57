"use client";

import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { CircleFadingArrowUp } from "lucide-react";
import { useTranslations } from "next-intl";
import SupportPopUp from "@/components/SupportPopUp";

export default function NoPermissionBox({
  pageType
}: {
  pageType: "freeRentSearch" | "freeSearchUpside" | "freeRentCommon" | "areaStationRentBuilding" | "areaStationRentMansion" | "areaStationRentHouse" | "priceChangeHistory" | "priceChangeHistoryChart" | "rent"
}) {
  const t = useTranslations("General");

  let mapper = {
    freeRentSearch: "/assets/fuzzy/noAccess/rent-main.jpg",
    freeSearchUpside: "/assets/fuzzy/noAccess/search-dp-upside.jpg",
    freeRentCommon: "/assets/fuzzy/noAccess/dp-rent-common.jpg",
    areaStationRentBuilding: "/assets/fuzzy/noAccess/area-dp-rent-building.jpg",
    areaStationRentMansion: "/assets/fuzzy/noAccess/area-dp-rent-mansion.jpg",
    areaStationRentHouse: "/assets/fuzzy/noAccess/area-dp-rent-house.jpg",
    priceChangeHistory: "/assets/fuzzy/noAccess/price-change-history.jpg",
    priceChangeHistoryChart: "/assets/fuzzy/noAccess/price-change-history-chart.png",
    rent: "/assets/fuzzy/noAccess/rent.jpg",
  }

  return (
    <div className="relative h-full w-full">
      <div className="absolute top-0 left-0 w-full h-full bg-black/65 flex flex-col items-center justify-center gap-4">
        <CircleFadingArrowUp className="w-10 h-10 text-white" />
        <div className="text-white text-2xl font-bold">
          {t("noPermission")}
        </div>

        <Button variant="secondary" className="font-bold text-xl z-10 px-10 py-6" size={"lg"} asChild>
          <Link href="/my/billing" className="">{t("upgrade")}</Link>
        </Button>


        <SupportPopUp domType="text" message={t("orAddSupportStaff")} />
      </div>

      {/* Prioritize width */}
      <Image src={mapper[pageType as keyof typeof mapper]} alt="dp" width={1000} height={600} className="w-full object-cover top-0 left-0" />
    </div>
  );
}