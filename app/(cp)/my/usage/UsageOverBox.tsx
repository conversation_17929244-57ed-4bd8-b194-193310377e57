"use client";

import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Lock } from "lucide-react";
import { useTranslations } from "next-intl";

export default function UsageOverBox({
  pageType
}: {
  pageType: "searchDp" | "valuationDp" | "valuationMain" | "analysisAreaDp" | "analysisAreaMain" | "analysisStationDp" | "analysisStationMain" | "analysisMansionDp" | "analysisMansionMain"
}) {
  const t = useTranslations("Common");
  let mapper = {
    searchDp: "/assets/fuzzy/usageOver/search-dp.jpg",
    // searchMain: "/assets/fuzzy/usageOver/search-main.jpg",
    valuationDp: "/assets/fuzzy/usageOver/valuation-dp.jpg",
    valuationMain: "/assets/fuzzy/usageOver/valuation-main.jpg",
    analysisAreaDp: "/assets/fuzzy/usageOver/analysis-area-dp.jpg",
    analysisAreaMain: "/assets/fuzzy/usageOver/analysis-area-main.jpg",
    analysisStationDp: "/assets/fuzzy/usageOver/analysis-station-dp.jpg",
    analysisStationMain: "/assets/fuzzy/usageOver/analysis-station-main.jpg",
    analysisMansionDp: "/assets/fuzzy/usageOver/analysis-mansion-dp.jpg",
    analysisMansionMain: "/assets/fuzzy/usageOver/analysis-mansion-main.jpg",
  }

  return (
    <div className="relative h-full w-full">
      <div className="absolute top-0 left-0 w-full h-full bg-black/70 flex flex-col items-center justify-center gap-4">
        <Lock className="w-10 h-10 text-white" />
        <div className="text-white text-2xl font-bold">
          {t("usageReached")}
        </div>
        <div className="text-white text-sm">
          {t("usageReachedDescription")}
        </div>

        <Button variant="secondary" className="font-bold hover:bg-white hover:text-black z-10" size={"lg"} asChild>
          <Link href="/my/billing" className="">{t("upgrade")}</Link>
        </Button>
      </div>

      <Image src={mapper[pageType]} alt="dp" width={1000} height={2000} className="w-full h-full object-cover top-0 left-0" />
    </div>
  );
}