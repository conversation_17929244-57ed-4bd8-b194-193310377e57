"use client";

import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Clock, CircleFadingArrowUp } from "lucide-react";
import { useTranslations } from "next-intl";
import SupportPopUp from "@/components/SupportPopUp";

export default function DailyLimitExhaustedBox({
  pageType,
  currentCount,
  maxCount
}: {
  pageType: "priceChangeHistory" | "priceChangeHistoryChart" | "rent";
  currentCount: number;
  maxCount: number;
}) {
  const t = useTranslations("General");

  let mapper = {
    priceChangeHistory: "/assets/fuzzy/noAccess/price-change-history.jpg",
    priceChangeHistoryChart: "/assets/fuzzy/noAccess/price-change-history-chart.png",
    rent: "/assets/fuzzy/noAccess/rent.jpg",
  }

  return (
    <div className="relative h-full w-full">
      <div className="absolute top-0 left-0 w-full h-full bg-black/65 flex flex-col items-center justify-center gap-4">
        <Clock className="w-10 h-10 text-white" />
        <div className="text-white text-2xl font-bold">
          {t("dailyLimitReached")}
        </div>
        <div className="text-white text-sm text-center px-4">
          今日の利用回数: {currentCount}/{maxCount}
          <br />
          明日またお試しください
        </div>

        <Button variant="secondary" className="font-bold text-xl z-10 px-10 py-6" size={"lg"} asChild>
          <Link href="/my/billing" className="">{t("upgrade")}</Link>
        </Button>

        <SupportPopUp domType="text" message={t("orAddSupportStaff")} />
      </div>

      {/* Prioritize width */}
      <Image src={mapper[pageType as keyof typeof mapper]} alt="dp" width={1000} height={600} className="w-full object-cover top-0 left-0" />
    </div>
  );
}
