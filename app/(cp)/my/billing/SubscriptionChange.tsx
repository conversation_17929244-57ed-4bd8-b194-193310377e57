"use client"

import { createStripePortalSession, getUserSubscriptionStatus } from "@/actions/tllUserSubscription";
import { getUserByEmail } from "@/actions/tllUser";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { useAuthStore } from "@/store/auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { TllUserProps, TllUserSubscriptionProps, TllUserSubscriptionStatus } from "@/lib/definitions/tllUser";
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";

export default function SubscriptionChange() {
  const { currentUser } = useAuthStore();
  const router = useRouter();
  const [user, setUser] = useState<TllUserProps | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [userSubscriptionStatus, setUserSubscriptionStatus] = useState<TllUserSubscriptionProps[] | null>(null);
  const t = useTranslations("Billing");

  useEffect(() => {
    if (currentUser?.email) {
      getUserByEmail({ email: currentUser?.email as string }).then((res) => {
        setUser(res.data);
      });

      getUserSubscriptionStatus({ email: currentUser?.email as string }).then((res: any) => {
        setUserSubscriptionStatus(res.data);
      });
    }
  }, [currentUser]);


  const goToPortal = async () => {
    setIsLoading(true);
    let res = await createStripePortalSession();
    if (res.success) {
      window.location.href = res.data;
    } else {
      toast({
        title: "エラー",
        description: res.message,
      });
    }
    setIsLoading(false);
  }

  const findNextSubscriptionEndTime = () => {
    if (!userSubscriptionStatus) {
      return null;
    }

    const now = new Date();
    const nextSubscriptionEndTime = userSubscriptionStatus?.find((status: any) => status.subscriptionEndAt > now && status.subscriptionStatus === "ACTIVE");
    return nextSubscriptionEndTime?.subscriptionEndAt?.toLocaleDateString();
  }

  return <div className="flex flex-col bg-neutral-50 border border-neutral-300">
    <div className="flex justify-between items-center p-2 border-b border-neutral-300">
      <h2 className="text-lg font-bold" aria-label={t("subscriptionChange")}>{t("subscriptionChange")}</h2>
    </div>

    <div className="p-2">
      <div className="flex flex-col gap-4">
        <div className="flex flex-row items-center justify-start gap-2">
          {t("currentPlan")}: <Badge variant="outline" className="font-bold">
            {user?.subscriptionPlan || "FREE"}
          </Badge>
        </div>

        <div className="flex justify-start">
          <p>
            {t("nextBillingDate")}: {findNextSubscriptionEndTime() || "-"}
          </p>
        </div>

        <div className="flex justify-start flex-col gap-2">
          <p>
            {t("subscriptionHistory")}:
          </p>
          <div className="flex flex-col gap-2">
            {userSubscriptionStatus?.map((status: any) => (
              <p key={status.id} className="flex flex-row gap-2">
                <Badge variant="outline" className="font-bold">
                  {status.subscriptionPlan}
                </Badge>
                {status.subscriptionStartAt?.toLocaleDateString()} - {status.subscriptionEndAt?.toLocaleDateString()}
                <Badge variant="outline" className="font-bold"> {status.subscriptionStatus} </Badge>
              </p>
            ))}
          </div>
        </div>

        <div className="flex justify-start">
          {/* <Link href="https://billing.stripe.com/p/login/eVa00g9UkdwseHedQQ"> */}
          <Button variant="default" onClick={() => {
            goToPortal();
          }} disabled={isLoading || (!userSubscriptionStatus || userSubscriptionStatus.length === 0)}>
            {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : t("manageSubscription")}
          </Button>
          {/* </Link> */}
        </div>
      </div>
    </div>
  </div>
}
