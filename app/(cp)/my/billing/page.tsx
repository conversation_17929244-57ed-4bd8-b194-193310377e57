"use client";

import Pricing from "@/app/(homepage)/Pricing";
import SubscriptionChange from "./SubscriptionChange";
import { Separator } from "@/components/ui/separator";
import { useAuthStore } from "@/store/auth";
import { TllUserSubscriptionStatus } from "@/lib/definitions/tllUser";
import { useTranslations } from "next-intl";

export default function Billing() {
  const { currentUser } = useAuthStore();
  const t = useTranslations("Billing");

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label={t("title")}>{t("title")}</h1>
    </div>

    <Separator className="" />

    <div className="p-4">
      <SubscriptionChange />
    </div>

    <Separator className="" />

    <div className="p-4">
      <div className="bg-neutral-50 border border-neutral-300">
        <Pricing />
      </div>
    </div>
  </div>
}