"use client";

import { Separator } from "@/components/ui/separator";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, Phone, MessageCircle, ExternalLink } from "lucide-react";
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import Image from "next/image";
import Link from "next/link";
import SupportPopUp from "@/components/SupportPopUp";

export default function SupportPage() {
  const t = useTranslations("Support");
  const tHomepage = useTranslations("Homepage");

  return (
    <div>
      <div className="flex flex-row justify-between items-center p-4">
        <h1 className="text-2xl font-bold flex-1" aria-label={t("title")}>
          {t("title")}
        </h1>
      </div>

      <Separator className="" />

      <div className="p-4 flex flex-col gap-4">
        <div className="flex flex-col gap-4 bg-gray-100 p-4 rounded-md">
          <div className="text-lg font-bold">{t("contactSupportStaff")}</div>
          <Separator className="" />
          <div className="text-sm text-gray-500">
            {t("contactSupportStaffDescription")}
          </div>

          <div className="flex flex-row gap-4 flex-nowrap overflow-x-auto">
            <SupportPopUp />

            <Button variant="outline" className="w-full" onClick={() => {
              window.open("mailto:<EMAIL>", "_blank");
            }}>
              <Mail className="w-4 h-4" />
              {t("mail")}
            </Button>

            <Button variant="outline" className="w-full" onClick={() => {
              window.open("tel:+81345663208");
            }}>
              <Phone className="w-4 h-4" />
              {t("phone")}
            </Button>
          </div>
        </div>

        <div className="flex flex-col gap-4 bg-gray-100 p-4 rounded-md">
          <div className="text-lg flex flex-row justify-between items-center">
            <div className="font-bold">FAQ</div>
          </div>

          <Separator className="" />
          <Accordion type="single" collapsible defaultValue="item-0">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((faq: any, index: number) => (
              <AccordionItem key={index} value={`item-${index}`} className=" text-black mb-1">
                <AccordionTrigger className="p-4 text-base sm:text-sm font-semibold">{tHomepage(`faq.question${index + 1}.question`)}</AccordionTrigger>
                <AccordionContent className="px-4  text-base sm:text-sm text-black">{tHomepage(`faq.question${index + 1}.answer`)}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          <div className="text-gray-500 underline pl-4">
            <Link href="https://tllrealestate.jp.larksuite.com/wiki/SGdJwZNMqirvCKkABFbjhWEdpSd?from=from_copylink" target="_blank" className="flex flex-row items-center gap-2">
              <ExternalLink className="w-4 h-4" /> {t("noAnswerCheckWiki")}
            </Link>
          </div>
        </div>

        <div className="flex flex-col bg-gray-100 rounded-md">
          <div className="text-lg font-bold p-4">{t("bookSession")}</div>
          <Separator className="" />
          <iframe
            src="https://cal.com/譚琛-tvz53r/15min"
            width="100%"
            height="650"
            style={{ border: '0' }}
            allowFullScreen
          ></iframe>
        </div>
      </div>
    </div>
  );
}