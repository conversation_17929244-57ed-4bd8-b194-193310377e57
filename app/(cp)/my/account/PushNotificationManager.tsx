"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/hooks/use-toast";
import {
  createUserPushSubscription,
  deleteUserPushSubscription,
  getUserCurrentActivePushSubscriptionsForDevice,
  sendNotificationToMe,
} from "@/actions/tllUserPushSubscriptions";
import { TllUserPushSubscriptionProps } from "@/lib/definitions";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { Loader2 } from "lucide-react";

export const urlBase64ToUint8Array = (base64String: string) => {
  const padding = "=".repeat((4 - (base64String.length % 4)) % 4);
  const base64 = (base64String + padding).replace(/-/g, "+").replace(/_/g, "/");

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
};

export const getDeviceId = () => {
  if (typeof window !== "undefined") {
    // Ensures it runs only in the browser
    const existingDeviceId = localStorage.getItem("deviceId");
    if (existingDeviceId) {
      return existingDeviceId;
    }
    const newDeviceId = crypto.randomUUID();
    localStorage.setItem("deviceId", newDeviceId);
    return newDeviceId;
  }
  // Return a consistent fallback for server-side rendering
  return "server-side-fallback";
};

export const PushNotificationManager = ({
  currentUser,
}: {
  currentUser: TllUserProps | null;
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [subscription, setSubscription] = useState<any>(null);
  const [activeSubscription, setActiveSubscription] =
    useState<TllUserPushSubscriptionProps | null>(null);
  const deviceId = getDeviceId();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if ("serviceWorker" in navigator && "PushManager" in window) {
      setIsSupported(true);
      registerServiceWorker();
    }

    getUserCurrentActivePushSubscriptionsForDevice(deviceId).then(
      (res: any) => {
        if (res.success) {
          setActiveSubscription(res.data);
        }
      },
    );
  }, []);

  async function registerServiceWorker() {
    const registration = await navigator.serviceWorker.register("/sw.js", {
      scope: "/",
      updateViaCache: "none",
    });
    const sub = (await registration.pushManager.getSubscription()) as any;
    setSubscription(sub);
  }

  async function subscribeToPush() {
    try {
      setIsLoading(true);
      const registration = await navigator.serviceWorker.ready;
      const sub = (await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(
          "BCzpwf2zq50OBW6OKa3mp3Kzcu9vIZJt-tehye2WBQovqw-tqhaBXrNpxH7BZHQWhjj3g37JzgjE_0ECkDG2Q1g",
        ),
      })) as any;

      let res = await createUserPushSubscription(sub.toJSON(), deviceId);
      if (res.success) {
        toast({
          title: "通知を受け取るようにしました",
        });
        setSubscription(sub);
        setActiveSubscription(res.data);
      } else {
        toast({
          title: "通知を受け取るようにできませんでした",
          description: res.message,
        });
      }
    } catch (error: any) {
      console.error("Error subscribing to push notifications:", error);
      toast({
        title: "通知を受け取るようにできませんでした",
        description: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  }

  async function unsubscribeFromPush() {
    if (activeSubscription) {
      setIsLoading(true);
      let res = await deleteUserPushSubscription(activeSubscription?.id);
      if (res.success) {
        toast({
          title: "通知を受け取らないようにしました",
        });
        setSubscription(null);
        await subscription?.unsubscribe();
        setActiveSubscription(null);
      } else {
        toast({
          title: "通知を受け取らないようにできませんでした",
          description: res.message,
        });
      }
      setIsLoading(false);
    }
  }

  if (!isSupported) {
    return <p>Push notifications are not supported in this browser.</p>;
  }

  return (
    <div className="flex flex-col gap-4">
      {currentUser?.accessLevel && currentUser?.accessLevel >= 30 && (
        <div className="flex flex-col gap-4 mt-4">
          <Card className="p-4 flex flex-col gap-4">
            <div className="flex flex-row gap-4 flex-1 items-center justify-between">
              <div className="flex flex-col flex-1">
                <p className="text-base font-bold">新規物件の通知を受け取る</p>
                <p className="text-sm text-gray-500">- 見張り中変更あり物件</p>
                <p className="text-sm text-gray-500">- 気に入った物件変更</p>
              </div>

              <div className="flex items-center gap-3">
                {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Switch
                  checked={activeSubscription ? true : false}
                  onCheckedChange={async () => {
                    if (activeSubscription) {
                      await unsubscribeFromPush();
                      toast({
                        title: "通知を受け取らないようにしました",
                      });
                    } else {
                      await subscribeToPush();
                      toast({
                        title: "通知を受け取るようにしました",
                      });
                    }
                  }}
                />
              </div>
            </div>
            {/* <div className="flex flex-col text-sm text-gray-500">
            <p>Current browser subscription is {subscription?.endpoint?.slice(0, 20)}...</p>
            <p>Current endpoint in DB is  {activeSubscription?.subscription?.endpoint?.slice(0, 20)}...</p>
          </div> */}
          </Card>
        </div>
      )}
    </div>
  );
};
