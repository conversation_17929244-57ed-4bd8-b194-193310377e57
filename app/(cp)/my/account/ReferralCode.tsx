"use client"

import { useReferralCode } from "@/actions/referralCode"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Loader2, Mail } from "lucide-react"
import { useEffect, useState } from "react"
import { toast } from "@/hooks/use-toast"
import { useUIStore } from "@/store/ui"
import { getUserByEmail } from "@/actions/tllUser"
import { useAuthStore } from "@/store/auth"
import { useRouter, useSearchParams } from "next/navigation"

export default function ReferralCode() {
  const searchParams = useSearchParams();

  const { currentUserFull, setCurrentUserFull } = useUIStore()
  const [loading, setLoading] = useState(false)
  const [code, setCode] = useState(searchParams.get("code") || "")
  const { currentUser, logout } = useAuthStore();
  const router = useRouter();

  const getUserFull = async () => {
    if (currentUser && currentUserFull === null) {
      const res = await getUserByEmail({ email: currentUser.email as string })
      if (res.success) {
        console.log("res.data", res.data)
        setCurrentUserFull(res.data)
      }
    }
  }

  useEffect(() => {
    getUserFull()
  }, [currentUser])

  async function useCode() {
    setLoading(true)
    const res = await useReferralCode(code)

    if (res.success) {
      toast({
        title: "紹介コードが利用されました、再ログインしてください...",
        description: res.message,
        duration: 10000,
      })

      fetch('/api/auth/logout', { method: 'POST' });
      logout();
      router.push('/');
    } else {
      toast({
        title: "紹介コードが利用できません",
        description: res.message,
        variant: "destructive",
        duration: 3000,
      })
    }
    setLoading(false)
  }

  console.log("currentUserFull", currentUserFull)

  return <div className="flex flex-col gap-8">

    <div className="bg-neutral-100 rounded-md p-4 flex flex-row gap-2">
      <div className="flex flex-col gap-2 flex-1">
        <p className="text-sm text-neutral-600">利用した紹介コード</p>
        <p className="text-lg text-neutral-600">
          {currentUserFull?.referralCode?.code ? currentUserFull?.referralCode?.code : "なし"}
        </p>
      </div>

      <div className="flex flex-col gap-2 justify-end items-end">
        <p className="text-sm text-neutral-600">紹介者</p>
        <p className="text-lg text-neutral-600 hover:underline flex flex-row justify-middle items-center gap-1 w-full" onClick={() => {
            window.open("mailto:" + currentUserFull?.referralCode?.createdByUser?.email, "_blank");
            }}>
          {currentUserFull?.referralCode?.createdByUser?.name ? currentUserFull?.referralCode?.createdByUser?.name : "なし"}
          <Mail className="w-4 h-4" />  
        </p>
      </div>
    </div>

    <div className="flex flex-col gap-2">
      <p className="text-sm text-neutral-600">
        紹介コード(利用は一回のみ、利用後再ログインが必要となります)
      </p>

      <Input type="text" placeholder="紹介コードを入力してください" value={code} onChange={(e) => setCode(e.target.value)} disabled={currentUserFull?.referralCodeId} />

      <div className="flex justify-start">
        <Button className="" disabled={currentUserFull?.referralCodeId || loading} onClick={() => {
          useCode();
        }}>
          {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : "利用する"}
        </Button>
      </div>
    </div>
  </div>
}