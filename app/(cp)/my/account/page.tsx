"use client"

import { Separator } from "@/components/ui/separator";
import { PushNotificationManager } from "./PushNotificationManager";
import { useAuthStore } from "@/store/auth";
import { PasswordChange } from "./PasswordChange";
import { ProfileChange } from "./ProfileChange";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { toast } from "@/hooks/use-toast";
import { Switch } from "@/components/ui/switch";
import { Loader2 } from "lucide-react";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { getUserByEmail, updateUserEmailSubscription, updateUserSetting } from "@/actions/tllUser";
import ReferralCode from "./ReferralCode";
import { useUIStore } from "@/store/ui";

export default function MyAccountPage() {
  const { currentUser } = useAuthStore()
  const [tab, setTab] = useState("profile")
  const searchParams = useSearchParams()
  const tabFromParams = searchParams.get("tab") as string
  const referralCode = searchParams.get("code") as string
  const router = useRouter()

  const { currentUserFull, setCurrentUserFull } = useUIStore()
  const [isLoading, setIsLoading] = useState(false)

  console.log("currentUserFull", currentUserFull)

  useEffect(() => {
    if (tabFromParams) {
      if(referralCode) {
        // FIX: this is because the link https://www.urbalytics.jp/my/account?tab=signUp&code=5ZVT8NTX
        // So it will use the signUp tab in signup but no tab from account
        setTab("referralCode")
      } else {
      setTab(tabFromParams)
      }
    }
  }, [tabFromParams])

  const getUserFull = async (email: string) => {
    const res = await getUserByEmail({ email })
    if (res.success) {
      setCurrentUserFull(res.data)
    }
  }

  useEffect(() => {
    if (currentUser && !currentUserFull) {
      getUserFull(currentUser.email as string)
    }
  }, [currentUser])

  return <div>
    <div className="flex justify-between items-center p-4">
      <h1 className="text-2xl font-bold" aria-label="アカウント">こんにちは、{currentUser?.name}さん</h1>
    </div>

    <Separator className="" />

    <div className="p-4">
      <Tabs value={tab} onValueChange={(value) => {
        setTab(value)
        router.push(`/my/account?tab=${value}`)
      }}>
        <TabsList>
          <TabsTrigger value="profile">プロフィール & パスワード</TabsTrigger>
          <TabsTrigger value="push">プッシュ通知</TabsTrigger>
          <TabsTrigger value="referralCode">紹介コードを利用</TabsTrigger>
        </TabsList>
      </Tabs>
    </div>

    <div className="flex flex-col p-4 gap-8">
      {tab === "referralCode" && <>
        <ReferralCode />
      </>}

      {tab === "profile" && <>
        <ProfileChange />
        <PasswordChange />
      </>}

      {tab === "push" &&
        <div className="flex flex-col gap-4">
          <div className="flex flex-col bg-neutral-50 border border-neutral-300">
            <div className="flex justify-between items-center p-2 border-b border-neutral-300">
              <h2 className="text-lg font-bold" aria-label="メール通知">メール通知</h2>
            </div>

            <div className="p-2 flex flex-col gap-4">
              <div className="flex flex-row gap-4 flex-1 items-center justify-between bg-white p-4 border border-neutral-300 rounded-md">
                <div className="flex flex-col flex-1">
                  <p className="text-base font-bold">おすすめ物件の通知を受け取る</p>
                  <p className="text-sm text-gray-500">
                    - １割値下げ物件
                  </p>
                  <p className="text-sm text-gray-500">
                    - 利回り７％以上物件
                  </p>
                </div>

                {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Switch
                  checked={currentUserFull?.emailSubscription?.propertyPickup ? true : false}
                  onCheckedChange={async () => {
                    await updateUserEmailSubscription({ propertyPickup: !currentUserFull?.emailSubscription?.propertyPickup })

                    await getUserFull(currentUser?.email as string)
                    toast({
                      title: "メール通知を更新しました",
                    })
                  }}
                />
              </div>

              <div className="flex flex-row gap-4 flex-1 items-center justify-between bg-white p-4 border border-neutral-300 rounded-md">
                <div className="flex flex-col flex-1">
                  <p className="text-base font-bold">Urbalyticsエージェントより物件紹介メールを受け取る</p>
                </div>

                {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
                <Switch
                  checked={currentUserFull?.emailSubscription?.salesManMarketingEmail ? true : false}
                  onCheckedChange={async () => {
                    await updateUserEmailSubscription({ salesManMarketingEmail: !currentUserFull?.emailSubscription?.salesManMarketingEmail })

                    await getUserFull(currentUser?.email as string)

                    toast({
                      title: "メール通知を更新しました",
                    })
                  }}
                />
              </div>
            </div>
          </div>

          <div className="flex flex-col bg-neutral-50 border border-neutral-300">
            <div className="flex justify-between items-center p-2 border-b border-neutral-300">
              <h2 className="text-lg font-bold" aria-label="プッシュ通知">プッシュ通知</h2>
            </div>

            <div className="p-2">
              <PushNotificationManager currentUser={currentUser ?? null} />
            </div>
          </div>
        </div>
      }
    </div>
  </div>
}
