"use client"

import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { useEffect, useState } from "react"
import { toast } from "@/hooks/use-toast"
import { useAuthStore } from "@/store/auth"
import { updateUserProfile } from "@/actions/tllUser"

export function ProfileChange() {
  const { currentUser, setCurrentUser } = useAuthStore()
  const [name, setName] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (currentUser) {
      setName(currentUser.name ?? "")
    }
  }, [currentUser])

  const submit = async () => {
    if (name === "") {
      toast({
        title: "プロフィール名を入力してください",
      })
      return
    }

    let response = await updateUserProfile({ name })

    if (response.success) {
      let newUser = {
        ...currentUser,
        ...response.data,
      }

      console.log("🔥newUser", newUser)

      // FIXME: this wont really work as the setCurrentUser in header will reset it everytime you refresh the page

      setCurrentUser(newUser)
      // const session = await getSession(); // optional: get current session
      // await update(); // ask next-auth to re-fetch JWT from your jwt callback

      toast({
        title: "プロフィールを変更しました",
      })
    } else {
      toast({
        title: "プロフィールを変更できませんでした",
        description: response.message,
      })
    }
  }

  return <div className="flex flex-col bg-neutral-50 border border-neutral-300">
    <div className="flex justify-between items-center p-2 border-b border-neutral-300">
      <h2 className="text-lg font-bold" aria-label="プロフィール変更">プロフィール変更</h2>
    </div>

    <div className="p-2">
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-4">
          <label htmlFor="name">プロフィール名</label>
          <Input type="name" id="name" value={name} onChange={(e) => setName(e.target.value)} />
        </div>

        <div className="flex justify-start">
          <Button onClick={async () => {
            setIsLoading(true)
            await submit();
            setIsLoading(false)
          }} disabled={isLoading}>{isLoading ? "変更中..." : "変更"}</Button>
        </div>
      </div>
    </div>
  </div>
}
