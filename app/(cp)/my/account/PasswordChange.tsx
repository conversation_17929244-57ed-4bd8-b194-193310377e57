"use client"

import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { useState } from "react"
import { toast } from "@/hooks/use-toast"
import { changePasswordAction } from "@/actions/users"
import { <PERSON>ert, AlertTitle, AlertDescription } from "@/components/ui/alert"
import { Info, Link } from "lucide-react"
import { BellRing } from "lucide-react"
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity"
import { useAuthStore } from "@/store/auth"
import { useRouter } from "next/navigation"

export function PasswordChange() {
  const { currentUser, logout } = useAuthStore();
  const router = useRouter();
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [newPasswordConfirm, setNewPasswordConfirm] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const submit = async () => {
    if (currentPassword === "" || newPassword === "" || newPasswordConfirm === "") {
      toast({
        title: "パスワードを入力してください",
        variant: "destructive",
      })
      return
    }

    if (newPassword !== newPasswordConfirm) {
      toast({
        title: "新しいパスワードが一致しません",
        variant: "destructive",
      })
      return
    }

    if (newPassword.length < 8) {
      toast({
        title: "新しいパスワードが短すぎます",
        variant: "destructive",
      })
      return
    }

    if (!/^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,}$/.test(newPassword)) {
      toast({
        title: "新しいパスワードが英数字の組み合わせで入力してください",
        variant: "destructive",
      })
      return
    }

    let response = await changePasswordAction({ currentPassword, newPassword })

    if (response.success) {
      toast({
        title: "パスワードを変更しました... ログイン画面からログインしなおしてください",
        description: "ログイン画面からログインしなおしてください",
        duration: 2000,
      })

      createNewSystemUserActivityAction({
        data: {
          eventType: "LOGOUT",
          route: "/api/auth/logout",
        },
      });
      fetch('/api/auth/logout', { method: 'POST' });
      logout();
      router.push('/');
    } else {
      toast({
        title: "パスワードを変更できませんでした",
        description: response.message,
        variant: "destructive",
      })
    }
  }

  return <div className="flex flex-col bg-neutral-50 border border-neutral-300">
    <div className="flex justify-between items-center p-2 border-b border-neutral-300">
      <h2 className="text-lg font-bold" aria-label="パスワード変更">パスワード変更</h2>
    </div>

    <div className="p-2">
      <div className="flex flex-col gap-4">
        <Alert variant="default" className="bg-gray-100">
          <Info className="h-4 w-4" />
          <AlertDescription>
            新パスワードは8文字以上で入力してください。英文字と数字の組み合わせてください。
          </AlertDescription>
        </Alert>

        <div className="flex flex-col gap-4">
          <label htmlFor="password">現在のパスワード</label>
          <Input type="password" id="password" value={currentPassword} onChange={(e) => setCurrentPassword(e.target.value)} />
        </div>

        <div className="flex flex-col gap-2">
          <label htmlFor="password">新しいパスワード</label>
          <Input type="password" id="password" value={newPassword} onChange={(e) => setNewPassword(e.target.value)} />
        </div>

        <div className="flex flex-col gap-2">
          <label htmlFor="password">新しいパスワード（確認）</label>
          <Input type="password" id="password" value={newPasswordConfirm} onChange={(e) => setNewPasswordConfirm(e.target.value)} />
        </div>

        <div className="flex justify-start">
          <Button onClick={async () => {
            setIsLoading(true)
            await submit();
            setIsLoading(false)
          }} disabled={isLoading}>{isLoading ? "変更中..." : "変更"}</Button>
        </div>
      </div>
    </div>
  </div>
}
