import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { mean } from "lodash-es";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { removeOutlier } from "@/lib/helper/stats";

export const getAllTimeAverageSellingPrice = (records: UserLambdaRecordProps[]): {
  averageUnitPrice: any,
  averageUnitSize: any,
  averagePrice: any,
  averagePriceRaw: any
} => {
  if (!records) {
    return {
      averageUnitPrice: 0,
      averageUnitSize: 0,
      averagePrice: 0,
      averagePriceRaw: 0,
    };
  }

  let arrayForUnitPrice: number[] = [];
  let arrayForUnitSize: number[] = [];

  records.forEach((record) => {
    let unitSize = record.buildingSize || record.recordValues?.unitArea || record.landSize;

    if (record.priceChanges) {
      record.priceChanges.forEach((priceChange) => {
        if (priceChange.price && priceChange.price > 0 && unitSize > 0) {
          arrayForUnitPrice.push(priceChange.price / unitSize);
          arrayForUnitSize.push(unitSize);
        }
      });
    } else if (record.price && record.price > 0 && record.buildingSize) {
      arrayForUnitPrice.push(record.price / record.buildingSize);
      arrayForUnitSize.push(record.buildingSize);
    }
  });

  arrayForUnitPrice = removeOutlier(arrayForUnitPrice);
  arrayForUnitSize = removeOutlier(arrayForUnitSize);


  let res = {
    averageUnitPrice: parseFloat((mean(arrayForUnitPrice) * 3.3).toFixed(0)).toLocaleString() || 0,
    averageUnitSize: parseFloat(mean(arrayForUnitSize).toFixed(2)).toLocaleString() || 0,
    averagePrice: parseFloat((mean(arrayForUnitPrice) * mean(arrayForUnitSize)).toFixed(0)).toLocaleString() || 0,
    averagePriceRaw: mean(arrayForUnitPrice) * mean(arrayForUnitSize) || 0,
  }

  return res;
}

export const getAllTimeAverageRentPrice = (records: ProMansionRentProps[]): {
  averageUnitPrice: any,
  averageUnitSize: any,
  averagePrice: any,
} => {
  if (!records) {
    return {
      averageUnitPrice: 0,
      averageUnitSize: 0,
      averagePrice: 0,
    };
  }

  let arrayForUnitPrice: number[] = [];
  let arrayForUnitSize: number[] = [];

  records.forEach((record) => {
    if (record.feeRent && record.unitSize) {
      arrayForUnitPrice.push((record.feeRent + (record.feeManagement || 0) / 10000) / record.unitSize);
      arrayForUnitSize.push(record.unitSize);
    }
  });

  return {
    averageUnitPrice: parseFloat((mean(arrayForUnitPrice) * 3.3).toFixed(2)).toLocaleString() || 0,
    averageUnitSize: parseFloat(mean(arrayForUnitSize).toFixed(2)).toLocaleString() || 0,
    averagePrice: parseFloat((mean(arrayForUnitPrice) * mean(arrayForUnitSize)).toFixed(0)).toLocaleString() || 0,
  }
}

export const getAllTimeAverageBuildingHouseRentPrice = (records: ProBuildingHouseRentProps[]): {
  averageUnitPrice: number,
  averageUnitSize: number,
  averagePrice: number,
} => {
  if (!records) {
    return {
      averageUnitPrice: 0,
      averageUnitSize: 0,
      averagePrice: 0,
    };
  }

  let arrayForUnitPrice: number[] = [];
  let arrayForUnitSize: number[] = [];

  records.forEach((record) => {
    if (record.feeRent && record.buildingSize) {
      arrayForUnitPrice.push((record.feeRent + (record.feeManagement || 0) / 10000) / record.buildingSize);
      arrayForUnitSize.push(record.buildingSize);
    }
  });

  return {
    averageUnitPrice: parseFloat((mean(arrayForUnitPrice) * 3.3).toFixed(2)) || 0,
    averageUnitSize: parseFloat(mean(arrayForUnitSize).toFixed(2)) || 0,
    averagePrice: parseFloat((mean(arrayForUnitPrice) * mean(arrayForUnitSize)).toFixed(0)) || 0,
  }
}