"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import SearchBarMansionBuildingUI from "@/components/ui/SearchBarMansionBuildingUI";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { SystemReportViewHistoryProps, SystemReportViewHistoryRecordType } from "@/lib/definitions/system";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";
import { Loader2 } from "lucide-react";
import { getMyReportViewHistory } from "@/actions/systemReportViewHistory";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import Link from "next/link";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";


export default function Mansion() {
  const [selectedBuilding, setSelectedBuilding] = useState<ProBuildingProps | null>(null);
  const router = useRouter();
  const currentUser = useAuthStore((state) => state.currentUser);
  const [isLoading, setIsLoading] = useState(false);
  const [myHistory, setMyHistory] = useState<SystemReportViewHistoryProps[]>([]);
  useEffect(() => {
    getMyReportViewHistory({
      recordType: SystemReportViewHistoryRecordType.MANSION,
    }).then((res) => {
      setMyHistory(res.data);
    });
  }, []);

  return (
    <div>
      <div className="px-4 py-2 flex flex-row gap-4 w-full mt-2">
        <div className="flex-1 w-full">
          <SearchBarMansionBuildingUI selectedBuilding={selectedBuilding} setSelectedBuilding={setSelectedBuilding} />
        </div>

        <Button id="goAnalysisDpButton" onClick={() => {
          setIsLoading(true);

          createNewSystemUserActivityAction({
            data: {
              eventType: "BUTTON_CLICK",
              route: "/an/mansion",
              eventMetadata: {
                buttonName: "goAnalysisDpButton",
                selectedBuilding: selectedBuilding,
              },
            },
          });

          sendLark({
            message: `[🏢][分析][マンション]${currentUser?.name}さんが${selectedBuilding?.nameJa}を検索しました`,
            url: LARK_URLS.USER_ACTIVITY_CHANNEL,
          });

          if (selectedBuilding) {
            router.push(`/an/mansion/${selectedBuilding.id}`);
          }
          setIsLoading(false);
        }} disabled={isLoading || !selectedBuilding}>{
            isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "検索"
          }</Button>
      </div>

      {/* <Separator className="" /> */}

      <div className="px-4 py-2 flex flex-col gap-1 w-full mt-4">
        <div className="text-sm text-gray-800">
          過去履歴
        </div>
        <div className="flex flex-col gap-4">
          {myHistory?.sort((a, b) => dayjsWithTz(b.viewDate).diff(dayjsWithTz(a.viewDate))).map((history) => (
            <Link href={`/an/mansion/${history.buildingId}`} key={history.id} className="text-sm text-gray-500 underline">
              {dayjsWithTz(history.viewDate).format("YYYY/MM/DD")}  - {history.building?.nameJa}
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}