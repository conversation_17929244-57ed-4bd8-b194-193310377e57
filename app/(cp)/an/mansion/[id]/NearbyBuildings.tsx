import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { ColumnDef } from "@tanstack/react-table";
import { Column } from "@tanstack/react-table";
import Link from "next/link";
import { getAllTimeAverageRentPrice, getAllTimeAverageSellingPrice } from "../utiliy/getAllTimeData";
import { nearbyBuildingColumns } from "./nearbyBuildingColumns";
import { calcCoordinateDistance } from "@/lib/helper/geoDistance";

export default function NearbyBuildings({
  records,
  proBuilding,
}: {
  records: ProBuildingProps[];
  proBuilding: ProBuildingProps;
}) {

  const padDistance = (records: ProBuildingProps[]) => {
    if (!proBuilding?.latitude || !proBuilding?.longitude) {
      return records;
    }

    return records.map((record) => ({
      ...record,
      distance: parseFloat((calcCoordinateDistance(proBuilding.latitude as number, proBuilding.longitude as number, record.latitude as number, record.longitude as number) * 1000).toFixed(0)),
    })).sort((a, b) => a.distance - b.distance);
  }

  return <div className="w-full flex flex-col gap-2">
    <div className="text-sm text-gray-500">
      近隣建物: 合計{records.length}件
    </div>

    <div className="w-full h-full">
      <DataTable columns={nearbyBuildingColumns} data={padDistance(records)} defaultPageSize={5} />
    </div>
  </div>
}