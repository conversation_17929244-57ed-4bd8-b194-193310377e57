
import { useState } from "react";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { DataTable } from "@/components/ui/data-table";
import dayjs from "dayjs";
import { mansionRentColumns } from "./mansionRentColumns";
import RentChart from "../../(common)/RentChart";
import { Badge } from "@/components/ui/badge";
import { getAllTimeAverageRentPrice } from "../utiliy/getAllTimeData";
import { Separator } from "@/components/ui/separator";
import { useAuthStore } from "@/store/auth";
import NoPermissionBox from "@/app/(cp)/my/usage/NoPermissionBox";

export function RentSection({ mansionRents }: { mansionRents: ProMansionRentProps[] }) {
  const [selectedRoomType, setSelectedRoomType] = useState<string>("all");
  const [selectedMaxLevel, setSelectedMaxLevel] = useState<string>("all");
  const [selectedMaxArea, setSelectedMaxArea] = useState<string>("all");

  const { currentUser } = useAuthStore();
  let filteredRecords = (record: ProMansionRentProps) => {
    if (selectedRoomType !== "all") {
      if (!record.unitLayout?.includes(selectedRoomType)) {
        return false;
      }
    }

    if (selectedMaxLevel !== "all") {
      if (record.unitLevel) {
        if (selectedMaxLevel === "<=10") {
          if (record.unitLevel > 10) {
            return false;
          }
        } else if (selectedMaxLevel === "<=20") {
          if (record.unitLevel > 20) {
            return false;
          }
        } else if (selectedMaxLevel === ">30") {
          if (record.unitLevel <= 30) {
            return false;
          }
        }
      }
    }

    if (selectedMaxArea !== "all") {
      if (record.unitSize) {
        if (selectedMaxArea === "<=50") {
          if (record.unitSize > 50) {
            return false;
          }
        } else if (selectedMaxArea === "<=100") {
          if (record.unitSize > 100) {
            return false;
          }
        } else if (selectedMaxArea === ">100") {
          if (record.unitSize <= 100) {
            return false;
          }
        }
      }
    }
    return true;
  }

  return currentUser?.accessLevel && currentUser?.accessLevel < 10 ? <NoPermissionBox pageType="freeRentCommon" /> : (
    <>
      <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
        本部件賃貸履歴
      </div>

      {mansionRents && mansionRents.length > 0 ? <div className="p-2 grid grid-cols-1 sm:grid-cols-3 gap-2">
        <div className="flex flex-col gap-4 col-span-1">
          <div className="flex flex-col gap-2 bg-gray-50 p-2">
            <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
              <h1 className="text-base">月別賃料推移</h1>
            </div>

            <RentChart records={mansionRents?.filter(filteredRecords).sort((a: ProMansionRentProps, b: ProMansionRentProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt))) as any[]} unit="month" />
          </div>

          <div className="flex flex-col gap-2 bg-gray-50 p-2">
            <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
              <h1 className="text-base">四半期別賃料推移</h1>
            </div>

            <RentChart records={mansionRents?.filter(filteredRecords).sort((a: ProMansionRentProps, b: ProMansionRentProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt))) as any[]} unit="quarter" />
          </div>
        </div>

        <div className="flex flex-col col-span-2 gap-2">
          <div className="w-full overflow-x-auto scrollbar-hide">
            <div className="flex flex-wrap sm:flex-row gap-2">
              <div className="min-w-max border-r border-gray-200 py-2 pr-2 flex flex-row gap-2">
                <Badge variant={selectedRoomType === "all" ? "default" : "outline"} onClick={() => setSelectedRoomType("all")}>
                  全て
                </Badge>
                <Badge variant={selectedRoomType === "1" ? "default" : "outline"} onClick={() => setSelectedRoomType("1")}>
                  1K/1DK/1LDK
                </Badge>
                <Badge variant={selectedRoomType === "2" ? "default" : "outline"} onClick={() => setSelectedRoomType("2")}>
                  2LDK
                </Badge>
                <Badge variant={selectedRoomType === "3" ? "default" : "outline"} onClick={() => setSelectedRoomType("3")}>
                  3LDK
                </Badge>
              </div>

              <div className="border-r border-gray-200 py-2 pr-2 flex flex-row gap-2">
                <Badge variant={selectedMaxLevel === "all" ? "default" : "outline"} onClick={() => setSelectedMaxLevel("all")}>
                  全て
                </Badge>
                <Badge variant={selectedMaxLevel === "<=10" ? "default" : "outline"} onClick={() => setSelectedMaxLevel("<=10")}>
                  10階以下
                </Badge>
                <Badge variant={selectedMaxLevel === "<=20" ? "default" : "outline"} onClick={() => setSelectedMaxLevel("<=20")}>
                  20階以下
                </Badge>
                <Badge variant={selectedMaxLevel === ">30" ? "default" : "outline"} onClick={() => setSelectedMaxLevel(">30")}>
                  30階越
                </Badge>
              </div>

              <div className="py-2 pr-2 flex flex-row gap-2">
                <Badge variant={selectedMaxArea === "all" ? "default" : "outline"} onClick={() => setSelectedMaxArea("all")}>
                  全て
                </Badge>
                <Badge variant={selectedMaxArea === "<=50" ? "default" : "outline"} onClick={() => setSelectedMaxArea("<=50")}>
                  ~50平米
                </Badge>
                <Badge variant={selectedMaxArea === "<=100" ? "default" : "outline"} onClick={() => setSelectedMaxArea("<=100")}>
                  ~100平米
                </Badge>
                <Badge variant={selectedMaxArea === ">100" ? "default" : "outline"} onClick={() => setSelectedMaxArea(">100")}>
                  100平米~
                </Badge>
              </div>
            </div>
          </div>

          <div className="text-sm text-gray-500 mb-2">
            合計:{mansionRents?.filter(filteredRecords).length}件データ | 平均面積: {getAllTimeAverageRentPrice(mansionRents?.filter(filteredRecords)).averageUnitSize}平米 | 平均坪賃料: {getAllTimeAverageRentPrice(mansionRents?.filter(filteredRecords)).averageUnitPrice}万円/坪

            {/* 平均賃料は{proBuilding.mansionRents?.length > 0 ? (proBuilding.mansionRents.reduce((acc: number, curr: ProMansionRentProps) => acc + curr.unitPrice, 0) / proBuilding.mansionRents.length).toFixed(2) + "万円/坪" : "-"} */}
          </div>

          {mansionRents?.length > 0 && <DataTable columns={mansionRentColumns.filter(column => !["物件タイプ	", "建物情報"].includes(column.header as string))} data={mansionRents.filter(filteredRecords).sort((a: ProMansionRentProps, b: ProMansionRentProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} defaultPageSize={10} />}
        </div>
      </div> : <div className="text-center text-2xl text-gray-400 h-[300px] flex items-center justify-center bg-neutral-100">
        データなし
      </div>}
    </>
  )
}