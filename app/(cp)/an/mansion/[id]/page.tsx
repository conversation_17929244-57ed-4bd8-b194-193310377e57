"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";
import { getProBuildingById } from "@/actions/proBuilding";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { useParams } from "next/navigation";
import LeafletMap from "@/components/LeafletMap";
import { DataTable } from "@/components/ui/data-table";
import { toast } from "@/hooks/use-toast";
import { Share, EyeOff, ScanEye } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { getAllTimeAverageRentPrice, getAllTimeAverageSellingPrice } from "../utiliy/getAllTimeData";
import { getNearbyBuildingSummary } from "@/actions/proBuilding";
import NearbyBuildings from "./NearbyBuildings";
import { nearbyBuildingColumns } from "./nearbyBuildingColumns";
import { RentSection } from "./RentSection";
import SaleSection from "./SaleSection";
import { calcCoordinateDistance } from "@/lib/helper/geoDistance";


export default function MansionPage() {
  const [selectedTab, setSelectedTab] = useState("details");
  const [proBuilding, setProBuilding] = useState<ProBuildingProps>({} as ProBuildingProps);
  const params = useParams();
  const [nearbyBuildingSummary, setNearbyBuildingSummary] = useState<any[]>([]);
  const [isWatched, setIsWatched] = useState(false);

  const fetchProBuilding = async () => {
    const response = await getProBuildingById({ id: params.id as string, isSystem: false });

    if (response && response.success && response.data) {
      setProBuilding(response.data as ProBuildingProps);
    }

    const nearbyBuildingSummaryResponse = await getNearbyBuildingSummary(params.id as string);
    if (nearbyBuildingSummaryResponse && nearbyBuildingSummaryResponse.success && nearbyBuildingSummaryResponse.data) {
      setNearbyBuildingSummary(nearbyBuildingSummaryResponse.data);
    }
  };

  const padDistance = (records: ProBuildingProps[]) => {
    if (!proBuilding?.latitude || !proBuilding?.longitude) {
      return records;
    }

    return records.map((record) => ({
      ...record,
      distance: parseFloat((calcCoordinateDistance(proBuilding.latitude as number, proBuilding.longitude as number, record.latitude as number, record.longitude as number) * 1000).toFixed(0)),
    })).sort((a, b) => a.distance - b.distance);
  }

  useEffect(() => {
    fetchProBuilding();
  }, []);

  const prepText = (data: ProBuildingProps) => {
    let descriptionMansion = `🏠マンション名: ${data.nameJa} \n📍住所: ${data.address}`;
    let descriptionInsights = `💰平均価格: ${getAllTimeAverageSellingPrice(data.tllUserLambdaRecords).averagePrice}万円 \n💸平均賃料: ${getAllTimeAverageRentPrice(data.mansionRents).averagePrice}万円`;
    let descriptionInsights2 = `💸平均利回り: ${proBuilding.tllUserLambdaRecords && proBuilding.mansionRents ? (getAllTimeAverageRentPrice(proBuilding.mansionRents).averagePrice * 12 / (getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averagePrice) * 100).toFixed(2) : 0}%`;

    return `${descriptionMansion} \n${descriptionInsights} \n${descriptionInsights2}`;
  }

  const nameMapper = {
    "nameJa": "マンション名",
    "address": "住所",
    "postalCode": "郵便番号",
    "nearestStation": "最寄駅",
    // "nearestStationGroupId": "最寄駅グループID",
    "level": "階数",
    "builtYear": "築年数",
    // "link": "リンク",
  }

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="マンション詳細">{proBuilding.nameJa}</h1>

        <div className="flex flex-row gap-2 justify-end items-center">
          <Tooltip delayDuration={200}>
            <TooltipTrigger className="flex flex-row gap-2 items-center" asChild>
              <Button variant="outline" onClick={() => {
                navigator.clipboard.writeText(
                  prepText(proBuilding as ProBuildingProps) + "\n" + "🔗 https://urbalytics.jp/an/mansion/" + proBuilding?.id);
                toast({
                  title: "PreviewURLをクリップボードにコピーしました",
                });
              }}>
                <Share className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              プレビューページのリンクを作成中...
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      <Separator className="" />

      <Tabs value={selectedTab} onValueChange={(value) => {
        setSelectedTab(value);
        const element = document.getElementById(value);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }} className="px-4 py-2 sticky top-0 bg-white z-10 w-full border-b border-gray-200">
        <div className="w-full overflow-x-auto scrollbar-hide px-2">
          <TabsList className="bg-white flex space-x-4 p-4 pl-0 min-w-max justify-start">
            <TabsTrigger value="details">マンション詳細</TabsTrigger>
            <TabsTrigger value="history">
              売買履歴
              {proBuilding.tllUserLambdaRecords?.length > 0 && <span className="">: {proBuilding.tllUserLambdaRecords?.length}件</span>}
            </TabsTrigger>

            <TabsTrigger value="rentHistory">賃貸履歴
              {proBuilding.mansionRents?.length > 0 && <span className="">: {proBuilding.mansionRents?.length}件</span>}
            </TabsTrigger>

            <TabsTrigger value="nearby">
              近隣物件
              {nearbyBuildingSummary.length > 0 && <span className="">: {nearbyBuildingSummary.length}件</span>}
            </TabsTrigger>
          </TabsList>
        </div>
      </Tabs>

      <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100">
        <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
          <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
            マンション詳細
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2">
            <div className="flex flex-col gap-2">
              <div className="grid grid-cols-3 border-b border-gray-200">
                <div className="flex flex-col gap-1 border-r border-gray-200 p-2">
                  <div className="text-base">平均価格</div>
                  <div className="text-xl font-bold">
                    {proBuilding.tllUserLambdaRecords ? getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averagePrice.toLocaleString() : 0}万円
                  </div>
                  <div className="text-sm flex flex-row gap-1 text-neutral-500">
                    <div>平均坪単価</div>
                    <div className="">
                      {proBuilding.tllUserLambdaRecords ? getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averageUnitPrice.toLocaleString() : 0}万円
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-1 border-r border-gray-200 p-2">
                  <div className="text-base">平均月賃料</div>
                  <div className="text-xl font-bold">
                    {proBuilding.mansionRents ? getAllTimeAverageRentPrice(proBuilding.mansionRents).averagePrice : 0}万円
                  </div>
                </div>

                <div className="flex flex-col gap-1 border-gray-200 p-2">
                  <div className="text-base">平均利回</div>
                  <div className="text-xl font-bold">
                    {proBuilding.tllUserLambdaRecords && proBuilding.mansionRents ? (Number(getAllTimeAverageRentPrice(proBuilding.mansionRents).averagePrice) * 12 / Number(getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averagePriceRaw) * 100).toFixed(2) : 0}%
                  </div>
                </div>
              </div>

              <div className="px-2 flex flex-col gap-2">
                {Object.keys(nameMapper).map((key) => {
                  return <div key={key}>
                    <b>{nameMapper[key as keyof typeof nameMapper]}</b>
                    : {proBuilding[key as keyof ProBuildingProps] as string}
                  </div>
                })}
              </div>

              <div className="grid grid-cols-2 gap-2 p-2">
                <div className="flex flex-col gap-2 p-1 border border-gray-200 rounded-md bg-neutral-50">
                  <div className="border-b border-gray-200 pb-2 flex flex-row gap-2 items-center">
                    <div className="text-xl font-bold flex-1">販売</div>
                    <div>
                      {proBuilding.tllUserLambdaRecords?.length}件
                    </div>
                  </div>

                  {proBuilding.tllUserLambdaRecords && proBuilding.tllUserLambdaRecords.length > 0 ? <div className="flex flex-col gap-2">
                    <div>
                      <b className="mr-1">平均単価:</b>
                      {proBuilding.tllUserLambdaRecords ? getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averageUnitPrice : 0}万円/坪
                    </div>
                    <div>
                      <b className="mr-1">平均面積:</b>
                      {proBuilding.tllUserLambdaRecords ? getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averageUnitSize : 0}平米
                    </div>
                    <div>
                      <b className="mr-1">平均価格:</b>
                      {proBuilding.tllUserLambdaRecords ? getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords).averagePrice : 0}万円
                    </div>
                  </div> : <div className="text-center text-gray-400 h-full flex items-center justify-center bg-neutral-100">
                    データなし
                  </div>}
                </div>

                <div className="flex flex-col gap-2 p-1 border border-gray-200 rounded-md bg-neutral-50">
                  <div className="border-b border-gray-200 pb-2 flex flex-row gap-2 items-center">
                    <div className="text-xl font-bold flex-1">賃貸</div>
                    <div>
                      {proBuilding.mansionRents?.length}件
                    </div>
                  </div>

                  {proBuilding.mansionRents && proBuilding.mansionRents.length > 0 ? <div className="flex flex-col gap-2">
                    <div>
                      <b className="mr-1">平均単価:</b>
                      {proBuilding.mansionRents ? getAllTimeAverageRentPrice(proBuilding.mansionRents).averageUnitPrice : 0}万円/坪
                    </div>
                    <div>
                      <b className="mr-1">平均面積:</b>
                      {proBuilding.mansionRents ? getAllTimeAverageRentPrice(proBuilding.mansionRents).averageUnitSize : 0}平米
                    </div>
                    <div>
                      <b className="mr-1">平均価格:</b>
                      {proBuilding.mansionRents ? getAllTimeAverageRentPrice(proBuilding.mansionRents).averagePrice : 0}万円
                    </div>
                  </div> : <div className="text-center text-gray-400 h-full flex items-center justify-center bg-neutral-100">
                    データなし
                  </div>}
                </div>
              </div>

              <div className="p-2 border-t border-gray-200 w-full">
                <NearbyBuildings records={nearbyBuildingSummary} proBuilding={proBuilding} />
              </div>
            </div>

            <div className="flex flex-col gap-2 border border-gray-200">
              {((proBuilding?.latitude && proBuilding?.longitude) || (nearbyBuildingSummary.filter((building) => building.latitude && building.longitude).length > 0)) && <LeafletMap
                height="800px"
                center={proBuilding?.latitude && proBuilding?.longitude ? [proBuilding.latitude as number, proBuilding.longitude as number] : [nearbyBuildingSummary.filter((building) => building.latitude && building.longitude)[0].latitude as number, nearbyBuildingSummary.filter((building) => building.latitude && building.longitude)[0].longitude as number]}
                data={nearbyBuildingSummary.filter((building) => building.latitude && building.longitude).map((building) => ({
                  name: building.nameJa,
                  colorDot: "green",
                  coordinate: [building.latitude as number, building.longitude as number],
                  link: `/an/mansion/${building.id}`,
                  popUpRenderFunction: () => {
                    const sellData = getAllTimeAverageSellingPrice(building.tllUserLambdaRecords);
                    const rentData = getAllTimeAverageRentPrice(building.mansionRents);

                    return <div>
                      <div>{building.nameJa}</div>
                      <div>{building.address}</div>

                      <div>売買: {sellData.averageUnitPrice}万円/坪  | {building.tllUserLambdaRecords?.length}件</div>

                      <div>賃貸: {rentData.averageUnitPrice}万円/坪  | {building.mansionRents?.length}件</div>

                      <div>
                        ROI: {rentData.averageUnitPrice ? sellData.averageUnitPrice ? (sellData.averageUnitPrice ? rentData.averageUnitPrice * 100 * 12 / sellData.averageUnitPrice : 0).toFixed(2) + "%" : "-" : "-"}
                      </div>
                    </div>
                  }
                }))}
                zoom={16}
                legend={
                  <div className="flex flex-row gap-2 bottom-0 absolute z-999 text-center justify-center bg-white p-1">

                    <div className="w-4 h-4 bg-blue-500"></div>
                    <div>本物件</div>

                    <div className="w-4 h-4 bg-green-500"></div>
                    <div>近隣物件({nearbyBuildingSummary.filter((building) => building.latitude && building.longitude).length}件)</div>
                  </div>
                } />
              }

              <div className="flex flex-row gap-2 text-xs text-gray-500 text-center justify-center py-2">
                <div>
                  longitude: {proBuilding.longitude}
                </div>
                <div>
                  latitude: {proBuilding.latitude}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white flex flex-col gap-2 border border-gray-200" id="saleHistory">
          <SaleSection proBuilding={proBuilding} />
        </div>

        <div className="bg-white flex flex-col gap-2 border border-gray-200" id="rentHistory">
          <RentSection mansionRents={proBuilding.mansionRents} />
        </div>

        <div className="bg-white flex flex-col gap-2 border border-gray-200" id="nearby">
          <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
            近隣建物
          </div>

          <div className="p-2">
            <DataTable columns={nearbyBuildingColumns} data={padDistance(nearbyBuildingSummary)} defaultPageSize={20} />
          </div>
        </div>
      </div>
    </div>
  )
}
