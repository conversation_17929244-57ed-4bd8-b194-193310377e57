import { useState } from "react";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import dayjs from "dayjs";
import {
  CartesianGrid,
  ComposedChart,
  Line,
  Tooltip,
  XAxis,
  YAxis,
  Bar,
} from "recharts";
import {
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { ChartContainer } from "@/components/ui/chart";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import { TrendingUp, TrendingDown } from "lucide-react";
dayjs.extend(quarterOfYear);

export default function SaleChart({ records, unit }: { records: UserLambdaRecordProps[], unit: any }) {
  if (!records) {
    return <div>No records</div>;
  }

  let allChangesForEachMonth = {} as {
    [key: string]: {
      price: number;
      dividerValue: number;
      tsuboPrice: number;
    }[];
  };

  let unitMapper = {
    "month": "ヶ月",
    "quarter": "四半期",
    "year": "年",
  }

  records.forEach((record) => {
    record.priceChanges?.forEach((change) => {
      let dividerValue = null;

      if (record.recordType === UserLambdaRecordType.MANSION) {
        dividerValue = record.buildingSize || record.recordValues?.unitArea;
      } else if (record.recordType === UserLambdaRecordType.HOUSE) {
        dividerValue = record.buildingSize;
      } else if (record.recordType === UserLambdaRecordType.BUILDING) {
        dividerValue = record.buildingSize;
      } else if (record.recordType === UserLambdaRecordType.LAND) {
        dividerValue = record.landSize;
      }

      if (!dividerValue || dividerValue === 1) {
        // Some wrong info for building will mark as 1
        return;
      }

      let startOfPeriod = dayjs(change.recordDate).startOf(unit).format("YYYY/MM/DD");
      if (!allChangesForEachMonth[startOfPeriod]) {
        allChangesForEachMonth[startOfPeriod] = [];
      }

      if (change.price && dividerValue) {
        allChangesForEachMonth[startOfPeriod].push({
          price: change.price,
          dividerValue: dividerValue,
          tsuboPrice: change.price * 3.3 / dividerValue,
        });
      }
    });
  });

  let parsedChartData = Object.keys(allChangesForEachMonth).map((startOfPeriod) => {
    return {
      startOfPeriod: startOfPeriod,
      count: allChangesForEachMonth[startOfPeriod].length,
      averageTsuboPrice: parseFloat(
        (
          allChangesForEachMonth[startOfPeriod].reduce((acc, curr) => acc + curr.tsuboPrice, 0) /
          allChangesForEachMonth[startOfPeriod].length
        ).toFixed(0)
      ),
    };
  }).reverse();

  let overallIncrease = parsedChartData.length > 0 ? parsedChartData[parsedChartData.length - 1]?.averageTsuboPrice - parsedChartData[0]?.averageTsuboPrice : 0;
  let overallIncreasePercentage = (overallIncrease / (parsedChartData.length > 0 ? parsedChartData[0]?.averageTsuboPrice : 1)) * 100;
  let timeDiff = dayjs(parsedChartData.length > 0 ? parsedChartData[parsedChartData.length - 1]?.startOfPeriod : "").diff(dayjs(parsedChartData.length > 0 ? parsedChartData[0]?.startOfPeriod : ""), unit);
  let averageIncreasePercentage = timeDiff > 0 ? overallIncreasePercentage / timeDiff : 0;

  return (
    <div className="w-full h-full flex flex-col">
      <ChartContainer config={{
      }}>
        <ComposedChart data={parsedChartData.sort((a, b) => dayjs(a.startOfPeriod).diff(dayjs(b.startOfPeriod)))} margin={{ top: 24, right: 0, bottom: 0, left: 0 }}>
          <CartesianGrid vertical={false} />

          <XAxis
            dataKey="startOfPeriod"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => dayjs(value).format("YYYY/MM")}
          />

          <YAxis
            yAxisId="left"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => value.toFixed(0)}
            domain={[
              (dataMin: number) => dataMin * 0.8,
              (dataMax: number) => dataMax * 1.2,
            ]}
            label={{
              value: "坪単価",
              position: "top",
              offset: 10,
            }}
          />

          {/* Second Y-Axis for `count` (right side) */}
          <YAxis
            yAxisId="right"
            orientation="right"
            tickLine={false}
            axisLine={false}
            tickMargin={12}
            tickFormatter={(value) => value.toFixed(0)}
            domain={[
              (dataMin: number) => dataMin * 0.8,
              (dataMax: number) => dataMax * 1.2,
            ]}
            label={{
              value: "件数",
              position: "top",
              offset: 10,
            }}
          />

          <Tooltip />

          <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />

          {/* Line for `averageTsuboPrice` (left Y-axis) */}
          <Line
            yAxisId="left"
            dataKey="averageTsuboPrice"
            type="natural"
            stroke="hsl(var(--chart-1))"
            strokeWidth={2}
            dot={false}
            connectNulls={true}
          />

          {/* Bar for `count` (right Y-axis) */}
          <Bar
            yAxisId="right"
            dataKey="count"
            fill="hsl(var(--chart-2))"
            barSize={30}
            opacity={0.8}
          />
        </ComposedChart>
      </ChartContainer>

      <div className="flex flex-col text-sm gap-1 justify-between items-center">
        <div className="text-center flex flex-col md:flex-row items-center justify-between gap-1">
          <div>
            過去{timeDiff}{unitMapper[unit as keyof typeof unitMapper]}単価:
          </div>
          <span className={`flex flex-row items-center ${overallIncreasePercentage > 0 ? "text-green-500" : "text-red-500"}`}>
            {overallIncreasePercentage.toFixed(2)}%
            {/* {overallIncreasePercentage > 0 ? "上昇" : "下落"} */}
            {overallIncreasePercentage > 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
          </span>
        </div>
        <div className="flex flex-row items-center gap-1">
          平均変動率:
          <span className={`flex flex-row items-center ${averageIncreasePercentage > 0 ? "text-green-500" : "text-red-500"}`}>
            {averageIncreasePercentage.toFixed(2)}%
            {averageIncreasePercentage > 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
          </span>
        </div>
        <div className="text-xs text-neutral-500">
          * 各変更記録は各物件ごとに記録しています
        </div>
      </div>
    </div>
  );
}