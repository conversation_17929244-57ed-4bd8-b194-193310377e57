import { getAllTimeAverageSellingPrice } from "../utiliy/getAllTimeData";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { Button } from "@/components/ui/button";
import { ColumnDef } from "@tanstack/react-table";
import { getAllTimeAverageRentPrice } from "../utiliy/getAllTimeData";
import Link from "next/link";

export const nearbyBuildingColumns = [
  {
    header: "距離(m2)",
    accessorKey: "distance",
  },
  {
    header: "マンション名",
    accessorKey: "nameJa",
  },
  {
    header: "住所",
    accessorKey: "address",
  },
  {
    header: "築年数",
    accessorKey: "buildingBuiltYear",
  },
  {
    header: "最寄駅",
    accessorKey: "nearestStation",
  },
  {
    header: "売買坪単価",
    cell: ({ row: { original } }: { row: { original: ProBuildingProps } }) => {
      const sellData = getAllTimeAverageSellingPrice(original.tllUserLambdaRecords);

      return original.tllUserLambdaRecords?.length ? <div className="flex flex-col">
        <div>
          {/* 面積: {sellData.averageUnitSize}
          価格: {sellData.averagePrice} */}
          {sellData.averageUnitPrice}
        </div>
        <div className="text-xs text-gray-500">
          {original.tllUserLambdaRecords?.length}件
        </div>
      </div> : <div>
        -
      </div>
    },
  },
  {
    header: "賃貸坪単価",
    cell: ({ row: { original } }: { row: { original: ProBuildingProps } }) => {
      const renData = getAllTimeAverageRentPrice(original.mansionRents);

      return original.mansionRents?.length ? <div className="flex flex-col">
        <div>
          {renData.averageUnitPrice}
        </div>
        <div className="text-xs text-gray-500">
          {original.mansionRents?.length}件
        </div>
      </div> : <div>
        -
      </div>
    },
  },
  {
    header: "ROI",
    cell: ({ row: { original } }: { row: { original: ProBuildingProps } }) => {
      const sellData = getAllTimeAverageSellingPrice(original.tllUserLambdaRecords);
      const rentData = getAllTimeAverageRentPrice(original.mansionRents);

      return rentData.averageUnitPrice > 0 && sellData.averageUnitPrice > 0 ? (sellData.averageUnitPrice ? rentData.averageUnitPrice * 100 * 12 / sellData.averageUnitPrice : 0).toFixed(2) + "%" : "-"
    },
  },
  {
    header: "操作",
    cell: ({ row: { original } }: { row: { original: ProBuildingProps } }) => {
      return <Link href={`/an/mansion/${original.id}`}>
        <Button variant="outline">
          詳細画面へ
        </Button>
      </Link>
    },
  }
] as ColumnDef<ProBuildingProps>[];   