"use client"

import { DataTable } from "@/components/ui/data-table"
import { ProBuildingProps } from "@/lib/definitions/proBuilding"
import dayjs from "dayjs"
import { useEffect, useState } from "react"
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord"
import { TllUserProps } from "@/lib/definitions/tllUser"
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns"
import { useAuthStore } from "@/store/auth"
import { TllUser, TllUserLambdaRecord } from "@prisma/client"
import { getAllTimeAverageSellingPrice } from "../utiliy/getAllTimeData"
import { Badge } from "@/components/ui/badge"
import SaleChart from "./SaleChart"
import { getStatus } from "@/lib/userLambdaRecord/getStatus"
import { set } from "zod"

export default function SaleSection({ proBuilding }: { proBuilding: ProBuildingProps }) {
  const [columns, setColumns] = useState<any[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const { currentUser } = useAuthStore();

  useEffect(() => {
    if (currentUser) {
      let columns = getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser: currentUser as TllUserProps });
      setColumns(columns.filter(column => !["ROI", "住所 | 最寄駅", "土地(㎡)", "タイプ"].includes(column.header as string)));
    }
  }, [currentUser]);


  const [selectedMaxArea, setSelectedMaxArea] = useState<string>("all");

  let filteredRecords = (record: UserLambdaRecordProps) => {
    if (selectedStatus !== "all") {
      if (getStatus(record as UserLambdaRecordProps) !== selectedStatus) {
        return false;
      }
    }

    if (selectedMaxArea !== "all") {
      let buildingSize = record.buildingSize || record.recordValues?.unitArea; // legacy data
      if (buildingSize) {
        if (selectedMaxArea === "<=50") {
          if (buildingSize > 50) {
            return false;
          }
        } else if (selectedMaxArea === "<=100") {
          if (buildingSize > 100) {
            return false;
          }
        } else if (selectedMaxArea === ">100") {
          if (buildingSize <= 100) {
            return false;
          }
        }
      }
    }
    return true;
  }

  return (
    <>
      <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
        売買履歴
      </div>

      {proBuilding.tllUserLambdaRecords && proBuilding.tllUserLambdaRecords.length > 0 ? <div className="p-2 gap-2 grid grid-cols-1 sm:grid-cols-3">
        <div className="flex flex-col gap-4 col-span-1">
          <div className="flex flex-col gap-2 bg-gray-50 p-2">
            <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
              <h1 className="text-base">月別坪単価価格推移</h1>
            </div>

            <SaleChart records={proBuilding.tllUserLambdaRecords?.filter(filteredRecords).sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} unit="month" />
          </div>

          <div className="flex flex-col gap-2 bg-gray-50 p-2">
            <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
              <h1 className="text-base">四半期別坪単価価格推移</h1>
            </div>

            <SaleChart records={proBuilding.tllUserLambdaRecords?.filter(filteredRecords).sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} unit="quarter" />
          </div>
        </div>

        <div className="flex flex-col col-span-2 gap-2">
          <div className="flex flex-row text-sm gap-2 justify-start items-center w-full">
            <div className="border-r border-gray-200 py-2 pr-2 flex flex-row gap-2">
              状況:
              <Badge variant={selectedStatus === "all" ? "default" : "outline"} onClick={() => setSelectedStatus("all")}>
                全て
              </Badge>
              <Badge variant={selectedStatus === "公開中" ? "default" : "outline"} onClick={() => setSelectedStatus("公開中")}>
                公開中
              </Badge>
              <Badge variant={selectedStatus === "成約" ? "default" : "outline"} onClick={() => setSelectedStatus("成約")}>
                成約
              </Badge>
            </div>

            <div className="border-r border-gray-200 py-2 pr-2 flex flex-row gap-2">
              面積:
              <Badge variant={selectedMaxArea === "all" ? "default" : "outline"} onClick={() => setSelectedMaxArea("all")}>
                全て
              </Badge>
              <Badge variant={selectedMaxArea === "<=50" ? "default" : "outline"} onClick={() => setSelectedMaxArea("<=50")}>
                50平米以下
              </Badge>
              <Badge variant={selectedMaxArea === "<=100" ? "default" : "outline"} onClick={() => setSelectedMaxArea("<=100")}>
                100平米以下
              </Badge>
              <Badge variant={selectedMaxArea === ">100" ? "default" : "outline"} onClick={() => setSelectedMaxArea(">100")}>
                100平米越
              </Badge>
            </div>
          </div>

          <div className="text-sm text-gray-500 mb-2">
            合計:{proBuilding.tllUserLambdaRecords?.filter(filteredRecords).length}件データ | 平均面積: {getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords?.filter(filteredRecords)).averageUnitSize}平米 | 平均坪単価: {getAllTimeAverageSellingPrice(proBuilding.tllUserLambdaRecords?.filter(filteredRecords)).averageUnitPrice}万円/坪

            {/* 平均賃料は{proBuilding.mansionRents?.length > 0 ? (proBuilding.mansionRents.reduce((acc: number, curr: ProMansionRentProps) => acc + curr.unitPrice, 0) / proBuilding.mansionRents.length).toFixed(2) + "万円/坪" : "-"} */}
          </div>


          {proBuilding.tllUserLambdaRecords?.length > 0 && <DataTable columns={columns} data={proBuilding.tllUserLambdaRecords?.filter(filteredRecords).sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} defaultPageSize={10} />}
        </div>
      </div> : <div className="text-center text-2xl text-gray-400 h-[300px] flex items-center justify-center bg-neutral-100">
        データなし
      </div>}
    </>
  )
}