import { Badge } from "@/components/ui/badge";
import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import Link from "next/link";

const rentColumns: ColumnDef<any>[] = [
  {
    header: "マンション名",
    accessorKey: "buildingName", // 使用 proMansionRent 中的 compositeTitle
  },
  {
    header: "作成日",
    cell: ({ row }) => {
      return <div className="flex flex-col">
        <div>
          {dayjs().diff(dayjs(row.original.createdAt), "day") < 7 ? <Badge variant="outline">新規</Badge> : ""}
        </div>
        {dayjs(row.original.createdAt).format("YYYY/MM/DD")}
      </div>
    }
  },
  {
    header: "物件タイプ",
    accessorKey: "propertyType",
  },
  {
    header: "建物情報",
    cell: ({ row }) => {
      return <div className="flex flex-col">
        {row.original.buildingId || row.original.buildingName ? <>
          {row.original.buildingId ?
            <Link href={`/an/mansion/${row.original.buildingId}`} className="underline" target="_blank">
              {row.original.buildingName}

              {row.original.building?.mansionRents.length > 0 && <Badge variant="outline">
                {row.original.building?.mansionRents.length}件
              </Badge>}
            </Link>
            :
            <span className="ml-2">
              {row.original.buildingName}
            </span>
          }
        </>
          : <> </>
        }
        <div className="text-xs text-gray-500">
          {row.original.locationPostalCode && <span className="text-xs text-gray-500">〒{row.original.locationPostalCode} </span>}

          {row.original.address && <span className="text-xs text-gray-500"> {row.original.address}</span>}

          {row.original.buildingAddress && <span className="text-xs text-gray-500"> {row.original.buildingAddress}</span>}
        </div>
        <div className="text-xs text-gray-500"> {row.original.transport && row.original.transport}{row.original.nearestStationWalkMinute && row.original.nearestStationWalkMinute + " | "}
          <span className={`${row.original.buildingBuiltYear && row.original.buildingBuiltYear <= 1981 ? "text-red-500" : ""}`}>
            {row.original.buildingBuiltYear && row.original.buildingBuiltYear + "年築"}
          </span>
        </div>
      </div>
    }
  },
  {
    header: "仲介情報",
    accessorKey: "brokerReinsNumber", // 使用 proMansionRent 中的 
    cell: ({ row }) => {
      return <div className="flex flex-col text-xs text-gray-500">
        <div>{row.original?.brokerReinsNumber}</div>
        <div>{row.original?.brokerType} | {row.original?.brokerStatus}</div>
        {row.original?.brokerListingCompany && <div className="text-xs text-gray-500">{row.original?.brokerListingCompany?.slice(0, 5)} | {row.original?.brokerListingCompanyNumber}</div>}
      </div>
    }
  },
  {
    header: "土地面積",
    cell: ({ row }) => {
      return <div className="flex flex-col text-xs">
        {row.original.landSize > 0 ? row.original.landSize + "㎡" : "-"}
        {row.original.landType && <div className="text-xs text-gray-500">{row.original.landType}</div>}
      </div>
    }
  },
  {
    header: "間取り",
    accessorKey: "unitLayout", // 使用 proMansionRent 中的 unitLayout
  },
  {
    header: "間取り(一棟)",
    accessorKey: "buildingLayout", // 使用 proMansionRent 中的 unitLayout
  },
  {
    header: "階数",
    accessorKey: "unitLevel", // 使用 proMansionRent 中的 unitLevel
  },
  {
    header: "賃料/管理費",
    accessorKey: "feeRent", // 使用 proMansionRent 中的 feeRent
    cell: ({ row }) => {
      return <div className="flex flex-col text-center justify-center items-center">
        <div>
          {row.original.feeRent ? row.original.feeRent.toFixed(2) + "万円" : "-"}
        </div>
        {row.original.feeManagement > 0 && <div className="text-xs text-gray-500">
          管理費: {row.original.feeManagement ? (row.original.feeManagement / 10000).toFixed(2) + "万円" : "-"}
        </div>}
        {row.original.feeUtility > 0 && <div className="text-xs text-gray-500">
          光熱: {(row.original.feeUtility / 10000).toFixed(2)}万円
        </div>}
      </div>
    }
  },
  {
    header: "賃貸面積",
    cell: ({ row }) => {
      return <div className="flex flex-col">
        <div>{row.original.buildingSize ? row.original.buildingSize + "㎡" : "-"}
        </div>
      </div>
    }
  },
  {
    header: "専有面積",
    cell: ({ row }) => {
      return <div>{row.original.unitSize ? row.original.unitSize + "㎡" : "-"}
      </div>;
    }
  },
  {
    header: "坪単価",
    cell: ({ row }) => {

      let averagePrice = 0;

      let unitSize = row.original.unitSize || row.original.buildingSize;

      if (row.original.building?.mansionRents.length > 0) {
        averagePrice = row.original.building?.mansionRents.reduce((acc: number, curr: any) => acc + (curr.feeRent / (curr.unitSize || curr.buildingSize) * 3.305785), 0) / row.original.building?.mansionRents.length;
      }

      let currentPrice = row.original.feeRent / unitSize * 3.305785;


      return <div className="flex flex-col text-center justify-center items-center">
        <div>
          {row.original.feeRent && unitSize > 0 ? currentPrice.toFixed(2) + "万円" : "-"}
        </div>

        {averagePrice > 0 && <div className="text-xs text-gray-500">
          平均: {averagePrice.toFixed(2) + "万円"} {averagePrice > 0 ? currentPrice > averagePrice ? <span className="text-red-500 font-bold">↑</span> : currentPrice < averagePrice ? <span className="text-green-500 font-bold">↓</span> : <span className="text-gray-500"></span> : ""}
        </div>}
      </div>
    }
  },
  {
    header: "礼金/権利金",
    accessorKey: "feeGiftMoney", // 使用 proMansionRent 中的 feeGiftMoney
  },
  {
    header: "敷金/保証金",
    accessorKey: "feeDepositMoney", // 使用 proMansionRent 中的 feeDepositMoney
  },
];


export const mansionRentColumns: ColumnDef<any>[] = rentColumns.filter((column: ColumnDef<any>) => column.header && ["作成日", "物件タイプ", "建物情報", "専有面積", "間取り", "階数", "賃料/管理費", "坪単価", "礼金", "敷金/保証金"].includes(column.header as string))

export const mansionRentForRefillColumns: ColumnDef<any>[] = rentColumns.filter((column: ColumnDef<any>) => column.header && ["マンション名", "作成日", "物件タイプ", "建物情報", "専有面積", "間取り", "階数", "賃料/管理費", "坪単価", "礼金", "敷金/保証金"].includes(column.header as string))

export const buildingRentColumns: ColumnDef<any>[] = rentColumns.filter((column: ColumnDef<any>) => column.header && ["作成日", "物件タイプ", "建物情報", "土地面積", "賃貸面積", "賃料/管理費", "坪単価", "礼金", "敷金/保証金"].includes(column.header as string))

export const buildingRentForRefillColumns: ColumnDef<any>[] = rentColumns.filter((column: ColumnDef<any>) => column.header && ["マンション名", "作成日", "物件タイプ", "建物情報", "土地面積", "賃貸面積", "賃料/管理費", "坪単価", "礼金", "敷金/保証金"].includes(column.header as string))

export const houseRentColumns: ColumnDef<any>[] = rentColumns.filter((column: ColumnDef<any>) => column.header && ["作成日", "物件タイプ", "建物情報", "賃貸面積", "間取り(一棟)", "賃料/管理費", "坪単価", "礼金", "敷金/保証金"].includes(column.header as string))
