import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { PencilIcon, StarIcon } from "lucide-react";
import Link from "next/link";
import dayjs from "dayjs";

export const companyColumns = [
  {
    header: "ID",
    cell: ({ row }: { row: any }) => <Link href={`/an/company/${row.original.id}`} className="underline">{row.original.id}</Link>,
  },
  {
    header: "フルネーム",
    accessorKey: "fullName",
    cell: ({ row }: { row: any }) => <div className="flex flex-col">
      <div>
        {row.original.fullName || "-"}
      </div>
      <div className="text-xs whitespace-pre-line text-xs text-neutral-500">
        {row.original.address?.split("\n").map((line: string) => <div key={line}>{line}</div>)}
      </div>
      <div className="text-xs text-neutral-500">
        {row.original.licenseNumber && (
          <div>
            免許番号: {row.original.licenseNumber || "-"}
          </div>
        )}
      </div>
    </div>,
  },
  {
    header: "代表名",
    accessorKey: "repName",
  },
  {
    header: "連絡",
    accessorKey: "contactNumber",
    cell: ({ row }: { row: any }) => <div className="flex flex-col text-xs">
      {row.original.contactNumber && (
        <div>
          電話: {row.original.contactNumber || "-"}
        </div>
      )}
      {row.original.faxNumber && (
        <div>
          FAX: {row.original.faxNumber || "-"}
        </div>
      )}
      {row.original.email && (
        <div>
          メール: {row.original.email || "-"}
        </div>
      )}
    </div>,
  },
  {
    header: "URL",
    accessorKey: "url",
  },
  {
    header: "コメント",
    accessorKey: "comments",
  },
  {
    header: "お気に入り",
    cell: ({ row }: { row: any }) => <div className="flex items-center gap-2 w-full justify-center">
      {row.original.isFav ? <StarIcon className="fill-current text-yellow-500 w-4 h-4" /> : <StarIcon className="w-4 h-4 text-neutral-500" />}
    </div>
  },
  {
    header: "追加日",
    accessorKey: "updatedAt",
    cell: ({ row }: { row: any }) => <div className=""> {row.original.updatedAt ? dayjs(row.original.updatedAt).format("YYYY/MM/DD") : "-"} </div>
  },
  {
    header: "操作",
    cell: ({ row }: { row: any }) => <div className="flex items-center gap-2 w-full justify-center">
      <Link href={`/an/company/${row.original.id}`} className="underline">
        <Button variant="outline" size="icon">
          <PencilIcon className="w-4 h-4" />
        </Button>
      </Link>
    </div>
  },
];
