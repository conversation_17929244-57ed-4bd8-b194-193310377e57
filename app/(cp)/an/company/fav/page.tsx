"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { getFavCompaniesAction } from "@/actions/proCompany";
import { ProCompanyProps } from "@/lib/definitions/proCompany";
import { DataTable } from "@/components/ui/data-table";
import { useEffect } from "react";
import { useState } from "react";
import Link from "next/link";
import { companyColumns } from "../companyColumns";
import dayjs from "dayjs";
export default function CompanyPlaceholderPage() {
  const [companies, setCompanies] = useState<ProCompanyProps[]>([]);

  useEffect(() => {
    const fetchCompanies = async () => {
      const companies = await getFavCompaniesAction();

      if (companies.success) {
        setCompanies(companies.data);
      }
    };
    fetchCompanies();
  }, []);

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="業者">お気に入り業者一覧</h1>
      </div>

      <Separator className="mb-2" />

      <div className="p-2">
        <DataTable columns={companyColumns} data={companies.sort((a: any, b: any) => dayjs(b.updatedAt).diff(dayjs(a.updatedAt)))} />
      </div>
    </div>
  );
}
