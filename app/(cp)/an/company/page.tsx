"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { getFavCompaniesAction } from "@/actions/proCompany";
import { ProCompanyProps } from "@/lib/definitions/proCompany";
import { DataTable } from "@/components/ui/data-table";
import { useEffect } from "react";
import { useState } from "react";
import Link from "next/link";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { companyColumns } from "./companyColumns";
import { fuzzySearchCompaniesAction } from "@/actions/proCompany";
import { toast } from "@/hooks/use-toast";
import dayjs from "dayjs";
export default function CompanyPlaceholderPage() {
  const [companies, setCompanies] = useState<ProCompanyProps[]>([]);
  const [search, setSearch] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const searchCompany = async () => {
    setIsLoading(true);
    const companies = await fuzzySearchCompaniesAction(search);
    if (companies.success) {
      setCompanies(companies.data);
      toast({
        title: "検索成功",
        description: "業者を検索しました",
      });
    } else {
      toast({
        title: "検索失敗",
        description: companies.message,
      });
    }
    setIsLoading(false);
  };

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="業者">業者一覧</h1>
      </div>

      <Separator className="mb-2" />

      <div className="flex justify-end p-2 gap-2">
        <Input placeholder="検索" value={search} onChange={(e) => setSearch(e.target.value)} />
        <Button onClick={() => searchCompany()}>検索</Button>
      </div>

      <div className="p-2">
        <DataTable columns={companyColumns} data={companies.sort((a: any, b: any) => dayjs(b.updatedAt).diff(dayjs(a.updatedAt)))} isLoading={isLoading} />
      </div>
    </div>
  );
}
