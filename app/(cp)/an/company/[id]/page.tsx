"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { useEffect, useState } from "react";
import { getCompanyDetailsAction } from "@/actions/proCompany";
import { ProCompanyProps } from "@/lib/definitions/proCompany";
import { useParams } from "next/navigation";
import { Separator } from "@/components/ui/separator";
import dayjs from "dayjs";
import { DataTable } from "@/components/ui/data-table";
import { priceChangeColumns } from "@/app/(cp)/ex/search/[id]/(details)/(priceChange)/priceChangeColumns";
import { getColor } from "@/lib/color";
import { Badge } from "@/components/ui/badge";
import { Eye, Pencil, StarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { <PERSON><PERSON><PERSON> } from "./AnalysisPie";
import { AnalysisPiePrefecture } from "./AnalysisPiePrefecture";
import { AnalysisPiePriceRange } from "./AnalysisPiePriceRange";
import { AnalysisPieBrokerType } from "./AnalysisBrokerType";

export default function CompanyPlaceholderPage() {
  const [company, setCompany] = useState<ProCompanyProps | null>(null);
  const [selectedRecordType, setSelectedRecordType] = useState<string>("all");
  const { id } = useParams();

  useEffect(() => {
    const fetchCompany = async () => {
      if (!id) {
        return;
      }
      const company = await getCompanyDetailsAction(id as string);
      if (company.success) {
        setCompany(company.data);
      }
    };
    fetchCompany();
  }, []);

  const nameMapper = [
    [{ companyName: "会社名" }],
    [{ fullName: "フルネーム" }],
    [{ repName: "代表者名" }],
    [{ branchName: "支社名" }],
    [{ address: "住所" }],
    [{ contactNumber: "連絡先" }, { faxNumber: "FAX番号" }],
    [{ email: "メールアドレス" }],
    [{ licenseNumber: "免許番号" }],
    [{ url: "URL" }],
    [{ comments: "コメント" }],
  ];

  const getUniqueLambdaRecords = (): any[] => {
    let uIds = Array.from(new Set(company?.priceChanges?.map((priceChange) => priceChange.recordId) || []));

    return uIds.map((id) => {
      return {
        ...company?.priceChanges?.filter((priceChange) => priceChange.recordId === id)[0].tllUserLambdaRecord,
        priceChanges: company?.priceChanges?.sort((a, b) => dayjs(b.recordDate).diff(dayjs(a.recordDate))).filter((priceChange) => priceChange.recordId === id) || []
      }
    })
  }

  const getCountByType = (type: string) => {
    return getUniqueLambdaRecords().filter((record: any) => type === "all" ? true : record.recordType === type).length;
  }

  return (
    <div>
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="業者">
          <span className="mr-2">業者詳細:</span>
          {company?.fullName}
        </h1>
      </div>

      <Separator className="mb-2" />

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4">
        <div className="flex flex-col gap-4">
          <div className="border border-neutral-200">
            <div className="flex flex-row justify-between items-center bg-neutral-100 gap-2 px-2">
              <h2 className="text-lg font-bold  p-2 flex-1">基本情報</h2>

              <StarIcon
                className={`cursor-pointer ${company?.isFav ? "fill-current text-yellow-500" : ""}`}
              />

              <Button variant="outline" size="sm">
                <Link href={`/an/company/${company?.id}/edit`}>
                  <Pencil />
                </Link>
              </Button>
            </div>

            <Separator className="mb-2" />

            <div className="p-2 flex flex-wrap flex-col gap-2">
              {nameMapper.map((key, index) => (
                <div key={index}>
                  {key && (
                    <div className={`grid grid-cols-${key.length} w-full`}>
                      {key.map((pair) => (
                        <div key={Object.keys(pair)[0]}>
                          <span className="font-bold">{Object.values(pair)[0]}:
                          </span>
                          <span> {company?.[Object.keys(pair)[0] as keyof ProCompanyProps]}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="border border-neutral-200">
            <h2 className="text-lg font-bold bg-neutral-100 p-2">販売物件分析
            </h2>

            <div className="grid grid-cols-2">
              <div className="border border-neutral-200">
                <h3 className="text-md font-bold p-2">物件タイプ</h3>
                <Separator className="mb-2" />
                <div className="p-2">
                  <AnalysisPie uniqueRecords={getUniqueLambdaRecords() || []} recordKey={"recordType"} />
                </div>
              </div>

              <div className="border border-neutral-200">
                <h3 className="text-md font-bold p-2">物件場所</h3>
                <Separator className="mb-2" />
                <div className="p-2">
                  <AnalysisPiePrefecture uniqueRecords={getUniqueLambdaRecords() || []} recordKey={"prefectureCode"} />
                </div>
              </div>

              <div className="border border-neutral-200">
                <h3 className="text-md font-bold p-2">物件価格帯</h3>
                <Separator className="mb-2" />
                <div className="p-2">
                  <AnalysisPiePriceRange uniqueRecords={getUniqueLambdaRecords() || []} />
                </div>
              </div>

              <div className="border border-neutral-200">
                <h3 className="text-md font-bold p-2">仲介タイプ
                </h3>
                <Separator className="mb-2" />
                <div className="p-2">
                  <AnalysisPieBrokerType uniqueRecords={getUniqueLambdaRecords() || []} />
                </div>
              </div>
            </div>
          </div>
        </div>


        <div className="flex flex-col border border-neutral-200">
          <h2 className="text-lg font-bold bg-neutral-100 p-2 border-b border-neutral-200">販売物件一覧</h2>


          <div className="p-2">
            <ToggleGroup type="single" value={selectedRecordType} onValueChange={(value) => {
              setSelectedRecordType(value);
            }}>
              <ToggleGroupItem value="all">すべて
                <Badge variant="outline"

                >{getCountByType("all")}</Badge>
              </ToggleGroupItem>
              <ToggleGroupItem value="BUILDING">建物
                <Badge variant="outline"
                >{getCountByType("BUILDING")}</Badge>
              </ToggleGroupItem>
              <ToggleGroupItem value="MANSION">マンション
                <Badge variant="outline"
                >{getCountByType("MANSION")}</Badge>
              </ToggleGroupItem>
              <ToggleGroupItem value="HOUSE">戸建て
                <Badge variant="outline"
                >{getCountByType("HOUSE")}</Badge>
              </ToggleGroupItem>
              <ToggleGroupItem value="LAND">土地
                <Badge variant="outline"
                >{getCountByType("LAND")}</Badge>
              </ToggleGroupItem>
            </ToggleGroup>
          </div>

          <Separator className="" />

          <div className="flex flex-col gap-2">



            {getUniqueLambdaRecords().filter((record: any) => {
              if (selectedRecordType === "all") {
                return true;
              }
              return record.recordType === selectedRecordType;
            }).map((record: any) => (
              <div key={record.id} className="bg-neutral-100 p-2">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center gap-2 flex-row">
                    <Badge variant="outline"
                      style={{
                        backgroundColor: getColor(record.propertyAnalysisResult?.overallStarLevel),
                        color: 'white',
                        borderColor: 'transparent',
                      }}
                    >{record.propertyAnalysisResult?.overallStarLevel}</Badge>
                    [{record.recordType}]
                    {record.address} {record.price}万円(
                    {record.yearlyIncome ? (record.yearlyIncome / record.price * 100).toFixed(2) : '0.00'}%)
                  </div>

                  <Button variant="outline">
                    <Link href={`/ex/search/${record.id}`}>
                      <Eye />
                    </Link>
                  </Button>
                </div>
                <DataTable
                  columns={priceChangeColumns}
                  data={record.priceChanges || []}
                  showFooter={false}
                />
              </div>
            ))}

          </div>
        </div>
      </div>
    </div>
  );
}
