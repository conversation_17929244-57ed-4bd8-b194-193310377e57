"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

export function AnalysisPiePriceRange({ uniqueRecords }: { uniqueRecords: any[] }) {
  let ranges = [{
    min: 0,
    max: 10000,
    label: "1億以下"
  }, {
    min: 10000,
    max: 30000,
    label: "1億円以上3億円以下"
  }, {
    min: 30000,
    max: 100000,
    label: "10億円以下"
  }, {
    min: 100000,
    max: 1000000,
    label: "10億円以上"
  }]

  let prepData = ranges.map((range, index) => {
    return {
      key: range.label,
      value: uniqueRecords.filter((record) => record.price >= range.min && record.price <= range.max).length,
      fill: "hsl(var(--chart-" + (index + 1) + "))"
    }
  })
  console.log("prepData ", prepData)

  let chartConfig = {} as any


  prepData.forEach((item) => {
    chartConfig[item.key] = {
      label: item.key,
      color: item.fill
    }
  })

  console.log("chartConfig ", chartConfig)


  return (
    <div className="flex flex-col border-none border-0">
      {/* <CardHeader className="items-center pb-0">
        <CardTitle>Pie Chart</CardTitle>
        <CardDescription>January - June 2024</CardDescription>
      </CardHeader> */}
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig as any}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent nameKey="key" hideLabel />}
            />
            <Pie data={prepData} dataKey="value" nameKey="key" label>
              <LabelList
                dataKey="key"
                className="fill-background"
                stroke="none"
                fontSize={12}
                formatter={(value: keyof typeof chartConfig) =>
                  chartConfig[value]?.label as string
                }
              />
            </Pie>
            <ChartLegend />
          </PieChart>

        </ChartContainer>
      </CardContent>
      {/* <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total visitors for the last 6 months
        </div>
      </CardFooter> */}
    </div>
  )
}
