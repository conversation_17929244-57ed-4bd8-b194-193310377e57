"use client";
import { getCompanyDetailsAction } from "@/actions/proCompany";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { ProCompanyProps } from "@/lib/definitions/proCompany";
import { StarIcon } from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { updateCompanyAction } from "@/actions/proCompany";
import { toast } from "@/hooks/use-toast";
import { useRouter } from "next/navigation";

export default function CompanyEditPage() {
  const [company, setCompany] = useState<ProCompanyProps | null>(null);
  const [comments, setComments] = useState<string>("");
  const [isFav, setIsFav] = useState<boolean>(false);
  const { id } = useParams();
  const router = useRouter();

  const fetchCompany = async () => {
    const company = await getCompanyDetailsAction(id as string);
    if (company.success) {
      setCompany(company.data);
      setIsFav(company.data.isFav);
      setComments(company.data.comments);
    }
  }

  useEffect(() => {
    fetchCompany();
  }, [id]);

  const handleSave = async () => {
    const company = await updateCompanyAction(id as string, { comments, isFav: isFav ? 1 : 0 });
    if (company.success) {
      setCompany(company.data);
      toast({
        title: "保存しました",
      });
      router.push(`/an/company/${id}`);
    } else {
      toast({
        title: "保存に失敗しました",
      });
    }
  }

  return <div>
    <div className="flex flex-row justify-between items-center">
      <div className="flex justify-between items-center p-4">
        <h1 className="text-2xl font-bold" aria-label="業者編集">
          <span className="mr-2">業者編集:</span>
          {company?.fullName}
        </h1>
      </div>
    </div>

    <Separator className="mb-2" />

    <div className="flex flex-col gap-4 p-4">
      <Label>コメント</Label>
      <Textarea
        placeholder="コメント"
        value={comments || ""}
        onChange={(e) => setComments(e.target.value)}
      />


      <Label>お気に入り</Label>
      <Checkbox checked={isFav} onCheckedChange={(checked) => setIsFav(checked as boolean)} />

      <Button onClick={handleSave}>保存</Button>
    </div>
  </div>;
}
