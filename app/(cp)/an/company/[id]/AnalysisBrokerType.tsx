"use client"

import { TrendingUp } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> } from "recharts"

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

export function AnalysisPieBrokerType({ uniqueRecords }: { uniqueRecords: any[] }) {
  let uniqueKeys = [...new Set(uniqueRecords.map((record) => record.priceChanges[0].brokerType))]
  console.log("uniqueKeys ", uniqueKeys)



  let prepData = uniqueKeys.map((key, index) => {
    return {
      key: key,
      value: uniqueRecords.filter((record) => record.priceChanges[0].brokerType === key).length,
      fill: "hsl(var(--chart-" + (index + 1) + "))"
    }
  })
  console.log("prepData ", prepData)

  let chartConfig = {} as any


  prepData.forEach((item) => {
    chartConfig[item.key] = {
      label: item.key,
      color: item.fill
    }
  })

  return (
    <div className="flex flex-col border-none border-0">
      {/* <CardHeader className="items-center pb-0">
        <CardTitle>Pie Chart</CardTitle>
        <CardDescription>January - June 2024</CardDescription>
      </CardHeader> */}
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig as any}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent nameKey="key" hideLabel />}
            />
            <Pie data={prepData} dataKey="value" nameKey="key" label>
              <LabelList
                dataKey="key"
                className="fill-background"
                stroke="none"
                fontSize={12}
                formatter={(value: keyof typeof chartConfig) =>
                  chartConfig[value]?.label as string
                }
              />
            </Pie>
            <ChartLegend />
          </PieChart>

        </ChartContainer>
      </CardContent>
      {/* <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total visitors for the last 6 months
        </div>
      </CardFooter> */}
    </div>
  )
}
