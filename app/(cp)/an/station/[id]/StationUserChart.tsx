"use client"

import { TrendingUp } from "lucide-react"
import { CartesianGrid, Line, LineChart, XAxis, YAxis } from "recharts"

import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import dayjs from "dayjs"


const chartConfig = {
  userCount: {
    label: "userCount",
    color: "hsl(var(--chart-1))",
  },
} satisfies ChartConfig

export function StationUserChart({ data }: { data: any }) {
  let chartData = [] as any;

  ["2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023"].forEach((year) => {
    chartData.push({
      month: year,
      userCount: data?.[`stationUserCombined${year}`] as number | undefined || "-"
    });
  });

  return (
    <ChartContainer config={chartConfig}>
      <Line<PERSON>hart
        accessibilityLayer
        data={chartData}
        margin={{
          left: 12,
          right: 12,
        }}
      >
        <CartesianGrid vertical={false} />
        <YAxis
          dataKey="userCount"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          tickFormatter={(value) => value.toString()}
          domain={[(dataMin: number) => parseFloat((dataMin * 0.9).toFixed(0)), (dataMax: number) => parseFloat((dataMax * 1.1).toFixed(0))]}
        />
        <XAxis
          dataKey="month"
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          tickFormatter={(value) => dayjs(value).format("YYYY")}
        />
        <ChartTooltip
          cursor={false}
          content={<ChartTooltipContent hideLabel />}
        />
        <Line
          dataKey="userCount"
          type="natural"
          stroke="var(--color-userCount)"
          strokeWidth={2}
          dot={false}
        />
      </LineChart>
    </ChartContainer>
  )
}
