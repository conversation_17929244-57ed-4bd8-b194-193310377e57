"use client";

import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>List } from "@/components/ui/tabs";
import { UserLambdaRecordProps, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import LeafletMap from "@/components/LeafletMap";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { ColumnDef } from "@tanstack/react-table";
import { useAuthStore } from "@/store/auth";
import { Button } from "@/components/ui/button";
import { Tooltip } from "@/components/ui/tooltip";
import { TooltipContent } from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>hare, LockIcon } from "lucide-react";
import { Tooltip<PERSON>rigger } from "@/components/ui/tooltip";
import { getStationGroupAction } from "@/actions/geoRailwayStationGroups";
import { StationUserChart } from "./StationUserChart";
import Summary from "../../(common)/summary";
import { getAveargePriceForType, getAverageRoiForType } from "@/lib/userLambdaRecord/summaryForType";
import { useInView } from 'react-intersection-observer';
import { RecordTypeSection } from "../../(common)/RecordTypeSection";
import { mapper } from "../../(common)/recordTypeMapper";
import { getUserLambdaRecordSummary } from "@/actions/tllUserLambdaRecordSummary";
import { RecordRentSection } from "../../(common)/RecordRentSection";
import { getProMansionRentSummary } from "@/actions/proMansionRent";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { getProBuildingHouseRentSummary } from "@/actions/proBuildingHouseRent";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";


export default function StationPage() {
  const [selectedTab, setSelectedTab] = useState<string>("summary");
  const [stationGroup, setStationGroup] = useState<any | null>(null);
  const { id } = useParams();
  const [userLambdaRecordSummaries, setUserLambdaRecordSummaries] = useState<UserLambdaRecordProps[]>([]);
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const { currentUser } = useAuthStore();
  const [proMansionRentSummary, setProMansionRentSummary] = useState<ProMansionRentProps[]>([]);
  const [proBuildingRentSummary, setProBuildingRentSummary] = useState<ProBuildingHouseRentProps[]>([]);
  const [proHouseRentSummary, setProHouseRentSummary] = useState<ProBuildingHouseRentProps[]>([]);
  const [isWatched, setIsWatched] = useState<boolean>(false);
  const { ref: refBuilding, inView: inViewBuilding } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refBuildingRent, inView: inViewBuildingRent } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refMansion, inView: inViewMansion } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refMansionRent, inView: inViewMansionRent } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refHouse, inView: inViewHouse } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refHouseRent, inView: inViewHouseRent } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refLand, inView: inViewLand } = useInView({ triggerOnce: true, threshold: 0.1 });

  useEffect(() => {
    if (stationGroup) {
      document.title = `Urbalytics | ${stationGroup?.name}駅分析`;
    }
  }, [stationGroup]);

  useEffect(() => {
    getStationGroupAction(Number(id)).then((res) => {
      setStationGroup(res.data);
    });

    getUserLambdaRecordSummary({ nearestStationGroupId: id as string }).then((res) => {
      setUserLambdaRecordSummaries(res.data);
    });

    getProMansionRentSummary({ nearestStationGroupId: id as string }).then((res) => {
      setProMansionRentSummary(res.data);
    });

    getProBuildingHouseRentSummary({ nearestStationGroupId: id as string, recordType: "BUILDING" }).then((res) => {
      setProBuildingRentSummary(res.data);
    });

    getProBuildingHouseRentSummary({ nearestStationGroupId: id as string, recordType: "HOUSE" }).then((res) => {
      setProHouseRentSummary(res.data);
    });
  }, [id]);

  useEffect(() => {
    if (currentUser) {
      setColumns(getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser }));
    }
  }, [currentUser]);

  const prepText = (data: UserLambdaRecordProps[]) => {
    let descriptionMansion = `〒${stationGroup?.postalCode} ${stationGroup?.address} - ${stationGroup?.name}駅`;

    return `${descriptionMansion}\n${[UserLambdaRecordType.BUILDING, UserLambdaRecordType.MANSION, UserLambdaRecordType.HOUSE, UserLambdaRecordType.LAND].map((type) => `[${mapper[type].nameFull}売買] ${data.filter((record) => record.recordType === type).length}件 | 平均価格: ${getAveargePriceForType(data, type)}万円 ${type === UserLambdaRecordType.BUILDING ? `| 平均利回り: ${getAverageRoiForType(data, type)}%` : ""} `).join("\n")}`;
  }

  return (
    <div>
      <div className="flex flex-row justify-between items-center p-4">
        <h1 className="text-2xl font-bold flex-1" aria-label="地域分析">
          {stationGroup?.name}駅
        </h1>

        <div className="flex flex-row gap-2 justify-end items-center">
          <Tooltip delayDuration={200}>
            <TooltipTrigger className="flex flex-row gap-2 items-center" asChild>
              <Button variant="outline" onClick={() => {
                navigator.clipboard.writeText(
                  prepText(userLambdaRecordSummaries) + "\n" + "🔗 https://urbalytics.jp/an/area/" + id);
                toast({
                  title: "PreviewURLをクリップボードにコピーしました",
                });
              }}>
                <Share className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              プレビューページのリンクを作成中...
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      <Separator className="" />

      <Tabs value={selectedTab} onValueChange={(value) => {
        setSelectedTab(value);
        const element = document.getElementById(value);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }} className="px-4 py-2 sticky top-0 bg-white z-10 w-full border-b border-gray-200">
        <div className="w-full overflow-x-auto scrollbar-hide px-2">
          <TabsList className="bg-white flex space-x-4 p-4 pl-0 min-w-max justify-start">
            <TabsTrigger value="summary">要約</TabsTrigger>
            <TabsTrigger value="building">
              一棟収益売買
              {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.BUILDING).length > 0 && <span className="text-sm text-neutral-500">
                : {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.BUILDING).length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="buildingRent">
              一棟収益賃貸
              {proBuildingRentSummary.length > 0 && <span className="text-sm text-neutral-500">
                : {proBuildingRentSummary.length}件
              </span>}

              {currentUser?.accessLevel && currentUser?.accessLevel < 10 && <LockIcon className="ml-2 w-4 h-4 text-neutral-500" />}
            </TabsTrigger>

            <TabsTrigger value="mansion">
              区分マンション売買
              {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.MANSION).length > 0 && <span className="text-sm text-neutral-500">
                : {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.MANSION).length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="mansionRent">
              区分マンション賃貸
              {proMansionRentSummary.length > 0 && <span className="text-sm text-neutral-500">
                : {proMansionRentSummary.length}件
              </span>}

              {currentUser?.accessLevel && currentUser?.accessLevel < 10 && <LockIcon className="ml-2 w-4 h-4 text-neutral-500" />}
            </TabsTrigger>

            <TabsTrigger value="house">
              戸建売買
              {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.HOUSE).length > 0 && <span className="text-sm text-neutral-500">
                : {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.HOUSE).length}件
              </span>}
            </TabsTrigger>


            <TabsTrigger value="houseRent">
              戸建賃貸
              {proHouseRentSummary.length > 0 && <span className="text-sm text-neutral-500">
                : {proHouseRentSummary.length}件
              </span>}

              {currentUser?.accessLevel && currentUser?.accessLevel < 10 && <LockIcon className="ml-2 w-4 h-4 text-neutral-500" />}
            </TabsTrigger>

            <TabsTrigger value="land">土地
              {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.LAND).length > 0 && <span className="text-sm text-neutral-500">
                : {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.LAND).length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="priceTrend" disabled={true}>
              地価推移
            </TabsTrigger>

            {/* <TabsTrigger value="potential" disabled={true}>
            アップサイド
          </TabsTrigger> */}
          </TabsList>
        </div>
      </Tabs>

      <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100" id="summary">
        <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
          <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
            基本情報
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 p-2 gap-2">
            <div className="flex flex-col justify-between gap-2">
              <div className="flex flex-col gap-2 bg-white border border-gray-200 rounded-md p-2">
                <div>
                  住所: {stationGroup?.address}
                </div>
                <div className="text-sm text-neutral-500">
                  緯度: {stationGroup?.latitude} | 経度: {stationGroup?.longitude}
                </div>
                <div>
                  県: {stationGroup?.prefectureCode} | 郵便番号: {stationGroup?.postalCode}
                </div>
              </div>

              <div className="bg-white border border-gray-200 rounded-md">
                <div className="text-base font-bold p-2 border-b border-gray-200" >{stationGroup?.name} 駅利用者推移</div>

                <div className="flex flex-row gap-2 w-full p-2">
                  <div className="flex flex-col gap-1">
                    {["2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018", "2019", "2020", "2021", "2022", "2023"].filter((year) => stationGroup?.[`stationUserCombined${year}`] !== undefined && stationGroup?.[`stationUserCombined${year}`] !== null && stationGroup?.[`stationUserCombined${year}`] !== 0).map((year) => (
                      <div key={year}>
                        {year}: {stationGroup?.[`stationUserCombined${year}`]?.toLocaleString() as number | undefined || "-"}人
                      </div>
                    ))}
                  </div>

                  <div className="flex-1">
                    <StationUserChart data={stationGroup} />
                  </div>
                </div>
              </div>

              <Summary userLambdaRecords={userLambdaRecordSummaries} proMansionRentSummary={proMansionRentSummary} proBuildingRentSummary={proBuildingRentSummary} proHouseRentSummary={proHouseRentSummary} />
            </div>

            <div>
              {stationGroup?.latitude && stationGroup?.longitude && <LeafletMap
                center={[stationGroup?.latitude as number, stationGroup?.longitude as number]}
                zoom={16}
                data={[]}
                height="640px"
              />}
            </div>
          </div>
        </div>
      </div>

      <div ref={refBuilding} id="building">
        {inViewBuilding ? <RecordTypeSection recordType={UserLambdaRecordType.BUILDING} filter={{ nearestStationGroupId: id as string }} /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refBuildingRent} id="buildingRent">
        {inViewBuildingRent ? <RecordRentSection recordType={UserLambdaRecordType.BUILDING} filter={{ nearestStationGroupId: id as string }} name="BUILDING_RENT" /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refMansion} id="mansion">
        {inViewMansion ? <RecordTypeSection recordType={UserLambdaRecordType.MANSION} filter={{ nearestStationGroupId: id as string }} /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refMansionRent} id="mansionRent">
        {inViewMansionRent ? <RecordRentSection recordType={UserLambdaRecordType.MANSION} filter={{ nearestStationGroupId: id as string }} name="MANSION_RENT" /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refHouse} id="house">
        {inViewHouse ? <RecordTypeSection recordType={UserLambdaRecordType.HOUSE} filter={{ nearestStationGroupId: id as string }} /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refHouseRent} id="houseRent">
        {inViewHouseRent ? <RecordRentSection recordType={UserLambdaRecordType.HOUSE} filter={{ nearestStationGroupId: id as string }} name="HOUSE_RENT" /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refLand} id="land">
        {inViewLand ? <RecordTypeSection recordType={UserLambdaRecordType.LAND} filter={{ nearestStationGroupId: id as string }} /> : <div className="h-20">Loading...</div>}
      </div>

      {/* <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100" id="priceTrend">
      <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
        <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
          地価推移
        </div>
      </div>
    </div> */}

      {/* <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100" id="potential">
      <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
        <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
          アップサイド
        </div>

        <div className="p-2 grid grid-cols-1 sm:grid-cols-2">
          <div>
            人口変動
          </div>
          <div>
            駅ユーザー変動
          </div>
          <div>
            世帯変動
          </div>
          <div>
            大開発予定
          </div>
        </div>
      </div>
    </div> */}
    </div>
  )
}