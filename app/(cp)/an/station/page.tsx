"use client";

import SearchBarPostalCodeUI from "@/components/ui/SearchBarPostalCodeUI";
import { sendLark, LARK_URLS } from "@/lib/thirdParty/lark";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import SearchBarStationUI from "@/components/ui/SearchBarStationUI";
import { useAuthStore } from "@/store/auth";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import { getRecordsWithInRange } from "@/actions/geoPostalCodes";
import UsageOverBox from "@/app/(cp)/my/usage/UsageOverBox";


export default function Station() {
  const [selectedStation, setSelectedStation] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { currentUser } = useAuthStore();
  // const [postalCodes, setPostalCodes] = useState<any[]>([]);
  // const [bounds, setBounds] = useState<any>({
  //   topLeft: null,
  //   bottomRight: null,
  // });

  useEffect(() => {
    if (selectedStation) {
      router.push(`/an/station/${selectedStation.value}`); // station group id 

      createNewSystemUserActivityAction({
        data: {
          eventType: "BUTTON_CLICK",
          route: "/an/station",
          eventMetadata: {
            buttonName: "goAnalysisDpButton",
            selectedStation: selectedStation,
          },
        },
      });

      sendLark({
        message: `[🏢][分析][駅]${currentUser?.name}さんが${selectedStation?.label}を検索しました`,
        url: LARK_URLS.USER_ACTIVITY_CHANNEL,
      });
    }
  }, [selectedStation]);


  return (
    <div>
      <div className="px-4 py-2 flex flex-row gap-4 w-full mt-2">
        <div className="flex-1 w-full">
          <SearchBarStationUI selectedStation={selectedStation} setSelectedStation={setSelectedStation} />
        </div>
      </div>
      <Separator className="my-2" />
    </div>
  )
}
