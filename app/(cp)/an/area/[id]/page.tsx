"use client";

import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>List } from "@/components/ui/tabs";
import { GeoPostalCodeProps } from "@/lib/definitions";
import { UserLambdaRecordProps, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { getPostalCodeAction } from "@/actions/geoPostalCodes";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import LeafletMap from "@/components/LeafletMap";
import { Button } from "@/components/ui/button";
import { Tooltip } from "@/components/ui/tooltip";
import { TooltipContent } from "@/components/ui/tooltip";
import { toast } from "@/hooks/use-toast";
import { ScanEye, Share } from "lucide-react";
import { TooltipTrigger } from "@/components/ui/tooltip";
import Summary from "../../(common)/summary";
import { RecordTypeSection } from "../../(common)/RecordTypeSection";
import { useInView } from 'react-intersection-observer';
import { mapper } from "../../(common)/recordTypeMapper";
import { getUserLambdaRecordSummary } from "@/actions/tllUserLambdaRecordSummary";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { getProMansionRentSummary } from "@/actions/proMansionRent";
import { getProBuildingHouseRentSummary } from "@/actions/proBuildingHouseRent";
import { RecordRentSection } from "../../(common)/RecordRentSection";
import { getAveargePriceForType, getAverageRoiForType } from "@/lib/userLambdaRecord/summaryForType";
import { useRouter } from "next/navigation";


export default function AreaPage() {
  const [selectedTab, setSelectedTab] = useState<string>("summary");
  const [postalCode, setPostalCode] = useState<GeoPostalCodeProps | null>(null);
  const { id } = useParams();
  const [userLambdaRecordSummaries, setUserLambdaRecordSummaries] = useState<UserLambdaRecordProps[]>([]);
  const [isWatched, setIsWatched] = useState<boolean>(false);

  const { ref: refBuilding, inView: inViewBuilding } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refBuildingRent, inView: inViewBuildingRent } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refMansion, inView: inViewMansion } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refMansionRent, inView: inViewMansionRent } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refHouse, inView: inViewHouse } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refHouseRent, inView: inViewHouseRent } = useInView({ triggerOnce: true, threshold: 0.1 });
  const { ref: refLand, inView: inViewLand } = useInView({ triggerOnce: true, threshold: 0.1 });
  const searchParams = useSearchParams();
  const router = useRouter();
  const [proMansionRentSummary, setProMansionRentSummary] = useState<ProMansionRentProps[]>([]);
  const [proBuildingRentSummary, setProBuildingRentSummary] = useState<ProBuildingHouseRentProps[]>([]);
  const [proHouseRentSummary, setProHouseRentSummary] = useState<ProBuildingHouseRentProps[]>([]);

  useEffect(() => {
    if (postalCode) {
      document.title = `Urbalytics | ${postalCode?.prefectureName}${postalCode?.cityName}${postalCode?.areaName}${postalCode?.choumeName}分析`;
    }
  }, [postalCode]);

  useEffect(() => {
    getPostalCodeAction(Number(id)).then((res) => {
      setPostalCode(res.data[0]);
    });

    getUserLambdaRecordSummary({ postalCode: Number(id) }).then((res) => {
      setUserLambdaRecordSummaries(res.data);
    });

    getProMansionRentSummary({ postalCode: Number(id) }).then((res) => {
      setProMansionRentSummary(res.data);
    });

    getProBuildingHouseRentSummary({ postalCode: Number(id), recordType: "BUILDING" }).then((res) => {
      setProBuildingRentSummary(res.data);
    });

    getProBuildingHouseRentSummary({ postalCode: Number(id), recordType: "HOUSE" }).then((res) => {
      setProHouseRentSummary(res.data);
    });
  }, [id]);

  const prepText = (data: UserLambdaRecordProps[]) => {
    let descriptionMansion = `〒${postalCode?.postalCode} ${postalCode?.prefectureName}${postalCode?.cityName}${postalCode?.areaName}${postalCode?.choumeName} `;

    return `${descriptionMansion}\n${[UserLambdaRecordType.BUILDING, UserLambdaRecordType.MANSION, UserLambdaRecordType.HOUSE, UserLambdaRecordType.LAND].map((type) => `[${mapper[type]?.name}] ${data.filter((record) => record.recordType === type).length}件 | 平均価格: ${getAveargePriceForType(data, type)}万円 ${type === UserLambdaRecordType.BUILDING ? `| 平均利回り: ${getAverageRoiForType(data, type)}%` : ""} `).join("\n")}`;
  }

  useEffect(() => {
    const section = searchParams.get("section");
    if (section) {
      router.push(`/an/area/${id}?section=${section}`);
    }
  }, []);

  return (
    <div>
      <div className="flex flex-row justify-between items-center p-4">
        <h1 className="text-2xl font-bold flex-1" aria-label="地域分析">
          〒{postalCode?.postalCode}: {postalCode?.prefectureName}{postalCode?.cityName}{postalCode?.areaName}{postalCode?.choumeName}
        </h1>

        <div className="flex flex-row gap-2 justify-end items-center">
          <Tooltip delayDuration={200}>
            <TooltipTrigger className="flex flex-row gap-2 items-center" asChild>
              <Button variant="outline" onClick={() => {
                navigator.clipboard.writeText(
                  prepText(userLambdaRecordSummaries) + "\n" + "🔗 https://urbalytics.jp/an/area/" + id);
                toast({
                  title: "PreviewURLをクリップボードにコピーしました",
                });
              }}>
                <Share className="w-4 h-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              プレビューページのリンクを作成中...
            </TooltipContent>
          </Tooltip>

        </div>
      </div>

      <Separator className="" />

      <Tabs value={selectedTab} onValueChange={(value) => {
        setSelectedTab(value);
        const element = document.getElementById(value);
        // if (element) {
        //   element.scrollIntoView({ behavior: "smooth" });
        // }
        router.replace(`#${value}`);

      }} className="px-4 py-2 sticky top-0 bg-white z-10 w-full border-b border-gray-200">
        <div className="w-full overflow-x-auto scrollbar-hide px-2">
          <TabsList className="bg-white flex space-x-4 p-4 pl-0 min-w-max justify-start">
            <TabsTrigger value="summary">要約</TabsTrigger>
            <TabsTrigger value="building">
              一棟収益物件
              {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.BUILDING).length > 0 && <span className="text-sm text-neutral-500">
                : {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.BUILDING).length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="buildingRent">一棟収益賃貸
              {proBuildingRentSummary.length > 0 && <span className="text-sm text-neutral-500">
                : {proBuildingRentSummary.length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="mansion">
              区分マンション売買
              {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.MANSION).length > 0 && <span className="text-sm text-neutral-500">
                : {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.MANSION).length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="mansionRent">
              区分マンション賃貸
              {proMansionRentSummary.length > 0 && <span className="text-sm text-neutral-500">
                : {proMansionRentSummary.length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="house">戸建
              {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.HOUSE).length > 0 && <span className="text-sm text-neutral-500">
                : {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.HOUSE).length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="houseRent">戸建賃貸
              {proHouseRentSummary.length > 0 && <span className="text-sm text-neutral-500">
                : {proHouseRentSummary.length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="land">土地
              {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.LAND).length > 0 && <span className="text-sm text-neutral-500">
                : {userLambdaRecordSummaries.filter((record) => record.recordType === UserLambdaRecordType.LAND).length}件
              </span>}
            </TabsTrigger>

            <TabsTrigger value="priceTrend" disabled={true}>
              地価推移
            </TabsTrigger>

            {/* <TabsTrigger value="potential" disabled={true}>
            アップサイド
          </TabsTrigger> */}
          </TabsList>
        </div>
      </Tabs>

      <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100" id="summary">
        <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
          <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
            要約
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 p-2 gap-2">
            <div className="flex flex-col justify-between gap-2">
              <div className="flex flex-col gap-2 bg-white border border-gray-200 rounded-md p-2">
                <div>
                  県: {postalCode?.prefectureName}
                </div>
                <div>
                  シティ名: {postalCode?.cityName}
                </div>
                <div>
                  エリア名: {postalCode?.areaName}
                </div>
                <div>
                  丁目: {postalCode?.choumeName}
                </div>
                <div>
                  郵便番号: {postalCode?.postalCode}
                </div>
              </div>

              <Summary userLambdaRecords={userLambdaRecordSummaries} proMansionRentSummary={proMansionRentSummary} proBuildingRentSummary={proBuildingRentSummary} proHouseRentSummary={proHouseRentSummary} />
            </div>

            <div>
              {postalCode?.latitude && postalCode?.longitude && <LeafletMap
                center={[postalCode?.latitude as number, postalCode?.longitude as number]}
                zoom={16}
                data={[]}
              />}
            </div>
          </div>
        </div>
      </div>

      <div ref={refBuilding} id="building">
        {inViewBuilding ? <RecordTypeSection recordType={UserLambdaRecordType.BUILDING} filter={{ postalCode: Number(id) }} /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refBuildingRent} id="buildingRent">
        {inViewBuildingRent ? <RecordRentSection recordType={UserLambdaRecordType.BUILDING} filter={{ postalCode: Number(id) }} name="BUILDING_RENT" /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refMansion} id="mansion">
        {inViewMansion ? <RecordTypeSection recordType={UserLambdaRecordType.MANSION} filter={{ postalCode: Number(id) }} /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refMansionRent} id="mansionRent">
        {inViewMansionRent ? <RecordRentSection recordType={UserLambdaRecordType.MANSION} filter={{ postalCode: Number(id) }} name="MANSION_RENT" /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refHouse} id="house">
        {inViewHouse ? <RecordTypeSection recordType={UserLambdaRecordType.HOUSE} filter={{ postalCode: Number(id) }} /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refHouseRent} id="houseRent">
        {inViewHouseRent ? <RecordRentSection recordType={UserLambdaRecordType.HOUSE} filter={{ postalCode: Number(id) }} name="HOUSE_RENT" /> : <div className="h-20">Loading...</div>}
      </div>

      <div ref={refLand} id="land">
        {inViewLand ? <RecordTypeSection recordType={UserLambdaRecordType.LAND} filter={{ postalCode: Number(id) }} /> : <div className="h-20">Loading...</div>}
      </div>

      {/* <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100" id="priceTrend">
      <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
        <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
          地価推移
        </div>
      </div>
    </div> */}

      {/* <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100" id="potential">
      <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
        <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
          アップサイド
        </div>

        <div className="p-2 grid grid-cols-1 sm:grid-cols-2">
          <div>
            人口変動
          </div>
          <div>
            駅ユーザー変動
          </div>
          <div>
            世帯変動
          </div>
          <div>
            大開発予定
          </div>
        </div>

      </div>
    </div> */}
    </div>
  )
}