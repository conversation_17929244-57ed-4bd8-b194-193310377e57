"use client"


import { Building2, LandPlot, House, Hotel } from "lucide-react"
import { mapper } from "./recordTypeMapper";
import { Separator } from "@/components/ui/separator";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { getAllTimeAverageBuildingHouseRentPrice, getAllTimeAverageRentPrice } from "../mansion/utiliy/getAllTimeData";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { UserLambdaRecordType, UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { getAveargePriceForType, getAverageRoiForType, getAverageAreaForType, getAverageLandAreaForType } from "@/lib/userLambdaRecord/summaryForType";

export default function Summary({ userLambdaRecords, proMansionRentSummary, proBuildingRentSummary, proHouseRentSummary }: { userLambdaRecords: UserLambdaRecordProps[], proMansionRentSummary: ProMansionRentProps[], proBuildingRentSummary: ProBuildingHouseRentProps[], proHouseRentSummary: ProBuildingHouseRentProps[] }) {
  return <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
    {[UserLambdaRecordType.BUILDING, UserLambdaRecordType.MANSION, UserLambdaRecordType.HOUSE, UserLambdaRecordType.LAND].map((type) => (
      <div key={type} className="border rounded-md border-gray-200 min-h-20 flex items-center justify-start p-2 flex-row gap-4">
        <div className="flex flex-col items-center justify-center">
          {mapper[type].iconBig}
          <div>
            <span className="text-sm">{mapper[type].name}</span>
          </div>
        </div>

        <div className="flex flex-col flex-1">
          <div className="flex flex-row flex-1 justify-between items-center">
            <div className="flex flex-col text-sm flex-1">
              <div className="font-bold">
                平均価格: {getAveargePriceForType(userLambdaRecords, type) || "-"}万円
              </div>

              {type === UserLambdaRecordType.BUILDING && <div>
                利回: {getAverageRoiForType(userLambdaRecords, type) || 0}% | 面積: {getAverageAreaForType(userLambdaRecords, type) || 0}平米
              </div>}

              {type === UserLambdaRecordType.MANSION && <div>
                面積: {getAverageAreaForType(userLambdaRecords, type) || 0}平米
              </div>}

              {type === UserLambdaRecordType.HOUSE && <div>
                面積: {getAverageAreaForType(userLambdaRecords, type) || 0}平米
              </div>}

              {type === UserLambdaRecordType.LAND && <div>
                面積: {getAverageLandAreaForType(userLambdaRecords, type) || 0}平米
              </div>}
            </div>

            <div className="text-2xl float-right">
              {userLambdaRecords.filter((record) => record.recordType === type).length}
              <span className="text-sm ml-1">件</span>
            </div>
          </div>

          {type === UserLambdaRecordType.MANSION &&
            <>
              <Separator className="mb-0.5 mt-1" />
              <div className="flex flex-row text-xs justify-center items-center flex-1 text-neutral-500">
                <div className="flex flex-col flex-1">
                  賃貸坪単価: {getAllTimeAverageRentPrice(proMansionRentSummary).averageUnitPrice || 0}万円
                </div>

                <div className="x-1">
                  {proMansionRentSummary?.length}件
                </div>
              </div>
            </>
          }

          {type === UserLambdaRecordType.BUILDING &&
            <>
              <Separator className="mb-0.5 mt-1" />
              <div className="flex flex-row text-xs justify-center items-center flex-1 text-neutral-500">
                <div className="flex flex-col flex-1">
                  賃貸坪単価: {getAllTimeAverageBuildingHouseRentPrice(proBuildingRentSummary).averageUnitPrice || 0}万円
                </div>

                <div className="x-1">
                  {proBuildingRentSummary?.length}件
                </div>
              </div>
            </>
          }

          {type === UserLambdaRecordType.HOUSE &&
            <>
              <Separator className="mb-0.5 mt-1" />
              <div className="flex flex-row text-xs justify-center items-center flex-1 text-neutral-500">
                <div className="flex flex-col flex-1">
                  賃貸坪単価: {getAllTimeAverageBuildingHouseRentPrice(proHouseRentSummary).averageUnitPrice || 0}万円
                </div>

                <div className="x-1">
                  {proHouseRentSummary?.length}件
                </div>
              </div>
            </>
          }
        </div>
      </div>
    ))}
  </div>
}