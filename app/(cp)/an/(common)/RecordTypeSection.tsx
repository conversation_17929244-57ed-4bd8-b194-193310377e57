"use client";

import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { DataTable } from "@/components/ui/data-table";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { useEffect, useState } from "react";
import { mapper } from "./recordTypeMapper";
import Sale<PERSON>hart from "../mansion/[id]/SaleChart";
import dayjs from "dayjs";
import { getUserLambdaRecords } from "@/actions/tllUserLambdaRecords";
import { ColumnDef } from "@tanstack/react-table";
import { useAuthStore } from "@/store/auth";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { Loader2 } from "lucide-react";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";
import { Select, SelectItem, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select";

export function RecordTypeSection({ recordType, filter }: { recordType: UserLambdaRecordType, filter: any }) {
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedYear, setSelectedYear] = useState<string>("all");
  const { currentUser } = useAuthStore();
  const [columns, setColumns] = useState<ColumnDef<any>[]>([]);
  const [userLambdaRecords, setUserLambdaRecords] = useState<UserLambdaRecordProps[] | null>(null);

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const filterRecords = (record: UserLambdaRecordProps) => {
    if (record.recordType !== recordType) {
      return false;
    }

    if (selectedStatus !== "all") {
      if (getStatus(record) !== selectedStatus) {
        return false;
      }
    }

    if (selectedYear !== "all") {
      let monthDiff = dayjs().diff(dayjs(record.updatedAt), "month");
      if (monthDiff > Number(selectedYear) * 12) {
        return false;
      }
    }

    return true;
  }

  useEffect(() => {
    if (recordType && userLambdaRecords === null) {
      setIsLoading(true);
      getUserLambdaRecords({
        ...filter,
        recordType: recordType
      }).then((res) => {
        setUserLambdaRecords(res.data);
        setIsLoading(false);
      });
    }
  }, []);

  useEffect(() => {
    if (currentUser) {
      setColumns(getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel({ currentUser }));
    }
  }, [currentUser]);

  return <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100" id="building">
    <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
      <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
        {mapper[recordType].nameFull}売買
      </div>

      <div className="p-2 gap-2 grid grid-cols-1 sm:grid-cols-3">
        {isLoading || userLambdaRecords === null ? <div className="h-[240px] w-full flex flex-col items-center justify-center">
          <Loader2 className="w-12 h-12 animate-spin" />
        </div> :
          <>
            <div className="flex flex-col gap-4 col-span-1">
              <div className="flex flex-col gap-2 bg-gray-50 p-2">
                <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
                  <h1 className="text-base">月別坪単価価格推移</h1>
                </div>

                <SaleChart records={userLambdaRecords.filter(r => filterRecords(r)).sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} unit="month" />
              </div>

              <div className="flex flex-col gap-2 bg-gray-50 p-2">
                <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
                  <h1 className="text-base">四半期別坪単価価格推移</h1>
                </div>

                <SaleChart records={userLambdaRecords.filter(r => filterRecords(r)).sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} unit="quarter" />
              </div>
            </div>

            <div className="flex flex-col gap-2 col-span-2">
              <div className="flex flex-row text-sm gap-2">
                <div className="border-r border-gray-200 py-2 pr-2 flex flex-row gap-2 justify-center items-center">
                  <label className="text-sm text-gray-500 w-[60px]">状況:</label>
                  <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value)} >
                    <SelectTrigger>
                      <SelectValue placeholder="状況を選択してください" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全て</SelectItem>
                      <SelectItem value="公開中">公開中</SelectItem>
                      <SelectItem value="成約">成約</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="border-r border-gray-200 py-2 pr-2 flex flex-row gap-2 w-[160px] justify-center items-center">
                  <label className="text-sm text-gray-500 w-[100px]">変更日:</label>
                  <Select value={selectedYear} onValueChange={(value) => setSelectedYear(value)} >
                    <SelectTrigger>
                      <SelectValue placeholder="作成日を選択してください" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全て</SelectItem>
                      <SelectItem value="0.5">半年以内</SelectItem>
                      <SelectItem value="1">1年以内</SelectItem>
                      <SelectItem value="3">3年以内</SelectItem>
                      <SelectItem value="5">5年以内</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex flex-row justify-between items-center text-sm text-neutral-500 mb-2">
                合計: {userLambdaRecords.filter((record) => filterRecords(record)).length}件
              </div>
              <DataTable columns={columns} data={userLambdaRecords.filter((record) => filterRecords(record)).sort((a: UserLambdaRecordProps, b: UserLambdaRecordProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} defaultPageSize={10} />
            </div>
          </>}
      </div>
    </div>
  </div>
}