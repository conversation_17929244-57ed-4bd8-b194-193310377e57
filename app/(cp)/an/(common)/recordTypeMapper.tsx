import { Building2, LandPlot, House, Hotel } from "lucide-react";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";

export const mapper = {
  [UserLambdaRecordType.BUILDING]: {
    "nameFull": "一棟収益",
    "nameFullEn": "Multi-family",
    "name": "一棟",
    "nameEn": "Multi-family",
    "iconBig": <Hotel className="w-[36px] h-[36px] stroke-1" />,
    "icon": <Hotel className="w-[24px] h-[24px] stroke-1" />,
    "iconSmall": <Hotel className="w-[16px] h-[16px] stroke-1" />
  },
  [UserLambdaRecordType.MANSION]: {
    "nameFull": "区分マンション",
    "nameFullEn": "Apartment",
    "name": "区分",
    "nameEn": "Apartment",
    "iconBig": <Building2 className="w-[48px] h-[48px] stroke-1" />,
    "icon": <Building2 className="w-[24px] h-[24px] stroke-1" />,
    "iconSmall": <Building2 className="w-[16px] h-[16px] stroke-1" />
  },
  [UserLambdaRecordType.HOUSE]: {
    "nameFullEn": "House",
    "nameFull": "戸建",
    "nameEn": "House",
    "name": "戸建",
    "iconBig": <House className="w-[36px] h-[36px] stroke-1" />,
    "icon": <House className="w-[24px] h-[24px] stroke-1" />,
    "iconSmall": <House className="w-[16px] h-[16px] stroke-1" />
  },
  [UserLambdaRecordType.LAND]: {
    "nameFullEn": "Land",
    "nameFull": "土地",
    "nameEn": "Land",
    "name": "土地",
    "iconBig": <LandPlot className="w-[36px] h-[36px] stroke-1" />,
    "icon": <LandPlot className="w-[24px] h-[24px] stroke-1" />,
    "iconSmall": <LandPlot className="w-[16px] h-[16px] stroke-1" />
  },
} as any;