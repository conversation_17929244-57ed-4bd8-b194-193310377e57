"use client";

import { DataTable } from "@/components/ui/data-table";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { useEffect, useState } from "react";
import SaleChart from "../mansion/[id]/SaleChart";
import dayjs from "dayjs";
import { getUserLambdaRecords } from "@/actions/tllUserLambdaRecords";
import { ColumnDef } from "@tanstack/react-table";
import { useAuthStore } from "@/store/auth";
import { getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel } from "@/components/userLambdaRecord/userlambdaRecordTableColumns";
import { Badge } from "@/components/ui/badge";
import { Loader2 } from "lucide-react";
import { buildingRentColumns, houseRentColumns, mansionRentColumns } from "../mansion/[id]/mansionRentColumns";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { getProBuildingHouseRent } from "@/actions/proBuildingHouseRent";
import RentBuildingHouseChart from "./RentBuildingHouseChart";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";
import { getProMansionRent } from "@/actions/proMansionRent";
import RentChart from "./RentChart";
import { Select, SelectItem, SelectContent, SelectTrigger, SelectValue } from "@/components/ui/select";
import NoPermissionBox from "@/app/(cp)/my/usage/NoPermissionBox";

export function RecordRentSection({ recordType, filter, name }: { recordType: any, filter: any, name: any }) {
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedYear, setSelectedYear] = useState<string>("all");
  const { currentUser } = useAuthStore();
  const [selectedUnitSize, setSelectedUnitSize] = useState<string>("all");
  const [rentRecords, setRentRecords] = useState<ProBuildingHouseRentProps[] | ProMansionRentProps[] | null>(null);

  const [isLoading, setIsLoading] = useState<boolean>(false);

  let mapper = {
    "BUILDING_RENT": {
      "nameFull": "一棟収益賃貸",
      "pageType": "areaStationRentBuilding"
    },
    "HOUSE_RENT": {
      "nameFull": "一戸建て賃貸",
      "pageType": "areaStationRentHouse"
    },
    "MANSION_RENT": {
      "nameFull": "区分マンション賃貸",
      "pageType": "areaStationRentMansion"
    }
  }

  let columns = recordType === UserLambdaRecordType.BUILDING ? buildingRentColumns : recordType === UserLambdaRecordType.HOUSE ? houseRentColumns : mansionRentColumns;

  const filterRecords = (record: any) => {
    // if (selectedStatus !== "all") {
    //   if (record.brokerStatus !== selectedStatus) {
    //     return false;
    //   }
    // }

    if (selectedYear !== "all") {
      let year = dayjs().diff(dayjs(record.createdAt), "year");
      console.log(year, selectedYear);
      if (year > Number(selectedYear)) {
        return false;
      }
    }

    if (selectedUnitSize !== "all" && recordType === UserLambdaRecordType.MANSION) {
      if (selectedUnitSize === "30") {
        if (record.unitSize > 30) {
          return false;
        }
      } else if (selectedUnitSize === "30_50") {
        if (record.unitSize < 30 || record.unitSize > 50) {
          return false;
        }
      } else if (selectedUnitSize === "999") {
        if (record.unitSize < 50) {
          return false;
        }
      }
    }

    return true;
  }

  useEffect(() => {
    if (recordType && rentRecords === null) {
      setIsLoading(true);

      if (recordType === UserLambdaRecordType.BUILDING || recordType === UserLambdaRecordType.HOUSE) {
        getProBuildingHouseRent({
          ...filter,
          recordType: recordType
        }).then((res) => {
          setRentRecords(res.data);
          setIsLoading(false);
        });
      } else if (recordType === UserLambdaRecordType.MANSION) {
        getProMansionRent({
          ...filter,
          recordType: recordType
        }).then((res) => {
          setRentRecords(res.data);
          setIsLoading(false);
        });
      }
    }
  }, []);

  return <div className="px-4 py-2 flex flex-col gap-4 w-full bg-neutral-100" id="building">
    {currentUser?.accessLevel && currentUser?.accessLevel < 10 ? <NoPermissionBox pageType={mapper[name as keyof typeof mapper].pageType as any} /> :
      <div className="bg-white flex flex-col gap-2 border border-gray-200" id="details">
        <div className="text-lg font-bold text-neutral-900 border-b border-neutral-200 p-2">
          {mapper[name as keyof typeof mapper].nameFull}
        </div>

        <div className="p-2 gap-2 grid grid-cols-1 sm:grid-cols-3">
          {isLoading || rentRecords === null ? <div className="h-[240px] w-full flex flex-col items-center justify-center">
            <Loader2 className="w-12 h-12 animate-spin" />
          </div> :
            <>
              <div className="flex flex-col gap-4 col-span-1">
                <div className="flex flex-col gap-2 bg-gray-50 p-2">
                  <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
                    <h1 className="text-base">月別坪単価価格推移</h1>
                  </div>

                  {name !== "MANSION_RENT" ? <RentBuildingHouseChart records={rentRecords.filter(r => filterRecords(r)).sort((a: ProBuildingHouseRentProps, b: ProBuildingHouseRentProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} unit="month" /> : <RentChart records={rentRecords.filter(r => filterRecords(r)).sort((a: any, b: any) => dayjs(b.createdAt).diff(dayjs(a.createdAt))) as any[]} unit="month" />}
                </div>

                <div className="flex flex-col gap-2 bg-gray-50 p-2">
                  <div className="flex flex-col items-start border-gray-200 pb-2 mb-2">
                    <h1 className="text-base">四半期別坪単価価格推移</h1>
                  </div>

                  {name !== "MANSION_RENT" ? <RentBuildingHouseChart records={rentRecords.filter(r => filterRecords(r)).sort((a: ProBuildingHouseRentProps, b: ProBuildingHouseRentProps) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} unit="quarter" /> : <RentChart records={rentRecords.filter(r => filterRecords(r)).sort((a: any, b: any) => dayjs(b.createdAt).diff(dayjs(a.createdAt))) as any[]} unit="quarter" />}
                </div>
              </div>

              <div className="flex flex-col gap-2 col-span-2">
                <div className="flex flex-row text-sm gap-2 justify-start items-center w-full">
                  {/* <div className="border-r border-gray-200 py-2 pr-2 flex flex-row gap-2">
                  状況:
                  <Badge variant={selectedStatus === "all" ? "default" : "outline"} onClick={() => setSelectedStatus("all")}>
                    全て
                  </Badge>
                  <Badge variant={selectedStatus === "公開中" ? "default" : "outline"} onClick={() => setSelectedStatus("公開中")}>
                    公開中
                  </Badge>
                  <Badge variant={selectedStatus === "成約" ? "default" : "outline"} onClick={() => setSelectedStatus("成約")}>
                    成約
                  </Badge>
                </div> */}

                  <div className="border-r border-gray-200 py-2 pr-2 flex flex-row gap-2 w-[160px] justify-center items-center">
                    <label className="text-sm text-gray-500 w-[100px]">作成日:</label>
                    <Select value={selectedYear} onValueChange={(value) => setSelectedYear(value)} >
                      <SelectTrigger>
                        <SelectValue placeholder="作成日を選択してください" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全て</SelectItem>
                        <SelectItem value="1">1年内</SelectItem>
                        <SelectItem value="3">3年内</SelectItem>
                        <SelectItem value="5">5年内</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {recordType === UserLambdaRecordType.MANSION && <div className="py-2 pr-2 flex flex-row gap-2 w-[160px] justify-center items-center">
                    <label className="text-sm text-gray-500 w-[100px]">サイズ:</label>
                    <Select value={selectedUnitSize} onValueChange={(value) => setSelectedUnitSize(value)} >
                      <SelectTrigger>
                        <SelectValue placeholder="サイズを選択してください" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全て</SelectItem>
                        <SelectItem value="30">30㎡以下</SelectItem>
                        <SelectItem value="30_50">30㎡~50㎡</SelectItem>
                        <SelectItem value="999">50㎡~</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>}
                </div>

                <div className="flex flex-row justify-between items-center text-sm text-neutral-500 mb-2">
                  合計: {rentRecords.filter(r => filterRecords(r)).length}件
                </div>
                <DataTable columns={columns} data={rentRecords.filter(r => filterRecords(r)).sort((a: any, b: any) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))} defaultPageSize={10} />
              </div>
            </>}
        </div>
      </div>
    }
  </div>
}