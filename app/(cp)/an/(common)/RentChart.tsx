import dayjs from "dayjs";
import {
  CartesianGrid,
  ComposedChart,
  Line,
  Tooltip,
  XAxis,
  YAxis,
  Bar,
  ResponsiveContainer,
} from "recharts";
import {
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Separator } from "@radix-ui/react-separator";
import { ChartContainer } from "@/components/ui/chart";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import { TrendingUp, TrendingDown } from "lucide-react";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";

dayjs.extend(quarterOfYear);

export default function RentChart({ records, unit }: { records: ProMansionRentProps[], unit: any }) {
  if (!records) {
    return <div>No records</div>;
  }

  let allChangesForEachMonth = {} as {
    [key: string]: {
      price: number;
      buildingSize: number;
      tsuboPrice: number;
    }[];
  };

  records.filter((record) => record.feeRent && record.unitSize).forEach((record) => {
    let startOfPeriod = dayjs(record.createdAt).startOf(unit).format("YYYY/MM/DD");

    if (!allChangesForEachMonth[startOfPeriod]) {
      allChangesForEachMonth[startOfPeriod] = [];
    }

    if (record.feeRent) {
      allChangesForEachMonth[startOfPeriod].push({
        price: record.feeRent,
        buildingSize: record.unitSize || 0,
        tsuboPrice: record.feeRent * 3.3 / (record.unitSize || 0),
      });
    }
  });

  let parsedChartData = Object.keys(allChangesForEachMonth).map((startOfPeriod) => {
    return {
      startOfPeriod: startOfPeriod,
      count: allChangesForEachMonth[startOfPeriod].length,
      averageTsuboPrice: parseFloat(
        (
          allChangesForEachMonth[startOfPeriod].reduce((acc, curr) => acc + curr.tsuboPrice, 0) /
          allChangesForEachMonth[startOfPeriod].length
        ).toFixed(2)
      ),
    };
  }).reverse();

  let overallIncrease = parsedChartData.length > 0 ? parsedChartData[parsedChartData.length - 1]?.averageTsuboPrice - parsedChartData[0]?.averageTsuboPrice : 0;
  let overallIncreasePercentage = (overallIncrease / (parsedChartData.length > 0 ? parsedChartData[0]?.averageTsuboPrice : 1)) * 100;
  let timeDiff = dayjs(parsedChartData.length > 0 ? parsedChartData[parsedChartData.length - 1]?.startOfPeriod : "").diff(dayjs(parsedChartData.length > 0 ? parsedChartData[0]?.startOfPeriod : ""), unit);
  let averageIncreasePercentage = timeDiff > 0 ? overallIncreasePercentage / timeDiff : 0;

  let unitMapper = {
    "month": "ヶ月",
    "quarter": "四半期",
    "year": "年",
  }

  return (
    <div className="w-full h-full">
      <ChartContainer className="" config={{
      }}>
        <ComposedChart data={parsedChartData.sort((a, b) => dayjs(a.startOfPeriod).diff(dayjs(b.startOfPeriod)))} margin={{ top: 24, right: 0, bottom: 0, left: 0 }}>
          <CartesianGrid vertical={false} />

          {/* X-Axis */}
          <XAxis
            dataKey="startOfPeriod"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => dayjs(value).format("YYYY/MM")}
          />

          {/* Y-Axis for `averageTsuboPrice` (left side) */}
          <YAxis
            yAxisId="left"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => value.toFixed(0)}
            domain={[
              (dataMin: number) => dataMin * 0.8,
              (dataMax: number) => dataMax * 1.2,
            ]}
            label={{
              value: "坪単価",
              position: "top",
              offset: 10,
            }}
          />

          {/* Second Y-Axis for `count` (right side) */}
          <YAxis
            yAxisId="right"
            orientation="right"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => value.toFixed(0)}
            domain={[
              (dataMin: number) => dataMin * 0.8,
              (dataMax: number) => dataMax * 1.2,
            ]}
            label={{
              value: "件数",
              position: "top",
              offset: 10,
            }}
          />

          <Tooltip />

          <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />

          {/* Line for `averageTsuboPrice` (left Y-axis) */}
          <Line
            yAxisId="left"
            dataKey="averageTsuboPrice"
            type="natural"
            stroke="hsl(var(--chart-1))"
            strokeWidth={2}
            dot={false}
          />

          {/* Bar for `count` (right Y-axis) */}
          <Bar
            yAxisId="right"
            dataKey="count"
            fill="hsl(var(--chart-2))"
            barSize={30}
            opacity={0.8}
          />
        </ComposedChart>
      </ChartContainer>

      <div className="flex flex-col text-sm gap-1 text-neutral-500 justify-between items-center">
        <div className="text-neutral-500 text-center flex flex-row items-center">
          過去{timeDiff}{unitMapper[unit as keyof typeof unitMapper]}の間に、平均坪単価は
          <span className={`flex flex-row items-center ${overallIncreasePercentage > 0 ? "text-green-500" : "text-red-500"}`}>
            {overallIncreasePercentage.toFixed(2)}%
          </span>
        </div>
        <div className="flex flex-row items-center">平均変動率は
          <span className={`flex flex-row items-center ${averageIncreasePercentage > 0 ? "text-green-500" : "text-red-500"}`}>
            {averageIncreasePercentage.toFixed(2)}%
            {averageIncreasePercentage > 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
          </span>
        </div>
        <div className="text-xs text-neutral-500">
          * 各変更記録は、各物件ごとの記録に代わって記録してます
        </div>
      </div>
    </div>
  );
}