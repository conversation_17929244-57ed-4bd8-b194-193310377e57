import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Noto_Sans_JP } from 'next/font/google';
import "./globals.css";
import React from 'react';
import { Toaster } from "@/components/ui/toaster"
import Header from '@/components/header'; // ヘッダーコンポーネントをインポート
import { NextIntlClientProvider } from "next-intl";
import { getMessages, getLocale } from "next-intl/server";
import { Analytics } from "@vercel/analytics/react"
import Script from "next/script";
import { PageLogger } from "./PageLogger";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

const inter = Inter({ subsets: ["latin"], weight: ['400', '700'], });
const noto = Noto_Sans_JP({ subsets: ["latin"], weight: ['400', '700'], });

export const metadata: Metadata = {
  metadataBase: new URL('https://www.urbalytics.jp'),
  title: {
    template: '%s | Urbalytics',
    default: 'Urbalytics - 日本不動産AI駆動市場情報プラットフォーム',
  },
  description: '日本最先端の不動産データプラットフォーム。AI駆動の市場分析、投資物件検索、収益性評価ツールで不動産投資を最適化。リアルタイム市場データと高精度な投資シミュレーションを提供。',
  keywords: '不動産投資, 日本不動産, AI分析, 市場データ, 投資物件, 収益性評価, 不動産テック, プロパティテック, Urbalytics',
  authors: [{ name: 'Urbalytics Team' }],
  creator: 'Urbalytics',
  publisher: 'Urbalytics',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'ja_JP',
    url: 'https://www.urbalytics.jp',
    siteName: 'Urbalytics',
    title: 'Urbalytics - 日本不動産AI駆動市場情報プラットフォーム',
    description: '日本最先端の不動産データプラットフォーム。AI駆動の市場分析で不動産投資を最適化。',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Urbalytics',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Urbalytics - 日本不動産AI駆動市場情報プラットフォーム',
    description: '日本最先端の不動産データプラットフォーム。AI駆動の市場分析で不動産投資を最適化。',
    images: ['/og-image.jpg'],
    creator: '@urbalytics',
  },
  alternates: {
    canonical: 'https://www.urbalytics.jp',
    languages: {
      'ja-JP': 'https://www.urbalytics.jp',
      'en-US': 'https://www.urbalytics.jp?lang=en',
      'zh-CN': 'https://www.urbalytics.jp?lang=zh',
    },
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon.ico",
    apple: "/icon-192x192.png",
  },
  manifest: "/manifest.json",
};

export const viewport = {
  initialScale: 1,
  width: "device-width",
  height: "device-height",
  userScalable: false,
  themeColor: "#000000",
};

interface RootLayoutProps {
  children: React.ReactNode;
}

if (process.env.NODE_ENV === "production") {
  console.log = () => { }; // Disable console.log
  // console.warn = () => {}; // Disable console.warn
  // console.error = () => {}; // Optional: Disable console.error
}

export default async function RootLayout({ children }: RootLayoutProps) {
  const messages = await getMessages();
  const locale = await getLocale();

  // const isLocalhost = process.env.NODE_ENV === "development";
  // console.log("🔥 in the layout, the locale is", locale);

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#000000" />
        {/* <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta property="og:title" content="Documentation Wiki" />
        <meta property="og:description" content="Complete documentation for the project" />
        <link rel="icon" href="/favicon.ico" /> */}

        {/* Enhanced Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@graph": [
                {
                  "@type": "Organization",
                  "@id": "https://www.urbalytics.jp/#organization",
                  name: "Urbalytics",
                  url: "https://www.urbalytics.jp",
                  logo: {
                    "@type": "ImageObject",
                    url: "https://www.urbalytics.jp/logo.png",
                    width: 512,
                    height: 512,
                  },
                  sameAs: [
                    "https://x.com/urbalytics",
                    "https://www.linkedin.com/company/urbalytics",
                  ],
                  contactPoint: {
                    "@type": "ContactPoint",
                    contactType: "customer service",
                    availableLanguage: ["Japanese", "English", "Chinese"],
                  },
                  description: "日本最先端の不動産データプラットフォーム。AI駆動の市場分析で不動産投資を最適化。",
                },
                {
                  "@type": "WebSite",
                  "@id": "https://www.urbalytics.jp/#website",
                  url: "https://www.urbalytics.jp",
                  name: "Urbalytics",
                  description: "日本最先端の不動産データプラットフォーム",
                  publisher: {
                    "@id": "https://www.urbalytics.jp/#organization",
                  },
                  potentialAction: [
                    {
                      "@type": "SearchAction",
                      target: {
                        "@type": "EntryPoint",
                        urlTemplate: "https://www.urbalytics.jp/ex/search?searchValue={search_term_string}",
                      },
                      "query-input": "required name=search_term_string",
                    },
                  ],
                },
              ],
            }),
          }}
        />
      </head>

      <body className={`${locale === 'ja' ? noto.className : inter.className} antialiased`}>
        <NextIntlClientProvider locale={locale} messages={messages}>
          <Header />
          <main className="grow">{children}</main>
        </NextIntlClientProvider>
        <Toaster />
        <Analytics />
        <PageLogger />
        {/* {!isLocalhost && (
          <Script
            src={`//code.tidio.co/kulayyxvkecvrnkbopvhggkyxny9i34x.js?language=${locale}`}
            strategy="afterInteractive"
          />
        )} */}
        {/* Has to be here, has error other wise */}


        {/* <div className="gtranslate_wrapper"></div>
      <script>window.gtranslateSettings = {"default_language":"ja","native_language_names":true,"languages":["ja","zh-CN"],"wrapper_selector":".gtranslate_wrapper","horizontal_position":"right","vertical_position":"bottom"}</script> */}
      </body>
    </html>
  );
}
