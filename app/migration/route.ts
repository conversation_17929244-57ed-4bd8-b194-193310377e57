import { NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";

import { TllCustomerNeedCriteriaType } from "@/lib/definitions";
import { TllUserLambdaRecordRecordType } from "@prisma/client";
import dayjs from "dayjs";

export async function GET(req: Request) {
  if (req.headers.get('Authorization') !== `Bearer ${process.env.API_WHITELIST_TOKEN}`) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    let totalProcessed = 0;
    let totalSkipped = 0;
    const batchSize = 100;

    while (true) {
      const matches = await prisma.proProject.findMany({
        where: {
          postalCode: {
            not: null,
          },
          prefectureCode: null,
        },
        take: batchSize,
      });

      if (matches.length === 0) {
        break;
      }

      console.log(`🔥 Processing batch of ${matches.length} records`);

      const batchPromises = matches.map(async (d) => {
        try {
          let matchPostalCode = await prisma.geoPostalCode.findFirst({
            where: {
              postalCode: d.postalCode?.toString() || "",
            },
          });

          console.log("updating project", d.id, "with postal code", d.postalCode);

          if (!matchPostalCode) {
            console.error(`❌ No postal code found for project ${d.id}`);
            totalSkipped++;
            return;
          }

          await prisma.proProject.update({
            where: { id: d.id },
            data: {
              prefectureCode: matchPostalCode.prefectureCode ? Number(matchPostalCode.prefectureCode) : null,
              areaCode: matchPostalCode.areaCode ? Number(matchPostalCode.areaCode) : null,
            },
          });
          totalProcessed++;
        } catch (err) {
          console.error(`❌ Failed to update project ${d.id}:`, err);
          totalSkipped++;
        }
      });

      await Promise.all(batchPromises);
      console.log(`🔥 Batch completed. Total processed: ${totalProcessed} vs total ${matches.length}, Total skipped: ${totalSkipped}`);
    }

    return NextResponse.json({
      message: "Migration completed!",
      stats: {
        totalProcessed,
        totalSkipped
      }
    });
  } catch (err: any) {
    logger.error("🔥 Migration error 🔥", err);
    return NextResponse.json({ message: "Migration error!", error: err.message });
  }
}