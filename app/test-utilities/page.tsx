export default function TestUtilities() {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-3xl font-bold">Tailwind 4 Utilities Test</h1>
      
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Scrollbar Hide Test</h2>
        <div className="w-64 h-32 border border-gray-300 overflow-auto scrollbar-hide">
          <div className="w-96 h-64 bg-gradient-to-r from-blue-500 to-purple-500 p-4 text-white">
            This content is larger than the container. 
            The scrollbar should be hidden but scrolling should still work.
            Try scrolling to see the rest of this content.
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Animation Test</h2>
        <div className="space-y-2">
          <div className="w-32 h-32 bg-blue-500 animate-bounce-slow rounded-lg flex items-center justify-center text-white font-bold">
            Bounce Slow
          </div>
          
          <details className="border border-gray-300 rounded-lg">
            <summary className="p-4 cursor-pointer bg-gray-100 hover:bg-gray-200">
              Click to test accordion animation
            </summary>
            <div className="p-4 animate-accordion-down">
              This content should animate down when opened and up when closed.
              The animation should be smooth and use the accordion keyframes.
            </div>
          </details>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Scrollbar Default Test</h2>
        <div className="w-64 h-32 border border-gray-300 overflow-auto scrollbar-default">
          <div className="w-96 h-64 bg-gradient-to-r from-green-500 to-blue-500 p-4 text-white">
            This content shows the default scrollbar.
            You should see the normal scrollbar in this container.
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
          </div>
        </div>
      </div>

      <div className="mt-8 p-4 bg-green-100 border border-green-300 rounded-lg">
        <h3 className="text-lg font-semibold text-green-800">Migration Status</h3>
        <p className="text-green-700">
          ✅ tailwindcss-animate plugin functionality migrated<br/>
          ✅ tailwind-scrollbar-hide plugin functionality migrated<br/>
          ✅ Custom animations working<br/>
          ✅ All utilities available in Tailwind 4
        </p>
      </div>
    </div>
  )
}
