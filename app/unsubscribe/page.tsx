"use client"


import { getUserByUnsubscribeToken } from "@/actions/tllUser"
import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { Separator } from "@/components/ui/separator"

export default function UnsubscribePage() {
  const searchParams = useSearchParams()
  const token = searchParams.get("token")

  const [isLoading, setIsLoading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [message, setMessage] = useState("")

  useEffect(() => {
    if (token) {
      getUserByUnsubscribeToken({ token }).then((res) => {
        if (res.success) {
          setIsSuccess(true)
          setMessage(res.message ?? "")
        } else {
          setIsSuccess(false)
          setMessage(res.message ?? "")
        }
      })
    }
  }, [token])

  return <div className="flex flex-col items-center justify-center h-screen p-4">
    <h1 className="text-2xl font-bold">
      メール通知を解除
    </h1>

    <Separator className="w-full my-4" />

    <div className="flex flex-col items-center justify-center">
      <p className={`text-base ${isSuccess ? "text-green-500" : "text-red-500"}`}>
        {isSuccess ? "✓" : "✗"}
        {isLoading ? "解除中..." : message}
      </p>
    </div>
  </div>
}