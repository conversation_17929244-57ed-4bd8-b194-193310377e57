"use client"

import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useState } from "react";
import { sendLark, LARK_URLS } from "@/lib/thirdParty/lark";
import Cta from "@/app/blog/Cta";
import FooterForNonLogin from "@/components/footerForNonLogin";
import { toast } from "@/hooks/use-toast";


export default function ContactUs() {
  const [userType, setUserType] = useState<string>("individual");
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const formData = new FormData(e.currentTarget);
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const phone = formData.get('phone') as string;
    const message = formData.get('message') as string;

    // 验证必填字段
    if (!name || !name.trim()) {
      toast({
        title: "エラー",
        description: "お名前を入力してください",
        variant: "destructive",
      });
      return;
    }

    if (!email || !email.trim()) {
      toast({
        title: "エラー",
        description: "メールアドレスを入力してください",
        variant: "destructive",
      });
      return;
    }

    if (!phone || !phone.trim()) {
      toast({
        title: "エラー",
        description: "電話番号を入力してください",
        variant: "destructive",
      });
      return;
    }

    if (!message || !message.trim()) {
      toast({
        title: "エラー",
        description: "詳細内容を入力してください",
        variant: "destructive",
      });
      return;
    }

    const howDidYouHear = formData.get('howDidYouHear');
    if (!howDidYouHear) {
      toast({
        title: "エラー",
        description: "当社を知ったきっかけを選択してください",
        variant: "destructive",
      });
      return;
    }

    const data = {
      name: name.trim(),
      userType: formData.get('userType'),
      preferredLanguage: formData.get('preferredLanguage'),
      email: email.trim(),
      phone: phone.trim(),
      company: formData.get('company'),
      position: formData.get('position'),
      businessType: formData.get('businessType'),
      purpose: formData.getAll('purpose'),
      message: message.trim(),
      howDidYouHear: howDidYouHear,
      submittedAt: new Date().toISOString()
    };

    console.log("🔥 Contact form submitted:", data);
    console.log("🔥 JSON data:", JSON.stringify(data, null, 2));

    try {
      await sendLark({
        message: `📧 新しいお問い合わせ\n\n${JSON.stringify(data, null, 2)}`,
        url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
      });
      console.log("🔥 Lark message sent successfully");

      toast({
        title: "送信完了",
        description: "お問い合わせを送信しました。ありがとうございます。",
      });

      setIsSubmitted(true);
      // e.currentTarget.reset();
      // setUserType("individual");
    } catch (error) {
      console.error("🔥 Error sending Lark message:", error);
      toast({
        title: "エラー",
        description: "送信に失敗しました。もう一度お試しください。",
        variant: "destructive",
        duration: 10000,
      });
    }
  };

  return <div className="pt-(--header-height) pb-(--footer-height)">
    <div className="mt-8 w-full max-w-[1200px] mx-auto  p-4">
      <div className="text-center text-neutral-500 tracking-widest mb-1 font-bold mt-8">
        INQUIRY
      </div>

      <h2 className="text-center text-3xl tracking-wide">
        問い合わせ
      </h2>

      <Separator className="my-8" />

      <section className="container mx-auto pb-12 pt-4 px-4">
        {isSubmitted ? (
          <div className="max-w-2xl mx-auto p-8 bg-green-50 border border-green-200 rounded-md text-center">
            <div className="text-green-600 text-6xl mb-4">✓</div>
            <h3 className="text-2xl font-semibold text-green-800 mb-4">
              お問い合わせありがとうございます
            </h3>
            <p className="text-green-700 mb-6">
              お問い合わせを受け付けました。担当者より3営業日以内にご連絡いたします。
            </p>
            <p className="text-sm text-green-600">
              Thank you for your inquiry. We will get back to you within 3 business days.
            </p>
            <Button
              onClick={() => setIsSubmitted(false)}
              className="mt-6"
              variant="outline"
            >
              新しいお問い合わせ / New Inquiry
            </Button>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="max-w-2xl mx-auto p-6 bg-white shadow-md rounded-md space-y-6">
            <div>
              <Label className="block mb-3">
                Name / お名前 <span className="text-red-500">*</span>
              </Label>
              <Input type="text" name="name" required />
            </div>

            <div>
              <Label className="block mb-3">User Type / ユーザー種別</Label>
              <Select name="userType" defaultValue="individual" onValueChange={setUserType}>
                <SelectTrigger>
                  <SelectValue placeholder="ユーザー種別を選択" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="individual">Individual / 個人</SelectItem>
                  <SelectItem value="corporate">Corporate / 法人</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="block mb-3">Preferred Language / ご希望の言語</Label>
              <Select name="preferredLanguage" defaultValue="ja">
                <SelectTrigger>
                  <SelectValue placeholder="ご希望の言語を選択" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="zh">Chinese / 中文</SelectItem>
                  <SelectItem value="ja">Japanese / 日本語</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="block mb-3">
                Email / メールアドレス <span className="text-red-500">*</span>
              </Label>
              <Input type="email" name="email" required />
            </div>

            <div>
              <Label className="block mb-3">
                Phone / 電話番号 <span className="text-red-500">*</span>
              </Label>
              <Input type="tel" name="phone" required />
            </div>

            {userType === "corporate" && (
              <>
                <div>
                  <Label className="block mb-3">Company Name / 会社名（法人のみ）</Label>
                  <Input type="text" name="company" />
                </div>

                <div>
                  <Label className="block mb-3">Position / 役職（法人のみ）</Label>
                  <Input type="text" name="position" />
                </div>

                <div>
                  <Label className="block mb-3">Business Type / 事業種別 （法人のみ）</Label>
                  <Select name="businessType">
                    <SelectTrigger>
                      <SelectValue placeholder="事業種別を選択" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="solutionShisankanri">資産管理会社</SelectItem>
                      <SelectItem value="solutionMinpaku">民泊運用者</SelectItem>
                      <SelectItem value="solutionKaitori">不動産買取業者</SelectItem>
                      <SelectItem value="solutionChuukai">不動産仲介業者</SelectItem>
                      <SelectItem value="solutionChintai">不動産賃貸管理業者</SelectItem>
                      <SelectItem value="other">その他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}

            <div>
              <Label className="block mb-3">
                Inquiry Purpose / ご相談内容（複数選択可）
              </Label>
              <div className="space-y-2">
                <label className="flex items-center gap-2">
                  <Checkbox name="purpose" value="urbalytics-usage" />
                  <span>Urbalyticsの活用相談</span>
                </label>

                <label className="flex items-center gap-2">
                  <Checkbox name="purpose" value="urbalytics-implementation" />
                  <span>Urbalyticsの導入相談</span>
                </label>

                <label className="flex items-center gap-2">
                  <Checkbox name="purpose" value="valuation" />
                  <span>物件査定</span>
                </label>

                <label className="flex items-center gap-2">
                  <Checkbox name="purpose" value="sell" />
                  <span>売却の相談</span>
                </label>

                <label className="flex items-center gap-2">
                  <Checkbox name="purpose" value="other" />
                  <span>その他</span>
                </label>

              </div>
            </div>

            <div>
              <Label className="block mb-3">
                Message / 詳細内容 <span className="text-red-500">*</span>
              </Label>
              <Textarea name="message" rows={5} required />
            </div>

            <div>
              <Label className="block mb-3">
                How did you hear about us? / 当社を知ったきっかけ <span className="text-red-500">*</span>
              </Label>
              <Select name="howDidYouHear" required>
                <SelectTrigger>
                  <SelectValue placeholder="選択してください" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="friends">Friends / 友人・知人からの紹介</SelectItem>
                  <SelectItem value="google">Google / 検索エンジン</SelectItem>
                  <SelectItem value="sns">SNS (Facebook, Twitter, Instagram, LinkedIn)</SelectItem>
                  <SelectItem value="online-ad">Online Advertisement / オンライン広告</SelectItem>
                  <SelectItem value="blog">Blog / ブログ・記事</SelectItem>
                  <SelectItem value="event">Event / イベント・セミナー</SelectItem>
                  <SelectItem value="other">Other / その他</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button type="submit" className="w-full">Submit / 送信</Button>
          </form>
        )}
      </section>
    </div>

    <Cta />

    <FooterForNonLogin />
  </div>
}