"use client"

import { Separator } from "@/components/ui/separator";
import { useTranslations, useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { sanityClient } from "@/lib/thirdParty/sanity/client";
import BlogCard from "../blog/BlogCard";
import { AnimatePresence } from "framer-motion";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import { getBlogs } from "@/actions/blog";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function Blog() {
  const t = useTranslations("Homepage");
  const currentLocale = useLocale(); // 🔥 Use next-intl's useLocale instead of useUIStore
  const [blogs, setBlogs] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [blogsLocalDb, setBlogsLocalDb] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchData = async () => {
    setIsLoading(true);
    const POSTS_QUERY = `*[
      _type == "post" &&
       !(_id in path("drafts.**")) &&
      defined(title_${currentLocale}) &&
      count(body_${currentLocale}) > 0
    ]|order(publishedAt desc)[0...3]{
      _id, title_${currentLocale}, slug_${currentLocale}, publishedAt, body_${currentLocale}, image,
      category->{_id, name_${currentLocale}},
      "author": coalesce(author->name, "Urbalytics Team")
    }`;

    const CATEGORIES_QUERY = `*[_type == "category"]{_id, name_ja, name_en, name_zh}`;

    const postsRes = await sanityClient.fetch(POSTS_QUERY);
    const categoriesRes = await sanityClient.fetch(CATEGORIES_QUERY);

    await setBlogs(postsRes);
    await setCategories(categoriesRes);
    setIsLoading(false);
  };

  useEffect(() => {
    fetchData();

    getBlogs({}).then((res) => {
      setBlogsLocalDb(res?.data);
    });
  }, [currentLocale]);


  return <section className="py-16 px-4 w-full max-w-[1200px] mx-auto text-center">
    <div className="text-center text-neutral-500 tracking-widest mb-1 font-bold mt-8">
      BLOG
    </div>

    <h2 className="text-2xl md:text-3xl mb-12">
      {t("blog")}
    </h2>

    <Separator className="my-8" />

    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-full">
          <Loader2 className="w-10 h-10 animate-spin" />
        </div>
      ) : (
        <AnimatePresence mode="popLayout">
          {blogs
            .slice(0, 3)
            .filter((post) => selectedCategory === "" || post?.category?._id === selectedCategory)
            .map((post) => (
              <motion.div
                key={post._id}
                layout // <== 关键：启用自动 layout 动画
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <BlogCard post={post} postLocalDb={blogsLocalDb.find((postLocalDb: any) => postLocalDb.id === post._id)} currentLocale={currentLocale} />
              </motion.div>
            ))}
        </AnimatePresence>
      )}
    </div>

    <Button variant="default" className="mt-8 px-8" size="lg">
      <Link href="/blog" className="text-sm">
        {t("blogAll")}
      </Link>
    </Button>
  </section >
}