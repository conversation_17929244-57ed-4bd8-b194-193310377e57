"use client"

import Cta from "@/app/blog/Cta"
import FooterForNonLogin from "@/components/footerForNonLogin"
import { Separator } from "@/components/ui/separator"
import { useTranslations } from "next-intl"
import Image from "next/image"
import { motion } from "framer-motion"

export default function AboutUrbalytics() {
  const t = useTranslations()

  return <div className="pt-(--header-height) pb-(--footer-height)">
    <div className="w-full max-w-[1200px] mx-auto p-4 min-h-[calc(100vh-var(--header-height))] flex flex-col gap-4 justify-center">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-8">
        <motion.div
          className="flex flex-col justify-center text-center lg:text-left"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.div
            className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight mb-4 lg:mb-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
          >
            {t("AboutUrbalytics.title1")}
          </motion.div>
          <motion.div
            className="text-base lg:text-lg text-neutral-600"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
          >
            {t("AboutUrbalytics.title2")}
          </motion.div>
        </motion.div>

        <motion.div
          className="flex items-center justify-center order-first lg:order-last"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 0.3, ease: "easeOut" }}
        >
          <Image src="/assets/about/top.svg" alt="Urbalytics" width={300} height={300} className="w-64 h-64 lg:w-[500px] lg:h-[500px]" />
        </motion.div>
      </div>
    </div>

    <Separator />

    <div className="w-full max-w-[1200px] mx-auto">
      <motion.div
        className="py-16 lg:py-[160px] px-4 lg:px-8 min-h-[400px] lg:min-h-[500px] flex flex-col gap-6 lg:gap-8 justify-center grid grid-cols-1 lg:grid-cols-2 items-center"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <motion.div
          className="flex flex-col gap-4 lg:gap-6 text-center lg:text-left"
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
        >
          <motion.div
            className="text-sm font-semibold text-blue-600 uppercase tracking-wide"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
          >
            {t("AboutUrbalytics.whoWeAre")}
          </motion.div>
          <motion.div
            className="text-2xl md:text-3xl lg:text-4xl font-bold"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          >
            {t("AboutUrbalytics.whoWeAreTitle")}
          </motion.div>
          <motion.div
            className="space-y-3 lg:space-y-4 text-sm lg:text-base text-neutral-600 leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
          >
            <p>
              {t("AboutUrbalytics.whoWeAreDescription1")}
            </p>
            <p>
              {t("AboutUrbalytics.whoWeAreDescription2")}
            </p>
          </motion.div>
        </motion.div>

        <motion.div
          className="flex items-center justify-center order-first lg:order-last"
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
        >
          <Image src="/assets/about/who-we-are.svg" alt="Urbalytics" width={300} height={300} className="w-48 h-48 lg:w-[400px] lg:h-[400px]" />
        </motion.div>
      </motion.div>

      <Separator />

      <motion.div
        className="py-16 lg:py-[160px] px-4 lg:px-8 min-h-[400px] lg:min-h-[500px] flex flex-col gap-6 lg:gap-8 justify-center grid grid-cols-1 lg:grid-cols-2 items-center"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <motion.div
          className="flex items-center justify-center order-first"
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
        >
          <Image src="/assets/about/start.svg" alt="Urbalytics" width={300} height={300} className="w-48 h-48 lg:w-[400px] lg:h-[400px]" />
        </motion.div>

        <motion.div
          className="flex flex-col gap-4 lg:gap-6 text-center lg:text-left"
          initial={{ opacity: 0, x: 30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
        >
          <motion.div
            className="text-sm font-semibold text-blue-600 uppercase tracking-wide"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
          >
            {t("AboutUrbalytics.ourBackstory")}
          </motion.div>
          <motion.div
            className="text-2xl md:text-3xl lg:text-4xl font-bold"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          >
            {t("AboutUrbalytics.ourBackstoryTitle")}
          </motion.div>
          <motion.div
            className="space-y-3 lg:space-y-4 text-sm lg:text-base text-neutral-600 leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
          >
            <p>
              {t("AboutUrbalytics.ourBackstoryDescription1")}
            </p>
            <p>
              {t("AboutUrbalytics.ourBackstoryDescription2")}
            </p>
          </motion.div>
        </motion.div>
      </motion.div>

      <Separator />

      <motion.div
        className="py-16 lg:py-[160px] px-4 lg:px-8 min-h-[400px] lg:min-h-[500px] flex flex-col gap-6 lg:gap-8 justify-center grid grid-cols-1 lg:grid-cols-2 items-center"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <motion.div
          className="flex flex-col gap-4 lg:gap-6 text-center lg:text-left"
          initial={{ opacity: 0, x: -30 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
        >
          <motion.div
            className="text-sm font-semibold text-blue-600 uppercase tracking-wide"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.1, ease: "easeOut" }}
          >
            {t("AboutUrbalytics.howItWorks")}
          </motion.div>
          <motion.div
            className="text-2xl md:text-3xl lg:text-4xl font-bold"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.2, ease: "easeOut" }}
          >
            {t("AboutUrbalytics.howItWorksTitle")}
          </motion.div>
          <motion.div
            className="space-y-3 lg:space-y-4 text-sm lg:text-base text-neutral-600 leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.6, delay: 0.3, ease: "easeOut" }}
          >
            <p>
              {t("AboutUrbalytics.howItWorksDescription1")}
            </p>
            <p>
              {t("AboutUrbalytics.howItWorksDescription2")}
            </p>
          </motion.div>
        </motion.div>

        <motion.div
          className="flex items-center justify-center order-first lg:order-last"
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
        >
          <Image src="/assets/about/how-it-works.svg" alt="Urbalytics" width={300} height={300} className="w-48 h-48 lg:w-[400px] lg:h-[400px]" />
        </motion.div>
      </motion.div>
    </div>

    <Cta />

    <FooterForNonLogin />
  </div>
} 