"use client"

import Image from "next/image";
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { useTranslations } from "next-intl";
import Autoplay from "embla-carousel-autoplay"

const featuredLogos = [
  { src: "/logos/mitsui.png", alt: "Mitsui" },
  { src: "/logos/nomura.png", alt: "Nomura" },
  { src: "/logos/mck.png", alt: "McKinsey" },
  { src: "/logos/bain.png", alt: "Bain" },
  { src: "/logos/ken.jpg", alt: "Ken" },
];

const moreLogos = [
  "amazon.png", "rakuten.png", "line.png", "renosy.png",
  "softbank.png", "kddi.png", "tokyou.png", "keio.png", "ntt.png", "orix.png", "tokyostar.png"
];

export default function TrustedBySection() {
  const t = useTranslations();

  return (
    <section className="py-16 px-4 w-full max-w-[1200px] mx-auto text-center">
      <h2 className="text-2xl md:text-3xl mb-12">
        {t("Homepage.trustedBy")}
      </h2>

      {/* Featured logos */}
      <div className="flex flex-wrap justify-center items-center gap-8 mb-12">
        {featuredLogos.map((logo, idx) => (
          <div
            key={idx}
            className="w-[140px] h-[60px] flex items-center justify-center"
          >
            <Image
              src={logo.src}
              alt={logo.alt}
              width={140}
              height={80}
              className="object-contain grayscale hover:grayscale-0 transition duration-300"
            />
          </div>
        ))}
      </div>

      {/* Carousel logos */}
      <Carousel className="w-full max-w-5xl mx-auto" plugins={[Autoplay({
        delay: 2000,
        stopOnMouseEnter: true,
      })]}>
        <CarouselContent className="flex gap-6">
          {moreLogos.map((src, idx) => (
            <CarouselItem key={idx} className="basis-1/3 sm:basis-1/5 md:basis-1/6">
              <div className="w-[120px] h-[60px] flex items-center justify-center mx-auto">
                <Image
                  src={`/logos/t2/${src}`}
                  alt={src.split(".")[0]}
                  width={100}
                  height={60}
                  className="object-contain grayscale hover:grayscale-0 transition duration-300"
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>
    </section>
  );
}
