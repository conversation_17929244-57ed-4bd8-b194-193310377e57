"use client";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import { animate, motion, useAnimation, useTransform, useMotionValue } from "motion/react";
import { useRef, useState, useEffect } from "react";
import { getMetricsForDB } from "@/actions/systemMetrics";
import { LOADIPHLPAPI } from "dns";
import { Loader2 } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export function AnimatedNumber({ target }: { target: number }) {
  const startValue = target * 0.98;
  const count = useMotionValue(startValue);
  const [currentTarget, setCurrentTarget] = useState(target);
  const maxTarget = target + 30;

  const formatted = useTransform(count, (latest) =>
    Math.floor(latest).toLocaleString()
  );

  const ref = useRef<HTMLDivElement>(null);
  const [inView, setInView] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !inView) {
          setInView(true);
          animate(count, currentTarget, {
            duration: 1.5,
            ease: "easeOut",
          });
        }
      },
      { threshold: 0.6 }
    );

    if (ref.current) observer.observe(ref.current);
    return () => observer.disconnect();
  }, [count, currentTarget, inView]);


  useEffect(() => {
    if (!inView) return;

    const interval = setInterval(() => {
      setCurrentTarget((prev) => {
        const next = Math.min(prev + Math.floor(Math.random() * 3 + 1), maxTarget);
        animate(count, next, {
          duration: 1.2,
          ease: "easeOut",
        });
        return next;
      });
    }, 3000); // 每 5 秒微涨一次

    return () => clearInterval(interval);
  }, [inView, count, maxTarget]);

  return (
    <motion.div ref={ref} className="text-4xl sm:text-5xl text-center mb-4">
      <motion.span>{formatted}</motion.span>
    </motion.div>
  );
}

export default function Stats() {
  const [metrics, setMetrics] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const t = useTranslations("Homepage");
  const controls = useAnimation();

  useEffect(() => {
    setIsLoading(true);
    getMetricsForDB().then((res) => {
      setMetrics(res.data);
      setIsLoading(false);
    });
  }, []);

  return (
    <div className="w-full mx-auto max-w-[1200px] px-4">
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center vertical-center w-full">
        <div className="border-b sm:border-b-0 sm:border-r border-neutral-200 text-center p-4">
          {isLoading ? <div className="flex justify-center items-center h-full">
            <Skeleton className="h-4 pb-4 w-[250px]" />
          </div> : metrics?.recordsMetrics ? <AnimatedNumber target={metrics?.recordsMetrics} /> : <div></div>}
          <div className="text-xl sm:text-2xl">
            {t("stats.propertiesTracked")}
          </div>
        </div>

        <div className="border-b sm:border-b-0 sm:border-r border-neutral-200 text-center p-4">
          {isLoading ? <div className="flex justify-center items-center h-full">
            <Skeleton className="h-4 pb-4 w-[250px]" />
          </div> : metrics?.changeMetrics ? <AnimatedNumber target={metrics?.changeMetrics} /> : <div></div>}
          <div className="text-xl sm:text-2xl">
            {t("stats.pastRecordLogged")}
          </div>
        </div>

        <div className="text-center p-4">
          {isLoading ? <div className="flex justify-center items-center h-full">
            <Skeleton className="h-4 pb-4 w-[250px]" />
          </div> : metrics?.valuationMetrics ? <AnimatedNumber target={metrics?.valuationMetrics} /> : <div></div>}
          <div className="text-xl sm:text-2xl">
            {t("stats.valuationConducted")}
          </div>
        </div>
      </div>
    </div>
  );
}