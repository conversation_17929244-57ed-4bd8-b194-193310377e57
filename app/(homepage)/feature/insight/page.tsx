"use client"

import FeatureTemplatePage from "../FeatureTemplate";
import { useTranslations } from "next-intl";

export default function InsightPage() {
  const t = useTranslations();

  const title = t("Header.menuFeatureMarketInsight");
  const description = t("Header.menuFeatureMarketInsightDetails");
  const heroSectionTitle = "エリア全体の価格・ROI・開発動向をマクロ視点で一目で把握";
  const heroSectionDescriptions = [
    "地図を動かすだけで、エリアごとの平均価格・利回り・投資指標を即時表示",
    "新築・再開発プロジェクトや売買事例の分布を地図とリストで可視化",
    "用途・規模・竣工年など多彩な条件で柔軟に絞り込み・比較が可能",
    "マクロな市場動向から投資判断まで、直感的にインサイトを得られる"
  ];

  const heroSectionImage = "/assets/feature/insight/insight.svg";
  const sections = [
    {
      label: "建設計画マップ",
      anchor: "development-map",
      photo: "/assets/feature/insight/1.jpg",
      title: "エリア内の新築・再開発プロジェクトを地図とリストで一目で把握",
      salesPoint: [
        "地図上に建設プロジェクトをピン表示、位置関係が直感的に分かる",
        "リストで詳細情報（名称・用途・規模・完了予定日など）を確認",
        "用途・規模・竣工年などで柔軟に絞り込み・検索が可能",
        "エリアの開発動向や将来価値の変化を素早く把握"
      ]
    },
    {
      label: "エリア相場マップ",
      anchor: "area-map",
      photo: "/assets/feature/insight/2.jpg",
      title: "地図を動かして、エリアごとの相場や投資指標を直感的に把握",
      salesPoint: [
        "地図上でエリアごとの平均価格・ROI・利回りなどを可視化",
        "地図を移動・拡大縮小するだけで、任意の地域のインサイトを即時取得",
        "エリアごとに売買事例や投資物件の分布を一目で確認",
        "物件タイプや価格帯などで柔軟に絞り込み・比較が可能"
      ]
    },
  ];

  return <FeatureTemplatePage
    title={title}
    description={description}
    heroSectionTitle={heroSectionTitle}
    heroSectionDescriptions={heroSectionDescriptions}
    heroSectionImage={heroSectionImage}
    sections={sections}
  />
}