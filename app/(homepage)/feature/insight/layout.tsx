import type { Metadata } from "next";
import { generateSEOMetadata, generateServiceSchema } from "@/lib/seo/utils";

export const metadata: Metadata = generateSEOMetadata({
  title: 'マーケットインサイト - 不動産市場の包括的分析ツール | Urbalytics',
  description: 'エリア全体の価格・ROI・開発動向をマクロ視点で把握。地図ベースの直感的インターフェースで市場トレンドを可視化し、投資判断をサポート。リアルタイム市場データと予測分析を提供。',
  keywords: 'マーケットインサイト, 市場分析, 不動産市場, ROI分析, 開発動向, 市場トレンド, 投資判断, 地図分析',
  url: 'https://www.urbalytics.jp/feature/insight',
  type: 'website',
});

export default function InsightLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const serviceSchema = generateServiceSchema({
    name: "マーケットインサイト",
    description: "エリア全体の価格・ROI・開発動向をマクロ視点で把握。地図ベースの直感的インターフェースで市場トレンドを可視化し、投資判断をサポート。",
    url: "https://www.urbalytics.jp/feature/insight",
    serviceType: "Real Estate Market Analysis",
    areaServed: "Japan",
    provider: "Urbalytics"
  });

  return (
    <>
      {/* サービス構造化データ */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema)
        }}
      />

      {children}
    </>
  );
}
