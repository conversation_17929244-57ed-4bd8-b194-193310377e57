import type { Metadata } from "next";
import { generateSEOMetadata } from "@/lib/seo/utils";

export const metadata: Metadata = generateSEOMetadata({
  title: '不動産投資機能 - AI駆動の市場分析ツール | Urbalytics',
  description: '不動産投資に必要な全ての機能を一つのプラットフォームで。物件検索、収益性評価、市場分析、投資シミュレーションなど、AI技術を活用した高度な分析ツールを提供します。',
  keywords: '不動産投資機能, 市場分析, 収益性評価, 投資シミュレーション, AI分析, 不動産テック, プロパティテック',
  url: 'https://www.urbalytics.jp/feature',
  type: 'website',
});

export default function FeatureLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
