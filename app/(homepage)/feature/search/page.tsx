"use client";

import FooterForNonLogin from "@/components/footerForNonLogin";
import { Button } from "@/components/ui/button";
import { CheckCircle2 } from "lucide-react";
import { useTranslations } from "next-intl"
import Image from "next/image";
import { HomeIcon, SearchIcon, MapIcon, Building2Icon, UserIcon } from "lucide-react";
import TrustBy from "../../TrustBy";
import Link from "next/link";
import { useState } from "react";
import FeatureTemplatePage from "../FeatureTemplate";

export default function SearchPage() {
  const t = useTranslations();
  const [openImage, setOpenImage] = useState<string | null>(null);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const sections = [
    {
      label: "検索",
      icon: HomeIcon,
      sectionId: "id1",
      anchor: "search",
      photo: "/assets/feature/search/1.png",
      title: "理想の物件を素早く検索、あなたに最適な住まいを発見",
      salesPoint: [
        "多彩な条件で物件を絞り込み可能",
        "地図とリストの両方で直感的に物件を閲覧",
        "最新の物件情報をリアルタイムで取得",
        "お気に入り登録や比較機能で検討がスムーズ"
      ]
    },
    {
      label: "変更履歴",
      icon: MapIcon,
      anchor: "history",
      photo: "/assets/feature/search/3.png",
      title: "物件の変更履歴を可視化、リスクや価値変動を把握",
      salesPoint: [
        "所有者や用途変更などの履歴を時系列で表示",
        "タイムライン形式で直感的に把握",
        "リスク要因や価値変動の兆候を早期発見",
        "信頼できる公的データを使用"
      ]
    },
    {
      label: "近隣売買履歴",
      icon: Building2Icon,
      anchor: "nearby-history",
      photo: "/assets/feature/search/4.png",
      title: "周辺の売買履歴を公開、相場やトレンドを把握",
      salesPoint: [
        "近隣エリアの過去取引事例を一覧表示",
        "価格・面積・成約日など詳細情報も充実",
        "条件で絞り込みや並び替えが可能",
        "価格交渉や投資判断の参考に最適"
      ]
    },
    {
      label: "賃料査定",
      icon: UserIcon,
      anchor: "rent",
      photo: "/assets/feature/search/5.png",
      title: "AI賃料査定で、適正な家賃をスピーディーに算出",
      salesPoint: [
        "最新の市場データとAIで賃料を自動計算",
        "類似物件との比較で相場感が分かる",
        "エリアや物件タイプごとに柔軟に対応",
        "賃料推移グラフで将来予測も可能"
      ]
    },
    {
      label: "CF試算",
      icon: UserIcon,
      anchor: "cf-calculation",
      photo: "/assets/feature/search/6.png",
      title: "キャッシュフローシミュレーションで投資収益を見える化",
      salesPoint: [
        "購入費用・ローン・賃料などを入力するだけで自動計算",
        "複数シナリオでリスクとリターンを比較",
        "グラフで収支推移を分かりやすく表示",
        "レポート出力で投資計画の資料作成も簡単"
      ]
    },
    {
      label: "近隣開発",
      icon: UserIcon,
      anchor: "nearby-development",
      photo: "/assets/feature/search/7.jpg",
      title: "周辺開発情報をリアルタイムで把握、将来価値を先取り",
      salesPoint: [
        "エリア内の新築・再開発プロジェクトを一覧表示",
        "地図上で開発状況をビジュアル化",
        "将来の資産価値向上のチャンスを逃さない",
        "開発タイプや時期などで柔軟に絞り込み可能"
      ]
    },

  ]

  const heroSectionTitle = "全ての不動産情報を一元管理。プロのような判断が誰でもできる";
  const heroSectionDescriptions = [
    "すでに<b> 3,000+ </b>の個人投資家が活用中",
    "物件検索から査定・履歴・開発情報までワンストップで提供",
    "地図・グラフ・履歴など多彩な可視化で直感的に分析",
    "開発・売買・賃貸など多角的な情報で最適な意思決定を支援"
  ];

  const heroSectionImage = "/assets/feature/search/search.svg";
  const title = t("Header.menuFeatureSearch");
  const description = t("Header.menuFeatureSearchDetails");

  return <FeatureTemplatePage
    title={title}
    description={description}
    heroSectionTitle={heroSectionTitle}
    heroSectionDescriptions={heroSectionDescriptions}
    heroSectionImage={heroSectionImage}
    sections={sections}
  />
}