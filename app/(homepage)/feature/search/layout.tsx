import type { Metadata } from "next";
import { generateSEOMetadata } from "@/lib/seo/utils";

export const metadata: Metadata = generateSEOMetadata({
  title: '物件検索 - AI駆動の投資物件発見ツール | Urbalytics',
  description: '高度な検索フィルターとAI分析で理想の投資物件を発見。収益性、立地条件、市場動向を総合的に評価し、最適な不動産投資機会を提案します。',
  keywords: '物件検索, 投資物件, AI検索, 不動産発見, 収益性分析, 立地評価, 投資機会',
  url: 'https://www.urbalytics.jp/feature/search',
  type: 'website',
});

export default function SearchFeatureLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
