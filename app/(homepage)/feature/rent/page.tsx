"use client";

import { useTranslations } from "next-intl"
import FeatureTemplatePage from "../FeatureTemplate";

export default function RentPage() {
  const t = useTranslations();
  const sections = [
    {
      label: "駅・住所から検索",
      anchor: "search-bar",
      photo: "/assets/feature/rent/1.png",
      title: "駅名や住所で素早く物件・エリアを検索",
      salesPoint: [
        "駅・住所・郵便番号で直感的に検索",
        "候補が自動サジェストされ入力が簡単",
        "複数の検索軸で柔軟に絞り込み",
        "エリアごとの賃料相場もすぐに確認"
      ]
    },
    {
      label: "賃料査定シミュレーション",
      anchor: "simulator",
      photo: "/assets/feature/rent/2.png",
      title: "入力するだけで、最適な賃料相場を即時算出",
      salesPoint: [
        "築年数・面積・現在賃料などを簡単入力",
        "AIが最新データから賃料レンジを自動計算",
        "坪単価や割安・割高も一目で分かる",
        "現賃料との比較で改善ポイントを発見"
      ]
    },
    {
      label: "エリア賃料相場・推移",
      anchor: "area-stats",
      photo: "/assets/feature/rent/3.png",
      title: "エリアごとの賃料相場と変動をグラフで可視化",
      salesPoint: [
        "平均賃料・面積・坪単価を自動集計",
        "直近の賃料推移や変動率をグラフ表示",
        "件数や期間ごとに柔軟に分析",
        "エリアごとのトレンドを素早く把握"
      ]
    },
    {
      label: "賃貸事例一覧・比較",
      anchor: "case-list",
      photo: "/assets/feature/rent/4.png",
      title: "最新の賃貸事例を一覧・比較して相場感を把握",
      salesPoint: [
        "エリア・物件タイプごとに事例を一覧表示",
        "賃料・面積・築年数など詳細情報も充実",
        "新着順や条件で並び替え・絞り込み可能",
        "複数事例を比較して最適な賃料設定をサポート"
      ]
    }
  ];

  const heroSectionTitle = "全ての不動産情報を一元管理。プロのような判断が誰でもできる";
  const heroSectionDescriptions = [
    "すでに<b> 3,000+ </b>の個人投資家が活用中",
    "物件検索から査定・履歴・開発情報までワンストップで提供",
    "地図・グラフ・履歴など多彩な可視化で直感的に分析",
    "開発・売買・賃貸など多角的な情報で最適な意思決定を支援"
  ];

  const heroSectionImage = "/assets/feature/rent/rent.svg";
  const title = t("Header.menuFeatureRent");
  const description = t("Header.menuFeatureRentDetails");

  return <FeatureTemplatePage
    title={title}
    description={description}
    heroSectionTitle={heroSectionTitle}
    heroSectionDescriptions={heroSectionDescriptions}
    heroSectionImage={heroSectionImage}
    sections={sections}
  />
}