import type { Metadata } from "next";
import { generateSEOMetadata, generateServiceSchema } from "@/lib/seo/utils";

export const metadata: Metadata = generateSEOMetadata({
  title: '賃貸収益分析 - 賃貸物件の収益性評価ツール | Urbalytics',
  description: '賃貸物件の収益性を詳細に分析。家賃相場、空室率、運営コストを考慮した正確なキャッシュフロー予測で、賃貸投資の成功をサポートします。',
  keywords: '賃貸収益分析, 家賃相場, 空室率, キャッシュフロー, 賃貸投資, 収益性評価, 運営コスト',
  url: 'https://www.urbalytics.jp/feature/rent',
  type: 'website',
});

export default function RentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const serviceSchema = generateServiceSchema({
    name: "賃貸収益分析",
    description: "賃貸物件の収益性を詳細に分析。家賃相場、空室率、運営コストを考慮した正確なキャッシュフロー予測で、賃貸投資の成功をサポート。",
    url: "https://www.urbalytics.jp/feature/rent",
    serviceType: "Rental Property Analysis",
    areaServed: "Japan",
    provider: "Urbalytics"
  });

  return (
    <>
      {/* サービス構造化データ */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema)
        }}
      />

      {children}
    </>
  );
}
