"use client"

import { useTranslations } from "next-intl";
import { useState } from "react";
import FeatureTemplatePage from "../FeatureTemplate";

export default function ValuationPage() {
  const t = useTranslations();
  const [openImage, setOpenImage] = useState<string | null>(null);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const sections = [
    {
      label: "駅・住所から検索",
      anchor: "search-bar",
      photo: "/assets/feature/valuation/1.png",
      title: "駅名や住所で物件を素早く検索、査定対象を簡単選択",
      salesPoint: [
        "駅名・住所・郵便番号で物件候補を瞬時に検索",
        "サジェスト機能で入力がスムーズ",
        "物件タイプやエリアで絞り込み可能",
        "査定したい物件をワンクリックで選択"
      ]
    },
    {
      label: "既存データと照合",
      anchor: "auto-valuation",
      photo: "/assets/feature/valuation/2.png",
      title: "過去の取引データと照合し、より正確な査定価格を算出",
      salesPoint: [
        "価格・面積・築年数・最寄駅などの基本情報を入力",
        "AIが類似物件を自動検索し、過去の取引履歴を表示",
        "同一物件の過去の取引履歴や価格推移を確認可能",
        "周辺エリアの類似物件の取引実績と比較分析",
        "市場動向に基づいた客観的な査定価格を算出"
      ]
    },
    {
      label: "PDF・写真から査定",
      anchor: "area-stats",
      photo: "/assets/feature/valuation/3.jpg",
      title: "PDFや写真をアップロードするだけで、AIが自動で物件情報を読み取り査定",
      salesPoint: [
        "PDFや写真をアップロードするだけで自動入力",
        "AIが物件情報を自動で抽出・解析",
        "取得したデータをそのまま査定フォームに反映",
        "手間なく正確な査定が可能"
      ]
    },
    {
      label: "URLから査定",
      anchor: "auto-url-valuation",
      photo: "/assets/feature/valuation/4.png",
      title: "物件サイトのURLを貼るだけで、AIが自動で情報を読み取り査定",
      salesPoint: [
        "楽待・SUUMO・アットホームなど主要サイトに対応",
        "URLを貼るだけで物件情報を自動抽出",
        "AIが物件データを解析し、正確な査定を実施",
        "手入力の手間を大幅に削減、スピーディーな査定が可能"
      ]
    },
    {
      label: "多面な査定",
      anchor: "valuation",
      photo: "/assets/feature/valuation/5.png",
      title: "異なる用途での査定で、正確な不動産価値を即時算出",
      salesPoint: [
        "ビッグデータとAIで市場価格を自動算出",
        "マンション・戸建て・土地など幅広く対応",
        "過去の取引事例や周辺相場も一目で確認",
        "査定レポートをPDFで出力可能",
        "投資・自住・賃貸など用途別の価値評価で機会損失を防止"
      ]
    },
  ];

  const heroSectionTitle = "AIによる自動査定で、正確な不動産価値を即時算出";
  const heroSectionDescriptions = [
    "手入力・写真・PDF・URLなど多彩な入力方法に対応",
    "ビッグデータとAIで市場価格を自動算出",
    "マンション・戸建て・土地など幅広く対応",
    "異なる用途での価値も分析・比較が可能"
  ];

  const heroSectionImage = "/assets/feature/valuation/valuation.svg";
  const title = t("Header.menuFeatureValuation");
  const description = t("Header.menuFeatureValuationDetails");

  return <FeatureTemplatePage
    title={title}
    description={description}
    heroSectionTitle={heroSectionTitle}
    heroSectionDescriptions={heroSectionDescriptions}
    heroSectionImage={heroSectionImage}
    sections={sections}
  />
}