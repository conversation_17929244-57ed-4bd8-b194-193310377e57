import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion"; // 假设你有一个Accordion组件
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";

export default function PricingFaq() {
  const t = useTranslations("Homepage");

  return (
    <div className="w-full mx-auto max-w-[1200px] px-4">

      {/* <div className="text-center text-neutral-500 tracking-widest mb-1 font-bold mt-8">
        PRICE
      </div> */}
      <div className="text-center text-neutral-500 tracking-widest mb-1 font-bold mt-8">
        FAQ
      </div>
      <h2 className="text-center text-3xl tracking-wide">
        {t("faq.title")}
      </h2>
      <Separator className="my-8" />

      <div className="w-full max-w-[1200px] mx-auto px-4">
        <Accordion type="single" collapsible defaultValue="item-0">
          {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((faq: any, index: number) => (
            <AccordionItem key={index} value={`item-${index}`} className="border-b border-neutral-400 text-black mb-1">
              <AccordionTrigger className="p-4 text-base sm:text-xl font-semibold">{t(`faq.question${index + 1}.question`)}</AccordionTrigger>
              <AccordionContent className="px-4  text-base sm:text-xl text-black">{t(`faq.question${index + 1}.answer`)}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  );
}