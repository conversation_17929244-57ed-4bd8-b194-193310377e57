"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowUpRight, Check, Loader2 } from "lucide-react";
import { use, useRef, useState } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { PricingTier } from "@/lib/constants/pricingTier";
import Link from "next/link";
import { motion } from "motion/react";
import { useAuthStore } from "@/store/auth";
import { getUserByEmail } from "@/actions/tllUser";
import { useEffect } from "react";
import { TllUserProps, TllUserSubscriptionPlan, TllUserSubscriptionStatus } from "@/lib/definitions/tllUser";
import { createCheckoutSession, validateSession } from "@/actions/tllUserSubscription";
import { toast, useToast } from "@/hooks/use-toast";
import PricingDetails from "./PricingDetails";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { sendLark, LARK_URLS } from "@/lib/thirdParty/lark";

export default function Pricing() {
  const { currentUser, setCurrentUser, logout } = useAuthStore();
  const [currentPlanUnit, setCurrentPlanUnit] = useState<"MONTHLY" | "YEARLY">("YEARLY");
  const [user, setUser] = useState<TllUserProps | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const params = useSearchParams();
  const sessionId = params.get("session_id")
  const t = useTranslations("pricing");

  useEffect(() => {
    if (currentUser?.email) {
      getUserByEmail({ email: currentUser?.email as string }).then((res) => {
        setUser(res.data);
      });
    }
  }, [currentUser]);

  // To makae sure validateSession is onyl run once
  const hasValidatedRef = useRef(false); // ✅ 初始为 false

  useEffect(() => {
    if (sessionId && currentUser && !hasValidatedRef.current) {
      hasValidatedRef.current = true; // ✅ 马上标记为已执行，防止重复触发

      validateSession({ sessionId: sessionId as string }).then((res) => {
        if (res.success) {
          toast({
            title: "サブスクリプションが更新されました、再度ログインしてください",
            description: res.message,
            duration: 10000,
          });

          fetch('/api/auth/logout', { method: 'POST' });
          logout();
          router.push('/');

          // window.history.replaceState(null, "", window.location.pathname);
          // window.location.reload();
        } else {
          toast({
            title: "エラーが発生しました",
            description: res.message,
            variant: "destructive",
            duration: 10000,
          });
        }

        router.replace("/my/billing");
      })
    }
  }, [sessionId, currentUser]);

  const pricingPlans = [
    {
      dbTitle: "FREE",
      title: "Free",
      subtitle: t("freeSubtitle"),
      price: PricingTier[1].monthlyPrice,
      monthlyPrice: PricingTier[1].monthlyPrice,
      yearlyPrice: PricingTier[1].yearlyPrice,
      features: [
        `${t("featureDetailsPage")}：${PricingTier[1].dailySearchCount}${t("times")}/${t("day")}`,
        `${t("featureRentSearch")}：${PricingTier[1].dailyRentSearchCount}${t("times")}/${t("day")}`,
        `${t("featurePriceChangeHistory")}：${PricingTier[1].dailyPriceChangeHistoryCount}${t("times")}/${t("day")}`,
        `${t("featureAnalysis")}`,
        `${t("featureValuation")}`,
        `${t("featureCFSimulation")}`,
        `${t("featureConstructionMap")}`,
        `${t("featureMinpaku")}`
      ],
      buttonText: t("freeButtonText"),
    },
    {
      dbTitle: "PLUS",
      title: "Plus",
      subtitle: t("plusSubtitle"),
      price: PricingTier[10].monthlyPrice,
      monthlyPrice: PricingTier[10].monthlyPrice,
      yearlyPrice: PricingTier[10].yearlyPrice,
      features: [
        `${t("featureDetailsPage")}：${PricingTier[10].dailySearchCount}${t("times")}/${t("day")}`,
        `${t("featureRentSearch")}：${t("unlimited")}`,
        `${t("featurePriceChangeHistory")}：${t("unlimited")}`,
        `${t("featureFree")}`,
        `${t("featureNearbySeiyaku")}`,
        `${t("featurePlus1")}`,
        `${t("featurePlus4")}`,
        `${t("featureCapRateChecker")}`,
      ],
      buttonText: t("plusButtonText"),
    },
    {
      dbTitle: "PRO",
      title: "Pro",
      subtitle: t("proSubtitle"),
      price: PricingTier[20].monthlyPrice,
      monthlyPrice: PricingTier[20].monthlyPrice,
      yearlyPrice: PricingTier[20].yearlyPrice,
      features: [
        `${t("featureDetailsPage")}：${PricingTier[20].dailySearchCount}${t("times")}/${t("day")}`,
        `${t("featurePlus")}`,
        `${t("featureCustomExtractionConditions")}`,
        `${t("featurePropertyRegistrationLedger")}`,
        `${t("featureIndividualSupport")}`,
      ],
      buttonText: t("proButtonText"),
    },
  ];

  const checkIsCurrentPlan = (plan: any) => {
    if (user?.subscriptionPlan === null && plan.dbTitle === "FREE") {
      return true;
    } else {
      return user?.subscriptionPlan === plan.dbTitle;
    }
  }

  const checkout = async (plan: any) => {
    if (!currentUser) {
      router.push(`/login?tab=login&callbackUrl=/pricing`);
      return;
    }

    if (plan.dbTitle === "FREE") {
      router.push("/login");
      return;
    }

    setIsLoading(true);
    let compositeTitle = `${plan.dbTitle}_${currentPlanUnit}` as TllUserSubscriptionPlan;
    console.log("compositeTitle", compositeTitle);

    let res = await createCheckoutSession(compositeTitle as any);

    setIsLoading(false);
    if (res.success) {
      window.location.href = res.data as string;
    } else {
      setIsLoading(false);
      toast({
        title: "エラーが発生しました",
        description: res.message,
        variant: "destructive",
      });
    }
  }

  return <div className="mt-8 w-full max-w-[1200px] mx-auto">
    <div className="text-center text-neutral-500 tracking-widest mb-1 font-bold mt-8">
      PRICE
    </div>

    <h2 className="text-center text-3xl tracking-wide">
      {t("title")}
    </h2>

    <Separator className="my-8" />

    <section className="container mx-auto pb-16 pt-8 px-4">
      <div className="flex justify-center mt-0 mb-12">
        <ToggleGroup
          type="single"
          value={currentPlanUnit}
          onValueChange={(val) => {
            if (val) {
              setCurrentPlanUnit(val as "MONTHLY" | "YEARLY");
            }
          }}
          className="rounded-lg bg-black p-1 transition-all duration-300"
        >
          <ToggleGroupItem
            value="MONTHLY"
            className={`flex-1 px-6 py-2 text-sm rounded-md transition-all duration-300 ${currentPlanUnit === "MONTHLY" ? "bg-white text-black" : "text-gray-400"
              }`}
          >
            {t("monthly")}
          </ToggleGroupItem>
          <ToggleGroupItem
            value="YEARLY"
            className={`px-4 py-2 text-sm rounded-md transition-all duration-300 ${currentPlanUnit === "YEARLY" ? "bg-white text-black" : "text-gray-400"
              }`}
          >
            {t("yearly")}
            <span className="text-sm text-green-500 ml-[-10px] font-bold">
              （{((PricingTier[10].monthlyPrice * 12 - PricingTier[10].yearlyPrice) / PricingTier[10].monthlyPrice / 12 * 100).toFixed(0)}% {t("discount")}）
            </span>
          </ToggleGroupItem>
        </ToggleGroup>
      </div>

      <div className="grid md:grid-cols-3 gap-12 mt-8">
        {pricingPlans.map((plan, index) => (
          <Card key={index} className={`${
            plan.dbTitle === "PLUS"
              ? "bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-300 shadow-lg scale-105 relative"
              : checkIsCurrentPlan(plan)
                ? "bg-neutral-50 border border-neutral-400"
                : "bg-white border border-neutral-200"
          } text-black flex flex-col`}>
            {/* Best Value Badge for Plus */}
            {plan.dbTitle === "PLUS" && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-4 py-1 rounded-full text-sm font-bold shadow-lg">
                {t("bestValue")}
              </div>
            )}
            <CardHeader>
              <CardTitle className="text-2xl font-bold">{plan.title}</CardTitle>
              <p className="text-sm text-gray-500">{plan.subtitle}</p>
            </CardHeader>
            <CardContent className="grow">
              <div className="flex flex-col gap-1">
                <h2 className="text-4xl font-bold">
                  {currentPlanUnit === "YEARLY" ? plan.yearlyPrice > 0 ? `¥${((Math.ceil(plan.yearlyPrice / 12)).toLocaleString())}` : t("free") : plan.price > 0 ? `¥${plan.price.toLocaleString()}` : t("free")}

                  <span className="text-lg ml-1">
                    {/* {currentPlanUnit === "YEARLY" ? <span className="text-gray-500">/{t("year")}</span> : */}

                    <span className="">/{t("month")}</span>
                  </span>
                </h2>

                <div className="text-sm text-gray-600  h-4">
                  {currentPlanUnit === "YEARLY" && plan.yearlyPrice > 0 ? `${t("or")} ¥${((Math.ceil(plan.monthlyPrice)).toLocaleString())}/${t("month")} (${t("whenMonthly")})` : null}

                  {/* （{((PricingTier[10].monthlyPrice * 12 - PricingTier[10].yearlyPrice) / PricingTier[10].monthlyPrice / 12 * 100).toFixed(0)}% 割引） */}
                </div>
              </div>

              <ul className="mt-4 space-y-2 text-sm">
                {plan.features.map((feature, i) => (
                  <li key={i} className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-green-400" />
                    {feature}
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              {checkIsCurrentPlan(plan) ? <Button variant="outline" className="w-full cursor-not-allowed">
                {t("currentPlan")}
              </Button> : <Button variant="default" className="w-full" onClick={() => {
                sendLark({
                  message: `[${currentUser?.email}]が[${plan.dbTitle}][${currentPlanUnit}] サブスクリプションクリック。`,
                  url: LARK_URLS.ADMIN_ACTIVITY_CHANNEL,
                })
                checkout(plan)
              }} disabled={(currentUser && currentUser?.accessLevel && currentUser?.accessLevel >= 10 && plan.dbTitle === "FREE") || isLoading}>
                {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : plan.buttonText}
              </Button>}
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="text-black pt-16 text-center flex flex-col gap-2">
        <div className="text-2xl">
          {t("enterprisePlan")}
        </div>

        <Link
          href="/contact-us"
          className="text-black text-2xl underline underline-offset-4 flex items-center justify-center gap-1 mt-1 hover:opacity-80"
        >
          {t("aboutSales")} <ArrowUpRight size={16} />
        </Link>
      </div>

      <div className="mt-20 sm:mt-32">
        <PricingDetails />
      </div>
    </section >
  </div >
}