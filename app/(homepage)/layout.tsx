import type { Metadata } from "next";
import { generateOrganizationSchema, generateWebSiteSchema } from "@/lib/seo/utils";

export const metadata: Metadata = {
  title: 'Urbalytics - 日本不動産AI駆動市場情報プラットフォーム',
  description: '日本最先端の不動産データプラットフォーム。AI駆動の市場分析、投資物件検索、収益性評価ツールで不動産投資を最適化。リアルタイム市場データと高精度な投資シミュレーションを提供。',
  keywords: '不動産投資, 日本不動産, AI分析, 市場データ, 投資物件, 収益性評価, 不動産テック, プロパティテック',
  openGraph: {
    title: 'Urbalytics - 日本不動産AI駆動市場情報プラットフォーム',
    description: '日本最先端の不動産データプラットフォーム。AI駆動の市場分析で不動産投資を最適化。',
    url: 'https://www.urbalytics.jp',
    siteName: 'Urbalytics',
    images: [
      {
        url: 'https://www.urbalytics.jp/og-image-home.jpg',
        width: 1200,
        height: 630,
        alt: 'Urbalytics - 日本不動産データプラットフォーム',
      },
    ],
    locale: 'ja_JP',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Urbalytics - 日本不動産AI駆動市場情報プラットフォーム',
    description: '日本最先端の不動産データプラットフォーム。AI駆動の市場分析で不動産投資を最適化。',
    images: ['https://www.urbalytics.jp/og-image-home.jpg'],
    creator: '@urbalytics',
  },
  alternates: {
    canonical: 'https://www.urbalytics.jp',
    languages: {
      'ja-JP': 'https://www.urbalytics.jp',
      'en-US': 'https://www.urbalytics.jp?lang=en',
      'zh-CN': 'https://www.urbalytics.jp?lang=zh',
    },
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function HomepageLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const organizationSchema = generateOrganizationSchema();
  const websiteSchema = generateWebSiteSchema();

  return (
    <>
      {/* 组织结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />

      {/* 网站结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />

      {children}
    </>
  );
}
