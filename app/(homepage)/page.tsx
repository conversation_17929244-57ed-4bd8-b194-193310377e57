"use client"

import { useEffect, useRef, useState } from "react";
import Typed from "typed.js";
import Image from 'next/image';
import { useTranslations } from "next-intl";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import SalesPoint from "./SalesPoint";
import Pricing from "./Pricing";
import PricingFaq from "./PricingFaq";
import Stats from "./Stats";
import TrustBy from "./TrustBy";
import FooterForNonLogin from "@/components/footerForNonLogin";
import { motion } from "framer-motion";
import SearchBoxHP from "./SearchBoxHP";
import { Orbitron } from 'next/font/google';
import clsx from "clsx";
import Loading from "../loading";
import Blog from "./Blog";
import Cta from '@/app/blog/Cta';


const orbitron = Orbitron({
  subsets: ['latin'],
  weight: ['600', '700'],
  variable: '--font-orbitron',
});

export default function BackgroundVideo() {
  const t = useTranslations("Homepage");
  const typedRef = useRef<HTMLSpanElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [hideScroll, setHideScroll] = useState(false);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.playbackRate = 0.5;
    }

    const options = {
      strings: [
        t("typed.options1"),
        t("typed.options2"),
        t("typed.options3"),
        t("typed.options4"),
        t("typed.options5")
      ],
      typeSpeed: 100,
      backSpeed: 20,
      loop: true,
    };

    const typed = new Typed(typedRef.current!, options);
    return () => {
      typed.destroy();
    };
  }, [t]);

  // Hide scroll indicator when user scrolls
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setHideScroll(true);
      } else {
        setHideScroll(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // so the header menu will work even if you jump from other pages
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const el = document.querySelector(hash);
      if (el) {
        setTimeout(() => {
          el.scrollIntoView({ behavior: 'smooth' });
        }, 100); // 延迟一下，确保渲染完毕
      }
    }
  }, []);


  // Function to scroll down
  const scrollToContent = () => {
    window.scrollTo({
      top: window.innerHeight,
      behavior: "smooth",
    });
  };

  return (
    <>
      <div
        id="background-video-wrapper"
        style={{
          position: 'relative',
          width: '100vw',
          height: '100vh',
          overflow: 'hidden',
        }}
        translate="no"
      >
        {/* 在移动设备上显示图像 */}
        {/* <video
          ref={videoRef}
          className="absolute top-0 left-0 w-full h-full object-cover hidden md:block"
          autoPlay
          loop
          muted
          playsInline
          controls
        >
          <source src="/videos/video_hp.mp4" type="video/mp4" />
        </video>
        <Image
          src="/assets/bg1.avif"
          className="absolute top-0 left-0 w-full h-full object-cover md:hidden"
          alt="Background"
          layout="fill"
          objectFit="cover"
        /> */}

        <motion.div
          className="absolute top-0 left-0 w-full h-full z-0 overflow-hidden"
          initial={{ scale: 1 }}
          animate={{ scale: [1, 1.05, 1] }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Image
            src="/assets/bg2.png"
            alt="Background"
            layout="fill"
            objectFit="cover"
            priority
          />
        </motion.div>

        <div className="absolute top-0 left-0 w-full h-full flex items-center justify-center bg-linear-to-br from-black/60 to-black/40 text-white">
          <div className="absolute z-20 flex flex-col justify-center h-full text-white px-2 sm:px-12 lg:px-24 max-w-3xl sm:max-w-6xl xs:w-full w-full text-center">
            <div className="text-center px-4">
              <div className="leading-snug md:leading-tight tracking-wide mb-3">
                <span
                  className={clsx(
                    orbitron.className,
                    "text-4xl md:text-6xl font-extrabold transition-transform duration-500 hover:scale-105 hover:drop-shadow-[0_0_6px_rgba(255,255,255,0.6)]"
                  )}
                >
                  {t("titlePrefix")} {/* 5秒 */}
                </span>
                <span className="font-light md:font-normal text-2xl md:text-3xl whitespace-nowrap">
                  {t("title")} {/* でわかる、物件の真の価値 */}
                </span>
              </div>
            </div>

            <SearchBoxHP />

            <div className="text-xl text-sm sm:text-xl mt-2 sm:mt-4 leading-relaxed px-2 justify-center px-4 h-[80px]">
              <span className="">{t("typedPrefix")}</span>
              <span ref={typedRef}></span>
            </div>

            {/* <motion.p
              className="text-md font-bold text-sm sm:text-xl text-white/80 md:mt-4 p-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 2, duration: 0.6, ease: "easeOut" }}
            >
              {t.rich("description", {
                u: (chunks) => <span className="underline underline-offset-4 mx-1 tracking-widest">{chunks}</span>,
              })}
            </motion.p> */}
          </div>
        </div>

        {/* Scroll Down Indicator */}
        {!hideScroll && (
          <div
            className="absolute bottom-4 sm:bottom-8 left-[calc(50%-30px)] transform -translate-x-1/2 flex flex-col items-center cursor-pointer animate-bounce-slow z-20 opacity-80"
            onClick={scrollToContent}
          >
            <span className="text-white text-sm">{t("scrollDown")}</span>
            <svg
              className="w-6 h-6 text-white mt-1"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7"></path>
            </svg>
          </div>
        )}

        <style jsx>{`
        #background-video-wrapper::after {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 10;
          width: 100%;
          height: 100%;
          background-image: url('/videos/pattern.png');
          background-repeat: repeat;
          opacity: 1;
          content: '';
          pointer-events: none;
        }
      `}</style>
      </div>

      <div className="pt-4 sm:pt-8" id="sales-point" translate="no">
        <SalesPoint />
      </div>

      <div className="py-8 sm:py-16 bg-neutral-100 mt-8" id="stats" translate="no">
        <Stats />
      </div>

      <div className="pt-4 sm:pt-8" id="trustBy">
        <TrustBy />
      </div>

      <div className="pt-4 sm:pt-8" id="pricing">
        <Pricing />
      </div>

      <div className="pt-4 sm:pt-8" id="blog">
        <Blog />
      </div>

      <div className="pt-4 sm:pt-8 mb-[24px]" id="pricing-faq">
        <PricingFaq />
      </div>

      <Cta />

      <FooterForNonLogin />
    </>
  );
}