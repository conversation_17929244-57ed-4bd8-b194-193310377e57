"use client"

import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { sendLark, LARK_URLS } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";
import { Tabs as TabsRaxis, TabsContent, TabsList as TabsListRaxis, TabsTrigger as TabsTriggerRaxis } from "@/components/ui/tabsRadixHP";
import { Banknote, Building2Icon, ConstructionIcon, MapPin } from "lucide-react";
import { useLocale } from "next-intl";
import { useHomepageCounts } from "@/hooks/use-homepage-counts";

const SearchBarCombinedUIForHP = dynamic(() => import("./SearchBarCombinedUIForHP"), { ssr: false });

export default function SearchBoxHP() {
  const [selectRecordType, setSelectRecordType] = useState<RecordType>("MANSION");
  type RecordType = "BUILDING" | "HOUSE" | "LAND" | "MANSION"; // 根据你的业务扩展
  const [selectedOption, setSelectedOption] = useState<any>(null);
  // 🚀 Use cached homepage counts for better performance
  const {
    data: countsData,
    isLoading,
    refetch: refetchCounts
  } = useHomepageCounts();

  const t = useTranslations("Homepage.search");
  const [selectedResultTab, setSelectedResultTab] = useState("sale");
  const router = useRouter();
  const { currentUser } = useAuthStore();

  const currentLocale = useLocale();

  function capitalizeWords(input : string ) {
    return input.replace(/\b\w/g, char => char.toUpperCase());
  }

  useEffect(() => {
    if (selectedOption?.value) {
      console.log("selectedOption", selectedOption);
    }
  }, [selectedOption, selectRecordType]);



  return (
    <div className="rounded-lg p-1 py-2 m-2 md:w-full relative">
      <TabsRaxis defaultValue={selectedResultTab} className="flex flex-row justify-start items-center flex-wrap overflow-x-auto" onValueChange={(value: string) => {
        setSelectedResultTab(value)
      }}>
        <TabsListRaxis>
          <TabsTriggerRaxis value="sale">{t("sale")}</TabsTriggerRaxis>
          <TabsTriggerRaxis value="rent">{t("rent")}</TabsTriggerRaxis>
          <TabsTriggerRaxis value="valuation">{t("valuation")}</TabsTriggerRaxis>
          <TabsTriggerRaxis value="tools">
            <div className="flex flex-row gap-2 items-center">
              {t("tools")}
              {/* <Badge className="text-xs">NEW</Badge> */}
            </div>
          </TabsTriggerRaxis>
        </TabsListRaxis>
      </TabsRaxis>

      {(selectedResultTab === "sale" || selectedResultTab === "rent") && <div className="flex flex-col gap-1 w-full bg-neutral-100 rounded-b-md rounded-r-md py-2 px-2 relative h-[100px]">
        <div className="flex flex-row justify-start items-center flex-wrap overflow-x-auto">
          <Tabs value={selectRecordType} onValueChange={(value) => {
            setSelectRecordType(value as RecordType);
          }}>
            <TabsList>
              {(selectedResultTab === "sale" ? ["MANSION", "BUILDING", "HOUSE", "LAND"] : ["MANSION", "BUILDING", "HOUSE"]).map((recordType) => <TabsTrigger key={recordType} value={recordType}>
                <div className="flex flex-row gap-2 items-center">
                  {mapper[recordType as keyof typeof mapper].iconSmall}
                  {mapper[recordType as keyof typeof mapper][`name${capitalizeWords(currentLocale)}`] !== undefined ? mapper[recordType as keyof typeof mapper][`name${capitalizeWords(currentLocale)}`] : mapper[recordType as keyof typeof mapper].name}
                </div>
              </TabsTrigger>)}
            </TabsList>
          </Tabs>
        </div>

        <div className="flex flex-col gap-2 w-full text-left px-1">
          <div className="flex flex-col gap-1 w-full text-left">
            <SearchBarCombinedUIForHP
              selectedOption={selectedOption}
              setSelectedOption={setSelectedOption}
              recordType={selectRecordType}
              selectedResultTab={selectedResultTab}
            />

            {selectedResultTab === "sale" && !isLoading && (
              <div className="flex flex-row items-center absolute top-4 right-2 rounded-lg p-1 z-10 hidden sm:flex gap-1">
                <Badge variant="secondary" className="items-center font-normal text-gray-500 p-0">
                  {t("newlyCreatedCount")} {countsData.newlyCreatedCount.toLocaleString()} {t("unit")}
                </Badge>
                <Badge variant="secondary" className="items-center font-normal text-gray-500 p-0">
                  {t("priceChangeCount")} {countsData.priceChangeCount.toLocaleString()} {t("unit")}
                </Badge>
              </div>
            )}
          </div>
        </div>
      </div>}

      {selectedResultTab === "valuation" && <div className="flex flex-col gap-1 w-full bg-neutral-100 rounded-b-md rounded-r-md py-2 px-2 h-[100px] relative">
        <div className="flex flex-col gap-1 w-full text-lg text-left text-center flex-1 justify-center items-center">
          <p className="text-base text-black">{t("valuationIntro")}</p>
        </div>

        <div className="mt-auto">
          <Button variant="default" className="w-full" onClick={() => {
            router.push("/ex/valuation");
            sendLark({
              message: `[👀][估值]${currentUser?.name || "匿名"}さんが估值を実行しました from hp link`,
              url: LARK_URLS.USER_ACTIVITY_CHANNEL,
            });
          }}>{t("valuationCta")}</Button>
        </div>
      </div>}

      {selectedResultTab === "tools" && <div className="flex flex-col gap-1 w-full bg-neutral-100 rounded-b-md rounded-r-md py-2 px-2 h-[100px] relative flex flex-wrap overflow-x-auto md:grid md:grid-cols-3 gap-2">
        <div
          className="col-span-1 bg-transparent hover:bg-black transition-colors duration-200 rounded-lg p-3 flex flex-row gap-3 justify-start items-center cursor-pointer group border border-black/10"
          onClick={() => router.push("/it/insight")}
        >
          <div className="bg-black/10 p-2 rounded-lg group-hover:bg-white/20 transition-colors duration-200">
            <ConstructionIcon className="w-8 h-8 text-black group-hover:text-white" />
          </div>
          <div className="flex flex-col gap-1 text-left">
            <div className="text-lg font-bold text-black group-hover:text-white">
              {t("tools1Title")}
            </div>
            <div className="text-sm text-black/80 group-hover:text-white/90">
              {t("tools1Description")}
            </div>
          </div>
        </div>

        <div
          className="col-span-1 bg-transparent hover:bg-black transition-colors duration-200 rounded-lg p-3 flex flex-row gap-3 justify-start items-center cursor-pointer group border border-black/10"
          onClick={() => router.push("/it/chiban")}
        >
          <div className="bg-black/10 p-2 rounded-lg group-hover:bg-white/20 transition-colors duration-200">
            <MapPin className="w-8 h-8 text-black group-hover:text-white" />
          </div>
          <div className="flex flex-col gap-1 text-left">
            <div className="text-lg font-bold text-black group-hover:text-white">
              {t("tools2Title")}
            </div>
            <div className="text-sm text-black/80 group-hover:text-white/90">
              {t("tools2Description")}
            </div>
          </div>
        </div>

        <div
          className="col-span-1 bg-transparent hover:bg-black transition-colors duration-200 rounded-lg p-3 flex flex-row gap-3 justify-start items-center cursor-pointer group border border-black/10"
          onClick={() => router.push("/it/cf")}
        >
          <div className="bg-black/10 p-2 rounded-lg group-hover:bg-white/20 transition-colors duration-200">
            <Banknote className="w-8 h-8 text-black group-hover:text-white" />
          </div>
          <div className="flex flex-col gap-1 text-left">
            <div className="text-lg font-bold text-black group-hover:text-white">
              {t("tools3Title")}
            </div>
            <div className="text-sm text-black/80 group-hover:text-white/90">
              {t("tools3Description")}
            </div>
          </div>
        </div>
      </div>}
    </div>
  );
}