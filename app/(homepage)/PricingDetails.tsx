import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckIcon, Minus } from "lucide-react";
import { PricingTier } from "@/lib/constants/pricingTier";
import React from "react";
import { useTranslations } from "next-intl";

export default function PricingDetails() {
  const t = useTranslations("PricingDetail");

  const plans = [
    { name: "Free", action: t("freeButtonText") },
    { name: "Plus", action: "Get Plus" },
    { name: "Pro", action: "Get Pro" },
    { name: t("enterprise"), action: "Contact sales" },
  ];

  const features = {
    [t("propertySearch")]: [
      {
        label: t("propertySearchDetail"),
        values: [{
          value: PricingTier[1].dailySearchCount,
        }, {
          value: PricingTier[10].dailySearchCount,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchHistory"),
        values: [{
          value: t("limit"),
          description: t("noSeiyaku")
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchPriceChange"),
        values: [{
          value: PricingTier[1].dailyPriceChangeHistoryCount,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchDiscount"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchRentUp"),
        values: [{
          value: false
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      // {
      //   label: "近隣公示成約事例",
      //   values: [
      //     {
      //       value: true,
      //     }, {
      //       value: true,
      //     }, {
      //       value: true,
      //     }, {
      //       value: true,
      //     }
      //   ],
      // },
      {
        label: t("propertySearchFav"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchFavCondition"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchRent"),
        values: [
        {
          value: PricingTier[1].dailyRentSearchCount,
          description: t("perDay")
        },
        {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchValuation"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchCsv"),
        values: [{
          value: false,
        }, {
          value: false,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("propertySearchAuction"),
        values: [{
          value: false,
        }, {
          value: false,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
    ],
    [t("marketInsight")]: [
      {
        label: t("marketInsightDetail"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("marketInsightApartment"),
        values: [{
          value: t("limit"),
          description: "売買掲載事例のみ"
        }, {
          value: true,
          description: "賃料・売買成約・掲載事例"
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("marketInsightStation"),
        values: [{
          value: t("limit"),
          description: "売買掲載事例のみ"
        }, {
          value: true,
          description: "賃料・売買成約・掲載事例"
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("marketInsightArea"),
        values: [{
          value: t("limit"),
          description: "売買掲載事例のみ"
        },
        {
          value: true,
          description: "賃料・売買成約・掲載事例"
        },
        {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("marketInsightCompany"),
        values: [{
          value: false,
        }, {
          value: false,
        }, {
          value: false,
        }, {
          value: true,
        }],
      }
    ],
    [t("investmentTool")]: [
      {
        label: t("investmentToolMap"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("investmentToolKensaku"),
        values: [{
          value: true,
        },
        {
          value: true,
        },
        {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("investmentToolCf"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("investmentToolMinpaku"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("investmentToolCapRate"),
        values: [{
          value: false,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("investmentToolRegistration"),
        values: [{
          value: false,
        }, {
          value: false,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("investmentToolLoan"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("investmentToolCustomize"),
        values: [{
          value: false,
        }, {
          value: false,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
    ],
    [t("customSupport")]: [
      {
        label: t("customSupportProperty"),
        values: [{
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("customSupportIndividual"),
        values: [{
          value: false,
        }, {
          value: false,
        }, {
          value: true,
        }, {
          value: true,
        }],
      },
      {
        label: t("customSupportApi"),
        values: [{
          value: false,
        }, {
          value: false,
        }, {
          value: false,
        }, {
          value: true,
          description: t("extraCost")
        }],
      },
      {
        label: t("customSupportCustomize"),
        values: [{
          value: false,
        }, {
          value: false,
        }, {
          value: false,
        }, {
          value: true,
          description: t("extraCost")
        }],
      },
    ],
  } as const;

  return (
    <div className="w-full max-w-[1200px] mx-auto px-4">
      <h2 className="text-2xl font-semibold text-center mb-8">
        {t("comparePlan")}
      </h2>

      <div className="grid grid-cols-5 gap-4 bg-white sticky top-0">
        <div></div>
        {plans.map((plan, index) => (
          <div key={index} className="text-center border-b border-gray-200">
            <h3 className="text-xl font-semibold mb-2">{plan.name}</h3>
            {/* <Button variant="outline" size="sm">
                {plan.action}
              </Button> */}
          </div>
        ))}
      </div>

      {Object.keys(features).map((key, index) => (
        <div className="mt-8 grid grid-cols-5" key={index}>
          <div className="text-xl sm:text-2xl font-semibold py-6 pb-2 col-span-5">{key}</div>

          {features[key as keyof typeof features].map((feature: any, index: number) => (
            <React.Fragment key={feature.label}>
              <div className="text-xs font-semibold sm:font-normal sm:text-base py-4 border-b border-gray-200 flex items-center justify-start" >
                {feature.label}
              </div>

              {feature.values.map((entry: any, i: number) => (
                <div
                  key={`${index}-${i}`}
                  className="flex flex-col py-4 text-xs sm:text-base text-center w-full border-b border-gray-200 justify-center items-center flex"
                >
                  <div>
                    {entry.value === true ? <CheckIcon strokeWidth={3} className="font-bold w-4 h-4" /> : entry.value === false ? <Minus strokeWidth={1} className="font-bold w-4 h-4 text-gray-400" /> : typeof entry.value === "number" ? <div className="flex items-end justify-center text-center">
                      <span className="text-base sm:text-xl">
                        {entry.value}
                      </span>
                      <span className="text-xs text-gray-700 ml-1">{t("times")}/{t("day")}</span>
                    </div> : entry.value}
                  </div>
                  {entry.description && <div className="text-xs text-gray-500 mt-1">({entry.description})</div>}
                </div>
              ))}
            </React.Fragment>
          ))}
        </div>
      ))}
    </div>
  );
}