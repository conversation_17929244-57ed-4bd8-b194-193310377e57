"use client";

import Image from "next/image";
import { Separator } from "@/components/ui/separator";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";

export default function SalesPoint() {
  const t = useTranslations("Homepage");

  const [openImage, setOpenImage] = useState<string | null>(null);

  const services = [
    {
      title: t("salesPoint1.title"),
      subtitle: t("salesPoint1.subtitle"),
      description: t("salesPoint1.description"),
      footNote: [t("salesPoint1.footNote"),  t("salesPoint1.footNote2")],
      buttonLink: "#",
      tier: "Free",
      image: "/hp/hp_01.png",
    },
    {
      title: t("salesPoint2.title"),
      subtitle: t("salesPoint2.subtitle"),
      description: t("salesPoint2.description"),
      footNote: [t("salesPoint2.footNote")],
      buttonLink: "#",
      tier: "Free",
      image: "/hp/hp_02.png",
    },
    {
      title: t("salesPoint3.title"),
      subtitle: t("salesPoint3.subtitle"),
      description: t("salesPoint3.description"),
      buttonLink: "#",
      tier: "Free",
      image: "/hp/hp_03.png",
    },
    {
      title: t("salesPoint4.title"),
      subtitle: t("salesPoint4.subtitle"),
      description: t("salesPoint4.description"),
      buttonLink: "#",
      tier: "Free",
      image: "/hp/hp_04.png",
    },
    {
      title: t("salesPoint5.title"),
      subtitle: t("salesPoint5.subtitle"),
      description: t("salesPoint5.description"),
      buttonLink: "#",
      tier: "Free",
      image: "/hp/hp_05.jpg",
    },
    {
      title: t("salesPoint6.title"),
      subtitle: t("salesPoint6.subtitle"),
      description: t("salesPoint6.description"),
      buttonLink: "#",
      tier: "Plus",
      image: "/hp/hp_06.jpg",
    },
  ];

  return (
    <div className="w-full max-w-[1200px] mx-auto px-4">
      <div className="text-center text-neutral-500 tracking-widest mb-2 font-bold mt-8">
        WHY URBALYTICS
      </div>
      <h2 className="text-center text-3xl tracking-wide font-semibold">
        {t("salesPoint.title")}
      </h2>

      <Separator className="my-8" />

      <div className="flex flex-col gap-16 p-4 sm:p-8">
        {services.map((service, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div
              className={`flex flex-col md:flex-row ${index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
                } items-center relative`}
            >
              {/* Image */}
              <div
                className="relative w-full md:w-1/2 h-[280px] sm:h-[480px] cursor-pointer transition-all duration-1000 ease-in-out hover:scale-105"
                onClick={() => setOpenImage(`/assets${service.image}`)}
              >
                <Image
                  src={`/assets${service.image}`}
                  alt={service.title}
                  fill
                  className={`object-contain w-full h-full bg-white ${index % 2 === 0 ? "object-right" : "object-left"
                    }`}
                />
              </div>

              {/* Text Content */}
              <div className="w-full md:w-1/2 p-4 sm:p-6 text-left">

                {/* Enhanced Tier Badge */}
                <div className="mb-3">
                  {service.tier === "Free" ? (
                    <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full text-sm font-bold shadow-lg">
                      <span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
                      <span>FREE ✨</span>
                      {/* <span className="text-xs opacity-90">{t("salesPoint.freeToUse")}</span> */}
                    </div>
                  ) : service.tier === "Plus" ? (
                    <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full text-sm font-bold shadow-lg">
                      <span className="w-2 h-2 bg-white rounded-full"></span>
                      <span>PLUS</span>
                    </div>
                  ) : (
                    <Badge className="mb-1" variant="outline">{service.tier}</Badge>
                  )}
                </div>

                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-xl sm:text-3xl font-semibold tracking-wide">
                    {service.title}
                  </h3>
                  {/* {service.tier === "Free" && (
                    <div className="hidden sm:flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 rounded-md text-xs font-medium">
                      <span>✨</span>
                      <span>{t("salesPoint.freeToUse")}</span>
                    </div>
                  )} */}
                </div>

                <p className="text-primary/80 font-bold text-lg sm:text-xl">
                  {service.subtitle}
                </p>

                <p className="text-neutral-900 text-base sm:text-lg leading-relaxed mt-4">
                  {service.description}
                </p>
                {service.footNote && <div className="flex flex-col gap-1 mt-3">
                  {service.footNote.map((note, index) => (
                    <div className="text-neutral-500 text-xs sm:text-sm" key={index}>
                      ※ {note}
                    </div>  
                  ))}
                </div>}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Modal for Zoomed Image */}
      {openImage && (
        <div
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center"
          onClick={() => setOpenImage(null)}
        >
          <div className="relative w-[90vw] h-[80vh]">
            <Image
              src={openImage}
              alt="Zoomed Image"
              fill
              className="object-contain"
            />
          </div>
        </div>
      )}
    </div>
  );
}