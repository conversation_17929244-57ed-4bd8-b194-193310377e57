{"buildCommand": "prisma generate && pnpm run build:fast && next-sitemap", "installCommand": "pnpm install --frozen-lockfile", "framework": "nextjs", "regions": ["hnd1"], "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1", "NODE_OPTIONS": "--max-old-space-size=4096"}}, "crons": [{"path": "/api/cron/sumitomoAuction", "schedule": "15,45 4,9,11 * * *"}, {"path": "/api/cron/acquisition", "schedule": "*/10 11 * * *"}, {"path": "/api/cron/reins?recordType=BUILDING", "schedule": "*/15 4,9,12 * * *"}, {"path": "/api/cron/reins?recordType=HOUSE", "schedule": "*/15 4,9,12 * * *"}, {"path": "/api/cron/reins?recordType=LAND", "schedule": "*/15 4,9,12 * * *"}, {"path": "/api/cron/reins?recordType=MANSION", "schedule": "*/15 4,9,12 * * *"}, {"path": "/api/cron/reins/rentMansion", "schedule": "*/15 12,13 * * *"}, {"path": "/api/cron/reinsSold/rentMansionBuildingPart", "schedule": "15 13 * * *"}, {"path": "/api/cron/reinsSold/rentBuildingWholeHouse", "schedule": "*/15 0,13 * * *"}, {"path": "/api/cron/reinsSold", "schedule": "25,55 12 * * *"}, {"path": "/api/cron/reinsFill/recordAnalysis", "schedule": "29,59 4,9,12 * * *"}, {"path": "/api/cron/reinsFill/companyDetails", "schedule": "55 4,12 * * *"}, {"path": "/api/cron/reinsRefresh/post", "schedule": "0,30 0 * * *"}, {"path": "/api/cron/acquisition/sites/suumo", "schedule": "*/15 4,12 * * *"}, {"path": "/api/cron/acquisition/sites/suumo/land", "schedule": "*/15 9 * * *"}, {"path": "/api/cron/acquisition/sites/rakumachi", "schedule": "10,40 10 * * *"}, {"path": "/api/cron/push/dailyCustomerMatch", "schedule": "0 13 * * *"}, {"path": "/api/cron/push/dailyUserPush", "schedule": "15 4,13 * * *"}, {"path": "/api/cron/push/dailyEmail", "schedule": "0 13 * * *"}, {"path": "/api/cron/system/expireViewHistory", "schedule": "30 13 * * *"}]}