import { getRequestConfig } from 'next-intl/server';
import { cookies, headers } from "next/headers"

export default getRequestConfig(async () => {
  const headersList = await headers();
  const cookieStore = await cookies();

  // 🔥 Get locale from multiple sources with priority
  // 1. URL parameter (from referer or current URL)
  const referer = headersList.get('referer') || '';
  const urlParams = new URLSearchParams(referer.split('?')[1] || '');
  const urlLang = urlParams.get('lang');

  // 2. Cookie
  const cookieLocale = cookieStore.get('locale')?.value;

  // 3. Accept-Language header
  const acceptLanguage = headersList.get('accept-language') || '';
  const browserLang = acceptLanguage.split(',')[0]?.split('-')[0];

  // 🔥 Priority: URL parameter > Cookie > Browser > Default
  const supportedLocales = ['en', 'ja', 'zh'];
  const locale =
    (urlLang && supportedLocales.includes(urlLang)) ? urlLang :
    (cookieLocale && supportedLocales.includes(cookieLocale)) ? cookieLocale :
    (browserLang && supportedLocales.includes(browserLang)) ? browserLang :
    'ja';

  console.log('🔥 [i18n/request] Locale detection:', {
    urlLang,
    cookieLocale,
    browserLang,
    finalLocale: locale
  });

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default
  };
});