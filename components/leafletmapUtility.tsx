export const getCenterFromRecords = (records: any[]) => {
  const validRecords = records.filter(
    (record) => typeof record.latitude === 'number' && typeof record.longitude === 'number'
  );

  if (validRecords.length === 0) return null;

  const latitudes = validRecords.map((r) => r.latitude);
  const longitudes = validRecords.map((r) => r.longitude);

  const minLat = Math.min(...latitudes);
  const maxLat = Math.max(...latitudes);
  const minLng = Math.min(...longitudes);
  const maxLng = Math.max(...longitudes);

  const centerLat = (minLat + maxLat) / 2;
  const centerLng = (minLng + maxLng) / 2;

  return {
    latitude: centerLat,
    longitude: centerLng,
  };
};