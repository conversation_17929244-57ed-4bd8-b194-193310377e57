"use client"; // ✅ Ensure client-side rendering

import { useEffect, useRef, useState } from "react";
import "leaflet/dist/leaflet.css";
// @ts-ignore
import { LatLngExpression } from "leaflet";
import { Loader } from "lucide-react";
import dynamic from "next/dynamic";
import debounce from "lodash-es/debounce";
import { useUIStore } from "@/store/ui";
// @ts-ignore
// import L from 'leaflet'; // 确保引入 L

//FIXME: note this will break the hash showMap=true
let L: any; // Declare L globally

if (typeof window !== "undefined") {
  L = require("leaflet"); // ✅ Import Leaflet only in the browser
}
import Link from "next/link";
import { toast } from "@/hooks/use-toast";

// ✅ Dynamically import Leaflet components to avoid SSR issues
const MapContainer = dynamic(
  () => import("react-leaflet").then((mod) => mod.MapContainer),
  { ssr: false }
);
const TileLayer = dynamic(() => import("react-leaflet").then((mod) => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import("react-leaflet").then((mod) => mod.Marker), { ssr: false }) as any;
const Popup = dynamic(() => import("react-leaflet").then((mod) => mod.Popup), { ssr: false });
// const useMap = dynamic(() => import("react-leaflet").then((mod) => mod.useMap), { ssr: false });
import { useMap } from "react-leaflet"; // ✅ 替换原来 dynamic 的方式
import { Button } from "@react-email/components";
import { useMemo } from "react";
import { getCenterFromRecords } from "./leafletmapUtility";

// This is useful, e.g. when search bar pick a new address
// is not updated immediately, so we need to recenter the map
// after the data is updated
function Recenter({ center }: { center: LatLngExpression }) {
  const map = useMap();
  const prevCenterRef = useRef<LatLngExpression | null>(null);

  useEffect(() => {
    const isEqual =
      JSON.stringify(center) === JSON.stringify(prevCenterRef.current);

    if (!isEqual && center) {
      map.setView(center, map.getZoom(), { animate: true });
      prevCenterRef.current = center;
    }
  }, [center]);

  return null;
}

export default function LeafletMapSimple({
  height = "360px", zoom = 13, minZoom = 11, data, useCustomIcon = false, mapStyle = "light", selectedPropertyCoordinates
}:
  {
    height?: string;
    zoom?: number;
    minZoom?: number;
    data?: {
      name: string;
      link?: string;
      colorDot?: string;
      coordinate: LatLngExpression;
      popUpRenderFunction?: () => React.ReactNode;
      textToShow?: {
        title: string;
        description: string;
      };
      id?: string;
    }[];
    useCustomIcon?: boolean;
    mapStyle?: string;
    selectedPropertyCoordinates?: LatLngExpression;
  }
) {
  const fallbackCenter: LatLngExpression = [35.6895, 139.6917]; // Tokyo
  const [selectedMarkerIndex, setSelectedMarkerIndex] = useState<number | null>(null);
  const borderColor = "#888"; // 或你想要的任何颜色，例如 "#4B5563"（Tailwind 的 gray-700）
  const [center, setCenter] = useState<{ lat: number; lng: number } | null>(null);
  const { selectedProejctIdForKensetsu, setSelectedProejctIdForKensetsu } = useUIStore();

  const mapRef = useRef<any>(null);

  useEffect(() => {
    if (mapRef.current) {
      setTimeout(() => {
        mapRef.current.invalidateSize();
      }, 200); // 延迟触发，确保容器加载完成
    }
  }, []);

  useEffect(() => {
    if (data && data.length > 0) {
      const calculated = getCenterFromRecords([
        ...data,
        ...(selectedPropertyCoordinates ? [{
          coordinate: selectedPropertyCoordinates,
          name: "本物件",
        }] : []),
      ]);

      if (calculated) {
        setCenter({
          lat: calculated.latitude,
          lng: calculated.longitude,
        });
      }
    }
  }, [data]);

  const handleMarkerClick = (id?: string) => {
    if (id) {
      console.log('🔥 设置项目 ID:', id);
      setSelectedProejctIdForKensetsu(id);
    }
  };

  return (
    <MapContainer
      // @ts-ignore 
      className="custom-zoom-map"
      center={center || fallbackCenter}
      zoom={zoom}
      minZoom={minZoom || 11}
      style={{ minHeight: "200px", height: height, width: "100%", position: "relative", zIndex: 0 }}
      scrollWheelZoom={false}
      ref={mapRef}
    >
      <style jsx global>{`
  .custom-zoom-map .leaflet-control-zoom {
    margin-top: 48px;
  }
`}</style>
      {center && <Recenter key={`${center.lat},${center.lng}`} center={center || fallbackCenter} />}

      {mapStyle === "light" && <TileLayer
        url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
      />}
      {mapStyle === "dark" && <TileLayer
        url="https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png"
      />}
      {mapStyle === "streets" && <TileLayer
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />}

      {data?.map((d, index) => (
        <Marker
          key={index}
          position={d.coordinate}
          eventHandlers={{
            click: () => handleMarkerClick(d.id),
          }}
          icon={useCustomIcon && d.textToShow ? L.divIcon({
            html: `
     <div style="
      position: relative;
      background: ${selectedMarkerIndex === index ? "#dbeafe" : 'whitesmoke'};
      padding: 0px 4px;
    border: 1px solid ${selectedMarkerIndex === index ? 'black' : 'gray'};
    border-radius: 6px;
      font-size: 12px;
      text-align: center;
      color: black;
    ">
      <div style="
        font-size: 10px;
      ">
        ${d.textToShow ? d.textToShow.title : ""}
      </div>
      <div style="
        font-size: 12px;
      ">
        ${d.textToShow ? d.textToShow ? d.textToShow.description : "" : ""}
      </div>
      <div style="
        position: absolute;
        bottom: -7px;
        left: 50%;
        margin-left: -7px;
        width: 0;
        height: 0;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
      border-top: 7px solid ${selectedMarkerIndex === index ? '#3b82f6' : borderColor};
      "></div>
      <div style="
        position: absolute;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 6px solid white;
      "></div>
    </div>
  `,
            className: '', // 清除默认样式
            iconSize: [100, 30], // 你可以根据内容大小调整
            iconAnchor: [50, 30],
          }) : L.icon({
            iconUrl: `/assets/map/${d.colorDot || "red"}-dot.png`,
            iconSize: [40, 40],
            iconAnchor: [20, 40],
            popupAnchor: [1, -34],
          })}>
          <Popup eventHandlers={{
            add: () => {
              setSelectedMarkerIndex(index);
              handleMarkerClick(d.id);
            },
            remove: () => setSelectedMarkerIndex(null),
          }}>
            {d.link ? <Link href={d.link || ""} target="_blank" rel="noopener noreferrer">
              {d.popUpRenderFunction ? d.popUpRenderFunction() : d.name}
            </Link> : d.popUpRenderFunction ? d.popUpRenderFunction() : <div>{d.name}</div>}
          </Popup>
        </Marker>
      ))}


      {selectedPropertyCoordinates && <Marker position={selectedPropertyCoordinates} icon={L.icon({
        iconUrl: `/assets/map/blue-dot.png`,
        iconSize: [40, 40],
        iconAnchor: [20, 40],
        popupAnchor: [1, -34],
      })} />}

      {selectedPropertyCoordinates && <div className="flex flex-row gap-2 bottom-0 absolute z-999 text-center justify-center bg-neutral-50 p-1">
        <div className="w-4 h-4 bg-blue-500"></div>
        <div>本物件</div>
      </div>
      }
    </MapContainer>
  );
}