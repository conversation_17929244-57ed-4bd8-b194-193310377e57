"use client";

import { useSearchParams } from "next/navigation";
import { useLocale } from "next-intl";
import { setCookie } from "cookies-next";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { Globe } from 'lucide-react';
import { sendLark, LARK_URLS } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import { usePathname } from "next/navigation";

const locales = ["en", "ja", "zh"]; // Available languages

export default function LanguageSwitcher({ color }: { color: string }) {
  const searchParams = useSearchParams();
  const currentUser = useAuthStore((state) => state.currentUser);
  const currentLocale = useLocale();
  const pathname = usePathname()

  const changeLanguage = (locale: string) => {
    // Prevent unnecessary changes
    if (locale === currentLocale) {
      console.log("🔥[LanguageSwitcher] Language already set to", locale);
      return;
    }

    console.log("🔥[LanguageSwitcher] changing locale from", currentLocale, "to", locale);

    // 🔥 Set cookie immediately for persistence
    setCookie("locale", locale, {
      path: "/",
      maxAge: 60 * 60 * 24 * 365, // 1 year
      sameSite: 'lax'
    });

    // 🔥 Handle wiki pages with language-specific routing
    if (pathname.includes("wiki")) {
      console.log("🔥[LanguageSwitcher] Handling wiki page language change to:", locale);

      // Replace existing language in wiki path with new language
      const wikiPathRegex = /^\/wiki\/([a-z]{2})(\/.*)?$/;
      const match = pathname.match(wikiPathRegex);

      let newRoute;
      if (match) {
        // If path has language segment, replace it
        const [, currentLang, restOfPath] = match;
        newRoute = `/wiki/${locale}${restOfPath || ''}`;
        console.log("🔥[LanguageSwitcher] Replacing", currentLang, "with", locale, "in path:", pathname, "->", newRoute);
      } else {
        // If no language segment, add it
        newRoute = pathname.replace('/wiki', `/wiki/${locale}`);
        console.log("🔥[LanguageSwitcher] Adding language to path:", pathname, "->", newRoute);
      }

      // 🔥 For subsequent changes, use window.location.href directly for reliability
      console.log("🔥[LanguageSwitcher] Navigating to wiki route:", newRoute);
      window.location.href = newRoute;
    } else {
      // For non-wiki pages, use URL parameter approach
      const currentParams = new URLSearchParams(searchParams.toString());
      currentParams.set('lang', locale);
      const newUrl = `${pathname}?${currentParams.toString()}`;

      console.log("🔥[LanguageSwitcher] Navigating to non-wiki route:", newUrl);

      // 🔥 For subsequent changes, use window.location.href directly for reliability
      window.location.href = newUrl;
    }

    // Track user activity (this might not execute due to page reload, but that's okay)
    createNewSystemUserActivityAction({
      data: {
        eventType: "BUTTON_CLICK",
        route: pathname,
        eventMetadata: {
          buttonName: "languageSwitcher",
          locale: locale,
        },
      },
    });

    sendLark({ message: `[${currentUser?.name || 'unknown'}]が 言語を${locale}に変更しました`, url: LARK_URLS.USER_ACTIVITY_CHANNEL });
  };

  // next-intl handles locale detection automatically, no need for manual useEffect

  const mapper = {
    ja: "日本語",
    en: "EN",
    zh: "中文",
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="border-none">
        <Button variant="ghost" className={`flex items-center ${color} px-2`}>
          <Globe /> {/* 添加语言图标 */}
          {mapper[currentLocale as keyof typeof mapper]}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        {locales.map((locale) => (
          <DropdownMenuItem
            key={locale}
            onClick={() => changeLanguage(locale)}
          >
            {mapper[locale as keyof typeof mapper]}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}