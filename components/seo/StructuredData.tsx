import React from 'react';

interface StructuredDataProps {
  data: object;
  id?: string;
}

export function StructuredData({ data, id }: StructuredDataProps) {
  return (
    <script
      id={id}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data)
      }}
    />
  );
}

// 多个结构化数据组件
interface MultipleStructuredDataProps {
  schemas: Array<{ data: object; id?: string }>;
}

export function MultipleStructuredData({ schemas }: MultipleStructuredDataProps) {
  return (
    <>
      {schemas.map((schema, index) => (
        <StructuredData 
          key={schema.id || `schema-${index}`}
          data={schema.data}
          id={schema.id}
        />
      ))}
    </>
  );
}

// 预定义的结构化数据组件
export function OrganizationStructuredData() {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Urbalytics",
    "url": "https://www.urbalytics.jp",
    "logo": "https://www.urbalytics.jp/logo.png",
    "description": "日本最先端の不動産データプラットフォーム。AI駆動の市場分析、投資物件検索、収益性評価ツールで不動産投資を最適化。",
    "foundingDate": "2023",
    "industry": "Real Estate Technology",
    "areaServed": {
      "@type": "Country",
      "name": "Japan"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://twitter.com/urbalytics",
      "https://www.linkedin.com/company/urbalytics"
    ]
  };

  return <StructuredData data={organizationSchema} id="organization-schema" />;
}

export function WebSiteStructuredData() {
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Urbalytics",
    "url": "https://www.urbalytics.jp",
    "description": "日本最先端の不動産データプラットフォーム。AI駆動の市場分析で不動産投資を最適化。",
    "publisher": {
      "@type": "Organization",
      "name": "Urbalytics"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://www.urbalytics.jp/ex/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  return <StructuredData data={websiteSchema} id="website-schema" />;
}
