"use client"

import { Ch<PERSON>ronRight, Lock, type LucideIcon } from "lucide-react"
import { Badge } from "@/components/ui/badge"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"
import Link from "next/link";

export function NavMain({
  items,
  currentPath,
  notification,
  currentUser
}: {
  items: {
    title: string
    menus: {
      title: string
      url: string
      icon: LucideIcon
      isActive?: boolean
      disable?: boolean // 新增 disable 属性
      items?: {
        title: string
        url: string,
        supposedAccessLevel?: number
        accessLevel?: number
      }[],
      supposedAccessLevel?: number
    }[]
  }[]
  currentPath: string,
  notification: any,
  currentUser: any
}) {
  const { toggleSidebar } = useSidebar();

  return (
    <SidebarGroup>
      {items.map((item) => {
        return (
          <div key={item.title} className="my-2">
            {item.title && <SidebarGroupLabel>{item.title}</SidebarGroupLabel>}
            {item.menus.map((menu) => {
              const isActive = currentPath === menu.url ||
                (menu.items && menu.items.some(subItem => currentPath.includes(subItem.url))) ||
                currentPath.startsWith(menu.url); // 添加对子路由的支持

              return (
                <SidebarMenu key={menu.title}>
                  <Collapsible key={item.title} asChild defaultOpen={isActive} disabled={menu.disable} open={isActive}>
                    <SidebarMenuItem> {/* 修改背景色为更深的灰色 */}
                      <SidebarMenuButton asChild tooltip={menu.title} onClick={() => { if (!menu.disable && window.innerWidth <= 768) { toggleSidebar(); } }}>
                        <Link
                          href={menu.disable ? '#' : menu.url}
                          className={`${menu.disable ? 'text-gray-400 pointer-events-none' : ''} ${isActive ? 'bg-neutral-200' : ''}`}
                          prefetch={true}>

                          <menu.icon />

                          <span>{menu.title}</span>

                          {menu.supposedAccessLevel && menu.supposedAccessLevel > currentUser?.accessLevel ? (
                            <Lock className="w-4 h-4 text-neutral-600 fill-neutral-300 absolute right-2" />
                          ) : (
                            <></>
                          )}

                          {notification && notification[menu.url] && notification[menu.url]?.value && notification[menu.url]?.value.length > 0 && (
                            <Badge variant="outline" className="text-neutral-600 text-center">{notification[menu.url]?.value}</Badge>
                          )}
                        </Link>
                      </SidebarMenuButton>

                      {menu.items?.length ? (
                        <>
                          <CollapsibleTrigger asChild>
                            <SidebarMenuAction className={`${isActive ? 'rotate-90' : ''} ${menu.disable ? 'text-gray-400' : ''}`}>
                              <ChevronRight />
                              <span className="sr-only">Toggle</span>
                            </SidebarMenuAction>
                          </CollapsibleTrigger>
                          <CollapsibleContent  >
                            <SidebarMenuSub>
                              {menu.items?.map((subItem) => {
                                const isSubActive = currentPath.includes(subItem.url);
                                return (
                                  <SidebarMenuSubItem key={subItem.title}>
                                    <SidebarMenuSubButton asChild>
                                      <Link href={menu.disable ? '#' : subItem.url} className={`${menu.disable ? 'text-gray-400 pointer-events-none' : ''} ${isSubActive ? 'bg-neutral-100' : ''} text-sm`}>
                                        {subItem.title}

                                        {subItem.supposedAccessLevel && subItem.supposedAccessLevel > currentUser?.accessLevel ? (
                                          <Lock className="w-4 h-4 text-neutral-600 fill-neutral-300 absolute right-2" />
                                        ) : (
                                          <></>
                                        )}
                                      </Link>
                                    </SidebarMenuSubButton>
                                  </SidebarMenuSubItem>
                                );
                              })}
                            </SidebarMenuSub>
                          </CollapsibleContent>
                        </>
                      ) : null}
                    </SidebarMenuItem>
                  </Collapsible>
                </SidebarMenu>
              )
            })}
          </div>
        );
      })}
    </SidebarGroup>
  )
}
