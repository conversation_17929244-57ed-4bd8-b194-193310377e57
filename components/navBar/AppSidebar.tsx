"use client"

import * as React from "react"
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Card,
} from "lucide-react"
import { usePathname } from 'next/navigation';

import { NavSecondary } from "@/components/navBar/NavSecondary"
import { NavUser } from "@/components/navBar/NavUser"
import { NavMain } from "@/components/navBar/NavMain"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
} from "@/components/ui/sidebar"
import { useAuthStore } from "@/store/auth";
import { SIDE_BAR_MAIN_DATA_RAW } from "@/lib/constants/navigation";
import { getNotificationAction } from "@/actions/notification";
import { useEffect, useState } from "react";
import { useUIStore } from "@/store/ui";
import { useTranslations } from "next-intl";

export function renderSideBarMainData(currentUser: any) {
  let filteredSideBarMainData = [] as any;
  const t = useTranslations("Menu");

  SIDE_BAR_MAIN_DATA_RAW.forEach((item) => {
    if (item.accessLevel && currentUser?.accessLevel && currentUser?.accessLevel < item.accessLevel) {
      // Do nothing if access Level is not enough
    } else {
      let filteredSubMenus = item.menus.filter((menu) => {
        if (menu.accessLevel === undefined) {
          return true
        }
        if (currentUser?.accessLevel >= menu.accessLevel) {
          return true;
        }
        return false;
      }).map((menu: any) => {
        return {
          title: menu.title ? t(menu.title) : menu.title,
          url: menu.url,
          accessLevel: menu.accessLevel,
          icon: menu.icon,
          disable: menu.disable,
          items: menu.items?.map((item: any) => {
            return {
              title: item.title ? t(item.title) : item.title,
              url: item.url,
              accessLevel: item.accessLevel,
              supposedAccessLevel: item.supposedAccessLevel,
            }
          }),
        }
      })

      filteredSideBarMainData.push({
        title: item.title ? t(item.title) : item.title,
        url: item.url,
        accessLevel: item.accessLevel,
        menus: filteredSubMenus
      })
    }
  });

  return filteredSideBarMainData.map((item: any) => currentUser?.accessLevel && currentUser?.accessLevel >= item.accessLevel ? item : null).filter(Boolean);
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const t = useTranslations("Menu");

  const pathname = usePathname();
  const { currentUser } = useAuthStore();
  const [hydrated, setHydrated] = useState(false);
  const navBarSecondaryData = [
    {
      title: t("payment"),
      url: "/my/billing",
      icon: CreditCard,
    },
    {
      title: t("support"),
      url: "/my/support",
      icon: MessageCircleQuestion,
    },
  ];

  // 确保在组件挂载时设置 hydrated 状态, so you can read from the store first
  useEffect(() => {
    const unsub = useUIStore.persist.onFinishHydration(() => {
      setHydrated(true);
    });
    setHydrated(useUIStore.persist.hasHydrated()); // 如果已经 ready

    return () => {
      unsub(); // 清理监听器
    };
  }, []);

  const { notification, setNotification } = useUIStore();

  const getNotification = async () => {
    getNotificationAction().then((res) => {
      if (res.success) {
        setNotification(res.data);
      }
    });
  }

  useEffect(() => {
    if (hydrated && !notification) {
      getNotification();
    }
  }, [hydrated, notification]);

  return (
    <Sidebar
      className="top-(--header-height) h-[calc(100svh-var(--header-height))]!"
      {...props}
    >
      <SidebarContent>
        <NavMain currentUser={currentUser} items={renderSideBarMainData(currentUser) as any} currentPath={pathname} notification={notification} />
        
        <NavSecondary items={navBarSecondaryData} className="mt-auto" />
      </SidebarContent>

      <SidebarFooter className="border-t border-gray-200">
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  )
}
