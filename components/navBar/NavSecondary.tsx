import * as React from "react"
import { CircleGauge, User, type LucideIcon } from "lucide-react"
import Link from "next/link";

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useUIStore } from "@/store/ui"
import { PricingTier } from "@/lib/constants/pricingTier";
import { useAuthStore } from "@/store/auth";
import { useUserUsageStore } from "@/store/userUsage";
import { useTranslations } from "next-intl";
import {
  getTotalDailyUsageCount,
  DAILY_USAGE_FEATURES
} from "@/lib/utils/dailyUsageTracker";
import { useEffect, useState } from "react";
export function NavSecondary({
  items,
  ...props
}: {
  items: {
    title: string
    url: string
    icon: LucideIcon
  }[]
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const { todaySearchUsageData } = useUserUsageStore();
  const { currentUser } = useAuthStore();
  const [priceChangeHistoryUsage, setPriceChangeHistoryUsage] = useState(0);
  const [rentSearchUsage, setRentSearchUsage] = useState(0);

  const fetchPricingTier = () => {
    if (currentUser?.accessLevel) {
      return PricingTier[currentUser?.accessLevel as keyof typeof PricingTier];
    }
    return null;
  }

  // Update PriceChangeHistory and RentSearch usage counts
  useEffect(() => {
    const updateUsage = () => {
      const priceChangeCount = getTotalDailyUsageCount(DAILY_USAGE_FEATURES.PRICE_CHANGE_HISTORY);
      const rentSearchCount = getTotalDailyUsageCount(DAILY_USAGE_FEATURES.RENT_SEARCH);
      setPriceChangeHistoryUsage(priceChangeCount);
      setRentSearchUsage(rentSearchCount);
    };

    updateUsage();

    // Update every 5 seconds to catch real-time changes
    const interval = setInterval(updateUsage, 5000);

    return () => clearInterval(interval);
  }, []);

  const t = useTranslations("Menu");

  return (
    <SidebarGroup {...props}>
      <SidebarGroupContent className="border-t border-gray-200">
        <SidebarMenu>
          <SidebarMenuItem className="flex flex-row gap-2 items-center text-xs p-2 hover:bg-neutral-100 hover:rounded-md hover:cursor-pointer hover:p-2">
            <CircleGauge className="w-4 h-4" />
            {/* <SidebarMenuButton asChild size="sm">
              <a href="/my/account">
                <User />
                <span>アカウント</span>
              </a>
            </SidebarMenuButton> */}
            <Link href="/my/usage" className="flex flex-col text-xs gap-1">
              <div className="flex flex-row gap-2 items-center text-sm font-bold text-neutral-600">
                {t("usage")}
              </div>
              <div className="flex flex-row gap-2 items-center text-xs">
                {t("usageSearch")}:
                <span className={todaySearchUsageData > (fetchPricingTier()?.dailySearchCount || 0) ? "text-red-500" : ""}>{todaySearchUsageData} / {fetchPricingTier()?.dailySearchCount || "∞"} </span>
              </div>

              {/* Show PriceChangeHistory usage only for free users */}
              {currentUser?.accessLevel === 1 && (
                <div className="flex flex-row gap-2 items-center text-xs">
                  {t("usagePriceChangeHistory")}:
                  <span className={priceChangeHistoryUsage >= (fetchPricingTier()?.dailyPriceChangeHistoryCount || 3) ? "text-red-500" : ""}>
                    {priceChangeHistoryUsage} / {fetchPricingTier()?.dailyPriceChangeHistoryCount || 3}
                  </span>
                </div>
              )}

              {/* Show RentSearch usage only for free users */}
              {currentUser?.accessLevel === 1 && (
                <div className="flex flex-row gap-2 items-center text-xs">
                  {t("usageRentSearch")}:
                  <span className={rentSearchUsage >= (fetchPricingTier()?.dailyRentSearchCount || 5) ? "text-red-500" : ""}>
                    {rentSearchUsage} / {fetchPricingTier()?.dailyRentSearchCount || 5}
                  </span>
                </div>
              )}

            </Link>
          </SidebarMenuItem>

          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild size="sm">
                <a href={item.url}>
                  <item.icon />
                  <span>{item.title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
