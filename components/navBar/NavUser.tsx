import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  Download,
  DownloadIcon,
  Link,
  LogOut,
  Settings,
  Sparkles,
  UserIcon,
} from "lucide-react"
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { useRouter } from 'next/navigation'; // 导入路由
import { useAuthStore } from "@/store/auth"; // 导入 auth 状态管理
import { Badge } from "../ui/badge";
import { checkUserAccess } from "@/lib/accessCheck";
import { createNewSystemUserActivityAction } from "@/actions/systemUserActivity";
import { LARK_URLS } from "@/lib/thirdParty/lark";
import { sendLark } from "@/lib/thirdParty/lark";

export function NavUser() {
  const { currentUser, logout } = useAuthStore();
  const router = useRouter(); // 使用路由
  const { isMobile } = useSidebar()

  const name = currentUser?.name?.split(' ').map((name: string) => name.charAt(0).toUpperCase()).join('');

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={currentUser?.imageUrl || ''} alt={currentUser?.name || ''} />
                <AvatarFallback className="rounded-lg">
                  {name}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <div className="flex items-center mb-1">
                  <span className="truncate font-semibold">
                    {currentUser?.name}
                  </span>
                  {currentUser && <Badge variant="outline" className="ml-2">{checkUserAccess(currentUser).split(' ')[0]}</Badge>}
                </div>
                <span className="truncate text-xs">{currentUser?.email}</span>
              </div>
              <ChevronsUpDown className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={currentUser?.imageUrl || ''} alt={currentUser?.name || ''} />
                  <AvatarFallback className="rounded-lg">
                    {currentUser?.name?.split(' ').map((name: string) => name.charAt(0).toUpperCase()).join('')}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{currentUser?.name}</span>
                  <span className="truncate text-xs">{currentUser?.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={() => {
                router.push("/my/billing");
              }}>
                <CreditCard />
                支払い
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={() => {
                router.push("/my/account");
              }}>
                <UserIcon />
                アカウント
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                router.push("/app");
              }}>
                <DownloadIcon />
                アプリダウンロード
              </DropdownMenuItem>

              {/* <DropdownMenuItem>
                <Bell />
                通知
              </DropdownMenuItem> */}
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={async () => {
              createNewSystemUserActivityAction({
                data: {
                  eventType: "LOGOUT",
                  route: "/api/auth/logout",
                },
              });
              sendLark({
                message: `[auth][logout] Logout successful: ${currentUser?.email}`,
                url: LARK_URLS.USER_AQUISITION_CHANNEL,
              });
              fetch('/api/auth/logout', { method: 'POST' });
              logout();
              router.push('/');
            }}>
              <LogOut />
              ログアウト
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu >
  )
}
