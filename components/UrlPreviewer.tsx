"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Lo<PERSON>, Settings } from "lucide-react";
import IframeImage from "@/components/IframeImage";
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer";
import { fetchMetadataFromUrlAction } from "@/actions/helper/cheerio";
import { ScrollArea } from "@/components/ui/scroll-area";

export default function UrlPreviewer({
  inputUrl,
  setInputUrl,
  metaData,
  setMetaData,
}: {
  inputUrl: string;
  setInputUrl: (url: string) => void;
  metaData: any;
  setMetaData: (data: any) => void;
}) {
  const [url, setUrl] = useState("");

  const handleSearch = async () => {
    setUrl(inputUrl);

    const resMetadata = await fetchMetadataFromUrlAction(inputUrl);
    console.log(resMetadata)
    if (resMetadata.success) {
      setMetaData(resMetadata.data);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputUrl(e.target.value);
  };

  return (
    <div>
      <Drawer >
        <div className="flex flex-row gap-2 mb-2">
          <Input type="text" placeholder="URLを入力" value={inputUrl} onChange={handleInputChange} /> {/* 绑定输入值 */}
          <Button variant="outline" onClick={() => handleSearch()} disabled={!inputUrl}>検索</Button>
        </div>

        {url ? (
          <div className="flex flex-col gap-2">
            <div className="relative">
              <IframeImage url={url} />
              <DrawerTrigger className="w-10 h-10 absolute top-2 right-2 bg-neutral-900 text-white flex items-center justify-center rounded-full">
                <Settings className="w-5 h-5" />
              </DrawerTrigger>
            </div >
          </div>
        ) : (
          <div className="w-full h-[500px] bg-gray-200 flex items-center justify-center">
            <p>URLを入力してください</p>
          </div>
        )}

        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Meta Data</DrawerTitle>
          </DrawerHeader>
          <ScrollArea className="h-[calc(100vh-200px)] p-4 select-text">
            <div className="grid grid-cols-1 gap-2 my-2">
              <b>Title:</b> {metaData?.title || "No title"}
              <br />
              <b>Description:</b> {metaData?.description || "No description"}
              <br />
              <b>ogTitle:</b> {metaData?.ogTitle || "No ogTitle"}
              <br />
              <b>ogDescription:</b> {metaData?.ogDescription || "No ogDescription"}
              <br />
              <b>ogImage:</b> {metaData?.ogImage || "No ogImage"}
              {metaData?.ogImage && <img src={metaData?.ogImage} alt="ogImage" width={500} height={300} />}
              <br />
              <b>favicon:</b> {metaData?.favicon || "No favicon"}
              {metaData?.favicon && <img src={metaData?.favicon} alt="favicon" width={50} height={50} />}
            </div>
          </ScrollArea>
        </DrawerContent>
      </Drawer>
    </div>
  )
}
