"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";

export function PwaInstallButton() {
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [showWidget, setShowWidget] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Check if PWA is already installed (Standalone mode)
    if (window.matchMedia("(display-mode: standalone)").matches) {
      console.log("✅ PWA already installed");
      return;
    }

    const handleBeforeInstallPrompt = (e: any) => {
      e.preventDefault(); // Prevent default browser install prompt
      setDeferredPrompt(e); // Store the event
      setShowWidget(true); // Show the install widget
    };

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstall = () => {
    if (!deferredPrompt) return;

    deferredPrompt.prompt(); // Show install prompt
    deferredPrompt.userChoice.then((choiceResult: any) => {
      if (choiceResult.outcome === "accepted") {
        console.log("✅ User accepted the install prompt");
        toast({
          title: "🎉 App Installed",
          description: "You've successfully installed our PWA!",
        });
      } else {
        console.log("❌ User dismissed the install prompt");
      }
      setDeferredPrompt(null);
      setShowWidget(false);
    });
  };

  if (!showWidget) return null; // Don't show widget if not installable

  return (
    <Card className="fixed bottom-4 left-4 right-4 shadow-lg rounded-lg bg-white border flex items-center animate-fadeIn sm:hidden">
      <div className="flex flex-row p-4 w-full justify-between items-center pr-8">
        <p className="text-sm flex-1">📲 より良い体験のためにアプリをインストールしてください！</p>

        <Button onClick={handleInstall} variant="default">
          Install
        </Button>
      </div>

      <Button className="top-0 right-0 absolute " variant="ghost" size="icon" onClick={() => setShowWidget(false)}>
        ✖
      </Button>
    </Card>
  );
};

export default PwaInstallButton;