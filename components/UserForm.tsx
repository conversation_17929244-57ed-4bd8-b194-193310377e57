"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"; // 导入表单组件
import { Input } from "@/components/ui/input"; // 导入输入组件
import { Button } from "@/components/ui/button"; // 导入按钮组件
import { TllUserFormSchema, TllUserProps } from "@/lib/definitions/tllUser";
import { useState } from "react";
import { Loader2 } from "lucide-react";
import { Select, SelectTrigger, SelectContent, SelectItem, SelectValue } from '@/components/ui/select'; // 假设有Select组件

interface UserFormProps {
  onSubmit: (data: z.infer<typeof TllUserFormSchema>) => Promise<void>;
  defaultValues?: TllUserProps; // 可选的默认值
  isNewUser?: boolean;
}

const UserForm: React.FC<UserFormProps> = ({ onSubmit, defaultValues, isNewUser = false }) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof TllUserFormSchema>>({
    resolver: zodResolver(TllUserFormSchema),
    defaultValues: defaultValues || {
      id: "",
      name: "",
      email: "",
      source: "MANUAL",
      password: "",
      accessLevel: 1,
      imageUrl: "",
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={async (e) => {
        e.preventDefault();
        setIsLoading(true);
        await onSubmit(form.getValues());
        setIsLoading(false);
      }} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>名前:</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  {...field}
                  className="border p-2 w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>メール:</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  {...field}
                  className="border p-2 w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>パスワード:</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  {...field}
                  className="border p-2 w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="source"
          render={({ field }) => (
            <FormItem>
              <FormLabel>ソース:</FormLabel>
              <FormControl>
                <Input
                  type="text"
                  {...field}
                  className="border p-2 w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="accessLevel"
          render={({ field }) => (
            <FormItem>
              <FormLabel>アクセスレベル:</FormLabel>
              <FormControl>

                <Select
                  value={field.value.toString()}
                  onValueChange={(value) => field.onChange(Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="アクセスレベル" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Free</SelectItem>
                    <SelectItem value="10">Plus</SelectItem>
                    <SelectItem value="20">Pro</SelectItem>
                    <SelectItem value="30">Partner</SelectItem>
                    <SelectItem value="90">Admin</SelectItem>
                    <SelectItem value="99">Super Admin</SelectItem>
                  </SelectContent>
                </Select>

                {/* <Input
                  type="number"
                  {...field}
                  className="border p-2 w-full"
                  step="1" // 确保输入为整数
                  onChange={(e) => {
                    const value = parseInt(e.target.value);
                    if (!isNaN(value)) {
                      field.onChange(value);
                    }
                  }}
                /> */}
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="mt-4" disabled={isLoading}>
          {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : "保存"}
        </Button>
      </form>
    </Form>
  );
};

export default UserForm; 