import dayjs from "dayjs";
import { getPriceChangeHistory } from "./priceChangeUtilities";
import { Line, LineChart, YAxis, XAxis, Tooltip } from "recharts";

export const PriceChangeChartInTable = ({ record, height = 60, width = 160, showTooltip = true }: { record: any, height?: number, width?: number, showTooltip?: boolean }) => {
  const changes = getPriceChangeHistory(record.price, record.priceChanges)?.rawData?.priceChangesAsc;

  if (changes && changes.length >= 2) {
    const data = changes.map((change) => ({
      date: dayjs(change.recordDate).format('YYYY-MM-DD'), // 格式化日期
      price: change.price,
    })).reverse();

    return (
      <div
        style={{
          width: `${width}px`,
          height: `${height}px`,
          overflow: 'hidden',
        }}
      >
        <LineChart width={width} height={height} data={data}>
          <XAxis dataKey="date" tickLine={true} axisLine={false} hide={true} />
          <YAxis domain={['dataMin - 10', 'dataMax + 10']} tickLine={true} axisLine={false} hide={true} />
          {showTooltip && <Tooltip />}
          <Line type="monotone" dataKey="price" stroke="gray" strokeWidth={1} dot={true} />
        </LineChart>
      </div>
    );
  }

  return '';
};
