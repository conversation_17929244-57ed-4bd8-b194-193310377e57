import { Badge } from "@/components/ui/badge"; // 引入shadui的Badge组件
import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import dayjs from "dayjs";
import { getColor } from "@/lib/color";
import { Heart, LockIcon, Pencil } from "lucide-react";
import { getPriceChangeHistory, getLatestChangeValue, detectPriceIncreaseAfterOneMonth } from "./priceChangeUtilities";
import { PriceChangeChartInTable } from "./PriceChangeChartInTable";
import { PriceChangeSummaryInTable } from "./PriceChangeSummaryInTable";
import { getBuildingPrice } from "@/lib/helper/sekisan";
import { Button } from "@/components/ui/button";
import { compareAndRenderStar, renderStarRating } from "@/lib/userLambdaRecord/valueRanking";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";
import dayjsWithTz from "@/lib/thirdParty/dayjsWithTz";
import { useAuthStore } from "@/store/auth";
import Image from "next/image";

export const renderLock = () => (
  <Link
    href={`/my/billing`}
    className="inline-block font-semibold text-gray-400 tracking-normal relative cursor-pointer group"
  >
    <LockIcon className="w-4 h-4 text-neutral-500" />
    <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 rounded bg-gray-800 text-white text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
      有料プランで閲覧可能
    </span>
  </Link>
)

export const renderMask = (id: string) => (
  <Link
    href={`/ex/search/${id}`}
    target="_blank"
    className="inline-block font-semibold text-gray-400 tracking-normal relative cursor-pointer group"
  >
    ●●●
    <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 rounded bg-gray-800 text-white text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
      詳細はクリック または 有料プランで閲覧可能
    </span>
  </Link>
)

const userlambdaRecordTableColumnsCommon: ColumnDef<any>[] = [ // 修改为ExtendedColumn
  // {
  //   header: '#',
  //   cell: ({ row }: { row: any }) => {
  //     const image = row.original.image;

  //     return <Image src={row.original.image} alt={row.original.title} width={100} height={100} />
  //   }
  // },
  {
    header: '状態',
    cell: ({ row }: { row: any }) => (
      <Badge variant="outline" className={getStatus(row.original) === '成約' ? 'bg-red-100 border-none' : ''}>{getStatus(row.original)}</Badge>
    ),
  },
  {
    header: 'ランク',
    // accessorKey: 'overallStarLevel',
    cell: ({ row }) => {
      const score = row.original.propertyAnalysisResult?.overallStarLevel; // 确保类型为number
      return <div className="flex flex-col gap-1 items-center justify-center">
        {
          score > 0 ? <Badge variant="outline"
            style={{
              backgroundColor: getColor(score),
              color: 'white',
              borderColor: 'transparent',
            }}
          >{score}</Badge> : "-"
        }
        {/* <div className="flex flex-row gap-1 text-xs">
          {
            renderStarRating(row.original, row.original.recordType)
          }
        </div> */}
      </div>
    },
  },
  {
    header: 'タイプ',
    // accessorKey: 'recordSubType',
    cell: ({ row }) => {
      const recordSubType = row.original.recordSubType; // 确保类型为string

      return <div className="text-xs flex flex-col">
        <span>{row.original.recordType}</span>
        <span>{recordSubType?.slice(0, 5)}</span>
      </div>; // 使用shadui的Badge组件
    },
  },
  {
    header: '価格',
    // accessorKey: 'price',
    cell: ({ row }) => {
      //   let maxP = 0;

      //   if (row.original.priceChanges === undefined) {
      //     return row.original.price;
      //   }

      //   row.original.priceChanges.forEach((d: any) => {
      //     if (d.price > maxP) {
      //       maxP = d.price;
      //     }
      //   });

      return <span className="text font-bold">{row.original.price}</span>
    }
  },
  {
    header: 'ROI',
    cell: ({ row }) => {
      let currentUser = useAuthStore((state) => state.currentUser);

      if (row.original.yearlyIncome > 0) {
        const income = row.original.yearlyIncome; // 确保类型为number
        const price = row.original.price; // 确保类型为number
        return income > 0 ? (income / price * 100).toFixed(2) : ''; // 使用shadui的Badge组件
      } else {
        return "-";
      }
    },
  },
  {
    header: 'TLL目線',
    cell: ({ row, table }: { row: any, table: any }) => {
      const favMapIds = table.options.meta?.favMapIds || [];


      let bidPrice = row.original.analysisSimulationResults?.optimalBiddingPriceCalulation?.bidPrice;
      // show empty for now
      return <div className="flex flex-col items-center">
        {
          favMapIds.includes(row.original.id) &&
          <Heart className="w-3 h-3 text-red-500 fill-current" />
        }

        {bidPrice > 0 && <>
          <span className="">
            {bidPrice?.toFixed(0) || '-'}
          </span>
          <span className={`text-xs ${bidPrice ? (bidPrice / row.original.price * 100) > 120 ? 'text-green-800 font-bold' : bidPrice / row.original.price * 100 > 100 ? 'text-green-600' : bidPrice / row.original.price * 100 > 80 ? 'text-green-400' : 'text-neutral-200' : 'inherit'}`}>
            {bidPrice ? (bidPrice / row.original.price * 100).toFixed(1) + "%" : '-'}
          </span>
        </>}
        {row.original.bids?.length > 0 && <span className={`text-xs text-red-500`}>
          入札数: {row.original.bids?.length}
        </span>}
      </div>
    },
  },
  {
    header: "UPSIDE",
    cell: ({ row }) => {
      const roiAveragePriceLR = compareAndRenderStar({
        comparator: row.original.propertyAnalysisResult?.roiValuation?.lrPrice,
        basePrice: row.original.price
      });

      const upsidePrice = compareAndRenderStar({
        comparator: row.original.propertyAnalysisResult?.rentValuation?.priceAfterUpside,
        basePrice: row.original.price
      });

      // const upsidePrice = compareAndRenderStar(
      //   row.original.propertyAnalysisResult?.nearbyAvgRent,
      //   (row.original.price / (row.original.buildingSize || row.original.recordValues.unitArea || 1))
      // );


      const avgGfaPrice = compareAndRenderStar({
        comparator: row.original.propertyAnalysisResult?.gfaValuation?.avgPrice,
        basePrice: row.original.price
      });

      const avgSekisanPrice = compareAndRenderStar({
        comparator: row.original.propertyAnalysisResult?.sekisanValuation?.avgPrice,
        basePrice: row.original.price
      });

      const avgIsshuPrice = compareAndRenderStar({
        comparator: row.original.propertyAnalysisResult?.unitLandPriceValuation?.avgPrice,
        basePrice: row.original.price
      });

      return (
        <div className="text-center text-xs flex flex-col items-center">
          {row.original.recordType === 'BUILDING' && (
            <div className="flex items-center gap-1 flex-col">
              <div className="flex items-center gap-2">
                {roiAveragePriceLR} / {upsidePrice}
              </div>
              <div className="flex items-center gap-2">
                {avgGfaPrice} / {avgSekisanPrice}
              </div>
            </div>
          )}
          {row.original.recordType === 'HOUSE' && (
            <div className="flex items-center gap-2">
              {avgGfaPrice} / {avgIsshuPrice} / {avgSekisanPrice}
            </div>
          )}
          {row.original.recordType === 'LAND' && (
            <div className="flex items-center gap-2">
              {avgIsshuPrice} /  {avgSekisanPrice}
            </div>
          )}
        </div>
      );
    },
  },
  {
    header: '住所 | 最寄駅',
    accessorKey: 'address',
    cell: ({ row }) => {
      let data = row.original['nearestStationGroup'];
      let userCount = data?.['stationUserCombined2018'];
      let userCountDisplay = userCount !== undefined ? `${userCount}人` : '';

      return (
        <div className="flex flex-col">
          <span className="text-xs font-bold">
            {row.original.address.slice(0, 20)}
          </span>

          {row.original.recordType === 'MANSION' && <>
            {row.original.buildingId ? <Link href={`/an/mansion/${row.original.buildingId}`} target="_blank" className="text-xs font-bold">
              <span className="text-xs font-bold underline">
                建物名: {row.original.buildingName || "-"}
              </span>
            </Link> : <span className="text-xs font-bold">
              建物名: {row.original.buildingName || "-"}
            </span>}
          </>}

          <span className="text-gray-800 text-xs">
            {row.original['sourceData'] === 'REINS' ? (
              <>
                {row.original['transport']?.slice(0, 15) || row.original['nearestStation']?.slice(0, 15) || ''}

                <span className={`text-xs ${row.original['nearestStationWalkMinute'] <= 5 && row.original['nearestStationWalkMinute'] > 0 ? 'text-green-500' : row.original['nearestStationWalkMinute'] <= 15 && row.original['nearestStationWalkMinute'] > 5 ? '' : 'text-red-500'}`}>
                  <span>
                    {row.original['nearestStationWalkMinute'] === 999 ? '不明' : row.original['nearestStationWalkMinute'] + '分'}
                  </span>
                </span>
              </>
            ) : (
              <>
                {row.original['transport']?.slice(0, 12) || `${row.original['nearestStation']?.slice(0, 12) || ''}${row.original['nearestStationWalkMinute'] === 999 ? '' : row.original['nearestStationWalkMinute'] + '分'}`}
              </>
            )}
          </span>

          <span className={`text-xs ${userCount <= 30000 && userCount > 0 ? 'text-red-400' : 'text-neutral-400'}`}>
            駅利用者: {userCountDisplay}
          </span>
        </div>
      );
    },
  },
  {
    header: "土地(㎡)",
    cell: ({ row }) => {
      let currentUser = useAuthStore((state) => state.currentUser);

      return (
        <div className="text-xs flex flex-col items-center">
          <span
            className="text-sm font-bold"
            style={{
              color: 'black',
            }}
          >
            {row.original.landSize}
          </span>
          <span className="text-gray-800 text-xs">
            <span style={{ color: ["一低", "一中", "二低", "二中"].includes(row.original.landType) ? 'black' : 'green', margin: '0 2px' }}>
              {row.original.landType || '-'}
            </span>
            |
            <span style={{ color: row.original.landRight !== '所有権' ? 'red' : 'black', margin: '0 2px' }}>
              {row.original.landRight}
            </span>
            |
            <span style={{ margin: '0 2px' }}>
              {row.original.roadConnection || '-'}
              {row.original.roadConnectionFirstFacing || ""}
            </span>
          </span>

          <span className="text-gray-800 text-xs">
            <span className="text-gray-800 text-xs">
              建蔽率:{row.original.landBuildingCoverageRatio || '-'}/容積率:{row.original.landFloorAreaRatio || '-'}
            </span>
          </span>
        </div>
      );
    },
  },
  {
    header: "建物(㎡)",
    cell: ({ row }) => {
      return (
        <div className="text-xs flex flex-col items-center">
          <span
            className="text-sm font-bold"
            style={{
              color: 'black',
            }}
          >
            {row.original.buildingSize}
          </span>
          <span className="text-gray-800 text-xs" style={{
            color: row.original.buildingBuiltYear && row.original.buildingBuiltYear <= 1981 ? 'red' : 'black',
          }}>
            {row.original.buildingBuiltYear ?
              `${row.original.buildingBuiltYear} (残存耐用${getBuildingPrice({
                recordType: row.original.recordType,
                buildingMaterial: row.original.buildingMaterial,
                buildingBuiltYear: row.original.buildingBuiltYear,
                buildingSize: row.original.buildingSize || 0,
                unitArea: row.original.recordValues?.unitArea || 0,
              }).remainY}年)`
              : row.original.buildingBuiltYear || '-'}
          </span>

          <span className="text-gray-800 text-xs">
            {row.original?.buildingMaterial?.slice(0, 6) || '-'} | {row.original?.buildingLayout?.slice(0, 6) || '-'} | {row.original?.buildingLevel?.slice(0, 6) || row.original?.recordValues?.unitLevel?.slice(0, 6) || '-'}
          </span>
        </div>
      );
    },
  },
  {
    header: '変更要約',
    cell: ({ row }) => {
      return <PriceChangeSummaryInTable record={row.original} />
    }
  },
  {
    header: '価格変更履歴',
    cell: ({ row }) => {
      // Note this will hide p3 year data // 注意、これは3年前のデータを非表示にします
      return <PriceChangeChartInTable record={row.original} />
    }
  },
  {
    header: '簡易変更履歴',
    cell: ({ row }) => {
      // Note this will hide p3 year data // 注意、これは3年前のデータを非表示にします
      return <PriceChangeChartInTable record={row.original} showTooltip={false} />
    }
  },
  {
    header: '業者',
    cell: ({ row }) => {
      const record = row.original;
      return (
        <div className="text-xs flex flex-col items-center">
          <div>
            {record.sourceData}
          </div>

          <Link
            href={`/an/company/${getLatestChangeValue(record, 'companyId')}`}
            style={{
              color:
                record.priceChanges && getLatestChangeValue(record, 'broker')?.includes('有')
                  ? 'green'
                  : 'black',
            }}
          >
            {record.priceChanges ? getLatestChangeValue(record, 'broker') ? getLatestChangeValue(record, 'broker')?.slice(0, 5) : record.sourceData : '-'}
          </Link>
          <div className="flex flex-row gap-2">
            <span
              style={{
                color:
                  getLatestChangeValue(record, 'brokerType')?.indexOf('売主') > -1 ||
                    getLatestChangeValue(record, 'brokerType')?.indexOf('代理') > -1
                    ? 'green'
                    : 'black',
              }}
            >
              {record.priceChanges ? getLatestChangeValue(record, 'brokerType') : '-'}
            </span>
            <span>
              {record.priceChanges
                && getLatestChangeValue(record, 'canAdvertise') !== null && record.priceChanges
                && getLatestChangeValue(record, 'canAdvertise').length ?
                '広告' + getLatestChangeValue(record, 'canAdvertise')?.slice(0, 3)
                : '-'}
            </span>
          </div>

        </div>
      );
    },
  },
  {
    header: '作成更新',
    accessorKey: 'updatedAt',
    cell: ({ row }) => {
      return <div className="text-xs flex flex-col">
        <div className="flex flex-row gap-2">
          <span>作成:</span>
          <span>{dayjs(row.original.createdAt).format('YYYY-MM-DD')}</span>
          {dayjs().diff(dayjs(row.original.createdAt), 'day') < 7 && <span className="text-green-500">新規</span>}
        </div>
        {dayjs(row.original.updatedAt).format('YYYY-MM-DD') !== dayjs(row.original.createdAt).format('YYYY-MM-DD') && <div className="flex flex-row gap-2">
          <span>更新:</span>
          <span>{dayjs(row.original.updatedAt).format('YYYY-MM-DD')}</span>
          {dayjs().diff(dayjs(row.original.updatedAt), 'day') < 7 && <span className="text-green-500">新規</span>}
        </div>}
        {/* <div className="flex flex-row gap-2">
          <span>更新:</span>
          <span>{getLatestChangeValue(row.original, 'recordDate') ? dayjs(getLatestChangeValue(row.original, 'recordDate')).format('YYYY-MM-DD ') : "-"}</span>
        </div> */}
      </div>
    },
  },
];

export const userlambdaRecordTableColumnsCommonSearch: ColumnDef<any>[] = [
  {
    header: '#',
    accessorKey: 'index',
    cell: ({ row }) => {
      return <Link href={`/ex/search/${row.original.id}`} target="_blank" className="text-xs flex flex-row gap-2 underline">
        {row.index + 1}
      </Link>
    },
  },
  // ...userlambdaRecordTableColumnsCommon.filter((column) => !["簡易変更履歴"].includes(column.header as string)),
  ...userlambdaRecordTableColumnsCommon,
  {
    header: '操作',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row gap-2 justify-center items-center">
          <Link href={`/ex/search/${row.original.id}`} target="_blank"
          >
            <Button variant="outline" size="sm">
              詳細確認
            </Button>
          </Link>
        </div>
      )
    }
  },
];

export const userlambdaRecordTableColumnsCommonValuation: ColumnDef<any>[] = [
  {
    header: '#',
    accessorKey: 'index',
    cell: ({ row }) => {
      return <Link href={`/ex/valuation/${row.original.id}`}
        className="hover:text-blue-700 underline"
      >{row.index + 1}</Link>; // 使用shadui的Link组件
    },
  },
  ...userlambdaRecordTableColumnsCommon,
  {
    header: '操作',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row gap-2 justify-center items-center">
          <Link href={`/ex/valuation/${row.original.id}`}
          >
            <Button variant="outline" size="sm">
              詳細確認
            </Button>
          </Link>
        </div>
      )
    }
  },
];