import { ColumnDef } from "@tanstack/react-table";
import dayjs from "dayjs";
import { getLatestChangeValue } from "./priceChangeUtilities";
import { PriceChangeSummaryInTable } from "./PriceChangeSummaryInTable";
import { PriceChangeChartInTable } from "./PriceChangeChartInTable";

export const nearbySeiyakuColumnForBroker: ColumnDef<any>[] = [
  {
    header: '価格',
    accessorKey: 'price',
    cell: ({ row }) => {
      return <span className="text font-bold">{Math.round(row.original.price / 1000) * 1000}万円台</span>
    }
  },
  {
    header: '住所',
    accessorKey: 'address',
    cell: ({ row }) => {
      let data = row.original['nearestStationGroup'];
      let userCount = data?.['stationUserCombined2018'];
      let userCountDisplay = userCount !== undefined ? `${userCount}人` : '';

      return (
        <div className="flex flex-col">
          <span className="text-xs font-bold">
            {row.original.address}
          </span>
          <span className="text-gray-800 text-xs">
            最寄駅: {row.original['sourceData'] === 'REINS' ? (
              <span className={`text-xs ${row.original['nearestStationWalkMinute'] <= 15 && row.original['nearestStationWalkMinute'] > 0 ? '' : 'text-red-500'}`}>
                {row.original['transport']?.slice(0, 15) || row.original['nearestStation']?.slice(0, 15) || ''}
              </span>
            ) : (
              <>{row.original['transport']?.substr(0, 12)}
              </>
            )}
          </span>
        </div>
      );
    },
  },
  {
    header: "土地(㎡)",
    cell: ({ row }) => {
      const landSize = row.original.landSize;
      const roundedLandSize = landSize ? Math.round(landSize / 10) * 10 : '-';
      return (
        <div className="text-xs flex flex-col items-center">
          {roundedLandSize}
          <span className="text-gray-800 text-xs">
            <span style={{ color: ["一低", "一中", "二低", "二中"].includes(row.original.landType) ? 'black' : 'green', margin: '0 2px' }}>
              {row.original.landType || '-'}
            </span>
            |
            <span style={{ color: row.original.landRight !== '所有権' ? 'red' : 'black', margin: '0 2px' }}>
              {row.original.landRight}
            </span>
            |
            <span style={{ color: row.original.roadConnection ? 'black' : 'red', margin: '0 2px' }}>
              {row.original.roadConnection || '-'}
            </span>
          </span>
        </div>
      );
    },
  },
  {
    header: "建物(㎡)",
    cell: ({ row }) => {
      return (
        <div className="text-xs flex flex-col items-center">
          {Math.round((row.original.buildingSize || row.original.recordValues?.unitArea) / 10) * 10}
        </div>
      );
    },
  },
  // {
  //   header: '変更要約',
  //   cell: ({ row }) => {
  //     const sortedPriceChanges = row.original.priceChanges?.sort((a: any, b: any) => dayjs(a.recordDate).diff(dayjs(b.recordDate))) || [];

  //     let priceChangeCount = 0;
  //     let seiyakuCount = 0;
  //     let differentCompanyCount = new Set(
  //       sortedPriceChanges.map((priceChange: any) => priceChange.companyId)
  //     ).size;


  //     for (let i = 1; i < sortedPriceChanges.length; i++) {
  //       if (sortedPriceChanges[i].price !== sortedPriceChanges[i - 1].price) {
  //         priceChangeCount++;
  //       }
  //       if (sortedPriceChanges[i].status === '成約') {
  //         seiyakuCount++;
  //       }
  //     }

  //     return (
  //       <div className="text-xs text-neutral-500">
  //         <div>価格変更:
  //           <span className={priceChangeCount > 0 ? "text-red-500 font-bold" : ""}>{priceChangeCount}回 </span>
  //           | 成約:
  //           <span className={seiyakuCount > 0 ? "text-red-500 font-bold" : ""}>{seiyakuCount}回 </span>
  //         </div>
  //         {/* <div>
  //           仲介会社:<span className={differentCompanyCount > 0 ? "text-red-500 font-bold" : ""}> {differentCompanyCount}個 </span>
  //         </div> */}
  //       </div>
  //     );
  //   }
  // },
  {
    header: '変更履歴',
    cell: ({ row }) => {
      return <PriceChangeChartInTable record={row.original} height={40} width={160} />
    }
  },
  // {
  //   header: '成約時期',
  //   accessorKey: 'updatedAt',
  //   cell: ({ row }) => {
  //     return <div className="text-xs">
  //       <span>{dayjs(getLatestChangeValue(row.original, 'recordDate')).format('YYYY-MM')}</span>
  //     </div>
  //   },
  // },
]