import dayjs from "dayjs";

export const getLatestRecordPriceChange = (priceChanges: any) => {
  if (!priceChanges || priceChanges.length === 0) return null;
  return priceChanges.reduce(
    (latest: any, current: any) => (current.recordDate > latest ? current.recordDate : latest),
    priceChanges[0].recordDate,
  );
};

export const getLatestChangeValue = (record: any, field: string) => {
  // if lastest field is not null, then use latest, else use the first one
  let largestChangeDate = getLatestRecordPriceChange(record?.priceChanges);

  let recordToUse = record?.priceChanges?.find((r: any) => r.recordDate === largestChangeDate);

  let secondRecord =
    record?.priceChanges?.length >= 2 ? record.priceChanges[record.priceChanges.length - 2] : null;
  // let recordToUse = latest[field] !== null ? latest : record.recordValues; // INFO: don ot use the latest fields any more

  let res = null;

  if (recordToUse) {
    if (field === 'brokerType') {
      res =
        recordToUse?.brokerType?.indexOf('オーナーチェンジ') > -1
          ? recordToUse?.brokerType?.replace('オーナーチェンジ', 'OC')
          : recordToUse?.brokerType;
    } else if (field === 'broker') {
      if (recordToUse.status === '成約') {
        return secondRecord === null
          ? ''
          : secondRecord.company?.fullName?.slice(0, 7) || "-";
      } else {
        res = recordToUse.company?.fullName?.slice(0, 7) || "-";
      }
    } else if (field === 'recordDate') {
      res = dayjs(recordToUse[field]).format("YYYY/MM/DD");
    } else {
      res = recordToUse[field];
    }
  }

  return res;
};

/**
 * Detects if there is a price increase of at least 10% that occurs at least 1 month after the previous price.
 * This identifies a 再販 (resale) case.
 * The function returns the price before the increase starts.
 *
 * @param {Array} priceChanges - Array of price change records, each with a price and recordDate.
 * @returns {number|null} - The price before the increase starts, or null if no such pattern is found.
 */
export const detectPriceIncreaseAfterOneMonth = (priceChanges: any) => {
  if (!priceChanges || priceChanges.length === 0) return null;

  let filteredPriceChanges = priceChanges?.filter((priceChange: any) => priceChange.recordDate > dayjs().subtract(3, 'year').toDate());

  if (!filteredPriceChanges || filteredPriceChanges.length < 2) return null;

  for (let i = 1; i < filteredPriceChanges.length; i++) {
    // Check if the price increase is at least 10% and occurs at least 1 month after the previous price
    if (
      filteredPriceChanges[i].price > filteredPriceChanges[i - 1].price * 1.1 &&
      dayjs(filteredPriceChanges[i].recordDate).diff(dayjs(filteredPriceChanges[i - 1].recordDate), 'month') >= 1
    ) {
      return filteredPriceChanges[i - 1].price;
    }
  }

  return null;
};

export const getPriceChangeHistory = (currentPrice: number, priceChanges: any): {
  rawData: {
    priceChangesAsc: any[];
    priceChangeRecent: number | null;
    priceChangePerc: number | null;
    priceChangePrev: number | null;
    priceChangePrevDate: string | null;
    threeMonthHighest: number;
    threeMonthPerc: number | null;
    allTimeHighest: number;
    allTimePerc: number | null;
    allTimeDiff: number | null;
  };
  output: {
    changeString: string,
    priceChangeRecent: React.ReactNode,
    threeMonthPerc: React.ReactNode,
    allTimePerc: React.ReactNode,
  };
} | null => {
  // Filter out those > 3 years when calculating
  if (!priceChanges || priceChanges.length === 0) return null;

  priceChanges = priceChanges.filter((priceChange: any) => priceChange.recordDate > dayjs().subtract(3, 'year').toDate());

  if (!priceChanges) return null;

  let priceChangeRecent: number | null = null;
  let priceChangePerc: number | null = null;
  let priceChangePrev: number | null = null;
  let priceChangePrevDate: string | null = null;
  let threeMonthHighest = 0;
  let threeMonthPerc: number | null = null;
  let allTimeHighest = 0;
  let allTimePerc: number | null = null;
  let allTimeDiff: number | null = null;
  let changeString = '';

  let priceChangesAsc =
    priceChanges && priceChanges?.length
      ? [...priceChanges]
        // .filter((r) => dayjs().diff(dayjs(r.recordDate), 'days') > 1)
        .sort((a, b) => (a.recordDate > b.recordDate ? 1 : -1))
      : []; // last one most recent

  priceChangesAsc.reverse().forEach((d) => {
    if (priceChangeRecent === null && d.price !== currentPrice) {
      priceChangeRecent = currentPrice - d.price;
      priceChangePerc = priceChangeRecent / d.price;
      priceChangePrev = d.price;
      priceChangePrevDate = d.recordDate;
      priceChangePerc = priceChangeRecent / d.price;
    }

    if (allTimeHighest < d.price) {
      allTimeHighest = d.price;
      allTimeDiff = currentPrice - allTimeHighest;
      allTimePerc = (currentPrice - allTimeHighest) / allTimeHighest;
    }
  });

  // Get T3M price
  priceChangesAsc
    .filter((r) => dayjs().diff(dayjs(r.recordDate), 'days') <= 90)
    .forEach((d) => {
      if (threeMonthHighest < d.price) {
        threeMonthHighest = d.price;
        threeMonthPerc = (currentPrice - threeMonthHighest) / threeMonthHighest;
      }
    });

  // Show the change string
  let lastPrice = 0;
  priceChangesAsc.forEach((d) => {
    if (lastPrice !== d.price) {
      changeString += `${d.price}(${dayjs(d.recordDate).format("YYYY-MM-DD")}),`;
      lastPrice = d.price;
    }
  });

  // Result string, recent change, 3 m change
  return {
    rawData: {
      priceChangesAsc,
      priceChangeRecent,
      priceChangePrev,
      priceChangePrevDate,
      priceChangePerc,
      threeMonthHighest,
      threeMonthPerc,
      allTimeHighest,
      allTimeDiff,
      allTimePerc,
    },
    output: {
      changeString,
      priceChangeRecent: priceChanges.length === 1 || priceChangePerc === 0 ? (
        '-'
      ) : (
        <span
          style={{
            color: priceChangePerc !== null && priceChangePerc > 0 ? 'red' : 'green',
            fontWeight: priceChangePerc !== null && priceChangePerc * 100 <= -10 ? 'bold' : 'normal',
          }}
        >
          {priceChangeRecent}

          {priceChangePerc !== null && (
            <span style={{ fontSize: '12px' }}>
              ({(priceChangePerc !== null ? (priceChangePerc * 100).toFixed(1) : 0)}%)</span>
          )}
        </span>
      ),
      threeMonthPerc: threeMonthPerc !== 0 ? (
        <span
          style={{
            color: threeMonthPerc !== null && threeMonthPerc > 0 ? 'red' : 'green',
            fontWeight: threeMonthPerc !== null && threeMonthPerc < (priceChangePerc !== null && priceChangePerc * 100 || 0) ? 'bold' : 'normal',
          }}
        >
          {threeMonthPerc !== null && (
            <>
              {threeMonthPerc}
              <span style={{ fontSize: '12px' }}>
                ({(threeMonthPerc !== null ? (threeMonthPerc * 100).toFixed(1) : 0)}%)</span>
            </>
          )}
        </span>
      ) : (
        ''
      ),
      allTimePerc: allTimePerc !== 0 ? (
        <span
          style={{
            color: allTimePerc !== null && allTimePerc > 0 ? 'red' : 'green',
            fontWeight: allTimePerc !== null && allTimePerc < (priceChangePerc !== null && priceChangePerc * 100 || 0) ? 'bold' : 'normal',
          }}
        >
          {allTimeDiff}
          <span style={{ fontSize: '12px' }}>({(allTimePerc !== null && allTimePerc * 100 || 0).toFixed(1)}%)</span>
        </span>
      ) : (
        ''
      ),
    },
  };
};
