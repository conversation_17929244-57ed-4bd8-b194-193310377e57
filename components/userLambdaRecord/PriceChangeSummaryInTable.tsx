import dayjs from "dayjs";
import { Badge } from "../ui/badge";
import { detectPriceIncreaseAfterOneMonth, getPriceChangeHistory } from "./priceChangeUtilities";


export const PriceChangeSummaryInTable = ({ record }: { record: any }) => {
  const sortedPriceChanges = record.priceChanges?.sort((a: any, b: any) => dayjs(a.recordDate).diff(dayjs(b.recordDate))) || [];

  let priceChangeCount = 0;
  let seiyakuCount = sortedPriceChanges?.filter((priceChange: any) => priceChange.status === '成約').length;

  for (let i = 1; i < sortedPriceChanges.length; i++) {
    if (sortedPriceChanges[i].price !== sortedPriceChanges[i - 1].price) {
      priceChangeCount++;
    }
  }

  let res1 = getPriceChangeHistory(record.price, record.priceChanges)?.output.priceChangeRecent;
  let res1Raw = getPriceChangeHistory(record.price, record.priceChanges)?.rawData.priceChangeRecent;
  let res2 = getPriceChangeHistory(record.price, record.priceChanges)?.output.allTimePerc;
  let res2Raw = getPriceChangeHistory(record.price, record.priceChanges)?.rawData.allTimeDiff;


  return (
    <div className="text-xs text-neutral-500">

      {/* {sortedPriceChanges.length > 1 && <div>掲載履歴: {sortedPriceChanges.length}回 </div>} */}
      {priceChangeCount > 0 && <div>
        価格変更<span className={priceChangeCount > 0 ? "text-red-300 font-bold" : ""}>{priceChangeCount}回</span>

        {seiyakuCount > 0 && <span className="ml-1">
          成約<span className={seiyakuCount > 0 ? "text-red-500 font-bold" : ""}>{seiyakuCount}回</span>
        </span>}
      </div>}

      <div className="flex flex-col text-xs">
        {/* {detectPriceIncreaseAfterOneMonth(record.priceChanges) !== null && (
          <div
            className="text-bold text-xs"
          >
            [再販]
          </div>
        )} */}
        {res1Raw !== null && <div>
          直近:{res1}
        </div>}
        {res2Raw !== null && res2Raw !== 0 && res1Raw !== null && res1Raw !== 0 && res2Raw !== res1Raw && <div>
          全期:{res2}
        </div>}
      </div>

      {/* {differentCompanyCount > 1 && <div>
        仲介会社:<span className={differentCompanyCount > 1 ? "" : ""}> {differentCompanyCount}社 </span>
      </div>} */}
    </div>
  );
}