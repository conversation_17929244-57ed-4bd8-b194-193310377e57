import { ColumnDef } from "@tanstack/react-table";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { userlambdaRecordTableColumnsCommonSearch, userlambdaRecordTableColumnsCommonValuation } from "./userlambdaRecordTableColumnsCommon";
import { calculateAvgPrice, getDiff, renderStarRating } from "@/lib/userLambdaRecord/valueRanking";
import { Badge } from "@/components/ui/badge";
import { getStatus } from "@/lib/userLambdaRecord/getStatus";
import { LockIcon } from "lucide-react";
import { renderMask, renderLock } from "./userlambdaRecordTableColumnsCommon";
import dayjs from "dayjs";
import Link from "next/link";

export const paddColumnWithDistance = (columns: ColumnDef<any>[]) => {
  return [
    {
      header: '距離(m)',
      accessorKey: 'distance',
      cell: ({ row }: { row: any }) => {
        return <div>{row.original.distance === -1 && row.original.recordType === "MANSION" ? "本建物" : row.original.distance?.toFixed(0)}</div>;
      }
    },
    ...columns
  ]
}

export const maskStatus = (columns: ColumnDef<any>[]) => {
  return columns.map((column) => {
    if (column.header === "状態") {
      return {
        ...column,
        cell: ({ row }: { row: any }) => {
          return <Badge variant="outline" className={getStatus(row.original) === '成約' ? 'bg-red-100 border-none' : ''}>
            {getStatus(row.original)}
          </Badge>
        }
      }
    }
    return column;
  })
}

export const replaceRankAndPrice = ({ columns, currentUser }: { columns: ColumnDef<any>[], currentUser: TllUserProps }) => {
  return columns.map((column) => {
    if (currentUser?.accessLevel && currentUser?.accessLevel < 10 && column.header === "状態") {
      return {
        header: '販売状況',
        cell: ({ row }: { row: any }) => {
          return renderLock()
        }
      };
    }

    if (column.header === "ランク") {
      return [{
        header: '割安度(%)',
        cell: ({ row }: { row: any }) => {
          let res = renderStarRating(row.original, row.original.recordType)
          const comparator = calculateAvgPrice(row.original, row.original.recordType);

          if (!comparator) {
            return <div className="text-gray-200">
              データ取得中
            </div>
          }

          return <div>
            {/* {currentUser?.accessLevel && currentUser?.accessLevel >= 10 ? 
            res : 
            renderMask(row.original.id)} */}
            {res}
          </div>;
        }
      }, {
        header: '割安額(万円)',
        cell: ({ row }: { row: any }) => {
          const comparator = calculateAvgPrice(row.original, row.original.recordType);

          let diff = row.original.price - comparator

          if (!comparator || !diff) {
            return ""
          }

          return <div className="text-gray-600">
            {/* {currentUser?.accessLevel && currentUser?.accessLevel >= 10 ?
              <> */}
                {diff > 0 && "▲"}
                {Math.abs(diff).toFixed(0)}
              {/* </> : renderMask(row.original.id)} */}
          </div>;
        }
      }]
    }

    // if (column.header === "価格") {
    //   return {
    //     header: '価格',
    //     cell: ({ row }: { row: any }) => {
    //       return <Link
    //         href={`/ex/search/${row.original.id}`}
    //         target="_blank">
    //         <div className="font-bold">
    //           {Math.round(row.original.price / 1000) * 1000}
    //         </div>
    //         <div className="text-xs text-neutral-500 inline-block text-gray-400 tracking-normal relative cursor-pointer group">
    //           万円台
    //           <span className="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 px-2 py-1 rounded bg-gray-800 text-white text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
    //             詳細価格はクリック または 有料プランで閲覧可能
    //           </span>
    //         </div>
    //       </Link>
    //     }
    //   };
    // }
    return column;
  });
}

export const getUserlambdaRecordSearchTableColumnsBasedOnAccessLevel = ({ currentUser, recordType, isForReport = false }: { currentUser: TllUserProps, recordType?: string, isForReport?: boolean }) => {
  let col = [] as ColumnDef<any>[];

  if (isForReport) {
    return [
      ...maskStatus(userlambdaRecordTableColumnsCommonSearch.filter((column) => !["TLL目線", "簡易変更履歴", "業者", "変更額", "価格変更履歴", "UPSIDE", "ランク", "変更要約", "作成更新", "操作"].includes(column.header as string))),
      {
        header: '記録月',
        cell: ({ row }: { row: any }) => {
          return <div className="text-sm flex flex-col">
            <span>{dayjs(row.original.createdAt).format('YYYY-MM')}</span>
          </div>
        },
      },
    ];
  }

  // 変更履歴 is for all user
  // 価格変更履歴 is for >= 30 user
  if (currentUser?.accessLevel && currentUser?.accessLevel >= 90) {
    col = userlambdaRecordTableColumnsCommonSearch.filter((column) => !["簡易変更履歴"].includes(column.header as string));
  } else if (currentUser?.accessLevel && currentUser?.accessLevel >= 30) {
    col = maskStatus(replaceRankAndPrice({ columns: userlambdaRecordTableColumnsCommonSearch.filter((column) => !["TLL目線", "UPSIDE", "簡易変更履歴"].includes(column.header as string)), currentUser }).flat())
  } else {

    col = [
      ...maskStatus(replaceRankAndPrice({ columns: userlambdaRecordTableColumnsCommonSearch.filter((column) => !["TLL目線", "業者", "変更額", "価格変更履歴", "UPSIDE"].includes(column.header as string)), currentUser }).flat())
    ];
  }

  if (recordType === "MANSION") {
    col = col.filter((column) => ["土地(㎡)"].indexOf(column.header as string) === -1);
  }

  if (recordType === "LAND") {
    col = col.filter((column) => ["建物(㎡)", "ROI"].indexOf(column.header as string) === -1);
  }

  return col;
}

export const getUserlambdaRecordValuationTableColumnsBasedOnAccessLevel = (currentUser: TllUserProps) => {
  const commonExcludingColumns = ["状態", '変更額', 'TLL目線', '簡易変更履歴', "変更要約", '業者', "ランク", "価格変更履歴", "UPSIDE"];

  const padResults = (columns: ColumnDef<any>[]) => {
    return [
      {
        header: '割安度',
        cell: ({ row }: { row: any }) => {
          return <div>{
            renderStarRating(row.original, row.original.recordType)
          }
          </div>;
        }
      },
      ...columns
    ]
  }

  if (currentUser?.accessLevel && currentUser?.accessLevel >= 90) {
    return padResults(userlambdaRecordTableColumnsCommonValuation);
  }

  else if (currentUser?.accessLevel && currentUser?.accessLevel >= 30) {
    return padResults(maskStatus(userlambdaRecordTableColumnsCommonValuation.filter((column) => !commonExcludingColumns.includes(column.header as string))))
  }

  return padResults(maskStatus(userlambdaRecordTableColumnsCommonValuation.filter((column) => !commonExcludingColumns.includes(column.header as string))))
}