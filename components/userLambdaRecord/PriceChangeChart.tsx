import { Line } from "recharts";
import { CartesianGrid, XAxis, YAxis } from "recharts"; // 添加 YAxis
import { type ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { UserLambdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { LineChart } from "recharts";

export default function PriceChangeChart({ priceChanges, showLabel = true }: { priceChanges: UserLambdaRecordPriceChangeProps[], showLabel?: boolean }) {
  const chartData = priceChanges?.map(change => ({
    recordDate: change.recordDate,
    price: change.price
  })).sort((a, b) => new Date(a.recordDate).getTime() < new Date(b.recordDate).getTime() ? -1 : 1);

  const chartConfig = {
    a: {
      label: "Desktop",
      color: "hsl(var(--chart-1))",
    },
  } satisfies ChartConfig

  const getDateDomain = (data: any) => {
    const dates = data.map((d: any) => new Date(d.recordDate).getTime());
    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));
    return [new Date(minDate.setMonth(minDate.getMonth() - 1)).getTime(), new Date(maxDate.setMonth(maxDate.getMonth() + 1)).getTime()]; // 设置 X 轴的范围为开始日期前一个月到结束日期后一个月
  };

  const getPriceDomain = (data: any) => {
    const values = data.map((d: any) => d.price);
    const min = Math.min(...values);
    const max = Math.max(...values);
    return [min - (max - min) * 0.2, max + (max - min) * 0.2]; // Keeping 80% of the range for price
  };

  return (
    <ChartContainer config={chartConfig} className="w-full h-full bg-neutral-100 overflow-hidden" >
      <LineChart
        accessibilityLayer
        data={chartData}
        margin={{
          left: 2,
          right: 2,
        }}
      >
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey="recordDate"
          tickLine={false}
          axisLine={showLabel} // 根据 showLabel 控制 X 轴标签的显示
          tickMargin={1}
          domain={getDateDomain(chartData)} // 更新 X 轴的范围
          hide={!showLabel} // 根据 showLabel 控制 X 轴的隐藏
        />
        <YAxis
          domain={getPriceDomain(chartData)} // 更新 Y 轴的范围
          tickLine={false}
          axisLine={showLabel} // 根据 showLabel 控制 Y 轴标签的显示
          tickFormatter={(value) => value.toLocaleString()}
          width={50} // 减小 Y 轴标签的宽度
          hide={!showLabel} // 根据 showLabel 控制 Y 轴的隐藏
        />
        <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
        <Line dataKey="price" type="linear" stroke="gray" strokeWidth={1} dot={true} />
      </LineChart>
    </ChartContainer>
  );
}