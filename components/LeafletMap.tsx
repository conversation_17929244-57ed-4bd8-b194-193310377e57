"use client"; // ✅ Ensure client-side rendering

import { useEffect, useState } from "react";
import "leaflet/dist/leaflet.css";
// @ts-ignore
import { LatLngExpression } from "leaflet";
import { Loader } from "lucide-react";
import dynamic from "next/dynamic";
import debounce from "lodash-es/debounce";
// @ts-ignore
// import L from 'leaflet'; // 确保引入 L

//FIXME: note this will break the hash showMap=true
let L: any; // Declare L globally

if (typeof window !== "undefined") {
  L = require("leaflet"); // ✅ Import Leaflet only in the browser
}
import Link from "next/link";
import { toast } from "@/hooks/use-toast";

// ✅ Dynamically import Leaflet components to avoid SSR issues
const MapContainer = dynamic(
  () => import("react-leaflet").then((mod) => mod.MapContainer),
  { ssr: false }
);
const TileLayer = dynamic(() => import("react-leaflet").then((mod) => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import("react-leaflet").then((mod) => mod.Marker), { ssr: false }) as any;
const Popup = dynamic(() => import("react-leaflet").then((mod) => mod.Popup), { ssr: false });
// const useMap = dynamic(() => import("react-leaflet").then((mod) => mod.useMap), { ssr: false });
import { useMap } from "react-leaflet"; // ✅ 替换原来 dynamic 的方式
import { Button } from "@react-email/components";


// This is useful, e.g. when search bar pick a new address
// is not updated immediately, so we need to recenter the map
// after the data is updated
function Recenter({ center }: { center: [number, number] }) {
  const map = useMap(); // ✅ 替换原来 dynamic 的方式

  useEffect(() => {
    if (center) {
      map.setView(center, map.getZoom(), {
        animate: true,
      });
    }
  }, [center]);

  return null;
}

function BoundsWatcher({
  onBoundsChange,
  isMapLocked,
}: {
  onBoundsChange: (bounds: any) => void;
  isMapLocked: boolean;
}) {
  const map = useMap();

  // ✅ 根据锁状态，开启或关闭交互
  useEffect(() => {
    if (isMapLocked) {
      map.dragging.disable();
      map.touchZoom.disable();
      map.doubleClickZoom.disable();
      map.scrollWheelZoom.disable();
      map.boxZoom.disable();
      map.keyboard.disable();
    } else {
      map.dragging.enable();
      map.touchZoom.enable();
      map.doubleClickZoom.enable();
      map.scrollWheelZoom.enable();
      map.boxZoom.enable();
      map.keyboard.enable();
    }
  }, [isMapLocked]);

  // ✅ 移动结束时触发防抖回调（前提是地图已解锁）
  const debouncedOnChange = debounce(() => {
    if (isMapLocked) return; // 地图锁定时忽略
    // Don't close popup automatically - let user interactions handle it
    const bounds = map.getBounds();

    const topLeft = {
      lat: bounds.getNorth(),
      lng: bounds.getWest(),
    };
    const bottomRight = {
      lat: bounds.getSouth(),
      lng: bounds.getEast(),
    };

    onBoundsChange({ topLeft, bottomRight });
  }, 1000);

  useEffect(() => {
    let isDragging = false;
    let isZooming = false;

    const handleDragStart = () => { isDragging = true; };
    const handleZoomStart = () => { isZooming = true; };

    const handleMoveEnd = () => {
      // Only trigger bounds change if it was caused by dragging or zooming
      if (isDragging || isZooming) {
        debouncedOnChange();
      }
      isDragging = false;
      isZooming = false;
    };

    map.on("dragstart", handleDragStart);
    map.on("zoomstart", handleZoomStart);
    map.on("moveend", handleMoveEnd);

    return () => {
      map.off("dragstart", handleDragStart);
      map.off("zoomstart", handleZoomStart);
      map.off("moveend", handleMoveEnd);
      debouncedOnChange.cancel();
    };
  }, [map, isMapLocked]);

  return null;
}



export default function LeafletMap({
  height = "360px", center, zoom = 13, minZoom = 11, data, legend, onBoundsChange, useCustomIcon = false, isMapLocked = false, onMarkerClick, selectedMarkerId
}:
  {
    height?: string;
    center?: [number, number]; // ✅ Use LatLngExpression type
    zoom?: number;
    minZoom?: number;
    data?: {
      name: string;
      link?: string;
      colorDot?: string;
      coordinate: LatLngExpression;
      popUpRenderFunction?: () => React.ReactNode;
      textToShow?: {
        title: string;
        description: string;
      };
      id?: string;
    }[];
    legend?: any;
    onBoundsChange?: (bounds: any) => void;
    useCustomIcon?: boolean;
    isMapLocked?: boolean;
    onMarkerClick?: (data: any) => void;
    selectedMarkerId?: string;
  }
) {
  const [mounted, setMounted] = useState(false);
  const [mapCenter, setMapCenter] = useState<[number, number]>(center || [35.6895, 139.6917]); // default shinjuku

  useEffect(() => {
    if (!center) {
      if ("geolocation" in navigator) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            setMapCenter([position.coords.latitude, position.coords.longitude]);
            toast({
              title: "位置情報の取得に成功しました。",
              description: "Geolocation Success:" + position.coords.latitude + "," + position.coords.longitude,
              variant: "default"
            });
          },
          (err) => {
            toast({
              title: "位置情報の取得に失敗しました。",
              description: "Geolocation Error:",
              variant: "destructive"
            });
            console.error("Geolocation Error:", err);
          }
        );
      } else {
        toast({
          title: "このブラウザは位置情報をサポートしていません。",
          variant: "destructive"
        });
      }
    }

    setMounted(true); // ✅ Fix hydration mismatch
  }, []);

  const [lastExternalCenter, setLastExternalCenter] = useState<[number, number] | null>(null);

  useEffect(() => {
    if (
      center &&
      (!lastExternalCenter || center[0] !== lastExternalCenter[0] || center[1] !== lastExternalCenter[1])
    ) {
      setMapCenter(center);
      setLastExternalCenter(center);
    }
  }, [center]);

  if (!mounted) return <div className="flex justify-center items-center h-full w-full">
    <Loader className="w-4 h-4 animate-spin" />
  </div> // Prevent SSR issues

  return (
    <MapContainer
      // @ts-ignore 
      center={mapCenter as LatLngExpression} // ✅ Ensure correct type
      zoom={zoom}
      minZoom={minZoom || 11}
      style={{ height: height, width: "100%", position: "relative", zIndex: 0 }}
      scrollWheelZoom={false}
    >
      {isMapLocked && <div className="absolute top-2 left-2 z-1000 text-xs bg-blue-100 px-2 py-1 rounded flex flex-row items-center gap-2">
        <Loader className="w-4 h-4 animate-spin" />
        データ読み込み中...</div>}

      <Recenter center={mapCenter} />

      {onBoundsChange && <BoundsWatcher onBoundsChange={onBoundsChange} isMapLocked={isMapLocked} />}

      <TileLayer
        url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
      />

      <Marker position={mapCenter as LatLngExpression} icon={L.icon({
        iconUrl: "/assets/map/blue-dot.png",
        iconSize: [40, 40],
        iconAnchor: [20, 40],
        popupAnchor: [1, -34],
      })}> {/* 使用 icon 属性 */}
        <Popup>中心点</Popup>
      </Marker>

      {data?.map((d, index) => {
        const isSelected = selectedMarkerId && d.id === selectedMarkerId;

        return (
          <Marker
            key={index}
            position={d.coordinate}
            eventHandlers={{
              click: () => {
                console.log("🔥 Marker clicked:", d, index);
                onMarkerClick?.(d);
              }
            }}
            icon={useCustomIcon && d.textToShow ? L.divIcon({
              html: `
       <div class="map-pin-custom" style="
        position: relative;
        background: ${isSelected ? "linear-gradient(135deg, #3b82f6, #1d4ed8)" : 'linear-gradient(135deg, #f8fafc, #e2e8f0)'};
        padding: 4px 6px;
        border: 2px solid ${isSelected ? '#1d4ed8' : '#64748b'};
        border-radius: 8px;
        font-size: 12px;
        text-align: center;
        color: ${isSelected ? 'white' : 'black'};
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: ${isSelected ? 'scale(1.15)' : 'scale(1)'};
        box-shadow: ${isSelected ? '0 8px 16px rgba(59, 130, 246, 0.4)' : '0 4px 8px rgba(0,0,0,0.1)'};
        font-weight: ${isSelected ? '600' : '500'};
        min-width: 80px;
        z-index: ${isSelected ? '1000' : '100'};
      ">
        <div style="
          font-size: 9px;
          opacity: 0.9;
          margin-bottom: 1px;
        ">
          ${d.textToShow ? d.textToShow.title : ""}
        </div>
        <div style="
          font-size: 11px;
          font-weight: bold;
        ">
          ${d.textToShow ? d.textToShow ? d.textToShow.description : "" : ""}
        </div>
        <div style="
          position: absolute;
          bottom: -9px;
          left: 50%;
          margin-left: -8px;
          width: 0;
          height: 0;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          border-top: 8px solid ${isSelected ? '#1d4ed8' : '#64748b'};
        "></div>
        <div style="
          position: absolute;
          bottom: -7px;
          left: 50%;
          margin-left: -6px;
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 6px solid ${isSelected ? '#3b82f6' : '#f8fafc'};
        "></div>
      </div>
    `,
              className: 'custom-map-pin', // Add custom class for CSS animations
              iconSize: isSelected ? [110, 40] : [100, 35],
              iconAnchor: isSelected ? [55, 40] : [50, 35],
            }) : L.icon({
              iconUrl: `/assets/map/${d.colorDot || "red"}-dot.png`,
              iconSize: isSelected ? [48, 48] : [40, 40],
              iconAnchor: isSelected ? [24, 48] : [20, 40],
              popupAnchor: [1, -34],
            })}
          >
            <Popup>
              <Link href={d.link || ""} target="_blank" rel="noopener noreferrer">
                {d.popUpRenderFunction ? d.popUpRenderFunction() : d.name}
              </Link>
            </Popup>
          </Marker>
        );
      })}

      {legend && legend}

      {/* <Button
        className="absolute top-2 right-2 z-1000 bg-white shadow-md hover:bg-gray-100 text-xs"
        onClick={() => {
          if ("geolocation" in navigator) {
            navigator.geolocation.getCurrentPosition(
              (position) => {
                const newCenter = [position.coords.latitude, position.coords.longitude];
                setMapCenter(newCenter);
                toast({
                  title: "現在地に移動しました。",
                  description: `${position.coords.latitude}, ${position.coords.longitude}`,
                  variant: "default",
                });
              },
              (err) => {
                toast({
                  title: "位置情報取得に失敗しました。",
                  description: err.message,
                  duration: 10000,
                  variant: "destructive",
                });
              }
            );
          } else {
            toast({
              title: "このブラウザは位置情報をサポートしていません。",
              variant: "destructive",
              duration: 10000,
            });
          }
        }}
      >
        現在地へ移動
      </Button> */}
    </MapContainer>
  );
}