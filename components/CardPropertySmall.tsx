"use client"
import {
  Card,
  CardContent,
} from "@/components/ui/card"
import { UserLambdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { UserLambdaRecordProps, UserLambdaRecordPropsWithLink } from "@/lib/definitions/userLambdaRecord";
import { Badge } from "@/components/ui/badge"; // 引入 Badge 组件
import Link from 'next/link'; // 引入 Link 组件
import { Aperture } from "lucide-react";
import { mapper } from "@/app/(cp)/an/(common)/recordTypeMapper";
import { useImageCacheStore } from "@/store/uiImageStore";
import { useEffect } from "react";

export function CardPropertySmall({ record }: { record: UserLambdaRecordPropsWithLink }) {
  // const getFileLinks = async () => {
  //   const res = await getMaterialsFolderContentsInFolder({
  //     folderName: record.id as string,
  //     bucketKey: "propertyMaterialsPublic",
  //   });

  //   if (res.success) {
  //     setFileLinks(res.data);
  //   }
  // };

  // useEffect(() => {
  //   getFileLinks();
  // }, [record]);
  const { images, setImage } = useImageCacheStore();
  const cachedImage = images[record.id];

  useEffect(() => {
    const fetchAndCacheImage = async () => {
      if (!cachedImage && record.link) {
        try {
          const res = await fetch(record.link);
          const blob = await res.blob();
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64data = reader.result as string;
            setImage(record.id, base64data);
          };
          reader.readAsDataURL(blob);
        } catch (error) {
          console.error("Image fetch error:", error);
        }
      }
    };

    fetchAndCacheImage();
  }, [cachedImage, record.link, record.id, setImage]);


  const priceChangePercentage = (record: UserLambdaRecordProps) => {
    if (!record.priceChanges || record.priceChanges.length < 2) {
      return 0;
    }

    let last = record.priceChanges.sort((a, b) => new Date(a.recordDate).getTime() - new Date(b.recordDate).getTime())[record.priceChanges.length - 1];

    let secondLast = record.priceChanges.sort((a, b) => new Date(a.recordDate).getTime() - new Date(b.recordDate).getTime())[record.priceChanges.length - 2];

    if (!last.price || !secondLast.price || last.price === secondLast.price) {
      return 0;
    }

    let changePerc = (last.price - secondLast.price) / secondLast.price * 100;
    return changePerc;
  }

  const getSecondLastPrice = (record: UserLambdaRecordProps) => {
    if (!record.priceChanges || record.priceChanges.length < 2) {
      return "";
    }

    let secondLastPrice = record.priceChanges.sort((a, b) => new Date(a.recordDate).getTime() - new Date(b.recordDate).getTime())[record.priceChanges.length - 2].price;

    if (!secondLastPrice || secondLastPrice === record.price) {
      return "";
    }

    return secondLastPrice;
  }

  const getDistinctPriceChanges = (priceChanges: UserLambdaRecordPriceChangeProps[]) => {
    let distinctPriceChanges = new Set<number>();
    priceChanges.forEach((priceChange) => {
      if (priceChange.price) {
        distinctPriceChanges.add(priceChange.price);
      }
    });
    return distinctPriceChanges.size;
  }

  return <Link href={`/ex/search/${record.id}`} target="_blank">
    <Card className="overflow-hidden cursor-pointer"> {/* 添加光标样式 */}
      <CardContent className="relative p-0 overflow-hidden h-40"> {/* 移除 CardContent 的内边距并添加溢出隐藏 */}
        <div className="absolute bottom-2 left-2 z-10 flex items-center">
          <Badge variant="default">{mapper[record.recordType].nameFull}</Badge>
        </div>

        {priceChangePercentage(record) < 0 ? <div className="absolute top-2 right-2 z-10">
          <Badge variant="secondary" className={priceChangePercentage(record) > 0 ? "bg-red-500 text-white" : "bg-green-500 text-white"}>{priceChangePercentage(record).toFixed(1)}%</Badge>
        </div> : record.priceChanges && getDistinctPriceChanges(record.priceChanges) > 1 && <div className="absolute top-2 right-2 z-10">
          <Badge variant="secondary">{getDistinctPriceChanges(record.priceChanges)}件価格変更</Badge>
        </div>}

        <div className="relative w-full h-full overflow-hidden flex flex-col items-center justify-center bg-gray-200"> {/* 添加包装容器以保持高度和宽度一致 */}
          {record.link ?
            <img
              src={cachedImage?.base64 || record.link}
              alt={`${record.compositeTitle || '不動産物件'}の外観写真`}
              width={900}
              height={600}
              className="f-full object-cover overflow-auto cursor-zoom-in"
            /> :
            <div className="w-full h-full flex items-center flex-col justify-center bg-gray-200 gap-4">
              <p className="text-3xl text-neutral-300">画像準備中</p>
              <Aperture className="w-8 h-8 text-neutral-300" />
            </div>
          }
        </div>
      </CardContent>

      <div className="flex justify-between items-start flex-col p-2">
        <div className="text-lg  flex flex-row justify-between w-full items-center text-red-500">
          <div className="flex-1 text-xl flex flex-row items-end">
            <div className="font-bold">{record.price}</div>
            <span className="text-sm line-through ml-1">{getSecondLastPrice(record)}</span>
            <span className="text-sm">万円</span>
          </div>
        </div>

        <div className="text-sm text-red-500">
          {record.yearlyIncome ? `利回: ${((record.yearlyIncome / record.price) * 100).toFixed(2)}%` : "-"}
        </div>

        <div className="text-sm overflow-hidden text-ellipsis whitespace-nowrap" title={record.address || "-"}>
          {record.address || "-"}
        </div>

        <div className="text-xs text-gray-600 overflow-hidden whitespace-nowrap">
          {record.transport}

          {record.nearestStationWalkMinute && record.nearestStationWalkMinute > 100 ?
            <span className="text-red-500"></span>
            :
            <span className="ml-1">徒歩{record.nearestStationWalkMinute}分</span>
          }
        </div>
      </div>
    </Card>
  </Link>
}
