import { Button } from "@/components/ui/button";
import { useAuthStore } from "@/store/auth";
import { Download } from "lucide-react";

export function ExportCSVButton({
  data,
  type,
  filename = "records_export.csv",
}: {
  data: any[];
  type: "userLambdaRecord" | "userLambdaRecordPriceChange" | "rentRecord";
  filename?: string;
}) {
  const { currentUser } = useAuthStore();
  let fields = [] as string[];

  if (type === "userLambdaRecord") {
    fields = ["price", "address", "yearlyIncome", "recordType", "recordSubType", "nearestStation", "transport", "landSize", "landRight", "landType", "landBuildingCoverageRatio", "landFloorAreaRatio", "buildingSize", "buildingMaterial", "buildingLayout", "buildingBuiltYear"];
  } else if (type === "rentRecord") {
    fields = ["address", "transport", "nearestStation", "feeRent", "feeManagement", "feeUtility", "feeGiftMoney", "feeDepositMoney", "landType", "landSize", "buildingBuiltYear", "buildingName", "buildingSize", "unitSize", "createdAt", "updatedAt"];
  }

  const exportToCSV = () => {
    if (!data || data.length === 0) return;

    const csvRows = [
      fields.join(","), // CSV header
      ...data.map(row =>
        fields.map(field => {
          let val = row[field];
          if (val === null || val === undefined) return "";
          if (typeof val === "string") val = `"${val.replace(/"/g, '""')}"`;
          return val;
        }).join(",")
      ),
    ];

    const blob = new Blob([csvRows.join("\n")], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return currentUser && currentUser?.accessLevel >= 20 && (
    <Button variant="outline" onClick={exportToCSV} className="flex flex-row gap-1 items-center text-sm" disabled={data.length === 0}>
      <Download className="w-4 h-4" />
      CSV出力
    </Button>
  );
}