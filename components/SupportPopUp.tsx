"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { useState } from "react"
import { MessageCircle } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useTranslations } from "next-intl"

export default function SupportPopUp({
  domType = "button",
  message = ""
}: {
  domType?: "button" | "text",
  message?: string
}) {
  const [isOpen, setIsOpen] = useState(false)
  const t = useTranslations("Support")

  return (
    <Dialog>
      <DialogTrigger asChild>
        {domType === "button" ? (
          <Button variant="outline" className="w-full">
            <MessageCircle className="w-4 h-4" />
            {message || t("contactTantou")}
          </Button>
        ) : (
          <div className="text-sm hover:underline  z-10 text-white">
            {message}
          </div>
        )}
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            <div className="text-lg font-bold">{t("tantouAdd")}
            </div>

          </DialogTitle>
          <DialogDescription className="flex flex-col gap-4 text-center items-center">
            <div className="text-sm font-base text-gray-500 text-left w-full">
              {t("tantouAddDescription")}
            </div>

            <div className="flex flex-row gap-4  p-4">
              <div className="flex flex-col gap-2">
                <Image src="/assets/wechat-lxk.png" alt="Wechat" width={200} height={200} />

                {/* 添加下载图片的按钮 */}
                <Button variant="outline" className="w-full text-xs" onClick={() => {
                  const link = document.createElement('a');
                  link.href = '/assets/wechat-lxk.png'; // 图片的路径
                  link.download = 'urbalytics-support-wechat-lxk.png'; // 下载时的文件名
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}>
                  Wechat {t("qrCode")}
                  <br />
                  {t("download")}
                </Button>
              </div>

              <div className="flex flex-col gap-2">
                <Image src="/assets/whatsapp-qr.jpg" alt="Wechat" width={200} height={200} />

                <div className="flex flex-col gap-2 rounded-md">
                  <Button variant="outline" className="w-full text-xs" onClick={() => {
                    const link = document.createElement('a');
                    link.href = '/assets/whatsapp-qr.jpg'; // 图片的路径
                    link.download = 'urbalytics-support-whatsapp-qr.jpg'; // 下载时的文件名
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }}>
                    WhatsApp {t("qrCode")}
                    <br />
                    {t("download")}
                  </Button>
                </div>
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  )
}