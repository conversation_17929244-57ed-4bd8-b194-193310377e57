"use client"

import ReactSelect from "react-select";
import { useDebouncedCallback } from "use-debounce";
import { useState } from "react";
import { getPostalCodeByAddressAction } from "@/actions/geoPostalCodes";
import { getProBuildingFuzzyName } from "@/actions/proBuilding";
import { ProBuildingProps } from "@/lib/definitions/proBuilding";
import { Loader2 } from "lucide-react";
import { matchBuildingNameIsWeird } from "@/lib/userLambdaRecord/building";

const CustomOption = (props: any) => {
  const { data, innerRef, innerProps } = props;
  return (
    <div
      ref={innerRef}
      {...innerProps}
      className={`flex items-center p-2 hover:bg-gray-100 cursor-pointer ${data.isTopRanked ? "bg-blue-200" : ""}`}
    >
      <div className="flex flex-row gap-2 w-full justify-between items-center">
        <span
          className={`font-semibold flex-1 text-left text-black 
            }`}
        >
          {data.label}</span>
        <span className="text-xs text-gray-600 flex flex-col text-right justify-end items-end gap-1">
          <div className="flex flex-row gap-2">
            {data.postalCode && `〒 ${data.postalCode} | `}
            {data.address}
          </div>
          <div className="flex flex-row gap-2">
            {data.saleHistoryCount > 0 && <span className="text-xs text-gray-500">売買履歴: {data.saleHistoryCount}件</span>}
            {data.rentHistoryCount > 0 && <span className="text-xs text-gray-500">賃貸履歴: {data.rentHistoryCount}件</span>}
          </div>
        </span>
      </div>
    </div>
  );
};

// Use any because it can be either a single station or an array of stations
export default function SearchBarMansionBuildingUI({ selectedBuilding, setSelectedBuilding }: { selectedBuilding: ProBuildingProps | null, setSelectedBuilding: (building: ProBuildingProps | null) => void }) {
  const [nearestBuildingOptions, setNearestBuildingOptions] = useState<any[]>([]);
  const [isLoadingBuilding, setIsLoadingBuilding] = useState(false);

  const handleSearch = useDebouncedCallback(async (term: string) => {
    if (!term) {
      return;
    }

    try {
      setIsLoadingBuilding(true); // 设置正在加载状态
      const response = await getProBuildingFuzzyName({ name: term, isFuzzy: true });
      if (response.success) {
        setNearestBuildingOptions(response.data.filter((building: ProBuildingProps) => building.nameJa && !matchBuildingNameIsWeird(building.nameJa)).map((building: ProBuildingProps) => ({
          label: `${building.nameJa || ""}${building.nameEn || ""}`.trim(),
          value: building.id,
          saleHistoryCount: building.tllUserLambdaRecords?.length || 0,
          rentHistoryCount: building.mansionRents?.length || 0,
          ...building
        })).sort((a: any, b: any) => (b.saleHistoryCount + b.rentHistoryCount) - (a.saleHistoryCount + a.rentHistoryCount)).map((building: any, index: number) => ({
          ...building,
          isTopRanked: index === 0, // ✅ mark first row
        })));
      }
    } catch (error) {
      console.error('Failed to fetch postal codes:', error);
    } finally {
      setIsLoadingBuilding(false); // 设置正在加载状态
    }
  }, 1000);


  return (
    <ReactSelect
      loadingMessage={() =>
        <div className="flex flex-row gap-2 items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>読み込み中</span>
        </div>
      } // ✅ 修改为日语
      instanceId="mansion-building-bar"
      aria-activedescendant="mansion-building-search-bar"
      placeholder="マンション名を検索...."
      options={nearestBuildingOptions}
      onInputChange={(term) => {
        handleSearch(term);
      }} // Calls API as user types
      isLoading={isLoadingBuilding}
      isSearchable
      value={selectedBuilding}
      isClearable={true}
      noOptionsMessage={() => "データなし"}
      onChange={(selected) => {
        setSelectedBuilding(selected as ProBuildingProps);
      }}
      components={{
        Option: CustomOption,
      }}
    />
  )
}