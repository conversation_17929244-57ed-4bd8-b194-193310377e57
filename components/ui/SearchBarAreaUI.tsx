"use client"

import ReactSelect from "react-select";
import { useDebouncedCallback } from "use-debounce";
import { useEffect, useState, useRef } from "react";
import { GeoAreaProps, GeoPostalCodeProps, RailwayStationProps } from "@/lib/definitions";
import { getPostalCodeByAddressAction } from "@/actions/geoPostalCodes";
import { Loader2 } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { getAreaByAreaNameAction } from "@/actions/geoArea";

const CustomOption = (props: any) => {
  const { data, innerRef, innerProps } = props;
  return (
    <div
      ref={innerRef}
      {...innerProps}
      className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
    >
      <div className="flex flex-row gap-2 w-full justify-between items-center">
        <span className="font-semibold text-black flex-1 text-left">{data.label}</span>
        <span className="text-sm text-gray-600">〒{data.postalCode}</span>
      </div>
    </div>
  );
};

// Use any because it can be either a single station or an array of stations
export default function SearchBarAreaUI({ selectedArea, setSelectedArea }: { selectedArea: { label: string; value: number, areaCode: string, latitude?: number, longitude?: number }, setSelectedArea: (area: { label: string; value: number, areaCode: string, latitude?: number, longitude?: number }) => void }) {
  const [areaOptions, setAreaOptions] = useState<{ label: string; value: number, areaCode: string, latitude?: number, longitude?: number }[]>([]);
  const [isLoadingArea, setIsLoadingArea] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  // 🚀 Performance optimizations
  const searchCache = useRef<Map<string, any>>(new Map());
  const abortControllerRef = useRef<AbortController | null>(null);
  const initialValue = searchParams.get("value")?.replace(/\s/g, "");
  const pathname = usePathname()

  const handleSearch = useDebouncedCallback(async ({
    value,
    isInitialValue
  }: {
    value: string,
    isInitialValue: boolean
  }) => {
    if (!value) {
      return;
    }

    // 🚀 Optimization 1: Check cache first
    const cacheKey = `area-${value}`;
    if (searchCache.current.has(cacheKey)) {
      console.log('🔥 Using cached results for:', value);
      const cachedData = searchCache.current.get(cacheKey);
      setAreaOptions(cachedData.options);

      if (isInitialValue && selectedArea && !selectedArea.value && cachedData.options.length > 0) {
        setSelectedArea(cachedData.options[0]);
      }
      return;
    }

    // 🚀 Optimization 2: Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    try {
      setIsLoadingArea(true);
      const response = await getAreaByAreaNameAction(value);

      if (response.success) {
        const options = response.data.map((area: GeoAreaProps) => ({
          label: `${area.prefecture?.name || ""}${area.city?.name || ""}${area.nameJa || ""}`.trim(),
          value: area.id,
          areaCode: area.code,
          latitude: area.latitude,
          longitude: area.longitude
        }));

        // 🚀 Optimization 3: Cache the results (limit cache size)
        if (searchCache.current.size > 50) {
          const firstKey = searchCache.current.keys().next().value;
          if (firstKey) {
            searchCache.current.delete(firstKey);
          }
        }
        searchCache.current.set(cacheKey, { options });

        setAreaOptions(options);

        if (isInitialValue && selectedArea && !selectedArea.value && options.length > 0) {
          setSelectedArea(options[0]);
        }
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Failed to fetch area data:', error);
      }
    } finally {
      setIsLoadingArea(false);
      if (isInitialValue) {
        setMenuOpen(false);
      }
    }
  }, 300); // 🚀 Reduced from 1000ms to 300ms for faster response

  useEffect(() => {
    if (initialValue) {
      setMenuOpen(true);
      handleSearch({
        value: initialValue,
        isInitialValue: true,
      });
    }
  }, []);

  // 🚀 Cleanup: Cancel pending requests on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return (
    <ReactSelect
      menuIsOpen={menuOpen} // ✅ 控制下拉展开
      onMenuClose={() => setMenuOpen(false)}
      onMenuOpen={() => setMenuOpen(true)}
      loadingMessage={() =>
        <div className="flex flex-row gap-2 items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>読み込み中</span>
        </div>
      } // ✅ 修改为日语
      instanceId="area-search-bar"
      aria-activedescendant="area-search-bar"
      placeholder="場所を検索...."
      options={areaOptions || []}
      onInputChange={(value) => {
        handleSearch({
          value,
          isInitialValue: false,
        });
      }} // Calls API as user types
      isLoading={isLoadingArea}
      isSearchable
      value={selectedArea}
      isClearable={true}
      noOptionsMessage={() => "データなし"}
      onChange={(selected) => {
        setSelectedArea(selected as { label: string; value: number, areaCode: string, latitude?: number, longitude?: number });

        const newParams = new URLSearchParams(searchParams.toString())
        newParams.set("value", selected?.label || "")
        router.push(`${pathname}?${newParams.toString()}`, { scroll: false })
      }}
      components={{
        Option: CustomOption,
      }}
    />
  )
}