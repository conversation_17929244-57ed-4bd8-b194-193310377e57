"use client"

import ReactSelect from "react-select";
import { useDebouncedCallback } from "use-debounce";
import { useEffect, useState, useRef } from "react";
import { Building2, LandPlot, Loader2, MapPin, TrainFront } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
import { getAreaByAreaNameAction } from "@/actions/geoArea";
import { logger } from "@/lib/logger";
import { getAreaPostalStationMansionBuildingByNameAction } from "@/actions/geoArea";
import dayjs from "dayjs";

const CustomOption = (props: any) => {
  const { data, innerRef, innerProps } = props;

  return (
    <div
      ref={innerRef}
      {...innerProps}
      className="flex items-center pl-9 pr-4 py-1 hover:bg-gray-100 cursor-pointer"
    >
      <div className="flex flex-col w-full justify-start items-start">
        <span className="font-semibold text-black flex-1 text-left">
          {data.label}
        </span>

        {data.type === "postalCode" &&
          <span className="text-sm text-gray-600">〒{data.postalCode}</span>
        }

        {data.type === "building" && <div className="text-xs text-gray-600 flex flex-col text-right justify-start items-start gap-1">
          <div className="flex flex-row gap-2">
            {data.address + " | "}
            {data.tllUserLambdaRecordsCount > 0 && <span className="text-xs">売買履歴
              <strong>{data.tllUserLambdaRecordsCount}</strong>件
            </span>}
            {data.mansionRentsCount > 0 && <span className="text-xs">賃貸履歴
              <strong>{data.mansionRentsCount}</strong>件
            </span>}
          </div>
        </div>}

        {data.type === "station" && <div className="text-xs text-gray-600 flex flex-col text-right justify-end items-end gap-1">
          <div className="flex flex-row gap-2">
            {data.geoRailwayStationGroups && `〒${data.geoRailwayStationGroups.postalCode} | `}
            {data.geoRailwayStationGroups && data.geoRailwayStationGroups.address}
          </div>
        </div>}
      </div>
    </div>
  );
};

// Use any because it can be either a single station or an array of stations
export default function SearchBarCombinedUI({ selectedOption, setSelectedOption, recordType }: { selectedOption: { label: string; value: number, type: string } | null, setSelectedOption: (selectedOption: { label: string; value: number, type: string } | null) => void, recordType: string }) {
  const [options, setOptions] = useState<{ label: string; value: number, type: string }[]>([]);
  const [isLoadingArea, setIsLoadingArea] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  // 🚀 Performance optimizations
  const searchCache = useRef<Map<string, any>>(new Map());
  const abortControllerRef = useRef<AbortController | null>(null);

  const initialValue = searchParams.get("value")?.replace(/\s/g, "");
  const initialType = searchParams.get("type")?.replace(/\s/g, "");
  const initialSearchValue = searchParams.get("searchValue")?.replace(/\s/g, "");
  const pathname = usePathname()

  const toFullWidth = (input: string) => {
    return input.replace(/[A-Za-z0-9]/g, function (s) {
      return String.fromCharCode(s.charCodeAt(0) + 0xFEE0);
    });
  };

  let mapper = {
    area: {
      label: "市区町村",
      type: "area",
      icon: <LandPlot className="w-4 h-4" />,
    },
    station: {
      label: "駅",
      type: "station",
      icon: <TrainFront className="w-4 h-4" />,
    },
    building: {
      label: "建物",
      type: "building",
      icon: <Building2 className="w-4 h-4" />,
    },
    postalCode: {
      label: "住所(郵便番号)",
      type: "postalCode",
      icon: <MapPin className="w-4 h-4" />,
    },
  }

  const handleSearch = useDebouncedCallback(async ({
    value,
    isInitialValue
  }: {
    value: string,
    isInitialValue: boolean
  }) => {
    if (!value) {
      return;
    }

    // 🚀 Optimization 1: Check cache first
    const cacheKey = `${value}-${recordType}`;
    if (searchCache.current.has(cacheKey)) {
      console.log('🔥 Using cached results for:', value);
      const cachedData = searchCache.current.get(cacheKey);
      setOptions(cachedData.options);

      if (isInitialValue && initialType && cachedData.options) {
        let matched = cachedData.options.filter((option: any) => option.options.length > 0 && option.type === initialType)[0];
        if (matched) {
          setSelectedOption(initialValue && initialValue.length > 0 ? matched.options.filter((option: any) => option.value === initialValue)[0] : matched.options[0]);
        }
      }
      return;
    }

    // 🚀 Optimization 2: Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    abortControllerRef.current = new AbortController();

    try {
      setIsLoadingArea(true);

      const response = await getAreaPostalStationMansionBuildingByNameAction({
        name: value,
        recordType
      });

      if (response.success) {
        if (Object.keys(response.data).length > 0) {
          let optionsCombined = [] as any;
          Object.keys(response.data).forEach((key: any) => {
            optionsCombined = optionsCombined.concat({
              label: mapper[key as keyof typeof mapper].label,
              icon: mapper[key as keyof typeof mapper].icon,
              options: response.data[key],
              type: mapper[key as keyof typeof mapper].type,
            });
          });

          // 🚀 Optimization 3: Cache the results (limit cache size)
          if (searchCache.current.size > 50) {
            const firstKey = searchCache.current.keys().next().value;
            if (firstKey) {
              searchCache.current.delete(firstKey);
            }
          }
          searchCache.current.set(cacheKey, { options: optionsCombined });

          setOptions(optionsCombined);

          if (isInitialValue && initialType) {
            let matched = optionsCombined.filter((option: any) => option.options.length > 0 && option.type === initialType)[0];
            if (matched) {
              setSelectedOption(initialValue && initialValue.length > 0 ? matched.options.filter((option: any) => option.value === initialValue)[0] : matched.options[0]);
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Failed to fetch search results:', error);
      }
    } finally {
      setIsLoadingArea(false);
      if (isInitialValue) {
        setMenuOpen(false);
      }
    }
  }, 300); // 🚀 Reduced from 1000ms to 300ms for faster response

  useEffect(() => {
    if (initialSearchValue) {
      setMenuOpen(true);
      handleSearch({
        value: initialSearchValue || "",
        isInitialValue: true,
      });
    }
  }, []);

  // 🚀 Cleanup: Cancel pending requests on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return (
    <ReactSelect
      styles={{
        // control: (base) => ({
        //   ...base,
        //   minHeight: 48,
        //   fontSize: "16px",
        // }),
        // menu: (base) => ({
        //   ...base,
        //   fontSize: "16px",
        //   height: "480px",      // ✅ 推荐设置 maxHeight 而不是 height
        //   overflowY: "auto",       // ✅ 显示滚动条
        //   zIndex: 9999,            // ✅ 确保不会被其他元素遮挡（很关键）
        // }),
        // placeholder: (base) => ({
        //   ...base,
        //   fontSize: "16px",
        // }),
        // group: (base) => ({
        //   ...base,
        //   fontSize: "16px",
        // }),
      }}
      menuIsOpen={menuOpen} // ✅ 控制下拉展开
      onMenuClose={() => setMenuOpen(false)}
      onMenuOpen={() => setMenuOpen(true)}
      loadingMessage={() =>
        <div className="flex flex-row gap-2 items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>読み込み中</span>
        </div>
      } // ✅ 修改为日语
      instanceId="combined-search-bar"
      aria-activedescendant="combined-search-bar"
      placeholder="場所、駅、建物名で検索..."
      options={options || []}
      onInputChange={(value) => {
        const fullWidthValue = toFullWidth(value); // 🔁 自动转为全角

        if (value.length > 0) {
          const newParams = new URLSearchParams(searchParams.toString());
          newParams.set("searchValue", fullWidthValue);
          router.push(`${pathname}?${newParams.toString()}`, { scroll: false })

          handleSearch({
            value: fullWidthValue,
            isInitialValue: false,
          });
        }
      }} // Calls API as user types
      isLoading={isLoadingArea}
      isSearchable
      value={selectedOption}
      isClearable={true}
      noOptionsMessage={() => "データなし"}
      onChange={(selected) => {
        setSelectedOption(selected as { label: string; value: number, type: string });
        const newParams = new URLSearchParams(searchParams.toString())
        newParams.set("value", selected?.value?.toString() || "")
        newParams.set("type", selected?.type || "")
        newParams.set("selectRecordType", recordType)
        router.push(`${pathname}?${newParams.toString()}`, { scroll: false })
      }}
      formatGroupLabel={(group: any) => (
        <div className="text-sm font-bold text-gray-700 w-full py-2 flex flex-row gap-2 items-center justify-start border-t border-gray-200">
          {group.icon}
          <span className="text-sm font-bold text-gray-700">{group.label}</span>
        </div>
      )}

      components={{
        Option: CustomOption,
      }}
    />
  )
}