"use client"

import ReactSelect from "react-select";
import { useDebouncedCallback } from "use-debounce";
import { useState } from "react";
import { ActionResponse, RailwayStationProps } from "@/lib/definitions";
import { findNearestStationAction } from "@/actions/geoRailwayStationGroups";
import { Loader2 } from "lucide-react";

const CustomOption = (props: any) => {
  const { data, innerRef, innerProps } = props;
  return (
    <div
      ref={innerRef}
      {...innerProps}
      className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
    >
      <div className="flex flex-row gap-2 w-full justify-between items-center">
        <span className="font-semibold text-black flex-1 text-left">{data.label}</span>
        <span className="text-sm text-gray-600">〒{data.postalCode} | {data.address}</span>
      </div>
    </div>
  );
};

// Use any because it can be either a single station or an array of stations
export default function StationSearchBarUI({ selectedStation, setSelectedStation }: { selectedStation: { label: string; value: string }, setSelectedStation: (station: { label: string; value: string }) => void }) {
  const [nearestStationOptions, setNearestStationOptions] = useState<{ label: string; value: string }[]>([]);
  const [isLoadingStation, setIsLoadingStation] = useState(false);

  const handleSearch = useDebouncedCallback(async (term: string) => {
    if (!term) {
      return;
    }

    try {
      setIsLoadingStation(true); // 设置正在加载状态
      const response = await findNearestStationAction(term) as ActionResponse<RailwayStationProps[]>;
      if (response.success) {
        setNearestStationOptions(response.data.map((station: RailwayStationProps) => ({
          label: station.name,
          value: station.geoRailwayStationGroups.id,
          address: station.geoRailwayStationGroups.address?.includes(station.prefecture.name) ? station.geoRailwayStationGroups.address : station.prefecture.name + station.geoRailwayStationGroups.address,
          postalCode: station.geoRailwayStationGroups.postalCode,
          geoRailwayStationGroup: station.geoRailwayStationGroups
        }))); // 更新最近车站
      }
    } catch (error) {
      console.error('Failed to fetch nearest stations:', error);
    } finally {
      setIsLoadingStation(false); // 设置正在加载状态
    }
  }, 500);

  return (
    <ReactSelect
      loadingMessage={() =>
        <div className="flex flex-row gap-2 items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>読み込み中</span>
        </div>
      } // ✅ 修改为日语
      instanceId="station-search-bar"
      aria-activedescendant="station-search-bar"
      placeholder="駅を検索...."
      options={nearestStationOptions || []}
      onInputChange={(term) => {
        handleSearch(term);
      }} // Calls API as user types
      isLoading={isLoadingStation}
      isSearchable
      value={selectedStation}
      isClearable={true}
      noOptionsMessage={() => "データなし"}
      onChange={(selected) => {
        setSelectedStation(selected as { label: string; value: string });
      }}
      components={{
        Option: CustomOption,
      }}
    />
  )
}