"use client"

import ReactSelect from "react-select";
import { useDebouncedCallback } from "use-debounce";
import { useEffect, useState } from "react";
import { GeoPostalCodeProps, RailwayStationProps } from "@/lib/definitions";
import { getPostalCodeByAddressAction } from "@/actions/geoPostalCodes";
import { Loader2 } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";

const CustomOption = (props: any) => {
  const { data, innerRef, innerProps } = props;
  return (
    <div
      ref={innerRef}
      {...innerProps}
      className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
    >
      <div className="flex flex-row gap-2 w-full justify-between items-center">
        <span className="font-semibold text-black flex-1 text-left">{data.label}</span>
        <span className="text-sm text-gray-600">〒{data.postalCode}</span>
      </div>
    </div>
  );
};

// Use any because it can be either a single station or an array of stations
export default function SearchBarPostalCodeUI({ selectedPostalCode, setSelectedPostalCode }: { selectedPostalCode: { label: string; value: number, postalCode: number, latitude?: number, longitude?: number }, setSelectedPostalCode: (postalCode: { label: string; value: number, postalCode: number, latitude?: number, longitude?: number }) => void }) {
  const [nearestPostalCodeOptions, setNearestPostalCodeOptions] = useState<{ label: string; value: number, postalCode: number, latitude?: number, longitude?: number }[]>([]);
  const [isLoadingPostalCode, setIsLoadingPostalCode] = useState(false);
  const [menuOpen, setMenuOpen] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const initialValue = searchParams.get("value")?.replace(/\s/g, "");
  const pathname = usePathname()

  const handleSearch = useDebouncedCallback(async ({
    value,
    isInitialValue
  }: {
    value: string,
    isInitialValue: boolean
  }) => {
    if (!value) {
      return;
    }

    try {
      setIsLoadingPostalCode(true); // 设置正在加载状态
      const response = await getPostalCodeByAddressAction(value);

      if (response.success) {
        const options = response.data.map((postalCode: GeoPostalCodeProps) => ({
          label: `${postalCode.prefectureName || ""}${postalCode.cityName || ""}${postalCode.areaName || ""}${postalCode.choumeName || ""}`.trim(),
          value: postalCode.id,
          postalCode: postalCode.postalCode,
          latitude: postalCode.latitude,
          longitude: postalCode.longitude
        }));
        setNearestPostalCodeOptions(options);

        if (isInitialValue && selectedPostalCode && !selectedPostalCode.value && options.length > 0) {
          setSelectedPostalCode(options[0]); // ✅ 自动选中第一个
        }
      }
    } catch (error) {
      console.error('Failed to fetch postal codes:', error);
    } finally {
      setIsLoadingPostalCode(false); // 设置正在加载状态
      if (isInitialValue) {
        setMenuOpen(false);
      }
    }
  }, 1000);

  useEffect(() => {
    if (initialValue) {
      setMenuOpen(true);
      handleSearch({
        value: initialValue,
        isInitialValue: true,
      });
    }
  }, []);

  return (
    <ReactSelect
      menuIsOpen={menuOpen} // ✅ 控制下拉展开
      onMenuClose={() => setMenuOpen(false)}
      onMenuOpen={() => setMenuOpen(true)}
      loadingMessage={() =>
        <div className="flex flex-row gap-2 items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>読み込み中</span>
        </div>
      } // ✅ 修改为日语
      instanceId="postal-code-search-bar"
      aria-activedescendant="postal-code-search-bar"
      placeholder="場所を検索...."
      options={nearestPostalCodeOptions || []}
      onInputChange={(value) => {
        handleSearch({
          value,
          isInitialValue: false,
        });
      }} // Calls API as user types
      isLoading={isLoadingPostalCode}
      isSearchable
      value={selectedPostalCode}
      isClearable={true}
      noOptionsMessage={() => "データなし"}
      onChange={(selected) => {
        setSelectedPostalCode(selected as { label: string; value: number, postalCode: number, latitude?: number, longitude?: number });

        const newParams = new URLSearchParams(searchParams.toString())
        newParams.set("value", selected?.label || "")
        router.push(`${pathname}?${newParams.toString()}`, { scroll: false })
      }}
      components={{
        Option: CustomOption,
      }}
    />
  )
}