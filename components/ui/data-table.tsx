"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useRouter, usePathname } from "next/navigation";
import { InboxIcon, Loader } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface DataTableProps<TData, TValue> {
  columns: (ColumnDef<TData, TValue> & { minWidth?: number })[]
  data: TData[],
  showHeader?: boolean,
  showTableHeader?: boolean,
  showFooter?: boolean,
  searchColumn?: string,
  isLoading?: boolean, // 添加 isLoading 属性
  defaultPageSize?: number,
  meta?: {
    favMapIds?: string[]
  },
  useCursor?: boolean,
  fetchNextPage?: () => Promise<void>; // 获取下一页数据的函数
  hasNextPage?: boolean;
  highlightedId?: string; // 添加高亮ID属性
  highlightedColumnId?: string[];
}

export function DataTable<TData, TValue>({
  columns,
  data,
  showHeader,
  showTableHeader = true,
  searchColumn,
  showFooter = true,
  isLoading = false, // 默认值为 false
  defaultPageSize = 50,
  meta = {},
  useCursor = false,
  fetchNextPage,
  hasNextPage,
  highlightedId, // 添加高亮ID参数
  highlightedColumnId,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [pageSize, setPageSize] = React.useState(defaultPageSize);
  const [pageIndex, setPageIndex] = React.useState(0); // 新增状态来管理当前页码
  const [cursorLoading, setCursorLoading] = React.useState(false);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: {
        pageIndex, // 使用新的页码状态
        pageSize,
      },
    },
    meta,
  })

  return (
    <div>
      {showHeader && (
        <div className="flex items-center py-4 justify-end">
          {searchColumn && (
            <Input
              placeholder={`Filter ${searchColumn}...`}
              value={(table.getColumn(searchColumn)?.getFilterValue() as string) ?? ""}
              onChange={(event) =>
                table.getColumn(searchColumn)?.setFilterValue(event.target.value)
              }
              className="flex-1"
            />
          )}
          <div className="flex ml-4">
            {/* <Button onClick={() => {
              console.log("Navigating to create page");
              router.push(`${pathname}/create`);
            }}>
              新規作成
            </Button> */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="ml-2">
                  列選択
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter(
                    (column) => column.getCanHide()
                  )
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    )
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      )}

      <div className="overflow-x-auto">
        <div className="rounded-md border border-neutral-200">
          <Table className="">
            {showTableHeader && <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead
                        key={header.id}
                        className={`whitespace-nowrap ${highlightedColumnId?.includes(header.column.columnDef.header as string) ? "bg-blue-50 font-semibold" : ""}`}
                        style={{ minWidth: (header.column.columnDef as any).minWidth }}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                      </TableHead>
                    )
                  })}
                </TableRow>
              ))}
            </TableHeader>}

            <TableBody className="whitespace-nowrap">
              {isLoading ? ( // 检查是否正在加载
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    <div className="flex items-center justify-center h-full">
                      <Loader className="animate-spin w-8 h-8 text-neutral-500" />
                      <p className="mt-2 text-neutral-500">読み込み中...</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className={highlightedId && ((row.original as any).id === highlightedId || (row.original as any).postalCode === highlightedId) ? "bg-blue-100 hover:bg-blue-200 border-l-4 border-blue-500" : ""}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className={highlightedColumnId?.includes(cell.column.columnDef.header as string) ? "bg-blue-50 font-semibold" : ""}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    <div className="flex flex-col items-center justify-center h-full">
                      <InboxIcon className="w-8 h-8 mb-2 text-neutral-300" />
                      <div className="text-neutral-500">データがありません</div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {showFooter && <div className="flex items-center justify-end space-x-2 py-4">
        <select
          value={pageSize}
          onChange={(e) => setPageSize(Number(e.target.value))}
          className="border rounded-md p-0.5"
        >
          <option value={5}>5</option>
          <option value={10}>10</option>
          <option value={20}>20</option>
          <option value={50}>50</option>
          <option value={100}>100</option>
          <option value={200}>200</option>
          <option value={500}>500</option>
        </select>
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            if (useCursor && fetchNextPage && hasNextPage) {
              setCursorLoading(true);
              fetchNextPage();
              setCursorLoading(false);
            } else {
              setPageIndex((prev) => Math.max(prev - 1, 0)); // 更新页码
              table.previousPage();
            }
          }}
          disabled={!table.getCanPreviousPage()}
        >
          {useCursor && cursorLoading ? <Loader className="animate-spin w-4 h-4" /> : "前へ"}
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={async () => {
            if (useCursor && fetchNextPage && hasNextPage) {
              setCursorLoading(true);
              await fetchNextPage();
              setCursorLoading(false);
            } else {
              setPageIndex((prev) => Math.min(prev + 1, table.getPageCount() - 1)); // 更新页码
              table.nextPage();
            }
          }}
          disabled={!table.getCanNextPage()}
        >
          {useCursor && cursorLoading ? <Loader className="animate-spin w-4 h-4" /> : "次へ"}
        </Button>
      </div>}
    </div>
  )
}
