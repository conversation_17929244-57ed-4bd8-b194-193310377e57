"use client";

import { getRecommendationPriceDown } from "@/actions/dashboard";
import { useUIRecommendationsStore } from "@/store/uiRecommendations";
import { useEffect, useState } from "react";
import { CarouselRecom } from "./CarouselRecom";
import { Loader2 } from "lucide-react";

export default function PriceDownRecom() {
  const { recommendationPriceDownProperties, setRecommendationPriceDownProperties } = useUIRecommendationsStore();
  const [hydratedRecommendations, setHydratedRecommendations] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const fetchRecommendationPriceDown = async () => {
    setIsLoading(true);
    const response = await getRecommendationPriceDown();
    if (response.success) {
      setRecommendationPriceDownProperties(response.data);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    const unsub = useUIRecommendationsStore.persist.onFinishHydration(() => {
      setHydratedRecommendations(true);
    });
    setHydratedRecommendations(useUIRecommendationsStore.persist.hasHydrated()); // 如果已经 ready

    return () => {
      unsub(); // 清理监听器
    };
  }, []);

  useEffect(() => {
    if (hydratedRecommendations && recommendationPriceDownProperties === null) {
      fetchRecommendationPriceDown();
    }

  }, [hydratedRecommendations, recommendationPriceDownProperties]);

  return (
    <div className="h-full bg-white">
      <div className="p-2 flex flex-row gap-2 border-b border-gray-200 pb-2 text-lg font-bold">
        <div className="flex-1">
          価格1割以上下落物件
        </div>
      </div>

      <div className="p-2 flex flex-col gap-2">
        <div className="flex-1 px-12">
          {isLoading ? <div className="text-center text-gray-500 flex flex-col gap-2 p-6 items-center justify-center">
            <Loader2 className="w-4 h-4 animate-spin text-2xl" />
          </div> :
            recommendationPriceDownProperties?.length > 0 ? <CarouselRecom records={recommendationPriceDownProperties} /> : <div className="text-center text-gray-700 flex flex-col gap-2 p-6">
              現在、おすすめの物件はありません。
            </div>}
        </div>
      </div>
    </div>
  );
}