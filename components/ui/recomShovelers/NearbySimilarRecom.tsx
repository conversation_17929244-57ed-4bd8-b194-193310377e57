"use client";

import { getRecommendation<PERSON>igh<PERSON><PERSON>, getRecommendationPriceDown } from "@/actions/dashboard";
import { useUIRecommendationsStore } from "@/store/uiRecommendations";
import { useEffect, useState } from "react";
import { CarouselRecom } from "./CarouselRecom";
import { Loader2 } from "lucide-react";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { getTopResultsFromIds } from "@/actions/tllUserLambdaRecordNearbyRecords";
import { getPostalCodeAction } from "@/actions/geoPostalCodes";
import dayjs from "dayjs";

export default function NearbyProperties({ currentUserLambdaRecord }: { currentUserLambdaRecord: UserLambdaRecordProps }) {
  const [nearbyRecords, setNearbyRecords] = useState<UserLambdaRecordProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchNearbyRecords = async () => {
    setIsLoading(true);
    let distanceXIds = currentUserLambdaRecord?.propertyAnalysisResult?.nearbyRecordIdAndDistance || {};

    const res = await getTopResultsFromIds({ distanceXId: distanceXIds, forRecommendation: true });
    if (res.success) {
      let nearbyRecords = res.data;
      setNearbyRecords(nearbyRecords);
    }

    setIsLoading(false);
  }

  useEffect(() => {
    if (currentUserLambdaRecord?.id) {
      fetchNearbyRecords();
    }
  }, [currentUserLambdaRecord?.id]);

  return (
    <div className="h-full bg-white">
      <div className="p-2 flex flex-row gap-2 border-b border-gray-200 pb-2 text-lg font-bold">
        <div className="flex-1">
          近隣おすすめ物件
        </div>
      </div>

      <div className="p-2 flex flex-col gap-2">
        <div className="flex-1 px-12">
          {isLoading ? <div className="text-center text-gray-500 flex flex-col gap-2 p-6 items-center justify-center">
            <Loader2 className="w-4 h-4 animate-spin text-2xl" />
          </div> :
            nearbyRecords?.length > 0 ? <CarouselRecom records={nearbyRecords} /> : <div className="text-center text-gray-700 flex flex-col gap-2 p-6">
              現在、おすすめの物件はありません。
            </div>}
        </div>
      </div>
    </div>
  );
}