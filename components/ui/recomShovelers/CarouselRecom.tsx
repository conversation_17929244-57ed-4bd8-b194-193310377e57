import * as React from "react"

import { Card, CardContent } from "@/components/ui/card"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { UserLambdaRecordProps, UserLambdaRecordPropsWithLink } from "@/lib/definitions/userLambdaRecord";
import { CardPropertySmall } from "@/components/CardPropertySmall";
import Autoplay from "embla-carousel-autoplay"

export function CarouselRecom({ records }: { records: UserLambdaRecordProps[] }) {
  return (
    // [Autoplay({
    //   delay: 2000,
    //   stopOnMouseEnter: true,
    // })]
    <Carousel className="w-full">
      <CarouselContent className="ml-1">
        {records.map((record: any) => (
          <CarouselItem
            key={record.id}
            className="box-border basis-2/3 md:basis-1/3 lg:basis-1/4 xl:basis-1/5 hover:border-neutral-500 duration-200 border-2 hover:rounded-md border-transparent pl-0" // 添加鼠标悬停时的边框
          >
            <CardPropertySmall record={record as UserLambdaRecordPropsWithLink} />
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  )
}
