"use client";

import { getRecommendationBasedOnSearchHistory, getRecommendationBasedOnValuationHistory, getRecommendationPriceDown } from "@/actions/dashboard";
import { useUIRecommendationsStore } from "@/store/uiRecommendations";
import { useEffect, useState } from "react";
import { CarouselRecom } from "./CarouselRecom";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function () {
  const { recommendationsBasedOnSearchHistory, setRecommendationsBasedOnSearchHistory } = useUIRecommendationsStore();
  const [hydratedRecommendations, setHydratedRecommendations] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const fetchRecommendationBasedOnSearchHistory = async () => {
    setIsLoading(true);
    const response = await getRecommendationBasedOnSearchHistory();
    if (response.success) {
      setRecommendationsBasedOnSearchHistory(response.data);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    const unsub = useUIRecommendationsStore.persist.onFinishHydration(() => {
      setHydratedRecommendations(true);
    });
    setHydratedRecommendations(useUIRecommendationsStore.persist.hasHydrated()); // 如果已经 ready

    return () => {
      unsub(); // 清理监听器
    };
  }, []);

  useEffect(() => {
    if (hydratedRecommendations && recommendationsBasedOnSearchHistory === null) {
      fetchRecommendationBasedOnSearchHistory();
    }

  }, [hydratedRecommendations, recommendationsBasedOnSearchHistory]);

  return (
    <div className="h-full bg-white">
      <div className="h-full bg-neutral-50">
        <div className="p-2 flex flex-row gap-2 border-b border-gray-200 pb-2 text-lg font-bold">
          <div className="flex-1">
            検索履歴の条件に近い物件
          </div>
          <Link href="/ex/search/history">
            <Button variant="outline" size="sm">
              検索閲覧一覧
            </Button>
          </Link>
        </div>


        <div className="flex-1 px-12">
          {isLoading ? <div className="text-center text-gray-500 flex flex-col gap-2 p-6 items-center justify-center">
            <Loader2 className="w-4 h-4 animate-spin text-2xl" />
          </div> :
            recommendationsBasedOnSearchHistory?.length > 0 ? <CarouselRecom records={recommendationsBasedOnSearchHistory} /> : <div className="text-center text-gray-700 flex flex-col gap-2 p-6">
              現在、おすすめの物件はありません。
              <br />
              物件を閲覧し始めていただければ、より多くのおすすめを提案できます。
              <Link href="/ex/search">
                <Button variant="outline" size="lg">
                  物件を検索
                </Button>
              </Link>
            </div>}
        </div>
      </div>
    </div>
  );
}