"use client";

import { getRecommendationBasedOnSearchHistory, getRecommendationBasedOnValuationHistory, getRecommendationPriceDown } from "@/actions/dashboard";
import { useUIRecommendationsStore } from "@/store/uiRecommendations";
import { useEffect, useState } from "react";
import { CarouselRecom } from "./CarouselRecom";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function PerRecomMIhari() {
  const { recommendationsWatchListProperties, setRecommendationsWatchListProperties } = useUIRecommendationsStore();
  const [hydratedRecommendations, setHydratedRecommendations] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const fetchRecommendationBasedOnSearchHistory = async () => {
    setIsLoading(true);
    const response = await getRecommendationBasedOnSearchHistory();
    if (response.success) {
      setRecommendationsWatchListProperties(response.data);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    const unsub = useUIRecommendationsStore.persist.onFinishHydration(() => {
      setHydratedRecommendations(true);
    });
    setHydratedRecommendations(useUIRecommendationsStore.persist.hasHydrated()); // 如果已经 ready

    return () => {
      unsub(); // 清理监听器
    };
  }, []);

  useEffect(() => {
    if (hydratedRecommendations && recommendationsWatchListProperties === null) {
      fetchRecommendationBasedOnSearchHistory();
    }

  }, [hydratedRecommendations, recommendationsWatchListProperties]);

  return (
    <div className="h-full bg-white">
      <div className="p-2 flex flex-row gap-2 border-b border-gray-200 pb-2 text-lg font-bold">
        <div className="flex-1">
          見張中新規物件
        </div>
        <Link href="/ex/need">
          <Button variant="outline" size="sm">
            物件見張り一覧
          </Button>
        </Link>
      </div>


      <div className="flex-1 px-12">
        {isLoading ? <div className="text-center text-gray-500 flex flex-col gap-2 p-6 items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin text-2xl" />
        </div> :
          recommendationsWatchListProperties?.length > 0 ? <CarouselRecom records={recommendationsWatchListProperties} /> : <div className="text-center text-gray-700 flex flex-col gap-2 p-6">
            現在、おすすめの物件はありません。
            <br />
            物件を見張り始めていただければ、より多くのおすすめを提案できます。
            <Link href="/ex/need">
              <Button variant="outline" size="lg">
                物件を見張り
              </Button>
            </Link>
          </div>}
      </div>
    </div>
  );
}