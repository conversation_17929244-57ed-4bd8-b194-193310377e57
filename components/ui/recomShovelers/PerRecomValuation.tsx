"use client";

import { getRecommendationBasedOnValuationHistory, getRecommendationPriceDown } from "@/actions/dashboard";
import { useUIRecommendationsStore } from "@/store/uiRecommendations";
import { useEffect, useState } from "react";
import { CarouselRecom } from "./CarouselRecom";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export default function PerRecomValuation() {
  const { recommendationsBasedOnValuationHistory, setRecommendationsBasedOnValuationHistory } = useUIRecommendationsStore();
  const [hydratedRecommendations, setHydratedRecommendations] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const fetchRecommendationBasedOnValuationHistory = async () => {
    setIsLoading(true);
    const response = await getRecommendationBasedOnValuationHistory();
    if (response.success) {
      setRecommendationsBasedOnValuationHistory(response.data);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    const unsub = useUIRecommendationsStore.persist.onFinishHydration(() => {
      setHydratedRecommendations(true);
    });
    setHydratedRecommendations(useUIRecommendationsStore.persist.hasHydrated()); // 如果已经 ready

    return () => {
      unsub(); // 清理监听器
    };
  }, []);

  useEffect(() => {
    if (hydratedRecommendations && recommendationsBasedOnValuationHistory === null) {
      fetchRecommendationBasedOnValuationHistory();
    }

  }, [hydratedRecommendations, recommendationsBasedOnValuationHistory]);

  return (
    <div className="h-full bg-white">
      <div className="h-full bg-neutral-50">
        <div className="p-2 flex flex-row gap-2 border-b border-gray-200 pb-2 text-lg font-bold">
          <div className="flex-1">
            査定履歴の条件に近い物件
          </div>
          <Link href="/ex/valuation/history">
            <Button variant="outline" size="sm">
              査定履歴一覧
            </Button>
          </Link>
        </div>

        <div className="flex-1 px-12">
          {isLoading ? <div className="text-center text-gray-500 flex flex-col gap-2 p-6 items-center justify-center h-full">
            <Loader2 className="w-4 h-4 animate-spin text-2xl" />
          </div> :
            recommendationsBasedOnValuationHistory?.length > 0 ? <CarouselRecom records={recommendationsBasedOnValuationHistory} /> : <div className="text-center text-gray-700 flex flex-col gap-2 p-6">
              現在、おすすめの物件はありません。
              <br />
              物件を査定し始めていただければ、より多くのおすすめを提案できます。
              <Link href="/ex/valuation">
                <Button variant="outline" size="lg">
                  物件を査定
                </Button>
              </Link>
            </div>}
        </div>
      </div>
    </div>
  );
}