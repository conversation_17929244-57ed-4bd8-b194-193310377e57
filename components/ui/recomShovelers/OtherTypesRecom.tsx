"use client";

import { getRecommendationHighYield, getRecommendationPriceDown } from "@/actions/dashboard";
import { useUIRecommendationsStore } from "@/store/uiRecommendations";
import { useEffect, useState } from "react";
import { CarouselRecom } from "./CarouselRecom";
import { Loader2 } from "lucide-react";
import { UserLambdaRecordProps, UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { getTopResultsFromIds } from "@/actions/tllUserLambdaRecordNearbyRecords";
import { getPostalCodeAction } from "@/actions/geoPostalCodes";
import dayjs from "dayjs";
import { getRecommendationOtherTypesForRecommendation } from "@/actions/tllUserLambdaRecordsRecommendation";
import { record } from "zod";

export default function OtherTypesRecom({ recordType, option, currentUserLambdaRecord }: { recordType: UserLambdaRecordType, option: any, currentUserLambdaRecord: UserLambdaRecordProps }) {
  const [nearbyRecords, setNearbyRecords] = useState<UserLambdaRecordProps[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchNearbyRecords = async () => {
    setIsLoading(true);
    let res = await getRecommendationOtherTypesForRecommendation({
      ...(option?.type === "area" && { areaCode: option?.value ? option?.value : undefined }),
      ...(option?.type === "station" && { nearestStationGroupId: option?.value ? option?.value : undefined }),
      ...(option?.type === "building" && { buildingId: option?.value ? option?.value : undefined }),
      ...(option?.type === "postalCode" && { postalCode: option?.value ? option?.value : undefined }),
      recordType: recordType
    });
    if (res.success) {
      setNearbyRecords(res.data);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    if (currentUserLambdaRecord?.id && recordType && option) {
      fetchNearbyRecords();
    }
  }, [currentUserLambdaRecord, option, recordType]);

  return (
    <div className="h-full bg-white">
      <div className="p-2 flex flex-row gap-2 border-b border-gray-200 pb-2 text-lg font-bold">
        <div className="flex-1">
          {option.label}での他のタイプの物件
        </div>
      </div>

      <div className="p-2 flex flex-col gap-2">
        <div className="flex-1 px-12">
          {isLoading ? <div className="text-center text-gray-500 flex flex-col gap-2 p-6 items-center justify-center">
            <Loader2 className="w-4 h-4 animate-spin text-2xl" />
          </div> :
            nearbyRecords?.length > 0 ? <CarouselRecom records={nearbyRecords} /> : <div className="text-center text-gray-700 flex flex-col gap-2 p-6">
              現在、おすすめの物件はありません。
            </div>}
        </div>
      </div>
    </div>
  );
}