"use client";

import { getRecommendationHighYield, getRecommendationPriceDown } from "@/actions/dashboard";
import { useUIRecommendationsStore } from "@/store/uiRecommendations";
import { useEffect, useState } from "react";
import { CarouselRecom } from "./CarouselRecom";
import { Loader2 } from "lucide-react";

export default function HighReturnRecom() {
  const { recommendationHighYieldProperties, setRecommendationHighYieldProperties } = useUIRecommendationsStore();
  const [hydratedRecommendations, setHydratedRecommendations] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const fetchRecommendationHighYield = async () => {
    setIsLoading(true);
    const response = await getRecommendationHighYield();
    if (response.success) {
      setRecommendationHighYieldProperties(response.data);
    }
    setIsLoading(false);
  }

  useEffect(() => {
    const unsub = useUIRecommendationsStore.persist.onFinishHydration(() => {
      setHydratedRecommendations(true);
    });
    setHydratedRecommendations(useUIRecommendationsStore.persist.hasHydrated()); // 如果已经 ready

    return () => {
      unsub(); // 清理监听器
    };
  }, []);

  useEffect(() => {
    if (hydratedRecommendations && recommendationHighYieldProperties === null) {
      fetchRecommendationHighYield();
    }

  }, [hydratedRecommendations, recommendationHighYieldProperties]);

  return (
    <div className="h-full bg-white">
      <div className="p-2 flex flex-row gap-2 border-b border-gray-200 pb-2 text-lg font-bold">
        <div className="flex-1">
          新規利回7%以上物件
        </div>
      </div>

      <div className="p-2 flex flex-col gap-2">
        <div className="flex-1 px-12">
          {isLoading ? <div className="text-center text-gray-500 flex flex-col gap-2 p-6 items-center justify-center">
            <Loader2 className="w-4 h-4 animate-spin text-2xl" />
          </div> :
            recommendationHighYieldProperties?.length > 0 ? <CarouselRecom records={recommendationHighYieldProperties} /> : <div className="text-center text-gray-700 flex flex-col gap-2 p-6">
              現在、おすすめの物件はありません。
            </div>}
        </div>
      </div>
    </div>
  );
}