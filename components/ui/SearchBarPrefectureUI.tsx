import { getPrefecturesAction } from "@/actions/geoPrefecture";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { GeoPrefectureProps } from "@/lib/definitions";
import { useEffect, useState } from "react";

export default function SearchBarPrefectureUI({ need, setNeed }: { need: any, setNeed: (need: any) => void }) {
  const [prefectures, setPrefectures] = useState<GeoPrefectureProps[]>([]);
  const [selectedPrefecture, setSelectedPrefecture] = useState<GeoPrefectureProps | null>(null);

  useEffect(() => {
    const fetchPrefectures = async () => {
      const prefectures = await getPrefecturesAction();
      if (prefectures.success) {
        setPrefectures(prefectures.data);
      }
    };

    fetchPrefectures();
  }, []);

  return (
    <div className="flex flex-col gap-2 col-span-1">
      <div className="text-sm text-neutral-500">都道府県</div>
      <Select value={need.prefectureCodes} onValueChange={(value) => {
        setNeed({ ...need, prefectureCodes: value as string });
      }}>
        <SelectTrigger>
          <SelectValue placeholder="都道府県を選択" />
        </SelectTrigger>
        <SelectContent>
          {prefectures.map((prefecture: GeoPrefectureProps) => (
            <SelectItem key={prefecture.code} value={prefecture.code}>{prefecture.name}</SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}