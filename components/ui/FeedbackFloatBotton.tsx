"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import {
  Toolt<PERSON>,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Send,
  Loader2,
  MessageCircleQuestion,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { LARK_URLS, sendLark } from "@/lib/thirdParty/lark";
import { useAuthStore } from "@/store/auth";
import { useToast } from "@/hooks/use-toast";

export default function FeedbackButton() {
  const [open, setOpen] = useState(false);
  const [feedback, setFeedback] = useState("");
  const [isThumbUp, setIsThumbUp] = useState(false);
  const [isThumbDown, setIsThumbDown] = useState(false);
  const { currentUser } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const wrapperRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    }

    if (open) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [open]);

  return (
    <TooltipProvider delayDuration={100}>
      <div className="fixed bottom-6 right-6 flex items-end gap-4 z-50 group">
        {/* 反馈框 */}
        {open && (
          <div
            ref={wrapperRef}
            className="w-[280px] sm:w-[360px] bg-white border border-neutral-200 rounded-lg shadow-xl p-2"
          >
            <Textarea
              placeholder="フィードバックをお願いします..."
              value={feedback}
              className="resize-none"
              onChange={(e) => setFeedback(e.target.value)}
            />
            <div className="flex justify-between items-center mt-2">
              <div className="flex gap-4">
                <ThumbsUp
                  className={cn("w-4 h-4 cursor-pointer text-gray-400 hover:text-black", isThumbUp && "text-neutral-500")}
                  onClick={() => {
                    setIsThumbUp(true);
                    setIsThumbDown(false);
                  }}
                />
                <ThumbsDown
                  className={cn("w-4 h-4 cursor-pointer text-gray-400 hover:text-black", isThumbDown && "text-neutral-500")}
                  onClick={() => {
                    setIsThumbUp(false);
                    setIsThumbDown(true);
                  }}
                />
              </div>

              <Button
                size="sm"
                disabled={!feedback.trim() || isLoading}
                onClick={async () => {
                  setIsLoading(true);
                  await sendLark({
                    message: `[💬][${isThumbUp ? "👍" : isThumbDown ? "👎" : "🤔"}][フィードバック][by ${currentUser?.email}] ${feedback}`,
                    url: LARK_URLS.USER_ACTIVITY_CHANNEL,
                  });
                  setIsLoading(false);
                  setOpen(false);
                  setFeedback("");
                  toast({
                    title: "フィードバックを送信しました",
                    description: "ありがとうございます 😄",
                    duration: 2000,
                  });
                }}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <>
                    送信 <Send className="w-4 h-4 ml-1" />
                  </>
                )}
              </Button>
            </div>
          </div>
        )}

        {/* 主按钮 + サポート按钮 */}
        <div className="relative group flex flex-col items-center">
          {/* サポート按钮 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Link href="/my/support">
                <Button
                  size="icon"
                  variant="ghost"
                  className="absolute -top-12 right-0 opacity-0 group-hover:opacity-100 transition-opacity border border-neutral-200 bg-white text-black hover:bg-neutral-100 rounded-full"
                  aria-label="サポート"
                >
                  <MessageCircleQuestion className="w-4 h-4" />
                </Button>
              </Link>
            </TooltipTrigger>
            <TooltipContent side="left" sideOffset={8} className="mb-14 mr-4"> {/* 修正这里 */}
              <p>サポート</p>
            </TooltipContent>
          </Tooltip>

          {/* 主按钮 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                ref={buttonRef}
                className={cn(
                  "bg-black text-white rounded-full p-3 shadow-lg transition",
                  open && "bg-neutral-700"
                )}
                variant="outline"
                size="icon"
                aria-label="フィードバックを送信"
                onClick={() => setOpen((prev) => !prev)}
              >
                <MessageSquare className="w-5 h-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>フィードバックを送信</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
}