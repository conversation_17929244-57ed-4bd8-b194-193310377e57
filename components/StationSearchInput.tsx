"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useDebouncedCallback } from "use-debounce"
import { findNearestStationAction } from "@/actions/geoRailwayStationGroups"

const frameworks = [
  {
    value: "next.js",
    label: "Next.js",
  },
  {
    value: "sveltekit",
    label: "SvelteKit",
  },
  {
    value: "nuxt.js",
    label: "Nuxt.js",
  },
  {
    value: "remix",
    label: "Remix",
  },
  {
    value: "astro",
    label: "Astro",
  },
]

export function StationSearchInput() {
  const [open, setOpen] = React.useState(false)
  const [value, setValue] = React.useState("")
  const [nearestStations, setNearestStations] = React.useState([])

  const handleSearch = useDebouncedCallback(async (term: string) => {
    console.log('handleSearch', term);
    setValue(term)
    if (term) {
      const response = await findNearestStationAction(term);

      if (response.success) {
        setNearestStations(response.data); // 更新最近车站
      } else {
        console.error('Failed to fetch nearest stations:', response.message);
      }
    } else {
      setNearestStations([]); // 清空最近车站
    }
  }, 300);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between"
        >
          {value
            ? frameworks.find((framework) => framework.value === value)?.label
            : "Select station..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0">
        <Command>
          <CommandInput placeholder="Search station..." value={value} onInput={(e: any) => handleSearch(e.target.value)} />
          <CommandList hidden={value === ""} className="w-full left-0">
            <CommandEmpty>データなし</CommandEmpty>
            <CommandGroup className="max-h-[300px] overflow-y-auto relative">
              {frameworks.map((framework) => (
                <CommandItem
                  key={framework.value}
                  value={framework.value}
                  onSelect={(currentValue) => {
                    setValue(currentValue === value ? "" : currentValue)
                    setOpen(false)
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === framework.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {framework.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
