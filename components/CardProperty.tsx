"use client"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { UserLambdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { Badge } from "@/components/ui/badge"; // 引入 Badge 组件
import Link from 'next/link'; // 引入 Link 组件
import PriceChangeChart from "@/components/userLambdaRecord/PriceChangeChart";
import { Document, Page } from 'react-pdf';
import { useState } from "react";

interface CardPropertyProps {
  data?: UserLambdaRecordProps[];
}

export function CardProperty({ data = [] }: CardPropertyProps) {
  const getLatestPriceChange = (record: UserLambdaRecordProps) => {
    if (record.priceChanges && record.priceChanges.length > 0) {
      return record.priceChanges.sort((a: UserLambdaRecordPriceChangeProps, b: UserLambdaRecordPriceChangeProps) => {
        const dateA = new Date(a.recordDate);
        const dateB = new Date(b.recordDate);
        return dateB.getTime() - dateA.getTime(); // 降序排序
      })[0]; // 返回最新的价格变动记录
    }
    return null;
  };

  const [numPages, setNumPages] = useState<number>();

  function onDocumentLoadSuccess({ numPages }: { numPages: number }): void {
    setNumPages(numPages);
  }

  return data.map((record, index) => {
    const latestPriceChange = getLatestPriceChange(record); // 获取最新价格变动记录

    return (
      <Link key={index} href={`/ex/search/${record.id}`} passHref>
        <Card className="overflow-hidden cursor-pointer"> {/* 添加光标样式 */}
          <CardContent className="relative p-0 overflow-hidden h-60"> {/* 移除 CardContent 的内边距并添加溢出隐藏 */}
            <div className="absolute bottom-2 left-2 z-10 flex items-center">
              <Badge variant="default">{record.recordType}</Badge> {/* 显示 badge */}
              {record.recordSubType && <Badge variant="default" className="ml-2">{record.recordSubType}</Badge>} {/* 显示 recordSubType */}
            </div>
            <div className="absolute top-2 right-2 z-10">
              <Badge variant="secondary">{record.priceChanges ? record.priceChanges.length : 0} 变动</Badge> {/* 显示价格变动数量 */}
            </div>
            <div className="relative w-full h-full"> {/* 添加包装容器以保持高度和宽度一致 */}
              {latestPriceChange && /^https?:\/\//.test(latestPriceChange?.chirashiLink || "") ?
                <iframe
                  src={latestPriceChange.chirashiLink}
                  className="w-full h-full object-cover transition-transform duration-300 transform hover:scale-110 hover:cursor-pointer"
                />
                // <Document
                //   file={latestPriceChange.chirashiLink}
                //   onLoadSuccess={onDocumentLoadSuccess}
                // >
                //   <Page pageNumber={pageNumber} />
                // </Document>
                :
                <div className="w-full h-full flex items-center justify-center bg-gray-300">
                  <span className="text-4xl text-gray-700">No Data</span>
                </div>
              }
            </div>
          </CardContent>
          <CardHeader className="flex justify-between items-start flex-col p-4">
            <div className="relative">
              <CardTitle className="text-lg overflow-hidden text-ellipsis whitespace-nowrap" title={record.address || "-"}>{record.address || "-"}</CardTitle>
            </div>
            <CardDescription>{record.nearestStation ? `${record.nearestStation} (徒歩${record.nearestStationWalkMinute}分)` : "-"}</CardDescription>
            <p className="text-xl font-bold">{record.price ? `${record.price.toLocaleString()}万円` : "-"}
              <span className="text-sm text-neutral-500 ml-2">{record.yearlyIncome ? `年収: ${record.yearlyIncome.toLocaleString()}万円` : "-"}</span>
            </p>


            <div className="overflow-hidden" style={{ height: '100px', width: '100%' }}>
              <PriceChangeChart priceChanges={record.priceChanges || []} showLabel={false} />
            </div>

          </CardHeader>

          {/* {record.salesComments && record.salesComments.length > 0 && (
                <CardFooter>
                  <p>コメント: {record.salesComments}</p>
                </CardFooter>
              )} */}
        </Card>
      </Link>
    );
  })
}
