import { Button } from "@/components/ui/button";
import { Loader } from "lucide-react";

const agencyNeededSiteWhitelist = ["https://www.livable.co.jp", "https://www.gizmodo.jp", "https://news.yahoo.co.jp", "https://www.nikkei.com", "https://www.kenbiya.com", "https://toyokeizai.net", "https://www.rehouse.co.jp"];

export default function IframeImage({ url, height = "640px", showToolbar = false }: { url: string, height?: string, showToolbar?: boolean }) {
  return url ? (
    <iframe
      id="testIframeWithProxy"
      src={
        agencyNeededSiteWhitelist.some(item => url.includes(item)) ?
          `/api/proxy?url=${url}#toolbar=0`
          : `${url}#toolbar=${showToolbar ? "0" : "1"}`
      }
      className={`w-full h-[${height}] border-2 border-neutral-200`} title="URL Preview" />
  ) : (
    <div className="w-full bg-gray-200 flex items-center justify-center" style={{ height: "500px" }}>
      <div className="text-sm"  >URLを入力してください</div>
    </div>
  )
}

