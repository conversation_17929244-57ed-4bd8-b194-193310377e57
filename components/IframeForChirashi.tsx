import { Button } from "@/components/ui/button";
import { TllUserProps } from "@/lib/definitions/tllUser";
import { useEffect, useState } from "react";

export default function IframeImage({ url, currentUser }: { url: string, currentUser: TllUserProps }) {
  const [iframeUrl, setIframeUrl] = useState<string>("");
  const isPdf = url?.toLowerCase().endsWith('.pdf');

  useEffect(() => {
    if (!url) return;

    if (isPdf) {
      // 对于PDF文件，使用Google Docs Viewer来预览
      const encodedUrl = encodeURIComponent(url);
      setIframeUrl(`https://docs.google.com/viewer?url=${encodedUrl}&embedded=true`);
    } else {
      // 对于其他文件，直接使用原始URL
      setIframeUrl(url);
    }
  }, [url, isPdf]);

  if (!url) {
    return (
      <div className="w-full bg-gray-200 flex items-center justify-center" style={{ height: "500px" }}>
        <p>URLを入力してください</p>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full">
      {iframeUrl && <iframe
        id="testIframeWithProxy"
        allow="clipboard-read; clipboard-write"
        src={`${iframeUrl}${!isPdf ? `#toolbar=${currentUser?.accessLevel < 90 ? '0' : '1'}` : ''}`}
        className="w-full h-full border-2 border-neutral-200 min-h-[500px]"
        title="URL Preview"
      />}
      {/* {isPdf && (
        <div className="absolute top-2 right-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(url, '_blank', 'noopener,noreferrer')}
          >
            ダウンロード
          </Button>
        </div>
      )} */}
    </div>
  );
}

