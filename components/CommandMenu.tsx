"use client";

import { useEffect, useState, useRef } from "react";
import { Command, CommandDialog, CommandInput, CommandList, CommandItem, CommandEmpty, CommandGroup, CommandSeparator, CommandShortcut } from "@/components/ui/command";
import { useParams, useRouter } from "next/navigation";
import { BadgeJapaneseYen, CircleFadingPlus, HandCoins, Search, User } from "lucide-react";
import { DialogDescription, DialogTitle } from "@radix-ui/react-dialog";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useUIStore } from "@/store/ui";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden"; // Import hidden component
import { useAuthStore } from "@/store/auth";

export default function CommandMenu() {
  const [open, setOpen] = useState(false);
  const [query, setQuery] = useState("");
  const router = useRouter();
  const pathname = usePathname();
  const { id } = useParams();
  const { setBidSheetOpen } = useUIStore();
  const { currentUser } = useAuthStore();
  // console.log("🔥 現在のパス:", pathname);

  // メニューオプション (動的データに置き換えることができます)
  const suggestions = {
    "/ex/search/": [
      { id: "redbook", type: "link", title: "リンク: レポート", url: "/pub/rec/" + id + "/sns", icon: <CircleFadingPlus /> },
      { id: "chirashi", type: "link", title: "リンク: 仕入チラシ", url: "/pub/rec/" + id + "/broker", icon: <HandCoins /> },
      {
        id: "bidHistory", type: "button", title: "仕入管理", action: () => {
          setBidSheetOpen(true);
        }, icon: <BadgeJapaneseYen />,
        // accessLevel: 99,
      },
    ] as any
  } as any;

  // キーボードショートカットの処理
  function handleKeyDown(event: KeyboardEvent) {
    if ((event.ctrlKey || event.metaKey) && event.key === "k") {
      event.preventDefault();
      setOpen((prev) => !prev);
    } else if (event.key === "Escape") {
      setOpen(false);
    }
  }

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [query, suggestions, pathname, router]);

  return (
    <CommandDialog open={open} onOpenChange={setOpen} >
      <VisuallyHidden>
        <DialogTitle>グローバルコマンドメニュー</DialogTitle>
        <DialogDescription>
          アプリケーションを検索してナビゲート
        </DialogDescription>
      </VisuallyHidden>

      <Command className="rounded-lg border shadow-md md:min-w-[450px]">
        <CommandInput placeholder="コマンドを入力または検索..." />
        <CommandList>
          <CommandEmpty>結果が見つかりませんでした。</CommandEmpty>
          <CommandGroup heading="提案">
            {Object.entries(suggestions).flatMap(([key, items]) =>
              pathname.startsWith(key) ? items : []
            ).filter((item: any) => {
              if (item.accessLevel && currentUser?.accessLevel && currentUser?.accessLevel < item.accessLevel) {
                return false;
              }
              return true;
            }).map((item: any, index: number) => (
              <CommandItem key={index} disabled={item.disabled || false} asChild onSelect={() => {
                if (item.type === "link") {
                  // router.push(item.url);
                } else if (item.type === "button") {
                  item.action();
                  setOpen(false);
                }
              }}>
                {(() => {
                  switch (item.type) {
                    case "link":
                      return (
                        <Link href={item.url} target="_blank">
                          {item.icon}
                          <span>{item.title}</span>
                          {item.shortcut && <CommandShortcut>{item.shortcut}</CommandShortcut>}
                        </Link>
                      );
                    case "button":
                      return (
                        <div onClick={() => {
                          setOpen(false);
                          item.action();
                        }}>
                          {item.icon}
                          <span>{item.title}</span>
                          {item.shortcut && <CommandShortcut>{item.shortcut}</CommandShortcut>}
                        </div>
                      );
                    default:
                      return null;
                  }
                })()}
              </CommandItem>
            ))}
          </CommandGroup>
          <CommandSeparator />
          <CommandGroup heading="その他">
            <CommandItem onSelect={() => {
              setOpen(false);
              router.push("/my/account");
            }}>
              <User />
              <span>アカウント</span>
            </CommandItem>
            <CommandItem onSelect={() => {
              setOpen(false);
              window.open("https://www.urbalytics.jp/ex/search", "_blank");
            }}>
              <Search />
              <span>物件検索</span>
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </Command>
    </CommandDialog>
  );
}