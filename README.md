# Access Control Definitions
- Free User: 無料ユーザー
- Paid User: 有料ユーザー
- Partner: パートナー Company (e.g. YSJ)
- Admin: 管理者 (TLL free time employee - may be changed)
- Super Admin: スーパー管理者 (TLL super admin)


# UseFul SQL
## STEP 1 Find long running queries and kill 
SELECT count(*) FROM pg_stat_activity;  --- cannot go more than 60 for pro

## Check if coonnection pool is used
SELECT pid, client_port, usename, state, backend_type, application_name, client_addr
FROM pg_stat_activity
WHERE pid <> pg_backend_pid() AND backend_type = 'client backend';

SELECT pid, age(clock_timestamp(), query_start), state, query 
FROM pg_stat_activity 
ORDER BY query_start ASC;


##### RUN THIS EVERY TIME YOU HANG
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE state = 'idle'
  AND age(clock_timestamp(), query_start) > interval '2 minutes'
  AND pid <> pg_backend_pid()
  AND usename <> 'supabase_admin'

select pg_terminate_backend(pid);

## STEP 2 Getting size for all tables
SELECT
    relname AS table_name,
    pg_size_pretty(pg_total_relation_size(relid)) AS total_size,
    pg_size_pretty(pg_relation_size(relid)) AS table_size,
    pg_size_pretty(pg_total_relation_size(relid) - pg_relation_size(relid)) AS index_size
FROM pg_catalog.pg_statio_user_tables
ORDER BY pg_total_relation_size(relid) DESC;


## STEP 2.2 Getting size for all bloated indexes and kill 
SELECT
    relname AS index_name,
    pg_size_pretty(pg_relation_size(relid)) AS index_size,
    pg_size_pretty(pg_total_relation_size(relid) - pg_relation_size(relid)) AS index_bloat
FROM pg_stat_all_indexes
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(relid) - pg_relation_size(relid) DESC;


## STEP 3 Getting all indexes for a table and remove unused 
SELECT indexname, indexdef
FROM pg_indexes
WHERE tablename = 'tll_user_lambda_records';
SELECT 
    indexrelid::regclass AS index_name,
    pg_size_pretty(pg_relation_size(indexrelid)) AS index_size
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
AND relname = 'tll_user_lambda_records'
ORDER BY pg_relation_size(indexrelid) DESC;


-- DROP INDEX IF EXISTS tll_user_lambda_records_id_price_land_size_building_size_re_idx;
-- select * from property_analysis_results where valuation_record_id is not null;




# MORE DB IMPROVMENTS
1. set DB timeout for long idle tasks 
ALTER DATABASE postgres SET idle_in_transaction_session_timeout = '2min';
>> check with SHOW idle_in_transaction_session_timeout;


