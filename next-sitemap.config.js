/** @type {import('next-sitemap').IConfig} */

const { createClient } = require('@sanity/client');
const data = require('./app/glossary/data.json');
const { mainPages, staticPages } = require('./config/sitemapPages');
const { createSanityClient } = require('./utils/sanityClient');
const fs = require('fs');
const path = require('path');

const sanityClient = createSanityClient();

// 处理博客文章URL生成
const createBlogUrl = (slug, lang, publishedAt) => ({
  loc: `/blog/${lang}/${slug}`,
  lastmod: publishedAt
});

const createLocalizedPageUrl = (page, lang) => ({
  loc: `${page.path}?lang=${lang}`,
  priority: page.priority * 0.9,
  changefreq: page.changefreq
});

async function getBlogPostUrlsForAllLocales() {
  const query = `*[
    _type == "post" &&
    (defined(slug_en.current) || defined(slug_ja.current) || defined(slug_zh.current))
  ]{
    slug_en, slug_ja, slug_zh,
    publishedAt
  }`;

  const posts = await sanityClient.fetch(query);
  const urls = [];

  posts.forEach(post => {
    if (post.slug_en?.current) urls.push(createBlogUrl(post.slug_en.current, 'en', post.publishedAt));
    if (post.slug_ja?.current) urls.push(createBlogUrl(post.slug_ja.current, 'ja', post.publishedAt));
    if (post.slug_zh?.current) urls.push(createBlogUrl(post.slug_zh.current, 'zh', post.publishedAt));
  });

  // 添加主要页面的多语言版本
  mainPages.forEach(page => {
    urls.push(createLocalizedPageUrl(page, 'en'));
    urls.push(createLocalizedPageUrl(page, 'zh'));
  });

  return urls;
}

async function getGlossaryUrlsForAllLocales() {
  return data
    .filter(item => item.id)
    .map(item => ({
      loc: `/glossary/${item.id}`,
      lastmod: new Date().toISOString()
    }));
}

// 获取所有 wiki 页面路由
async function getWikiUrlsForAllLocales() {
  const wikiUrls = [];
  const addedPaths = new Set(); // 防止重复添加
  const wikiDir = path.join(process.cwd(), 'app/wiki');

  const addWikiUrl = (pagePath, priority = 0.6) => {
    if (addedPaths.has(pagePath)) return;

    addedPaths.add(pagePath);

    // 添加基础 wiki 页面
    wikiUrls.push({
      loc: `/wiki/${pagePath}`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority
    });

    // 添加多语言版本
    wikiUrls.push({
      loc: `/wiki/${pagePath}?lang=en`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority * 0.8
    });

    wikiUrls.push({
      loc: `/wiki/${pagePath}?lang=zh`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority * 0.8
    });
  };

  try {
    // 读取 _meta.json 文件获取页面配置
    const metaPath = path.join(wikiDir, '_meta.json');
    if (fs.existsSync(metaPath)) {
      const metaContent = fs.readFileSync(metaPath, 'utf8');
      const meta = JSON.parse(metaContent);

      // 遍历 meta 配置中的每个页面
      Object.keys(meta).forEach(key => {
        const pageConfig = meta[key];

        // 跳过隐藏页面和 index 页面
        if (pageConfig.display === 'hidden' || key === 'index') {
          return;
        }

        // 检查对应的页面文件是否存在
        const pagePath = path.join(wikiDir, key);
        const pageFile = path.join(pagePath, 'page.mdx');
        const directPageFile = path.join(wikiDir, `${key}.mdx`);

        if (fs.existsSync(pageFile) || fs.existsSync(directPageFile)) {
          addWikiUrl(key, 0.6);
        }
      });
    }

    // 递归扫描子目录
    const scanDirectory = (dirPath, relativePath = '') => {
      if (!fs.existsSync(dirPath)) return;

      const items = fs.readdirSync(dirPath);

      items.forEach(item => {
        const itemPath = path.join(dirPath, item);

        try {
          const stat = fs.statSync(itemPath);

          if (stat.isDirectory() && !item.startsWith('_') && !item.startsWith('.')) {
            const subPageFile = path.join(itemPath, 'page.mdx');
            const currentPath = relativePath ? `${relativePath}/${item}` : item;

            if (fs.existsSync(subPageFile)) {
              addWikiUrl(currentPath, 0.5);
            }

            // 递归扫描子目录
            scanDirectory(itemPath, currentPath);
          }
        } catch (error) {
          // 忽略无法访问的文件/目录
          console.warn(`Warning: Cannot access ${itemPath}:`, error.message);
        }
      });
    };

    // 扫描 wiki 目录下的所有子目录
    scanDirectory(wikiDir);

  } catch (error) {
    console.warn('Error reading wiki directory:', error.message);
  }

  return wikiUrls;
}

const getPathPriority = (path) => {
  const priorities = {
    '/': { priority: 1.0, changefreq: 'daily' },
    '/blog/': { priority: 0.8, changefreq: 'weekly' },
    '/feature/': { priority: 0.9, changefreq: 'monthly' },
    '/glossary/': { priority: 0.6, changefreq: 'monthly' },
    '/wiki/': { priority: 0.6, changefreq: 'weekly' }
  };

  const matchedPath = Object.keys(priorities).find(key => path.startsWith(key) || path === key);
  return matchedPath ? priorities[matchedPath] : { priority: 0.7, changefreq: 'daily' };
};

module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_BASE_URL || "https://www.urbalytics.jp",
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  sitemapSize: 5000,
  changefreq: "daily",
  priority: 0.7,
  transform: async (config, path) => {
    const { priority, changefreq } = getPathPriority(path);
    return {
      loc: path,
      changefreq,
      priority,
      lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
    };
  },
  additionalPaths: async () => {
    const [blogPaths, glossaryPaths, wikiPaths] = await Promise.all([
      getBlogPostUrlsForAllLocales(),
      getGlossaryUrlsForAllLocales(),
      getWikiUrlsForAllLocales()
    ]);

    return [...staticPages, ...blogPaths, ...glossaryPaths, ...wikiPaths];
  },
  robotsTxtOptions: {
    policies: [
      {
        userAgent: "*",
        allow: "/",
        disallow: ["/storage/", "/storage/v1/object/public/"]
      },
      { userAgent: "Googlebot", allow: "/" },
      { userAgent: "Bingbot", allow: "/" }
    ]
  },
  exclude: ["/storage/", "/storage/v1/object/public/"],
  outDir: "public"
};