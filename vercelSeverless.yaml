
    {
      "path": "/api/cron/reinsSold",
      "schedule": "5 12 * * *"
    },
    {
      "path": "/api/cron/reinsFill/building",
      "schedule": "35 3,12 * * *"
    },
    {
      "path": "/api/cron/reinsFill/record",
      "schedule": "45 3,12 * * *"
    },
    {
      "path": "/api/cron/reinsFill/company",
      "schedule": "5 12 * * *"
    },
    {
      "path": "/api/cron/reinsFill/aggregateRecentUpdatedBuildings",
      "schedule": "55 3,12 * * *"
    }



#################################################
########  GET MANSION RENT
#################################################
getMansionRent:
  handler: task/mansionRent/getMansionRent.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
    REINS_USERNAME: "130345663208"
    REINS_PASSWORD: "mVd6WiZcuisB"
    RECORD_TYPE: "MANSION_RENT"
    TASK_NAME: "getMansionRent"
  events:
    - schedule: cron(20 3,12 * * ? *) # UTC 0 is 9am




#################################################
########  OTHER TASKSあ
################################################

refreshReins:
  handler: task/refreshReins.run
  timeout: 600
  environment:
    REINS_USERNAME: "130345663208"
    REINS_PASSWORD: "mVd6WiZcuisB"
  events:
    - schedule: cron(0 0 * * ? *) # UTC 0 is 9am

refreshReinsRepost:
  handler: task/refreshReinsRepost.run
  # runtime: nodejs16.x
  # runtime: nodejs14.x
  timeout: 600
  environment:
    REINS_USERNAME: "130345663208"
    REINS_PASSWORD: "mVd6WiZcuisB"
  events:
    - schedule: cron(30 0 ? * 2,5,6,7 *) #1-7 but 7 is saturday, 1 is sunday

siteMetricsReins:
  handler: task/siteMetrics/reins.run
  timeout: 600
  environment:
    REINS_USERNAME: "130345663208"
    REINS_PASSWORD: "mVd6WiZcuisB"
  events:
    - schedule: cron(0 0 * * ? *) # UTC 0 is 9am

siteMetricsKenbiya:
  handler: task/siteMetrics/kenbiya.run
  timeout: 600
  environment:
    KENBIYA_COMPANY_CODE: tll
    KENBIYA_USERNAME: "tllgodokaisha"
    KENBIYA_PASSWORD: "YAtHANfE"
  events:
    - schedule: cron(0 0 * * ? *) # UTC 0 is 9am



# ########  管理
# #################################################
amPostCraigslist:
  handler: task/am/postCraigslist.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    CRAIGSLIST_USERNAME: "<EMAIL>"
    CRAIGSLIST_PASSWORD: "waip.VER1sqel2yoos"
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
  events:
    - schedule: cron(0 0 ? * 1,4,6 *) # UTC 0 is 9am // this means 2,5,7 btw

# #################################################
# ########  AQUISITION
# #################################################
getSumitomo:
  handler: task/aquisition/companySite/sumitomo.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    USERNAME: "<EMAIL>"
    PASSWORD: "f56zh2yp"
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
    DB_PG_USERNAME: "postgres"
    DB_PG_PASSWORD: "GBTtG0YO9DusjLVW"
  events:
    - schedule: cron(30 3,12 * * ? *) # UTC 0 is 9am

getMitsui:
  # runtime: nodejs16.x #FIXME: this will not work because 16 includes not the chrome library
  handler: task/aquisition/companySite/mitsui.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    USERNAME: "<EMAIL>"
    PASSWORD: "f56zh2yp"
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
  events:
    - schedule: cron(30 3,12 * * ? *) # UTC 0 is 9am

getLivable:
  # runtime: nodejs16.x
  handler: task/aquisition/companySite/livable.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    USERNAME: "<EMAIL>"
    PASSWORD: "f56zh2yp"
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
  events:
    - schedule: cron(30 3,12 * * ? *) # UTC 0 is 9am

getNomu:
  # runtime: nodejs16.x
  handler: task/aquisition/companySite/nomu.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    USERNAME: "<EMAIL>"
    PASSWORD: "f56zh2yp"
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
  events:
    - schedule: cron(30 3,12 * * ? *) # UTC 0 is 9am

getMitsubishi:
  # runtime: nodejs16.x
  handler: task/aquisition/companySite/mitsubishi.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    USERNAME: "<EMAIL>"
    PASSWORD: "f56zh2yp"
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
  events:
    - schedule: cron(30 3,12 * * ? *) # UTC 0 is 9am

getAuction:
  handler: task/auction/getAuction.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
    REINS_USERNAME: "130345663208"
    REINS_PASSWORD: "mVd6WiZcuisB"
  events:
    - schedule: cron(45 12 * * ? *) # UTC 0 is 9am

getAuctionResults:
  handler: task/auction/getAuctionResults.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
    REINS_USERNAME: "130345663208"
    REINS_PASSWORD: "mVd6WiZcuisB"
  events:
    - schedule: cron(45 12 * * ? *) # 5PM at night

createAuctionChangeRecords:
  handler: task/auction/createAuctionChangeRecords.run
  timeout: 900
  role: arn:aws:iam::923942996254:role/lambda-budget-role
  memorySize: 4096 # default is 1024
  environment:
    DB_USERNAME: "admin"
    DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
    REINS_USERNAME: "130345663208"
    REINS_PASSWORD: "mVd6WiZcuisB"
  events:
    - schedule: cron(55 12 * * ? *) # UTC 0 is 9am



# getAuctionSanten:
#   handler: task/auction/getAuctionSanten.run
#   timeout: 900
#   role: arn:aws:iam::923942996254:role/lambda-budget-role
#   memorySize: 4096 # default is 1024
#   environment:
#     DB_USERNAME: "admin"
#     DB_PASSWORD: "ihXvpawx36GZpWdksroqMj"
#   events:
#     - schedule: cron(0 13 * * ? *) # must be after 98
