// Method 1: suppressHydrationWarning on specific elements
export function ComponentWithHydrationWarning() {
  return (
    <div suppressHydrationWarning>
      {/* Content that might cause hydration mismatch */}
      {typeof window !== "undefined" && window.location.href}
    </div>
  );
}

// Method 2: Use ClientOnly wrapper (already exists in your codebase)
import { ClientOnly } from "@/hooks/use-client-only";

export function ComponentWithClientOnly() {
  return (
    <ClientOnly fallback={<div>Loading...</div>}>
      <div>
        {/* Browser-only content */}
        {window.location.href}
      </div>
    </ClientOnly>
  );
}

// Method 3: Use useEffect to set client-side state
import { useState, useEffect } from "react";

export function ComponentWithUseEffect() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {/* Client-side content */}
      {window.location.href}
    </div>
  );
}

// Method 4: Dynamic import with ssr: false
import dynamic from "next/dynamic";

// Example client component
function YourClientComponent() {
  return <div>{window.location.href}</div>;
}

const ClientOnlyComponent = dynamic(
  () => Promise.resolve(YourClientComponent),
  { ssr: false },
);

export function ComponentWithDynamicImport() {
  return (
    <div>
      <ClientOnlyComponent />
    </div>
  );
}

// Method 5: Conditional rendering with typeof window check
export function ComponentWithWindowCheck() {
  return (
    <div suppressHydrationWarning>
      {typeof window !== "undefined" ? (
        <div>{window.location.href}</div>
      ) : (
        <div>Server-side placeholder</div>
      )}
    </div>
  );
}
