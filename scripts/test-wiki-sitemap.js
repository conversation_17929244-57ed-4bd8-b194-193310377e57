#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 复制 sitemap 配置中的 wiki URL 生成函数
async function getWikiUrlsForAllLocales() {
  const wikiUrls = [];
  const addedPaths = new Set(); // 防止重复添加
  const wikiDir = path.join(process.cwd(), 'app/wiki');

  const addWikiUrl = (pagePath, priority = 0.6) => {
    if (addedPaths.has(pagePath)) {
      console.log(`⚠️  Skipping duplicate path: ${pagePath}`);
      return;
    }

    addedPaths.add(pagePath);

    // 添加基础 wiki 页面
    wikiUrls.push({
      loc: `/wiki/${pagePath}`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority
    });

    // 添加多语言版本
    wikiUrls.push({
      loc: `/wiki/${pagePath}?lang=en`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority * 0.8
    });

    wikiUrls.push({
      loc: `/wiki/${pagePath}?lang=zh`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly',
      priority: priority * 0.8
    });

    console.log(`✅ Added URLs for ${pagePath} (priority: ${priority})`);
  };
  
  try {
    // 读取 _meta.json 文件获取页面配置
    const metaPath = path.join(wikiDir, '_meta.json');
    if (fs.existsSync(metaPath)) {
      const metaContent = fs.readFileSync(metaPath, 'utf8');
      const meta = JSON.parse(metaContent);
      
      console.log('📋 Found wiki meta configuration:', meta);
      
      // 遍历 meta 配置中的每个页面
      Object.keys(meta).forEach(key => {
        const pageConfig = meta[key];
        
        console.log(`🔍 Processing page: ${key}`, pageConfig);
        
        // 跳过隐藏页面和 index 页面
        if (pageConfig.display === 'hidden' || key === 'index') {
          console.log(`⏭️  Skipping ${key} (hidden or index)`);
          return;
        }
        
        // 检查对应的页面文件是否存在
        const pagePath = path.join(wikiDir, key);
        const pageFile = path.join(pagePath, 'page.mdx');
        const directPageFile = path.join(wikiDir, `${key}.mdx`);
        
        console.log(`📁 Checking files:
          - ${pageFile} (exists: ${fs.existsSync(pageFile)})
          - ${directPageFile} (exists: ${fs.existsSync(directPageFile)})`);
        
        if (fs.existsSync(pageFile) || fs.existsSync(directPageFile)) {
          addWikiUrl(key, 0.6);
        }
      });
    }
    
    // 递归扫描子目录
    const scanDirectory = (dirPath, relativePath = '') => {
      if (!fs.existsSync(dirPath)) return;
      
      const items = fs.readdirSync(dirPath);
      
      items.forEach(item => {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory() && !item.startsWith('_') && !item.startsWith('.')) {
          const subPageFile = path.join(itemPath, 'page.mdx');
          const currentPath = relativePath ? `${relativePath}/${item}` : item;
          
          console.log(`📂 Scanning directory: ${currentPath}`);
          
          if (fs.existsSync(subPageFile)) {
            addWikiUrl(currentPath, 0.5);
          }
          
          // 递归扫描子目录
          scanDirectory(itemPath, currentPath);
        }
      });
    };
    
    // 扫描 wiki 目录下的所有子目录
    console.log('🔍 Scanning wiki directory for sub-pages...');
    scanDirectory(wikiDir);
    
  } catch (error) {
    console.error('❌ Error reading wiki directory:', error.message);
  }
  
  return wikiUrls;
}

// 运行测试
async function main() {
  console.log('🧪 Testing Wiki Sitemap URL Generation\n');
  
  const wikiUrls = await getWikiUrlsForAllLocales();
  
  console.log('\n📊 Generated Wiki URLs:');
  console.log('='.repeat(50));
  
  wikiUrls.forEach((url, index) => {
    console.log(`${index + 1}. ${url.loc} (priority: ${url.priority})`);
  });
  
  console.log(`\n✅ Total URLs generated: ${wikiUrls.length}`);
  
  // 按语言分组统计
  const stats = {
    ja: wikiUrls.filter(url => !url.loc.includes('?lang=')).length,
    en: wikiUrls.filter(url => url.loc.includes('?lang=en')).length,
    zh: wikiUrls.filter(url => url.loc.includes('?lang=zh')).length
  };
  
  console.log('\n📈 Language breakdown:');
  console.log(`- Japanese (default): ${stats.ja} URLs`);
  console.log(`- English: ${stats.en} URLs`);
  console.log(`- Chinese: ${stats.zh} URLs`);
}

main().catch(console.error);
