#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting build optimization...');

// 1. Clean old build artifacts
console.log('🧹 Cleaning old build artifacts...');
try {
  if (fs.existsSync('.next')) {
    execSync('rm -rf .next', { stdio: 'inherit' });
  }
  if (fs.existsSync('tsconfig.tsbuildinfo')) {
    execSync('rm -f tsconfig.tsbuildinfo', { stdio: 'inherit' });
  }
  console.log('✅ Build artifacts cleaned');
} catch (error) {
  console.warn('⚠️ Warning: Could not clean all build artifacts:', error.message);
}

// 2. Optimize pnpm cache
console.log('📦 Optimizing pnpm cache...');
try {
  execSync('pnpm store prune', { stdio: 'inherit' });
  console.log('✅ pnpm cache optimized');
} catch (error) {
  console.warn('⚠️ Warning: Could not optimize pnpm cache:', error.message);
}

// 3. Check for large dependencies
console.log('📊 Analyzing bundle size...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const largeDeps = [
  '@aws-sdk/client-s3',
  'puppeteer',
  'puppeteer-core',
  'echarts',
  'react-pdf'
];

console.log('📋 Large dependencies detected:');
largeDeps.forEach(dep => {
  if (packageJson.dependencies[dep]) {
    console.log(`  - ${dep}: ${packageJson.dependencies[dep]}`);
  }
});

// 4. Set environment variables for faster builds
console.log('⚙️ Setting build environment variables...');
const buildEnv = {
  'NEXT_TELEMETRY_DISABLED': '1',
  'SKIP_ENV_VALIDATION': 'true',
  'NODE_OPTIONS': '--max-old-space-size=4096'
};

Object.entries(buildEnv).forEach(([key, value]) => {
  process.env[key] = value;
  console.log(`  ${key}=${value}`);
});

console.log('🎯 Build optimization complete!');
console.log('');
console.log('💡 Recommended build commands:');
console.log('  Fast build:     pnpm run build:fast');
console.log('  Turbo build:    pnpm run build:turbo');
console.log('  Standard build: pnpm run build');
