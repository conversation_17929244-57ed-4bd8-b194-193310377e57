// Saving a file to s3
// Return the s3 path as result
import s3Client from "@/lib/instances/s3client";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { logger } from "../logger";

const fs = require("fs");

export async function saveToS3(fileName: string, newFilaName: string = ""): Promise<string> {
  logger.debug(`🔥 [S3] saving file ${fileName} to ${newFilaName}🔥`);

  const fileContent = await fs.promises.readFile(fileName); // 使用异步读取文件

  const params = {
    Bucket: "urbalytics.reins.downloads",
    Key: newFilaName.length
      ? newFilaName
      : fileName.split(
        process.env.NODE_ENV === "development" ? "downloaded/" : "tmp/"
      )[1],
    Body: fileContent,
    ContentDisposition: "inline", // View instead of download
    ContentType: "application/pdf", // View instead of download
  };

  // Upload the file to S3
  await s3Client.send(new PutObjectCommand(params));

  // Retrieve the region from the S3 client configuration
  const region = await s3Client.config.region();

  // Construct the URL of the uploaded object
  const objectUrl = `https://s3.${region}.amazonaws.com/${params.Bucket}/${params.Key}`;

  logger.info(`🔥 [S3] file uploaded successfully. Accessible at: ${objectUrl}🔥`);
  return objectUrl;
}


// FIXME: cannot use for sumitomo.. because the link is not pdf, but html
export async function saveToS3FromLink({ url, fileName }: { url: string, fileName: string }): Promise<string> {
  console.log("🔥 [S3] saving file from link 🔥");
  console.log(url, fileName);

  const response = await fetch(url);
  if (!response.ok) {
    throw new Error("无法下载图像");
  }

  const contentType = response.headers.get("content-type") || "";
  const arrayBuffer = await response.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  if (buffer.length < 100) {
    logger.warn("⚠️ 文件体积过小，可能是错误页面或空文件");
  }

  // 可选：检查是否真的是 PDF 开头
  const isPdf = buffer.slice(0, 4).toString("utf-8") === "%PDF";
  if (!isPdf) {
    logger.warn("⚠️ 文件开头不是 %PDF，可能不是有效 PDF 文件");
  }

  const params = {
    Bucket: "urbalytics.reins.downloads",
    Key: fileName,
    Body: Buffer.from(arrayBuffer),
    ContentDisposition: "inline", // View instead of download
    ContentType: "application/pdf", // View instead of download
  };

  // Upload the file to S3
  await s3Client.send(new PutObjectCommand(params));

  // Retrieve the region from the S3 client configuration
  const region = await s3Client.config.region();

  // Construct the URL of the uploaded object
  const objectUrl = `https://s3.${region}.amazonaws.com/${params.Bucket}/${params.Key}`;

  logger.info(`🔥 [S3] file uploaded successfully. Accessible at: ${objectUrl}🔥`);
  return objectUrl;
}