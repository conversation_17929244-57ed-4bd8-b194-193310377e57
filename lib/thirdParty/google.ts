import { Client, Language } from "@googlemaps/google-maps-services-js";

export const googleClient = new Client({});

export async function getGeocodeInfo(address: string): Promise<{
  lat?: number;
  lng?: number;
  postalCode?: number | null;
  raw?: any;
}> {
  try {
    const res = await googleClient.geocode({
      params: {
        address,
        language: "ja",
        key: process.env.GOOGLE_API_KEY1 as string,
      },
    });

    // FIXME: chatgpt recomend to use first result, but other places in my code is using the last result
    const result = res.data?.results?.[0];
    if (!result) return { postalCode: null };

    const location = result.geometry?.location;
    const components = result.address_components || [];

    const postal = components.find((comp: any) =>
      comp.types.includes("postal_code")
    );
    const postalCode = postal?.long_name
      ? parseInt(postal.long_name.replace("-", ""), 10)
      : null;

    return {
      lat: location?.lat,
      lng: location?.lng,
      postalCode,
      raw: result,
    };
  } catch (err) {
    console.error("Geocoding failed:", err);
    return { postalCode: null };
  }
}

export async function getLatAndLngFromAddress({ address }: { address: string }) {
  let res = await googleClient.geocode({
    params: {
      address,
      language: "ja",
      key: process.env.GOOGLE_API_KEY as string,
    },
  });
  // const res = await client
  //   .findPlaceFromText({
  //     params: {
  //       input: address,
  //       inputtype: "textquery",
  //       language: "ja",
  //       fields: ["formatted_address", "geometry"],
  //       //   fields: ["place_id", "name", "formatted_address"],
  //       key: ctx.app.config.googleApi.API_KEY,
  //     },
  //     timeout: 1000,
  //   })
  //   .catch((e) => {
  //     console.log("🔥 e.response🔥");
  //     console.log(e.response);
  //     // console.log(e.response.data.error_message);
  //   });

  return res === undefined || res.data === undefined
    ? undefined
    : res.data.results; // vs candidates for findPlaceFromText
}

export async function getPostalCodeFromAddress(address: string): Promise<number | null> {
  try {
    const res = await googleClient.geocode({
      params: {
        address,
        language: "ja",
        key: process.env.GOOGLE_API_KEY as string,
      },
    });

    const components = res?.data?.results?.[0]?.address_components || [];

    const postal = components.find((comp: any) =>
      comp.types.includes("postal_code")
    );

    return postal?.long_name ? parseInt(postal.long_name.replace("-", ""), 10) : null;
  } catch (err) {
    console.error("Geocoding failed:", err);
    return null;
  }
}

export async function getAddressFromCoordinates(lat: number, lng: number) {

  const res = await googleClient.reverseGeocode({
    params: {
      // location_type: "GEOMETRIC_CENTER" as any,
      latlng: [lat, lng],
      language: "ja" as Language,
      key: process.env.GOOGLE_API_KEY as string,
    },
  });

  // Find location based on it
  //  {
  //     address_components: [ [Object], [Object], [Object], [Object] ],
  //     formatted_address: 'QR5G+G8 日本、東京都足立区',
  //     geometry: {
  //       bounds: [Object],
  //       location: [Object],
  //       location_type: 'GEOMETRIC_CENTER',
  //       viewport: [Object]
  //     },
  //     place_id: 'GhIJE1hK4CDhQUARIbscwGx6YUA',
  //     plus_code: { compound_code: 'QR5G+G8 日本、東京都足立区', global_code: '8Q7XQR5G+G8' },
  //     types: [ 'plus_code' ]
  //   },
  // Find area code

  // logger.debug("🔥 res for reverseGeocode is", res);
  if (res === undefined || res.data === undefined) return null;

  // There are different types of formatted_address, find one that does not have postcal code 〒
  // JPH5+P3 日本、東京都目黒区

  let parsedRes = "";
  res.data.results.forEach((r: any) => {
    if (r.formatted_address.indexOf("〒") === -1 && parsedRes.length === 0) {
      parsedRes = r.formatted_address.slice(11);
    }
  });

  return parsedRes.length
    ? parsedRes
    : res.data.results[0].formatted_address.slice(11);
}
