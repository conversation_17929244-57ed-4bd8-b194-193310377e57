import { logger } from "../logger";

export const LARK_URLS = {
  CHUUKAI: "https://open.larksuite.com/open-apis/bot/v2/hook/3daf8552-9e20-4ed2-82b8-7598ca4ce62d",
  SHUKYAKU: "https://open.larksuite.com/open-apis/bot/v2/hook/063e02d1-e6fa-494b-b503-85da48c202ce",
  SHIIRE: "https://open.larksuite.com/open-apis/bot/v2/hook/031715f0-6815-4e10-8cea-df82ded6fd0b",
  // FE_ERROR_CHANNEL: "https://open.larksuite.com/open-apis/bot/v2/hook/9a51f013-e610-4fb3-9071-ffa2df6992ec",

  BOT_SHUUKEI_CHANNEL: "https://open.larksuite.com/open-apis/bot/v2/hook/153dda5d-bbb6-4ac7-933c-d9c0e57217b4",
  ADMIN_ACTIVITY_CHANNEL: "https://open.larksuite.com/open-apis/bot/v2/hook/7a671a34-371f-4722-b133-49130e8cc995",
  USER_ACTIVITY_CHANNEL: "https://open.larksuite.com/open-apis/bot/v2/hook/3f5724a3-846d-400b-ba98-65c562a1d566",
  USER_AQUISITION_CHANNEL: "https://open.larksuite.com/open-apis/bot/v2/hook/e11c7c10-0c8f-4e1b-9489-b8c5a5f31900",
  BOT_DEPLOYMENT_CHANNEL: "https://open.larksuite.com/open-apis/bot/v2/hook/54777a3b-fab5-4e91-b14f-2f1c417dda9b",
}

export async function sendLark({ message, url = LARK_URLS["BOT_SHUUKEI_CHANNEL"] }: { message: any, url?: string }) {
  try {
    const response = await fetch(
      url,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          msg_type: "text",
          content: { text: message },
        }),
        mode: "no-cors", // ⚠️ This hides response data
      }
    );

    return true;
  } catch (error) {
    console.error("🔥发送卡片失败🔥", error);
    return false;
  }
}

export async function sendLarkCard(header: any, elements: any, url?: string) {
  const response = await fetch(
    LARK_URLS[url as keyof typeof LARK_URLS] || LARK_URLS["BOT_SHUUKEI_CHANNEL"],
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        msg_type: "interactive",
        card: {
          config: {
            wide_screen_mode: true,
          },
          header: {
            template: "blue",
            title: {
              tag: "plain_text",
              content: header,
            },
          },
          elements: elements,
        },
      }),
    }
  );

  if (!response.ok) {
    console.log("🔥发送卡片失败🔥");
    throw new Error("Network response was not ok");
  }

  return true;
}