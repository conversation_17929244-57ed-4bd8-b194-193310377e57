import path from "path";
import puppeteer from 'puppeteer';
import { Page } from "puppeteer";
import fs from "fs";
import dayjs from "dayjs";
import { logger } from "../logger";
const randomUseragent = require("random-useragent");

export async function getPageWithRandomUserAgent(browser: any): Promise<Page> {
  const page = await browser.newPage();
  await page.setDefaultNavigationTimeout(30000);
  await page.setDefaultTimeout(30000);

  // 🔥 优化页面资源管理 - 只阻止图片以减少内存使用
  await page.setRequestInterception(true);
  page.on('request', (req: any) => {
    try {
      const resourceType = req.resourceType();
      if (resourceType === 'image') {
        req.abort();
      } else {
        req.continue();
      }
    } catch (error) {
      // Ignore request interception errors when frame is detached
      logger.debug('🔥 Request interception error (frame may be detached):', error);
    }
  });

  // 🔥 Add frame detached error handling
  page.on('framedetached', (frame: any) => {
    logger.warn('🔥 Frame detached:', frame.url());
  });

  page.on('error', (error: any) => {
    logger.error('🔥 Page error:', error);
  });

  page.on('pageerror', (error: any) => {
    logger.error('🔥 Page script error:', error);
  });

  // 严格筛选 desktop UA
  const allUAs = randomUseragent.getAllData();
  const desktopUAs = allUAs.filter(
    (ua: any) => ua.deviceType === 'desktop' && ua.userAgent
  );

  const randomUA =
    desktopUAs[Math.floor(Math.random() * desktopUAs.length)]?.userAgent ||
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";

  logger.debug(`✅ Using user-agent: ${randomUA}`);
  await page.setUserAgent(randomUA);

  // 设置 desktop 视图
  await page.setViewport({
    width: 1280,
    height: 800,
    deviceScaleFactor: 1,
    isMobile: false,
    hasTouch: false,
  });

  // 修正 UA 相关属性（模拟 PC）
  await page.evaluateOnNewDocument(() => {
    Object.defineProperty(navigator, 'platform', {
      get: () => 'Win32',
    });
    Object.defineProperty(navigator, 'maxTouchPoints', {
      get: () => 0,
    });
  });



  return page;
}

// 🔥 Utility function to check if page is still valid
export async function isPageValid(page: any): Promise<boolean> {
  try {
    if (!page || page.isClosed()) {
      return false;
    }
    // Try to access the page URL to check if frame is still attached
    await page.url();
    return true;
  } catch (error) {
    logger.debug('🔥 Page validation failed:', error);
    return false;
  }
}

// 🔥 Safe page operation wrapper
export async function safePageOperation<T>(
  page: any,
  operation: () => Promise<T>,
  operationName: string,
  maxRetries: number = 3
): Promise<T | null> {
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      // Check if page is still valid before operation
      if (!(await isPageValid(page))) {
        throw new Error(`Page is invalid or detached for operation: ${operationName}`);
      }

      const result = await operation();
      return result;
    } catch (error: any) {
      const isFrameError = error.message && (
        error.message.includes('detached Frame') ||
        error.message.includes('Execution context was destroyed') ||
        error.message.includes('Cannot find context') ||
        error.message.includes('Target closed')
      );

      if (isFrameError) {
        logger.warn(`🔥 Frame detached during ${operationName}, attempt ${attempt + 1}/${maxRetries}`);
        if (attempt < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000)); // Wait before retry
          continue;
        }
      }

      logger.error(`🔥 ${operationName} failed on attempt ${attempt + 1}:`, error);
      if (attempt === maxRetries - 1) {
        throw error;
      }
    }
  }
  return null;
}

export async function getChromeBrowser(): Promise<any> {
  let browser;

  if (process.env.NODE_ENV !== 'development') {
    const chromium = require('@sparticuz/chromium')
    // Optional: If you'd like to disable webgl, true is the default.
    chromium.setGraphicsMode = true;

    browser = await puppeteer.launch({
      args: [
        ...chromium.args,
        '--disable-dev-shm-usage',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-extensions',
        '--no-first-run',
        '--memory-pressure-off',
        '--disable-gpu',
        '--disable-software-rasterizer',
        '--disable-background-networking',
        '--disable-default-apps',
        '--disable-sync',
        '--no-sandbox',
        '--single-process',
        '--disable-setuid-sandbox',
        '--disable-zygote',
        '--temp-dir=/tmp',
        '--disk-cache-dir=/tmp/chrome-cache',
        '--disk-cache-size=50000000'
      ],
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
    })
  } else {
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: {
        width: 1280, // 増加した幅
        height: 1024,
      },
      args: [
        '--disable-dev-shm-usage',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-extensions',
        '--no-first-run',
        '--memory-pressure-off',
        '--disable-gpu',
        '--disable-software-rasterizer',
        '--disable-background-networking',
        '--disable-default-apps',
        '--disable-sync',
        '--no-sandbox',
        '--single-process',
        '--disable-setuid-sandbox',
        '--disable-zygote',
        '--temp-dir=/tmp',
        '--disk-cache-dir=/tmp/chrome-cache',
        '--disk-cache-size=50000000'
      ]
    })
  }

  return browser;
}

export async function getDownloadPathInFunction(page: Page) {
  const downloadPath = process.env.NODE_ENV === "development" ? path.resolve(process.cwd(), "downloaded") : "/tmp"; // NOTE NOTE NOTE: This is a hack to get the download path

  const client = await page.createCDPSession();
  await client.send('Page.setDownloadBehavior', {
    behavior: 'allow',
    downloadPath,
  });

  logger.debug(`ℹ️ downloading path is ${downloadPath} for environment ${process.env.NODE_ENV}`);
  return downloadPath;
}

export async function afterClickDownloadFileAndGetFilename(page: Page, urlPathCheck: string, fileNamePrefix: string = "", downloadPath: string, clickElement: {
  type: "element" | "selfClicker",
  selector: any,
}) {

  try {
    const initialFiles = new Set(fs.readdirSync(downloadPath));
    const [response] = await Promise.all([
      page.waitForResponse((res: any) => res.url().includes(urlPathCheck)),
      clickElement.type === "selfClicker" ? clickElement.selector.click() : page.click(clickElement.selector),
    ]);

    await new Promise((resolve) => setTimeout(resolve, 1000));
    logger.debug(`🔥 Intercepted Download URL: ${response.url()}`);

    // Extract filename from Content-Disposition header
    const disposition = response.headers()["content-disposition"];
    logger.debug(`🔥 disposition is ${disposition}🔥`);

    let filename = "downloaded_file.pdf"; // Default name
    if (disposition) {
      const filenameMatch = disposition.match(/filename="([^"]+)"/);
      const utf8FilenameMatch = disposition.match(/filename\*=UTF-8''([^;]+)/);

      if (filenameMatch) {
        filename = filenameMatch[1]; // Standard filename
      } else if (utf8FilenameMatch) {
        filename = decodeURIComponent(utf8FilenameMatch[1]); // Decode UTF-8 filename
      }
    }

    const downloadedFilePath = await waitForNewFile(downloadPath, initialFiles);
    // Step 3: Find and Rename the Downloaded File
    let newFileName = `${dayjs().format("YYYYMMDDHHmmss")}_${fileNamePrefix ? `${fileNamePrefix}_` : ''}${filename}`;
    let newPath = path.join(downloadPath, newFileName); const renamedFilePath = path.join(downloadPath, newFileName);
    logger.debug(`🔥 downloadedFilePath is ${downloadedFilePath}🔥, to be renamed to ${newPath}`);

    await fs.promises.rename(downloadedFilePath, newPath);

    logger.info(`🔥 renamedFilePath is ${renamedFilePath}, newFileName is ${newFileName}, filename is ${filename}🔥`);

    return {
      localFilePath: renamedFilePath,
      oldFileName: filename,
      newFileName: newFileName,
    };
  } catch (error) {
    logger.error("🔥 Error in afterClickDownloadFileAndGetFilename", error);
    throw error;
  }
}


// 🔄 Wait for a New File in the Folder (Ignores Old Files)
async function waitForNewFile(downloadPath: string, initialFiles: Set<string>, timeout = 30000) {
  const start = Date.now();

  logger.debug("⏳ Waiting for new file to appear...");
  while (Date.now() - start < timeout) {
    logger.debug("⏳ Checking for new files... time elapsed", Date.now() - start);
    const currentFiles = new Set(fs.readdirSync(downloadPath));
    const newFiles = [...currentFiles].filter(file => !initialFiles.has(file) && file.indexOf("chromium.Chromium") === -1); // in vercel remote somehow it has this file
    logger.debug("⏳ newFiles", newFiles);

    if (newFiles.length > 0) {
      const latestFile = path.join(downloadPath, newFiles[0]);
      logger.info("✅ Detected new file:", latestFile, ", newFiles", newFiles);
      return latestFile;
    }
    await new Promise((resolve) => setTimeout(resolve, 1000)); // Check every 500ms
  }

  throw new Error(`⏳ Timeout ${timeout}ms waiting for new file download`);
}