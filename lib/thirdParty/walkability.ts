import { getLatAndLngFromAddress } from "./google";

export const getWalkabilityScore = async (address: string, lat?: string, lng?: string): Promise<any> => {
  const query = {
    format: "json",
    address,
    lat: lat ? parseFloat(lat) : undefined,
    lng: lng ? parseFloat(lng) : undefined,
    transit: 1,
    bike: 1,
    wsapikey: process.env.WALKABILITY_API_KEY,
  } as any;

  if ((lat === undefined || lng === undefined) && address !== undefined) {
    let data = await getLatAndLngFromAddress({
      address
    }) as any;

    if (data.length) {
      let { location } = data[data.length - 1].geometry;
      query.lat = location.lat;
      query.lng = location.lng;
    }
  }

  // Make a post call to get the data
  var url = new URL("https://api.walkscore.com/score");

  Object.keys(query).forEach((key) =>
    url.searchParams.append(key, query[key])
  );

  // const result = await ctx.curl(url.toString(), {
  //   // 自动解析 JSON response
  //   //   dataType: "json",
  //   // 3 秒超时
  //   //   timeout: 3000,
  // });

  return [];
}