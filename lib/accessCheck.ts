import { TllUserProps } from "@/lib/definitions/tllUser";

export const checkUserAccess = (user: TllUserProps) => {
  if (user.accessLevel >= 99) {
    return 'Super Admin'; // ultra admin in Japanese
  }

  if (user.accessLevel >= 90) {
    return 'Admin'; // super admin in Japanese
  }

  if (user.accessLevel === 1) {
    return 'Free'; // free in Japanese
  } else if (user.accessLevel === 10) {
    return 'Plus'; // premium in Japanese
  } else if (user.accessLevel === 20) {
    return 'Pro'; // pro in Japanese
  } else if (user.accessLevel === 30) {
    return 'Partner'; // partner in Japanese
  } else {
    return 'Unknown'; // unknown access level in Japanese
  }
};
