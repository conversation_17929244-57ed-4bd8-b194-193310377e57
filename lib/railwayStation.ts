import { prisma } from "./prisma";
import { RAILWAY_LINE_DATA } from "./constants/railwayLineData";

export function GetStationName(name: string): string {
  let trimmedName = name?.trim().replaceAll("　", "").replaceAll(" ", ""); // replace all types of space

  let cleanedUpStationName = "";

  // Step 1: if [], then get first one in box
  const found = trimmedName?.match(/.*?「(.*?)」.*?/); // e.g. SUUMO ＪＲ山手線「五反田」徒歩11分
  // ? means lazy match so that "ＪＲ東海道本線ほか「戸塚」駅南東方約１．９ｋｍ（道路距離）「第三公園前」バス停徒歩約２分".match(/.*「(.*)」.*/) will only match 戸塚

  const found2 = trimmedName?.match(/.*（.*）(.*)/); // 東北本線（宇都宮線）白岡

  if (found !== null && found.length == 2) {
    // length == 2 means have both match and group
    cleanedUpStationName = found[1];
  } else if (found2 !== null && found?.length == 2) {
    cleanedUpStationName = found2[1];
  }

  // 0305 disabled becauase not sure of use case, also it wont work for auction data such as 東武伊勢崎線春日部駅北東方約３．９ｋｍ（道路距離）
  // else if (found2 !== null && found2.length == 2) {
  //   cleanedUpStationName = found2[1];
  // }
  else {
    // Step 2: if got 徒歩/歩/バス replace all the stuff after it
    // KENBIYA/RAKUMACHI 京王線北野駅歩3分
    // REINS 池上線洗足池 徒歩14分, edge case: 横浜ブルー新羽
    trimmedName = trimmedName
      .replace(/徒歩.*/, "")
      .replace(/バス.*/, "")
      .replace(/歩.*/, "");

    // Step 3:remove駅, if got 線, get all those below
    let found3 = trimmedName.match(/.*?線(.*?)駅.*?/);
    if (found3 !== null && found3.length == 2) {
      // 東武伊勢崎線大袋駅南方約１．２ｋｍ（道路距離）, from AUCTION
      cleanedUpStationName = found3[1];
    } else {
      // 東武伊勢崎線大袋, from REINS
      trimmedName = trimmedName.replace("駅", "");
      cleanedUpStationName = trimmedName.replace(/.*線/, "");
    }
  }

  // Some hard coded codt
  if (name === "丸ノ内方南方南町") {
    return "方南";
  }

  // some final clean up
  [
    "つくばＥＸ",
    "ゆりかもめ",
    "千葉モノレ",
    "多摩モノレ",
    "日暮里舎人",
    "東京モノレ",
    "東急多摩川",
    "東急新横浜",
    "横浜グリー",
    "横浜ブルー",
    "江ノ電",
    "湘南モノレ",
    "湘南新宿宇",
    "湘南新宿高",
    "西武多摩川",
    "西武有楽町",
    "西武",
  ].forEach((i) => {
    if (cleanedUpStationName.includes(i)) {
      cleanedUpStationName = cleanedUpStationName.replace(i, "");
    }
  });

  return cleanedUpStationName;
}

export async function GetAggregateUserForStationFussyString(input: string) {
  let cleanedUpStationName = await GetStationName(input);

  if (cleanedUpStationName === "") {
    return null;
  }

  const allStations = await prisma.geoRailwayStation.findMany({
    where: {
      name: cleanedUpStationName,
    },
  });

  let res = {
    station: allStations.length ? cleanedUpStationName : "", // if no match do not add up the raw data // e.g. prevent issue as 丸ノ内方南方南町
    total: 0,
    results: {},
    raw_data: allStations,
  } as any;

  allStations.forEach((a: any) => {
    res["total"] +=
      a["stationUser"] && a["stationUser"]["2018"] !== undefined
        ? a["stationUser"]["2018"]
        : 0;

    if (a["railwayLineId"] && RAILWAY_LINE_DATA[a["railwayLineId"]]) {
      let lineName = RAILWAY_LINE_DATA[a["railwayLineId"]]?.name;
      let key = `${lineName}`;
      if (res["results"][key] === undefined) {
        res["results"][key] =
          a["stationUser"] && a["stationUser"]["2018"] !== undefined
            ? a["stationUser"]["2018"]
            : 0;
      } else {
        res["results"][key] +=
          a["stationUser"] && a["stationUser"]["2018"] !== undefined
            ? a["stationUser"]["2018"]
            : 0;
      }
    }
  });

  return res;
}