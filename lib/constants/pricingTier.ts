import { TllUserProps } from "../definitions/tllUser";

export const PricingTier = {
  1: {
    dailySearchCount: 20,
    dailyPriceChangeHistoryCount: 3,
    dailyRentSearchCount: 5,
    monthlyPrice: 0,
    yearlyPrice: 0,
  },
  10: {
    dailySearchCount: 100,
    monthlyPrice: 9800,
    yearlyPrice: 50000,
  },
  20: {
    dailySearchCount: 1000,
    monthlyPrice: 98000,
    yearlyPrice: 500000,
  },
  30: {
    dailySearchCount: 1000,
    monthlyPrice: 0,
    yearlyPrice: 0,
  },
  90: {
    dailySearchCount: 5000,
    monthlyPrice: 0,
    yearlyPrice: 0,
  },
  99: {
    dailySearchCount: 5000,
    monthlyPrice: 0,
    yearlyPrice: 0,
  },
} as {
  [key: number]: PricingTierProps;
};

export type PricingTierProps = {
  dailySearchCount?: number;
  dailyPriceChangeHistoryCount?: number;
  dailyRentSearchCount?: number;
  monthlyPrice: number;
  yearlyPrice: number;
};

export const getPricingTierForUser = (user: TllUserProps) => {
  if (user?.accessLevel) {
    return PricingTier[user?.accessLevel as keyof typeof PricingTier];
  }
  return null;
};
