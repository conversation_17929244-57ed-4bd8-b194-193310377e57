import { AiNewsSourceType } from "@/lib/definitions";

const allHotspots = {
  "realEstateNews": [
    {
      source: AiNewsSourceType.RAKUMACHI,
      name: "楽待NEWS",
      url: "https://www.rakumachi.jp/news/column?uiaid=topColumn",
    },
    {
      source: AiNewsSourceType.KENBIYA,
      name: "KENBIYA",
      url: "https://www.kenbiya.com/ar/",
    },
    {
      source: AiNewsSourceType.YAHOO_FUDOUSAN,
      name: "Yahoo不動産",
      url: "https://news.yahoo.co.jp/search?p=%E4%B8%8D%E5%8B%95%E7%94%A3&ei=utf-8",
    },
    {
      source: AiNewsSourceType.NIKKEI_FUDOUSAN,
      name: "日経不動産",
      url: "https://www.nikkei.com/business/realestate/",
    },
    {
      source: AiNewsSourceType.TOYOKEIZAI_FUDOUSAN,
      name: "東洋経済",
      url: "https://toyokeizai.net/list/search?fulltext=%E4%B8%8D%E5%8B%95%E7%94%A3",
    }
  ]
}

const trendingWords = {
  "snsSites": [
    "https://trends.google.com/trending?geo=JP"
  ]
}

export { allHotspots, trendingWords }; 