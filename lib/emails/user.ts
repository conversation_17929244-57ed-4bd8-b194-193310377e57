import resend from "../thirdParty/resend";
import { TllUserProps } from "@/lib/definitions/tllUser";

export async function sendOnboardingEmail({
  email,
}: {
  email: string,
}) {
  resend.emails.send({
    from: 'Urbalytics <<EMAIL>>',
    to: email as string,
    replyTo: '<EMAIL>', // 👈 回信地址
    bcc: ['<EMAIL>', '<EMAIL>'], // 👈 多个 BCC
    subject: 'Welcome to Urbalytics | アカウント作成完了のお知らせ',
    html: `<div style="font-family: sans-serif; line-height: 1.6;">
      <p>
        [English follows Japanese][日语后有中文]
      </p>

      <p>
        Urbalyticsへようこそ！<br/>
        <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">こちら</a>からログインして、Urbalyticsのデータ分析体験をお楽しみください。
      </p>

      <p>
        私は創業者のChenです。もしご不明な点やご意見がございましたら、ぜひお気軽に私までご連絡ください。<br/>
        ご連絡は <a href="mailto:<EMAIL>"><EMAIL></a> までどうぞ。
      </p>

      <p>
        ご利用、誠にありがとうございます。<br/>
        Urbalyticsチーム一同
      </p>

      <hr style="margin: 24px 0;"/>

      <p>
        Welcome to Urbalytics!<br/>
        Please <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">log in here</a> to start exploring property data insights.
      </p>

      <p>
        My name is Chen, and I'm the founder of Urbalytics. If you have any questions or suggestions, I'd love to hear from you.<br/>
        Please feel free to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.
      </p>

      <p>
        Thank you again for joining us.<br/>
        — Chen and the Urbalytics Team
      </p>

      <hr style="margin: 24px 0;"/>

      <p>
        欢迎使用 Urbalytics！<br/>
        请 <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">点击此处登录</a>，开始探索丰富的不动产数据洞察。
      </p>

      <p>
        我叫 Chen，是 Urbalytics 的创始人。如果您有任何问题或建议，我非常欢迎您的反馈。<br/>
        请随时发送邮件至 <a href="mailto:<EMAIL>"><EMAIL></a> 与我们联系。
      </p>

      <p>
        再次感谢您的加入！<br/>
        — Chen 及 Urbalytics 团队
      </p>
    </div>`,
  });
}

export async function sendNewlySetUserWelcomeEmail(data: TllUserProps) {
  resend.emails.send({
    from: 'Urbalytics <<EMAIL>>',
    to: data.email as string,
    subject: 'Welcome to Urbalytics | アカウント作成完了お知らせ',
    html: `<div>
    <p>
    [English follows Japanese][日语后有中文]
    <br/>
    <p>
    Urbalyticsへようこそ！
    <br/>
    <br/>
    アカウントが作成されました。
    <br/>
    <strong>メールアドレス:</strong> ${data.email}
    <br/>
    <strong>パスワード:</strong> ${data.password}
    <br/>
    ログイン後、パスワードを変更してください。
    <br/>
    <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">ログイン</a>
    <br/>
    ご質問がありましたら、<EMAIL> までお問い合わせください。
    <br/>
    ありがとうございました。
    <br/>
    Urbalyticsチーム
    </p>

    <br/>
    <br/>
    Welcome to Urbalytics! 
    <br/>
    <br/>
    An account has been created for you with the following credentials:
    <br/>
    <strong>Email:</strong> ${data.email}
    <br/>
    <strong>Password:</strong> ${data.password}
    <br/>
    Please change your password after logging in.
    <br/>
    <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">Login</a>
    <br/>
    If you have any questions, please contact <NAME_EMAIL>
    <br/>
    Thank you for using Urbalytics!
    <br/>
    Urbalytics Team
    </p>

    <br/>
    <br/>
    欢迎使用 Urbalytics！ 
    <br/>
    <br/>
    我们已为您创建了账户，登录信息如下：
    <br/>
    <strong>邮箱：</strong> ${data.email}
    <br/>
    <strong>密码：</strong> ${data.password}
    <br/>
    请在登录后尽快修改您的密码。
    <br/>
    <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">点击此处登录</a>
    <br/>
    如有任何疑问，请联系 <EMAIL>
    <br/>
    感谢您使用 Urbalytics！
    <br/>
    Urbalytics 团队
    </p>
    </div>
    `,
  });
}

export async function sendPasswordChangedEmail(data: TllUserProps) {
  resend.emails.send({
    from: 'Urbalytics <<EMAIL>>',
    to: data.email as string,
    subject: 'Password Changed | パスワード変更完了お知らせ',
    html: `<div>
    <p>
    [English follows Japanese][日语后有中文]
    <br/>
    <p>
    Urbalyticsへようこそ！
    <br/>
    <br/>
    パスワードが変更されました。
    <br/>
    <strong>メールアドレス:</strong> ${data.email}
    <br/>
    <strong>パスワード:</strong> ${data.password}
    <br/>
    <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">ログイン</a>
    <br/>
    ご質問がありましたら、<EMAIL> までお問い合わせください。
    <br/>
    ありがとうございました。
    <br/>
    Urbalyticsチーム
    </p>

    <br/>
    <br/>
    Welcome to Urbalytics! 
    <br/>
    <br/>
    Your password has been reset.
    <br/>
    <strong>Email:</strong> ${data.email}
    <br/>
    <strong>Password:</strong> ${data.password}
    <br/>
    <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">Login</a>
    <br/>
    If you have any questions, please contact <NAME_EMAIL>
    <br/>
    Thank you for using Urbalytics!
    <br/>
    Urbalytics Team
    </p>

    <br/>
    <br/>
    欢迎使用 Urbalytics！ 
    <br/>
    <br/>
    您的密码已成功重置，新的登录信息如下：
    <br/>
    <strong>邮箱：</strong> ${data.email}
    <br/>
    <strong>新密码：</strong> ${data.password}
    <br/>
    请点击以下链接登录并尽快修改您的密码以确保账户安全：
    <br/>
    <a href="${process.env.NEXT_PUBLIC_BASE_URL}/login">立即登录</a>
    <br/>
    如有任何疑问，欢迎联系 <EMAIL>，我们将竭诚为您服务。
    <br/>
    感谢您使用 Urbalytics！
    <br/>
    Urbalytics 团队
    </p>
    </div>
    `,
  });
}

export async function sendPasswordResetLinkEmail(data: {
  email: string,
  token: string,
}) {
  resend.emails.send({
    from: 'Urbalytics <<EMAIL>>',
    to: data.email as string,
    subject: 'Password Reset Link | パスワードリセットリンク',
    html: `<div>
    <p>
    [English follows Japanese][日语后有中文]
    <br/>
    <p>
    Urbalyticsへようこそ！
    <br/>
    <br/>
    パスワードリセットリンクを送信しました。リンクをクリックしてパスワードをリセットしてください。
    <br/>
    <a href="${process.env.NEXT_PUBLIC_BASE_URL}/pub/passwordReset?token=${data.token}">パスワードリセット</a>
    <br/>
    ご質問がありましたら、<EMAIL> までお問い合わせください。
    <br/>
    ありがとうございました。
    <br/>
    Urbalyticsチーム
    </p>

    <br/>
    <br/>
    欢迎使用 Urbalytics！  
    <br/>
    <br/>
    您申请的密码重置请求已收到。请点击下方链接设置新密码：  
    <br/>
    <a href="${process.env.NEXT_PUBLIC_BASE_URL}/pub/passwordReset?token=${data.token}">重设密码</a>  
    <br/>
    如有任何疑问，欢迎随时联系 <EMAIL>。  
    <br/>
    感谢您使用 Urbalytics！  
    <br/>
    Urbalytics 团队  
    </p>
    </div>
    `,
  });
}

