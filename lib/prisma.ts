// https://www.prisma.io/docs/orm/more/help-and-troubleshooting/nextjs-help

// lib/prisma.ts
import { PrismaClient } from "@prisma/client";

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient(
  {
    datasources: {
      db: {
        url: process.env.POSTGRES_URL,
      },
    },
    // 🔥 Enhanced connection pool configuration for better connection management
    // Use PRISMA_LOG_QUERIES=true to enable query logging when needed
    log: (() => {
      const baseLog: ('error' | 'warn')[] = ['error', 'warn'];
      if (process.env.NODE_ENV === 'development' && process.env.PRISMA_LOG_QUERIES === 'true') {
        return [...baseLog, 'query'] as const;
      }
      return process.env.NODE_ENV === 'development' ? baseLog : ['error'] as const;
    })(),
    errorFormat: 'pretty',
    // 🔥 Connection pool and timeout configuration
    transactionOptions: {
      maxWait: 5000,    // Maximum time to wait for a transaction to start (5s)
      timeout: 30000,   // Maximum time a transaction can run (30s)
    },
  }
);

if (process.env.NODE_ENV !== "production") {
  globalForPrisma.prisma = prisma;
}

export const convertKeysToCamelCase = (obj: any): any => {
  if (Array.isArray(obj)) {
    return obj.map(convertKeysToCamelCase);
  } else if (obj !== null && typeof obj === "object") {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [
        key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()), // Convert snake_case to camelCase
        convertKeysToCamelCase(value),
      ])
    );
  }
  return obj;
};