// const { getTsuboPrice } = require("./emailDailyPickUpUtilities");

import dayjs from "dayjs";
import { logger } from "../logger";
import { UserLambdaRecordProps } from "../definitions/userLambdaRecord";

const PMT = (ir: number, np: number, pv: number, fv: number, type: number) => {
  /*
   * ir   - interest rate per month
   * np   - number of periods (months)
   * pv   - present value
   * fv   - future value
   * type - when the payments are due:
   *        0: end of the period, e.g. end of month (default)
   *        1: beginning of period
   */
  var pmt, pvif;

  fv || (fv = 0);
  type || (type = 0);

  if (ir === 0) return -(pv + fv) / np;

  pvif = Math.pow(1 + ir, np);
  pmt = (-ir * (pv * pvif + fv)) / (pvif - 1);

  if (type === 1) pmt /= 1 + ir;

  return pmt;
};

export const getBuildingMaterial = (buildingMaterial: string) => {
  if (!buildingMaterial) {
    return "木造";
  }

  if (["鉄筋コンクリート", "ＨＰＣ", "ＰＣ", "RC", "ＲＣ"].some(m => buildingMaterial.includes(m))) {
    return "鉄筋コンクリート造";
  } else if (["軽量鉄骨"].some(m => buildingMaterial.includes(m))) {
    return "軽量鉄骨造";
  } else if (["鉄骨"].some(m => buildingMaterial.includes(m))) {
    return "鉄骨造";
  } else if (["木", "ブロック"].some(m => buildingMaterial.includes(m))) {
    return "木造";
  } else {
    return "木造";
  }
}

export const getBuildingPrice = ({ recordType, buildingMaterial, buildingBuiltYear, buildingSize, unitArea }: { recordType: string, buildingMaterial: string, buildingBuiltYear: number, buildingSize: number, unitArea: number }): {
  rebildBuildingPrice: number,
  remainY: number,
  remainYPerc: number,
} => {
  if (!recordType || recordType === undefined || recordType === "LAND") return { rebildBuildingPrice: 0, remainY: 0, remainYPerc: 0 };

  // recordSubType will always be present, but not buildingMaterial
  // So we will honor buildingMaterial first
  let t = getBuildingMaterial(buildingMaterial);

  let size = recordType === "BUILDING" || recordType === "HOUSE" ? buildingSize : unitArea;

  // const getSize = ({ recordType, buildingSize, unitArea }: { recordType: string, buildingSize: number, unitArea: number }) => {
  //   if (recordType === "BUILDING" || recordType === "HOUSE") {
  //     return buildingSize;
  //   } else if (recordType === "MANSION") {
  //     return record["recordValues"]?.unitArea;
  //   }

  //   return record.buildingSize;
  // };

  let y = buildingBuiltYear || 50; // 新築 will not have buildingBuiltYear, however it might be empty for valuation (assume longest for now)

  let diffYear =
    y !== undefined && y !== null
      ? dayjs().year() -
      parseInt(y.toString().indexOf("/") > -1 ? y.toString().split("/")[0] : y.toString())
      : 50; // Default max year

  logger.debug("[積算] type | size | year | diffYear", t, size, y, diffYear);

  let rebildBuildingPrice;

  const WOOD_TAIYOU = 22;
  const WOOD_PRICE = 13;
  const TEKOTSU_TAIYOU = 19; // 軽量鉄骨造では主に19年
  const TEKOSTSU_PRICE = 13;
  const TEKOTSU_JURYOU_TAIYOU = 34;
  const TEKOTSU_JURYOU_PRICE = 18;
  const RC_TAIYOU = 47;
  const RC_PRICE = 20;

  // TODO: you should recalcualte everything based on latest sekisan
  const SEKISAN_FIX = 0.2;
  const SEKISAN_VARIABLE = 0.8; // will at least keep 20% of price as default
  let remainY = 999;
  let remainYPerc = null;

  if (
    t === "木造"
  ) {
    remainY = WOOD_TAIYOU <= diffYear ? 0 : WOOD_TAIYOU - diffYear;
    remainYPerc = remainY / WOOD_TAIYOU;
    rebildBuildingPrice =
      size *
      WOOD_PRICE *
      ((remainY / WOOD_TAIYOU) * SEKISAN_VARIABLE + SEKISAN_FIX);
  } else if (t === "軽量鉄骨造") {
    remainY = TEKOTSU_TAIYOU <= diffYear ? 0 : TEKOTSU_TAIYOU - diffYear;
    remainYPerc = remainY / TEKOTSU_TAIYOU;
    rebildBuildingPrice =
      size *
      TEKOSTSU_PRICE *
      ((remainY / TEKOTSU_TAIYOU) * SEKISAN_VARIABLE + SEKISAN_FIX);
  } else if (
    t === "鉄骨造"
  ) {
    remainY =
      TEKOTSU_JURYOU_TAIYOU <= diffYear ? 0 : TEKOTSU_JURYOU_TAIYOU - diffYear;
    remainYPerc = remainY / TEKOTSU_JURYOU_TAIYOU;
    rebildBuildingPrice =
      size *
      TEKOTSU_JURYOU_PRICE *
      ((remainY / TEKOTSU_JURYOU_TAIYOU) * SEKISAN_VARIABLE + SEKISAN_FIX);
  } else if (
    t === "鉄筋コンクリート造"
  ) {
    remainY = RC_TAIYOU <= diffYear ? 0 : RC_TAIYOU - diffYear;
    remainYPerc = remainY / RC_TAIYOU;
    rebildBuildingPrice =
      size *
      RC_PRICE *
      ((remainY / RC_TAIYOU) * SEKISAN_VARIABLE + SEKISAN_FIX);
  }

  let res = {
    rebildBuildingPrice: rebildBuildingPrice ? parseInt(rebildBuildingPrice.toFixed(0)) : 0,
    remainY,
    remainYPerc: remainYPerc || 0,
  }
  // logger.debug("🔥 res for building price", res);
  return res;
};

export const getSekisanAndPercentage = (r: any): {
  rebuildPrice: number;
  sekisan: number;
  sekisanLand: number;
} => {
  let landSize = r.landSize;
  if (
    r["recordValues"]?.landSizeNotCounted !== undefined &&
    r["recordValues"]?.landSizeNotCounted !== null
  ) {
    landSize -= parseInt(r["recordValues"].landSizeNotCounted);
  }

  let landPrice = parseInt((r.valueRosenka * landSize).toFixed(0), 10);

  let rebuildPrice = landPrice + getBuildingPrice({
    recordType: r.recordType,
    buildingMaterial: r.buildingMaterial,
    buildingBuiltYear: r.buildingBuiltYear,
    buildingSize: r.buildingSize,
    unitArea: r.recordValues?.unitArea || 0,
  }).rebildBuildingPrice || 0;

  let currentPrice =
    r.price !== undefined && r.price !== null ? r.price : r.recordValues.price;

  if (rebuildPrice > 0 && rebuildPrice !== undefined) {
    // rebuild price, sekisan %, sekisan land %
    let res = {
      rebuildPrice: parseInt(rebuildPrice.toFixed(0), 10),
      sekisan: parseInt(((rebuildPrice / currentPrice) * 100).toFixed(0), 10),
      sekisanLand: parseInt(((landPrice / currentPrice) * 100).toFixed(0), 10),
    }

    logger.debug("[積算] res", res);
    return res;
  }

  return {
    rebuildPrice: 0,
    sekisan: 0,
    sekisanLand: 0,
  };
};

export const getSimpleCFCalculation = async ({ cfConfigData, record }: { cfConfigData: any, record: UserLambdaRecordProps }) => {
  let {
    "investmentSellingEOYYear": sellingEOYYear,
    "investmentMinimalROC": minimalROC,
    "investmentMinimalNetProfitPerc": minimalNetProfitPerc,
    "purchasePurchaseCostPerc": purchaseCostPerc,
    "purchaseReformCost": reformCost,
    "loanAmount": loanAmount,
    "loanInterestPerc": loanInterestPerc,
    "loanYears": loanYears,
    "holdingInitialRentIncome": initialRentIncome,
    "holdingEndRentIncome": endRentIncome,
    "holdingRunningCostPerc": runningCostPerc,
    "sellSellingFeePerc": sellingFeePerc,
    "sellSellingCapRatePerc": sellingCapRate,
    "sellSellingGfaUnitPrice": sellingGfaUnitPrice,
    "sellSellingLandUnitPrice": sellingLandUnitPrice,
  } = cfConfigData;

  // assuming upside realized in one year
  const totalIncomeGain = sellingEOYYear == 1 ? initialRentIncome || 0 : initialRentIncome + (endRentIncome || 0) * (sellingEOYYear - 1);
  const totalIncomeGainNetCost = totalIncomeGain * (1 - runningCostPerc / 100);
  const totalIncomeGainNetCostAndInterest = totalIncomeGainNetCost - loanAmount * loanInterestPerc / 100 * sellingEOYYear;

  let targetSellingPrice = 0;
  if (record.recordType === 'BUILDING') {
    targetSellingPrice = (endRentIncome / sellingCapRate) * 100;
  } else if (record.recordType === 'LAND') {
    targetSellingPrice = sellingLandUnitPrice * (record.landSize || 0) * (record.landFloorAreaRatio || 100) / 100;
  } else if (record.recordType === 'HOUSE' || record.recordType === 'MANSION') {
    targetSellingPrice = sellingGfaUnitPrice * (record.buildingSize || 0);
  }

  const targetSellingPriceNetCost = targetSellingPrice * (1 - sellingFeePerc / 100);
  // logger.debug("totalIncomeGainNetCostAndInterest", totalIncomeGainNetCostAndInterest)
  // logger.debug("targetSellingPriceNetCost", targetSellingPriceNetCost)
  // logger.debug("minimalNetProfitPerc", minimalNetProfitPerc)

  // (bid price * purchaseCost + reform cost) * minimalNetProfitPerc = total income gain + capital gain = total income gain + selling landing price - (bid price * purchaseCost + reform cost)
  // 0.2 x = total income gain + selling landing price - x
  const initialCost = (totalIncomeGainNetCostAndInterest + targetSellingPriceNetCost) / (minimalNetProfitPerc / 100 + 1);
  // logger.debug("initialCost", initialCost)
  // logger.debug("reformCost", reformCost)
  const bidPrice = (initialCost - reformCost) / (1 + purchaseCostPerc / 100);
  // logger.debug("bidPrice", bidPrice)

  const targetNetProfit = initialCost * (minimalNetProfitPerc / 100);
  const totalCapitalGain = targetSellingPriceNetCost - initialCost;

  let res = {
    totalIncomeGain,
    totalIncomeGainNetCost,
    totalIncomeGainNetCostAndInterest,
    targetSellingPrice,
    targetSellingPriceNetCost,
    totalCapitalGain,
    initialCost,
    bidPrice,
    targetNetProfit,
    roc: targetNetProfit / (initialCost - loanAmount) * 100,
  };

  return res;
};