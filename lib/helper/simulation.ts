import dayjs from "dayjs";
import { sum, cloneDeep } from "lodash-es";
import { PMT, IPMT, IRR } from "./finance";
import { calcRecommendation } from "./simulationRecommendation";

export const getHouseDepreciationYear = (type: string, builtYear: number) => {
  let accountingYearsToDepreciate = 22;
  if (type === "LIGHT_STEEL") accountingYearsToDepreciate = 19;
  if (type === "WOOD") accountingYearsToDepreciate = 22;
  if (type === "STEEL") accountingYearsToDepreciate = 34; // default to 重量鉄骨
  if (type === "CONCRETE") accountingYearsToDepreciate = 47;

  let yearsElapsed = dayjs().year() - builtYear;

  let res =
    yearsElapsed > accountingYearsToDepreciate
      ? Math.ceil(accountingYearsToDepreciate * 0.2)
      : accountingYearsToDepreciate -
      yearsElapsed +
      Math.ceil(yearsElapsed * 0.2);

  return res;
};

export const calculateResult = (data: any) => {
  const {
    locationInfo,
    propertyInfo,
    loanInfo,
    expenseInfo,
    liquidationInfo,
  } = data;

  const {
    landPrice,
    propertyPrice,
    propertyMaterial,
    propertyBuiltYear, // to add
    rentIncome,
    vacancyPercentage,
  } = propertyInfo;

  let { downPaymentPercentage, balloonLoan, balloonPayment } = loanInfo;

  downPaymentPercentage =
    downPaymentPercentage === undefined ? 0.05 : downPaymentPercentage;

  let yearsElapsed = dayjs().year() - propertyBuiltYear;

  const RENT_NATURAL_DECREMENT = yearsElapsed >= 25 ? 0.0025 : 0.005; // If house older than 30 then just use 0.25
  const OPEX_DECREASE_RATE = 0.01;
  const TAX_RATE = 0.2;
  const LIQUIDATION_MISC_FEE = 0.04;

  let result = {
    basicInfo: {
      landPrice,
      propertyPrice,
      downPayment:
        (landPrice + propertyPrice) * (1 + downPaymentPercentage) -
        loanInfo["loanAmount"],
    },
    cashflowDetailsByYear: {
      monthlyRent: [],
      yearlyRent: [],
      yearlyRentPostVacancy: [],
      yearlyOpex: [],
      netOperatingIncome: [],
      loanRepayment: [],
      loanInterest: [],
      btcf: [],
      houseDepreciationAmount: [],
      expensableAmount: [],
      taxableIncome: [],
      taxPayable: [],
      afterTaxCashFlow: [],
      accumulativedATCF: [],
      liquidationPrice: [],
      liquidationMiscCosts: [],
      remainingLoan: [],
      liquidationBtcf: [],
      housingTotalPriceAfterAmortization: [],
      taxableLiquidationGain: [],
      taxPayableLiquidationGain: [],
      liquidationAtcf: [],
      totalReturnForTheYear: [],
      irr: [],
      multiple: [],
    } as any,
  } as any;

  const { cashflowDetailsByYear } = result;

  for (let i = 1; i <= loanInfo["loanDuration"]; i++) {
    // Income calculation
    cashflowDetailsByYear.monthlyRent.push(rentIncome || 0);
    const yearlyRent =
      rentIncome * 12 * (1 - RENT_NATURAL_DECREMENT * (i - 1)); // Use simple increment

    cashflowDetailsByYear.yearlyRent.push(yearlyRent || 0);

    let yearlyRentPostVacancy = yearlyRent * (1 - vacancyPercentage);
    cashflowDetailsByYear.yearlyRentPostVacancy.push(yearlyRentPostVacancy);

    // Expenses calculations
    let yearlyOpex =
      (expenseInfo["yearlyInsuranceCost"] +
        expenseInfo["yearlyManagementFee"] +
        expenseInfo["yearlyPropertyTax"] +
        expenseInfo["yearlyOtherCosts"]) *
      Math.pow(1 - OPEX_DECREASE_RATE, i - 1);
    cashflowDetailsByYear.yearlyOpex.push(yearlyOpex || 0);

    let netOperatingIncome = yearlyRentPostVacancy - yearlyOpex;
    cashflowDetailsByYear.netOperatingIncome.push(netOperatingIncome);

    // Loan calculations
    let loanRepayment;

    if (!balloonLoan) {
      // Normal amortization
      loanRepayment =
        - PMT(
          loanInfo["loanInterest"],
          loanInfo["loanDuration"],
          loanInfo["loanAmount"]
        ) || 0;
    } else {
      // Normal payment for the non baloon part
      let nonBalloonPayment =
        -PMT(
          loanInfo["loanInterest"],
          loanInfo["loanDuration"],
          loanInfo["loanAmount"] - balloonPayment
        ) || 0;

      let balloonInterest = balloonPayment * loanInfo["loanInterest"];

      // 3 parts of the total payment
      loanRepayment =
        nonBalloonPayment +
        balloonInterest +
        (i === loanInfo["loanDuration"] ? balloonPayment : 0);
    }

    cashflowDetailsByYear.loanRepayment.push(loanRepayment);

    let btcf = netOperatingIncome - loanRepayment; // loan payment is negative
    cashflowDetailsByYear.btcf.push(btcf);

    let houseDepreciationAmount = parseInt(
      (
        propertyPrice /
        getHouseDepreciationYear(propertyMaterial, propertyBuiltYear)
      ).toFixed(0),
      10
    );
    cashflowDetailsByYear.houseDepreciationAmount.push(
      getHouseDepreciationYear(propertyMaterial, propertyBuiltYear) >= i
        ? houseDepreciationAmount
        : 0
    );

    // Loan interest calculation
    let loanInterestAmount;

    if (!balloonLoan) {
      // Normal amortization
      // The number will be negative
      loanInterestAmount = parseInt(
        IPMT(
          loanInfo["loanAmount"],
          -loanRepayment,
          loanInfo["loanInterest"],
          i - 1 // interest perido should start with 0
        ).toFixed(0),
        10
      );
    } else {
      // The number will be negative
      let repay =
        -loanRepayment +
        (i === loanInfo["loanDuration"] ? balloonPayment : 0);

      let nonBalloonInterest = parseInt(
        IPMT(
          loanInfo["loanAmount"] - balloonPayment,
          repay, // if last period, need to remove the baloon payment
          loanInfo["loanInterest"],
          i - 1
        ).toFixed(0),
        10
      );

      loanInterestAmount =
        nonBalloonInterest - balloonPayment * loanInfo["loanInterest"];
    }

    cashflowDetailsByYear.loanInterest.push(loanInterestAmount);

    let expensableAmount = parseInt(
      (
        (btcf - houseDepreciationAmount - loanInterestAmount) *
        (1 - expenseInfo["targetExpensiblePercentageOfBTCF"])
      ).toFixed(0),
      10
    );
    cashflowDetailsByYear.expensableAmount.push(expensableAmount);
    let taxableIncome =
      btcf - houseDepreciationAmount - loanInterestAmount - expensableAmount;

    cashflowDetailsByYear.taxableIncome.push(taxableIncome);
    let taxPayable = taxableIncome > 0 ? taxableIncome * TAX_RATE : 0;
    cashflowDetailsByYear.taxPayable.push(taxPayable);

    let afterTaxCashFlow = btcf - taxPayable;
    cashflowDetailsByYear.afterTaxCashFlow.push(afterTaxCashFlow);

    const accumulativedATCF = !cashflowDetailsByYear.accumulativedATCF.length
      ? afterTaxCashFlow
      : cashflowDetailsByYear.accumulativedATCF[
      cashflowDetailsByYear.accumulativedATCF.length - 1
      ] + afterTaxCashFlow;
    cashflowDetailsByYear.accumulativedATCF.push(accumulativedATCF);

    // Liquidation related
    const liquidationPrice = parseInt(
      (
        (liquidationInfo["targetLiquidationMonthlyRent"] === undefined ||
          liquidationInfo["targetLiquidationMonthlyRent"] === null
          ? yearlyRent
          : liquidationInfo["targetLiquidationMonthlyRent"] * 12) /
        liquidationInfo["targetLiquidationReturnRate"]
      ).toFixed(0),
      10
    );
    cashflowDetailsByYear.liquidationPrice.push(liquidationPrice);
    const liquidationMiscCosts = parseInt(
      (liquidationPrice * LIQUIDATION_MISC_FEE).toFixed(0),
      10
    );
    cashflowDetailsByYear.liquidationMiscCosts.push(liquidationMiscCosts);

    // Remaining loan calculation
    // Only consider principles paid when calculating the remaining loan
    const remainingLoan =
      loanInfo["loanAmount"] -
      (sum(cashflowDetailsByYear.loanRepayment) +
        sum(cashflowDetailsByYear.loanInterest));

    cashflowDetailsByYear.remainingLoan.push(remainingLoan);

    const liquidationBtcf =
      liquidationPrice - liquidationMiscCosts - remainingLoan;
    cashflowDetailsByYear.liquidationBtcf.push(liquidationBtcf);

    const housingTotalPriceAfterAmortization =
      landPrice +
      propertyPrice -
      sum(cashflowDetailsByYear.houseDepreciationAmount);
    cashflowDetailsByYear.housingTotalPriceAfterAmortization.push(
      housingTotalPriceAfterAmortization
    );

    const taxableLiquidationGain =
      liquidationPrice -
      liquidationMiscCosts -
      housingTotalPriceAfterAmortization -
      landPrice -
      (landPrice + propertyPrice) * downPaymentPercentage;
    cashflowDetailsByYear.taxableLiquidationGain.push(taxableLiquidationGain);

    const taxPayableLiquidationGain =
      taxableLiquidationGain <= 0
        ? 0
        : taxableLiquidationGain * (i <= 5 ? 0.4 : 0.2);
    cashflowDetailsByYear.taxPayableLiquidationGain.push(
      taxPayableLiquidationGain
    );

    const liquidationAtcf = liquidationBtcf - taxPayableLiquidationGain;
    cashflowDetailsByYear.liquidationAtcf.push(liquidationAtcf);

    // Total calculations
    const totalReturnForTheYear = accumulativedATCF + liquidationAtcf;
    cashflowDetailsByYear.totalReturnForTheYear.push(totalReturnForTheYear);

    // IRR calcaulation
    const downpayment =
      (landPrice + propertyPrice) * (1 + downPaymentPercentage) -
      loanInfo["loanAmount"];

    cashflowDetailsByYear.multiple.push(totalReturnForTheYear / downpayment);
    const prevCashflows = cashflowDetailsByYear.afterTaxCashFlow.slice(
      0,
      i - 1
    );

    const irrArray = [
      -downpayment,
      ...prevCashflows,
      afterTaxCashFlow + liquidationAtcf,
    ];

    let irr = 0;

    if (totalReturnForTheYear >= 0) {
      irr = IRR(irrArray);
    }

    cashflowDetailsByYear.irr.push(irr);
  }

  const initialDownPayment =
    (landPrice + propertyPrice) * (1 + downPaymentPercentage) -
    loanInfo["loanAmount"];

  return [result, initialDownPayment];
};

export async function calculate(data: any) {

  // Step 1: get normal results based on current price
  let [result, initialDownPayment] = await calculateResult(data);

  // Step 2: get new results based on
  result["recommendation"] = await calcRecommendation(
    result,
    initialDownPayment,
    data
  );

  result["recommendationsSimulation"] = {};

  // Step 3: re-simulation for decreasing price
  for (let i = 1; i <= 5; i++) {
    let updatedInput = cloneDeep(data);
    let newResult;
    // Decrease
    updatedInput["propertyInfo"]["landPrice"] =
      data["propertyInfo"]["landPrice"] - 2000000 * i;
    [newResult, initialDownPayment] = await calculateResult(updatedInput);

    result["recommendationsSimulation"][
      updatedInput["propertyInfo"]["landPrice"] +
      updatedInput["propertyInfo"]["propertyPrice"]
    ] = await calcRecommendation(
      newResult,
      initialDownPayment,
      updatedInput
    );
  }

  return result;
}
