import { toRad } from "./stats";
import { prisma } from "../prisma";
import { sum } from "lodash-es";
import { TrEvaluationCombinedRecordsProps } from "../definitions";
import { calcCoordinateDistance } from "./geoDistance";

export async function getPrefectureCodeFromAddress(address: string): Promise<string | null> {
  let match = await prisma.geoPrefecture.findFirst({
    where: {
      name: {
        contains: address.slice(0, 4) || address.slice(0, 3), // 使用地址的前4个字符或前3个字符
      },
    },
  });

  return match?.name || null;
}

export async function getStationWalkMinuteFromAddress(address: string) {
  // TODO: this is a temporary solution, we should use a better way to get the station walk minute
  if (!address) {
    return null;
  }

  if (address.indexOf("バス") !== -1) {
    return null;
  }

  let matches = address?.match(/徒歩(\d+)分/);

  if (matches !== undefined && matches?.length === 2) {
    return parseInt(matches[1], 10);
  } else {
    return null;
  }
}


export async function GuessRosenkaFromCoordinates(lat: number, lng: number, prefectureCode?: string) {
  let res = {
    guessRosenka: null,
    guessRosenkaAverage: null,
    top5Results: null,
  } as any;

  let whereFilter = {};
  if (prefectureCode) {
    whereFilter = {
      locationCode: {
        startsWith: prefectureCode,
      }
    }
  }

  // FIXME:should use a better way to get the evaluation records, asuhc as postal code
  let evaluationRecords = await prisma.trEvaluationCombinedRecord.findMany({
    where: {
      locationCode: {
        contains: prefectureCode,
      }
    },
  });

  // Use Haversine Formule ato calculate distance 
  let nearestEvaluationRecords: {
    distance: number;
    data: TrEvaluationCombinedRecordsProps;
  }[] = [];

  evaluationRecords.forEach((g) => {
    if (!g.latitude || !g.longitude) {
      return;
    }

    let dis = calcCoordinateDistance(g.latitude, g.longitude, lat, lng);

    nearestEvaluationRecords.push({
      distance: dis * 1000, // convert into m
      data: g as TrEvaluationCombinedRecordsProps,
    });
  });

  // Use closest 5 only
  nearestEvaluationRecords.sort((a, b) => (a.distance > b.distance ? 1 : -1));
  let top5 = nearestEvaluationRecords.slice(0, 5);

  const results: any[] = [];
  let minRosenka = 9999999;
  let averageRosenkaArray: number[] = [];

  top5.forEach((t: any) => {
    // One record will have 3 data, kouji2022 or rosenka2011 or kijun2021
    let recordRosenka = t.data.kouji2023 * 0.8 || t.data.kijun2022 * 0.8;
    averageRosenkaArray.push(recordRosenka);

    minRosenka =
      recordRosenka < minRosenka && recordRosenka !== null && recordRosenka
        ? recordRosenka
        : minRosenka;

    results.push({
      distance: parseInt(t.distance.toFixed(0), 10),
      longitude: t.data.longitude,
      latitude: t.data.latitude,
      nearestStation: t.data.nearestStation,
      locationName: t.data.locationName,
      priceMultiplier: parseFloat(
        (
          (t.data.kouji_2022 === 0 || t.data.kouji_2022 === null
            ? t.data.kijun_2021
            : t.data.kouji_2022) / t.data.rosenka_2021
        ).toFixed(2)
      ),
      price: {
        rosenka2021: t.data.rosenka_2021 / 10000,
        kouji2022: t.data.kouji_2022 / 10000,
        kijun2021: t.data.kijun_2021 / 10000,
      },
      addressRegistered: t.data.addressRegistered,
      landUsage: t.data.landUsage,
    });
  });

  res["guessRosenka"] = minRosenka / 10000;
  res["guessRosenkaAverage"] =
    sum(averageRosenkaArray) / averageRosenkaArray.length / 10000;
  res["top5Results"] = results;

  return res;
}