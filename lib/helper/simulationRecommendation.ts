import dayjs from "dayjs";
import { indexOf, max } from "lodash-es";
import { getWalkabilityScore } from "../thirdParty/walkability";
export const getRebuildResults = (locationInfo: any, propertyInfo: any) => {
  let type = propertyInfo["propertyMaterial"];
  let rebuildUnitPrice = 15; // Wood
  if (type === "WOOD") rebuildUnitPrice = 15;
  if (type === "LIGHT_STEEL") rebuildUnitPrice = 16;
  if (type === "STEEL") rebuildUnitPrice = 19; // default to 重量鉄骨
  if (type === "CONCRETE") rebuildUnitPrice = 23;

  let accountingYearsToDepreciate = 22;
  if (type === "LIGHT_STEEL") accountingYearsToDepreciate = 19; // default to 重量鉄骨
  if (type === "WOOD") accountingYearsToDepreciate = 22;
  if (type === "STEEL") accountingYearsToDepreciate = 34; // default to 重量鉄骨

  if (type === "CONCRETE") accountingYearsToDepreciate = 47;

  let yearsElapsed = dayjs().year() - propertyInfo["propertyBuiltYear"];

  // Note setback price is only considered 30% of normal price

  let landRebuildlPrice;
  let landRebuildlPriceFromSetBack;

  if (propertyInfo["landSetbackSize"] === undefined) {
    landRebuildlPrice = locationInfo["rosenka"] * propertyInfo["landSize"];
  } else {
    landRebuildlPriceFromSetBack =
      locationInfo["rosenka"] * propertyInfo["landSetbackSize"] * 0.3;
    landRebuildlPrice =
      locationInfo["rosenka"] *
      (propertyInfo["landSize"] - propertyInfo["landSetbackSize"]) +
      landRebuildlPriceFromSetBack;
  }

  const propertyRebuildPrice =
    rebuildUnitPrice *
    10000 *
    propertyInfo["propertySize"] *
    (yearsElapsed >= accountingYearsToDepreciate
      ? 0
      : 1 - yearsElapsed / accountingYearsToDepreciate);

  const totalRebuildPrice = landRebuildlPrice + propertyRebuildPrice;
  return {
    landRebuildlPriceFromSetBack,
    landRebuildlPrice,
    propertyRebuildPrice,
    totalRebuildPrice,
    rebuildPercentage:
      totalRebuildPrice /
      (propertyInfo["propertyPrice"] + propertyInfo["landPrice"]),
  };
};

export const calcRecommendation = async (
  result: any,
  initialDownPayment: number,
  dataBody: any,
) => {
  const { locationInfo, propertyInfo } = dataBody;

  // Adding two more fields
  const IRR_LOWER_BAR = 10; // 0 - 20 will be 0 - 50
  const REBUILD_LOWER_BAR = 60; // 20-100 will be 0 to 40
  const REBUILD_RANGE = 40;
  const REPAY_UPPER_BAR = 60; // 20-100 will be 40 to 0
  const REPAY_RANGE = 40;
  const WALKABILITY_LOWER_BAR = 80; // 60 - 100 will be 0 to 10
  const WALKABILITY_RANGE = 20; // 60 - 100 will be 0 to 10

  let recommendation = {
    irr: {} as any,
    rebuild: {} as any,
    repay: {} as any,
    walkability: {} as any,
    overallRecommendation: {} as any,
  } as any;

  // Metrirc 1 - IRR
  let irr = {} as any;
  let irrScore = parseFloat(
    result["cashflowDetailsByYear"]["irr"].length > 5
      ? result["cashflowDetailsByYear"]["irr"][4].toFixed(2)
      : 0 // use 5th year data
  );
  let irrTrendScore = parseFloat(
    (((irrScore - IRR_LOWER_BAR) / IRR_LOWER_BAR) * 100).toFixed(2)
  );
  irr["output"] = {
    score: irrScore,
    scoreString: irrScore + "%",
    trendScore: irrTrendScore,
    trendValue: irrTrendScore + "%",
    trend: irrScore < IRR_LOWER_BAR ? "down" : "up",
  };
  irr["bestYearOfXIRR"] =
    indexOf(
      result["cashflowDetailsByYear"]["irr"],
      max(result["cashflowDetailsByYear"]["irr"])
    ) + 1;
  recommendation["irr"] = irr;

  // Metrirc 2 - Rebuild
  let rebuild = getRebuildResults(locationInfo, propertyInfo) as any;
  let rebuildScore = parseFloat(
    (rebuild["rebuildPercentage"] * 100).toFixed(2)
  );
  let rebuildTrendScore = parseFloat(
    (((rebuildScore - REBUILD_LOWER_BAR) / REBUILD_RANGE) * 100).toFixed(2)
  );

  rebuild["output"] = {
    score: rebuildScore,
    scoreString: rebuildScore + "%",
    trendScore: rebuildTrendScore,
    trendValue: rebuildTrendScore + "%",
    trend: rebuildScore < REBUILD_LOWER_BAR ? "down" : "up",
  };
  recommendation["rebuild"] = rebuild;

  // Metrirc 3 - Repay
  let repay = {} as any;
  let repayScore = parseFloat(
    (
      (result["cashflowDetailsByYear"]["loanRepayment"][0] /
        result["cashflowDetailsByYear"]["netOperatingIncome"][0]) *
      100
    ).toFixed(2)
  );
  let repayTrendScore = parseFloat(
    (((REPAY_UPPER_BAR - repayScore) / REPAY_RANGE) * 100).toFixed(2)
  );

  repay["output"] = {
    score: repayScore,
    scoreString: repayScore + "%",
    trendScore: repayTrendScore,
    trendValue: repayTrendScore + "%",
    trend: repayScore > REBUILD_LOWER_BAR ? "down" : "up",
  };
  recommendation["repay"] = repay;

  // Metrirc 4 - Walkability
  const walkabilityResult = await getWalkabilityScore(
    locationInfo["address"]
  );

  var newData = JSON.parse(walkabilityResult?.data.toString());

  let walkScore = newData.walkscore === undefined ? 0 : newData.walkscore;
  let walkabilityTrendScore = parseFloat(
    (((walkScore - WALKABILITY_LOWER_BAR) / WALKABILITY_RANGE) * 100).toFixed(2)
  );

  let walkability = {} as any;
  // let walkabilityScore = await app.controller.us
  walkability["output"] = {
    score: walkScore,
    scoreString: walkScore + "%",
    trendScore: walkabilityTrendScore,
    trendValue: walkabilityTrendScore + "%",
    trend: walkScore < WALKABILITY_LOWER_BAR ? "down" : "up",
  };

  recommendation["walkability"] = walkability;

  // Metrirc (5) - COC
  recommendation["cashOnCash"] = parseFloat(
    (
      (result["cashflowDetailsByYear"]["afterTaxCashFlow"][0] /
        initialDownPayment) *
      100
    ).toFixed(2)
  ); // Only consider first year for cash on cash

  ////////////////////////////////
  // Overall recommendations
  ///////////////////////////////////
  /// Ranking algorithm
  /// IRR - max of 50
  /// Hensai - max of 20
  /// Sekisan - max of 20
  /// Futuristic - max of 10
  /// Score = half of point + trend * half

  let scoring = {
    irr: 50,
    rebuild: 20,
    repay: 20,
    walkability: 10,
  } as any;

  let overallRecommendation = {} as any;
  let overallScore = 0;
  let notRecommendedItemCount = 0;
  ["irr", "rebuild", "repay", "walkability"].forEach((i) => {
    if (recommendation[i]["output"]["trend"] === "down") {
      notRecommendedItemCount += 1;
    }

    let score =
      (scoring[i] / 2) * (1 + recommendation[i]["output"]["trendScore"] / 100);

    score = score >= scoring[i] ? scoring[i] : score;
    score = score <= 0 ? 0 : score;

    overallRecommendation[`${i}Score`] = score;
    overallScore += score;
  });
  overallRecommendation["notRecommendedItemCount"] = notRecommendedItemCount;
  overallRecommendation["recommendation"] =
    notRecommendedItemCount > 0 ? "NOT RECOMMENDED" : "RECOMMENDED";
  recommendation["overallRecommendation"] = overallRecommendation;
  recommendation["overallScore"] = overallScore.toFixed(0)

  return recommendation;
};