import { sum } from "lodash-es";
import { logger } from "../logger";

export const removeOutlier = (resultsCapArray: number[], type = "") => {
  // 过滤掉无效值
  let filteredRaw = resultsCapArray.filter(
    (a) => a !== 0 && a !== undefined && a !== null
  );

  if (filteredRaw.length < 4) {
    return filteredRaw; // Too few elements to determine outliers
  }

  // 根据类型进行初步过滤
  const typeLimits: { [key: string]: number } = {
    gfa: 3000,
    rent: 100000,
    issu: 5000,
    sekisanPerc: 200,
  };

  if (type in typeLimits) {
    filteredRaw = filteredRaw.filter((a) => a < typeLimits[type]);
  }

  // Sort the array (required for percentile calculations)
  const sortedArr = [...filteredRaw].sort((a, b) => a - b);

  // Function to compute percentile-based value
  function percentile(arr: number[], p: number) {
    const index = (arr.length - 1) * p;
    const lower = Math.floor(index);
    const upper = lower + 1;
    const weight = index - lower;

    if (upper >= arr.length) return arr[lower];
    return arr[lower] * (1 - weight) + arr[upper] * weight;
  }

  // Calculate Q1 (25th percentile) and Q3 (75th percentile)
  const Q1 = percentile(sortedArr, 0.25);
  const Q3 = percentile(sortedArr, 0.75);

  // Compute IQR (Interquartile Range)
  const IQR = Q3 - Q1;

  if (IQR === 0) return filteredRaw; // Prevent division by zero

  // Define lower and upper bounds for outliers
  const lowerBound = Q1 - 1.5 * IQR;
  const upperBound = Q3 + 1.5 * IQR;

  // Filter values within the valid range
  return filteredRaw.filter(value => value >= lowerBound && value <= upperBound).sort((a, b) => a - b);
};

// Converts numeric degrees to radians
export function toRad(Value: number) {
  return (Value * Math.PI) / 180;
}
