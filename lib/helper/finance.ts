export const PMT = (ir: number, np: number, pv: number, fv?: number, type?: number) => {
  /*
   * ir   - interest rate per month
   * np   - number of periods (months)
   * pv   - present value
   * fv   - future value
   * type - when the payments are due:
   *        0: end of the period, e.g. end of month (default)
   *        1: beginning of period
   */
  var pmt, pvif;

  fv || (fv = 0);
  type || (type = 0);

  if (ir === 0) return -(pv + fv) / np;

  pvif = Math.pow(1 + ir, np);
  pmt = (-ir * (pv * pvif + fv)) / (pvif - 1);

  if (type === 1) pmt /= 1 + ir;

  return pmt;
};

export const IPMT = (pv: number, pmt: number, rate: number, per: number) => {
  var tmp = Math.pow(1 + rate, per);
  return 0 - (pv * tmp * rate + pmt * (tmp - 1));
};

export const cPPMT = (rate: number, per: number, nper: number, pv: number, fv: number, type: number) => {
  if (per < 1 || per >= nper + 1) return null;
  var pmt = PMT(rate, nper, pv, fv, type);
  var ipmt = IPMT(pv, pmt, rate, per - 1);
  return pmt - ipmt;
};

export const IRR = (CArray: number[]) => {
  let min = 0.0;
  let max = 1.0;
  let guess = 0;
  let NPV = 0;

  // Heuristic, max 500 guesses
  for (let i = 0; i < 500; i++) {
    guess = (min + max) / 2;
    NPV = 0;
    for (var j = 0; j < CArray.length; j++) {
      NPV += CArray[j] / Math.pow(1 + guess, j);
    }

    if (Math.abs(NPV) <= 0.000001) {
      // console.log("finish, round is", i);
      return guess * 100;
    }

    if (NPV > 0) {
      min = guess;
    } else {
      max = guess;
    }
  }

  return guess * 100;
};
