import NextAuth, { DefaultSession } from 'next-auth';
import Credentials from 'next-auth/providers/credentials';
import { prisma } from '@/lib/prisma'; // 引入 prisma
import { z } from 'zod';
import GoogleProvider from "next-auth/providers/google";
import AppleProvider from "next-auth/providers/apple";

import { authConfig } from '@/lib/auth.config';
import bcrypt from 'bcrypt';
import { logger } from './logger';
import { TllUserProps, TllUserSubscriptionStatus } from './definitions/tllUser';
import { sendLark, LARK_URLS } from './thirdParty/lark';
import { sendNewlySetUserWelcomeEmail, sendOnboardingEmail } from './emails/user';

declare module 'next-auth' {
  interface User {
    accessLevel: number;
    subscriptionStatus: TllUserSubscriptionStatus;
    imageUrl: string;
  }
  interface Session extends DefaultSession {
    user?: User & {
      accessLevel: number;
      subscriptionStatus: TllUserSubscriptionStatus;
      imageUrl: string;
    };
  }
}

export async function getUserByEmail({ email }: { email: string }): Promise<TllUserProps | null> {
  const user = await prisma.tllUser.findUnique({ where: { email } });
  return user as TllUserProps | null;
}

export const { auth, handlers, signIn, signOut } = NextAuth({
  ...authConfig,
  // debug: true, // ✅ Enables detailed error logging
  providers: [
    Credentials({
      async authorize(credentials) {
        const parsedCredentials = z
          .object({
            email: z.string().email(),
            password: z.string().min(6),
          })
          .safeParse(credentials);

        if (parsedCredentials.success) {
          const { email, password } = parsedCredentials.data;

          const user = await getUserByEmail({ email });
          if (!user) return null;
          if (!user.password) return null;

          const passwordsMatch = await bcrypt.compare(password, user.password);

          if (passwordsMatch) {
            return { ...user, accessLevel: user.accessLevel, subscriptionStatus: user.subscriptionStatus as TllUserSubscriptionStatus, imageUrl: user.imageUrl || '' }; // 返回用户和访问级别
          }
        }

        logger.error('Invalid credentials');
        return null;
      },
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    AppleProvider({
      clientId: process.env.APPLE_CLIENT_ID,
      clientSecret: process.env.APPLE_CLIENT_SECRET,
    }),
  ],
  // 🔥 Extend session data to include `id` and `accessLevel`
  callbacks: {
    async session({ session, token }) {
      // Below works for google login as well 
      if (session.user) {
        // logger.info("🔥 session.user", session.user);
        session.user.id = token.id as string; // Attach `id` to the session
        session.user.accessLevel = token.accessLevel as number; // Attach `accessLevel` to the session
        session.user.subscriptionStatus = token.subscriptionStatus as TllUserSubscriptionStatus; // Attach `subscriptionStatus` to the session
        session.user.imageUrl = token.imageUrl as string; // Attach `imageUrl` to the session
      }

      return session;
    },
    async jwt({ token, user, account }) {
      // console.log("🔥 jwt", token, user, account);
      // if (user) {
      //   token.id = user.id; // Attach `id` to JWT
      //   token.accessLevel = user.accessLevel; // Attach `accessLevel` to JWT
      // }
      // ✅ Google 登录用户：重新从数据库获取信息
      if (account?.provider === 'google' && user?.email) {
        const dbUser = await getUserByEmail({ email: user.email });

        if (dbUser) {
          token.id = dbUser.id;
          token.accessLevel = dbUser.accessLevel;
          token.subscriptionStatus = dbUser.subscriptionStatus;
          token.imageUrl = dbUser.imageUrl;
        } else {
          // 万一数据库找不到，保险起见降级处理（虽然 signIn() 已经拦了）
          token.accessLevel = 0;
          token.subscriptionStatus = TllUserSubscriptionStatus.FREE;
        }
      }

      // ✅ Credentials 登录用户（user 已包含 accessLevel）
      if (user && account?.provider === 'credentials') {
        token.id = user.id;
        token.accessLevel = user.accessLevel;
        token.subscriptionStatus = user.subscriptionStatus;
        token.imageUrl = user.imageUrl;
      }

      return token;
    },
    async signIn({ user, account }) {
      console.log("🔥 signIn", user, account);

      if (account?.provider === 'google' && user.email) {
        console.log("🔥 signIn using google", user, account);
        const existingUser = await getUserByEmail({ email: user.email });

        // if (!existingUser) {
        //   // ❌ 不允许 Google 用户登录（账号未注册）
        //   console.warn(`[auth] Google login rejected for unknown user: ${user.email}`);
        //   return false;
        // }

        if (existingUser) {
          // ✅ 已存在账号，说明之前用密码注册过
          // 更新 name/image 等资料
          let existingUserLoginHistory = existingUser.loginHistory && existingUser.loginHistory.length > 0 ? existingUser.loginHistory : [];

          existingUserLoginHistory.push({
            loginAt: new Date(),
            loginMethod: 'google',
          });

          await prisma.tllUser.update({
            where: { email: user.email },
            data: {
              name: user.name ?? undefined,
              imageUrl: user.image ?? undefined,
              lastLoginAt: new Date(),
              loginHistory: existingUserLoginHistory,
              // 可以记录 loginProvider: 'google'
            },
          });

          await sendLark({
            message: `[auth][google] Login successful: ${user.email}`,
            url: LARK_URLS.USER_AQUISITION_CHANNEL,
          });
        } else {
          // DO not create 🆕 新建账号
          await prisma.tllUser.create({
            data: {
              source: 'google',
              name: user.name,
              email: user.email,
              imageUrl: user.image ?? '',
              accessLevel: 1,
              loginHistory: [{
                loginAt: new Date(),
                loginMethod: 'google',
              }],
            },
          });

          await sendOnboardingEmail({ email: user.email });

          await sendLark({
            message: `[auth][google] Login + account creation for: ${user.email}`,
            url: LARK_URLS.USER_AQUISITION_CHANNEL,
          });
        }
      }

      // ✅ Credentials 登录也记录登录时间
      if (account?.provider === 'credentials' && user.email) {
        const existingUser = await getUserByEmail({ email: user.email });
        let userLoginHistory = existingUser?.loginHistory && existingUser.loginHistory.length > 0 ? existingUser.loginHistory : [];

        if (userLoginHistory) {
          userLoginHistory.push({
            loginAt: new Date(),
            loginMethod: 'credentials',
          });
        }

        await prisma.tllUser.update({
          where: { email: user.email },
          data: {
            lastLoginAt: new Date(),
            loginHistory: userLoginHistory,
          },
        });

        await sendLark({
          message: `[auth][credentials] Login successful: ${user.email}`,
          url: LARK_URLS.USER_AQUISITION_CHANNEL,
        });
      }

      return true; // Allow login
    },
  },
});
