import type { NextAuthConfig } from 'next-auth';

export const authConfig = {
  // secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: '/login',
  },
  session: {
    strategy: "jwt",  // ✅ Use JWT instead of database session
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },
  jwt: {
    maxAge: 7 * 24 * 60 * 60, // 7 days
  },

  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      // console.log("🔥 auth", auth);

      const isLoggedIn = !!auth?.user;

      if (!isLoggedIn) return false;

      return true;
    },
  },
  providers: [], // Add providers with an empty array for now
} satisfies NextAuthConfig;