// lib/prismaUtils.ts
import { prisma } from './prisma';
import { logger } from './logger';

/**
 * 🔥 Utility function to handle batch operations with proper connection management
 * Prevents connection timeouts by processing data in smaller batches
 */
export async function processBatchWithConnectionManagement<T, R>(
  items: T[],
  batchSize: number = 50,
  processor: (batch: T[]) => Promise<R[]>,
  onBatchComplete?: (batchIndex: number, totalBatches: number) => void
): Promise<R[]> {
  const results: R[] = [];
  const totalBatches = Math.ceil(items.length / batchSize);
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchIndex = Math.floor(i / batchSize) + 1;
    
    try {
      logger.info(`🔥 Processing batch ${batchIndex}/${totalBatches} (${batch.length} items)`);
      
      const batchResults = await processor(batch);
      results.push(...batchResults);
      
      // 🔥 Add small delay between batches to prevent connection overload
      if (batchIndex < totalBatches) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      onBatchComplete?.(batchIndex, totalBatches);
      
    } catch (error) {
      logger.error(`🔥 Error processing batch ${batchIndex}:`, error);
      throw error;
    }
  }
  
  return results;
}

/**
 * 🔥 Utility function to execute database operations with retry logic
 * Handles connection timeouts and retries failed operations
 */
export async function executeWithRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;
      
      // 🔥 Check if it's a connection-related error
      if (error.code === 'P1017' || error.code === 'P2024' || error.message?.includes('connection')) {
        logger.warn(`🔥 Connection error on attempt ${attempt}/${maxRetries}:`, error.message);
        
        if (attempt < maxRetries) {
          // 🔥 Exponential backoff
          const delay = delayMs * Math.pow(2, attempt - 1);
          logger.info(`🔥 Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }
      
      // 🔥 If it's not a connection error or we've exhausted retries, throw immediately
      throw error;
    }
  }
  
  throw lastError!;
}

/**
 * 🔥 Utility function to safely disconnect Prisma client
 * Use this in long-running operations or when you need to ensure clean shutdown
 */
export async function safeDisconnect(): Promise<void> {
  try {
    await prisma.$disconnect();
    logger.info('🔥 Prisma client disconnected successfully');
  } catch (error) {
    logger.error('🔥 Error disconnecting Prisma client:', error);
  }
}

/**
 * 🔥 Utility function to check database connection health
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error('🔥 Database connection check failed:', error);
    return false;
  }
}

/**
 * 🔥 Utility function for safe transaction execution with timeout
 */
export async function executeTransaction<T>(
  operation: (tx: any) => Promise<T>,
  timeoutMs: number = 30000
): Promise<T> {
  return executeWithRetry(async () => {
    return await prisma.$transaction(operation, {
      timeout: timeoutMs,
      maxWait: 5000, // Maximum time to wait for a connection from the pool
    });
  });
}
