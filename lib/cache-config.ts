// lib/cache-config.ts

/**
 * 🚀 Centralized cache configuration for different data types
 * This makes it easy to adjust cache strategies across the application
 */

// Time constants for better readability
export const TIME = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
} as const;

/**
 * Cache configurations for different data types
 */
export const CACHE_CONFIG = {
  // Homepage counts - updates once or twice per day
  HOMEPAGE_COUNTS: {
    key: 'homepage-counts',
    expiry: 12 * TIME.HOUR, // 12 hours
    description: 'Homepage property counts (newly created, price changes)'
  },

  // User profile - changes occasionally
  USER_PROFILE: {
    key: 'user-profile',
    expiry: 2 * TIME.HOUR, // 2 hours
    description: 'User profile information'
  },

  // Search results - can be cached for short periods
  SEARCH_RESULTS: {
    key: 'search-results',
    expiry: 15 * TIME.MINUTE, // 15 minutes
    description: 'Property search results'
  },

  // Static data - rarely changes
  STATIC_DATA: {
    key: 'static-data',
    expiry: 1 * TIME.DAY, // 1 day
    description: 'Static reference data (areas, stations, etc.)'
  },

  // Real-time data - very short cache
  REALTIME_DATA: {
    key: 'realtime-data',
    expiry: 1 * TIME.MINUTE, // 1 minute
    description: 'Real-time data that changes frequently'
  },

  // API responses - medium cache
  API_RESPONSES: {
    key: 'api-responses',
    expiry: 30 * TIME.MINUTE, // 30 minutes
    description: 'General API response caching'
  }
} as const;

/**
 * Helper function to get cache config by type
 */
export function getCacheConfig(type: keyof typeof CACHE_CONFIG) {
  return CACHE_CONFIG[type];
}

/**
 * Helper function to format cache expiry time for display
 */
export function formatCacheExpiry(milliseconds: number): string {
  const hours = Math.floor(milliseconds / TIME.HOUR);
  const minutes = Math.floor((milliseconds % TIME.HOUR) / TIME.MINUTE);
  
  if (hours > 0) {
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
  return `${minutes}m`;
}

/**
 * Helper function to check if cache is expired
 */
export function isCacheExpired(timestamp: number, expiry: number): boolean {
  return Date.now() - timestamp > expiry;
}

/**
 * Helper function to get cache timestamp key
 */
export function getCacheTimestampKey(cacheKey: string): string {
  return `${cacheKey}-timestamp`;
}

/**
 * 🔥 Debug helper to log cache information
 */
export function logCacheInfo(cacheKey: string, action: 'hit' | 'miss' | 'set' | 'clear') {
  if (process.env.NODE_ENV === 'development') {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`🔥 [${timestamp}] Cache ${action.toUpperCase()}: ${cacheKey}`);
  }
}
