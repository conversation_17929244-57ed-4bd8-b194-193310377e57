/**
 * 🔥 Daily usage tracker utility for localStorage
 * Tracks daily usage limits for features using record ID as unique key
 */

export interface DailyUsageData {
  date: string; // YYYY-MM-DD format
  keys: string[]; // Array of used keys for this feature
}

/**
 * Get today's date in YYYY-MM-DD format
 */
const getTodayString = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Get the localStorage key for a specific feature
 */
const getStorageKey = (feature: string): string => {
  return `daily_usage_${feature}`;
};

/**
 * Increment usage count for a specific feature and record ID
 * @deprecated Use markDailyUsage instead for single-count-per-record behavior
 */
export const incrementDailyUsage = (feature: string, recordId: string): number => {
  // Just use markDailyUsage for backward compatibility
  const wasMarked = markDailyUsage(feature, recordId);
  return wasMarked ? 1 : 0;
};

/**
 * Mark daily usage for a specific feature and record ID (only counts once per day per record)
 */
export const markDailyUsage = (feature: string, recordId: string): boolean => {
  if (typeof window === 'undefined') return false;

  try {
    const key = getStorageKey(feature);
    const stored = localStorage.getItem(key);
    const today = getTodayString();

    let data: DailyUsageData;

    if (!stored) {
      // Create new data structure
      data = {
        date: today,
        keys: []
      };
    } else {
      data = JSON.parse(stored);

      // If the stored date is not today, reset the data
      if (data.date !== today) {
        data = {
          date: today,
          keys: []
        };
      }
    }

    // If already viewed today, don't add again
    if (data.keys.includes(recordId)) {
      console.log(`🔥 Daily usage already marked for ${feature}:${recordId} - Already in keys`);
      return false;
    }

    // Add this recordId to the keys array
    data.keys.push(recordId);
    localStorage.setItem(key, JSON.stringify(data));

    console.log(`🔥 Daily usage marked for ${feature}:${recordId} - Total keys: ${data.keys.length}`);
    return true;
  } catch (error) {
    console.error('🔥 Error marking daily usage:', error);
    return false;
  }
};

/**
 * Check if total daily usage limit is exceeded for a specific feature
 */
export const isTotalDailyUsageLimitExceeded = (
  feature: string,
  limit: number
): boolean => {
  const totalCount = getTotalDailyUsageCount(feature);
  return totalCount >= limit;
};

/**
 * Reset daily usage for a specific feature (mainly for testing)
 */
export const resetDailyUsage = (feature: string): void => {
  if (typeof window === 'undefined') return;

  try {
    const key = getStorageKey(feature);
    localStorage.removeItem(key);
    console.log(`🔥 Daily usage reset for ${feature}`);
  } catch (error) {
    console.error('🔥 Error resetting daily usage:', error);
  }
};

/**
 * Reset daily usage for a specific feature and record ID (mainly for testing)
 * @deprecated Use resetDailyUsage(feature) instead
 */
export const resetDailyUsageForRecord = (feature: string, recordId: string): void => {
  // For backward compatibility, we'll remove the specific record from the keys array
  if (typeof window === 'undefined') return;

  try {
    const key = getStorageKey(feature);
    const stored = localStorage.getItem(key);

    if (!stored) return;

    const data: DailyUsageData = JSON.parse(stored);
    const today = getTodayString();

    // If the stored date is not today, just remove the entire entry
    if (data.date !== today) {
      localStorage.removeItem(key);
      return;
    }

    // Remove the specific recordId from keys array
    data.keys = data.keys.filter(id => id !== recordId);

    if (data.keys.length === 0) {
      // If no keys left, remove the entire entry
      localStorage.removeItem(key);
    } else {
      // Update the entry with the filtered keys
      localStorage.setItem(key, JSON.stringify(data));
    }

    console.log(`🔥 Daily usage reset for ${feature}:${recordId}`);
  } catch (error) {
    console.error('🔥 Error resetting daily usage for record:', error);
  }
};

/**
 * Get total daily usage count across all records for a specific feature
 */
export const getTotalDailyUsageCount = (feature: string): number => {
  if (typeof window === 'undefined') return 0;

  try {
    const key = getStorageKey(feature);
    const stored = localStorage.getItem(key);

    if (!stored) return 0;

    const data: DailyUsageData = JSON.parse(stored);
    const today = getTodayString();

    // If the stored date is not today, return 0
    if (data.date !== today) {
      return 0;
    }

    return data.keys.length;
  } catch (error) {
    console.error('🔥 Error getting total daily usage count:', error);
    return 0;
  }
};

/**
 * Get daily usage breakdown by record ID for a specific feature
 */
export const getDailyUsageBreakdown = (feature: string): Array<{recordId: string, count: number}> => {
  if (typeof window === 'undefined') return [];

  try {
    const key = getStorageKey(feature);
    const stored = localStorage.getItem(key);

    if (!stored) return [];

    const data: DailyUsageData = JSON.parse(stored);
    const today = getTodayString();

    // If the stored date is not today, return empty array
    if (data.date !== today) {
      return [];
    }

    // Convert keys array to breakdown format (each key has count of 1)
    return data.keys.map(recordId => ({ recordId, count: 1 }));
  } catch (error) {
    console.error('🔥 Error getting daily usage breakdown:', error);
    return [];
  }
};

// Feature constants
export const DAILY_USAGE_FEATURES = {
  PRICE_CHANGE_HISTORY: 'price_change_history',
  RENT_SEARCH: 'rent_search'
} as const;
