const LOG_LEVEL = process.env.LOG_LEVEL || "info"; // Default level is "info"

const levels = ["debug", "info", "warn", "error"];

function log(level: "debug" | "info" | "warn" | "error", ...args: any[]) {
  if (levels.indexOf(level) >= levels.indexOf(LOG_LEVEL)) {
    console[level](...args);
  }
}

export const logger = {
  debug: (...args: any[]) => log("debug", "[DEBUG]", ...args),
  info: (...args: any[]) => log("info", "[INFO]", ...args),
  warn: (...args: any[]) => log("warn", "[WARN❗]", ...args),
  error: (...args: any[]) => log("error", "[ERROR❌]", ...args),
};