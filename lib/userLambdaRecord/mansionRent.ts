import { mean, min } from "lodash-es";
import { prisma } from "../prisma";

export async function getAverageRentPriceForBuildingId(buildingId: string) {
  // Find all rentRecords that match
  let matchedRentRecords = await prisma.proMansionRent.findMany({
    where: {
      buildingId,
    },
  });

  // Find rent price / m2
  let averageRentPrices = [] as number[];
  matchedRentRecords.forEach((r: any) => {
    averageRentPrices.push(
      (r.feeRent * 10000 + r.feeManagement + r.feeUtility) / r.unitSize
    );
  });

  let averageRentPrice = mean(averageRentPrices);

  return {
    buildingId,
    averageRentPrices,
    averageRentPrice,
    matchedRentRecords,
  };
}

export async function getRoiAndRentAndSalesRecord(recordId: string) {
  let matchedRecord = await prisma.tllUserLambdaRecord.findUnique({
    where: {
      id: recordId,
    },
  }) as any;

  if (matchedRecord === null || matchedRecord.buildingId === null) {
    return null;
  }

  let res = await getAverageRentPriceForBuildingId(
    matchedRecord.buildingId,
  );

  if (res === null) {
    return {
      error: "Cannot find building",
    };
  }

  const {
    buildingId,
    averageRentPrice,
    averageRentPrices,
    matchedRentRecords,
  } = res;

  let roi = isNaN(averageRentPrice)
    ? null
    : ((averageRentPrice * matchedRecord.recordValues?.unitArea * 12) /
      10000 /
      matchedRecord.price) *
    100;

  // Getting sales data too
  let matchedSalesRecords = await prisma.tllUserLambdaRecord.findMany({
    where: {
      buildingId,
    },
    omit: {
      analysisSimulationConfig: true,
      analysisSimulationResults: true,
    },
  });

  let averageSalePrices = [] as number[];
  matchedSalesRecords.forEach((r: any) => {
    averageSalePrices.push(r.price / r.recordValues?.unitArea);
  });

  let averageSalePrice = mean(averageSalePrices);
  let lowGfaUnitPrice = min(averageSalePrices);
  let averageSalePriceDiff =
    matchedRecord.price /
    matchedRecord.recordValues?.unitArea /
    averageSalePrice -
    1;

  // Append ROI for each reciord if averageRentPrice is not null
  if (averageRentPrice !== null) {
    matchedSalesRecords.forEach((r: any) => {
      r.nearbyResultsAndCapsroi =
        ((averageRentPrice * r.nearbyResultsAndCapsrecordValues.unitArea * 12) /
          r.nearbyResultsAndCapsprice /
          10000) *
        100;
    });
  }

  return {
    averageRentPrices,
    averageRentPrice: parseFloat(averageRentPrice.toFixed(0)),
    averageSalePrices,
    avgGfaPriceSameBuilding: (averageSalePrice * matchedRecord.recordValues?.unitArea).toFixed(0),
    lowGfaUnitPrice,
    lowGfaSameBuilding: lowGfaUnitPrice ? (lowGfaUnitPrice * matchedRecord.recordValues?.unitArea).toFixed(0) : null,
    averageSalePrice: parseFloat(averageSalePrice.toFixed(2)),
    averageSalePriceDiff: parseFloat((averageSalePriceDiff * 100).toFixed(2)),
    roi: roi ? parseFloat(roi.toFixed(2)) : null,
    matchedRentRecords,
    matchedRecord,
    matchedSalesRecords: matchedSalesRecords.sort((a: any, b: any) => {
      if (
        a.nearbyResultsAndCapsyearlyIncome !== null &&
        b.nearbyResultsAndCapsyearlyIncome !== null
      )
        return a.nearbyResultsAndCapsroi < b.nearbyResultsAndCapsroi ? 1 : -1;

      return a.nearbyResultsAndCapsprice / a.nearbyResultsAndCapsrecordValues?.unitArea <
        b.nearbyResultsAndCapsprice / b.nearbyResultsAndCapsrecordValues?.unitArea
        ? -1
        : 1;
    }) as any,
  } as any;
}

export async function getRoiAndRentAndSalesRecordForBuildingId(buildingId: string) {
  let res = await getAverageRentPriceForBuildingId(
    buildingId,
  );

  const { averageRentPrice, averageRentPrices, matchedRentRecords } = res;

  // Getting sales data too
  let matchedSalesRecords = await prisma.tllUserLambdaRecord.findMany({
    where: {
      buildingId,
    },
    omit: {
      analysisSimulationConfig: true,
      analysisSimulationResults: true,
    }
  });

  let averageSalePrices = [] as number[];
  matchedSalesRecords.forEach((r: any) => {
    averageSalePrices.push(r.price / r.recordValues?.unitArea);
  });

  let averageSalePrice = mean(averageSalePrices);
  let lowGfaUnitPrice = min(averageSalePrices);

  // Append ROI for each reciord if averageRentPrice is not null
  if (averageRentPrice !== null) {
    matchedSalesRecords.forEach((r: any) => {
      r.roi =
        ((averageRentPrice * r.recordValues?.unitArea * 12) /
          r.price /
          10000) *
        100;
    });
  }

  return {
    averageRentPrices,
    averageRentPrice: parseFloat(averageRentPrice.toFixed(0)),
    averageSalePrices,
    lowGfaUnitPrice,
    averageSalePrice: parseFloat(averageSalePrice.toFixed(2)),
    matchedRentRecords,
    matchedSalesRecords: matchedSalesRecords.sort((a: any, b: any) => {
      if (
        a.yearlyIncome !== null &&
        b.yearlyIncome !== null
      )
        return a.nearbyResultsAndCapsroi < b.nearbyResultsAndCapsroi ? 1 : -1;

      return a.nearbyResultsAndCapsprice / a.nearbyResultsAndCapsrecordValues?.unitArea <
        b.nearbyResultsAndCapsprice / b.nearbyResultsAndCapsrecordValues?.unitArea
        ? -1
        : 1;
    }),
  };
}