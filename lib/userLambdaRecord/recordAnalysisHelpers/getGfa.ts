import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { removeOutlier } from "@/lib/helper/stats";
import { mean } from "lodash-es";
import { coef } from "@/app/api/cron/constants";
import { GfaValuationProps } from "@/lib/definitions/propertyAnalysisResults";

export async function getGfa(topResultsPadded: UserLambdaRecordProps[], record: UserLambdaRecordProps): Promise<GfaValuationProps> {
  let gfaUnitPriceArray = [] as number[];

  topResultsPadded.forEach((r) => {
    let buildingArea =
      r["recordValues"] !== undefined && r["buildingSize"] !== undefined
        ? r["buildingSize"]
        : r["recordValues"]["unitArea"];

    gfaUnitPriceArray.push(parseInt((r?.price / buildingArea).toFixed(0)));
  });

  let filteredGfaUnitPriceArray = removeOutlier(
    gfaUnitPriceArray,
    "gfa"
  );

  let avgGfaUnitPrice = mean(filteredGfaUnitPriceArray);
  let avgGfaPrice = (avgGfaUnitPrice *
    (record["buildingSize"] || record["recordValues"]["unitArea"]) *
    coef(record)
  ).toFixed(0);

  let lowGfaPrice = (
    filteredGfaUnitPriceArray[0] *
    (record["buildingSize"] || record["recordValues"]["unitArea"]) *
    coef(record)
  ).toFixed(0);

  return {
    avgPrice: Number(avgGfaPrice),
    lowPrice: Number(lowGfaPrice),
    rawDataUnitPriceArray: filteredGfaUnitPriceArray.sort((a, b) => a - b),
    rawDataUnitPriceArrayAverage: avgGfaUnitPrice,
  };
};