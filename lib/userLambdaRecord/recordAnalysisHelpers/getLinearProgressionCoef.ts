import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { logger } from "@/lib/logger";
import { linearRegression } from "simple-statistics";
import { sum } from "simple-statistics";

export function getLinearProgressionCoef(topResults: { data: UserLambdaRecordProps, distance: number }[]): {
  linearRegressionA: number;
  linearRegressionB: number;
} {

  // percentaga * sekisan % + y = roi (cap rate)

  let arr = [] as [number, number][]; // Sekisan, Caprate
  topResults.forEach((r: { data: UserLambdaRecordProps, distance: number }) => {
    // These are old data so assume the sekisan value exists
    let sekisanPercentage = r.data.propertyAnalysisResult?.sekisanValuation?.sekisanTotalPercentage;
    // console.log("r.data.yearlyIncome | sekisanPercentage | id ", r.data.propertyAnalysisResult?.analysisDate, sekisanPercentage, r.data.id);

    if (
      r.data.yearlyIncome !== undefined &&
      r.data.yearlyIncome !== null &&
      r.data.yearlyIncome > 0 && sekisanPercentage !== null
    ) {
      arr.push([
        sekisanPercentage || 0,
        (r.data.yearlyIncome * 100) / r.data.price,
      ]);
    }
  });

  // logger.info("origin arr for linear regression", arr);

  // Remove outlier for those that sekisan% is too high
  let total = sum(arr.map((a) => a[0]));
  let average = total / arr.length;
  let filteredArr = arr.filter(
    (a) => a[0] <= average * 0.8 || a[0] >= average * 1.3
  );

  // logger.info("origin arr for linear regression", arr.length);

  logger.info("filteredArr for linear regression", filteredArr.length);
  let a = linearRegression(filteredArr);
  logger.info("linear regression a", a);

  return {
    linearRegressionA: a["m"],
    linearRegressionB: a["b"],
  };

}