import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { RentValuationProps } from "@/lib/definitions/propertyAnalysisResults";
import { removeOutlier } from "@/lib/helper/stats";
import { mean } from "lodash-es";
import { coef } from "@/app/api/cron/constants";
import { getM2Rent } from "../m2Metrics";

export async function getUpsidePrice(topXNearbyResults: UserLambdaRecordProps[], record: UserLambdaRecordProps): Promise<RentValuationProps> {
  let rentAverageArray = [] as number[];

  topXNearbyResults.forEach((r: any) => {
    if (
      r.yearlyIncome !== undefined &&
      r.yearlyIncome !== null &&
      r.yearlyIncome > 0
    ) {
      let buildingArea: number =
        r["recordValues"] !== undefined && r["buildingSize"] !== undefined
          ? r["buildingSize"]
          : r["recordValues"]["unitArea"];

      let rent = (((r?.yearlyIncome / 12) * 10000) / buildingArea).toFixed(0);
      if (buildingArea > 1) {
        rentAverageArray.push(parseInt(rent));
      }
    }
  });

  let filteredRent = removeOutlier(
    rentAverageArray,
    "rent"
  );

  let avgRentMonthly = mean(filteredRent);

  // dataValues needed as it is padded value
  let m2RentMonthly = getM2Rent(record);
  let diff: number | null = null;
  let priceAfterUpside: number | null = null;

  if (m2RentMonthly !== null) {
    diff =
      m2RentMonthly !== null
        ? -(m2RentMonthly / avgRentMonthly - 1) * 100
        : null;

    // let buildingArea =
    //   record.buildingSize !== undefined
    //     ? record.buildingSize
    //     : record.recordValues?.unitArea;

    priceAfterUpside = (record.price / m2RentMonthly) * avgRentMonthly * coef(record);
  }

  return {
    priceAfterUpside: priceAfterUpside || undefined,
    rawDataRentArray: rentAverageArray,
    rawDataRentArrayAverage: avgRentMonthly,
  };
};