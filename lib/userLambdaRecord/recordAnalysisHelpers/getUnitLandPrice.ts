import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { UnitLandPriceValuationProps } from "@/lib/definitions/propertyAnalysisResults";
import { removeOutlier } from "@/lib/helper/stats";
import { mean } from "lodash-es";
import { coef } from "@/app/api/cron/constants";

export async function getUnitLandPrice(topResultsPadded: UserLambdaRecordProps[], record: UserLambdaRecordProps): Promise<UnitLandPriceValuationProps> {
  if (!record.landSize) {
    return {};
  }

  let landFloorAreaRatio = record.landFloorAreaRatio || 100; // Default 容積率

  let isshuUnitPriceArray = [] as number[];
  topResultsPadded.forEach((r: any) => {
    if (r["landFloorAreaRatio"] > 0) {
      let d =
        r?.price /
        r["landSize"] /
        (parseInt(r["landFloorAreaRatio"] || "100") / 100) // default to 100%

      if (!isNaN(d)) isshuUnitPriceArray.push(d);
    }
  });

  let filteredIsshuUnitPriceArray =
    removeOutlier(isshuUnitPriceArray, "issu");

  let avgIsshuUnitPrice = mean(filteredIsshuUnitPriceArray);

  let avgIsshuPrice =
    parseInt(avgIsshuUnitPrice.toFixed(0)) *
    record["landSize"] * (landFloorAreaRatio / 100) * coef(record);

  let lowIsshuPrice =
    parseInt(filteredIsshuUnitPriceArray[0]?.toFixed(0) || "0") *
    record["landSize"] *
    (landFloorAreaRatio / 100) * coef(record);

  return {
    avgPrice: avgIsshuPrice,
    lowPrice: lowIsshuPrice,
    rawDataIssueUnitPriceArray: filteredIsshuUnitPriceArray.sort((a, b) => a - b),
    rawDataIssueUnitPriceArrayAverage: avgIsshuUnitPrice,
  };;
};