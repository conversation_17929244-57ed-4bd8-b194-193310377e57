import { getProBuildingHouseRent } from "@/actions/proBuildingHouseRent";
import { ProBuildingHouseRentProps } from "@/lib/definitions/proBuildingHouseRent";
import { removeOutlier } from "@/lib/helper/stats";
import { mean } from "lodash-es";
import { ProMansionRentProps } from "@/lib/definitions/proMansionRent";

export function getMansionHouseAverageRent({
  records,
}: {
  records: any[];
}) {

  let unitRentArray = [] as number[];

  records.forEach((record) => {
    let builingOrUnitSize = record.buildingSize || record.unitSize;

    if (record.feeRent && builingOrUnitSize && builingOrUnitSize > 1) {
      unitRentArray.push((record.feeRent + (record.feeManagement || 0) / 10000 + (record.feeUtility || 0) / 10000) / builingOrUnitSize * 10000);
    }
  });

  let filteredUnitRentArray = removeOutlier(unitRentArray);

  let avgUnitRent = mean(filteredUnitRentArray);

  return parseInt((avgUnitRent * 3.3).toFixed(0));
}