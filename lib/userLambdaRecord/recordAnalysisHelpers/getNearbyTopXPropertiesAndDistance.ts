import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { getPrefectureCodeFromAddress } from "@/lib/helper/geo";
import { logger } from "@/lib/logger";
import { prisma } from "@/lib/prisma";
import { getAddressFromCoordinates } from "@/lib/thirdParty/google";
import { calcCoordinateDistance } from "@/lib/helper/geoDistance";
import { TllUserLambdaRecordRecordType } from "@prisma/client";
import dayjs from "dayjs";
import { VALUATION_MAX_YEAR_RETRIEVAL } from "@/app/api/cron/constants";

export async function getNearbyTopXPropertiesAndDistance({ record }: { record: any }): Promise<{ distance: number; data: UserLambdaRecordProps }[]> {
  let whereFilters = {
    longitude: {
      not: null,
    },
    latitude: {
      not: null,
    },
    recordType: record["recordType"],
    landRight: "所有権", // only use shoyuken for evaluatiin, but use coef to downgrade the price
    updatedAt: {
      gte: dayjs().subtract(VALUATION_MAX_YEAR_RETRIEVAL, "year").toDate(), // only look for records updated in the past 3 years, else data will be screwed
    }
  } as any;


  // Firs try with station group id and postal code
  if ((record["nearestStationGroupId"] !== undefined && record["nearestStationGroupId"] !== null) || (record["areaCode"] !== undefined && record["areaCode"] !== null) || (record["postalCode"] !== undefined && record["postalCode"] !== null)) {
    whereFilters["OR"] = [
      {
        nearestStationGroupId: record["nearestStationGroupId"],
      }
    ];

    // Use area code first, else postal code
    if (record["areaCode"] !== undefined && record["areaCode"] !== null) {
      whereFilters["OR"].push({
        areaCode: record["areaCode"],
      });
    }

    if (record["postalCode"] !== undefined && record["postalCode"] !== null) {
      whereFilters["OR"].push({
        postalCode: parseInt(record["postalCode"]),
      });
    }
  } else {
    return [];
  }

  // logger.debug("whereFilters for finding nearby stuff", whereFilters);
  let records = await prisma.tllUserLambdaRecord.findMany({
    where: whereFilters,
    include: {
      propertyAnalysisResult: true,
    }
  }) as UserLambdaRecordProps[];

  // if (records.length <= 100) {
  //   logger.info(` nearby not enough for ${record["id"]} only ${records.length}... use pref code`);

  //   if (isVercelJob) {
  //     logger.info("skipping because of vercel")
  //     return [];
  //   }

  //   if (
  //     record !== null &&
  //     record.prefectureCode !== undefined &&
  //     record.prefectureCode !== null
  //   ) {
  //     whereFilters["prefectureCode"] = record.prefectureCode;
  //   } else {
  //     let parsedAddress = await getAddressFromCoordinates(record.latitude, record.longitude);

  //     if (parsedAddress !== null) {
  //       let prefectureCode = await getPrefectureCodeFromAddress(parsedAddress);

  //       if (prefectureCode !== null) {
  //         whereFilters["prefectureCode"] = prefectureCode;
  //       }
  //     }
  //   }
  //   console.log("whereFilters11111, id is", record["id"], whereFilters);
  //   delete whereFilters["OR"];
  //   records = await prisma.tllUserLambdaRecord.findMany({
  //     where: whereFilters,
  //     include: {
  //       propertyAnalysisResult: true,
  //     }
  //   }) as UserLambdaRecordProps[];
  //   // logger.debug("whereFilters for finding nearby stuff with prefecture is", whereFilters);
  // } else {
  logger.info(` Found ${records.length} records for ${record["id"]} 🔥`);
  // }

  // Use Haversine Formulea to calculate distance
  let nearestRecords = await nearbyPadWithDistance({
    records,
    lat: record["latitude"],
    lng: record["longitude"],
    recordType: record["recordType"]
  }) as { distance: number; data: UserLambdaRecordProps }[];

  let RESULT_COUNT = 100;
  let RESULT_RADIUS = 3000;
  let resultsWithinRadius = nearestRecords.filter((a) => a.distance <= RESULT_RADIUS);

  let topResults =
    resultsWithinRadius.length > RESULT_COUNT
      ? resultsWithinRadius // FIXME: i think at some point you cannot use all data after all
      : nearestRecords.slice(0, RESULT_COUNT) as { distance: number; data: UserLambdaRecordProps }[];

  // logger.debug(`🔥${record?.id}, total ${topResults.length}/${nearestRecords.length} in radius🔥`);
  // logger.debug("topResults", Object.fromEntries(topResults.map(i => [i.data.id, i.distance])));

  return topResults;
}

export async function nearbyPadWithDistance({ records, lat, lng, recordType }: { records: UserLambdaRecordProps[], lat: number, lng: number, recordType: TllUserLambdaRecordRecordType }): Promise<{ distance: number; data: UserLambdaRecordProps }[]> {
  let nearestRecords: { distance: number; data: UserLambdaRecordProps }[] = [];

  records
    .filter((a: any) => {
      if (recordType !== undefined) {
        return a.recordType === recordType;
      }

      return a.recordSubType !== undefined && a.recordSubType !== "";
    })
    .forEach((record: any) => {
      let currentRecordLat = record.latitude;
      let currentRecordLng = record.longitude;

      if (record.building && record.building.latitude && record.building.longitude) {
        currentRecordLat = record.building.latitude;
        currentRecordLng = record.building.longitude;
      }

      let dis = calcCoordinateDistance(currentRecordLat, currentRecordLng, lat, lng);
      // Do not include onself
      if (dis !== 0) {
        nearestRecords.push({
          distance: dis * 1000, // convert into m
          data: record,
        });
      }
    });

  nearestRecords.sort((a, b) => (a.distance > b.distance ? 1 : -1));

  return nearestRecords;
}