import { getSekisanAndPercentage } from "@/lib/helper/sekisan";
import { removeOutlier } from "@/lib/helper/stats";
import { logger } from "@/lib/logger";
import { mean } from "lodash-es";
import { coef } from "@/app/api/cron/constants";
import { getBuildingPrice } from "@/lib/helper/sekisan";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { SekisanValuationProps } from "@/lib/definitions/propertyAnalysisResults";

export async function getSekisanPrice(topResultsPadded: UserLambdaRecordProps[], record: UserLambdaRecordProps): Promise<SekisanValuationProps> {
  let sekisanAndPercentage = getSekisanAndPercentage(record) as {
    rebuildPrice: number;
    sekisan: number;
    sekisanLand: number;
  };
  let sekisanPercArray = [] as number[];

  topResultsPadded.forEach((r: any) => {
    let d = r.propertyAnalysisResult?.sekisanValuation
      ?.sekisanTotalPercentage;
    if (d !== null) {
      sekisanPercArray.push(d);
    }
  });

  // logger.debug("sekisanPercArray", sekisanPercArray);
  let sekisanFiltered = removeOutlier(
    sekisanPercArray,
    "sekisanPerc"
  );
  // logger.debug("sekisanFiltered", sekisanFiltered);
  let avgSekisan = (sekisanAndPercentage?.rebuildPrice / mean(sekisanFiltered)) * 100;
  // logger.debug("avgSekisan", avgSekisan);

  return {
    avgPrice: Number((avgSekisan * coef(record)).toFixed(0)),
    lowPrice: Number((avgSekisan * 0.9 * coef(record)).toFixed(0)),
    rawDataSekisanPercentArray: sekisanFiltered.sort((a, b) => a - b),
    sekisanPrice: sekisanAndPercentage.rebuildPrice,
    sekisanTotalPercentage: sekisanAndPercentage.sekisan,
    sekisanLandPercentage: sekisanAndPercentage.sekisanLand,
  } as SekisanValuationProps;
};