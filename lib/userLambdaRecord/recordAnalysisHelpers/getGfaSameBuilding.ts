
import { mean } from "lodash-es";
import { coef } from "@/app/api/cron/constants";
import { getRoiAndRentAndSalesRecordForBuildingId, getRoiAndRentAndSalesRecord } from "../mansionRent";
import { getM2Rent } from "../m2Metrics";
import { logger } from "@/lib/logger";
import { GfaValuationProps } from "@/lib/definitions/propertyAnalysisResults";

export async function getGfaSameBuilding(recordId: string, buildingId: string) {
  // RecordId: for showing nearby proeprties data in the DP
  // buildingID: for building DP page display
  if (recordId === undefined && buildingId === undefined) {
    return {
      error: "Please enter recordId or buildingId",
    };
  }

  let res;
  if (recordId !== undefined) {
    res = await getRoiAndRentAndSalesRecord(
      recordId
    );
  } else {
    res =
      await getRoiAndRentAndSalesRecordForBuildingId(
        buildingId,
      );
  }

  return {
    avgGfaPriceSameBuilding: res?.avgGfaPriceSameBuilding,
    lowGfaSameBuilding: res?.lowGfaSameBuilding,
  };
};

