import { VALUATION_MAX_YEAR_RETRIEVAL } from "@/app/api/cron/constants";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import dayjs from "dayjs";
import { getNearbyMaxMin80Percentile } from "./getNearbyMaxMin80Percentile";
import { logger } from "../logger";
import { GfaValuationProps, PropertyAnalysisResultsProps } from "../definitions/propertyAnalysisResults";
import { nearbyPadWithDistance } from "./recordAnalysisHelpers/getNearbyTopXPropertiesAndDistance";
import { getGfa } from "./recordAnalysisHelpers/getGfa";
import { recordGetStarRank } from "./valueRanking";
import { prisma } from "../prisma";

export async function recordAnalysisMansion({
  record,
}: {
  record: UserLambdaRecordProps,
}): Promise<PropertyAnalysisResultsProps | null> {
  if (
    !record.latitude || !record.longitude
  ) {
    return null;
  }

  let otherRecordsInBuilding = [] as any[];
  let nearbyRecords = [] as any[];


  if (record.buildingId) {
    otherRecordsInBuilding = await prisma.tllUserLambdaRecord.findMany({
      where: {
        buildingId: record.buildingId,
        recordType: "MANSION",
        id: {
          not: record.id
        },
        updatedAt: {
          gte: dayjs().subtract(VALUATION_MAX_YEAR_RETRIEVAL, "year").toDate(), // only look for records updated in the past 3 years, else data will be screwed
        }
      },
    });

    logger.debug("otherRecordsInBuilding found, total ", otherRecordsInBuilding.length + " records");

    if (otherRecordsInBuilding.length < 10 && (record.postalCode || record.nearestStationGroupId)) {
      let buildingBuiltYear = record.buildingBuiltYear;

      let whereParams = {
        recordType: "MANSION",
        updatedAt: {
          gte: dayjs().subtract(VALUATION_MAX_YEAR_RETRIEVAL, "year").toDate(), // only look for records updated in the past 3 years, else data will be screwed
        },
        ...(buildingBuiltYear ? {
          buildingBuiltYear: {
            gte: buildingBuiltYear - 10,
            lte: buildingBuiltYear + 10
          }
        } : {}),
      } as any;

      if (record.postalCode && record.nearestStationGroupId) {
        whereParams.OR = [
          { postalCode: record.postalCode },
          { nearestStationGroupId: record.nearestStationGroupId }
        ];
      } else {
        if (record.postalCode) {
          whereParams.postalCode = record.postalCode;
        }
        if (record.nearestStationGroupId) {
          whereParams.nearestStationGroupId = record.nearestStationGroupId;
        }
      }

      nearbyRecords = await prisma.tllUserLambdaRecord.findMany({
        where: whereParams,
        include: {
          building: true,
        }
      });

      logger.debug("not enough nearby results, adding more records in the postal code... found", nearbyRecords.length + " records nearby");
    }
  }

  let topXNearbyResults = [
    ...otherRecordsInBuilding.map(i => ({ data: i, distance: -1 })), // use -1 for current same building
    ...(await nearbyPadWithDistance({
      records: nearbyRecords,
      lat: record.building?.latitude || record.latitude, // Building is more precise
      lng: record.building?.longitude || record.longitude, // building is more precise
      recordType: "MANSION"
    }))
  ]

  let nearbyRecordIdAndDistance = Object.fromEntries(topXNearbyResults.map(i => [i.data.id, i.distance]));

  if (!topXNearbyResults.length) return null; // For vercel case will drop

  let propertyAnalysisResult = {
    recordId: record.id,
    recordType: record.recordType,
    nearbyRecordIdAndDistance: nearbyRecordIdAndDistance, // { "abc123": 10, "xyz456": 20 }

    roiValuation: {},
    rentValuation: {},
    sekisanValuation: {},
    gfaValuation: {},
    unitLandPriceValuation: {},

    overallStarLevel: 0, // default to 0 

    analysisDate: dayjs().toDate(),

  } as PropertyAnalysisResultsProps;


  let { max, min, avg, eightyPercentile } = getNearbyMaxMin80Percentile(topXNearbyResults.map(i => i.data), "MANSION");
  propertyAnalysisResult.nearbyMaxGfa = max;
  propertyAnalysisResult.nearbyMinGfa = min;
  propertyAnalysisResult.nearbyAvgGfa = avg;
  propertyAnalysisResult.nearby80PGfa = eightyPercentile;

  propertyAnalysisResult.gfaValuation = await getGfa(
    topXNearbyResults.map(i => i.data),
    record
  ) as GfaValuationProps;


  // else {


  //   // FIXME:: using same building instead
  //   rentValuationInfo =
  //     await ctx.service.userLambdaRecordValuations.getUpsidePrice(
  //       topResultsPadded,
  //       record
  //     );
  //   sekisanValuation = {}; // not needed

  //   // FIXME:: using same building instead
  //   gfaValuation = await ctx.service.userLambdaRecordValuations.getGfa(
  //     topResultsPadded,
  //     record
  //   );

  //   // Not needed
  //   unitLandPriceValuation = {};
  // }


  // logger.debug("optimalBiddingPriceCalulation", optimalBiddingPriceCalulation);
  // logger.debug("cfSimulationParams", cfSimulationParams);

  propertyAnalysisResult.overallStarLevel = await recordGetStarRank(
    record,
    propertyAnalysisResult as any
  );

  return propertyAnalysisResult;
}



