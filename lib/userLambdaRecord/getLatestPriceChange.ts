import { UserLambdaRecordPriceChangeProps } from "@/lib/definitions/userLambdaRecordPriceChange";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

export const getLatestPriceChange = (record: UserLambdaRecordProps) => {
  if (record && record.priceChanges && record.priceChanges.length > 0) {
    return record.priceChanges.sort((a: UserLambdaRecordPriceChangeProps, b: UserLambdaRecordPriceChangeProps) => {
      const dateA = new Date(a.recordDate);
      const dateB = new Date(b.recordDate);
      return dateB.getTime() - dateA.getTime(); // 降序排序
    })[0]; // 返回最新的价格变动记录
  }

  return null;
};
