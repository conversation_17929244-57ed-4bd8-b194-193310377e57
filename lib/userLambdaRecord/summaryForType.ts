import { UserLambdaRecordType } from "../definitions/userLambdaRecord";

import { UserLambdaRecordProps } from "../definitions/userLambdaRecord";

export const getAveargePriceForType = (data: UserLambdaRecordProps[], type: UserLambdaRecordType) => {
  return parseFloat((data.filter((record) => record.recordType === type).reduce((acc, curr) => acc + curr.price, 0) / data.filter((record) => record.recordType === type).length).toFixed(0));
}

export const getAverageRoiForType = (data: UserLambdaRecordProps[], type: UserLambdaRecordType) => {
  let arrayOfRois = [] as any;

  data.filter((record) => record.recordType === type && record.yearlyIncome).forEach((record) => {
    arrayOfRois.push(record.yearlyIncome ? record.yearlyIncome / record.price * 100 : 0);
  });

  return parseFloat((arrayOfRois.filter((roi: number) => roi !== 0).reduce((acc: number, curr: number) => acc + curr, 0) / arrayOfRois.filter((roi: number) => roi !== 0).length).toFixed(2));
}

export const getAverageAreaForType = (data: UserLambdaRecordProps[], type: UserLambdaRecordType) => {
  let arrayOfAreas = [] as any;

  console.log("data", data)

  data.filter((record) => record.recordType === type && (record.buildingSize || record.recordValues?.unitArea)).forEach((record) => {
    arrayOfAreas.push(record.buildingSize || record.recordValues?.unitArea);
  });

  return parseFloat((arrayOfAreas.filter((area: number) => area !== 0).reduce((acc: number, curr: number) => acc + curr, 0) / arrayOfAreas.filter((area: number) => area !== 0).length).toFixed(0));
}

export const getAverageLandAreaForType = (data: UserLambdaRecordProps[], type: UserLambdaRecordType) => {
  let arrayOfLandAreas = [] as any;

  data.filter((record) => record.recordType === type && record.landSize).forEach((record) => {
    arrayOfLandAreas.push(record.landSize);
  });

  return parseFloat((arrayOfLandAreas.filter((area: number) => area !== 0).reduce((acc: number, curr: number) => acc + curr, 0) / arrayOfLandAreas.filter((area: number) => area !== 0).length).toFixed(0));
}