import { coef } from "@/app/api/cron/constants";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import { UserLambdaRecordType } from "@/lib/definitions/userLambdaRecord";
import { populationData } from "../constants/populationData";
import dayjs from "dayjs";
import { getSekisanAndPercentage } from "../helper/sekisan";
import { getUpsidePrice } from "./recordAnalysisHelpers/getUpsidePrice";
import { recordCfAnalysis } from "./recordCfAnalysis";
import { removeOutlier } from "../helper/stats";
import { mean } from "lodash-es";
import { getNearbyMaxMin80Percentile } from "./getNearbyMaxMin80Percentile";
import { GfaValuationProps, PropertyAnalysisResultsProps, RentValuationProps, RoiValuationProps, SekisanValuationProps, UnitLandPriceValuationProps } from "../definitions/propertyAnalysisResults";
import { getNearbyTopXPropertiesAndDistance } from "./recordAnalysisHelpers/getNearbyTopXPropertiesAndDistance";
import { getLinearProgressionCoef } from "./recordAnalysisHelpers/getLinearProgressionCoef";
import { getSekisanPrice } from "./recordAnalysisHelpers/getSekisanPrice";
import { getGfa } from "./recordAnalysisHelpers/getGfa";
import { getUnitLandPrice } from "./recordAnalysisHelpers/getUnitLandPrice";
import { recordGetStarRank } from "./valueRanking";

export async function recordAnalysis({
  record,
}: {
  record: UserLambdaRecordProps,
}): Promise<PropertyAnalysisResultsProps | null> {
  if (
    !record.latitude || !record.longitude
  ) {
    return null;
  }

  const topXNearbyResults = await getNearbyTopXPropertiesAndDistance({
    record: record,
  }) as { distance: number; data: UserLambdaRecordProps }[];
  if (!topXNearbyResults.length) return null; // For vercel case will drop

  let propertyAnalysisResult = {
    recordId: record.id,
    recordType: record.recordType,
    nearbyRecordIdAndDistance: Object.fromEntries(topXNearbyResults.map(i => [i.data.id, i.distance])), // { "abc123": 10, "xyz456": 20 }

    roiValuation: {},
    rentValuation: {},
    sekisanValuation: {},
    gfaValuation: {},
    unitLandPriceValuation: {},

    overallStarLevel: 0, // default to 0 

    analysisDate: dayjs().toDate(),
  } as PropertyAnalysisResultsProps;

  if (record.recordType === UserLambdaRecordType.BUILDING) {
    let { max, min, avg, eightyPercentile } = getNearbyMaxMin80Percentile(topXNearbyResults.map(i => i.data), "BUILDING");

    let sekisanAndPercentage = getSekisanAndPercentage(record);

    const { linearRegressionA, linearRegressionB } = getLinearProgressionCoef(topXNearbyResults);
    propertyAnalysisResult.nearbyMaxCap = max;
    propertyAnalysisResult.nearbyMinCap = min;
    propertyAnalysisResult.nearbyAvgCap = avg;
    propertyAnalysisResult.nearby80PCap = eightyPercentile;
    propertyAnalysisResult.nearbyLinearRegressionA = linearRegressionA;
    propertyAnalysisResult.nearbyLinearRegressionB = linearRegressionB;

    if (record.yearlyIncome !== undefined && record.yearlyIncome > 0) {
      propertyAnalysisResult.roiValuation = {
        lowPrice: (record.yearlyIncome / max) * 100 * coef(record),
        p80Price: (record.yearlyIncome / (eightyPercentile || 0)) * 100 * coef(record),
        avgPrice: (record.yearlyIncome / avg) * 100 * coef(record),
      };

      let roiByLinearRegression =
        linearRegressionA * sekisanAndPercentage.sekisan + linearRegressionB;
      propertyAnalysisResult.roiValuation.lrPrice =
        (record.yearlyIncome / roiByLinearRegression) * 100 * coef(record);
    }
  }

  let { max: nearbyMaxSekisanPercentage, min: nearbyMinSekisanPercentage, avg: nearbyAvgSekisanPercentage, eightyPercentile: nearby80PSekisanPercentage } = getNearbyMaxMin80Percentile(topXNearbyResults.map(i => i.data), "SEKISAN");
  propertyAnalysisResult.nearbyMaxSekisanPercentage = nearbyMaxSekisanPercentage;
  propertyAnalysisResult.nearbyMinSekisanPercentage = nearbyMinSekisanPercentage;
  propertyAnalysisResult.nearbyAvgSekisanPercentage = nearbyAvgSekisanPercentage;
  propertyAnalysisResult.nearby80PSekisanPercentage = nearby80PSekisanPercentage;
  propertyAnalysisResult.sekisanValuation =
    await getSekisanPrice(
      topXNearbyResults.map(i => i.data),
      record
    ) as SekisanValuationProps;

  let { max, min, avg, eightyPercentile } = getNearbyMaxMin80Percentile(topXNearbyResults.map(i => i.data), "LAND");
  propertyAnalysisResult.nearbyMaxIssuePrice = max;
  propertyAnalysisResult.nearbyMinIssuePrice = min;
  propertyAnalysisResult.nearbyAvgIssuePrice = avg;
  propertyAnalysisResult.nearby80PIssuePrice = eightyPercentile;

  propertyAnalysisResult.unitLandPriceValuation = await getUnitLandPrice(
    topXNearbyResults.map(i => i.data),
    record
  ) as UnitLandPriceValuationProps;

  if (record.recordType !== UserLambdaRecordType.LAND) {
    // 06-26 Moving it over 
    let { max: nearbyMaxRent, min: nearbyMinRent, avg: nearbyAvgRent, eightyPercentile: nearby80PRent } = getNearbyMaxMin80Percentile(topXNearbyResults.map(i => i.data), "RENT");
    propertyAnalysisResult.nearbyMaxRent = nearbyMaxRent;
    propertyAnalysisResult.nearbyMinRent = nearbyMinRent;
    propertyAnalysisResult.nearbyAvgRent = nearbyAvgRent;
    propertyAnalysisResult.nearby80PRent = nearby80PRent;

    propertyAnalysisResult.rentValuation = await getUpsidePrice(
      topXNearbyResults.map(i => i.data),
      record,
    ) as RentValuationProps;



    let { max, min, avg, eightyPercentile } = getNearbyMaxMin80Percentile(topXNearbyResults.map(i => i.data), "HOUSE");
    propertyAnalysisResult.nearbyMaxGfa = max;
    propertyAnalysisResult.nearbyMinGfa = min;
    propertyAnalysisResult.nearbyAvgGfa = avg;
    propertyAnalysisResult.nearby80PGfa = eightyPercentile;

    propertyAnalysisResult.gfaValuation = await getGfa(
      topXNearbyResults.map(i => i.data),
      record
    ) as GfaValuationProps;
  }

  propertyAnalysisResult.overallStarLevel = await recordGetStarRank(
    record,
    propertyAnalysisResult as any
  );

  // NOTE: all price evaluated used coeef
  // All the nearby average do not use cooef
  return propertyAnalysisResult;
}



