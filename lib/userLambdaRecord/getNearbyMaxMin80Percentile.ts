import { removeOutlier } from "../helper/stats";
import { UserLambdaRecordProps } from "../definitions/userLambdaRecord";
import { logger } from "../logger";

const calculateMetrics = (values: number[], recordType: string): {
  min: number;
  max: number;
  avg: number;
  eightyPercentile: number;
} => {
  // logger.debug("values", values);
  const cleanedValues = removeOutlier(values);
  // logger.debug("cleanedValues", cleanedValues);

  const min = Math.min(...cleanedValues).toFixed(2);
  const max = Math.max(...cleanedValues).toFixed(2);
  const avg = (cleanedValues.reduce((sum, value) => sum + value, 0) / cleanedValues.length).toFixed(2);

  const eightyPercentile = recordType === 'BUILDING' ? cleanedValues[Math.floor(cleanedValues.length * 0.8)]?.toFixed(2) : cleanedValues[Math.floor(cleanedValues.length * 0.2)]?.toFixed(2);
  return { min: parseFloat(min), max: parseFloat(max), avg: parseFloat(avg), eightyPercentile: parseFloat(eightyPercentile) };
};

export function getNearbyMaxMin80Percentile(records: UserLambdaRecordProps[], recordType: string): {
  max: number;
  min: number;
  avg: number;
  eightyPercentile: number;
  comparatorType: string;
} {
  if (!records || records.length === 0) {
    return { max: 0, min: 0, avg: 0, eightyPercentile: 0, comparatorType: '' };
  }

  console.log("🔥 recordType", recordType);

  const comparators = records.map((a) => {
    if (recordType === 'HOUSE' || recordType === 'MANSION') {
      return a.price / (a.buildingSize || a.recordValues?.unitArea);
    } else if (recordType === 'LAND') {
      return a.price / (a.landSize || 1) / (a.landFloorAreaRatio || 100) * 100;
    } else if (recordType === "RENT") {
      return (a.yearlyIncome || 0) / 12 * 10000 / (a.buildingSize || a.recordValues?.unitArea);
    } else if (recordType === "SEKISAN") {
      return a.propertyAnalysisResult?.sekisanValuation?.sekisanTotalPercentage || 0;
    } else {
      // Defalt ROI
      return (a.yearlyIncome || 0) / (a.price || 1) * 100;
    }
  }).filter((a) => a !== undefined && a !== null && a !== 0 && !isNaN(a));

  const { min, max, avg, eightyPercentile } = calculateMetrics(comparators.filter((a) => a !== undefined) as number[], recordType);

  const comparatorType = recordType === 'LAND' ? '土地単価/一種' : (recordType === 'HOUSE' || recordType === 'MANSION' ? '延床単価/平米' : 'ROI');

  // logger.debug("comparatorType", comparatorType);
  // logger.debug("eightyPercentile", eightyPercentile);
  // logger.debug("min", min);
  // logger.debug("max", max);
  // logger.debug("avg", avg);

  return { max, min, avg, eightyPercentile, comparatorType };
}
