import { PropertyAnalysisResultsProps } from "../definitions/propertyAnalysisResults";
import { UserLambdaRecordProps } from "../definitions/userLambdaRecord";
import { coef } from "@/app/api/cron/constants";

export const calculateMinPrice = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  let base = 0;
  switch (type) {
    case "BUILDING":
      base = (currentUserLambdaRecord?.yearlyIncome || 0) /
        (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyMaxCap || 1) * 100 || 0;
      break;
    case "HOUSE":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyMinGfa || 0) *
        (currentUserLambdaRecord?.buildingSize || 0);
      break;
    case "MANSION":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyMinGfa || 0) *
        (currentUserLambdaRecord?.buildingSize || 0);
      break;
    case "LAND":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyMinIssuePrice || 0) *
        (currentUserLambdaRecord?.landSize || 0) *
        (currentUserLambdaRecord?.landFloorAreaRatio || 100) / 100 || 0;
    default:
      return 0;
  }

  return base * coef(currentUserLambdaRecord);
};

export const calculateMaxPrice = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  let base = 0;
  switch (type) {
    case "BUILDING":
      base = (currentUserLambdaRecord?.yearlyIncome || 0) /
        (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyMinCap || 1) * 100 || 0;
      break;
    case "HOUSE":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyMaxGfa || 0) *
        (currentUserLambdaRecord?.buildingSize || 0);
      break;
    case "MANSION":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyMaxGfa || 0) *
        (currentUserLambdaRecord?.buildingSize || 0);
      break;
    case "LAND":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyMaxIssuePrice || 0) *
        (currentUserLambdaRecord?.landSize || 0) *
        (currentUserLambdaRecord?.landFloorAreaRatio || 100) / 100 || 0;
      break;
    default:
      return 0;
  }

  return base * coef(currentUserLambdaRecord);
};

export const calculateAvgPrice = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  let base = 0;

  switch (type) {
    case "MANSION":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgGfa || 0) *
        (currentUserLambdaRecord?.buildingSize || 0);
      break;
    case "BUILDING":
      base = (currentUserLambdaRecord?.yearlyIncome || 0) /
        (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgCap || 1) * 100 || 0;
      break;
    case "HOUSE":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgGfa || 0) *
        (currentUserLambdaRecord?.buildingSize || 0);
      break;
    case "LAND":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearbyAvgIssuePrice || 0) *
        (currentUserLambdaRecord?.landSize || 1) *
        (currentUserLambdaRecord?.landFloorAreaRatio || 100) / 100 || 0;
      break;
    default:
      return 0;
  }

  return base * coef(currentUserLambdaRecord);
};

export const calculateP80Price = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  let r = currentUserLambdaRecord?.propertyAnalysisResult;

  let base = 0;
  switch (type) {
    case "BUILDING":
      base = (currentUserLambdaRecord?.yearlyIncome || 0) /
        (currentUserLambdaRecord?.propertyAnalysisResult?.nearby80PCap || 1) * 100 || 0;
      break;
    case "HOUSE":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearby80PGfa || 0) *
        (currentUserLambdaRecord?.buildingSize || 0);
      break;
    case "MANSION":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearby80PGfa || 0) *
        (currentUserLambdaRecord?.buildingSize || 0);
      break;
    case "LAND":
      base = (currentUserLambdaRecord?.propertyAnalysisResult?.nearby80PIssuePrice || 0) *
        (currentUserLambdaRecord?.landSize || 1) *
        (currentUserLambdaRecord?.landFloorAreaRatio || 100) / 100 || 0;
      break;
    default:
      return 0;
  }

  return base * coef(currentUserLambdaRecord);
};

export const renderStarFromDiff = (mapper: any) => {
  let diff = (parseFloat(mapper.value) - parseFloat(mapper.comparator)) / parseFloat(mapper.comparator) * 100;

  if (!mapper.comparator || !mapper.value || parseFloat(mapper.comparator) === 0 || parseFloat(mapper.value) === 0 || !diff) return '';

  if (mapper.unit === "%") {
    return <span className="ml-1">
      {diff > 50 ? '⭐⭐⭐' : diff > 35 ? '⭐⭐' : diff > 15 ? '⭐' : ''}
    </span>
  } else {
    return <span className="ml-1">
      {diff < -50 ? '⭐⭐⭐' : diff < -35 ? '⭐⭐' : diff < -15 ? '⭐' : ''}
    </span>
  }
}

// FIXME: this is the calculated price from the unit value
// Thus everything is reversed
export const compareAndRenderStar = ({
  comparator,
  basePrice,
}: {
  comparator: number,
  basePrice: number,
}) => {
  if (comparator > 0) {
    let res = comparator;
    let diff = res / basePrice;

    // assuming 1/3 is realizeable
    let stars = `${diff > 1.5 ? '⭐' : ''}${diff > 1.35 ? '⭐' : ''}${diff > 1.15 ? '⭐' : ''
      }`;

    return (
      <span
        className={`${diff > 1 ? 'text-neutral-600' : 'text-neutral-300'} text-xs`}
        style={{
          textAlign: 'center',
          lineHeight: '1.3',
          // color: res > row.original.price ? 'green' : 'gray',
        }}
      >
        {Number(diff)?.toFixed(2) + "x" || '-'}
        <div style={{ fontSize: '8px' }}>{stars}</div>
      </span>
    );
  }
  return '-';
};

export const getDiff = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  const comparator = calculateAvgPrice(currentUserLambdaRecord, type);
  if (!comparator || comparator === 0) {
    return 0;
  }

  const diff = (currentUserLambdaRecord?.price - comparator) / comparator * 100;
  return diff;
}

export const getDiffAmount = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  const comparator = calculateAvgPrice(currentUserLambdaRecord, type);
  if (!comparator || comparator === 0) {
    return 0;
  }

  const diff = currentUserLambdaRecord?.price - comparator;
  return diff;
}

export const renderStarRatingClass = (diff: number) => {
  if (diff <= -35) {
    return "text-green-600 font-bold";
  } else if (diff < -15) {
    return "text-green-600";
  } else if (diff < 0) {
    return "text-green-400";
  } else if (diff >= 35) {
    return "text-red-600 font-bold";
  } else if (diff >= 15) {
    return "text-red-600";
  } else {
    return "text-red-400";
  }
}

export const renderStarRating = (currentUserLambdaRecord: UserLambdaRecordProps, type: string) => {
  const comparator = calculateAvgPrice(currentUserLambdaRecord, type);
  const diff = getDiff(currentUserLambdaRecord, type);

  if (!comparator) {
    return <div className="text-gray-200">
      データ取得中
    </div>
  }

  // const diffValue = (currentUserLambdaRecord?.price - comparator);

  const renderDiff = (diff: number) => <div className="flex flex-col items-center" >
    {diff > 0 && "▲"}
    {Math.abs(diff).toFixed(1)}%
    {/* <div className="text-xs">{diffValue?.toLocaleString()}</div> */}
  </div>

  return <span className={renderStarRatingClass(diff)}>{renderDiff(diff)}  </span>; // 割安
};

export async function recordGetStarRank(record: UserLambdaRecordProps, propertyAnalysisResult: PropertyAnalysisResultsProps) {
  let price = record.price;

  if (propertyAnalysisResult === null) return 0;

  let vCheck = (v: any) =>
    isNaN(v) || v === null || v === undefined || v <= price ? 0 : 1;

  let roiAverageLR = vCheck(
    propertyAnalysisResult.roiValuation?.lrPrice
  );
  let roiLow80 = vCheck(
    propertyAnalysisResult.roiValuation?.p80Price
  );
  let upside = vCheck(propertyAnalysisResult.rentValuation?.priceAfterUpside);
  let gfaAverage = vCheck(propertyAnalysisResult.gfaValuation?.avgPrice);


  let sekisanAverage = vCheck(
    propertyAnalysisResult.sekisanValuation?.avgPrice
  );
  let isshuAvearge = vCheck(
    propertyAnalysisResult.unitLandPriceValuation?.avgPrice
  );

  if (record.recordType === "BUILDING") {
    // ROI平均価格 + ROI下限価格 + アップサイド価格 + GFA平均価格 + 積算下限価格
    return roiAverageLR + roiLow80 + upside + gfaAverage + sekisanAverage;
  }

  if (record.recordType === "HOUSE") {
    // GFA平均価格 + 一種平均価格 + 積算下限価格
    let total = gfaAverage + isshuAvearge + sekisanAverage;
    return total == 2 ? 3 : total == 3 ? 5 : total;
  }

  if (record.recordType === "LAND") {
    // FIXME: shall you use the sekisan as comparator instead ? 
    // Since many data are missing yoseki and coverage ratio
    const comparator = propertyAnalysisResult.unitLandPriceValuation?.avgPrice || 0;

    const diff = (record?.price - comparator) / comparator * 100;
    return diff < -35 ? 5 : diff < -15 ? 3 : diff < 0 ? 1 : 0;
  }

  if (record.recordType === "MANSION") {
    const comparator = propertyAnalysisResult.gfaValuation?.avgPrice || 0;
    const diff = (record?.price - comparator) / comparator * 100;
    return diff < -35 ? 5 : diff < -15 ? 3 : diff < 0 ? 1 : 0;
  }

  return 0;
}