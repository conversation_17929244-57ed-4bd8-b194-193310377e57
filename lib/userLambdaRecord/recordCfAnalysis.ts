import { getSimpleCFCalculation } from "../helper/sekisan";
import { getNearbyMaxMin80Percentile } from "./getNearbyMaxMin80Percentile";
import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";

export async function recordCfAnalysis({ propertyAnalysisResult, record, eightyPercentile, avg }: { propertyAnalysisResult: any, record: UserLambdaRecordProps, eightyPercentile: number, avg: number }): Promise<any> {
  // FIXME: when you seperate this out then should work // 
  let isUrinushi = false;
  if (record.priceChanges && record.priceChanges.length > 0) {
    let mostRecentPriceChange = record.priceChanges.sort((a, b) => a.recordDate.getTime() - b.recordDate.getTime())[record.priceChanges.length - 1];
    if (mostRecentPriceChange.brokerType && mostRecentPriceChange.brokerType.includes("売主")) {
      isUrinushi = true;
    }
  }

  let rentDiff = null;
  if (record.recordType === "BUILDING" && record.yearlyIncome && record.buildingSize) {
    let nearbyAvg = propertyAnalysisResult.nearbyAvgRent;
    console.log("nearbyAvg", nearbyAvg);
    let avgRent = record.yearlyIncome * 10000 / 12 / record.buildingSize;
    console.log("avgRent", avgRent);
    rentDiff = (nearbyAvg - avgRent) / avgRent;
  }

  console.log("rentDiff", rentDiff);

  // FIXME: assuming 1/2 rent can be realized
  let holdingEndRentIncome = record.analysisSimulationConfig?.holdingEndRentIncome || (record.yearlyIncome || 0) * (1 + (rentDiff || 0) / 2) || 0;

  const updatedSimulationParams = {
    investmentSellingEOYYear: record.analysisSimulationConfig?.investmentSellingEOYYear || 1,
    investmentMinimalROCPerc: record.analysisSimulationConfig?.investmentMinimalROCPerc || 30,
    investmentMinimalNetProfitPerc: record.analysisSimulationConfig?.investmentMinimalNetProfitPerc || 20,
    purchasePurchaseCostPerc: record.analysisSimulationConfig?.purchasePurchaseCostPerc || (isUrinushi ? 4 : 7),
    purchaseReformCost: record.analysisSimulationConfig?.purchaseReformCost || 0,
    loanAmount: record.analysisSimulationConfig?.loanAmount || record.price * 0.8,
    loanInterestPerc: record.analysisSimulationConfig?.loanInterestPerc || 2.8,
    loanYears: record.analysisSimulationConfig?.loanYears || 1,
    holdingInitialRentIncome: record.analysisSimulationConfig?.holdingInitialRentIncome || record.yearlyIncome || 0,
    holdingEndRentIncome: Number(holdingEndRentIncome || 0).toFixed(0), // Assusming 1/3 rent can be realized
    holdingRunningCostPerc: record.analysisSimulationConfig?.holdingRunningCostPerc || 20,
    sellSellingFeePerc: record.analysisSimulationConfig?.sellSellingFeePerc || 3,
    ...(record.recordType === 'BUILDING' && { sellSellingCapRatePerc: parseFloat(((eightyPercentile + avg) / 2).toFixed(1)) }),
    ...(record.recordType === 'LAND' && { sellSellingLandUnitPrice: parseFloat(((eightyPercentile + avg) / 2).toFixed(1)) }),
    ...(record.recordType === 'HOUSE' && { sellSellingGfaUnitPrice: parseFloat(((eightyPercentile + avg) / 2).toFixed(1)) }),
  };

  const cfSimulationParams = updatedSimulationParams;

  const optimalBiddingPriceCalulation = await getSimpleCFCalculation({ cfConfigData: cfSimulationParams, record });

  return { optimalBiddingPriceCalulation, cfSimulationParams };
}
