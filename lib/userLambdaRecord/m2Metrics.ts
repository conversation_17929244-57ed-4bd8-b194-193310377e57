import { UserLambdaRecordProps } from "../definitions/userLambdaRecord";

export function getM2Rent(record: UserLambdaRecordProps): number {
  if (!record) return 0;

  let m2Rent = 0;
  if (
    record.yearlyIncome !== undefined &&
    record.yearlyIncome !== null &&
    record.yearlyIncome > 0
  ) {
    let buildingArea =
      record.buildingSize !== undefined
        ? record.buildingSize
        : record.recordValues?.unitArea;

    m2Rent = ((record.yearlyIncome / 12) * 10000) / buildingArea || 0;
  }

  return m2Rent;
}

export function getM2GFA(record: UserLambdaRecordProps): number {
  if (!record) return 0;

  let m2GFA = 0;

  let unitArea = 0;

  if (record?.recordType === 'MANSION') {
    unitArea = record?.recordValues?.unitArea || 0;
  } else {
    unitArea = record?.buildingSize || 0;
  }

  if (record.price !== undefined && record.price !== null && record.price > 0) {
    m2GFA = record.price / unitArea;
  }
  return m2GFA;
}