// 正则表达式常量定义
const AD_PATTERNS = [
  /中?(手数料|手出有)[\d．．０-９\.．]*パー?－?(出し)?/,
  /手[\d．．０-９\.．]*パー?/,
  /（?手.*?有り?）?/,
  /（手相）/,
  /（手出）/,
  /手あり/,
  /手アリ/,
  /てあり/,
  /手あり[０-９０-９]+万?円?/,
  /手３．０/,
  /（?手[０-９０-９]+万?円?あ?り?）?/,
  /テアリ[\d．．０-９\.．]*/,
  /[ａＡ][ｄＤ][０-９０-９]+パ?/
];

const OTHER_PATTERNS = [
  /（定借）/,
  /商談解除/,
  /現地対応中/,
  /現地/,
  /再公開/,
  /Ｐ有/,
  /最高値/,
  /案内可能/,
  /再募/,
  /[０-９０-９]+／[０-９０-９]+以降/,
  /内見可/,
  /価変/,
  /車可/,
  /ＮＵＲＯ光導入/,
  /ＮＵＲＯ光/,
  /（全媒体掲載可）/,
  /[０-９]+階/
];

/**
 * 处理建筑物名称，移除广告和其他特殊标记
 * @param name 原始建筑物名称
 * @returns 处理后的建筑物名称
 */
export function processBuildingName(name: string): string {
  if (!name) return "";

  let res = name.trim();
  res = res.replaceAll("・", ""); // 移除中圆点，例如"（築31年）"中的情况

  // 合并所有正则表达式为一个
  const allPatterns = [...AD_PATTERNS, ...OTHER_PATTERNS];
  const combinedPattern = new RegExp(allPatterns.map(pattern => pattern.source).join("|"), "g");

  res = res.replace(combinedPattern, "");
  return res;
}

/**
 * 检查建筑物名称是否包含特殊标记
 * @param name 建筑物名称
 * @returns 如果包含特殊标记返回true，否则返回false
 */
export function matchBuildingNameIsWeird(name: string): boolean {
  if (!name) return false;

  const allPatterns = [...AD_PATTERNS, ...OTHER_PATTERNS];
  return allPatterns.some(pattern => pattern.test(name));
}