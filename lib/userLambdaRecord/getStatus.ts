import { UserLambdaRecordProps } from "@/lib/definitions/userLambdaRecord";
import dayjs from "dayjs";

export const getStatus = (record: UserLambdaRecordProps) => {
  let status = "公開中";

  let priceChangeSorted = record?.priceChanges?.sort((a, b) => dayjs(b.recordDate).diff(dayjs(a.recordDate), 'days')) || [];

  if (record && record.priceChanges && record.priceChanges.length > 0) {
    status = priceChangeSorted[0].status || "公開中";
  }

  // もうしもみあり の場合は公開中にする
  if (status === "申込あり") {
    return "公開中";
  }

  if (status !== "成約" && status !== "公開中") {
    return "公開中";
  }

  return status;
};

