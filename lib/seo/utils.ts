import type { Metadata } from "next";

interface SEOConfig {
  title: string;
  description: string;
  keywords?: string;
  url: string;
  image?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  locale?: string;
}

export function generateSEOMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords,
    url,
    image = 'https://www.urbalytics.jp/og-image.jpg',
    type = 'website',
    publishedTime,
    modifiedTime,
    author,
    locale = 'ja_JP'
  } = config;

  // Generate language alternates with URL parameters
  const baseUrl = url.split('?')[0]; // Remove existing query params
  const languageAlternates = {
    'ja-JP': baseUrl,
    'en-US': `${baseUrl}?lang=en`,
    'zh-CN': `${baseUrl}?lang=zh`,
  };

  const metadata: Metadata = {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url,
      siteName: 'Urbalytics',
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale,
      type,
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [image],
      creator: '@urbalytics',
    },
    alternates: {
      canonical: url,
      languages: languageAlternates,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };

  // 添加作者信息（如果是文章）
  if (type === 'article' && author) {
    metadata.authors = [{ name: author }];
  }

  return metadata;
}

// 生成结构化数据
export function generateArticleSchema(config: {
  title: string;
  description: string;
  url: string;
  image: string;
  publishedTime: string;
  modifiedTime?: string;
  author: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": config.title,
    "description": config.description,
    "image": config.image,
    "url": config.url,
    "datePublished": config.publishedTime,
    "dateModified": config.modifiedTime || config.publishedTime,
    "author": {
      "@type": "Person",
      "name": config.author,
    },
    "publisher": {
      "@type": "Organization",
      "name": "Urbalytics",
      "logo": {
        "@type": "ImageObject",
        "url": "https://www.urbalytics.jp/logo.png",
      },
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": config.url,
    },
  };
}

// 生成FAQ结构化数据
export function generateFAQSchema(faqs: Array<{ question: string; answer: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer,
      },
    })),
  };
}

// 生成产品结构化数据
export function generateProductSchema(config: {
  name: string;
  description: string;
  image: string;
  url: string;
  price?: string;
  currency?: string;
  availability?: string;
}) {
  const schema: any = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": config.name,
    "description": config.description,
    "image": config.image,
    "url": config.url,
    "brand": {
      "@type": "Brand",
      "name": "Urbalytics",
    },
  };

  if (config.price) {
    schema.offers = {
      "@type": "Offer",
      "price": config.price,
      "priceCurrency": config.currency || "JPY",
      "availability": config.availability || "https://schema.org/InStock",
    };
  }

  return schema;
}

// 清理和优化描述文本
export function optimizeDescription(text: string, maxLength: number = 160): string {
  if (text.length <= maxLength) return text;
  
  const truncated = text.substring(0, maxLength - 3);
  const lastSpace = truncated.lastIndexOf(' ');
  
  return lastSpace > 0 
    ? truncated.substring(0, lastSpace) + '...'
    : truncated + '...';
}

// 生成关键词字符串
export function generateKeywords(keywords: string[]): string {
  return keywords.join(', ');
}

// 生成多语言 URL（使用参数方式）
export function generateLanguageUrls(baseUrl: string): Record<string, string> {
  const cleanUrl = baseUrl.split('?')[0]; // Remove existing query params
  return {
    'ja-JP': cleanUrl,
    'en-US': `${cleanUrl}?lang=en`,
    'zh-CN': `${cleanUrl}?lang=zh`,
  };
}

// 为当前页面生成完整的多语言 metadata
export function generateMultiLanguageSEO(config: SEOConfig & {
  currentLang?: string;
  translations?: {
    en?: { title: string; description: string; keywords?: string };
    zh?: { title: string; description: string; keywords?: string };
    ja?: { title: string; description: string; keywords?: string };
  };
}): Metadata {
  const { currentLang = 'ja', translations, ...baseConfig } = config;

  // Use translation if available, otherwise use base config
  const currentTranslation = translations?.[currentLang as keyof typeof translations];
  const finalConfig = {
    ...baseConfig,
    title: currentTranslation?.title || baseConfig.title,
    description: currentTranslation?.description || baseConfig.description,
    keywords: currentTranslation?.keywords || baseConfig.keywords,
  };

  return generateSEOMetadata(finalConfig);
}

// 生成房地产物件结构化数据
export function generateRealEstateSchema(config: {
  name: string;
  description: string;
  address: string;
  price: string;
  currency?: string;
  propertyType: string;
  floorSize?: string;
  numberOfRooms?: string;
  yearBuilt?: string;
  image?: string;
  url: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "RealEstateListing",
    "name": config.name,
    "description": config.description,
    "url": config.url,
    ...(config.image && { "image": config.image }),
    "listingAgent": {
      "@type": "RealEstateAgent",
      "name": "Urbalytics",
      "url": "https://www.urbalytics.jp"
    },
    "address": {
      "@type": "PostalAddress",
      "addressLocality": config.address,
      "addressCountry": "JP"
    },
    "offers": {
      "@type": "Offer",
      "price": config.price,
      "priceCurrency": config.currency || "JPY",
      "availability": "https://schema.org/InStock"
    },
    "additionalProperty": [
      ...(config.propertyType ? [{
        "@type": "PropertyValue",
        "name": "propertyType",
        "value": config.propertyType
      }] : []),
      ...(config.floorSize ? [{
        "@type": "PropertyValue",
        "name": "floorSize",
        "value": config.floorSize
      }] : []),
      ...(config.numberOfRooms ? [{
        "@type": "PropertyValue",
        "name": "numberOfRooms",
        "value": config.numberOfRooms
      }] : []),
      ...(config.yearBuilt ? [{
        "@type": "PropertyValue",
        "name": "yearBuilt",
        "value": config.yearBuilt
      }] : [])
    ]
  };
}

// 生成软件应用结构化数据
export function generateSoftwareApplicationSchema(config: {
  name: string;
  description: string;
  url: string;
  applicationCategory: string;
  operatingSystem?: string;
  price?: string;
  currency?: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": config.name,
    "description": config.description,
    "url": config.url,
    "applicationCategory": config.applicationCategory,
    "operatingSystem": config.operatingSystem || "Web Browser",
    "provider": {
      "@type": "Organization",
      "name": "Urbalytics",
      "url": "https://www.urbalytics.jp"
    },
    ...(config.price ? {
      "offers": {
        "@type": "Offer",
        "price": config.price,
        "priceCurrency": config.currency || "JPY"
      }
    } : {})
  };
}

// 生成服务结构化数据
export function generateServiceSchema(config: {
  name: string;
  description: string;
  url: string;
  serviceType: string;
  areaServed?: string;
  provider: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": config.name,
    "description": config.description,
    "url": config.url,
    "serviceType": config.serviceType,
    "areaServed": {
      "@type": "Country",
      "name": config.areaServed || "Japan"
    },
    "provider": {
      "@type": "Organization",
      "name": config.provider,
      "url": "https://www.urbalytics.jp"
    }
  };
}

// 生成组织结构化数据
export function generateOrganizationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Urbalytics",
    "url": "https://www.urbalytics.jp",
    "logo": "https://www.urbalytics.jp/logo.png",
    "description": "日本最先端の不動産データプラットフォーム。AI駆動の市場分析、投資物件検索、収益性評価ツールで不動産投資を最適化。",
    "foundingDate": "2023",
    "industry": "Real Estate Technology",
    "areaServed": {
      "@type": "Country",
      "name": "Japan"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://twitter.com/urbalytics",
      "https://www.linkedin.com/company/urbalytics"
    ]
  };
}

// 生成网站结构化数据
export function generateWebSiteSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Urbalytics",
    "url": "https://www.urbalytics.jp",
    "description": "日本最先端の不動産データプラットフォーム。AI駆动の市場分析で不動産投資を最適化。",
    "publisher": {
      "@type": "Organization",
      "name": "Urbalytics"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://www.urbalytics.jp/ex/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };
}

// 生成面包屑结构化数据
export function generateBreadcrumbListSchema(items: Array<{ name: string; url: string }>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };
}
