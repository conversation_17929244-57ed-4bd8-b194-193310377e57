# 多语言 SEO 实现示例

## 使用参数式语言切换的 SEO 配置

### 1. 基础页面 Metadata

```typescript
import { generateSEOMetadata, generateLanguageUrls } from '@/lib/seo/utils';

export const metadata = generateSEOMetadata({
  title: 'Urbalytics - 日本不動産AI駆動市場情報プラットフォーム',
  description: '日本最先端の不動産データプラットフォーム。AI駆動の市場分析で不動産投資を最適化。',
  url: 'https://www.urbalytics.jp',
  keywords: '不動産投資, 日本不動産, AI分析, 市場データ',
});
```

### 2. 多语言页面 Metadata

```typescript
import { generateMultiLanguageSEO } from '@/lib/seo/utils';

export const metadata = generateMultiLanguageSEO({
  title: 'Urbalytics - 日本不動産AI駆動市場情報プラットフォーム',
  description: '日本最先端の不動産データプラットフォーム。',
  url: 'https://www.urbalytics.jp',
  currentLang: 'ja', // 从 URL 参数或 cookie 获取
  translations: {
    en: {
      title: 'Urbalytics - AI-Driven Real Estate Market Intelligence Platform',
      description: 'Japan\'s most advanced real estate data platform. Optimize real estate investment with AI-driven market analysis.',
      keywords: 'real estate investment, Japan real estate, AI analysis, market data',
    },
    zh: {
      title: 'Urbalytics - AI驱动的日本房地产市场情报平台',
      description: '日本最先进的房地产数据平台。通过AI驱动的市场分析优化房地产投资。',
      keywords: '房地产投资, 日本房地产, AI分析, 市场数据',
    },
  },
});
```

### 3. 动态获取当前语言

```typescript
import { cookies } from 'next/headers';
import { generateMultiLanguageSEO } from '@/lib/seo/utils';

export async function generateMetadata({ searchParams }: { 
  searchParams: Promise<{ lang?: string }> 
}) {
  const params = await searchParams;
  const cookieStore = await cookies();
  
  // 优先级: URL参数 > Cookie > 默认值
  const currentLang = params.lang || cookieStore.get('locale')?.value || 'ja';
  
  return generateMultiLanguageSEO({
    title: 'Page Title',
    description: 'Page Description',
    url: 'https://www.urbalytics.jp/page',
    currentLang,
    translations: {
      // ... 翻译配置
    },
  });
}
```

### 4. 语言切换 URL 生成

```typescript
import { generateLanguageUrls } from '@/lib/seo/utils';

const languageUrls = generateLanguageUrls('https://www.urbalytics.jp/page');
// 结果:
// {
//   'ja-JP': 'https://www.urbalytics.jp/page',
//   'en-US': 'https://www.urbalytics.jp/page?lang=en',
//   'zh-CN': 'https://www.urbalytics.jp/page?lang=zh',
// }
```

## URL 结构对比

### ❌ 旧的路径式语言路由
```
https://www.urbalytics.jp/
https://www.urbalytics.jp/en/
https://www.urbalytics.jp/zh/
```

### ✅ 新的参数式语言切换
```
https://www.urbalytics.jp/
https://www.urbalytics.jp/?lang=en
https://www.urbalytics.jp/?lang=zh
```

## 优势

1. **简化路由结构** - 不需要为每种语言创建单独的路由
2. **更好的 SEO** - 主要内容在主域名下，语言只是参数
3. **易于维护** - 统一的页面结构，只需要处理语言参数
4. **向后兼容** - 可以同时支持两种方式，逐步迁移

## 注意事项

1. **博客文章** - 保持路径式路由 `/blog/[lang]/[slug]`，因为这是内容管理系统的结构
2. **Sitemap** - 同时包含两种格式的 URL，确保搜索引擎能找到所有内容
3. **Canonical URL** - 确保每个页面都有正确的 canonical URL
4. **Hreflang 标签** - 使用参数式 URL 作为 alternate 链接
