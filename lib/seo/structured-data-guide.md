# 结构化数据实施指南

## 📋 已实施的结构化数据类型

### 1. 组织 (Organization) - 首页
```typescript
import { generateOrganizationSchema } from "@/lib/seo/utils";

const organizationSchema = generateOrganizationSchema();
```

**用途**: 告诉搜索引擎关于 Urbalytics 公司的基本信息
**位置**: `app/(homepage)/layout.tsx`

### 2. 网站 (WebSite) - 首页
```typescript
import { generateWebSiteSchema } from "@/lib/seo/utils";

const websiteSchema = generateWebSiteSchema();
```

**用途**: 定义网站的搜索功能和基本信息
**位置**: `app/(homepage)/layout.tsx`

### 3. 软件应用 (SoftwareApplication) - 搜索页面
```typescript
import { generateSoftwareApplicationSchema } from "@/lib/seo/utils";

const softwareSchema = generateSoftwareApplicationSchema({
  name: "Urbalytics 不動産検索",
  description: "AI駆動の投資物件検索プラットフォーム",
  url: "https://www.urbalytics.jp/ex/search",
  applicationCategory: "BusinessApplication"
});
```

**用途**: 将搜索功能标识为软件应用
**位置**: `app/(cp)/ex/search/layout.tsx`

### 4. 服务 (Service) - 功能页面
```typescript
import { generateServiceSchema } from "@/lib/seo/utils";

const serviceSchema = generateServiceSchema({
  name: "マーケットインサイト",
  description: "市場分析服务描述",
  url: "https://www.urbalytics.jp/feature/insight",
  serviceType: "Real Estate Market Analysis",
  provider: "Urbalytics"
});
```

**用途**: 标识各种不动产服务
**位置**: 
- `app/(homepage)/feature/insight/layout.tsx`
- `app/(homepage)/feature/rent/layout.tsx`

### 5. 博客 (Blog) - 博客页面
```typescript
const blogSchema = {
  "@context": "https://schema.org",
  "@type": "Blog",
  "name": "Urbalytics ブログ",
  "description": "不動産投資ブログ",
  "url": "https://www.urbalytics.jp/blog"
};
```

**用途**: 标识博客内容
**位置**: `app/blog/layout.tsx`

### 6. 文章 (Article) - 博客文章
```typescript
import { generateArticleSchema } from "@/lib/seo/utils";

const articleSchema = generateArticleSchema({
  title: "文章标题",
  description: "文章描述",
  url: "文章URL",
  image: "文章图片",
  publishedTime: "发布时间",
  author: "作者"
});
```

**用途**: 标识单篇博客文章
**位置**: `app/blog/[lang]/[slug]/page.tsx`

### 7. 面包屑 (BreadcrumbList) - 导航页面
```typescript
import { generateBreadcrumbListSchema } from "@/lib/seo/utils";

const breadcrumbSchema = generateBreadcrumbListSchema([
  { name: "ホーム", url: "https://www.urbalytics.jp" },
  { name: "ブログ", url: "https://www.urbalytics.jp/blog" },
  { name: "記事タイトル", url: "https://www.urbalytics.jp/blog/article" }
]);
```

**用途**: 提供导航路径信息
**位置**: `components/seo/BreadcrumbSchema.tsx`

## 🔧 可用的结构化数据函数

### 房地产物件 (RealEstateListing)
```typescript
import { generateRealEstateSchema } from "@/lib/seo/utils";

const realEstateSchema = generateRealEstateSchema({
  name: "物件名称",
  description: "物件描述",
  address: "地址",
  price: "价格",
  propertyType: "物件类型",
  floorSize: "面积",
  numberOfRooms: "房间数",
  yearBuilt: "建造年份",
  url: "物件URL"
});
```

### FAQ页面 (FAQPage)
```typescript
import { generateFAQSchema } from "@/lib/seo/utils";

const faqSchema = generateFAQSchema([
  {
    question: "问题1",
    answer: "答案1"
  },
  {
    question: "问题2", 
    answer: "答案2"
  }
]);
```

### 产品 (Product)
```typescript
import { generateProductSchema } from "@/lib/seo/utils";

const productSchema = generateProductSchema({
  name: "产品名称",
  description: "产品描述",
  image: "产品图片",
  url: "产品URL",
  price: "价格",
  currency: "货币"
});
```

## 📝 使用方法

### 1. 在 Layout 组件中添加
```typescript
export default function MyLayout({ children }: { children: React.ReactNode }) {
  const schema = generateSomeSchema({...});
  
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(schema)
        }}
      />
      {children}
    </>
  );
}
```

### 2. 使用结构化数据组件
```typescript
import { StructuredData, MultipleStructuredData } from "@/components/seo/StructuredData";

// 单个结构化数据
<StructuredData data={schema} id="my-schema" />

// 多个结构化数据
<MultipleStructuredData 
  schemas={[
    { data: schema1, id: "schema-1" },
    { data: schema2, id: "schema-2" }
  ]} 
/>
```

## 🎯 最佳实践

### 1. 选择合适的 Schema 类型
- **Organization**: 公司/组织信息
- **WebSite**: 网站基本信息和搜索功能
- **Article**: 博客文章、新闻文章
- **Service**: 提供的服务
- **Product**: 销售的产品
- **RealEstateListing**: 房地产物件
- **SoftwareApplication**: 软件工具/应用

### 2. 数据质量要求
- 确保所有必需字段都有值
- 使用准确和描述性的文本
- 提供高质量的图片URL
- 使用标准的日期格式

### 3. 测试和验证
- 使用 Google Rich Results Test 验证
- 检查 Google Search Console 中的结构化数据报告
- 确保没有错误和警告

## 🔍 验证工具

1. **Google Rich Results Test**: https://search.google.com/test/rich-results
2. **Schema.org Validator**: https://validator.schema.org/
3. **Google Search Console**: 结构化数据报告

## 📈 SEO 效果

正确实施结构化数据可以带来：
- 更好的搜索结果展示（富摘要）
- 提高点击率
- 更好的搜索引擎理解
- 可能获得特殊的搜索功能（如轮播、知识面板等）
