import { SystemMetricKeyEnum, SystemUserActivityEventTypeEnum } from "@prisma/client";
import { z } from "zod";
import { TllUserFormSchema, TllUserProps } from "./tllUser";
import { ProBuildingSchema } from "./proBuilding";

export const SystemMetricSchema = z.object({
  key: z.nativeEnum(SystemMetricKeyEnum),
  value: z.string(),
  recordDate: z.string().optional(),
});

export type SystemMetricProps = z.infer<typeof SystemMetricSchema>;

export const SystemUserActivitySchema = z.object({
  userId: z.string().optional(),
  recordDate: z.string().optional(),

  route: z.string(),
  routeNormalized: z.string().optional(),
  eventType: z.nativeEnum(SystemUserActivityEventTypeEnum),
  userAgent: z.string().optional(),
  referer: z.string().optional(),
  eventMetadata: z.record(z.string(), z.any()).optional(),

  user: TllUserFormSchema.optional(),
});

export type SystemUserActivityProps = z.infer<typeof SystemUserActivitySchema>;

export enum SystemReportViewHistoryRecordType {
  MANSION = "MANSION",
  POSTAL_CODE = "POSTAL_CODE",
  STATION_GROUP = "STATION_GROUP",
}

export const SystemReportViewHistorySchema = z.object({
  id: z.string().optional(),

  recordType: z.nativeEnum(SystemReportViewHistoryRecordType).optional(),
  userId: z.string().optional(),
  buildingId: z.string().optional(),
  postalCodeId: z.string().optional(),
  nearestStationGroupId: z.string().optional(),

  viewDate: z.string().optional(),
  isValid: z.boolean().optional(),

  building: ProBuildingSchema.optional(),
});

export type SystemReportViewHistoryProps = z.infer<typeof SystemReportViewHistorySchema>;
