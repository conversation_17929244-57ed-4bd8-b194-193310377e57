import { z } from "zod"

export enum TllUserSubscriptionPlan {
  PLUS = "PLUS",
  PRO = "PRO",
  ENTERPRISE = "ENTERPRISE",
}

export enum TllUserSubscriptionStatus {
  FREE = "FREE",
  ACTIVE = "ACTIVE",
  PAST_DUE = "PAST_DUE",
  CANCELED = "CANCELED",
  TRIALING = "TRIALING",
}

export enum TllUserSubscriptionInterval {
  MONTHLY = "MONTHLY",
  YEARLY = "YEARLY",
}

export const TllUserFormSchema = z.object({
  id: z.string().optional(), // ユーザーID
  source: z.string().optional(), // ソース
  name: z.string().min(2, {
    message: "名前は2文字以上でなければなりません。",
  }).optional(),
  email: z.string().email().optional(), // メールアドレス
  password: z.string().optional(), // パスワード
  accessLevel: z.number(), // アクセスレベル
  imageUrl: z.string().optional(), // 画像URL

  userSetting: z.object({
    dailyEmailNotification: z.boolean().optional(),
  }).optional(),

  stripeCustomerId: z.string().optional(), // StripeサブスクリプションID
  subscriptionPlan: z.nativeEnum(TllUserSubscriptionPlan).optional(), // サブスクリプションプラン
  subscriptionStatus: z.nativeEnum(TllUserSubscriptionStatus).optional(), // サブスクリプションステータス
  lastLoginAt: z.date().optional(), // 最終ログイン日時
  loginHistory: z.array(z.object({
    loginAt: z.date().optional(),
    loginMethod: z.string().optional(),
  })).optional(),

  referralCodeId: z.string().optional(), // 紹介コードID
  referralCodeExpiresAt: z.date().optional(), // 紹介コードの有効期限

  createdAt: z.date().optional(), // 作成日時
  updatedAt: z.date().optional(), // 更新日時
})

export type TllUserProps = z.infer<typeof TllUserFormSchema>

export const TllUserSubscriptionSchema = z.object({
  id: z.string().optional(), // サブスクリプションID

  userId: z.string().optional(), // ユーザーID
  stripeSubscriptionId: z.string().optional(), // StripeサブスクリプションID
  amount: z.number().optional(), // 金額
  interval: z.nativeEnum(TllUserSubscriptionInterval).optional(), // 間隔

  subscriptionPlan: z.nativeEnum(TllUserSubscriptionPlan), // サブスクリプションプラン
  subscriptionStatus: z.string().optional(), // サブスクリプションステータス
  subscriptionStartAt: z.date().optional(), // サブスクリプション開始日
  subscriptionEndAt: z.date().optional(), // サブスクリプション終了日
})

export type TllUserSubscriptionProps = z.infer<typeof TllUserSubscriptionSchema>

export enum TllUserActivationTokenType {
  PASSWORD_RESET = "PASSWORD_RESET",
  EMAIL_VERIFICATION = "EMAIL_VERIFICATION",
}

export const TllUserActivationTokenSchema = z.object({
  id: z.string().optional(), // トークンID
  email: z.string().email(), // メールアドレス
  tokenType: z.nativeEnum(TllUserActivationTokenType), // トークンタイプ
  token: z.string(), // トークン
  expiresAt: z.date(), // トークンの有効期限
})
export type TllUserActivationTokenProps = z.infer<typeof TllUserActivationTokenSchema>