

import { z } from "zod";

export const TllReinsMetricsSchema = z.object({
  id: z.string().optional(),

  reinsId: z.number().optional(),
  listingTitle: z.string().optional(),
  listingType: z.string().optional(),
  listingSubtype: z.string().optional(),

  address: z.string().optional(),
  roomNumber: z.string().optional(),
  price: z.number().optional(),

  recordDate: z.string().optional(),
  registrationDate: z.string().optional(),
  changeDate: z.string().optional(),
  downloadDetailsCount: z.number().optional(),
  checkDetailsCount: z.number().optional(),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type TllReinsMetricsProps = z.infer<typeof TllReinsMetricsSchema>;