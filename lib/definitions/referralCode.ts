import { z } from "zod";
import { TllUserFormSchema } from "./tllUser";

export const ReferralCodeSchema = z.object({
  id: z.string().optional(), // 紹介コードID

  code: z.string().optional(), // 紹介コード
  createdByUserId: z.string().optional(), // 作成者ID

  createdAt: z.date().optional(), // 作成日時
  expiresAt: z.date().optional(), // 有効期限
  isActive: z.boolean().optional(), // 有効/無効  

  referredUsers: z.array(TllUserFormSchema).optional(), // 紹介されたユーザーID
  createdByUser: TllUserFormSchema.optional(), // 作成者
})

export type ReferralCodeProps = z.infer<typeof ReferralCodeSchema>
