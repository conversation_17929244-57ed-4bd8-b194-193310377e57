import { z } from "zod";
import { TllUserFormSchema } from "./tllUser";

export enum TllUserLambdaRecordBidStatusEnum {
  ONE_REQUEST_DATA = "1.資料請求",
  TWO_NEGOTIATION = "2.価格交渉",
  THREE_DOCUMENT_PROPERTY_CHECK = "3.資料・現地確認",
  FOUR_CONTRACT = "4.契約",
  FIVE_HANDOVER = "5.引渡",
  NINE_CANCEL = "9.キャンセル",
}

export const UserLambdaRecordBidSchema = z.object({
  id: z.string().optional(),
  recordId: z.string(),
  recordPrice: z.number(),
  status: z.string().optional(),
  salesUserId: z.string(),
  biddingPrice: z.number().optional(),
  biddingResult: z.number().optional(),
  isSumitomoKeibai: z.boolean().optional(),
  // checklist: z.record(z.string(), z.object({
  //   fullKey: z.string(),
  //   key: z.string(),
  //   question: z.string(),
  //   answer: z.string(),
  //   answerType: z.string(),
  // })).optional(),
  checklist: z.any().optional(),
  dataLink: z.string().optional(),
  comments: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),

  salesUser: TllUserFormSchema.optional(),
  tllUserLambdaRecord: z.any().optional(),
});

export type UserLambdaRecordBidProps = z.infer<typeof UserLambdaRecordBidSchema>;