import { TllUserLambdaRecordPriceChangeSourceType } from "@prisma/client"
import { ProCompanySchema } from "./proCompany"

import { z } from "zod"

export const UserLambdaRecordPriceChangeSchema = z.object({
  id: z.string(),
  recordId: z.string().optional(),
  recordDate: z.date(),
  source: z.nativeEnum(TllUserLambdaRecordPriceChangeSourceType),
  companyId: z.string().optional(),

  price: z.number().optional(),
  yearlyIncome: z.number().optional(),
  status: z.string().optional(),
  brokerType: z.string().optional(),
  reinsNumber: z.string().optional(),
  canAdvertise: z.string().optional(),
  chirashiLink: z.string().optional(),
  chirashiType: z.string().optional(),
  comments: z.string().optional(), // ProCompanyProps
  company: ProCompanySchema.optional(),
})

export type UserLambdaRecordPriceChangeProps = z.infer<typeof UserLambdaRecordPriceChangeSchema>
