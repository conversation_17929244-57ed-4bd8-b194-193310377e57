import { z } from "zod";
import { PropertyAnalysisResultsSchema } from "./propertyAnalysisResults";
import { ProBuildingSchema } from "./proBuilding";
import { UserLambdaRecordPriceChangeSchema } from "./userLambdaRecordPriceChange";
import { UserLambdaRecordBidSchema } from "./userLambdaRecordBid";

export enum UserLambdaRecordType {
  BUILDING = "BUILDING",
  LAND = "LAND",
  MANSION = "MANSION",
  HOUSE = "HOUSE",
}

export const UserLambdaRecordSchema = z.object({
  id: z.string(),
  sourceData: z.string().optional(), // 修改为可选的 String?
  recordType: z.nativeEnum(UserLambdaRecordType),
  recordSubType: z.string().optional(), // 修改为可选的 String?

  compositeTitle: z.string().optional(), // 修改为可选的 String?

  price: z.number(), // 修改为可选的 Int?
  yearlyIncome: z.number().optional(), // 修改为可选的 Float?
  transport: z.string().optional(), // 修改为可选的 String?
  nearestStation: z.string().optional(), // 修改为可选的 String?
  nearestStationGroupId: z.string().optional(), // 修改为可选的 Int?
  nearestStationWalkMinute: z.number().optional(), // 修改为可选的 Int?
  valueRosenka: z.number().optional(), // 修改为可选的 Int?
  prefectureCode: z.number().optional(), // 修改为可选的 Int?
  areaCode: z.number().optional(), // 修改为可选的 Int?
  postalCode: z.number().optional(), // 修改为可选的 Int?
  address: z.string().optional(), // 修改为可选的 String?
  longitude: z.number().optional(), // 修改为可选的 Float?
  latitude: z.number().optional(), // 修改为可选的 Float?
  landSize: z.number().optional(), // 修改为可选的 Float?
  landRight: z.string().optional(), // 修改为可选的 String?
  buildingSize: z.number().optional(), // 修改为可选的 Float?
  buildingId: z.string().optional(), // 修改为可选的 Int?
  buildingName: z.string().optional(), // 修改为可选的 String?
  buildingMaterial: z.string().optional(), // 修改为可选的 String?
  buildingLayout: z.string().optional(), // 修改为可选的 String?
  buildingBuiltYear: z.number().optional(), // 修改为可选的 Int?
  buildingLevel: z.string().optional(), // 修改为可选的 String?
  buildingRoomCount: z.number().optional(), // 修改为可选的 String?
  landType: z.string().optional(), // 修改为可选的 String?
  landBuildingCoverageRatio: z.number().optional(), // 修改为可选的 Int?
  landFloorAreaRatio: z.number().optional(), // 修改为可选的 Int?
  roadConnection: z.string().optional(), // 修改为可选的 String?
  roadConnectionFirstType: z.string().optional(), // 修改为可选的 String?
  roadConnectionFirstFacing: z.string().optional(), // 修改为可选的 String?
  roadConnectionFirstWidth: z.string().optional(), // 修改为可选的 String?
  roadConnectionSecondType: z.string().optional(), // 修改为可选的 String?
  roadConnectionSecondFacing: z.string().optional(), // 修改为可选的 String?
  roadConnectionSecondWidth: z.string().optional(), // 修改为可选的 String?
  recordValues: z.any().optional(), // 修改为可选的 Json?
  analysisSimulationConfig: z.any().optional(), // 修改为可选的 Json?
  analysisSimulationResults: z.any().optional(), // 修改为可选的 Json?

  salesComments: z.string().optional(), // 修改为可选的 String?
  createdAt: z.date().optional(), // 修改为可选的 DateTime?
  updatedAt: z.date().optional(), // 修改为可选的 DateTime?

  nearestStationGroup: z.any().optional(), // 修改为可选的 Json?
  priceChanges: z.array(UserLambdaRecordPriceChangeSchema).optional(), // 修改为可选的 Json?
  bids: z.array(UserLambdaRecordBidSchema).optional(), // 修改为可选的 Json?
  materialMappings: z.array(z.any()).optional(), // 修改为可选的 Json?
  propertyAnalysisResult: PropertyAnalysisResultsSchema.optional(), // 修改为可选的 Json?
  building: ProBuildingSchema.optional(),
});

export type UserLambdaRecordProps = z.infer<typeof UserLambdaRecordSchema>;

export type UserLambdaRecordPropsWithLink = UserLambdaRecordProps & {
  link: string;
}