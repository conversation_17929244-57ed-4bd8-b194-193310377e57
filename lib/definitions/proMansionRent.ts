import { z } from "zod";

export enum ProMansionRentRecordType {
  MANSION = "MANSION",
  BUILDING_PART = "BUILDING_PART",
}

export const ProMansionRentSchema = z.object({
  id: z.string().optional(),

  recordType: z.nativeEnum(ProMansionRentRecordType).optional(),
  propertyType: z.string().optional(),
  compositeTitle: z.string().optional(),
  brokerReinsNumber: z.string().optional(),
  brokerType: z.string().optional(),
  brokerStatus: z.string().optional(),
  brokerListingCompany: z.string().optional(),
  brokerListingCompanyNumber: z.string().optional(),

  feeRent: z.number().optional(),
  feeManagement: z.number().optional(),
  feeUtility: z.number().optional(),
  feeGiftMoney: z.string().optional(),
  feeDepositMoney: z.string().optional(),

  landType: z.string().optional(),
  unitSize: z.number().optional(),
  unitLayout: z.string().optional(),
  unitLevel: z.number().optional(),

  buildingBuiltYear: z.number().optional(),
  buildingAddress: z.string().optional(),
  buildingName: z.string().optional(),
  buildingId: z.string().optional(),

  transport: z.string().optional(),
  nearestStation: z.string().optional(),
  nearestStationGroupId: z.string().optional(),
  nearestStationWalkMinute: z.string().optional(),
  roadConnection: z.string().optional(),
  roadConnectionFirstFacing: z.string().optional(),

  locationPostalCode: z.string().optional(),
  locationLongitude: z.number().optional(),
  locationLatitude: z.number().optional(),
  locationPrefectureCode: z.number().optional(),
  locationAreaCode: z.number().optional(),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type ProMansionRentProps = z.infer<typeof ProMansionRentSchema>;
