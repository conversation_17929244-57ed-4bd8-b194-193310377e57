import { z } from "zod";

export const ProBuildingSchema = z.object({
  id: z.string().optional(),

  isKubun: z.boolean().optional(),
  nameJa: z.string().optional(),
  nameEn: z.string().optional(),

  address: z.string().optional(),
  addressDetailed: z.string().optional(),

  postalCode: z.string().optional(),
  longitude: z.number().optional(),
  latitude: z.number().optional(),

  level: z.number().optional(),
  builtYear: z.number().optional(),
  link: z.string().optional(),

  nearestStationGroupId: z.string().optional(),
  nearestStation: z.string().optional(),
  transport: z.string().optional(),
  nearestStationWalkMinute: z.number().optional(),

  tllUserLambdaRecords: z.any(),
  mansionRents: z.any(),
  rawSumitomoAuctions: z.any(),
  systemReportViewHistory: z.any(),
});

export type ProBuildingProps = z.infer<typeof ProBuildingSchema>;
