import { z } from "zod";

export const ProBuildingHouseRentSchema = z.object({
  id: z.string().optional(),

  recordType: z.string().optional(),
  propertyType: z.string().optional(),

  brokerReinsNumber: z.string().optional(),
  brokerType: z.string().optional(),
  brokerStatus: z.string().optional(),
  brokerListingCompany: z.string().optional(),
  brokerListingCompanyNumber: z.string().optional(),

  feeRent: z.number().optional(),
  feeManagement: z.number().optional(),
  feeUtility: z.number().optional(),
  feeGiftMoney: z.string().optional(),
  feeDepositMoney: z.string().optional(),

  landSize: z.number().optional(),
  landType: z.string().optional(),

  buildingBuiltYear: z.number().optional(),
  buildingSize: z.number().optional(),
  buildingName: z.string().optional(),
  buildingId: z.string().optional(),
  buildingLayout: z.string().optional(),

  address: z.string().optional(),
  addressDetailed: z.string().optional(),

  transport: z.string().optional(),
  nearestStation: z.string().optional(),
  nearestStationWalkMinute: z.string().optional(),
  nearestStationGroupId: z.string().optional(),

  roadConnection: z.string().optional(),
  roadConnectionFirstFacing: z.string().optional(),

  locationPostalCode: z.string().optional(),
  locationLongitude: z.number().optional(),
  locationLatitude: z.number().optional(),
  locationPrefectureCode: z.number().optional(),
  locationAreaCode: z.number().optional(),

  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type ProBuildingHouseRentProps = z.infer<typeof ProBuildingHouseRentSchema>;
