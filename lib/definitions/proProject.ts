import { ProProjectType } from "@prisma/client";
import { z } from "zod";


export const ProProjectSchema = z.object({
  id: z.string(),

  compositeTitle: z.string(),
  name: z.string(),
  address: z.string(),
  addressChiban: z.string(),

  type: z.nativeEnum(ProProjectType),
  projectUsage: z.string(),
  levelAboveGround: z.number(),
  levelBelowGround: z.number(),
  constructionArea: z.number(),

  postalCode: z.string(),
  prefectureCode: z.number(),
  areaCode: z.number(),
  longitude: z.number(),
  latitude: z.number(),

  projectStartDate: z.string(),
  projectEndDateYear: z.number(),
  projectEndDateMonth: z.number(),
  projectEndDate: z.string(),
  recordCreatedAt: z.string(),
});

export type ProProject = z.infer<typeof ProProjectSchema>;