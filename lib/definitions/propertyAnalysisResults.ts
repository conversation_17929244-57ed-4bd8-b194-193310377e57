import { z } from "zod";
import { UserLambdaRecordType } from "../definitions/userLambdaRecord";

export const RoiValuationSchema = z.object({
  lowPrice: z.number().optional(),
  avgPrice: z.any().optional(),
  p80Price: z.number().optional(),
  lrPrice: z.number().optional(),
});
export type RoiValuationProps = z.infer<typeof RoiValuationSchema>;

export const RentValuationSchema = z.object({
  priceAfterUpside: z.number().optional(),
  rawDataRentArray: z.array(z.number()).optional(),
  rawDataRentArrayAverage: z.number().optional(),
});
export type RentValuationProps = z.infer<typeof RentValuationSchema>;

export const GfaValuationSchema = z.object({
  lowPrice: z.number().optional(),
  avgPrice: z.number().optional(),
  rawDataUnitPriceArray: z.array(z.number()).optional(),
  rawDataUnitPriceArrayAverage: z.number().optional(),
});
export type GfaValuationProps = z.infer<typeof GfaValuationSchema>;

export const SekisanValuationSchema = z.object({
  avgPrice: z.number().optional(),
  lowPrice: z.number().optional(),
  rawDataSekisanPercentArray: z.array(z.number()).optional(),
  sekisanPrice: z.number().optional(),
  sekisanTotalPercentage: z.number().optional(),
  sekisanLandPercentage: z.number().optional(),
});
export type SekisanValuationProps = z.infer<typeof SekisanValuationSchema>;

export const UnitLandPriceValuationSchema = z.object({
  lowPrice: z.number().optional(),
  avgPrice: z.number().optional(),
  rawDataIssueUnitPriceArray: z.array(z.number()).optional(),
  rawDataIssueUnitPriceArrayAverage: z.number().optional(),
});
export type UnitLandPriceValuationProps = z.infer<typeof UnitLandPriceValuationSchema>;


export const PropertyAnalysisResultsSchema = z.object({
  id: z.string().optional(),
  recordId: z.string().optional(),
  valuationRecordId: z.string().optional(),

  recordType: z.nativeEnum(UserLambdaRecordType),
  recordYearlyIncome: z.number().optional(),
  valuationCoef: z.number().optional(),

  nearbyRecordIdAndDistance: z.any().optional(),
  nearbyAverageRent: z.number().optional(),
  nearbyMinCap: z.number().optional(),
  nearbyMaxCap: z.number().optional(),
  nearbyAvgCap: z.number().optional(),
  nearby80PCap: z.number().optional(),
  nearbyLinearRegressionA: z.number().optional(),
  nearbyLinearRegressionB: z.number().optional(),

  nearbyMinGfa: z.number().optional(),
  nearbyMaxGfa: z.number().optional(),
  nearbyAvgGfa: z.number().optional(),
  nearby80PGfa: z.number().optional(),

  nearbyMinIssuePrice: z.number().optional(),
  nearbyMaxIssuePrice: z.number().optional(),
  nearbyAvgIssuePrice: z.number().optional(),
  nearby80PIssuePrice: z.number().optional(),

  nearbyMinRent: z.number().optional(),
  nearbyMaxRent: z.number().optional(),
  nearbyAvgRent: z.number().optional(),
  nearby80PRent: z.number().optional(),

  nearbyMinSekisanPercentage: z.number().optional(),
  nearbyMaxSekisanPercentage: z.number().optional(),
  nearbyAvgSekisanPercentage: z.number().optional(),
  nearby80PSekisanPercentage: z.number().optional(),

  roiValuation: RoiValuationSchema.optional(),
  rentValuation: RentValuationSchema.optional(),
  gfaValuation: GfaValuationSchema.optional(),
  sekisanValuation: SekisanValuationSchema.optional(),
  unitLandPriceValuation: UnitLandPriceValuationSchema.optional(),

  overallStarLevel: z.number().optional(),

  analysisDate: z.date().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type PropertyAnalysisResultsProps = z.infer<typeof PropertyAnalysisResultsSchema>;