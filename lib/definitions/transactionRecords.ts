
import { z } from "zod";

export enum TrTransactionRecordType {
  FARM = "FARM",
  FOREST = "FOREST",
  HOUSE = "HOUSE",
  LAND = "LAND",
  MANSION = "MANSION",
}

export const TransactionRecordSchema = z.object({
  id: z.string().optional(),

  postalCode: z.string().optional(),
  prefectureCode: z.number().optional(),

  propertyType: z.nativeEnum(TrTransactionRecordType), // 使用枚举类型
  landType: z.string().optional(),
  municipalityCode: z.number().optional(),
  prefecture: z.string().optional(),
  cityWard: z.string().optional(),
  district: z.string().optional(),

  nearestStation: z.string().optional(),
  nearestStationGroupId: z.number().optional(),
  nearestStationDistance: z.number().optional(),

  transactionPrice: z.number().optional(),
  transactionPricePerSquareMeter: z.number().optional(),
  layout: z.string().optional(),
  landSize: z.number().optional(),
  landShape: z.string().optional(),
  frontage: z.number().optional(),
  houseSize: z.number().optional(),
  builtYear: z.string().optional(),
  material: z.string().optional(),
  buildingUse: z.string().optional(),
  frontRoadDirection: z.string().optional(),
  frontRoadClassification: z.string().optional(),
  frontRoadBreadth: z.number().optional(),
  areaCityPlanningType: z.string().optional(),
  landBuildingLandRatio: z.number().optional(),
  landFloorAreaRatio: z.number().optional(),
  transactionQuartileStartDate: z.string().optional(),
  reformed: z.number().optional(),
  comments: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

export type TransactionRecordProps = z.infer<typeof TransactionRecordSchema>;