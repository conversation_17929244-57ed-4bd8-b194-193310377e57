import { any, z } from "zod"

export const ProCompanySchema = z.object({
  id: z.string(),
  fullName: z.string(),
  companyName: z.string(),
  branchName: z.string(),
  address: z.string(),
  repName: z.string(),
  contactNumber: z.string(),
  faxNumber: z.string(),
  email: z.string(),
  licenseNumber: z.string(),
  url: z.string(),
  isFav: z.number(),
  comments: z.string().optional(),

  priceChanges: z.array(any()).optional(),
})
export type ProCompanyProps = z.infer<typeof ProCompanySchema>