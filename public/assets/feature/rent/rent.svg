<svg xmlns="http://www.w3.org/2000/svg" width="1006.8115" height="762" viewBox="0 0 1006.8115 762" xmlns:xlink="http://www.w3.org/1999/xlink" role="img" artist="<PERSON><PERSON>" source="https://undraw.co/"><title>apartment rent</title><rect x="627.40575" y="295" width="86" height="55" fill="#6c63ff"/><rect x="493.40575" y="381" width="86" height="55" fill="#6c63ff"/><path d="M1061.40575,382V265h-65V189h-44v76h-65V382h-36V119h-73V94h-97V69h-96V94h-91v25h-79V381h-42V264h-65V188h-44v76h-65V381h-42V796h96V649h66V796h258V625h112V796h156v1h96V650h66V797h96V382Zm-869.5,50h38v30h-38Zm.5,84h38v30h-38Zm38.5,114h-38V600h38Zm99.5-271h-88V341h88Zm0-26h-88V315h88Zm0-26h-88V289h88Zm12.5,125h38v30h-38Zm.5,84h38v30h-38Zm38.5,114h-38V600h38Zm155.5-50h-70V549h70Zm0-89h-70V460h70Zm0-89h-70V371h70Zm0-89h-70V282h70Zm0-89h-70V193h70Zm131,356h-70V549h70Zm0-89h-70V460h70Zm0-89h-70V371h70Zm0-89h-70V282h70Zm0-89h-70V193h70Zm131,356h-70V549h70Zm0-89h-70V460h70Zm0-89h-70V371h70Zm0-89h-70V282h70Zm0-89h-70V193h70Zm80.5,209h38v30h-38Zm.5,84h38v30h-38Zm38.5,114h-38V601h38Zm99.5-271h-88V342h88Zm0-26h-88V316h88Zm0-26h-88V290h88Zm12.5,125h38v30h-38Zm.5,84h38v30h-38Zm38.5,114h-38V601h38Z" transform="translate(-96.59425 -69)" fill="#f2f2f2"/><path d="M216.59435,694.70953c-.27972,3.95588-.71759,8.38062-1.39447,13.03l12.22571-13.55877.06732.227a35.62641,35.62641,0,0,0-3.576-9.3313l.07819.21448Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M216.64214,694.0155l7.169-9.12335A18.76685,18.76685,0,0,0,217.399,678.059c-.13135-.07825-.26343-.15179-.39551-.22479C217.04265,678.91168,217.23046,685.2373,216.64214,694.0155Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M210.17431,729.78284q-.47251,1.43885-.9759,2.82574l18.0047-10.55676.02521.26495q.56424-2.4624.93988-4.92437l-17.9776,12.511Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M212.67833,720.9223l16.2157-13.37323.04608.25025a56.00729,56.00729,0,0,0-1.39038-13.1471l-12.4588,13.817Q214.10948,715.00144,212.67833,720.9223Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M214.81286,708.20624l-19.40308-21.64936.10382-.24475a46.76143,46.76143,0,0,0-4.65558,8.19544l22.14185,23.5741C213.75853,714.64117,214.35082,711.30981,214.81286,708.20624Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M190.64855,694.99493l.00079.00086c.035-.0802.06537-.16779.10065-.24756Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M189.569,697.65051l-.09985.2475c.03173-.085.06842-.16241.1004-.24695Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M212.87212,718.656l-22.22278-23.66022q-.56341,1.29079-1.07983,2.65527l22.713,23.50769Q212.59242,719.90018,212.87212,718.656Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M228.21581,717.07642a66.88225,66.88225,0,0,0,.72174-8.98676l-16.435,13.55408q-.97677,3.94272-2.14947,7.58081l17.82776-12.4068Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M186.62249,718.76294,204.611,742.23187a80.44538,80.44538,0,0,0,3.90894-8.81231l-21.94953-23.05408a64.6937,64.6937,0,0,0-.07226,8.66095Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M216.67479,677.81445l.1612-.07208c-3.93952-2.1048-8.20441-1.87878-12.29212.25513l11.78138,16.00207C216.95934,684.502,216.67907,677.909,216.67479,677.81445Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M208.61473,734.17865a79.8935,79.8935,0,0,1-3.533,7.94623l18.09443-9.36182.01825.26038a31.68784,31.68784,0,0,0,3.17248-7.34052q.43122-1.5172.78693-3.04284l-18.21936,10.68249C208.82817,733.60852,208.723,733.89612,208.61473,734.17865Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M216.2766,694.70709,204.225,678.33777l.113-.23316a26.97065,26.97065,0,0,0-8.6795,8.0091l19.245,21.473C215.56957,702.98969,216.00084,698.61694,216.2766,694.70709Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M208.717,732.89087a108.559,108.559,0,0,0,3.426-11.17206l-22.76947-23.5661q-.81308,2.208-1.49353,4.59326a38.897,38.897,0,0,0-1.25525,6.96924l22.075,23.18573Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M232.10448,758.31549c-2.73846,2.86835-5.89789,5.997-9.3847,9.14612l18.06414-2.64508-.093.21783a35.62487,35.62487,0,0,0,3.19745-9.46771l-.0766.21509Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M243.34423,746.00458c-.05127-.144-.10608-.28491-.16126-.42542-.657.85492-4.547,5.84662-10.599,12.23224l11.34058-2.45416A18.76768,18.76768,0,0,0,243.34423,746.00458Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M204.7896,781.23376q-1.28165.80678-2.554,1.5539l20.60034,3.353-.1496.22015q2.0051-1.53663,3.86475-3.19324l-21.82593-1.8305Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M212.36956,776.00659l21.0188.04273-.12415.2221a56.01046,56.01046,0,0,0,7.31464-11.01257l-18.40839,2.69531Q217.24823,772.35922,212.36956,776.00659Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M205.28338,780.91785l21.644,1.81518-.138.22156a66.8867,66.8867,0,0,0,6.28772-6.46124l-21.303-.04322Q208.507,778.86374,205.28338,780.91785Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M178.7733,791.62891c.15326-.15516.31556-.32349.47553-.48822a55.63855,55.63855,0,0,0,12.84479-3.66974l.58825-15.59894q.26175-.59546.50873-1.19934l-.626,16.60083a80.42632,80.42632,0,0,0,8.63122-4.29395l-2.01-28.99829c.155-.08063.311-.16223.4718-.24725l2.00745,28.96081.01971.00318a108.56437,108.56437,0,0,0,9.76435-6.41937l-2.18872-28.53638c.155-.11353.30951-.22449.46484-.34027l2.1886,28.53424q1.04141-.77171,2.05035-1.55152l-1.80554-28.88714c.1546-.12677.30908-.25385.46338-.38275l1.80689,28.909c2.77868-2.166,5.35968-4.35388,7.695-6.44958l-1.13574-29.04968.23608-.12226c-.80651.21039-1.6264.46009-2.45312.73181a40.9887,40.9887,0,0,0,4.17981-5.6983l-18.26758,9.45142a61.45871,61.45871,0,0,1-4.25738,6.93268c-.32648.30816-.653.61608-.97468.93768l.024.3468c-.14911.19635-.29742.39417-.44824.587l-.03241-.46759c-.23059.23663-.45544.48681-.68341.72979a57.72676,57.72676,0,0,0,6.06281-9.26922l-17.85688-23.29724c.12171,2.08245.3252,4.17865.5871,6.25775a35.23727,35.23727,0,0,0-5.986-7.31348l.14068.17981L177.132,729.7561c.95672,3.84858,1.908,8.192,2.70135,12.82294l7.33393-16.44458c.0335.25635.069.5119.10424.76758l-7.31622,16.40466q1.08573,6.51572,1.55475,12.589l8.78113-13.79291c.05017.19818.10028.39649.15045.5918l-8.87573,13.94153q.28966,4.05158.29895,7.874l10.73394-14.16192c.05732.18646.11322.36718.16883.54608L181.92009,765.206l-.05279-.10968q-.00457,1.51439-.05463,2.989l12.10779-13.6322q.0965.28482.1792.52478l-12.31738,13.868c-.01264.30475-.02381.61084-.03937.913a79.9227,79.9227,0,0,1-.90387,8.64917l1.16846-1.18329q-.332.68875-.64667,1.358l-.66492.6734c-.07629.43775-.16321.86536-.24695,1.296q-.36795.80814-.70923,1.57715c.21961-.98352.42444-1.97235.59931-2.9704L156.15557,762.522c6.30932,16.14185,18.19031,31.49628,18.95093,32.47089.61743-1.15332,1.17846-2.34009,1.69354-3.55158.14966-.01416.29931-.02826.44885-.04394-.06177.14892-.126.29523-.189.44287-.07537.00793-.15179.02-.227.02753.0586.01642.12891.03552.19263.05316-.36291.84607-.73889,1.67834-1.14215,2.48163.52777-.47449,1.33179-1.22437,2.293-2.17621,5.68671,1.4516,21.00647,4.556,34.1239-.02991l-20.09778-4.37195A56.202,56.202,0,0,1,178.7733,791.62891Zm14.1225-25.43073.48194-.488-.02753.73108-.48193.4881Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M242.94243,745.35437l.17011.0473c-1.69171-4.13379-5.12061-6.67993-9.63-7.64361l-1.1325,19.839C238.89617,750.68652,242.88543,745.42993,242.94243,745.35437Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M200.78472,783.62469a79.92058,79.92058,0,0,1-7.78931,3.8667l19.90723,4.33045-.152.21216a31.6834,31.6834,0,0,0,7.1253-3.63007q1.29987-.89355,2.54688-1.84167L201.577,783.16919C201.31279,783.32166,201.04827,783.47607,200.78472,783.62469Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M222.589,767.155c3.445-3.11585,6.56616-6.20862,9.27234-9.044L233.02,737.81677l.23578-.10748a26.97114,26.97114,0,0,0-11.79316.63257Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M180.96087,718.18878a18.76756,18.76756,0,0,0-8.21033-4.5166c-.14911-.03381-.29743-.06293-.44562-.09149.3703,1.01257,2.50415,6.9704,4.658,15.50061Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M179.60949,743.14252l-25.14466-14.59174.02307-.26483a46.7609,46.7609,0,0,0-1.89441,9.23309l28.34424,15.57569C180.59581,749.58826,180.12938,746.23694,179.60949,743.14252Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M152.54491,738.0473l.001.00055c.00848-.0871.01031-.17975.01922-.26648Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M152.339,740.90649l-.01843.2663c.0039-.09064.01489-.1756.01916-.26593Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M180.99376,753.68048l-28.44787-15.63263q-.13688,1.40176-.20612,2.859l28.86688,15.336Q181.11236,754.95013,180.99376,753.68048Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M181.41808,769.06628l-28.00061-15.14056a64.71157,64.71157,0,0,0,2.60834,8.25916l.0368-.28907,24.36182,16.75946A80.44488,80.44488,0,0,0,181.41808,769.06628Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M171.98626,713.66345l.131-.11828c-4.39716-.78406-8.38336.74914-11.61133,4.04205l16.1507,11.57684C174.324,719.93555,172.01958,713.75208,171.98626,713.66345Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M176.829,729.852,160.30791,718.0094l.03546-.25671a26.96943,26.96943,0,0,0-5.77893,10.29968l24.93988,14.473C178.71672,737.94751,177.77526,733.65558,176.829,729.852Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M181.44207,768.50256a108.55611,108.55611,0,0,0-.195-11.6839L152.3084,741.44458q-.09082,2.35107-.00067,4.83a38.90261,38.90261,0,0,0,.96039,7.01593l28.16064,15.227Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1072.59435,691.70953c-.27966,3.95588-.71765,8.38062-1.39441,13.03l12.22559-13.55877.06738.227a35.62848,35.62848,0,0,0-3.57593-9.3313l.07813.21448Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1072.64208,691.0155l7.16907-9.12335a18.76593,18.76593,0,0,0-6.41224-6.83319c-.13134-.07825-.26342-.15179-.3955-.22479C1073.04271,675.91168,1073.23046,682.2373,1072.64208,691.0155Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1066.17431,726.78284q-.47252,1.43885-.9759,2.82574l18.0047-10.55676.02515.26495q.56433-2.4624.93994-4.92437l-17.9776,12.511Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1068.67833,717.9223l16.2157-13.37323.04614.25025a56.01047,56.01047,0,0,0-1.39038-13.1471l-12.45886,13.817Q1070.10948,712.00144,1068.67833,717.9223Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1070.81286,705.20624l-19.40308-21.64936.10382-.24475a46.76143,46.76143,0,0,0-4.65558,8.19544l22.14185,23.5741C1069.75853,711.64117,1070.35082,708.30981,1070.81286,705.20624Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1046.64855,691.99493l.00079.00086c.035-.0802.06537-.16779.10065-.24756Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1068.87212,715.656l-22.22278-23.66022q-.56342,1.29079-1.07983,2.65527l22.713,23.50769Q1068.59242,716.90018,1068.87212,715.656Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1045.569,694.65051l-.09985.2475c.03173-.085.06842-.16241.1004-.24695Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1084.21581,714.07642a66.86235,66.86235,0,0,0,.72168-8.98676l-16.43494,13.55408q-.97677,3.94272-2.14947,7.58081l17.82782-12.4068Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1042.62249,715.76294l17.98852,23.46893a80.44538,80.44538,0,0,0,3.90894-8.81231l-21.94953-23.05408a64.6937,64.6937,0,0,0-.07226,8.66095Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1072.67479,674.81445l.16114-.07208c-3.93946-2.1048-8.20435-1.87878-12.29206.25513l11.78131,16.00207C1072.95934,681.502,1072.67907,674.909,1072.67479,674.81445Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1064.61473,731.17865a79.8935,79.8935,0,0,1-3.533,7.94623l18.09443-9.36182.01818.26038a31.69035,31.69035,0,0,0,3.17261-7.34052q.431-1.5172.78687-3.04284l-18.21936,10.68249C1064.82817,730.60852,1064.723,730.89612,1064.61473,731.17865Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1072.2766,691.70709,1060.225,675.33777l.113-.23316a26.97065,26.97065,0,0,0-8.6795,8.0091l19.245,21.473C1071.56957,699.98969,1072.00084,695.61694,1072.2766,691.70709Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1064.717,729.89087a108.559,108.559,0,0,0,3.426-11.17206l-22.76947-23.5661q-.81308,2.208-1.49353,4.59326a38.897,38.897,0,0,0-1.25525,6.96924l22.075,23.18573Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1088.10448,755.31549c-2.73852,2.86835-5.898,5.997-9.38476,9.14612l18.0642-2.64508-.093.21783a35.62512,35.62512,0,0,0,3.19751-9.46771l-.07666.21509Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1099.34423,743.00458c-.05127-.144-.10608-.28491-.16126-.42542-.6571.85492-4.547,5.84662-10.599,12.23224l11.34058-2.45416A18.76768,18.76768,0,0,0,1099.34423,743.00458Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1060.7896,778.23376q-1.28165.80678-2.55395,1.5539l20.60028,3.353-.14954.22015q2.00518-1.53663,3.86475-3.19324l-21.82593-1.8305Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1068.36956,773.00659l21.01886.04273-.12427.2221a56.01085,56.01085,0,0,0,7.3147-11.01257l-18.40845,2.69531Q1073.24815,769.35922,1068.36956,773.00659Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1061.28338,777.91785l21.644,1.81518-.13806.22156a66.89524,66.89524,0,0,0,6.28784-6.46124l-21.3031-.04322Q1064.507,775.86374,1061.28338,777.91785Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1034.7733,788.62891c.15326-.15516.31556-.32349.47553-.48822a55.63855,55.63855,0,0,0,12.84479-3.66974l.58825-15.59894q.26175-.59546.50873-1.19934l-.626,16.60083a80.42632,80.42632,0,0,0,8.63122-4.29395l-2.01-28.99829c.155-.08063.311-.16223.4718-.24725l2.00745,28.96081.01971.00318a108.56437,108.56437,0,0,0,9.76435-6.41937l-2.18872-28.53638c.155-.11353.30951-.22449.46484-.34027l2.1886,28.53424q1.0414-.77171,2.05035-1.55152l-1.80554-28.88714c.1546-.12677.30908-.25385.46338-.38275l1.80682,28.909c2.77881-2.166,5.35975-4.35388,7.69507-6.44958l-1.13574-29.04968.23608-.12226c-.80651.21039-1.62646.46009-2.45312.73181a40.9887,40.9887,0,0,0,4.17981-5.6983l-18.26758,9.45142a61.45871,61.45871,0,0,1-4.25738,6.93268c-.32648.30816-.653.61608-.97468.93768l.024.3468c-.14911.19635-.29742.39417-.44824.587l-.03241-.46759c-.23059.23663-.45544.48681-.68341.72979a57.72676,57.72676,0,0,0,6.06281-9.26922l-17.85688-23.29724c.12171,2.08245.3252,4.17865.5871,6.25775a35.23727,35.23727,0,0,0-5.986-7.31348l.14068.17981L1033.132,726.7561c.95672,3.84858,1.908,8.192,2.70135,12.82294l7.33393-16.44458c.0335.25635.069.5119.10424.76758l-7.31622,16.40466q1.08573,6.51572,1.55475,12.589l8.78113-13.79291c.05017.19818.10028.39649.15045.5918l-8.87573,13.94153q.28966,4.05158.299,7.874l10.73394-14.16192c.05732.18646.11322.36718.16883.54608l-10.84748,14.3117-.05279-.10968q-.00457,1.51439-.05463,2.989l12.10779-13.6322q.0965.28482.1792.52478l-12.31738,13.868c-.01264.30475-.02381.61084-.03937.913a79.9227,79.9227,0,0,1-.90387,8.64917l1.16846-1.18329q-.332.68875-.64667,1.358l-.66492.6734c-.07629.43775-.16321.86536-.24695,1.296q-.368.80814-.70923,1.57715c.21961-.98352.42444-1.97235.59931-2.9704l-24.18347-16.6369c6.30932,16.14185,18.19031,31.49628,18.95093,32.47089.61743-1.15332,1.17846-2.34009,1.69354-3.55158.14966-.01416.29931-.02826.44885-.04394-.06177.14892-.126.29523-.189.44287-.07537.00793-.15179.02-.227.02753.0586.01642.12891.03552.19263.05316-.36291.84607-.73889,1.67834-1.14215,2.48163.52777-.47449,1.33179-1.22437,2.293-2.17621,5.68671,1.4516,21.00647,4.556,34.1239-.02991l-20.09778-4.37195A56.202,56.202,0,0,1,1034.7733,788.62891Zm14.1225-25.43073.48194-.488-.02753.73108-.48193.4881Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1098.94237,742.35437l.17017.0473c-1.69165-4.13379-5.12061-6.67993-9.62989-7.64361l-1.13256,19.839C1094.89623,747.68652,1098.88549,742.42993,1098.94237,742.35437Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1056.78472,780.62469a79.92058,79.92058,0,0,1-7.78931,3.8667l19.90723,4.33045-.152.21216a31.68409,31.68409,0,0,0,7.12537-3.63007q1.29967-.89355,2.54687-1.84167l-20.84589-3.39307C1057.31279,780.32166,1057.04827,780.47607,1056.78472,780.62469Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1078.5891,764.155c3.445-3.11585,6.56616-6.20862,9.27222-9.044l1.15869-20.29419.23572-.10748a26.97075,26.97075,0,0,0-11.79309.63257Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1036.96087,715.18878a18.76756,18.76756,0,0,0-8.21033-4.5166c-.14911-.03381-.29743-.06293-.44562-.09149.3703,1.01257,2.50415,6.9704,4.658,15.50061Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1035.60949,740.14252l-25.14466-14.59174.02307-.26483a46.7609,46.7609,0,0,0-1.89441,9.23309l28.34424,15.57569C1036.59581,746.58826,1036.12938,743.23694,1035.60949,740.14252Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1008.339,737.90649l-.01843.2663c.0039-.09064.01489-.1756.01916-.26593Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1008.54491,735.0473l.001.00055c.00848-.0871.01031-.17975.01922-.26648Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1036.99376,750.68048l-28.44787-15.63263q-.13687,1.40176-.20612,2.859l28.86688,15.336Q1037.11236,751.95013,1036.99376,750.68048Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1037.41808,766.06628l-28.00061-15.14056a64.71157,64.71157,0,0,0,2.60834,8.25916l.0368-.28907,24.36182,16.75946A80.44488,80.44488,0,0,0,1037.41808,766.06628Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1027.98626,710.66345l.131-.11828c-4.39716-.78406-8.38336.74914-11.61133,4.04205l16.1507,11.57684C1030.324,716.93555,1028.01958,710.75208,1027.98626,710.66345Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1032.829,726.852l-16.52112-11.84259.03546-.25671a26.96943,26.96943,0,0,0-5.77893,10.29968l24.93988,14.473C1034.71672,734.94751,1033.77526,730.65558,1032.829,726.852Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M1037.44207,765.50256a108.55611,108.55611,0,0,0-.195-11.6839l-28.93866-15.37408q-.09083,2.35107-.00067,4.83a38.90261,38.90261,0,0,0,.96039,7.01593l28.16064,15.227Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M60.8115,727h946a0,0,0,0,1,0,0v13.53543A21.46457,21.46457,0,0,1,985.34693,762H82.27607A21.46457,21.46457,0,0,1,60.8115,740.53543V727a0,0,0,0,1,0,0Z" fill="#2f2e41"/><rect x="60.8115" y="716" width="946" height="19" fill="#3f3d56"/><path d="M890.5489,134.974l10.86472,6.88982-6.5965-11.99363a10.74278,10.74278,0,0,1,6.57009-2.345q.08755,0,.17483.00153a12.59143,12.59143,0,0,0,2.2542-.1667l3.68187,2.33484-1.57838-2.8698a13.10483,13.10483,0,0,0,6.44136-4.88847l6.59018,4.17915-4.16321-7.56943c3.85356-4.6262,9.0486-7.46284,14.76491-7.46284,6.853,0,12.95772,4.0757,16.89868,10.42591a12.63182,12.63182,0,0,0,11.16981,6.02261q.18369-.00678.36854-.0068c7.56553,0,13.69861,8.57792,13.69861,19.15935S965.55553,165.8439,957.99,165.8439a10.28,10.28,0,0,1-4.77148-1.19458,19.768,19.768,0,0,0-16.58038-.32416,17.28375,17.28375,0,0,1-14.09627.03263,19.78229,19.78229,0,0,0-16.43361.31768,10.27107,10.27107,0,0,1-4.721,1.16843c-7.56553,0-13.6986-8.57794-13.6986-19.15935A24.32833,24.32833,0,0,1,890.5489,134.974Z" transform="translate(-96.59425 -69)" fill="#f2f2f2"/><path d="M337.791,191.24384l6.9113,4.38277-4.19619-7.62942a6.83372,6.83372,0,0,1,4.17938-1.49172q.05571,0,.11122.001a8.00891,8.00891,0,0,0,1.434-.106l2.34212,1.48524-1.004-1.82554a8.3362,8.3362,0,0,0,4.09749-3.10967l4.19216,2.65846L353.21,180.7938a12.268,12.268,0,0,1,9.39229-4.74728c4.35937,0,8.24271,2.59264,10.74964,6.63216a8.03536,8.03536,0,0,0,7.10536,3.83111q.11687-.0043.23445-.00432c4.8126,0,8.714,5.45661,8.714,12.1877s-3.90139,12.18769-8.714,12.18769a6.53926,6.53926,0,0,1-3.03525-.7599,12.57492,12.57492,0,0,0-10.54716-.2062,10.99464,10.99464,0,0,1-8.96695.02076,12.584,12.584,0,0,0-10.4538.20208,6.53371,6.53371,0,0,1-3.00317.74326c-4.8126,0-8.714-5.45662-8.714-12.18769A15.47584,15.47584,0,0,1,337.791,191.24384Z" transform="translate(-96.59425 -69)" fill="#f2f2f2"/><path d="M151.791,214.73837l6.9113,4.38277-4.19619-7.62942A6.83372,6.83372,0,0,1,158.68544,210q.05571,0,.11122.001a8.00891,8.00891,0,0,0,1.434-.106l2.34212,1.48524-1.004-1.82554a8.33626,8.33626,0,0,0,4.09749-3.10967l4.19216,2.65846L167.21,204.28833a12.268,12.268,0,0,1,9.39229-4.74728c4.35937,0,8.24271,2.59264,10.74964,6.63216a8.03536,8.03536,0,0,0,7.10536,3.83111q.11686-.0043.23445-.00432c4.8126,0,8.714,5.45661,8.714,12.1877s-3.90139,12.18769-8.714,12.18769a6.53926,6.53926,0,0,1-3.03525-.7599,12.57492,12.57492,0,0,0-10.54716-.2062,10.99464,10.99464,0,0,1-8.96695.02076,12.58394,12.58394,0,0,0-10.4538.20208,6.53371,6.53371,0,0,1-3.00317.74326c-4.8126,0-8.714-5.45662-8.714-12.18769A15.47584,15.47584,0,0,1,151.791,214.73837Z" transform="translate(-96.59425 -69)" fill="#f2f2f2"/><path d="M401.31429,665.8638l-1.20711,1.20711s.40237,13.27823.80474,16.09482,2.01185,7.645.80474,11.26638-1.60948,6.43793-1.20711,6.8403-.40237,2.01185-.40237,2.01185l3.219-2.01185,5.23082-12.07112-2.41423-12.87586Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M401.31429,665.8638l-1.20711,1.20711s.40237,13.27823.80474,16.09482,2.01185,7.645.80474,11.26638-1.60948,6.43793-1.20711,6.8403-.40237,2.01185-.40237,2.01185l3.219-2.01185,5.23082-12.07112-2.41423-12.87586Z" transform="translate(-96.59425 -69)" opacity="0.1"/><path d="M422.63992,791.001s0-4.42608-.80474-5.23082-2.01185-3.62133-3.62133-3.62133-7.24267,1.60948-7.24267,2.01185a7.3482,7.3482,0,0,1-.80474,3.62134c-.80474,1.20711-5.23082,6.03555-.80474,6.84029a11.24181,11.24181,0,0,0,8.44978-2.01185c.40237-.40237,2.01185-.80474,2.41422-.40237S422.63992,791.001,422.63992,791.001Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M433.9063,783.75836l2.41422,4.42608s.80474,4.0237,2.01185,4.42607h1.20712s1.20711,4.42608,4.0237,4.02371,6.03556-2.01186,6.03556-2.8166,0-3.219-1.20711-4.0237a57.33906,57.33906,0,0,1-4.82845-4.42608l-2.01185-2.01185Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M403.02629,636.367a5.42957,5.42957,0,0,0-2.90661,4.06383c-.224,2.87278,2.61593,5.57978,1.70235,8.31262-.51871,1.55165-2.13422,2.55646-2.59925,4.125a5.67458,5.67458,0,0,0,.52124,3.68184,28.10366,28.10366,0,0,0,2.311,4.467c1.6961,2.66316,3.90369,5.18955,4.357,8.31423a3.5479,3.5479,0,0,1-.78517,3.12853,7.50626,7.50626,0,0,1,2.74538-1.30557,34.93682,34.93682,0,0,0,11.26661-6.27513,7.03667,7.03667,0,0,0,2.60531-3.46333c.61414-2.47222-1.26214-4.94522-1.08223-7.48622.11508-1.6254,1.05312-3.0607,1.58424-4.60118a11.8862,11.8862,0,0,0-1.02654-9.26263,11.0508,11.0508,0,0,0-6.57733-5.72089,13.05127,13.05127,0,0,0-6.64675.192A24.42658,24.42658,0,0,0,403.02629,636.367Z" transform="translate(-96.59425 -69)" fill="#2f2e41"/><path d="M405.29141,658.67944a10.438,10.438,0,0,1,9.09992-5.49031c6.43793,0,5.23082-.40237,5.23082-.40237l-2.01186,8.04741-4.0237,4.02371Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M406.94748,650.17135s3.62133,6.03556,1.20711,8.44978-1.20711,4.82845-.80474,5.23082,11.66874,1.20711,11.66874,1.20711l.40237-5.63319,1.20711-4.82844s-6.43793-1.20711-5.63318-4.02371S406.94748,650.17135,406.94748,650.17135Z" transform="translate(-96.59425 -69)" fill="#fbbebe"/><path d="M406.94748,650.17135s3.62133,6.03556,1.20711,8.44978-1.20711,4.82845-.80474,5.23082,11.66874,1.20711,11.66874,1.20711l.40237-5.63319,1.20711-4.82844s-6.43793-1.20711-5.63318-4.02371S406.94748,650.17135,406.94748,650.17135Z" transform="translate(-96.59425 -69)" opacity="0.1"/><path d="M435.11341,707.308a27.742,27.742,0,0,1-1.60948,4.02371c-.40237.40237-1.60948-2.81659-1.60948-2.81659l1.20711-2.8166Z" transform="translate(-96.59425 -69)" fill="#fbbebe"/><polygon points="333.892 620 335.501 629.255 311.359 629.255 312.566 620 333.892 620" fill="#fbbebe"/><circle cx="314.7793" cy="576.74528" r="7.64504" fill="#fbbebe"/><path d="M412.983,661.43773s-4.42607.40237-4.42607-.40237-.40237-3.219-1.20711-2.8166-6.03556.80474-5.63319,2.01185,1.20711,3.62134.40237,3.62134a2.61879,2.61879,0,0,0-2.01185,3.219c.40237,2.01186,4.82844,14.083,4.82844,14.083s.40237,4.02371,1.60948,5.23082a3.611,3.611,0,0,1,0,4.42607c-.80474.80474,2.01186,1.60949,3.219,3.219s10.46163.40237,10.864,0,10.05927-.40237,10.864-1.20711-.40237-3.219-.40237-3.219-.40237-3.62133,0-4.82844-1.60949-20.92327-2.8166-22.13038,3.62134-4.82845,3.62134-4.82845-.80474-3.219-6.03556-3.62133c0,0-1.60948-2.41423-3.219-2.41423s-5.1041,1.872-5.1041,1.872-.93145,5.3707-2.13856,5.77307A6.634,6.634,0,0,0,412.983,661.43773Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M429.07785,656.60928l2.8166,1.20711s8.04741,24.947,7.24267,29.77542-2.8166,21.32564-4.02371,21.32564-3.62133-2.41423-3.62133-2.41423v-4.0237c0-.80474-1.20712-3.219-.40237-3.219s2.01185-.80474,1.20711-1.60948-1.60948-2.01185-.80474-3.219,1.20711-1.60948.80474-2.8166-1.20711-2.41422-.40237-3.219-2.01186-4.82845-2.01186-4.82845l-4.42607-17.7043.80474-7.24267Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M430.68734,694.83448s-15.29008-1.20711-17.30194-1.20711-6.52551-1.89469-6.52551-1.89469-11.58116,11.55158-7.55745,30.463,4.82844,42.24891,7.24266,49.08921a127.38228,127.38228,0,0,1,4.02371,14.8877s4.0237-7.24267,11.66874.80474c0,0,.80475-6.03555-.40237-8.44978s-2.81659-15.29008-2.81659-15.29008-2.81659-20.52089-2.81659-22.93511-1.60949-22.93512-1.20711-23.33749,2.41422,15.69245,2.81659,18.509,1.60948,24.5446,2.81659,26.95882,6.8403,21.728,10.05927,22.93512,14.083-.40237,13.68059-1.20711-3.219-5.23082-4.0237-6.43793l-3.219-4.82844-7.645-26.15409,3.62134-31.78727S435.11341,694.43211,430.68734,694.83448Z" transform="translate(-96.59425 -69)" fill="#2f2e41"/><path d="M416.03058,644.73935h-9.80395a1.74629,1.74629,0,0,1-1.72492-1.47393l-.97438-6.17111h15.29008l-1.36694,6.493A1.451,1.451,0,0,1,416.03058,644.73935Z" transform="translate(-96.59425 -69)" fill="#2f2e41"/><path d="M824.715,573.83422h15.4687a0,0,0,0,1,0,0v22.09814a0,0,0,0,1,0,0h0A15.46868,15.46868,0,0,1,824.715,580.46368v-6.62946A0,0,0,0,1,824.715,573.83422Z" fill="#2f2e41"/><polygon points="815.692 703.292 814.587 708.817 822.321 708.817 822.689 703.292 815.692 703.292" fill="#a0616a"/><path d="M912.28584,776.71211s-1.47321-1.1049-2.20982-.3683-3.31472,15.1004-3.683,15.4687-11.41737,5.89284-8.83925,6.26114,12.52227.7366,13.99548,0,5.52454-7.73435,5.52454-7.73435v6.26114h.7366s.73661-6.62944,2.57812-8.471-1.47321-8.10265-1.47321-8.10265.7366-4.05132.3683-4.05132S912.28584,776.71211,912.28584,776.71211Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><polygon points="838.526 699.425 837.421 704.95 845.156 704.95 845.524 699.425 838.526 699.425" fill="#a0616a"/><path d="M926.28133,732.51584s3.683,11.04906,4.41962,12.154,3.31473,21.72984,3.31473,21.72984-2.57812,4.05132.7366,4.41963,1.84151-.73661,4.05133,0a4.15952,4.15952,0,0,0,3.683-.36831s1.10491-2.20981.3683-2.94641.73661-19.88833.73661-19.88833l-2.20982-6.62944-1.84151-20.99323s2.20981-5.52454,1.10491-6.99775-4.78793,1.10491-4.78793,1.10491Z" transform="translate(-96.59425 -69)" fill="#2f2e41"/><path d="M921.4934,703.78826s-1.10491,11.78567-1.84152,12.52227-8.83925,21.36154-8.83925,21.36154-2.20981,2.57811-1.47321,12.154,2.57812,19.15172,2.57812,19.15172-1.47321,5.52454,1.1049,5.52454,3.683-1.47321,4.78793-.3683,1.84151,0,2.20982-.73661,1.84151-3.31472,1.84151-4.05132,1.47321-19.52,1.47321-19.52,12.154-26.88606,13.25888-28.35927,7.366-9.57586,6.99775-15.1004Z" transform="translate(-96.59425 -69)" fill="#2f2e41"/><circle cx="834.1067" cy="577.33309" r="7.73435" fill="#a0616a"/><path d="M929.22775,652.96254s2.94641,6.62944,3.31472,8.471,7.366-3.683,7.366-3.683-3.683-6.26114-2.57811-9.20756Z" transform="translate(-96.59425 -69)" fill="#a0616a"/><path d="M941.01342,656.64556s-5.52454-4.05133-9.57586,4.41963-5.15623,8.10265-5.15623,8.10265-4.78793,1.47321-3.31472,11.78567-4.78793,23.93965-2.20982,24.67626,26.51777,4.78793,26.51777,1.84151-2.20982-14.36379-.3683-21.36154S944.32814,654.80405,941.01342,656.64556Z" transform="translate(-96.59425 -69)" fill="#3f3d56"/><path d="M935.48888,658.48707s-4.41962,6.62944-4.05132,11.78567.3683,8.471.3683,8.471-7.36605,35.357-5.89284,35.357,5.89284,1.84151,6.26114.7366S941.75,691.266,941.75,691.266s2.20982-22.46644-.3683-27.62268S935.48888,658.48707,935.48888,658.48707Z" transform="translate(-96.59425 -69)" fill="#3f3d56"/><path d="M926.64963,712.99581s-3.683,12.52228,1.47321,12.89058,2.20981-12.154,2.20981-12.154Z" transform="translate(-96.59425 -69)" fill="#a0616a"/><path d="M936.45222,771.08113s-1.29013-.96759-1.93519-.32253-2.90277,13.22376-3.2253,13.54629-9.99846,5.16049-7.74074,5.483,10.966.64506,12.25617,0,4.838-6.77315,4.838-6.77315v5.483h.64506s.64506-5.80556,2.25771-7.41821-1.29012-7.09568-1.29012-7.09568.64506-3.54783.32253-3.54783S936.45222,771.08113,936.45222,771.08113Z" transform="translate(-96.59425 -69)" fill="#6c63ff"/><path d="M926.87766,641.265a2.15033,2.15033,0,0,1,.93187-.65957,1.94618,1.94618,0,0,1,1.01588.08745,10.36091,10.36091,0,0,1,5.6655,4.11947,3.18086,3.18086,0,0,0,1.14173,1.23552,6.7235,6.7235,0,0,1,.92281.35166c.86117.56908.34776,1.88812-.14733,2.79386a14.10516,14.10516,0,0,0-1.6888,7.61511,2.17742,2.17742,0,0,0,.38734,1.29058,2.29765,2.29765,0,0,0,1.53043.611,37.79641,37.79641,0,0,0,5.39.28842c1.7628-.0318,3.71863-.28173,4.86077-1.62485,1.22787-1.44394,1.0353-3.65527.23654-5.37416s-2.09749-3.15543-3.02-4.81123a28.21631,28.21631,0,0,1-2.06075-5.47768,22.9412,22.9412,0,0,0-3.04486-6.79311,9.107,9.107,0,0,0-6.1148-3.92005c-3.34585-.37333-6.43118,1.68708-9.12056,3.71226-3.01978,2.274-6.23326,5.08714-6.47567,8.85958-.1964,3.05631,1.63877,5.82123,2.59757,8.72988,1.48011,4.4901.86,9.36757.2243,14.05239,2.72693-1.63338,5.81212-3.90195,5.63342-7.07562-.12056-2.14131-1.73619-3.85021-2.74186-5.74451C920.23587,648.32257,923.38879,644.83044,926.87766,641.265Z" transform="translate(-96.59425 -69)" fill="#2f2e41"/><path d="M1035.64227,678.90828a26.48971,26.48971,0,0,0,3.5036-13.42889c0-12.13621-7.51542-21.97455-16.78612-21.97455s-16.78612,9.83834-16.78612,21.97455a26.49021,26.49021,0,0,0,3.5036,13.42889,27.48754,27.48754,0,0,0,0,26.85781,27.48748,27.48748,0,0,0,0,26.85778,27.48747,27.48747,0,0,0,0,26.85777,26.4902,26.4902,0,0,0-3.5036,13.42888c0,12.13621,7.51539,21.97456,16.78612,21.97456s16.78612-9.83835,16.78612-21.97456a26.4897,26.4897,0,0,0-3.5036-13.42888,27.48747,27.48747,0,0,0,0-26.85777,27.48748,27.48748,0,0,0,0-26.85778,27.48754,27.48754,0,0,0,0-26.85781Z" transform="translate(-96.59425 -69)" fill="#3f3d56"/><ellipse cx="925.76549" cy="569.62163" rx="16.78612" ry="21.97455" fill="#3f3d56"/><ellipse cx="925.76549" cy="542.76385" rx="16.78612" ry="21.97455" fill="#3f3d56"/><path d="M1085.19852,427.96747a80.586,80.586,0,0,0,6.25135-9.202l-44.10693-7.2439,47.70282.35477a80.54682,80.54682,0,0,0,1.53549-63.71012l-63.999,33.19949,59.022-43.38546a80.41174,80.41174,0,1,0-132.80275,89.98724,80.4519,80.4519,0,0,0-9.17,14.66342L1006.8854,472.376l-61.04863-20.489a80.45355,80.45355,0,0,0,12.96471,75.50688,80.40575,80.40575,0,1,0,126.397,0,80.41662,80.41662,0,0,0,0-99.42647Z" transform="translate(-96.59425 -69)" fill="#f2f2f2"/><path d="M941.59425,477.6807a80.0607,80.0607,0,0,0,17.20723,49.71324,80.40575,80.40575,0,1,0,126.397,0C1095.97529,513.71277,941.59425,468.6562,941.59425,477.6807Z" transform="translate(-96.59425 -69)" fill="#f2f2f2"/><path d="M1085.19852,427.96747a80.586,80.586,0,0,0,6.25135-9.202l-44.10693-7.2439,47.70282.35477a80.54682,80.54682,0,0,0,1.53549-63.71012l-63.999,33.19949,59.022-43.38546a80.41174,80.41174,0,1,0-132.80275,89.98724,80.4519,80.4519,0,0,0-9.17,14.66342L1006.8854,472.376l-61.04863-20.489a80.45355,80.45355,0,0,0,12.96471,75.50688,80.40575,80.40575,0,1,0,126.397,0,80.41662,80.41662,0,0,0,0-99.42647Z" transform="translate(-96.59425 -69)" fill="#57b894"/><path d="M941.59425,477.6807a80.0607,80.0607,0,0,0,17.20723,49.71324,80.40575,80.40575,0,1,0,126.397,0C1095.97529,513.71277,941.59425,468.6562,941.59425,477.6807Z" transform="translate(-96.59425 -69)" opacity="0.1"/><path d="M190.64227,678.90828a26.48971,26.48971,0,0,0,3.5036-13.42889c0-12.13621-7.51542-21.97455-16.78612-21.97455s-16.78612,9.83834-16.78612,21.97455a26.49021,26.49021,0,0,0,3.5036,13.42889,27.48754,27.48754,0,0,0,0,26.85781,27.48748,27.48748,0,0,0,0,26.85778,27.48747,27.48747,0,0,0,0,26.85777,26.4902,26.4902,0,0,0-3.5036,13.42888c0,12.13621,7.51539,21.97456,16.78612,21.97456s16.78612-9.83835,16.78612-21.97456a26.4897,26.4897,0,0,0-3.5036-13.42888,27.48747,27.48747,0,0,0,0-26.85777,27.48748,27.48748,0,0,0,0-26.85778,27.48754,27.48754,0,0,0,0-26.85781Z" transform="translate(-96.59425 -69)" fill="#3f3d56"/><ellipse cx="80.76549" cy="569.62163" rx="16.78612" ry="21.97455" fill="#3f3d56"/><ellipse cx="80.76549" cy="542.76385" rx="16.78612" ry="21.97455" fill="#3f3d56"/><path d="M240.19852,427.96747a80.586,80.586,0,0,0,6.25135-9.202l-44.10693-7.2439,47.70282.35477a80.54682,80.54682,0,0,0,1.53549-63.71012l-63.999,33.19949,59.022-43.38546a80.41174,80.41174,0,1,0-132.80275,89.98724,80.4519,80.4519,0,0,0-9.17,14.66342L161.8854,472.376l-61.04863-20.489a80.45355,80.45355,0,0,0,12.96471,75.50688,80.40575,80.40575,0,1,0,126.397,0,80.41662,80.41662,0,0,0,0-99.42647Z" transform="translate(-96.59425 -69)" fill="#f2f2f2"/><path d="M96.59425,477.6807a80.0607,80.0607,0,0,0,17.20723,49.71324,80.40575,80.40575,0,1,0,126.397,0C250.97529,513.71277,96.59425,468.6562,96.59425,477.6807Z" transform="translate(-96.59425 -69)" fill="#f2f2f2"/><path d="M240.19852,427.96747a80.586,80.586,0,0,0,6.25135-9.202l-44.10693-7.2439,47.70282.35477a80.54682,80.54682,0,0,0,1.53549-63.71012l-63.999,33.19949,59.022-43.38546a80.41174,80.41174,0,1,0-132.80275,89.98724,80.4519,80.4519,0,0,0-9.17,14.66342L161.8854,472.376l-61.04863-20.489a80.45355,80.45355,0,0,0,12.96471,75.50688,80.40575,80.40575,0,1,0,126.397,0,80.41662,80.41662,0,0,0,0-99.42647Z" transform="translate(-96.59425 -69)" fill="#57b894"/><path d="M96.59425,477.6807a80.0607,80.0607,0,0,0,17.20723,49.71324,80.40575,80.40575,0,1,0,126.397,0C250.97529,513.71277,96.59425,468.6562,96.59425,477.6807Z" transform="translate(-96.59425 -69)" opacity="0.1"/><polygon points="579.452 354.556 539.556 394.452 520.653 375.55 513.294 382.909 539.531 409.146 586.812 361.915 579.452 354.556" fill="#57b894"/><polygon points="706.452 263.556 666.556 303.452 647.653 284.55 640.294 291.909 666.531 318.146 713.812 270.915 706.452 263.556" fill="#57b894"/></svg>