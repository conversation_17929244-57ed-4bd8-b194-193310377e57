self.addEventListener('push', function (event) {
  if (event.data) {
    console.log('🔥 Push Notification Received:', event.data)

    let data;
    if (typeof event.data === 'string') {
      data = JSON.parse(event.data);
    } else {
      data = event.data.json();
    }

    // console.log('🔥 Parsed Push Notification Data:', data)

    const options = {
      body: data.body,
      icon: data.icon || '/favicon.ico',
      badge: '/favicon.ico',
      vibrate: [100, 50, 100],
      data: {
        url: data?.data?.url || "https://urbalytics.jp", // ✅ Pass URL here, this is because data is nested //
        dateOfArrival: Date.now(),
        primaryKey: '2',
      },
    };

    // console.log("🛠 Displaying Notification:", data.title, options);

    event.waitUntil(
      self.registration.showNotification(data.title || "Notification", options)
        .then(() => console.log("✅ Notification Displayed Successfully"))
        .catch(err => console.error("❌ Notification Display Error:", err))
    );
  }
});

self.addEventListener('notificationclick', function (event) {
  console.log('🔥 Notification click received:', event.notification.data);

  event.notification.close(); // Close the notification when clicked


  // 🔹 Extract URL from notification data
  const urlToOpen = event.notification.data?.url || "https://urbalytics.jp";

  event.waitUntil(
    clients.matchAll({ type: "window", includeUncontrolled: true }).then(clientList => {
      for (const client of clientList) {
        if (client.url === urlToOpen && "focus" in client) {
          return client.focus(); // Focus existing tab if open
        }
      }
      return clients.openWindow(urlToOpen); // Open new window if not already open
    })
  );
});


const IMAGE_CACHE_NAME = "supabase-image-cache-v1";

// Match both /object/public and /render/image
const SUPABASE_IMAGE_PREFIXES = [
  "https://waqahdtjadldhstauanr.supabase.co/storage/v1/object/public/",
  "https://waqahdtjadldhstauanr.supabase.co/storage/v1/render/image/public/",
];

self.addEventListener("fetch", (event) => {
  const url = event.request.url;

  const isSupabaseImage = SUPABASE_IMAGE_PREFIXES.some(prefix =>
    url.startsWith(prefix)
  );

  if (isSupabaseImage) {
    event.respondWith(
      caches.open(IMAGE_CACHE_NAME).then(async (cache) => {
        const cached = await cache.match(event.request);
        if (cached) {
          return cached;
        }

        return fetch(event.request)
          .then((response) => {
            if (response.status === 200 && event.request.method === 'GET') {
              cache.put(event.request, response.clone());
            }
            return response;
          })
          .catch((err) => {
            console.warn("⚠️ Supabase image fetch failed:", err);
            throw err;
          });
      })
    );
  }
});