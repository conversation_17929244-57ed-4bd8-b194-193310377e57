// hooks/use-cached-data.tsx
'use client'

import { useState, useEffect, useCallback } from 'react'

interface UseCachedDataOptions<T> {
  cacheKey: string
  fetchFunction: () => Promise<T>
  cacheExpiry?: number // in milliseconds, default 5 minutes
  defaultValue?: T
  enabled?: boolean
}

interface CachedDataResult<T> {
  data: T
  isLoading: boolean
  error: Error | null
  refetch: () => Promise<void>
  clearCache: () => void
}

/**
 * 🚀 Custom hook for caching data in localStorage with stale-while-revalidate strategy
 * 
 * @param options Configuration options
 * @returns Cached data with loading state and utilities
 */
export function useCachedData<T>({
  cacheKey,
  fetchFunction,
  cacheExpiry = 30 * 60 * 1000, // 30 minutes default
  defaultValue,
  enabled = true
}: UseCachedDataOptions<T>): CachedDataResult<T> {
  
  // Initialize with cached data or default value
  const [data, setData] = useState<T>(() => {
    if (typeof window !== 'undefined' && defaultValue !== undefined) {
      try {
        const cached = localStorage.getItem(cacheKey)
        return cached ? JSON.parse(cached) : defaultValue
      } catch (error) {
        console.warn(`🔥 Failed to parse cached data for ${cacheKey}:`, error)
        return defaultValue
      }
    }
    return defaultValue as T
  })
  
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  // Check if cache is expired
  const isCacheExpired = useCallback(() => {
    if (typeof window === 'undefined') return true
    
    const lastFetch = localStorage.getItem(`${cacheKey}-timestamp`)
    if (!lastFetch) return true
    
    const now = Date.now()
    return (now - parseInt(lastFetch, 10)) > cacheExpiry
  }, [cacheKey, cacheExpiry])
  
  // Fetch and cache data
  const fetchData = useCallback(async () => {
    if (!enabled) return
    
    try {
      setIsLoading(true)
      setError(null)
      
      const result = await fetchFunction()
      
      // Update state
      setData(result)
      
      // Cache the data
      if (typeof window !== 'undefined') {
        localStorage.setItem(cacheKey, JSON.stringify(result))
        localStorage.setItem(`${cacheKey}-timestamp`, Date.now().toString())
      }
      
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      console.error(`🔥 Failed to fetch data for ${cacheKey}:`, error)
    } finally {
      setIsLoading(false)
    }
  }, [cacheKey, fetchFunction, enabled])
  
  // Clear cache
  const clearCache = useCallback(() => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(cacheKey)
      localStorage.removeItem(`${cacheKey}-timestamp`)
    }
  }, [cacheKey])
  
  // Fetch data on mount if cache is expired
  useEffect(() => {
    if (enabled && isCacheExpired()) {
      fetchData()
    }
  }, [enabled, isCacheExpired, fetchData])
  
  return {
    data,
    isLoading,
    error,
    refetch: fetchData,
    clearCache
  }
}
