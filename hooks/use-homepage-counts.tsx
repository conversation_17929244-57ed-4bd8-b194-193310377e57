// hooks/use-homepage-counts.tsx
'use client'

import { getNewCountAndPriceChangeForHP } from "@/actions/tllUserLambdaRecordForHP";
import { useCachedData } from "./use-cached-data";
import { CACHE_CONFIG } from "@/lib/cache-config";

interface HomepageCounts {
  newlyCreatedCount: number;
  priceChangeCount: number;
}

/**
 * 🚀 Custom hook for homepage counts with localStorage caching
 * Uses stale-while-revalidate strategy for better UX
 */
export function useHomepageCounts() {
  const config = CACHE_CONFIG.HOMEPAGE_COUNTS;

  return useCachedData<HomepageCounts>({
    cacheKey: config.key,
    fetchFunction: async () => {
      const res = await getNewCountAndPriceChangeForHP();
      if (res.success) {
        return {
          newlyCreatedCount: res.data.newlyCreatedCount,
          priceChangeCount: res.data.priceChangeCount
        };
      }
      throw new Error('Failed to fetch homepage counts');
    },
    defaultValue: { newlyCreatedCount: 0, priceChangeCount: 0 },
    cacheExpiry: config.expiry
  });
}
