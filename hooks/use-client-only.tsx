"use client";

import { useEffect, useState } from "react";

/**
 * Hook to ensure component only renders on client-side
 * Prevents hydration mismatches for components that use browser-only APIs
 */
export function useClientOnly() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Component wrapper that only renders children on client-side
 * Useful for components that use localStorage, window, navigator, etc.
 */
export function ClientOnly({
  children,
  fallback = null,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const isClient = useClientOnly();

  if (!isClient) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Hook for hydration-safe rendering
 * Prevents hydration mismatches by ensuring consistent server/client rendering
 */
export function useHydrationSafe() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated;
}

/**
 * Component that suppresses hydration warnings for its children
 * Use this when you know the content will be different on server/client
 */
export function NoHydration({ children }: { children: React.ReactNode }) {
  return <div suppressHydrationWarning>{children}</div>;
}
