- I am using pnpm as package manger, unless specified otherwise
- Reply me using chinese unless specified other wise
- when you create console.log, adding emoji in such as 🔥, so i can see it in the console 
- use Japanese in html text for this code base where possible
- when i ask you to use new component / library, use components in `@/components/ui/` where possible, only look for external libraries if non-existant
- when making changes, DO NOT delete existing logic where possible
- using lucide-react icons whenever i mention icon
- always use `Image` component from Nextjs when handling image